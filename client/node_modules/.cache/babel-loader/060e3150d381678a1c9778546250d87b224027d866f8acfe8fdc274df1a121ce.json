{"ast": null, "code": "//\n// format - printf-like string formatting for JavaScript\n// github.com/samsonjs/format\n// @_sjs\n//\n// Copyright 2010 - 2013 <PERSON> <<EMAIL>>\n//\n// MIT License\n// http://sjs.mit-license.org\n//\n\n;\n(function () {\n  //// Export the API\n  var namespace;\n\n  // CommonJS / Node module\n  if (typeof module !== 'undefined') {\n    namespace = module.exports = format;\n  }\n\n  // Browsers and other environments\n  else {\n    // Get the global object. Works in ES3, ES5, and ES5 strict mode.\n    namespace = function () {\n      return this || (1, eval)('this');\n    }();\n  }\n  namespace.format = format;\n  namespace.vsprintf = vsprintf;\n  if (typeof console !== 'undefined' && typeof console.log === 'function') {\n    namespace.printf = printf;\n  }\n  function printf(/* ... */\n  ) {\n    console.log(format.apply(null, arguments));\n  }\n  function vsprintf(fmt, replacements) {\n    return format.apply(null, [fmt].concat(replacements));\n  }\n  function format(fmt) {\n    var argIndex = 1 // skip initial format argument\n      ,\n      args = [].slice.call(arguments),\n      i = 0,\n      n = fmt.length,\n      result = '',\n      c,\n      escaped = false,\n      arg,\n      tmp,\n      leadingZero = false,\n      precision,\n      nextArg = function () {\n        return args[argIndex++];\n      },\n      slurpNumber = function () {\n        var digits = '';\n        while (/\\d/.test(fmt[i])) {\n          digits += fmt[i++];\n          c = fmt[i];\n        }\n        return digits.length > 0 ? parseInt(digits) : null;\n      };\n    for (; i < n; ++i) {\n      c = fmt[i];\n      if (escaped) {\n        escaped = false;\n        if (c == '.') {\n          leadingZero = false;\n          c = fmt[++i];\n        } else if (c == '0' && fmt[i + 1] == '.') {\n          leadingZero = true;\n          i += 2;\n          c = fmt[i];\n        } else {\n          leadingZero = true;\n        }\n        precision = slurpNumber();\n        switch (c) {\n          case 'b':\n            // number in binary\n            result += parseInt(nextArg(), 10).toString(2);\n            break;\n          case 'c':\n            // character\n            arg = nextArg();\n            if (typeof arg === 'string' || arg instanceof String) result += arg;else result += String.fromCharCode(parseInt(arg, 10));\n            break;\n          case 'd':\n            // number in decimal\n            result += parseInt(nextArg(), 10);\n            break;\n          case 'f':\n            // floating point number\n            tmp = String(parseFloat(nextArg()).toFixed(precision || 6));\n            result += leadingZero ? tmp : tmp.replace(/^0/, '');\n            break;\n          case 'j':\n            // JSON\n            result += JSON.stringify(nextArg());\n            break;\n          case 'o':\n            // number in octal\n            result += '0' + parseInt(nextArg(), 10).toString(8);\n            break;\n          case 's':\n            // string\n            result += nextArg();\n            break;\n          case 'x':\n            // lowercase hexadecimal\n            result += '0x' + parseInt(nextArg(), 10).toString(16);\n            break;\n          case 'X':\n            // uppercase hexadecimal\n            result += '0x' + parseInt(nextArg(), 10).toString(16).toUpperCase();\n            break;\n          default:\n            result += c;\n            break;\n        }\n      } else if (c === '%') {\n        escaped = true;\n      } else {\n        result += c;\n      }\n    }\n    return result;\n  }\n})();", "map": {"version": 3, "names": ["namespace", "module", "exports", "format", "eval", "vsprintf", "console", "log", "printf", "apply", "arguments", "fmt", "replacements", "concat", "argIndex", "args", "slice", "call", "i", "n", "length", "result", "c", "escaped", "arg", "tmp", "leadingZero", "precision", "nextArg", "slurpNumber", "digits", "test", "parseInt", "toString", "String", "fromCharCode", "parseFloat", "toFixed", "replace", "JSON", "stringify", "toUpperCase"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/format/format.js"], "sourcesContent": ["//\n// format - printf-like string formatting for JavaScript\n// github.com/samsonjs/format\n// @_sjs\n//\n// Copyright 2010 - 2013 <PERSON> <<EMAIL>>\n//\n// MIT License\n// http://sjs.mit-license.org\n//\n\n;(function() {\n\n  //// Export the API\n  var namespace;\n\n  // CommonJS / Node module\n  if (typeof module !== 'undefined') {\n    namespace = module.exports = format;\n  }\n\n  // Browsers and other environments\n  else {\n    // Get the global object. Works in ES3, ES5, and ES5 strict mode.\n    namespace = (function(){ return this || (1,eval)('this') }());\n  }\n\n  namespace.format = format;\n  namespace.vsprintf = vsprintf;\n\n  if (typeof console !== 'undefined' && typeof console.log === 'function') {\n    namespace.printf = printf;\n  }\n\n  function printf(/* ... */) {\n    console.log(format.apply(null, arguments));\n  }\n\n  function vsprintf(fmt, replacements) {\n    return format.apply(null, [fmt].concat(replacements));\n  }\n\n  function format(fmt) {\n    var argIndex = 1 // skip initial format argument\n      , args = [].slice.call(arguments)\n      , i = 0\n      , n = fmt.length\n      , result = ''\n      , c\n      , escaped = false\n      , arg\n      , tmp\n      , leadingZero = false\n      , precision\n      , nextArg = function() { return args[argIndex++]; }\n      , slurpNumber = function() {\n          var digits = '';\n          while (/\\d/.test(fmt[i])) {\n            digits += fmt[i++];\n            c = fmt[i];\n          }\n          return digits.length > 0 ? parseInt(digits) : null;\n        }\n      ;\n    for (; i < n; ++i) {\n      c = fmt[i];\n      if (escaped) {\n        escaped = false;\n        if (c == '.') {\n          leadingZero = false;\n          c = fmt[++i];\n        }\n        else if (c == '0' && fmt[i + 1] == '.') {\n          leadingZero = true;\n          i += 2;\n          c = fmt[i];\n        }\n        else {\n          leadingZero = true;\n        }\n        precision = slurpNumber();\n        switch (c) {\n        case 'b': // number in binary\n          result += parseInt(nextArg(), 10).toString(2);\n          break;\n        case 'c': // character\n          arg = nextArg();\n          if (typeof arg === 'string' || arg instanceof String)\n            result += arg;\n          else\n            result += String.fromCharCode(parseInt(arg, 10));\n          break;\n        case 'd': // number in decimal\n          result += parseInt(nextArg(), 10);\n          break;\n        case 'f': // floating point number\n          tmp = String(parseFloat(nextArg()).toFixed(precision || 6));\n          result += leadingZero ? tmp : tmp.replace(/^0/, '');\n          break;\n        case 'j': // JSON\n          result += JSON.stringify(nextArg());\n          break;\n        case 'o': // number in octal\n          result += '0' + parseInt(nextArg(), 10).toString(8);\n          break;\n        case 's': // string\n          result += nextArg();\n          break;\n        case 'x': // lowercase hexadecimal\n          result += '0x' + parseInt(nextArg(), 10).toString(16);\n          break;\n        case 'X': // uppercase hexadecimal\n          result += '0x' + parseInt(nextArg(), 10).toString(16).toUpperCase();\n          break;\n        default:\n          result += c;\n          break;\n        }\n      } else if (c === '%') {\n        escaped = true;\n      } else {\n        result += c;\n      }\n    }\n    return result;\n  }\n\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAE,aAAW;EAEX;EACA,IAAIA,SAAS;;EAEb;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjCD,SAAS,GAAGC,MAAM,CAACC,OAAO,GAAGC,MAAM;EACrC;;EAEA;EAAA,KACK;IACH;IACAH,SAAS,GAAI,YAAU;MAAE,OAAO,IAAI,IAAI,CAAC,CAAC,EAACI,IAAI,EAAE,MAAM,CAAC;IAAC,CAAC,CAAC,CAAE;EAC/D;EAEAJ,SAAS,CAACG,MAAM,GAAGA,MAAM;EACzBH,SAAS,CAACK,QAAQ,GAAGA,QAAQ;EAE7B,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,GAAG,KAAK,UAAU,EAAE;IACvEP,SAAS,CAACQ,MAAM,GAAGA,MAAM;EAC3B;EAEA,SAASA,MAAMA,CAAC;EAAA,EAAW;IACzBF,OAAO,CAACC,GAAG,CAACJ,MAAM,CAACM,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EAC5C;EAEA,SAASL,QAAQA,CAACM,GAAG,EAAEC,YAAY,EAAE;IACnC,OAAOT,MAAM,CAACM,KAAK,CAAC,IAAI,EAAE,CAACE,GAAG,CAAC,CAACE,MAAM,CAACD,YAAY,CAAC,CAAC;EACvD;EAEA,SAAST,MAAMA,CAACQ,GAAG,EAAE;IACnB,IAAIG,QAAQ,GAAG,CAAC,CAAC;MAAA;MACbC,IAAI,GAAG,EAAE,CAACC,KAAK,CAACC,IAAI,CAACP,SAAS,CAAC;MAC/BQ,CAAC,GAAG,CAAC;MACLC,CAAC,GAAGR,GAAG,CAACS,MAAM;MACdC,MAAM,GAAG,EAAE;MACXC,CAAC;MACDC,OAAO,GAAG,KAAK;MACfC,GAAG;MACHC,GAAG;MACHC,WAAW,GAAG,KAAK;MACnBC,SAAS;MACTC,OAAO,GAAG,SAAAA,CAAA,EAAW;QAAE,OAAOb,IAAI,CAACD,QAAQ,EAAE,CAAC;MAAE,CAAC;MACjDe,WAAW,GAAG,SAAAA,CAAA,EAAW;QACvB,IAAIC,MAAM,GAAG,EAAE;QACf,OAAO,IAAI,CAACC,IAAI,CAACpB,GAAG,CAACO,CAAC,CAAC,CAAC,EAAE;UACxBY,MAAM,IAAInB,GAAG,CAACO,CAAC,EAAE,CAAC;UAClBI,CAAC,GAAGX,GAAG,CAACO,CAAC,CAAC;QACZ;QACA,OAAOY,MAAM,CAACV,MAAM,GAAG,CAAC,GAAGY,QAAQ,CAACF,MAAM,CAAC,GAAG,IAAI;MACpD,CAAC;IAEL,OAAOZ,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACjBI,CAAC,GAAGX,GAAG,CAACO,CAAC,CAAC;MACV,IAAIK,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;QACf,IAAID,CAAC,IAAI,GAAG,EAAE;UACZI,WAAW,GAAG,KAAK;UACnBJ,CAAC,GAAGX,GAAG,CAAC,EAAEO,CAAC,CAAC;QACd,CAAC,MACI,IAAII,CAAC,IAAI,GAAG,IAAIX,GAAG,CAACO,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;UACtCQ,WAAW,GAAG,IAAI;UAClBR,CAAC,IAAI,CAAC;UACNI,CAAC,GAAGX,GAAG,CAACO,CAAC,CAAC;QACZ,CAAC,MACI;UACHQ,WAAW,GAAG,IAAI;QACpB;QACAC,SAAS,GAAGE,WAAW,CAAC,CAAC;QACzB,QAAQP,CAAC;UACT,KAAK,GAAG;YAAE;YACRD,MAAM,IAAIW,QAAQ,CAACJ,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;YAC7C;UACF,KAAK,GAAG;YAAE;YACRT,GAAG,GAAGI,OAAO,CAAC,CAAC;YACf,IAAI,OAAOJ,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYU,MAAM,EAClDb,MAAM,IAAIG,GAAG,CAAC,KAEdH,MAAM,IAAIa,MAAM,CAACC,YAAY,CAACH,QAAQ,CAACR,GAAG,EAAE,EAAE,CAAC,CAAC;YAClD;UACF,KAAK,GAAG;YAAE;YACRH,MAAM,IAAIW,QAAQ,CAACJ,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;YACjC;UACF,KAAK,GAAG;YAAE;YACRH,GAAG,GAAGS,MAAM,CAACE,UAAU,CAACR,OAAO,CAAC,CAAC,CAAC,CAACS,OAAO,CAACV,SAAS,IAAI,CAAC,CAAC,CAAC;YAC3DN,MAAM,IAAIK,WAAW,GAAGD,GAAG,GAAGA,GAAG,CAACa,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YACnD;UACF,KAAK,GAAG;YAAE;YACRjB,MAAM,IAAIkB,IAAI,CAACC,SAAS,CAACZ,OAAO,CAAC,CAAC,CAAC;YACnC;UACF,KAAK,GAAG;YAAE;YACRP,MAAM,IAAI,GAAG,GAAGW,QAAQ,CAACJ,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;YACnD;UACF,KAAK,GAAG;YAAE;YACRZ,MAAM,IAAIO,OAAO,CAAC,CAAC;YACnB;UACF,KAAK,GAAG;YAAE;YACRP,MAAM,IAAI,IAAI,GAAGW,QAAQ,CAACJ,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC;YACrD;UACF,KAAK,GAAG;YAAE;YACRZ,MAAM,IAAI,IAAI,GAAGW,QAAQ,CAACJ,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC,CAACQ,WAAW,CAAC,CAAC;YACnE;UACF;YACEpB,MAAM,IAAIC,CAAC;YACX;QACF;MACF,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;QACpBC,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM;QACLF,MAAM,IAAIC,CAAC;MACb;IACF;IACA,OAAOD,MAAM;EACf;AAEF,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}