{"ast": null, "code": "export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"none\",\n    \"fontFamily\": \"\\\"Fira Code\\\", Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"#2E3440\",\n    \"fontFamily\": \"\\\"Fira Code\\\", Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\",\n    \"borderRadius\": \"0.3em\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#2E3440\",\n    \"padding\": \".1em\",\n    \"borderRadius\": \".3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"#636f88\"\n  },\n  \"prolog\": {\n    \"color\": \"#636f88\"\n  },\n  \"doctype\": {\n    \"color\": \"#636f88\"\n  },\n  \"cdata\": {\n    \"color\": \"#636f88\"\n  },\n  \"punctuation\": {\n    \"color\": \"#81A1C1\"\n  },\n  \".namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"tag\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"constant\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"symbol\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"deleted\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"number\": {\n    \"color\": \"#B48EAD\"\n  },\n  \"boolean\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"selector\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"attr-name\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"string\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"char\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"builtin\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"inserted\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"operator\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"entity\": {\n    \"color\": \"#81A1C1\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"#81A1C1\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"#81A1C1\"\n  },\n  \".style .token.string\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"variable\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"atrule\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"attr-value\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"function\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"class-name\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"keyword\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"regex\": {\n    \"color\": \"#EBCB8B\"\n  },\n  \"important\": {\n    \"color\": \"#EBCB8B\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  }\n};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/styles/prism/nord.js"], "sourcesContent": ["export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"none\",\n    \"fontFamily\": \"\\\"Fira Code\\\", Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"#2E3440\",\n    \"fontFamily\": \"\\\"Fira Code\\\", Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\",\n    \"borderRadius\": \"0.3em\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#2E3440\",\n    \"padding\": \".1em\",\n    \"borderRadius\": \".3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"#636f88\"\n  },\n  \"prolog\": {\n    \"color\": \"#636f88\"\n  },\n  \"doctype\": {\n    \"color\": \"#636f88\"\n  },\n  \"cdata\": {\n    \"color\": \"#636f88\"\n  },\n  \"punctuation\": {\n    \"color\": \"#81A1C1\"\n  },\n  \".namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"tag\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"constant\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"symbol\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"deleted\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"number\": {\n    \"color\": \"#B48EAD\"\n  },\n  \"boolean\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"selector\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"attr-name\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"string\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"char\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"builtin\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"inserted\": {\n    \"color\": \"#A3BE8C\"\n  },\n  \"operator\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"entity\": {\n    \"color\": \"#81A1C1\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"#81A1C1\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"#81A1C1\"\n  },\n  \".style .token.string\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"variable\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"atrule\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"attr-value\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"function\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"class-name\": {\n    \"color\": \"#88C0D0\"\n  },\n  \"keyword\": {\n    \"color\": \"#81A1C1\"\n  },\n  \"regex\": {\n    \"color\": \"#EBCB8B\"\n  },\n  \"important\": {\n    \"color\": \"#EBCB8B\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  }\n};"], "mappings": "AAAA,eAAe;EACb,4BAA4B,EAAE;IAC5B,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE,0EAA0E;IACxF,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE;EACb,CAAC;EACD,2BAA2B,EAAE;IAC3B,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,SAAS;IACvB,YAAY,EAAE,0EAA0E;IACxF,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE,MAAM;IAClB,cAAc,EAAE;EAClB,CAAC;EACD,wCAAwC,EAAE;IACxC,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,MAAM;IACjB,cAAc,EAAE,MAAM;IACtB,YAAY,EAAE;EAChB,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,SAAS,EAAE;EACb,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACN,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE;EACZ,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,6BAA6B,EAAE;IAC7B,OAAO,EAAE;EACX,CAAC;EACD,sBAAsB,EAAE;IACtB,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACN,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,WAAW,EAAE;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}