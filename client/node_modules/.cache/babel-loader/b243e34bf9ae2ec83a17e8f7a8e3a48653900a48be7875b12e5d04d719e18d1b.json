{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(', re, ')*');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/** @type LanguageFn */\nfunction gams(hljs) {\n  const KEYWORDS = {\n    keyword: 'abort acronym acronyms alias all and assign binary card diag display ' + 'else eq file files for free ge gt if integer le loop lt maximizing ' + 'minimizing model models ne negative no not option options or ord ' + 'positive prod put putpage puttl repeat sameas semicont semiint smax ' + 'smin solve sos1 sos2 sum system table then until using while xor yes',\n    literal: 'eps inf na',\n    built_in: 'abs arccos arcsin arctan arctan2 Beta betaReg binomial ceil centropy ' + 'cos cosh cvPower div div0 eDist entropy errorf execSeed exp fact ' + 'floor frac gamma gammaReg log logBeta logGamma log10 log2 mapVal max ' + 'min mod ncpCM ncpF ncpVUpow ncpVUsin normal pi poly power ' + 'randBinomial randLinear randTriangle round rPower sigmoid sign ' + 'signPower sin sinh slexp sllog10 slrec sqexp sqlog10 sqr sqrec sqrt ' + 'tan tanh trunc uniform uniformInt vcPower bool_and bool_eqv bool_imp ' + 'bool_not bool_or bool_xor ifThen rel_eq rel_ge rel_gt rel_le rel_lt ' + 'rel_ne gday gdow ghour gleap gmillisec gminute gmonth gsecond gyear ' + 'jdate jnow jstart jtime errorLevel execError gamsRelease gamsVersion ' + 'handleCollect handleDelete handleStatus handleSubmit heapFree ' + 'heapLimit heapSize jobHandle jobKill jobStatus jobTerminate ' + 'licenseLevel licenseStatus maxExecError sleep timeClose timeComp ' + 'timeElapsed timeExec timeStart'\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true\n  };\n  const SYMBOLS = {\n    className: 'symbol',\n    variants: [{\n      begin: /=[lgenxc]=/\n    }, {\n      begin: /\\$/\n    }]\n  };\n  const QSTR = {\n    // One-line quoted comment string\n    className: 'comment',\n    variants: [{\n      begin: '\\'',\n      end: '\\''\n    }, {\n      begin: '\"',\n      end: '\"'\n    }],\n    illegal: '\\\\n',\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const ASSIGNMENT = {\n    begin: '/',\n    end: '/',\n    keywords: KEYWORDS,\n    contains: [QSTR, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, hljs.C_NUMBER_MODE]\n  };\n  const COMMENT_WORD = /[a-z0-9&#*=?@\\\\><:,()$[\\]_.{}!+%^-]+/;\n  const DESCTEXT = {\n    // Parameter/set/variable description text\n    begin: /[a-z][a-z0-9_]*(\\([a-z0-9_, ]*\\))?[ \\t]+/,\n    excludeBegin: true,\n    end: '$',\n    endsWithParent: true,\n    contains: [QSTR, ASSIGNMENT, {\n      className: 'comment',\n      // one comment word, then possibly more\n      begin: concat(COMMENT_WORD,\n      // [ ] because \\s would be too broad (matching newlines)\n      anyNumberOfTimes(concat(/[ ]+/, COMMENT_WORD))),\n      relevance: 0\n    }]\n  };\n  return {\n    name: 'GAMS',\n    aliases: ['gms'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: [hljs.COMMENT(/^\\$ontext/, /^\\$offtext/), {\n      className: 'meta',\n      begin: '^\\\\$[a-z0-9]+',\n      end: '$',\n      returnBegin: true,\n      contains: [{\n        className: 'meta-keyword',\n        begin: '^\\\\$[a-z0-9]+'\n      }]\n    }, hljs.COMMENT('^\\\\*', '$'), hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE,\n    // Declarations\n    {\n      beginKeywords: 'set sets parameter parameters variable variables ' + 'scalar scalars equation equations',\n      end: ';',\n      contains: [hljs.COMMENT('^\\\\*', '$'), hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, ASSIGNMENT, DESCTEXT]\n    }, {\n      // table environment\n      beginKeywords: 'table',\n      end: ';',\n      returnBegin: true,\n      contains: [{\n        // table header row\n        beginKeywords: 'table',\n        end: '$',\n        contains: [DESCTEXT]\n      }, hljs.COMMENT('^\\\\*', '$'), hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, hljs.C_NUMBER_MODE\n      // Table does not contain DESCTEXT or ASSIGNMENT\n      ]\n    },\n    // Function definitions\n    {\n      className: 'function',\n      begin: /^[a-z][a-z0-9_,\\-+' ()$]+\\.{2}/,\n      returnBegin: true,\n      contains: [{\n        // Function title\n        className: 'title',\n        begin: /^[a-z0-9_]+/\n      }, PARAMS, SYMBOLS]\n    }, hljs.C_NUMBER_MODE, SYMBOLS]\n  };\n}\nmodule.exports = gams;", "map": {"version": 3, "names": ["source", "re", "anyNumberOfTimes", "concat", "args", "joined", "map", "x", "join", "gams", "hljs", "KEYWORDS", "keyword", "literal", "built_in", "PARAMS", "className", "begin", "end", "excludeBegin", "excludeEnd", "SYMBOLS", "variants", "QSTR", "illegal", "contains", "BACKSLASH_ESCAPE", "ASSIGNMENT", "keywords", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "C_NUMBER_MODE", "COMMENT_WORD", "DESCTEXT", "endsWithParent", "relevance", "name", "aliases", "case_insensitive", "COMMENT", "returnBegin", "beginKeywords", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/gams.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(', re, ')*');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/** @type LanguageFn */\nfunction gams(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'abort acronym acronyms alias all and assign binary card diag display ' +\n      'else eq file files for free ge gt if integer le loop lt maximizing ' +\n      'minimizing model models ne negative no not option options or ord ' +\n      'positive prod put putpage puttl repeat sameas semicont semiint smax ' +\n      'smin solve sos1 sos2 sum system table then until using while xor yes',\n    literal:\n      'eps inf na',\n    built_in:\n      'abs arccos arcsin arctan arctan2 Beta betaReg binomial ceil centropy ' +\n      'cos cosh cvPower div div0 eDist entropy errorf execSeed exp fact ' +\n      'floor frac gamma gammaReg log logBeta logGamma log10 log2 mapVal max ' +\n      'min mod ncpCM ncpF ncpVUpow ncpVUsin normal pi poly power ' +\n      'randBinomial randLinear randTriangle round rPower sigmoid sign ' +\n      'signPower sin sinh slexp sllog10 slrec sqexp sqlog10 sqr sqrec sqrt ' +\n      'tan tanh trunc uniform uniformInt vcPower bool_and bool_eqv bool_imp ' +\n      'bool_not bool_or bool_xor ifThen rel_eq rel_ge rel_gt rel_le rel_lt ' +\n      'rel_ne gday gdow ghour gleap gmillisec gminute gmonth gsecond gyear ' +\n      'jdate jnow jstart jtime errorLevel execError gamsRelease gamsVersion ' +\n      'handleCollect handleDelete handleStatus handleSubmit heapFree ' +\n      'heapLimit heapSize jobHandle jobKill jobStatus jobTerminate ' +\n      'licenseLevel licenseStatus maxExecError sleep timeClose timeComp ' +\n      'timeElapsed timeExec timeStart'\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true\n  };\n  const SYMBOLS = {\n    className: 'symbol',\n    variants: [\n      {\n        begin: /=[lgenxc]=/\n      },\n      {\n        begin: /\\$/\n      }\n    ]\n  };\n  const QSTR = { // One-line quoted comment string\n    className: 'comment',\n    variants: [\n      {\n        begin: '\\'',\n        end: '\\''\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      }\n    ],\n    illegal: '\\\\n',\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const ASSIGNMENT = {\n    begin: '/',\n    end: '/',\n    keywords: KEYWORDS,\n    contains: [\n      QSTR,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n  const COMMENT_WORD = /[a-z0-9&#*=?@\\\\><:,()$[\\]_.{}!+%^-]+/;\n  const DESCTEXT = { // Parameter/set/variable description text\n    begin: /[a-z][a-z0-9_]*(\\([a-z0-9_, ]*\\))?[ \\t]+/,\n    excludeBegin: true,\n    end: '$',\n    endsWithParent: true,\n    contains: [\n      QSTR,\n      ASSIGNMENT,\n      {\n        className: 'comment',\n        // one comment word, then possibly more\n        begin: concat(\n          COMMENT_WORD,\n          // [ ] because \\s would be too broad (matching newlines)\n          anyNumberOfTimes(concat(/[ ]+/, COMMENT_WORD))\n        ),\n        relevance: 0\n      }\n    ]\n  };\n\n  return {\n    name: 'GAMS',\n    aliases: ['gms'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: [\n      hljs.COMMENT(/^\\$ontext/, /^\\$offtext/),\n      {\n        className: 'meta',\n        begin: '^\\\\$[a-z0-9]+',\n        end: '$',\n        returnBegin: true,\n        contains: [\n          {\n            className: 'meta-keyword',\n            begin: '^\\\\$[a-z0-9]+'\n          }\n        ]\n      },\n      hljs.COMMENT('^\\\\*', '$'),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      // Declarations\n      {\n        beginKeywords:\n          'set sets parameter parameters variable variables ' +\n          'scalar scalars equation equations',\n        end: ';',\n        contains: [\n          hljs.COMMENT('^\\\\*', '$'),\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          ASSIGNMENT,\n          DESCTEXT\n        ]\n      },\n      { // table environment\n        beginKeywords: 'table',\n        end: ';',\n        returnBegin: true,\n        contains: [\n          { // table header row\n            beginKeywords: 'table',\n            end: '$',\n            contains: [DESCTEXT]\n          },\n          hljs.COMMENT('^\\\\*', '$'),\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          hljs.C_NUMBER_MODE\n          // Table does not contain DESCTEXT or ASSIGNMENT\n        ]\n      },\n      // Function definitions\n      {\n        className: 'function',\n        begin: /^[a-z][a-z0-9_,\\-+' ()$]+\\.{2}/,\n        returnBegin: true,\n        contains: [\n          { // Function title\n            className: 'title',\n            begin: /^[a-z0-9_]+/\n          },\n          PARAMS,\n          SYMBOLS\n        ]\n      },\n      hljs.C_NUMBER_MODE,\n      SYMBOLS\n    ]\n  };\n}\n\nmodule.exports = gams;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACD,EAAE,EAAE;EAC5B,OAAOE,MAAM,CAAC,GAAG,EAAEF,EAAE,EAAE,IAAI,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA,SAASI,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,QAAQ,GAAG;IACfC,OAAO,EACL,uEAAuE,GACvE,qEAAqE,GACrE,mEAAmE,GACnE,sEAAsE,GACtE,sEAAsE;IACxEC,OAAO,EACL,YAAY;IACdC,QAAQ,EACN,uEAAuE,GACvE,mEAAmE,GACnE,uEAAuE,GACvE,4DAA4D,GAC5D,iEAAiE,GACjE,sEAAsE,GACtE,uEAAuE,GACvE,sEAAsE,GACtE,sEAAsE,GACtE,uEAAuE,GACvE,gEAAgE,GAChE,8DAA8D,GAC9D,mEAAmE,GACnE;EACJ,CAAC;EACD,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE;EACd,CAAC;EACD,MAAMC,OAAO,GAAG;IACdL,SAAS,EAAE,QAAQ;IACnBM,QAAQ,EAAE,CACR;MACEL,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMM,IAAI,GAAG;IAAE;IACbP,SAAS,EAAE,SAAS;IACpBM,QAAQ,EAAE,CACR;MACEL,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,CACF;IACDM,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,CAACf,IAAI,CAACgB,gBAAgB;EAClC,CAAC;EACD,MAAMC,UAAU,GAAG;IACjBV,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRU,QAAQ,EAAEjB,QAAQ;IAClBc,QAAQ,EAAE,CACRF,IAAI,EACJb,IAAI,CAACmB,mBAAmB,EACxBnB,IAAI,CAACoB,oBAAoB,EACzBpB,IAAI,CAACqB,iBAAiB,EACtBrB,IAAI,CAACsB,gBAAgB,EACrBtB,IAAI,CAACuB,aAAa;EAEtB,CAAC;EACD,MAAMC,YAAY,GAAG,sCAAsC;EAC3D,MAAMC,QAAQ,GAAG;IAAE;IACjBlB,KAAK,EAAE,0CAA0C;IACjDE,YAAY,EAAE,IAAI;IAClBD,GAAG,EAAE,GAAG;IACRkB,cAAc,EAAE,IAAI;IACpBX,QAAQ,EAAE,CACRF,IAAI,EACJI,UAAU,EACV;MACEX,SAAS,EAAE,SAAS;MACpB;MACAC,KAAK,EAAEd,MAAM,CACX+B,YAAY;MACZ;MACAhC,gBAAgB,CAACC,MAAM,CAAC,MAAM,EAAE+B,YAAY,CAAC,CAC/C,CAAC;MACDG,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,gBAAgB,EAAE,IAAI;IACtBZ,QAAQ,EAAEjB,QAAQ;IAClBc,QAAQ,EAAE,CACRf,IAAI,CAAC+B,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,EACvC;MACEzB,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,eAAe;MACtBC,GAAG,EAAE,GAAG;MACRwB,WAAW,EAAE,IAAI;MACjBjB,QAAQ,EAAE,CACR;QACET,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACDP,IAAI,CAAC+B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EACzB/B,IAAI,CAACmB,mBAAmB,EACxBnB,IAAI,CAACoB,oBAAoB,EACzBpB,IAAI,CAACqB,iBAAiB,EACtBrB,IAAI,CAACsB,gBAAgB;IACrB;IACA;MACEW,aAAa,EACX,mDAAmD,GACnD,mCAAmC;MACrCzB,GAAG,EAAE,GAAG;MACRO,QAAQ,EAAE,CACRf,IAAI,CAAC+B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EACzB/B,IAAI,CAACmB,mBAAmB,EACxBnB,IAAI,CAACoB,oBAAoB,EACzBpB,IAAI,CAACqB,iBAAiB,EACtBrB,IAAI,CAACsB,gBAAgB,EACrBL,UAAU,EACVQ,QAAQ;IAEZ,CAAC,EACD;MAAE;MACAQ,aAAa,EAAE,OAAO;MACtBzB,GAAG,EAAE,GAAG;MACRwB,WAAW,EAAE,IAAI;MACjBjB,QAAQ,EAAE,CACR;QAAE;QACAkB,aAAa,EAAE,OAAO;QACtBzB,GAAG,EAAE,GAAG;QACRO,QAAQ,EAAE,CAACU,QAAQ;MACrB,CAAC,EACDzB,IAAI,CAAC+B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EACzB/B,IAAI,CAACmB,mBAAmB,EACxBnB,IAAI,CAACoB,oBAAoB,EACzBpB,IAAI,CAACqB,iBAAiB,EACtBrB,IAAI,CAACsB,gBAAgB,EACrBtB,IAAI,CAACuB;MACL;MAAA;IAEJ,CAAC;IACD;IACA;MACEjB,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,gCAAgC;MACvCyB,WAAW,EAAE,IAAI;MACjBjB,QAAQ,EAAE,CACR;QAAE;QACAT,SAAS,EAAE,OAAO;QAClBC,KAAK,EAAE;MACT,CAAC,EACDF,MAAM,EACNM,OAAO;IAEX,CAAC,EACDX,IAAI,CAACuB,aAAa,EAClBZ,OAAO;EAEX,CAAC;AACH;AAEAuB,MAAM,CAACC,OAAO,GAAGpC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}