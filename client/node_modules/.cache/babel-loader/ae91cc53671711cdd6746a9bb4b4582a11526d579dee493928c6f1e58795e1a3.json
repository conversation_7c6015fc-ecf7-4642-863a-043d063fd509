{"ast": null, "code": "'use strict';\n\nmodule.exports = markupTemplating;\nmarkupTemplating.displayName = 'markupTemplating';\nmarkupTemplating.aliases = [];\nfunction markupTemplating(Prism) {\n  ;\n  (function (Prism) {\n    /**\n     * Returns the placeholder for the given language id and index.\n     *\n     * @param {string} language\n     * @param {string|number} index\n     * @returns {string}\n     */\n    function getPlaceholder(language, index) {\n      return '___' + language.toUpperCase() + index + '___';\n    }\n    Object.defineProperties(Prism.languages['markup-templating'] = {}, {\n      buildPlaceholders: {\n        /**\n         * Tokenize all inline templating expressions matching `placeholderPattern`.\n         *\n         * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n         * `true` will be replaced.\n         *\n         * @param {object} env The environment of the `before-tokenize` hook.\n         * @param {string} language The language id.\n         * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n         * @param {(match: string) => boolean} [replaceFilter]\n         */\n        value: function (env, language, placeholderPattern, replaceFilter) {\n          if (env.language !== language) {\n            return;\n          }\n          var tokenStack = env.tokenStack = [];\n          env.code = env.code.replace(placeholderPattern, function (match) {\n            if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n              return match;\n            }\n            var i = tokenStack.length;\n            var placeholder; // Check for existing strings\n            while (env.code.indexOf(placeholder = getPlaceholder(language, i)) !== -1) {\n              ++i;\n            } // Create a sparse array\n            tokenStack[i] = match;\n            return placeholder;\n          }); // Switch the grammar to markup\n          env.grammar = Prism.languages.markup;\n        }\n      },\n      tokenizePlaceholders: {\n        /**\n         * Replace placeholders with proper tokens after tokenizing.\n         *\n         * @param {object} env The environment of the `after-tokenize` hook.\n         * @param {string} language The language id.\n         */\n        value: function (env, language) {\n          if (env.language !== language || !env.tokenStack) {\n            return;\n          } // Switch the grammar back\n          env.grammar = Prism.languages[language];\n          var j = 0;\n          var keys = Object.keys(env.tokenStack);\n          function walkTokens(tokens) {\n            for (var i = 0; i < tokens.length; i++) {\n              // all placeholders are replaced already\n              if (j >= keys.length) {\n                break;\n              }\n              var token = tokens[i];\n              if (typeof token === 'string' || token.content && typeof token.content === 'string') {\n                var k = keys[j];\n                var t = env.tokenStack[k];\n                var s = typeof token === 'string' ? token : token.content;\n                var placeholder = getPlaceholder(language, k);\n                var index = s.indexOf(placeholder);\n                if (index > -1) {\n                  ++j;\n                  var before = s.substring(0, index);\n                  var middle = new Prism.Token(language, Prism.tokenize(t, env.grammar), 'language-' + language, t);\n                  var after = s.substring(index + placeholder.length);\n                  var replacement = [];\n                  if (before) {\n                    replacement.push.apply(replacement, walkTokens([before]));\n                  }\n                  replacement.push(middle);\n                  if (after) {\n                    replacement.push.apply(replacement, walkTokens([after]));\n                  }\n                  if (typeof token === 'string') {\n                    tokens.splice.apply(tokens, [i, 1].concat(replacement));\n                  } else {\n                    token.content = replacement;\n                  }\n                }\n              } else if (token.content\n              /* && typeof token.content !== 'string' */) {\n                walkTokens(token.content);\n              }\n            }\n            return tokens;\n          }\n          walkTokens(env.tokens);\n        }\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "markupTemplating", "displayName", "aliases", "Prism", "getPlaceholder", "language", "index", "toUpperCase", "Object", "defineProperties", "languages", "buildPlaceholders", "value", "env", "placeholder<PERSON><PERSON><PERSON>", "replaceFilter", "tokenStack", "code", "replace", "match", "i", "length", "placeholder", "indexOf", "grammar", "markup", "tokenizePlaceholders", "j", "keys", "walkTokens", "tokens", "token", "content", "k", "t", "s", "before", "substring", "middle", "Token", "tokenize", "after", "replacement", "push", "apply", "splice", "concat"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/markup-templating.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = markupTemplating\nmarkupTemplating.displayName = 'markupTemplating'\nmarkupTemplating.aliases = []\nfunction markupTemplating(Prism) {\n  ;(function (Prism) {\n    /**\n     * Returns the placeholder for the given language id and index.\n     *\n     * @param {string} language\n     * @param {string|number} index\n     * @returns {string}\n     */\n    function getPlaceholder(language, index) {\n      return '___' + language.toUpperCase() + index + '___'\n    }\n    Object.defineProperties((Prism.languages['markup-templating'] = {}), {\n      buildPlaceholders: {\n        /**\n         * Tokenize all inline templating expressions matching `placeholderPattern`.\n         *\n         * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n         * `true` will be replaced.\n         *\n         * @param {object} env The environment of the `before-tokenize` hook.\n         * @param {string} language The language id.\n         * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n         * @param {(match: string) => boolean} [replaceFilter]\n         */\n        value: function (env, language, placeholderPattern, replaceFilter) {\n          if (env.language !== language) {\n            return\n          }\n          var tokenStack = (env.tokenStack = [])\n          env.code = env.code.replace(placeholderPattern, function (match) {\n            if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n              return match\n            }\n            var i = tokenStack.length\n            var placeholder // Check for existing strings\n            while (\n              env.code.indexOf((placeholder = getPlaceholder(language, i))) !==\n              -1\n            ) {\n              ++i\n            } // Create a sparse array\n            tokenStack[i] = match\n            return placeholder\n          }) // Switch the grammar to markup\n          env.grammar = Prism.languages.markup\n        }\n      },\n      tokenizePlaceholders: {\n        /**\n         * Replace placeholders with proper tokens after tokenizing.\n         *\n         * @param {object} env The environment of the `after-tokenize` hook.\n         * @param {string} language The language id.\n         */\n        value: function (env, language) {\n          if (env.language !== language || !env.tokenStack) {\n            return\n          } // Switch the grammar back\n          env.grammar = Prism.languages[language]\n          var j = 0\n          var keys = Object.keys(env.tokenStack)\n          function walkTokens(tokens) {\n            for (var i = 0; i < tokens.length; i++) {\n              // all placeholders are replaced already\n              if (j >= keys.length) {\n                break\n              }\n              var token = tokens[i]\n              if (\n                typeof token === 'string' ||\n                (token.content && typeof token.content === 'string')\n              ) {\n                var k = keys[j]\n                var t = env.tokenStack[k]\n                var s = typeof token === 'string' ? token : token.content\n                var placeholder = getPlaceholder(language, k)\n                var index = s.indexOf(placeholder)\n                if (index > -1) {\n                  ++j\n                  var before = s.substring(0, index)\n                  var middle = new Prism.Token(\n                    language,\n                    Prism.tokenize(t, env.grammar),\n                    'language-' + language,\n                    t\n                  )\n                  var after = s.substring(index + placeholder.length)\n                  var replacement = []\n                  if (before) {\n                    replacement.push.apply(replacement, walkTokens([before]))\n                  }\n                  replacement.push(middle)\n                  if (after) {\n                    replacement.push.apply(replacement, walkTokens([after]))\n                  }\n                  if (typeof token === 'string') {\n                    tokens.splice.apply(tokens, [i, 1].concat(replacement))\n                  } else {\n                    token.content = replacement\n                  }\n                }\n              } else if (\n                token.content\n                /* && typeof token.content !== 'string' */\n              ) {\n                walkTokens(token.content)\n              }\n            }\n            return tokens\n          }\n          walkTokens(env.tokens)\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,gBAAgB;AACjCA,gBAAgB,CAACC,WAAW,GAAG,kBAAkB;AACjDD,gBAAgB,CAACE,OAAO,GAAG,EAAE;AAC7B,SAASF,gBAAgBA,CAACG,KAAK,EAAE;EAC/B;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,cAAcA,CAACC,QAAQ,EAAEC,KAAK,EAAE;MACvC,OAAO,KAAK,GAAGD,QAAQ,CAACE,WAAW,CAAC,CAAC,GAAGD,KAAK,GAAG,KAAK;IACvD;IACAE,MAAM,CAACC,gBAAgB,CAAEN,KAAK,CAACO,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,EAAG;MACnEC,iBAAiB,EAAE;QACjB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACQC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAER,QAAQ,EAAES,kBAAkB,EAAEC,aAAa,EAAE;UACjE,IAAIF,GAAG,CAACR,QAAQ,KAAKA,QAAQ,EAAE;YAC7B;UACF;UACA,IAAIW,UAAU,GAAIH,GAAG,CAACG,UAAU,GAAG,EAAG;UACtCH,GAAG,CAACI,IAAI,GAAGJ,GAAG,CAACI,IAAI,CAACC,OAAO,CAACJ,kBAAkB,EAAE,UAAUK,KAAK,EAAE;YAC/D,IAAI,OAAOJ,aAAa,KAAK,UAAU,IAAI,CAACA,aAAa,CAACI,KAAK,CAAC,EAAE;cAChE,OAAOA,KAAK;YACd;YACA,IAAIC,CAAC,GAAGJ,UAAU,CAACK,MAAM;YACzB,IAAIC,WAAW,EAAC;YAChB,OACET,GAAG,CAACI,IAAI,CAACM,OAAO,CAAED,WAAW,GAAGlB,cAAc,CAACC,QAAQ,EAAEe,CAAC,CAAE,CAAC,KAC7D,CAAC,CAAC,EACF;cACA,EAAEA,CAAC;YACL,CAAC,CAAC;YACFJ,UAAU,CAACI,CAAC,CAAC,GAAGD,KAAK;YACrB,OAAOG,WAAW;UACpB,CAAC,CAAC,EAAC;UACHT,GAAG,CAACW,OAAO,GAAGrB,KAAK,CAACO,SAAS,CAACe,MAAM;QACtC;MACF,CAAC;MACDC,oBAAoB,EAAE;QACpB;AACR;AACA;AACA;AACA;AACA;QACQd,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAER,QAAQ,EAAE;UAC9B,IAAIQ,GAAG,CAACR,QAAQ,KAAKA,QAAQ,IAAI,CAACQ,GAAG,CAACG,UAAU,EAAE;YAChD;UACF,CAAC,CAAC;UACFH,GAAG,CAACW,OAAO,GAAGrB,KAAK,CAACO,SAAS,CAACL,QAAQ,CAAC;UACvC,IAAIsB,CAAC,GAAG,CAAC;UACT,IAAIC,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAACf,GAAG,CAACG,UAAU,CAAC;UACtC,SAASa,UAAUA,CAACC,MAAM,EAAE;YAC1B,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,MAAM,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;cACtC;cACA,IAAIO,CAAC,IAAIC,IAAI,CAACP,MAAM,EAAE;gBACpB;cACF;cACA,IAAIU,KAAK,GAAGD,MAAM,CAACV,CAAC,CAAC;cACrB,IACE,OAAOW,KAAK,KAAK,QAAQ,IACxBA,KAAK,CAACC,OAAO,IAAI,OAAOD,KAAK,CAACC,OAAO,KAAK,QAAS,EACpD;gBACA,IAAIC,CAAC,GAAGL,IAAI,CAACD,CAAC,CAAC;gBACf,IAAIO,CAAC,GAAGrB,GAAG,CAACG,UAAU,CAACiB,CAAC,CAAC;gBACzB,IAAIE,CAAC,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAACC,OAAO;gBACzD,IAAIV,WAAW,GAAGlB,cAAc,CAACC,QAAQ,EAAE4B,CAAC,CAAC;gBAC7C,IAAI3B,KAAK,GAAG6B,CAAC,CAACZ,OAAO,CAACD,WAAW,CAAC;gBAClC,IAAIhB,KAAK,GAAG,CAAC,CAAC,EAAE;kBACd,EAAEqB,CAAC;kBACH,IAAIS,MAAM,GAAGD,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE/B,KAAK,CAAC;kBAClC,IAAIgC,MAAM,GAAG,IAAInC,KAAK,CAACoC,KAAK,CAC1BlC,QAAQ,EACRF,KAAK,CAACqC,QAAQ,CAACN,CAAC,EAAErB,GAAG,CAACW,OAAO,CAAC,EAC9B,WAAW,GAAGnB,QAAQ,EACtB6B,CACF,CAAC;kBACD,IAAIO,KAAK,GAAGN,CAAC,CAACE,SAAS,CAAC/B,KAAK,GAAGgB,WAAW,CAACD,MAAM,CAAC;kBACnD,IAAIqB,WAAW,GAAG,EAAE;kBACpB,IAAIN,MAAM,EAAE;oBACVM,WAAW,CAACC,IAAI,CAACC,KAAK,CAACF,WAAW,EAAEb,UAAU,CAAC,CAACO,MAAM,CAAC,CAAC,CAAC;kBAC3D;kBACAM,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC;kBACxB,IAAIG,KAAK,EAAE;oBACTC,WAAW,CAACC,IAAI,CAACC,KAAK,CAACF,WAAW,EAAEb,UAAU,CAAC,CAACY,KAAK,CAAC,CAAC,CAAC;kBAC1D;kBACA,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;oBAC7BD,MAAM,CAACe,MAAM,CAACD,KAAK,CAACd,MAAM,EAAE,CAACV,CAAC,EAAE,CAAC,CAAC,CAAC0B,MAAM,CAACJ,WAAW,CAAC,CAAC;kBACzD,CAAC,MAAM;oBACLX,KAAK,CAACC,OAAO,GAAGU,WAAW;kBAC7B;gBACF;cACF,CAAC,MAAM,IACLX,KAAK,CAACC;cACN,4CACA;gBACAH,UAAU,CAACE,KAAK,CAACC,OAAO,CAAC;cAC3B;YACF;YACA,OAAOF,MAAM;UACf;UACAD,UAAU,CAAChB,GAAG,CAACiB,MAAM,CAAC;QACxB;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE3B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}