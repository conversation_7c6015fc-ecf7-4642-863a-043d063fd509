{"ast": null, "code": "'use strict';\n\nmodule.exports = inform7;\ninform7.displayName = 'inform7';\ninform7.aliases = [];\nfunction inform7(Prism) {\n  Prism.languages.inform7 = {\n    string: {\n      pattern: /\"[^\"]*\"/,\n      inside: {\n        substitution: {\n          pattern: /\\[[^\\[\\]]+\\]/,\n          inside: {\n            delimiter: {\n              pattern: /\\[|\\]/,\n              alias: 'punctuation'\n            } // See rest below\n          }\n        }\n      }\n    },\n    comment: {\n      pattern: /\\[[^\\[\\]]+\\]/,\n      greedy: true\n    },\n    title: {\n      pattern: /^[ \\t]*(?:book|chapter|part(?! of)|section|table|volume)\\b.+/im,\n      alias: 'important'\n    },\n    number: {\n      pattern: /(^|[^-])(?:\\b\\d+(?:\\.\\d+)?(?:\\^\\d+)?(?:(?!\\d)\\w+)?|\\b(?:eight|eleven|five|four|nine|one|seven|six|ten|three|twelve|two))\\b(?!-)/i,\n      lookbehind: true\n    },\n    verb: {\n      pattern: /(^|[^-])\\b(?:answering|applying to|are|asking|attacking|be(?:ing)?|burning|buying|called|carries|carry(?! out)|carrying|climbing|closing|conceal(?:ing|s)?|consulting|contain(?:ing|s)?|cutting|drinking|dropping|eating|enclos(?:es?|ing)|entering|examining|exiting|getting|giving|going|ha(?:s|ve|ving)|hold(?:ing|s)?|impl(?:ies|y)|incorporat(?:es?|ing)|inserting|is|jumping|kissing|listening|locking|looking|mean(?:ing|s)?|opening|provid(?:es?|ing)|pulling|pushing|putting|relat(?:es?|ing)|removing|searching|see(?:ing|s)?|setting|showing|singing|sleeping|smelling|squeezing|support(?:ing|s)?|swearing|switching|taking|tasting|telling|thinking|throwing|touching|turning|tying|unlock(?:ing|s)?|var(?:ies|y|ying)|waiting|waking|waving|wear(?:ing|s)?)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    keyword: {\n      pattern: /(^|[^-])\\b(?:after|before|carry out|check|continue the action|definition(?= *:)|do nothing|else|end (?:if|the story|unless)|every turn|if|include|instead(?: of)?|let|move|no|now|otherwise|repeat|report|resume the story|rule for|running through|say(?:ing)?|stop the action|test|try(?:ing)?|understand|unless|use|when|while|yes)\\b(?!-)/i,\n      lookbehind: true\n    },\n    property: {\n      pattern: /(^|[^-])\\b(?:adjacent(?! to)|carried|closed|concealed|contained|dark|described|edible|empty|enclosed|enterable|even|female|fixed in place|full|handled|held|improper-named|incorporated|inedible|invisible|lighted|lit|lock(?:able|ed)|male|marked for listing|mentioned|negative|neuter|non-(?:empty|full|recurring)|odd|opaque|open(?:able)?|plural-named|portable|positive|privately-named|proper-named|provided|publically-named|pushable between rooms|recurring|related|rubbing|scenery|seen|singular-named|supported|swinging|switch(?:able|ed(?: off| on)?)|touch(?:able|ed)|transparent|unconcealed|undescribed|unlit|unlocked|unmarked for listing|unmentioned|unopenable|untouchable|unvisited|variable|visible|visited|wearable|worn)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    position: {\n      pattern: /(^|[^-])\\b(?:above|adjacent to|back side of|below|between|down|east|everywhere|front side|here|in|inside(?: from)?|north(?:east|west)?|nowhere|on(?: top of)?|other side|outside(?: from)?|parts? of|regionally in|south(?:east|west)?|through|up|west|within)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    type: {\n      pattern: /(^|[^-])\\b(?:actions?|activit(?:ies|y)|actors?|animals?|backdrops?|containers?|devices?|directions?|doors?|holders?|kinds?|lists?|m[ae]n|nobody|nothing|nouns?|numbers?|objects?|people|persons?|player(?:'s holdall)?|regions?|relations?|rooms?|rule(?:book)?s?|scenes?|someone|something|supporters?|tables?|texts?|things?|time|vehicles?|wom[ae]n)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    punctuation: /[.,:;(){}]/\n  };\n  Prism.languages.inform7['string'].inside['substitution'].inside.rest = Prism.languages.inform7; // We don't want the remaining text in the substitution to be highlighted as the string.\n  Prism.languages.inform7['string'].inside['substitution'].inside.rest.text = {\n    pattern: /\\S(?:\\s*\\S)*/,\n    alias: 'comment'\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "inform7", "displayName", "aliases", "Prism", "languages", "string", "pattern", "inside", "substitution", "delimiter", "alias", "comment", "greedy", "title", "number", "lookbehind", "verb", "keyword", "property", "position", "type", "punctuation", "rest", "text"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/inform7.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = inform7\ninform7.displayName = 'inform7'\ninform7.aliases = []\nfunction inform7(Prism) {\n  Prism.languages.inform7 = {\n    string: {\n      pattern: /\"[^\"]*\"/,\n      inside: {\n        substitution: {\n          pattern: /\\[[^\\[\\]]+\\]/,\n          inside: {\n            delimiter: {\n              pattern: /\\[|\\]/,\n              alias: 'punctuation'\n            } // See rest below\n          }\n        }\n      }\n    },\n    comment: {\n      pattern: /\\[[^\\[\\]]+\\]/,\n      greedy: true\n    },\n    title: {\n      pattern: /^[ \\t]*(?:book|chapter|part(?! of)|section|table|volume)\\b.+/im,\n      alias: 'important'\n    },\n    number: {\n      pattern:\n        /(^|[^-])(?:\\b\\d+(?:\\.\\d+)?(?:\\^\\d+)?(?:(?!\\d)\\w+)?|\\b(?:eight|eleven|five|four|nine|one|seven|six|ten|three|twelve|two))\\b(?!-)/i,\n      lookbehind: true\n    },\n    verb: {\n      pattern:\n        /(^|[^-])\\b(?:answering|applying to|are|asking|attacking|be(?:ing)?|burning|buying|called|carries|carry(?! out)|carrying|climbing|closing|conceal(?:ing|s)?|consulting|contain(?:ing|s)?|cutting|drinking|dropping|eating|enclos(?:es?|ing)|entering|examining|exiting|getting|giving|going|ha(?:s|ve|ving)|hold(?:ing|s)?|impl(?:ies|y)|incorporat(?:es?|ing)|inserting|is|jumping|kissing|listening|locking|looking|mean(?:ing|s)?|opening|provid(?:es?|ing)|pulling|pushing|putting|relat(?:es?|ing)|removing|searching|see(?:ing|s)?|setting|showing|singing|sleeping|smelling|squeezing|support(?:ing|s)?|swearing|switching|taking|tasting|telling|thinking|throwing|touching|turning|tying|unlock(?:ing|s)?|var(?:ies|y|ying)|waiting|waking|waving|wear(?:ing|s)?)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    keyword: {\n      pattern:\n        /(^|[^-])\\b(?:after|before|carry out|check|continue the action|definition(?= *:)|do nothing|else|end (?:if|the story|unless)|every turn|if|include|instead(?: of)?|let|move|no|now|otherwise|repeat|report|resume the story|rule for|running through|say(?:ing)?|stop the action|test|try(?:ing)?|understand|unless|use|when|while|yes)\\b(?!-)/i,\n      lookbehind: true\n    },\n    property: {\n      pattern:\n        /(^|[^-])\\b(?:adjacent(?! to)|carried|closed|concealed|contained|dark|described|edible|empty|enclosed|enterable|even|female|fixed in place|full|handled|held|improper-named|incorporated|inedible|invisible|lighted|lit|lock(?:able|ed)|male|marked for listing|mentioned|negative|neuter|non-(?:empty|full|recurring)|odd|opaque|open(?:able)?|plural-named|portable|positive|privately-named|proper-named|provided|publically-named|pushable between rooms|recurring|related|rubbing|scenery|seen|singular-named|supported|swinging|switch(?:able|ed(?: off| on)?)|touch(?:able|ed)|transparent|unconcealed|undescribed|unlit|unlocked|unmarked for listing|unmentioned|unopenable|untouchable|unvisited|variable|visible|visited|wearable|worn)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    position: {\n      pattern:\n        /(^|[^-])\\b(?:above|adjacent to|back side of|below|between|down|east|everywhere|front side|here|in|inside(?: from)?|north(?:east|west)?|nowhere|on(?: top of)?|other side|outside(?: from)?|parts? of|regionally in|south(?:east|west)?|through|up|west|within)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    type: {\n      pattern:\n        /(^|[^-])\\b(?:actions?|activit(?:ies|y)|actors?|animals?|backdrops?|containers?|devices?|directions?|doors?|holders?|kinds?|lists?|m[ae]n|nobody|nothing|nouns?|numbers?|objects?|people|persons?|player(?:'s holdall)?|regions?|relations?|rooms?|rule(?:book)?s?|scenes?|someone|something|supporters?|tables?|texts?|things?|time|vehicles?|wom[ae]n)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    punctuation: /[.,:;(){}]/\n  }\n  Prism.languages.inform7['string'].inside['substitution'].inside.rest =\n    Prism.languages.inform7 // We don't want the remaining text in the substitution to be highlighted as the string.\n  Prism.languages.inform7['string'].inside['substitution'].inside.rest.text = {\n    pattern: /\\S(?:\\s*\\S)*/,\n    alias: 'comment'\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxBK,MAAM,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;QACNC,YAAY,EAAE;UACZF,OAAO,EAAE,cAAc;UACvBC,MAAM,EAAE;YACNE,SAAS,EAAE;cACTH,OAAO,EAAE,OAAO;cAChBI,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;QACF;MACF;IACF,CAAC;IACDC,OAAO,EAAE;MACPL,OAAO,EAAE,cAAc;MACvBM,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLP,OAAO,EAAE,gEAAgE;MACzEI,KAAK,EAAE;IACT,CAAC;IACDI,MAAM,EAAE;MACNR,OAAO,EACL,kIAAkI;MACpIS,UAAU,EAAE;IACd,CAAC;IACDC,IAAI,EAAE;MACJV,OAAO,EACL,mvBAAmvB;MACrvBS,UAAU,EAAE,IAAI;MAChBL,KAAK,EAAE;IACT,CAAC;IACDO,OAAO,EAAE;MACPX,OAAO,EACL,gVAAgV;MAClVS,UAAU,EAAE;IACd,CAAC;IACDG,QAAQ,EAAE;MACRZ,OAAO,EACL,2tBAA2tB;MAC7tBS,UAAU,EAAE,IAAI;MAChBL,KAAK,EAAE;IACT,CAAC;IACDS,QAAQ,EAAE;MACRb,OAAO,EACL,wQAAwQ;MAC1QS,UAAU,EAAE,IAAI;MAChBL,KAAK,EAAE;IACT,CAAC;IACDU,IAAI,EAAE;MACJd,OAAO,EACL,iWAAiW;MACnWS,UAAU,EAAE,IAAI;MAChBL,KAAK,EAAE;IACT,CAAC;IACDW,WAAW,EAAE;EACf,CAAC;EACDlB,KAAK,CAACC,SAAS,CAACJ,OAAO,CAAC,QAAQ,CAAC,CAACO,MAAM,CAAC,cAAc,CAAC,CAACA,MAAM,CAACe,IAAI,GAClEnB,KAAK,CAACC,SAAS,CAACJ,OAAO,EAAC;EAC1BG,KAAK,CAACC,SAAS,CAACJ,OAAO,CAAC,QAAQ,CAAC,CAACO,MAAM,CAAC,cAAc,CAAC,CAACA,MAAM,CAACe,IAAI,CAACC,IAAI,GAAG;IAC1EjB,OAAO,EAAE,cAAc;IACvBI,KAAK,EAAE;EACT,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}