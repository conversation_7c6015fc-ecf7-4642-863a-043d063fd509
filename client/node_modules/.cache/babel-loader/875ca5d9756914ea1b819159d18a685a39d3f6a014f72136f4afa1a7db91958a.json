{"ast": null, "code": "'use strict';\n\nmodule.exports = hsts;\nhsts.displayName = 'hsts';\nhsts.aliases = [];\nfunction hsts(Prism) {\n  /**\n   * Original by <PERSON>.\n   *\n   * Reference: https://scotthelme.co.uk/hsts-cheat-sheet/\n   */\n  Prism.languages.hsts = {\n    directive: {\n      pattern: /\\b(?:includeSubDomains|max-age|preload)(?=[\\s;=]|$)/i,\n      alias: 'property'\n    },\n    operator: /=/,\n    punctuation: /;/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "hsts", "displayName", "aliases", "Prism", "languages", "directive", "pattern", "alias", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/hsts.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = hsts\nhsts.displayName = 'hsts'\nhsts.aliases = []\nfunction hsts(Prism) {\n  /**\n   * Original by <PERSON>.\n   *\n   * Reference: https://scotthelme.co.uk/hsts-cheat-sheet/\n   */\n  Prism.languages.hsts = {\n    directive: {\n      pattern: /\\b(?:includeSubDomains|max-age|preload)(?=[\\s;=]|$)/i,\n      alias: 'property'\n    },\n    operator: /=/,\n    punctuation: /;/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;AACF;AACA;AACA;AACA;EACEA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,SAAS,EAAE;MACTC,OAAO,EAAE,sDAAsD;MAC/DC,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}