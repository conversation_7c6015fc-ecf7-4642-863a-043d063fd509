{"ast": null, "code": "'use strict';\n\nmodule.exports = unrealscript;\nunrealscript.displayName = 'unrealscript';\nunrealscript.aliases = ['uc', 'uscript'];\nfunction unrealscript(Prism) {\n  Prism.languages.unrealscript = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    category: {\n      pattern: /(\\b(?:(?:autoexpand|hide|show)categories|var)\\s*\\()[^()]+(?=\\))/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /(\\w\\s*)<\\s*\\w+\\s*=[^<>|=\\r\\n]+(?:\\|\\s*\\w+\\s*=[^<>|=\\r\\n]+)*>/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        property: /\\b\\w+(?=\\s*=)/,\n        operator: /=/,\n        punctuation: /[<>|]/\n      }\n    },\n    macro: {\n      pattern: /`\\w+/,\n      alias: 'property'\n    },\n    'class-name': {\n      pattern: /(\\b(?:class|enum|extends|interface|state(?:\\(\\))?|struct|within)\\s+)\\w+/,\n      lookbehind: true\n    },\n    keyword: /\\b(?:abstract|actor|array|auto|autoexpandcategories|bool|break|byte|case|class|classgroup|client|coerce|collapsecategories|config|const|continue|default|defaultproperties|delegate|dependson|deprecated|do|dontcollapsecategories|editconst|editinlinenew|else|enum|event|exec|export|extends|final|float|for|forcescriptorder|foreach|function|goto|guid|hidecategories|hidedropdown|if|ignores|implements|inherits|input|int|interface|iterator|latent|local|material|name|native|nativereplication|noexport|nontransient|noteditinlinenew|notplaceable|operator|optional|out|pawn|perobjectconfig|perobjectlocalized|placeable|postoperator|preoperator|private|protected|reliable|replication|return|server|showcategories|simulated|singular|state|static|string|struct|structdefault|structdefaultproperties|switch|texture|transient|travel|unreliable|until|var|vector|while|within)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    // https://docs.unrealengine.com/udk/Three/UnrealScriptExpressions.html\n    operator: />>|<<|--|\\+\\+|\\*\\*|[-+*/~!=<>$@]=?|&&?|\\|\\|?|\\^\\^?|[?:%]|\\b(?:ClockwiseFrom|Cross|Dot)\\b/,\n    punctuation: /[()[\\]{};,.]/\n  };\n  Prism.languages.uc = Prism.languages.uscript = Prism.languages.unrealscript;\n}", "map": {"version": 3, "names": ["module", "exports", "unrealscript", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "category", "lookbehind", "alias", "metadata", "inside", "property", "operator", "punctuation", "macro", "keyword", "function", "boolean", "number", "uc", "uscript"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/unrealscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = unrealscript\nunrealscript.displayName = 'unrealscript'\nunrealscript.aliases = ['uc', 'uscript']\nfunction unrealscript(Prism) {\n  Prism.languages.unrealscript = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    category: {\n      pattern:\n        /(\\b(?:(?:autoexpand|hide|show)categories|var)\\s*\\()[^()]+(?=\\))/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /(\\w\\s*)<\\s*\\w+\\s*=[^<>|=\\r\\n]+(?:\\|\\s*\\w+\\s*=[^<>|=\\r\\n]+)*>/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        property: /\\b\\w+(?=\\s*=)/,\n        operator: /=/,\n        punctuation: /[<>|]/\n      }\n    },\n    macro: {\n      pattern: /`\\w+/,\n      alias: 'property'\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:class|enum|extends|interface|state(?:\\(\\))?|struct|within)\\s+)\\w+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:abstract|actor|array|auto|autoexpandcategories|bool|break|byte|case|class|classgroup|client|coerce|collapsecategories|config|const|continue|default|defaultproperties|delegate|dependson|deprecated|do|dontcollapsecategories|editconst|editinlinenew|else|enum|event|exec|export|extends|final|float|for|forcescriptorder|foreach|function|goto|guid|hidecategories|hidedropdown|if|ignores|implements|inherits|input|int|interface|iterator|latent|local|material|name|native|nativereplication|noexport|nontransient|noteditinlinenew|notplaceable|operator|optional|out|pawn|perobjectconfig|perobjectlocalized|placeable|postoperator|preoperator|private|protected|reliable|replication|return|server|showcategories|simulated|singular|state|static|string|struct|structdefault|structdefaultproperties|switch|texture|transient|travel|unreliable|until|var|vector|while|within)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    // https://docs.unrealengine.com/udk/Three/UnrealScriptExpressions.html\n    operator:\n      />>|<<|--|\\+\\+|\\*\\*|[-+*/~!=<>$@]=?|&&?|\\|\\|?|\\^\\^?|[?:%]|\\b(?:ClockwiseFrom|Cross|Dot)\\b/,\n    punctuation: /[()[\\]{};,.]/\n  }\n  Prism.languages.uc = Prism.languages.uscript = Prism.languages.unrealscript\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;AACxC,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3BA,KAAK,CAACC,SAAS,CAACJ,YAAY,GAAG;IAC7BK,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;MACNC,OAAO,EAAE,gDAAgD;MACzDC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACRF,OAAO,EACL,iEAAiE;MACnEG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZG,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRL,OAAO,EAAE,8DAA8D;MACvEG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZK,MAAM,EAAE;QACNC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,GAAG;QACbC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,KAAK,EAAE;MACLV,OAAO,EAAE,MAAM;MACfI,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE;MACZJ,OAAO,EACL,yEAAyE;MAC3EG,UAAU,EAAE;IACd,CAAC;IACDQ,OAAO,EACL,i2BAAi2B;IACn2BC,QAAQ,EAAE,uBAAuB;IACjCC,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EAAE,2DAA2D;IACnE;IACAN,QAAQ,EACN,0FAA0F;IAC5FC,WAAW,EAAE;EACf,CAAC;EACDb,KAAK,CAACC,SAAS,CAACkB,EAAE,GAAGnB,KAAK,CAACC,SAAS,CAACmB,OAAO,GAAGpB,KAAK,CAACC,SAAS,CAACJ,YAAY;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}