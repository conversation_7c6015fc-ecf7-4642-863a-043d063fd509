{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Markdown\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://daringfireball.net/projects/markdown/\nCategory: common, markup\n*/\n\nfunction markdown(hljs) {\n  const INLINE_HTML = {\n    begin: /<\\/?[A-Za-z_]/,\n    end: '>',\n    subLanguage: 'xml',\n    relevance: 0\n  };\n  const HORIZONTAL_RULE = {\n    begin: '^[-\\\\*]{3,}',\n    end: '$'\n  };\n  const CODE = {\n    className: 'code',\n    variants: [\n    // TODO: fix to allow these to work with sublanguage also\n    {\n      begin: '(`{3,})[^`](.|\\\\n)*?\\\\1`*[ ]*'\n    }, {\n      begin: '(~{3,})[^~](.|\\\\n)*?\\\\1~*[ ]*'\n    },\n    // needed to allow markdown as a sublanguage to work\n    {\n      begin: '```',\n      end: '```+[ ]*$'\n    }, {\n      begin: '~~~',\n      end: '~~~+[ ]*$'\n    }, {\n      begin: '`.+?`'\n    }, {\n      begin: '(?=^( {4}|\\\\t))',\n      // use contains to gobble up multiple lines to allow the block to be whatever size\n      // but only have a single open/close tag vs one per line\n      contains: [{\n        begin: '^( {4}|\\\\t)',\n        end: '(\\\\n)$'\n      }],\n      relevance: 0\n    }]\n  };\n  const LIST = {\n    className: 'bullet',\n    begin: '^[ \\t]*([*+-]|(\\\\d+\\\\.))(?=\\\\s+)',\n    end: '\\\\s+',\n    excludeEnd: true\n  };\n  const LINK_REFERENCE = {\n    begin: /^\\[[^\\n]+\\]:/,\n    returnBegin: true,\n    contains: [{\n      className: 'symbol',\n      begin: /\\[/,\n      end: /\\]/,\n      excludeBegin: true,\n      excludeEnd: true\n    }, {\n      className: 'link',\n      begin: /:\\s*/,\n      end: /$/,\n      excludeBegin: true\n    }]\n  };\n  const URL_SCHEME = /[A-Za-z][A-Za-z0-9+.-]*/;\n  const LINK = {\n    variants: [\n    // too much like nested array access in so many languages\n    // to have any real relevance\n    {\n      begin: /\\[.+?\\]\\[.*?\\]/,\n      relevance: 0\n    },\n    // popular internet URLs\n    {\n      begin: /\\[.+?\\]\\(((data|javascript|mailto):|(?:http|ftp)s?:\\/\\/).*?\\)/,\n      relevance: 2\n    }, {\n      begin: concat(/\\[.+?\\]\\(/, URL_SCHEME, /:\\/\\/.*?\\)/),\n      relevance: 2\n    },\n    // relative urls\n    {\n      begin: /\\[.+?\\]\\([./?&#].*?\\)/,\n      relevance: 1\n    },\n    // whatever else, lower relevance (might not be a link at all)\n    {\n      begin: /\\[.+?\\]\\(.*?\\)/,\n      relevance: 0\n    }],\n    returnBegin: true,\n    contains: [{\n      className: 'string',\n      relevance: 0,\n      begin: '\\\\[',\n      end: '\\\\]',\n      excludeBegin: true,\n      returnEnd: true\n    }, {\n      className: 'link',\n      relevance: 0,\n      begin: '\\\\]\\\\(',\n      end: '\\\\)',\n      excludeBegin: true,\n      excludeEnd: true\n    }, {\n      className: 'symbol',\n      relevance: 0,\n      begin: '\\\\]\\\\[',\n      end: '\\\\]',\n      excludeBegin: true,\n      excludeEnd: true\n    }]\n  };\n  const BOLD = {\n    className: 'strong',\n    contains: [],\n    // defined later\n    variants: [{\n      begin: /_{2}/,\n      end: /_{2}/\n    }, {\n      begin: /\\*{2}/,\n      end: /\\*{2}/\n    }]\n  };\n  const ITALIC = {\n    className: 'emphasis',\n    contains: [],\n    // defined later\n    variants: [{\n      begin: /\\*(?!\\*)/,\n      end: /\\*/\n    }, {\n      begin: /_(?!_)/,\n      end: /_/,\n      relevance: 0\n    }]\n  };\n  BOLD.contains.push(ITALIC);\n  ITALIC.contains.push(BOLD);\n  let CONTAINABLE = [INLINE_HTML, LINK];\n  BOLD.contains = BOLD.contains.concat(CONTAINABLE);\n  ITALIC.contains = ITALIC.contains.concat(CONTAINABLE);\n  CONTAINABLE = CONTAINABLE.concat(BOLD, ITALIC);\n  const HEADER = {\n    className: 'section',\n    variants: [{\n      begin: '^#{1,6}',\n      end: '$',\n      contains: CONTAINABLE\n    }, {\n      begin: '(?=^.+?\\\\n[=-]{2,}$)',\n      contains: [{\n        begin: '^[=-]*$'\n      }, {\n        begin: '^',\n        end: \"\\\\n\",\n        contains: CONTAINABLE\n      }]\n    }]\n  };\n  const BLOCKQUOTE = {\n    className: 'quote',\n    begin: '^>\\\\s+',\n    contains: CONTAINABLE,\n    end: '$'\n  };\n  return {\n    name: 'Markdown',\n    aliases: ['md', 'mkdown', 'mkd'],\n    contains: [HEADER, INLINE_HTML, LIST, BOLD, ITALIC, BLOCKQUOTE, CODE, HORIZONTAL_RULE, LINK, LINK_REFERENCE]\n  };\n}\nmodule.exports = markdown;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "markdown", "hljs", "INLINE_HTML", "begin", "end", "subLanguage", "relevance", "HORIZONTAL_RULE", "CODE", "className", "variants", "contains", "LIST", "excludeEnd", "LINK_REFERENCE", "returnBegin", "excludeBegin", "URL_SCHEME", "LINK", "returnEnd", "BOLD", "ITALIC", "push", "CONTAINABLE", "HEADER", "BLOCKQUOTE", "name", "aliases", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/markdown.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Markdown\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://daringfireball.net/projects/markdown/\nCategory: common, markup\n*/\n\nfunction markdown(hljs) {\n  const INLINE_HTML = {\n    begin: /<\\/?[A-Za-z_]/,\n    end: '>',\n    subLanguage: 'xml',\n    relevance: 0\n  };\n  const HORIZONTAL_RULE = {\n    begin: '^[-\\\\*]{3,}',\n    end: '$'\n  };\n  const CODE = {\n    className: 'code',\n    variants: [\n      // TODO: fix to allow these to work with sublanguage also\n      {\n        begin: '(`{3,})[^`](.|\\\\n)*?\\\\1`*[ ]*'\n      },\n      {\n        begin: '(~{3,})[^~](.|\\\\n)*?\\\\1~*[ ]*'\n      },\n      // needed to allow markdown as a sublanguage to work\n      {\n        begin: '```',\n        end: '```+[ ]*$'\n      },\n      {\n        begin: '~~~',\n        end: '~~~+[ ]*$'\n      },\n      {\n        begin: '`.+?`'\n      },\n      {\n        begin: '(?=^( {4}|\\\\t))',\n        // use contains to gobble up multiple lines to allow the block to be whatever size\n        // but only have a single open/close tag vs one per line\n        contains: [\n          {\n            begin: '^( {4}|\\\\t)',\n            end: '(\\\\n)$'\n          }\n        ],\n        relevance: 0\n      }\n    ]\n  };\n  const LIST = {\n    className: 'bullet',\n    begin: '^[ \\t]*([*+-]|(\\\\d+\\\\.))(?=\\\\s+)',\n    end: '\\\\s+',\n    excludeEnd: true\n  };\n  const LINK_REFERENCE = {\n    begin: /^\\[[^\\n]+\\]:/,\n    returnBegin: true,\n    contains: [\n      {\n        className: 'symbol',\n        begin: /\\[/,\n        end: /\\]/,\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'link',\n        begin: /:\\s*/,\n        end: /$/,\n        excludeBegin: true\n      }\n    ]\n  };\n  const URL_SCHEME = /[A-Za-z][A-Za-z0-9+.-]*/;\n  const LINK = {\n    variants: [\n      // too much like nested array access in so many languages\n      // to have any real relevance\n      {\n        begin: /\\[.+?\\]\\[.*?\\]/,\n        relevance: 0\n      },\n      // popular internet URLs\n      {\n        begin: /\\[.+?\\]\\(((data|javascript|mailto):|(?:http|ftp)s?:\\/\\/).*?\\)/,\n        relevance: 2\n      },\n      {\n        begin: concat(/\\[.+?\\]\\(/, URL_SCHEME, /:\\/\\/.*?\\)/),\n        relevance: 2\n      },\n      // relative urls\n      {\n        begin: /\\[.+?\\]\\([./?&#].*?\\)/,\n        relevance: 1\n      },\n      // whatever else, lower relevance (might not be a link at all)\n      {\n        begin: /\\[.+?\\]\\(.*?\\)/,\n        relevance: 0\n      }\n    ],\n    returnBegin: true,\n    contains: [\n      {\n        className: 'string',\n        relevance: 0,\n        begin: '\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        returnEnd: true\n      },\n      {\n        className: 'link',\n        relevance: 0,\n        begin: '\\\\]\\\\(',\n        end: '\\\\)',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'symbol',\n        relevance: 0,\n        begin: '\\\\]\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n  const BOLD = {\n    className: 'strong',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /_{2}/,\n        end: /_{2}/\n      },\n      {\n        begin: /\\*{2}/,\n        end: /\\*{2}/\n      }\n    ]\n  };\n  const ITALIC = {\n    className: 'emphasis',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /\\*(?!\\*)/,\n        end: /\\*/\n      },\n      {\n        begin: /_(?!_)/,\n        end: /_/,\n        relevance: 0\n      }\n    ]\n  };\n  BOLD.contains.push(ITALIC);\n  ITALIC.contains.push(BOLD);\n\n  let CONTAINABLE = [\n    INLINE_HTML,\n    LINK\n  ];\n\n  BOLD.contains = BOLD.contains.concat(CONTAINABLE);\n  ITALIC.contains = ITALIC.contains.concat(CONTAINABLE);\n\n  CONTAINABLE = CONTAINABLE.concat(BOLD, ITALIC);\n\n  const HEADER = {\n    className: 'section',\n    variants: [\n      {\n        begin: '^#{1,6}',\n        end: '$',\n        contains: CONTAINABLE\n      },\n      {\n        begin: '(?=^.+?\\\\n[=-]{2,}$)',\n        contains: [\n          {\n            begin: '^[=-]*$'\n          },\n          {\n            begin: '^',\n            end: \"\\\\n\",\n            contains: CONTAINABLE\n          }\n        ]\n      }\n    ]\n  };\n\n  const BLOCKQUOTE = {\n    className: 'quote',\n    begin: '^>\\\\s+',\n    contains: CONTAINABLE,\n    end: '$'\n  };\n\n  return {\n    name: 'Markdown',\n    aliases: [\n      'md',\n      'mkdown',\n      'mkd'\n    ],\n    contains: [\n      HEADER,\n      INLINE_HTML,\n      LIST,\n      BOLD,\n      ITALIC,\n      BLOCKQUOTE,\n      CODE,\n      HORIZONTAL_RULE,\n      LINK,\n      LINK_REFERENCE\n    ]\n  };\n}\n\nmodule.exports = markdown;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,eAAe;IACtBC,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,eAAe,GAAG;IACtBJ,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE;EACP,CAAC;EACD,MAAMI,IAAI,GAAG;IACXC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE;IACR;IACA;MACEP,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACEA,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,iBAAiB;MACxB;MACA;MACAQ,QAAQ,EAAE,CACR;QACER,KAAK,EAAE,aAAa;QACpBC,GAAG,EAAE;MACP,CAAC,CACF;MACDE,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EACD,MAAMM,IAAI,GAAG;IACXH,SAAS,EAAE,QAAQ;IACnBN,KAAK,EAAE,kCAAkC;IACzCC,GAAG,EAAE,MAAM;IACXS,UAAU,EAAE;EACd,CAAC;EACD,MAAMC,cAAc,GAAG;IACrBX,KAAK,EAAE,cAAc;IACrBY,WAAW,EAAE,IAAI;IACjBJ,QAAQ,EAAE,CACR;MACEF,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTY,YAAY,EAAE,IAAI;MAClBH,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,SAAS,EAAE,MAAM;MACjBN,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,GAAG;MACRY,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;EACD,MAAMC,UAAU,GAAG,yBAAyB;EAC5C,MAAMC,IAAI,GAAG;IACXR,QAAQ,EAAE;IACR;IACA;IACA;MACEP,KAAK,EAAE,gBAAgB;MACvBG,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEH,KAAK,EAAE,+DAA+D;MACtEG,SAAS,EAAE;IACb,CAAC,EACD;MACEH,KAAK,EAAET,MAAM,CAAC,WAAW,EAAEuB,UAAU,EAAE,YAAY,CAAC;MACpDX,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEH,KAAK,EAAE,uBAAuB;MAC9BG,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEH,KAAK,EAAE,gBAAgB;MACvBG,SAAS,EAAE;IACb,CAAC,CACF;IACDS,WAAW,EAAE,IAAI;IACjBJ,QAAQ,EAAE,CACR;MACEF,SAAS,EAAE,QAAQ;MACnBH,SAAS,EAAE,CAAC;MACZH,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,KAAK;MACVY,YAAY,EAAE,IAAI;MAClBG,SAAS,EAAE;IACb,CAAC,EACD;MACEV,SAAS,EAAE,MAAM;MACjBH,SAAS,EAAE,CAAC;MACZH,KAAK,EAAE,QAAQ;MACfC,GAAG,EAAE,KAAK;MACVY,YAAY,EAAE,IAAI;MAClBH,UAAU,EAAE;IACd,CAAC,EACD;MACEJ,SAAS,EAAE,QAAQ;MACnBH,SAAS,EAAE,CAAC;MACZH,KAAK,EAAE,QAAQ;MACfC,GAAG,EAAE,KAAK;MACVY,YAAY,EAAE,IAAI;MAClBH,UAAU,EAAE;IACd,CAAC;EAEL,CAAC;EACD,MAAMO,IAAI,GAAG;IACXX,SAAS,EAAE,QAAQ;IACnBE,QAAQ,EAAE,EAAE;IAAE;IACdD,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EACD,MAAMiB,MAAM,GAAG;IACbZ,SAAS,EAAE,UAAU;IACrBE,QAAQ,EAAE,EAAE;IAAE;IACdD,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,QAAQ;MACfC,GAAG,EAAE,GAAG;MACRE,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EACDc,IAAI,CAACT,QAAQ,CAACW,IAAI,CAACD,MAAM,CAAC;EAC1BA,MAAM,CAACV,QAAQ,CAACW,IAAI,CAACF,IAAI,CAAC;EAE1B,IAAIG,WAAW,GAAG,CAChBrB,WAAW,EACXgB,IAAI,CACL;EAEDE,IAAI,CAACT,QAAQ,GAAGS,IAAI,CAACT,QAAQ,CAACjB,MAAM,CAAC6B,WAAW,CAAC;EACjDF,MAAM,CAACV,QAAQ,GAAGU,MAAM,CAACV,QAAQ,CAACjB,MAAM,CAAC6B,WAAW,CAAC;EAErDA,WAAW,GAAGA,WAAW,CAAC7B,MAAM,CAAC0B,IAAI,EAAEC,MAAM,CAAC;EAE9C,MAAMG,MAAM,GAAG;IACbf,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,GAAG;MACRO,QAAQ,EAAEY;IACZ,CAAC,EACD;MACEpB,KAAK,EAAE,sBAAsB;MAC7BQ,QAAQ,EAAE,CACR;QACER,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE,KAAK;QACVO,QAAQ,EAAEY;MACZ,CAAC;IAEL,CAAC;EAEL,CAAC;EAED,MAAME,UAAU,GAAG;IACjBhB,SAAS,EAAE,OAAO;IAClBN,KAAK,EAAE,QAAQ;IACfQ,QAAQ,EAAEY,WAAW;IACrBnB,GAAG,EAAE;EACP,CAAC;EAED,OAAO;IACLsB,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CACP,IAAI,EACJ,QAAQ,EACR,KAAK,CACN;IACDhB,QAAQ,EAAE,CACRa,MAAM,EACNtB,WAAW,EACXU,IAAI,EACJQ,IAAI,EACJC,MAAM,EACNI,UAAU,EACVjB,IAAI,EACJD,eAAe,EACfW,IAAI,EACJJ,cAAc;EAElB,CAAC;AACH;AAEAc,MAAM,CAACC,OAAO,GAAG7B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}