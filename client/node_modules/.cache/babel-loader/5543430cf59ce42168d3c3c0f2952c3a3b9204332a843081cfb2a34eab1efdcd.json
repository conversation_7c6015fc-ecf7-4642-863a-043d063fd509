{"ast": null, "code": "'use strict';\n\nmodule.exports = tremor;\ntremor.displayName = 'tremor';\ntremor.aliases = [];\nfunction tremor(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.tremor = {\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,\n        lookbehind: true\n      },\n      'interpolated-string': null,\n      // see below\n      extractor: {\n        pattern: /\\b[a-z_]\\w*\\|(?:[^\\r\\n\\\\|]|\\\\(?:\\r\\n|[\\s\\S]))*\\|/i,\n        greedy: true,\n        inside: {\n          regex: {\n            pattern: /(^re)\\|[\\s\\S]+/,\n            lookbehind: true\n          },\n          function: /^\\w+/,\n          value: /\\|[\\s\\S]+/\n        }\n      },\n      identifier: {\n        pattern: /`[^`]*`/,\n        greedy: true\n      },\n      function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())\\b/,\n      keyword: /\\b(?:args|as|by|case|config|connect|connector|const|copy|create|default|define|deploy|drop|each|emit|end|erase|event|flow|fn|for|from|group|having|insert|into|intrinsic|let|links|match|merge|mod|move|of|operator|patch|pipeline|recur|script|select|set|sliding|state|stream|to|tumbling|update|use|when|where|window|with)\\b/,\n      boolean: /\\b(?:false|null|true)\\b/i,\n      number: /\\b(?:0b[01_]*|0x[0-9a-fA-F_]*|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee][+-]?[\\d_]+)?)\\b/,\n      'pattern-punctuation': {\n        pattern: /%(?=[({[])/,\n        alias: 'punctuation'\n      },\n      operator: /[-+*\\/%~!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?>?=?|(?:absent|and|not|or|present|xor)\\b/,\n      punctuation: /::|[;\\[\\]()\\{\\},.:]/\n    };\n    var interpolationPattern = /#\\{(?:[^\"{}]|\\{[^{}]*\\}|\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\")*\\}/.source;\n    Prism.languages.tremor['interpolated-string'] = {\n      pattern: RegExp(/(^|[^\\\\])/.source + '(?:' + '\"\"\"(?:' + /[^\"\\\\#]|\\\\[\\s\\S]|\"(?!\"\")|#(?!\\{)/.source + '|' + interpolationPattern + ')*\"\"\"' + '|' + '\"(?:' + /[^\"\\\\\\r\\n#]|\\\\(?:\\r\\n|[\\s\\S])|#(?!\\{)/.source + '|' + interpolationPattern + ')*\"' + ')'),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          inside: {\n            punctuation: /^#\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.tremor\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    };\n    Prism.languages.troy = Prism.languages['tremor'];\n    Prism.languages.trickle = Prism.languages['tremor'];\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "tremor", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "extractor", "greedy", "inside", "regex", "function", "value", "identifier", "keyword", "boolean", "number", "alias", "operator", "punctuation", "interpolationPattern", "source", "RegExp", "interpolation", "expression", "string", "troy", "trickle"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/tremor.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = tremor\ntremor.displayName = 'tremor'\ntremor.aliases = []\nfunction tremor(Prism) {\n  ;(function (Prism) {\n    Prism.languages.tremor = {\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,\n        lookbehind: true\n      },\n      'interpolated-string': null,\n      // see below\n      extractor: {\n        pattern: /\\b[a-z_]\\w*\\|(?:[^\\r\\n\\\\|]|\\\\(?:\\r\\n|[\\s\\S]))*\\|/i,\n        greedy: true,\n        inside: {\n          regex: {\n            pattern: /(^re)\\|[\\s\\S]+/,\n            lookbehind: true\n          },\n          function: /^\\w+/,\n          value: /\\|[\\s\\S]+/\n        }\n      },\n      identifier: {\n        pattern: /`[^`]*`/,\n        greedy: true\n      },\n      function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())\\b/,\n      keyword:\n        /\\b(?:args|as|by|case|config|connect|connector|const|copy|create|default|define|deploy|drop|each|emit|end|erase|event|flow|fn|for|from|group|having|insert|into|intrinsic|let|links|match|merge|mod|move|of|operator|patch|pipeline|recur|script|select|set|sliding|state|stream|to|tumbling|update|use|when|where|window|with)\\b/,\n      boolean: /\\b(?:false|null|true)\\b/i,\n      number:\n        /\\b(?:0b[01_]*|0x[0-9a-fA-F_]*|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee][+-]?[\\d_]+)?)\\b/,\n      'pattern-punctuation': {\n        pattern: /%(?=[({[])/,\n        alias: 'punctuation'\n      },\n      operator:\n        /[-+*\\/%~!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?>?=?|(?:absent|and|not|or|present|xor)\\b/,\n      punctuation: /::|[;\\[\\]()\\{\\},.:]/\n    }\n    var interpolationPattern =\n      /#\\{(?:[^\"{}]|\\{[^{}]*\\}|\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\")*\\}/.source\n    Prism.languages.tremor['interpolated-string'] = {\n      pattern: RegExp(\n        /(^|[^\\\\])/.source +\n          '(?:' +\n          '\"\"\"(?:' +\n          /[^\"\\\\#]|\\\\[\\s\\S]|\"(?!\"\")|#(?!\\{)/.source +\n          '|' +\n          interpolationPattern +\n          ')*\"\"\"' +\n          '|' +\n          '\"(?:' +\n          /[^\"\\\\\\r\\n#]|\\\\(?:\\r\\n|[\\s\\S])|#(?!\\{)/.source +\n          '|' +\n          interpolationPattern +\n          ')*\"' +\n          ')'\n      ),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          inside: {\n            punctuation: /^#\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.tremor\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n    Prism.languages.troy = Prism.languages['tremor']\n    Prism.languages.trickle = Prism.languages['tremor']\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;MACvBK,OAAO,EAAE;QACPC,OAAO,EAAE,+CAA+C;QACxDC,UAAU,EAAE;MACd,CAAC;MACD,qBAAqB,EAAE,IAAI;MAC3B;MACAC,SAAS,EAAE;QACTF,OAAO,EAAE,mDAAmD;QAC5DG,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,KAAK,EAAE;YACLL,OAAO,EAAE,gBAAgB;YACzBC,UAAU,EAAE;UACd,CAAC;UACDK,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE;QACT;MACF,CAAC;MACDC,UAAU,EAAE;QACVR,OAAO,EAAE,SAAS;QAClBG,MAAM,EAAE;MACV,CAAC;MACDG,QAAQ,EAAE,mCAAmC;MAC7CG,OAAO,EACL,kUAAkU;MACpUC,OAAO,EAAE,0BAA0B;MACnCC,MAAM,EACJ,8EAA8E;MAChF,qBAAqB,EAAE;QACrBX,OAAO,EAAE,YAAY;QACrBY,KAAK,EAAE;MACT,CAAC;MACDC,QAAQ,EACN,uFAAuF;MACzFC,WAAW,EAAE;IACf,CAAC;IACD,IAAIC,oBAAoB,GACtB,iEAAiE,CAACC,MAAM;IAC1EnB,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,qBAAqB,CAAC,GAAG;MAC9CM,OAAO,EAAEiB,MAAM,CACb,WAAW,CAACD,MAAM,GAChB,KAAK,GACL,QAAQ,GACR,kCAAkC,CAACA,MAAM,GACzC,GAAG,GACHD,oBAAoB,GACpB,OAAO,GACP,GAAG,GACH,MAAM,GACN,uCAAuC,CAACC,MAAM,GAC9C,GAAG,GACHD,oBAAoB,GACpB,KAAK,GACL,GACJ,CAAC;MACDd,UAAU,EAAE,IAAI;MAChBE,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNc,aAAa,EAAE;UACblB,OAAO,EAAEiB,MAAM,CAACF,oBAAoB,CAAC;UACrCX,MAAM,EAAE;YACNU,WAAW,EAAE,UAAU;YACvBK,UAAU,EAAE;cACVnB,OAAO,EAAE,SAAS;cAClBI,MAAM,EAAEP,KAAK,CAACC,SAAS,CAACJ;YAC1B;UACF;QACF,CAAC;QACD0B,MAAM,EAAE;MACV;IACF,CAAC;IACDvB,KAAK,CAACC,SAAS,CAACuB,IAAI,GAAGxB,KAAK,CAACC,SAAS,CAAC,QAAQ,CAAC;IAChDD,KAAK,CAACC,SAAS,CAACwB,OAAO,GAAGzB,KAAK,CAACC,SAAS,CAAC,QAAQ,CAAC;EACrD,CAAC,EAAED,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}