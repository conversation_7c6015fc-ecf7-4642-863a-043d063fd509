{"ast": null, "code": "// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n  // DecimalFloatingPointLiteral\n  // including ExponentPart\n  {\n    begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` + `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b`\n  },\n  // excluding ExponentPart\n  {\n    begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)`\n  }, {\n    begin: `(${frac})[fFdD]?\\\\b`\n  }, {\n    begin: `\\\\b(${decimalDigits})[fFdD]\\\\b`\n  },\n  // HexadecimalFloatingPointLiteral\n  {\n    begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` + `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b`\n  },\n  // DecimalIntegerLiteral\n  {\n    begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b'\n  },\n  // HexIntegerLiteral\n  {\n    begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b`\n  },\n  // OctalIntegerLiteral\n  {\n    begin: '\\\\b0(_*[0-7])*[lL]?\\\\b'\n  },\n  // BinaryIntegerLiteral\n  {\n    begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b'\n  }],\n  relevance: 0\n};\n\n/*\nLanguage: Java\nAuthor: Vsevolod Solovyov <<EMAIL>>\nCategory: common, enterprise\nWebsite: https://www.java.com/\n*/\n\nfunction java(hljs) {\n  var JAVA_IDENT_RE = '[\\u00C0-\\u02B8a-zA-Z_$][\\u00C0-\\u02B8a-zA-Z_$0-9]*';\n  var GENERIC_IDENT_RE = JAVA_IDENT_RE + '(<' + JAVA_IDENT_RE + '(\\\\s*,\\\\s*' + JAVA_IDENT_RE + ')*>)?';\n  var KEYWORDS = 'false synchronized int abstract float private char boolean var static null if const ' + 'for true while long strictfp finally protected import native final void ' + 'enum else break transient catch instanceof byte super volatile case assert short ' + 'package default double public try this switch continue throws protected public private ' + 'module requires exports do';\n  var ANNOTATION = {\n    className: 'meta',\n    begin: '@' + JAVA_IDENT_RE,\n    contains: [{\n      begin: /\\(/,\n      end: /\\)/,\n      contains: [\"self\"] // allow nested () inside our annotation\n    }]\n  };\n  const NUMBER = NUMERIC;\n  return {\n    name: 'Java',\n    aliases: ['jsp'],\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [hljs.COMMENT('/\\\\*\\\\*', '\\\\*/', {\n      relevance: 0,\n      contains: [{\n        // eat up @'s in emails to prevent them to be recognized as doctags\n        begin: /\\w+@/,\n        relevance: 0\n      }, {\n        className: 'doctag',\n        begin: '@[A-Za-z]+'\n      }]\n    }),\n    // relevance boost\n    {\n      begin: /import java\\.[a-z]+\\./,\n      keywords: \"import\",\n      relevance: 2\n    }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n      className: 'class',\n      beginKeywords: 'class interface enum',\n      end: /[{;=]/,\n      excludeEnd: true,\n      // TODO: can this be removed somehow?\n      // an extra boost because Java is more popular than other languages with\n      // this same syntax feature (this is just to preserve our tests passing\n      // for now)\n      relevance: 1,\n      keywords: 'class interface enum',\n      illegal: /[:\"\\[\\]]/,\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      // Expression keywords prevent 'keyword Name(...)' from being\n      // recognized as a function definition\n      beginKeywords: 'new throw return else',\n      relevance: 0\n    }, {\n      className: 'class',\n      begin: 'record\\\\s+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n      returnBegin: true,\n      excludeEnd: true,\n      end: /[{;=]/,\n      keywords: KEYWORDS,\n      contains: [{\n        beginKeywords: \"record\"\n      }, {\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n        returnBegin: true,\n        relevance: 0,\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      }, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [hljs.C_BLOCK_COMMENT_MODE]\n      }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      className: 'function',\n      begin: '(' + GENERIC_IDENT_RE + '\\\\s+)+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n      returnBegin: true,\n      end: /[{;=]/,\n      excludeEnd: true,\n      keywords: KEYWORDS,\n      contains: [{\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n        returnBegin: true,\n        relevance: 0,\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      }, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [ANNOTATION, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, NUMBER, hljs.C_BLOCK_COMMENT_MODE]\n      }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, NUMBER, ANNOTATION]\n  };\n}\nmodule.exports = java;", "map": {"version": 3, "names": ["decimalDigits", "frac", "hexDigits", "NUMERIC", "className", "variants", "begin", "relevance", "java", "hljs", "JAVA_IDENT_RE", "GENERIC_IDENT_RE", "KEYWORDS", "ANNOTATION", "contains", "end", "NUMBER", "name", "aliases", "keywords", "illegal", "COMMENT", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "beginKeywords", "excludeEnd", "UNDERSCORE_TITLE_MODE", "UNDERSCORE_IDENT_RE", "returnBegin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/java.js"], "sourcesContent": ["// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\nLanguage: Java\nAuthor: Vsevolod Solovyov <<EMAIL>>\nCategory: common, enterprise\nWebsite: https://www.java.com/\n*/\n\nfunction java(hljs) {\n  var JAVA_IDENT_RE = '[\\u00C0-\\u02B8a-zA-Z_$][\\u00C0-\\u02B8a-zA-Z_$0-9]*';\n  var GENERIC_IDENT_RE = JAVA_IDENT_RE + '(<' + JAVA_IDENT_RE + '(\\\\s*,\\\\s*' + JAVA_IDENT_RE + ')*>)?';\n  var KEYWORDS = 'false synchronized int abstract float private char boolean var static null if const ' +\n    'for true while long strictfp finally protected import native final void ' +\n    'enum else break transient catch instanceof byte super volatile case assert short ' +\n    'package default double public try this switch continue throws protected public private ' +\n    'module requires exports do';\n\n  var ANNOTATION = {\n    className: 'meta',\n    begin: '@' + JAVA_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\"self\"] // allow nested () inside our annotation\n      },\n    ]\n  };\n  const NUMBER = NUMERIC;\n\n  return {\n    name: 'Java',\n    aliases: ['jsp'],\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/, relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      // relevance boost\n      {\n        begin: /import java\\.[a-z]+\\./,\n        keywords: \"import\",\n        relevance: 2\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'class interface enum', end: /[{;=]/, excludeEnd: true,\n        // TODO: can this be removed somehow?\n        // an extra boost because Java is more popular than other languages with\n        // this same syntax feature (this is just to preserve our tests passing\n        // for now)\n        relevance: 1,\n        keywords: 'class interface enum',\n        illegal: /[:\"\\[\\]]/,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new throw return else',\n        relevance: 0\n      },\n      {\n        className: 'class',\n        begin: 'record\\\\s+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n        returnBegin: true,\n        excludeEnd: true,\n        end: /[{;=]/,\n        keywords: KEYWORDS,\n        contains: [\n          { beginKeywords: \"record\" },\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n            returnBegin: true,\n            relevance: 0,\n            contains: [hljs.UNDERSCORE_TITLE_MODE]\n          },\n          {\n            className: 'params',\n            begin: /\\(/, end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        className: 'function',\n        begin: '(' + GENERIC_IDENT_RE + '\\\\s+)+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(', returnBegin: true, end: /[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(', returnBegin: true,\n            relevance: 0,\n            contains: [hljs.UNDERSCORE_TITLE_MODE]\n          },\n          {\n            className: 'params',\n            begin: /\\(/, end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              ANNOTATION,\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              NUMBER,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      NUMBER,\n      ANNOTATION\n    ]\n  };\n}\n\nmodule.exports = java;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG,iBAAiB;AACrC,IAAIC,IAAI,GAAG,OAAOD,aAAa,GAAG;AAClC,IAAIE,SAAS,GAAG,6BAA6B;AAC7C,IAAIC,OAAO,GAAG;EACZC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE;EACR;EACA;EACA;IAAEC,KAAK,EAAE,QAAQN,aAAa,MAAMC,IAAI,YAAYA,IAAI,IAAI,GAC1D,aAAaD,aAAa;EAAc,CAAC;EAC3C;EACA;IAAEM,KAAK,EAAE,OAAON,aAAa,MAAMC,IAAI;EAA+B,CAAC,EACvE;IAAEK,KAAK,EAAE,IAAIL,IAAI;EAAc,CAAC,EAChC;IAAEK,KAAK,EAAE,OAAON,aAAa;EAAa,CAAC;EAE3C;EACA;IAAEM,KAAK,EAAE,aAAaJ,SAAS,UAAUA,SAAS,SAASA,SAAS,IAAI,GACtE,aAAaF,aAAa;EAAc,CAAC;EAE3C;EACA;IAAEM,KAAK,EAAE;EAAiC,CAAC;EAE3C;EACA;IAAEA,KAAK,EAAE,YAAYJ,SAAS;EAAY,CAAC;EAE3C;EACA;IAAEI,KAAK,EAAE;EAAyB,CAAC;EAEnC;EACA;IAAEA,KAAK,EAAE;EAAgC,CAAC,CAC3C;EACDC,SAAS,EAAE;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,aAAa,GAAG,oDAAoD;EACxE,IAAIC,gBAAgB,GAAGD,aAAa,GAAG,IAAI,GAAGA,aAAa,GAAG,YAAY,GAAGA,aAAa,GAAG,OAAO;EACpG,IAAIE,QAAQ,GAAG,sFAAsF,GACnG,0EAA0E,GAC1E,mFAAmF,GACnF,yFAAyF,GACzF,4BAA4B;EAE9B,IAAIC,UAAU,GAAG;IACfT,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,GAAG,GAAGI,aAAa;IAC1BI,QAAQ,EAAE,CACR;MACER,KAAK,EAAE,IAAI;MACXS,GAAG,EAAE,IAAI;MACTD,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;EAEL,CAAC;EACD,MAAME,MAAM,GAAGb,OAAO;EAEtB,OAAO;IACLc,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,QAAQ,EAAEP,QAAQ;IAClBQ,OAAO,EAAE,OAAO;IAChBN,QAAQ,EAAE,CACRL,IAAI,CAACY,OAAO,CACV,SAAS,EACT,MAAM,EACN;MACEd,SAAS,EAAE,CAAC;MACZO,QAAQ,EAAE,CACR;QACE;QACAR,KAAK,EAAE,MAAM;QAAEC,SAAS,EAAE;MAC5B,CAAC,EACD;QACEH,SAAS,EAAE,QAAQ;QACnBE,KAAK,EAAE;MACT,CAAC;IAEL,CACF,CAAC;IACD;IACA;MACEA,KAAK,EAAE,uBAAuB;MAC9Ba,QAAQ,EAAE,QAAQ;MAClBZ,SAAS,EAAE;IACb,CAAC,EACDE,IAAI,CAACa,mBAAmB,EACxBb,IAAI,CAACc,oBAAoB,EACzBd,IAAI,CAACe,gBAAgB,EACrBf,IAAI,CAACgB,iBAAiB,EACtB;MACErB,SAAS,EAAE,OAAO;MAClBsB,aAAa,EAAE,sBAAsB;MAAEX,GAAG,EAAE,OAAO;MAAEY,UAAU,EAAE,IAAI;MACrE;MACA;MACA;MACA;MACApB,SAAS,EAAE,CAAC;MACZY,QAAQ,EAAE,sBAAsB;MAChCC,OAAO,EAAE,UAAU;MACnBN,QAAQ,EAAE,CACR;QAAEY,aAAa,EAAE;MAAqB,CAAC,EACvCjB,IAAI,CAACmB,qBAAqB;IAE9B,CAAC,EACD;MACE;MACA;MACAF,aAAa,EAAE,uBAAuB;MACtCnB,SAAS,EAAE;IACb,CAAC,EACD;MACEH,SAAS,EAAE,OAAO;MAClBE,KAAK,EAAE,YAAY,GAAGG,IAAI,CAACoB,mBAAmB,GAAG,SAAS;MAC1DC,WAAW,EAAE,IAAI;MACjBH,UAAU,EAAE,IAAI;MAChBZ,GAAG,EAAE,OAAO;MACZI,QAAQ,EAAEP,QAAQ;MAClBE,QAAQ,EAAE,CACR;QAAEY,aAAa,EAAE;MAAS,CAAC,EAC3B;QACEpB,KAAK,EAAEG,IAAI,CAACoB,mBAAmB,GAAG,SAAS;QAC3CC,WAAW,EAAE,IAAI;QACjBvB,SAAS,EAAE,CAAC;QACZO,QAAQ,EAAE,CAACL,IAAI,CAACmB,qBAAqB;MACvC,CAAC,EACD;QACExB,SAAS,EAAE,QAAQ;QACnBE,KAAK,EAAE,IAAI;QAAES,GAAG,EAAE,IAAI;QACtBI,QAAQ,EAAEP,QAAQ;QAClBL,SAAS,EAAE,CAAC;QACZO,QAAQ,EAAE,CACRL,IAAI,CAACc,oBAAoB;MAE7B,CAAC,EACDd,IAAI,CAACa,mBAAmB,EACxBb,IAAI,CAACc,oBAAoB;IAE7B,CAAC,EACD;MACEnB,SAAS,EAAE,UAAU;MACrBE,KAAK,EAAE,GAAG,GAAGK,gBAAgB,GAAG,QAAQ,GAAGF,IAAI,CAACoB,mBAAmB,GAAG,SAAS;MAAEC,WAAW,EAAE,IAAI;MAAEf,GAAG,EAAE,OAAO;MAChHY,UAAU,EAAE,IAAI;MAChBR,QAAQ,EAAEP,QAAQ;MAClBE,QAAQ,EAAE,CACR;QACER,KAAK,EAAEG,IAAI,CAACoB,mBAAmB,GAAG,SAAS;QAAEC,WAAW,EAAE,IAAI;QAC9DvB,SAAS,EAAE,CAAC;QACZO,QAAQ,EAAE,CAACL,IAAI,CAACmB,qBAAqB;MACvC,CAAC,EACD;QACExB,SAAS,EAAE,QAAQ;QACnBE,KAAK,EAAE,IAAI;QAAES,GAAG,EAAE,IAAI;QACtBI,QAAQ,EAAEP,QAAQ;QAClBL,SAAS,EAAE,CAAC;QACZO,QAAQ,EAAE,CACRD,UAAU,EACVJ,IAAI,CAACe,gBAAgB,EACrBf,IAAI,CAACgB,iBAAiB,EACtBT,MAAM,EACNP,IAAI,CAACc,oBAAoB;MAE7B,CAAC,EACDd,IAAI,CAACa,mBAAmB,EACxBb,IAAI,CAACc,oBAAoB;IAE7B,CAAC,EACDP,MAAM,EACNH,UAAU;EAEd,CAAC;AACH;AAEAkB,MAAM,CAACC,OAAO,GAAGxB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}