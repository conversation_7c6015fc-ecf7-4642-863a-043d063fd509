{"ast": null, "code": "'use strict';\n\nvar normalize = require('../../normalize');\nvar Schema = require('./schema');\nvar DefinedInfo = require('./defined-info');\nmodule.exports = create;\nfunction create(definition) {\n  var space = definition.space;\n  var mustUseProperty = definition.mustUseProperty || [];\n  var attributes = definition.attributes || {};\n  var props = definition.properties;\n  var transform = definition.transform;\n  var property = {};\n  var normal = {};\n  var prop;\n  var info;\n  for (prop in props) {\n    info = new DefinedInfo(prop, transform(attributes, prop), props[prop], space);\n    if (mustUseProperty.indexOf(prop) !== -1) {\n      info.mustUseProperty = true;\n    }\n    property[prop] = info;\n    normal[normalize(prop)] = prop;\n    normal[normalize(info.attribute)] = prop;\n  }\n  return new Schema(property, normal, space);\n}", "map": {"version": 3, "names": ["normalize", "require", "<PERSON><PERSON><PERSON>", "DefinedInfo", "module", "exports", "create", "definition", "space", "mustUseProperty", "attributes", "props", "properties", "transform", "property", "normal", "prop", "info", "indexOf", "attribute"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/lib/util/create.js"], "sourcesContent": ["'use strict'\n\nvar normalize = require('../../normalize')\nvar Schema = require('./schema')\nvar DefinedInfo = require('./defined-info')\n\nmodule.exports = create\n\nfunction create(definition) {\n  var space = definition.space\n  var mustUseProperty = definition.mustUseProperty || []\n  var attributes = definition.attributes || {}\n  var props = definition.properties\n  var transform = definition.transform\n  var property = {}\n  var normal = {}\n  var prop\n  var info\n\n  for (prop in props) {\n    info = new DefinedInfo(\n      prop,\n      transform(attributes, prop),\n      props[prop],\n      space\n    )\n\n    if (mustUseProperty.indexOf(prop) !== -1) {\n      info.mustUseProperty = true\n    }\n\n    property[prop] = info\n\n    normal[normalize(prop)] = prop\n    normal[normalize(info.attribute)] = prop\n  }\n\n  return new Schema(property, normal, space)\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC1C,IAAIC,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;AAChC,IAAIE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAE3CG,MAAM,CAACC,OAAO,GAAGC,MAAM;AAEvB,SAASA,MAAMA,CAACC,UAAU,EAAE;EAC1B,IAAIC,KAAK,GAAGD,UAAU,CAACC,KAAK;EAC5B,IAAIC,eAAe,GAAGF,UAAU,CAACE,eAAe,IAAI,EAAE;EACtD,IAAIC,UAAU,GAAGH,UAAU,CAACG,UAAU,IAAI,CAAC,CAAC;EAC5C,IAAIC,KAAK,GAAGJ,UAAU,CAACK,UAAU;EACjC,IAAIC,SAAS,GAAGN,UAAU,CAACM,SAAS;EACpC,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,IAAI;EACR,IAAIC,IAAI;EAER,KAAKD,IAAI,IAAIL,KAAK,EAAE;IAClBM,IAAI,GAAG,IAAId,WAAW,CACpBa,IAAI,EACJH,SAAS,CAACH,UAAU,EAAEM,IAAI,CAAC,EAC3BL,KAAK,CAACK,IAAI,CAAC,EACXR,KACF,CAAC;IAED,IAAIC,eAAe,CAACS,OAAO,CAACF,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACxCC,IAAI,CAACR,eAAe,GAAG,IAAI;IAC7B;IAEAK,QAAQ,CAACE,IAAI,CAAC,GAAGC,IAAI;IAErBF,MAAM,CAACf,SAAS,CAACgB,IAAI,CAAC,CAAC,GAAGA,IAAI;IAC9BD,MAAM,CAACf,SAAS,CAACiB,IAAI,CAACE,SAAS,CAAC,CAAC,GAAGH,IAAI;EAC1C;EAEA,OAAO,IAAId,MAAM,CAACY,QAAQ,EAAEC,MAAM,EAAEP,KAAK,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}