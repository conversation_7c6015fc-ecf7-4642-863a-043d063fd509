{"ast": null, "code": "/*\nLanguage: Apache config\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://httpd.apache.org\nDescription: language definition for Apache configuration files (httpd.conf & .htaccess)\nCategory: common, config\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction apache(hljs) {\n  const NUMBER_REF = {\n    className: 'number',\n    begin: /[$%]\\d+/\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: /\\d+/\n  };\n  const IP_ADDRESS = {\n    className: \"number\",\n    begin: /\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?/\n  };\n  const PORT_NUMBER = {\n    className: \"number\",\n    begin: /:\\d{1,5}/\n  };\n  return {\n    name: 'Apache config',\n    aliases: ['apacheconf'],\n    case_insensitive: true,\n    contains: [hljs.HASH_COMMENT_MODE, {\n      className: 'section',\n      begin: /<\\/?/,\n      end: />/,\n      contains: [IP_ADDRESS, PORT_NUMBER,\n      // low relevance prevents us from claming XML/HTML where this rule would\n      // match strings inside of XML tags\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        relevance: 0\n      })]\n    }, {\n      className: 'attribute',\n      begin: /\\w+/,\n      relevance: 0,\n      // keywords aren’t needed for highlighting per se, they only boost relevance\n      // for a very generally defined mode (starts with a word, ends with line-end\n      keywords: {\n        nomarkup: 'order deny allow setenv rewriterule rewriteengine rewritecond documentroot ' + 'sethandler errordocument loadmodule options header listen serverroot ' + 'servername'\n      },\n      starts: {\n        end: /$/,\n        relevance: 0,\n        keywords: {\n          literal: 'on off all deny allow'\n        },\n        contains: [{\n          className: 'meta',\n          begin: /\\s\\[/,\n          end: /\\]$/\n        }, {\n          className: 'variable',\n          begin: /[\\$%]\\{/,\n          end: /\\}/,\n          contains: ['self', NUMBER_REF]\n        }, IP_ADDRESS, NUMBER, hljs.QUOTE_STRING_MODE]\n      }\n    }],\n    illegal: /\\S/\n  };\n}\nmodule.exports = apache;", "map": {"version": 3, "names": ["apache", "hljs", "NUMBER_REF", "className", "begin", "NUMBER", "IP_ADDRESS", "PORT_NUMBER", "name", "aliases", "case_insensitive", "contains", "HASH_COMMENT_MODE", "end", "inherit", "QUOTE_STRING_MODE", "relevance", "keywords", "nomarkup", "starts", "literal", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/apache.js"], "sourcesContent": ["/*\nLanguage: Apache config\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://httpd.apache.org\nDescription: language definition for Apache configuration files (httpd.conf & .htaccess)\nCategory: common, config\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction apache(hljs) {\n  const NUMBER_REF = {\n    className: 'number',\n    begin: /[$%]\\d+/\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: /\\d+/\n  };\n  const IP_ADDRESS = {\n    className: \"number\",\n    begin: /\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?/\n  };\n  const PORT_NUMBER = {\n    className: \"number\",\n    begin: /:\\d{1,5}/\n  };\n  return {\n    name: 'Apache config',\n    aliases: [ 'apacheconf' ],\n    case_insensitive: true,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'section',\n        begin: /<\\/?/,\n        end: />/,\n        contains: [\n          IP_ADDRESS,\n          PORT_NUMBER,\n          // low relevance prevents us from claming XML/HTML where this rule would\n          // match strings inside of XML tags\n          hljs.inherit(hljs.QUOTE_STRING_MODE, { relevance: 0 })\n        ]\n      },\n      {\n        className: 'attribute',\n        begin: /\\w+/,\n        relevance: 0,\n        // keywords aren’t needed for highlighting per se, they only boost relevance\n        // for a very generally defined mode (starts with a word, ends with line-end\n        keywords: {\n          nomarkup:\n            'order deny allow setenv rewriterule rewriteengine rewritecond documentroot ' +\n            'sethandler errordocument loadmodule options header listen serverroot ' +\n            'servername'\n        },\n        starts: {\n          end: /$/,\n          relevance: 0,\n          keywords: { literal: 'on off all deny allow' },\n          contains: [\n            {\n              className: 'meta',\n              begin: /\\s\\[/,\n              end: /\\]$/\n            },\n            {\n              className: 'variable',\n              begin: /[\\$%]\\{/,\n              end: /\\}/,\n              contains: [\n                'self',\n                NUMBER_REF\n              ]\n            },\n            IP_ADDRESS,\n            NUMBER,\n            hljs.QUOTE_STRING_MODE\n          ]\n        }\n      }\n    ],\n    illegal: /\\S/\n  };\n}\n\nmodule.exports = apache;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,UAAU,GAAG;IACjBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,MAAM,GAAG;IACbF,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAME,UAAU,GAAG;IACjBH,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMG,WAAW,GAAG;IAClBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,OAAO;IACLI,IAAI,EAAE,eAAe;IACrBC,OAAO,EAAE,CAAE,YAAY,CAAE;IACzBC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,CACRV,IAAI,CAACW,iBAAiB,EACtB;MACET,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,MAAM;MACbS,GAAG,EAAE,GAAG;MACRF,QAAQ,EAAE,CACRL,UAAU,EACVC,WAAW;MACX;MACA;MACAN,IAAI,CAACa,OAAO,CAACb,IAAI,CAACc,iBAAiB,EAAE;QAAEC,SAAS,EAAE;MAAE,CAAC,CAAC;IAE1D,CAAC,EACD;MACEb,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,KAAK;MACZY,SAAS,EAAE,CAAC;MACZ;MACA;MACAC,QAAQ,EAAE;QACRC,QAAQ,EACN,6EAA6E,GAC7E,uEAAuE,GACvE;MACJ,CAAC;MACDC,MAAM,EAAE;QACNN,GAAG,EAAE,GAAG;QACRG,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE;UAAEG,OAAO,EAAE;QAAwB,CAAC;QAC9CT,QAAQ,EAAE,CACR;UACER,SAAS,EAAE,MAAM;UACjBC,KAAK,EAAE,MAAM;UACbS,GAAG,EAAE;QACP,CAAC,EACD;UACEV,SAAS,EAAE,UAAU;UACrBC,KAAK,EAAE,SAAS;UAChBS,GAAG,EAAE,IAAI;UACTF,QAAQ,EAAE,CACR,MAAM,EACNT,UAAU;QAEd,CAAC,EACDI,UAAU,EACVD,MAAM,EACNJ,IAAI,CAACc,iBAAiB;MAE1B;IACF,CAAC,CACF;IACDM,OAAO,EAAE;EACX,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGvB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}