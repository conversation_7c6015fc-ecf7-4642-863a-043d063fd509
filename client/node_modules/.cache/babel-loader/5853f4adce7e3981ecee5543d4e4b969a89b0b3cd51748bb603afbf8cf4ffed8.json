{"ast": null, "code": "'use strict';\n\nmodule.exports = bnf;\nbnf.displayName = 'bnf';\nbnf.aliases = ['rbnf'];\nfunction bnf(Prism) {\n  Prism.languages.bnf = {\n    string: {\n      pattern: /\"[^\\r\\n\"]*\"|'[^\\r\\n']*'/\n    },\n    definition: {\n      pattern: /<[^<>\\r\\n\\t]+>(?=\\s*::=)/,\n      alias: ['rule', 'keyword'],\n      inside: {\n        punctuation: /^<|>$/\n      }\n    },\n    rule: {\n      pattern: /<[^<>\\r\\n\\t]+>/,\n      inside: {\n        punctuation: /^<|>$/\n      }\n    },\n    operator: /::=|[|()[\\]{}*+?]|\\.{3}/\n  };\n  Prism.languages.rbnf = Prism.languages.bnf;\n}", "map": {"version": 3, "names": ["module", "exports", "bnf", "displayName", "aliases", "Prism", "languages", "string", "pattern", "definition", "alias", "inside", "punctuation", "rule", "operator", "rbnf"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/bnf.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bnf\nbnf.displayName = 'bnf'\nbnf.aliases = ['rbnf']\nfunction bnf(Prism) {\n  Prism.languages.bnf = {\n    string: {\n      pattern: /\"[^\\r\\n\"]*\"|'[^\\r\\n']*'/\n    },\n    definition: {\n      pattern: /<[^<>\\r\\n\\t]+>(?=\\s*::=)/,\n      alias: ['rule', 'keyword'],\n      inside: {\n        punctuation: /^<|>$/\n      }\n    },\n    rule: {\n      pattern: /<[^<>\\r\\n\\t]+>/,\n      inside: {\n        punctuation: /^<|>$/\n      }\n    },\n    operator: /::=|[|()[\\]{}*+?]|\\.{3}/\n  }\n  Prism.languages.rbnf = Prism.languages.bnf\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,CAAC,MAAM,CAAC;AACtB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,MAAM,EAAE;MACNC,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE;MACVD,OAAO,EAAE,0BAA0B;MACnCE,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;MAC1BC,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,IAAI,EAAE;MACJL,OAAO,EAAE,gBAAgB;MACzBG,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACDE,QAAQ,EAAE;EACZ,CAAC;EACDT,KAAK,CAACC,SAAS,CAACS,IAAI,GAAGV,KAAK,CAACC,SAAS,CAACJ,GAAG;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}