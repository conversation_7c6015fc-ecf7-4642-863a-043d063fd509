{"ast": null, "code": "'use strict';\n\nmodule.exports = avisynth;\navisynth.displayName = 'avisynth';\navisynth.aliases = ['avs'];\nfunction avisynth(Prism) {\n  // http://avisynth.nl/index.php/The_full_AviSynth_grammar\n  ;\n  (function (Prism) {\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return replacements[+index];\n      });\n    }\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '');\n    }\n    var types = /bool|clip|float|int|string|val/.source;\n    var internals = [\n    // bools\n    /is(?:bool|clip|float|int|string)|defined|(?:(?:internal)?function|var)?exists?/.source,\n    // control\n    /apply|assert|default|eval|import|nop|select|undefined/.source,\n    // global\n    /opt_(?:allowfloataudio|avipadscanlines|dwchannelmask|enable_(?:b64a|planartopackedrgb|v210|y3_10_10|y3_10_16)|usewaveextensible|vdubplanarhack)|set(?:cachemode|maxcpu|memorymax|planarlegacyalignment|workingdir)/.source,\n    // conv\n    /hex(?:value)?|value/.source,\n    // numeric\n    /abs|ceil|continued(?:denominator|numerator)?|exp|floor|fmod|frac|log(?:10)?|max|min|muldiv|pi|pow|rand|round|sign|spline|sqrt/.source,\n    // trig\n    /a?sinh?|a?cosh?|a?tan[2h]?/.source,\n    // bit\n    /(?:bit(?:and|not|x?or|[lr]?shift[aslu]?|sh[lr]|sa[lr]|[lr]rotatel?|ro[rl]|te?st|set(?:count)?|cl(?:ea)?r|ch(?:an)?ge?))/.source,\n    // runtime\n    /average(?:[bgr]|chroma[uv]|luma)|(?:[rgb]|chroma[uv]|luma|rgb|[yuv](?=difference(?:fromprevious|tonext)))difference(?:fromprevious|tonext)?|[yuvrgb]plane(?:median|min|max|minmaxdifference)/.source,\n    // script\n    /getprocessinfo|logmsg|script(?:dir(?:utf8)?|file(?:utf8)?|name(?:utf8)?)|setlogparams/.source,\n    // string\n    /chr|(?:fill|find|left|mid|replace|rev|right)str|format|[lu]case|ord|str(?:cmpi?|fromutf8|len|toutf8)|time|trim(?:all|left|right)/.source,\n    // version\n    /isversionorgreater|version(?:number|string)/.source,\n    // helper\n    /buildpixeltype|colorspacenametopixeltype/.source,\n    // avsplus\n    /addautoloaddir|on(?:cpu|cuda)|prefetch|setfiltermtmode/.source].join('|');\n    var properties = [\n    // content\n    /has(?:audio|video)/.source,\n    // resolution\n    /height|width/.source,\n    // framerate\n    /frame(?:count|rate)|framerate(?:denominator|numerator)/.source,\n    // interlacing\n    /getparity|is(?:field|frame)based/.source,\n    // color format\n    /bitspercomponent|componentsize|hasalpha|is(?:planar(?:rgba?)?|interleaved|rgb(?:24|32|48|64)?|y(?:8|u(?:va?|y2))?|yv(?:12|16|24|411)|420|422|444|packedrgb)|numcomponents|pixeltype/.source,\n    // audio\n    /audio(?:bits|channels|duration|length(?:[fs]|hi|lo)?|rate)|isaudio(?:float|int)/.source].join('|');\n    var filters = [\n    // source\n    /avi(?:file)?source|directshowsource|image(?:reader|source|sourceanim)|opendmlsource|segmented(?:avisource|directshowsource)|wavsource/.source,\n    // color\n    /coloryuv|convertbacktoyuy2|convertto(?:RGB(?:24|32|48|64)|(?:planar)?RGBA?|Y8?|YV(?:12|16|24|411)|YUVA?(?:411|420|422|444)|YUY2)|fixluminance|gr[ae]yscale|invert|levels|limiter|mergea?rgb|merge(?:chroma|luma)|rgbadjust|show(?:alpha|blue|green|red)|swapuv|tweak|[uv]toy8?|ytouv/.source,\n    // overlay\n    /(?:colorkey|reset)mask|layer|mask(?:hs)?|merge|overlay|subtract/.source,\n    // geometry\n    /addborders|(?:bicubic|bilinear|blackman|gauss|lanczos4|lanczos|point|sinc|spline(?:16|36|64))resize|crop(?:bottom)?|flip(?:horizontal|vertical)|(?:horizontal|vertical)?reduceby2|letterbox|skewrows|turn(?:180|left|right)/.source,\n    // pixel\n    /blur|fixbrokenchromaupsampling|generalconvolution|(?:spatial|temporal)soften|sharpen/.source,\n    // timeline\n    /trim|(?:un)?alignedsplice|(?:assume|assumescaled|change|convert)FPS|(?:delete|duplicate)frame|dissolve|fade(?:in|io|out)[02]?|freezeframe|interleave|loop|reverse|select(?:even|odd|(?:range)?every)/.source,\n    // interlace\n    /assume[bt]ff|assume(?:field|frame)based|bob|complementparity|doubleweave|peculiarblend|pulldown|separate(?:columns|fields|rows)|swapfields|weave(?:columns|rows)?/.source,\n    // audio\n    /amplify(?:db)?|assumesamplerate|audiodub(?:ex)?|audiotrim|convertaudioto(?:(?:8|16|24|32)bit|float)|converttomono|delayaudio|ensurevbrmp3sync|get(?:left|right)?channel|kill(?:audio|video)|mergechannels|mixaudio|monotostereo|normalize|resampleaudio|ssrc|supereq|timestretch/.source,\n    // conditional\n    /animate|applyrange|conditional(?:filter|reader|select)|frameevaluate|scriptclip|tcp(?:server|source)|writefile(?:end|if|start)?/.source,\n    // export\n    /imagewriter/.source,\n    // debug\n    /blackness|blankclip|colorbars(?:hd)?|compare|dumpfiltergraph|echo|histogram|info|messageclip|preroll|setgraphanalysis|show(?:framenumber|smpte|time)|showfiveversions|stack(?:horizontal|vertical)|subtitle|tone|version/.source].join('|');\n    var allinternals = [internals, properties, filters].join('|');\n    Prism.languages.avisynth = {\n      comment: [{\n        // Matches [* *] nestable block comments, but only supports 1 level of nested comments\n        // /\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|<self>)*\\*\\]/\n        pattern: /(^|[^\\\\])\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\]))*\\*\\])*\\*\\]/,\n        lookbehind: true,\n        greedy: true\n      }, {\n        // Matches /* */ block comments\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        greedy: true\n      }, {\n        // Matches # comments\n        pattern: /(^|[^\\\\$])#.*/,\n        lookbehind: true,\n        greedy: true\n      }],\n      // Handle before strings because optional arguments are surrounded by double quotes\n      argument: {\n        pattern: re(/\\b(?:<<0>>)\\s+(\"?)\\w+\\1/.source, [types], 'i'),\n        inside: {\n          keyword: /^\\w+/\n        }\n      },\n      // Optional argument assignment\n      'argument-label': {\n        pattern: /([,(][\\s\\\\]*)\\w+\\s*=(?!=)/,\n        lookbehind: true,\n        inside: {\n          'argument-name': {\n            pattern: /^\\w+/,\n            alias: 'punctuation'\n          },\n          punctuation: /=$/\n        }\n      },\n      string: [{\n        // triple double-quoted\n        pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n        greedy: true\n      }, {\n        // single double-quoted\n        pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n        greedy: true,\n        inside: {\n          constant: {\n            // These *are* case-sensitive!\n            pattern: /\\b(?:DEFAULT_MT_MODE|(?:MAINSCRIPT|PROGRAM|SCRIPT)DIR|(?:MACHINE|USER)_(?:CLASSIC|PLUS)_PLUGINS)\\b/\n          }\n        }\n      }],\n      // The special \"last\" variable that takes the value of the last implicitly returned clip\n      variable: /\\b(?:last)\\b/i,\n      boolean: /\\b(?:false|no|true|yes)\\b/i,\n      keyword: /\\b(?:catch|else|for|function|global|if|return|try|while|__END__)\\b/i,\n      constant: /\\bMT_(?:MULTI_INSTANCE|NICE_FILTER|SERIALIZED|SPECIAL_MT)\\b/,\n      // AviSynth's internal functions, filters, and properties\n      'builtin-function': {\n        pattern: re(/\\b(?:<<0>>)\\b/.source, [allinternals], 'i'),\n        alias: 'function'\n      },\n      'type-cast': {\n        pattern: re(/\\b(?:<<0>>)(?=\\s*\\()/.source, [types], 'i'),\n        alias: 'keyword'\n      },\n      // External/user-defined filters\n      function: {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()|(\\.)[a-z_]\\w*\\b/i,\n        lookbehind: true\n      },\n      // Matches a \\ as the first or last character on a line\n      'line-continuation': {\n        pattern: /(^[ \\t]*)\\\\|\\\\(?=[ \\t]*$)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      number: /\\B\\$(?:[\\da-f]{6}|[\\da-f]{8})\\b|(?:(?:\\b|\\B-)\\d+(?:\\.\\d*)?\\b|\\B\\.\\d+\\b)/i,\n      operator: /\\+\\+?|[!=<>]=?|&&|\\|\\||[?:*/%-]/,\n      punctuation: /[{}\\[\\]();,.]/\n    };\n    Prism.languages.avs = Prism.languages.avisynth;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "avisynth", "displayName", "aliases", "Prism", "replace", "pattern", "replacements", "m", "index", "re", "flags", "RegExp", "types", "source", "internals", "join", "properties", "filters", "allinternals", "languages", "comment", "lookbehind", "greedy", "argument", "inside", "keyword", "alias", "punctuation", "string", "constant", "variable", "boolean", "function", "number", "operator", "avs"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/avisynth.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = avisynth\navisynth.displayName = 'avisynth'\navisynth.aliases = ['avs']\nfunction avisynth(Prism) {\n  // http://avisynth.nl/index.php/The_full_AviSynth_grammar\n  ;(function (Prism) {\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return replacements[+index]\n      })\n    }\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    var types = /bool|clip|float|int|string|val/.source\n    var internals = [\n      // bools\n      /is(?:bool|clip|float|int|string)|defined|(?:(?:internal)?function|var)?exists?/\n        .source, // control\n      /apply|assert|default|eval|import|nop|select|undefined/.source, // global\n      /opt_(?:allowfloataudio|avipadscanlines|dwchannelmask|enable_(?:b64a|planartopackedrgb|v210|y3_10_10|y3_10_16)|usewaveextensible|vdubplanarhack)|set(?:cachemode|maxcpu|memorymax|planarlegacyalignment|workingdir)/\n        .source, // conv\n      /hex(?:value)?|value/.source, // numeric\n      /abs|ceil|continued(?:denominator|numerator)?|exp|floor|fmod|frac|log(?:10)?|max|min|muldiv|pi|pow|rand|round|sign|spline|sqrt/\n        .source, // trig\n      /a?sinh?|a?cosh?|a?tan[2h]?/.source, // bit\n      /(?:bit(?:and|not|x?or|[lr]?shift[aslu]?|sh[lr]|sa[lr]|[lr]rotatel?|ro[rl]|te?st|set(?:count)?|cl(?:ea)?r|ch(?:an)?ge?))/\n        .source, // runtime\n      /average(?:[bgr]|chroma[uv]|luma)|(?:[rgb]|chroma[uv]|luma|rgb|[yuv](?=difference(?:fromprevious|tonext)))difference(?:fromprevious|tonext)?|[yuvrgb]plane(?:median|min|max|minmaxdifference)/\n        .source, // script\n      /getprocessinfo|logmsg|script(?:dir(?:utf8)?|file(?:utf8)?|name(?:utf8)?)|setlogparams/\n        .source, // string\n      /chr|(?:fill|find|left|mid|replace|rev|right)str|format|[lu]case|ord|str(?:cmpi?|fromutf8|len|toutf8)|time|trim(?:all|left|right)/\n        .source, // version\n      /isversionorgreater|version(?:number|string)/.source, // helper\n      /buildpixeltype|colorspacenametopixeltype/.source, // avsplus\n      /addautoloaddir|on(?:cpu|cuda)|prefetch|setfiltermtmode/.source\n    ].join('|')\n    var properties = [\n      // content\n      /has(?:audio|video)/.source, // resolution\n      /height|width/.source, // framerate\n      /frame(?:count|rate)|framerate(?:denominator|numerator)/.source, // interlacing\n      /getparity|is(?:field|frame)based/.source, // color format\n      /bitspercomponent|componentsize|hasalpha|is(?:planar(?:rgba?)?|interleaved|rgb(?:24|32|48|64)?|y(?:8|u(?:va?|y2))?|yv(?:12|16|24|411)|420|422|444|packedrgb)|numcomponents|pixeltype/\n        .source, // audio\n      /audio(?:bits|channels|duration|length(?:[fs]|hi|lo)?|rate)|isaudio(?:float|int)/\n        .source\n    ].join('|')\n    var filters = [\n      // source\n      /avi(?:file)?source|directshowsource|image(?:reader|source|sourceanim)|opendmlsource|segmented(?:avisource|directshowsource)|wavsource/\n        .source, // color\n      /coloryuv|convertbacktoyuy2|convertto(?:RGB(?:24|32|48|64)|(?:planar)?RGBA?|Y8?|YV(?:12|16|24|411)|YUVA?(?:411|420|422|444)|YUY2)|fixluminance|gr[ae]yscale|invert|levels|limiter|mergea?rgb|merge(?:chroma|luma)|rgbadjust|show(?:alpha|blue|green|red)|swapuv|tweak|[uv]toy8?|ytouv/\n        .source, // overlay\n      /(?:colorkey|reset)mask|layer|mask(?:hs)?|merge|overlay|subtract/.source, // geometry\n      /addborders|(?:bicubic|bilinear|blackman|gauss|lanczos4|lanczos|point|sinc|spline(?:16|36|64))resize|crop(?:bottom)?|flip(?:horizontal|vertical)|(?:horizontal|vertical)?reduceby2|letterbox|skewrows|turn(?:180|left|right)/\n        .source, // pixel\n      /blur|fixbrokenchromaupsampling|generalconvolution|(?:spatial|temporal)soften|sharpen/\n        .source, // timeline\n      /trim|(?:un)?alignedsplice|(?:assume|assumescaled|change|convert)FPS|(?:delete|duplicate)frame|dissolve|fade(?:in|io|out)[02]?|freezeframe|interleave|loop|reverse|select(?:even|odd|(?:range)?every)/\n        .source, // interlace\n      /assume[bt]ff|assume(?:field|frame)based|bob|complementparity|doubleweave|peculiarblend|pulldown|separate(?:columns|fields|rows)|swapfields|weave(?:columns|rows)?/\n        .source, // audio\n      /amplify(?:db)?|assumesamplerate|audiodub(?:ex)?|audiotrim|convertaudioto(?:(?:8|16|24|32)bit|float)|converttomono|delayaudio|ensurevbrmp3sync|get(?:left|right)?channel|kill(?:audio|video)|mergechannels|mixaudio|monotostereo|normalize|resampleaudio|ssrc|supereq|timestretch/\n        .source, // conditional\n      /animate|applyrange|conditional(?:filter|reader|select)|frameevaluate|scriptclip|tcp(?:server|source)|writefile(?:end|if|start)?/\n        .source, // export\n      /imagewriter/.source, // debug\n      /blackness|blankclip|colorbars(?:hd)?|compare|dumpfiltergraph|echo|histogram|info|messageclip|preroll|setgraphanalysis|show(?:framenumber|smpte|time)|showfiveversions|stack(?:horizontal|vertical)|subtitle|tone|version/\n        .source\n    ].join('|')\n    var allinternals = [internals, properties, filters].join('|')\n    Prism.languages.avisynth = {\n      comment: [\n        {\n          // Matches [* *] nestable block comments, but only supports 1 level of nested comments\n          // /\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|<self>)*\\*\\]/\n          pattern:\n            /(^|[^\\\\])\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\]))*\\*\\])*\\*\\]/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // Matches /* */ block comments\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // Matches # comments\n          pattern: /(^|[^\\\\$])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      // Handle before strings because optional arguments are surrounded by double quotes\n      argument: {\n        pattern: re(/\\b(?:<<0>>)\\s+(\"?)\\w+\\1/.source, [types], 'i'),\n        inside: {\n          keyword: /^\\w+/\n        }\n      },\n      // Optional argument assignment\n      'argument-label': {\n        pattern: /([,(][\\s\\\\]*)\\w+\\s*=(?!=)/,\n        lookbehind: true,\n        inside: {\n          'argument-name': {\n            pattern: /^\\w+/,\n            alias: 'punctuation'\n          },\n          punctuation: /=$/\n        }\n      },\n      string: [\n        {\n          // triple double-quoted\n          pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n          greedy: true\n        },\n        {\n          // single double-quoted\n          pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n          greedy: true,\n          inside: {\n            constant: {\n              // These *are* case-sensitive!\n              pattern:\n                /\\b(?:DEFAULT_MT_MODE|(?:MAINSCRIPT|PROGRAM|SCRIPT)DIR|(?:MACHINE|USER)_(?:CLASSIC|PLUS)_PLUGINS)\\b/\n            }\n          }\n        }\n      ],\n      // The special \"last\" variable that takes the value of the last implicitly returned clip\n      variable: /\\b(?:last)\\b/i,\n      boolean: /\\b(?:false|no|true|yes)\\b/i,\n      keyword:\n        /\\b(?:catch|else|for|function|global|if|return|try|while|__END__)\\b/i,\n      constant: /\\bMT_(?:MULTI_INSTANCE|NICE_FILTER|SERIALIZED|SPECIAL_MT)\\b/,\n      // AviSynth's internal functions, filters, and properties\n      'builtin-function': {\n        pattern: re(/\\b(?:<<0>>)\\b/.source, [allinternals], 'i'),\n        alias: 'function'\n      },\n      'type-cast': {\n        pattern: re(/\\b(?:<<0>>)(?=\\s*\\()/.source, [types], 'i'),\n        alias: 'keyword'\n      },\n      // External/user-defined filters\n      function: {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()|(\\.)[a-z_]\\w*\\b/i,\n        lookbehind: true\n      },\n      // Matches a \\ as the first or last character on a line\n      'line-continuation': {\n        pattern: /(^[ \\t]*)\\\\|\\\\(?=[ \\t]*$)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      number:\n        /\\B\\$(?:[\\da-f]{6}|[\\da-f]{8})\\b|(?:(?:\\b|\\B-)\\d+(?:\\.\\d*)?\\b|\\B\\.\\d+\\b)/i,\n      operator: /\\+\\+?|[!=<>]=?|&&|\\|\\||[?:*/%-]/,\n      punctuation: /[{}\\[\\]();,.]/\n    }\n    Prism.languages.avs = Prism.languages.avisynth\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AAC1B,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EACA;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,SAASC,OAAOA,CAACC,OAAO,EAAEC,YAAY,EAAE;MACtC,OAAOD,OAAO,CAACD,OAAO,CAAC,YAAY,EAAE,UAAUG,CAAC,EAAEC,KAAK,EAAE;QACvD,OAAOF,YAAY,CAAC,CAACE,KAAK,CAAC;MAC7B,CAAC,CAAC;IACJ;IACA,SAASC,EAAEA,CAACJ,OAAO,EAAEC,YAAY,EAAEI,KAAK,EAAE;MACxC,OAAOC,MAAM,CAACP,OAAO,CAACC,OAAO,EAAEC,YAAY,CAAC,EAAEI,KAAK,IAAI,EAAE,CAAC;IAC5D;IACA,IAAIE,KAAK,GAAG,gCAAgC,CAACC,MAAM;IACnD,IAAIC,SAAS,GAAG;IACd;IACA,gFAAgF,CAC7ED,MAAM;IAAE;IACX,uDAAuD,CAACA,MAAM;IAAE;IAChE,oNAAoN,CACjNA,MAAM;IAAE;IACX,qBAAqB,CAACA,MAAM;IAAE;IAC9B,+HAA+H,CAC5HA,MAAM;IAAE;IACX,4BAA4B,CAACA,MAAM;IAAE;IACrC,yHAAyH,CACtHA,MAAM;IAAE;IACX,8LAA8L,CAC3LA,MAAM;IAAE;IACX,uFAAuF,CACpFA,MAAM;IAAE;IACX,kIAAkI,CAC/HA,MAAM;IAAE;IACX,6CAA6C,CAACA,MAAM;IAAE;IACtD,0CAA0C,CAACA,MAAM;IAAE;IACnD,wDAAwD,CAACA,MAAM,CAChE,CAACE,IAAI,CAAC,GAAG,CAAC;IACX,IAAIC,UAAU,GAAG;IACf;IACA,oBAAoB,CAACH,MAAM;IAAE;IAC7B,cAAc,CAACA,MAAM;IAAE;IACvB,wDAAwD,CAACA,MAAM;IAAE;IACjE,kCAAkC,CAACA,MAAM;IAAE;IAC3C,qLAAqL,CAClLA,MAAM;IAAE;IACX,iFAAiF,CAC9EA,MAAM,CACV,CAACE,IAAI,CAAC,GAAG,CAAC;IACX,IAAIE,OAAO,GAAG;IACZ;IACA,uIAAuI,CACpIJ,MAAM;IAAE;IACX,sRAAsR,CACnRA,MAAM;IAAE;IACX,iEAAiE,CAACA,MAAM;IAAE;IAC1E,6NAA6N,CAC1NA,MAAM;IAAE;IACX,sFAAsF,CACnFA,MAAM;IAAE;IACX,sMAAsM,CACnMA,MAAM;IAAE;IACX,mKAAmK,CAChKA,MAAM;IAAE;IACX,kRAAkR,CAC/QA,MAAM;IAAE;IACX,iIAAiI,CAC9HA,MAAM;IAAE;IACX,aAAa,CAACA,MAAM;IAAE;IACtB,0NAA0N,CACvNA,MAAM,CACV,CAACE,IAAI,CAAC,GAAG,CAAC;IACX,IAAIG,YAAY,GAAG,CAACJ,SAAS,EAAEE,UAAU,EAAEC,OAAO,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC;IAC7DZ,KAAK,CAACgB,SAAS,CAACnB,QAAQ,GAAG;MACzBoB,OAAO,EAAE,CACP;QACE;QACA;QACAf,OAAO,EACL,sFAAsF;QACxFgB,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAjB,OAAO,EAAE,iCAAiC;QAC1CgB,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAjB,OAAO,EAAE,eAAe;QACxBgB,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACD;MACAC,QAAQ,EAAE;QACRlB,OAAO,EAAEI,EAAE,CAAC,yBAAyB,CAACI,MAAM,EAAE,CAACD,KAAK,CAAC,EAAE,GAAG,CAAC;QAC3DY,MAAM,EAAE;UACNC,OAAO,EAAE;QACX;MACF,CAAC;MACD;MACA,gBAAgB,EAAE;QAChBpB,OAAO,EAAE,2BAA2B;QACpCgB,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE;UACN,eAAe,EAAE;YACfnB,OAAO,EAAE,MAAM;YACfqB,KAAK,EAAE;UACT,CAAC;UACDC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,MAAM,EAAE,CACN;QACE;QACAvB,OAAO,EAAE,gBAAgB;QACzBiB,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAjB,OAAO,EAAE,qCAAqC;QAC9CiB,MAAM,EAAE,IAAI;QACZE,MAAM,EAAE;UACNK,QAAQ,EAAE;YACR;YACAxB,OAAO,EACL;UACJ;QACF;MACF,CAAC,CACF;MACD;MACAyB,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,4BAA4B;MACrCN,OAAO,EACL,qEAAqE;MACvEI,QAAQ,EAAE,6DAA6D;MACvE;MACA,kBAAkB,EAAE;QAClBxB,OAAO,EAAEI,EAAE,CAAC,eAAe,CAACI,MAAM,EAAE,CAACK,YAAY,CAAC,EAAE,GAAG,CAAC;QACxDQ,KAAK,EAAE;MACT,CAAC;MACD,WAAW,EAAE;QACXrB,OAAO,EAAEI,EAAE,CAAC,sBAAsB,CAACI,MAAM,EAAE,CAACD,KAAK,CAAC,EAAE,GAAG,CAAC;QACxDc,KAAK,EAAE;MACT,CAAC;MACD;MACAM,QAAQ,EAAE;QACR3B,OAAO,EAAE,uCAAuC;QAChDgB,UAAU,EAAE;MACd,CAAC;MACD;MACA,mBAAmB,EAAE;QACnBhB,OAAO,EAAE,4BAA4B;QACrCgB,UAAU,EAAE,IAAI;QAChBK,KAAK,EAAE;MACT,CAAC;MACDO,MAAM,EACJ,0EAA0E;MAC5EC,QAAQ,EAAE,iCAAiC;MAC3CP,WAAW,EAAE;IACf,CAAC;IACDxB,KAAK,CAACgB,SAAS,CAACgB,GAAG,GAAGhC,KAAK,CAACgB,SAAS,CAACnB,QAAQ;EAChD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}