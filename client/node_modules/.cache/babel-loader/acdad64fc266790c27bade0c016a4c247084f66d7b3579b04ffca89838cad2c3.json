{"ast": null, "code": "'use strict';\n\nvar create = require('./util/create');\nmodule.exports = create({\n  space: 'xml',\n  transform: xmlTransform,\n  properties: {\n    xmlLang: null,\n    xmlBase: null,\n    xmlSpace: null\n  }\n});\nfunction xmlTransform(_, prop) {\n  return 'xml:' + prop.slice(3).toLowerCase();\n}", "map": {"version": 3, "names": ["create", "require", "module", "exports", "space", "transform", "xmlTransform", "properties", "xmlLang", "xmlBase", "xmlSpace", "_", "prop", "slice", "toLowerCase"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/lib/xml.js"], "sourcesContent": ["'use strict'\n\nvar create = require('./util/create')\n\nmodule.exports = create({\n  space: 'xml',\n  transform: xmlTransform,\n  properties: {\n    xmlLang: null,\n    xmlBase: null,\n    xmlSpace: null\n  }\n})\n\nfunction xmlTransform(_, prop) {\n  return 'xml:' + prop.slice(3).toLowerCase()\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,eAAe,CAAC;AAErCC,MAAM,CAACC,OAAO,GAAGH,MAAM,CAAC;EACtBI,KAAK,EAAE,KAAK;EACZC,SAAS,EAAEC,YAAY;EACvBC,UAAU,EAAE;IACVC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,SAASJ,YAAYA,CAACK,CAAC,EAAEC,IAAI,EAAE;EAC7B,OAAO,MAAM,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}