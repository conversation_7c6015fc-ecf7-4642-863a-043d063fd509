{"ast": null, "code": "'use strict';\n\nmodule.exports = jq;\njq.displayName = 'jq';\njq.aliases = [];\nfunction jq(Prism) {\n  ;\n  (function (Prism) {\n    var interpolation = /\\\\\\((?:[^()]|\\([^()]*\\))*\\)/.source;\n    var string = RegExp(/(^|[^\\\\])\"(?:[^\"\\r\\n\\\\]|\\\\[^\\r\\n(]|__)*\"/.source.replace(/__/g, function () {\n      return interpolation;\n    }));\n    var stringInterpolation = {\n      interpolation: {\n        pattern: RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + interpolation),\n        lookbehind: true,\n        inside: {\n          content: {\n            pattern: /^(\\\\\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          punctuation: /^\\\\\\(|\\)$/\n        }\n      }\n    };\n    var jq = Prism.languages.jq = {\n      comment: /#.*/,\n      property: {\n        pattern: RegExp(string.source + /(?=\\s*:(?!:))/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      string: {\n        pattern: string,\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      function: {\n        pattern: /(\\bdef\\s+)[a-z_]\\w+/i,\n        lookbehind: true\n      },\n      variable: /\\B\\$\\w+/,\n      'property-literal': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        alias: 'property'\n      },\n      keyword: /\\b(?:as|break|catch|def|elif|else|end|foreach|if|import|include|label|module|modulemeta|null|reduce|then|try|while)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      number: /(?:\\b\\d+\\.|\\B\\.)?\\b\\d+(?:[eE][+-]?\\d+)?\\b/,\n      operator: [{\n        pattern: /\\|=?/,\n        alias: 'pipe'\n      }, /\\.\\.|[!=<>]?=|\\?\\/\\/|\\/\\/=?|[-+*/%]=?|[<>?]|\\b(?:and|not|or)\\b/],\n      'c-style-function': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n        alias: 'function'\n      },\n      punctuation: /::|[()\\[\\]{},:;]|\\.(?=\\s*[\\[\\w$])/,\n      dot: {\n        pattern: /\\./,\n        alias: 'important'\n      }\n    };\n    stringInterpolation.interpolation.inside.content.inside = jq;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "jq", "displayName", "aliases", "Prism", "interpolation", "source", "string", "RegExp", "replace", "stringInterpolation", "pattern", "lookbehind", "inside", "content", "punctuation", "languages", "comment", "property", "greedy", "function", "variable", "alias", "keyword", "boolean", "number", "operator", "dot"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/jq.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jq\njq.displayName = 'jq'\njq.aliases = []\nfunction jq(Prism) {\n  ;(function (Prism) {\n    var interpolation = /\\\\\\((?:[^()]|\\([^()]*\\))*\\)/.source\n    var string = RegExp(\n      /(^|[^\\\\])\"(?:[^\"\\r\\n\\\\]|\\\\[^\\r\\n(]|__)*\"/.source.replace(\n        /__/g,\n        function () {\n          return interpolation\n        }\n      )\n    )\n    var stringInterpolation = {\n      interpolation: {\n        pattern: RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + interpolation),\n        lookbehind: true,\n        inside: {\n          content: {\n            pattern: /^(\\\\\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          punctuation: /^\\\\\\(|\\)$/\n        }\n      }\n    }\n    var jq = (Prism.languages.jq = {\n      comment: /#.*/,\n      property: {\n        pattern: RegExp(string.source + /(?=\\s*:(?!:))/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      string: {\n        pattern: string,\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      function: {\n        pattern: /(\\bdef\\s+)[a-z_]\\w+/i,\n        lookbehind: true\n      },\n      variable: /\\B\\$\\w+/,\n      'property-literal': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        alias: 'property'\n      },\n      keyword:\n        /\\b(?:as|break|catch|def|elif|else|end|foreach|if|import|include|label|module|modulemeta|null|reduce|then|try|while)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      number: /(?:\\b\\d+\\.|\\B\\.)?\\b\\d+(?:[eE][+-]?\\d+)?\\b/,\n      operator: [\n        {\n          pattern: /\\|=?/,\n          alias: 'pipe'\n        },\n        /\\.\\.|[!=<>]?=|\\?\\/\\/|\\/\\/=?|[-+*/%]=?|[<>?]|\\b(?:and|not|or)\\b/\n      ],\n      'c-style-function': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n        alias: 'function'\n      },\n      punctuation: /::|[()\\[\\]{},:;]|\\.(?=\\s*[\\[\\w$])/,\n      dot: {\n        pattern: /\\./,\n        alias: 'important'\n      }\n    })\n    stringInterpolation.interpolation.inside.content.inside = jq\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,EAAE;AACnBA,EAAE,CAACC,WAAW,GAAG,IAAI;AACrBD,EAAE,CAACE,OAAO,GAAG,EAAE;AACf,SAASF,EAAEA,CAACG,KAAK,EAAE;EACjB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,aAAa,GAAG,6BAA6B,CAACC,MAAM;IACxD,IAAIC,MAAM,GAAGC,MAAM,CACjB,0CAA0C,CAACF,MAAM,CAACG,OAAO,CACvD,KAAK,EACL,YAAY;MACV,OAAOJ,aAAa;IACtB,CACF,CACF,CAAC;IACD,IAAIK,mBAAmB,GAAG;MACxBL,aAAa,EAAE;QACbM,OAAO,EAAEH,MAAM,CAAC,yBAAyB,CAACF,MAAM,GAAGD,aAAa,CAAC;QACjEO,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,OAAO,EAAE;YACPH,OAAO,EAAE,uBAAuB;YAChCC,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE,IAAI,CAAC;UACf,CAAC;UACDE,WAAW,EAAE;QACf;MACF;IACF,CAAC;IACD,IAAId,EAAE,GAAIG,KAAK,CAACY,SAAS,CAACf,EAAE,GAAG;MAC7BgB,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;QACRP,OAAO,EAAEH,MAAM,CAACD,MAAM,CAACD,MAAM,GAAG,eAAe,CAACA,MAAM,CAAC;QACvDM,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAE,IAAI;QACZN,MAAM,EAAEH;MACV,CAAC;MACDH,MAAM,EAAE;QACNI,OAAO,EAAEJ,MAAM;QACfK,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAE,IAAI;QACZN,MAAM,EAAEH;MACV,CAAC;MACDU,QAAQ,EAAE;QACRT,OAAO,EAAE,sBAAsB;QAC/BC,UAAU,EAAE;MACd,CAAC;MACDS,QAAQ,EAAE,SAAS;MACnB,kBAAkB,EAAE;QAClBV,OAAO,EAAE,2BAA2B;QACpCW,KAAK,EAAE;MACT,CAAC;MACDC,OAAO,EACL,uHAAuH;MACzHC,OAAO,EAAE,oBAAoB;MAC7BC,MAAM,EAAE,2CAA2C;MACnDC,QAAQ,EAAE,CACR;QACEf,OAAO,EAAE,MAAM;QACfW,KAAK,EAAE;MACT,CAAC,EACD,gEAAgE,CACjE;MACD,kBAAkB,EAAE;QAClBX,OAAO,EAAE,uBAAuB;QAChCW,KAAK,EAAE;MACT,CAAC;MACDP,WAAW,EAAE,mCAAmC;MAChDY,GAAG,EAAE;QACHhB,OAAO,EAAE,IAAI;QACbW,KAAK,EAAE;MACT;IACF,CAAE;IACFZ,mBAAmB,CAACL,aAAa,CAACQ,MAAM,CAACC,OAAO,CAACD,MAAM,GAAGZ,EAAE;EAC9D,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}