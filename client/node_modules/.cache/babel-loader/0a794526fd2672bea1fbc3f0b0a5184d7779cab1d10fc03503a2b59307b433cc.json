{"ast": null, "code": "/*\nLanguage: Erlang\nDescription: Erlang is a general-purpose functional language, with strict evaluation, single assignment, and dynamic typing.\nAuthor: <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.erlang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction erlang(hljs) {\n  const BASIC_ATOM_RE = '[a-z\\'][a-zA-Z0-9_\\']*';\n  const FUNCTION_NAME_RE = '(' + BASIC_ATOM_RE + ':' + BASIC_ATOM_RE + '|' + BASIC_ATOM_RE + ')';\n  const ERLANG_RESERVED = {\n    keyword: 'after and andalso|10 band begin bnot bor bsl bzr bxor case catch cond div end fun if ' + 'let not of orelse|10 query receive rem try when xor',\n    literal: 'false true'\n  };\n  const COMMENT = hljs.COMMENT('%', '$');\n  const NUMBER = {\n    className: 'number',\n    begin: '\\\\b(\\\\d+(_\\\\d+)*#[a-fA-F0-9]+(_[a-fA-F0-9]+)*|\\\\d+(_\\\\d+)*(\\\\.\\\\d+(_\\\\d+)*)?([eE][-+]?\\\\d+)?)',\n    relevance: 0\n  };\n  const NAMED_FUN = {\n    begin: 'fun\\\\s+' + BASIC_ATOM_RE + '/\\\\d+'\n  };\n  const FUNCTION_CALL = {\n    begin: FUNCTION_NAME_RE + '\\\\(',\n    end: '\\\\)',\n    returnBegin: true,\n    relevance: 0,\n    contains: [{\n      begin: FUNCTION_NAME_RE,\n      relevance: 0\n    }, {\n      begin: '\\\\(',\n      end: '\\\\)',\n      endsWithParent: true,\n      returnEnd: true,\n      relevance: 0\n      // \"contains\" defined later\n    }]\n  };\n  const TUPLE = {\n    begin: /\\{/,\n    end: /\\}/,\n    relevance: 0\n    // \"contains\" defined later\n  };\n  const VAR1 = {\n    begin: '\\\\b_([A-Z][A-Za-z0-9_]*)?',\n    relevance: 0\n  };\n  const VAR2 = {\n    begin: '[A-Z][a-zA-Z0-9_]*',\n    relevance: 0\n  };\n  const RECORD_ACCESS = {\n    begin: '#' + hljs.UNDERSCORE_IDENT_RE,\n    relevance: 0,\n    returnBegin: true,\n    contains: [{\n      begin: '#' + hljs.UNDERSCORE_IDENT_RE,\n      relevance: 0\n    }, {\n      begin: /\\{/,\n      end: /\\}/,\n      relevance: 0\n      // \"contains\" defined later\n    }]\n  };\n  const BLOCK_STATEMENTS = {\n    beginKeywords: 'fun receive if try case',\n    end: 'end',\n    keywords: ERLANG_RESERVED\n  };\n  BLOCK_STATEMENTS.contains = [COMMENT, NAMED_FUN, hljs.inherit(hljs.APOS_STRING_MODE, {\n    className: ''\n  }), BLOCK_STATEMENTS, FUNCTION_CALL, hljs.QUOTE_STRING_MODE, NUMBER, TUPLE, VAR1, VAR2, RECORD_ACCESS];\n  const BASIC_MODES = [COMMENT, NAMED_FUN, BLOCK_STATEMENTS, FUNCTION_CALL, hljs.QUOTE_STRING_MODE, NUMBER, TUPLE, VAR1, VAR2, RECORD_ACCESS];\n  FUNCTION_CALL.contains[1].contains = BASIC_MODES;\n  TUPLE.contains = BASIC_MODES;\n  RECORD_ACCESS.contains[1].contains = BASIC_MODES;\n  const DIRECTIVES = [\"-module\", \"-record\", \"-undef\", \"-export\", \"-ifdef\", \"-ifndef\", \"-author\", \"-copyright\", \"-doc\", \"-vsn\", \"-import\", \"-include\", \"-include_lib\", \"-compile\", \"-define\", \"-else\", \"-endif\", \"-file\", \"-behaviour\", \"-behavior\", \"-spec\"];\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: BASIC_MODES\n  };\n  return {\n    name: 'Erlang',\n    aliases: ['erl'],\n    keywords: ERLANG_RESERVED,\n    illegal: '(</|\\\\*=|\\\\+=|-=|/\\\\*|\\\\*/|\\\\(\\\\*|\\\\*\\\\))',\n    contains: [{\n      className: 'function',\n      begin: '^' + BASIC_ATOM_RE + '\\\\s*\\\\(',\n      end: '->',\n      returnBegin: true,\n      illegal: '\\\\(|#|//|/\\\\*|\\\\\\\\|:|;',\n      contains: [PARAMS, hljs.inherit(hljs.TITLE_MODE, {\n        begin: BASIC_ATOM_RE\n      })],\n      starts: {\n        end: ';|\\\\.',\n        keywords: ERLANG_RESERVED,\n        contains: BASIC_MODES\n      }\n    }, COMMENT, {\n      begin: '^-',\n      end: '\\\\.',\n      relevance: 0,\n      excludeEnd: true,\n      returnBegin: true,\n      keywords: {\n        $pattern: '-' + hljs.IDENT_RE,\n        keyword: DIRECTIVES.map(x => `${x}|1.5`).join(\" \")\n      },\n      contains: [PARAMS]\n    }, NUMBER, hljs.QUOTE_STRING_MODE, RECORD_ACCESS, VAR1, VAR2, TUPLE, {\n      begin: /\\.$/\n    } // relevance booster\n    ]\n  };\n}\nmodule.exports = erlang;", "map": {"version": 3, "names": ["erlang", "hljs", "BASIC_ATOM_RE", "FUNCTION_NAME_RE", "ERLANG_RESERVED", "keyword", "literal", "COMMENT", "NUMBER", "className", "begin", "relevance", "NAMED_FUN", "FUNCTION_CALL", "end", "returnBegin", "contains", "endsWithParent", "returnEnd", "TUPLE", "VAR1", "VAR2", "RECORD_ACCESS", "UNDERSCORE_IDENT_RE", "BLOCK_STATEMENTS", "beginKeywords", "keywords", "inherit", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "BASIC_MODES", "DIRECTIVES", "PARAMS", "name", "aliases", "illegal", "TITLE_MODE", "starts", "excludeEnd", "$pattern", "IDENT_RE", "map", "x", "join", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/erlang.js"], "sourcesContent": ["/*\nLanguage: Erlang\nDescription: Erlang is a general-purpose functional language, with strict evaluation, single assignment, and dynamic typing.\nAuthor: <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.erlang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction erlang(hljs) {\n  const BASIC_ATOM_RE = '[a-z\\'][a-zA-Z0-9_\\']*';\n  const FUNCTION_NAME_RE = '(' + BASIC_ATOM_RE + ':' + BASIC_ATOM_RE + '|' + BASIC_ATOM_RE + ')';\n  const ERLANG_RESERVED = {\n    keyword:\n      'after and andalso|10 band begin bnot bor bsl bzr bxor case catch cond div end fun if ' +\n      'let not of orelse|10 query receive rem try when xor',\n    literal:\n      'false true'\n  };\n\n  const COMMENT = hljs.COMMENT('%', '$');\n  const NUMBER = {\n    className: 'number',\n    begin: '\\\\b(\\\\d+(_\\\\d+)*#[a-fA-F0-9]+(_[a-fA-F0-9]+)*|\\\\d+(_\\\\d+)*(\\\\.\\\\d+(_\\\\d+)*)?([eE][-+]?\\\\d+)?)',\n    relevance: 0\n  };\n  const NAMED_FUN = {\n    begin: 'fun\\\\s+' + BASIC_ATOM_RE + '/\\\\d+'\n  };\n  const FUNCTION_CALL = {\n    begin: FUNCTION_NAME_RE + '\\\\(',\n    end: '\\\\)',\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        begin: FUNCTION_NAME_RE,\n        relevance: 0\n      },\n      {\n        begin: '\\\\(',\n        end: '\\\\)',\n        endsWithParent: true,\n        returnEnd: true,\n        relevance: 0\n        // \"contains\" defined later\n      }\n    ]\n  };\n  const TUPLE = {\n    begin: /\\{/,\n    end: /\\}/,\n    relevance: 0\n    // \"contains\" defined later\n  };\n  const VAR1 = {\n    begin: '\\\\b_([A-Z][A-Za-z0-9_]*)?',\n    relevance: 0\n  };\n  const VAR2 = {\n    begin: '[A-Z][a-zA-Z0-9_]*',\n    relevance: 0\n  };\n  const RECORD_ACCESS = {\n    begin: '#' + hljs.UNDERSCORE_IDENT_RE,\n    relevance: 0,\n    returnBegin: true,\n    contains: [\n      {\n        begin: '#' + hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /\\{/,\n        end: /\\}/,\n        relevance: 0\n        // \"contains\" defined later\n      }\n    ]\n  };\n\n  const BLOCK_STATEMENTS = {\n    beginKeywords: 'fun receive if try case',\n    end: 'end',\n    keywords: ERLANG_RESERVED\n  };\n  BLOCK_STATEMENTS.contains = [\n    COMMENT,\n    NAMED_FUN,\n    hljs.inherit(hljs.APOS_STRING_MODE, {\n      className: ''\n    }),\n    BLOCK_STATEMENTS,\n    FUNCTION_CALL,\n    hljs.QUOTE_STRING_MODE,\n    NUMBER,\n    TUPLE,\n    VAR1,\n    VAR2,\n    RECORD_ACCESS\n  ];\n\n  const BASIC_MODES = [\n    COMMENT,\n    NAMED_FUN,\n    BLOCK_STATEMENTS,\n    FUNCTION_CALL,\n    hljs.QUOTE_STRING_MODE,\n    NUMBER,\n    TUPLE,\n    VAR1,\n    VAR2,\n    RECORD_ACCESS\n  ];\n  FUNCTION_CALL.contains[1].contains = BASIC_MODES;\n  TUPLE.contains = BASIC_MODES;\n  RECORD_ACCESS.contains[1].contains = BASIC_MODES;\n\n  const DIRECTIVES = [\n    \"-module\",\n    \"-record\",\n    \"-undef\",\n    \"-export\",\n    \"-ifdef\",\n    \"-ifndef\",\n    \"-author\",\n    \"-copyright\",\n    \"-doc\",\n    \"-vsn\",\n    \"-import\",\n    \"-include\",\n    \"-include_lib\",\n    \"-compile\",\n    \"-define\",\n    \"-else\",\n    \"-endif\",\n    \"-file\",\n    \"-behaviour\",\n    \"-behavior\",\n    \"-spec\"\n  ];\n\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: BASIC_MODES\n  };\n  return {\n    name: 'Erlang',\n    aliases: ['erl'],\n    keywords: ERLANG_RESERVED,\n    illegal: '(</|\\\\*=|\\\\+=|-=|/\\\\*|\\\\*/|\\\\(\\\\*|\\\\*\\\\))',\n    contains: [\n      {\n        className: 'function',\n        begin: '^' + BASIC_ATOM_RE + '\\\\s*\\\\(',\n        end: '->',\n        returnBegin: true,\n        illegal: '\\\\(|#|//|/\\\\*|\\\\\\\\|:|;',\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: BASIC_ATOM_RE\n          })\n        ],\n        starts: {\n          end: ';|\\\\.',\n          keywords: ERLANG_RESERVED,\n          contains: BASIC_MODES\n        }\n      },\n      COMMENT,\n      {\n        begin: '^-',\n        end: '\\\\.',\n        relevance: 0,\n        excludeEnd: true,\n        returnBegin: true,\n        keywords: {\n          $pattern: '-' + hljs.IDENT_RE,\n          keyword: DIRECTIVES.map(x => `${x}|1.5`).join(\" \")\n        },\n        contains: [PARAMS]\n      },\n      NUMBER,\n      hljs.QUOTE_STRING_MODE,\n      RECORD_ACCESS,\n      VAR1,\n      VAR2,\n      TUPLE,\n      {\n        begin: /\\.$/\n      } // relevance booster\n    ]\n  };\n}\n\nmodule.exports = erlang;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,aAAa,GAAG,wBAAwB;EAC9C,MAAMC,gBAAgB,GAAG,GAAG,GAAGD,aAAa,GAAG,GAAG,GAAGA,aAAa,GAAG,GAAG,GAAGA,aAAa,GAAG,GAAG;EAC9F,MAAME,eAAe,GAAG;IACtBC,OAAO,EACL,uFAAuF,GACvF,qDAAqD;IACvDC,OAAO,EACL;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGN,IAAI,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACtC,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,+FAA+F;IACtGC,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,SAAS,GAAG;IAChBF,KAAK,EAAE,SAAS,GAAGR,aAAa,GAAG;EACrC,CAAC;EACD,MAAMW,aAAa,GAAG;IACpBH,KAAK,EAAEP,gBAAgB,GAAG,KAAK;IAC/BW,GAAG,EAAE,KAAK;IACVC,WAAW,EAAE,IAAI;IACjBJ,SAAS,EAAE,CAAC;IACZK,QAAQ,EAAE,CACR;MACEN,KAAK,EAAEP,gBAAgB;MACvBQ,SAAS,EAAE;IACb,CAAC,EACD;MACED,KAAK,EAAE,KAAK;MACZI,GAAG,EAAE,KAAK;MACVG,cAAc,EAAE,IAAI;MACpBC,SAAS,EAAE,IAAI;MACfP,SAAS,EAAE;MACX;IACF,CAAC;EAEL,CAAC;EACD,MAAMQ,KAAK,GAAG;IACZT,KAAK,EAAE,IAAI;IACXI,GAAG,EAAE,IAAI;IACTH,SAAS,EAAE;IACX;EACF,CAAC;EACD,MAAMS,IAAI,GAAG;IACXV,KAAK,EAAE,2BAA2B;IAClCC,SAAS,EAAE;EACb,CAAC;EACD,MAAMU,IAAI,GAAG;IACXX,KAAK,EAAE,oBAAoB;IAC3BC,SAAS,EAAE;EACb,CAAC;EACD,MAAMW,aAAa,GAAG;IACpBZ,KAAK,EAAE,GAAG,GAAGT,IAAI,CAACsB,mBAAmB;IACrCZ,SAAS,EAAE,CAAC;IACZI,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,CACR;MACEN,KAAK,EAAE,GAAG,GAAGT,IAAI,CAACsB,mBAAmB;MACrCZ,SAAS,EAAE;IACb,CAAC,EACD;MACED,KAAK,EAAE,IAAI;MACXI,GAAG,EAAE,IAAI;MACTH,SAAS,EAAE;MACX;IACF,CAAC;EAEL,CAAC;EAED,MAAMa,gBAAgB,GAAG;IACvBC,aAAa,EAAE,yBAAyB;IACxCX,GAAG,EAAE,KAAK;IACVY,QAAQ,EAAEtB;EACZ,CAAC;EACDoB,gBAAgB,CAACR,QAAQ,GAAG,CAC1BT,OAAO,EACPK,SAAS,EACTX,IAAI,CAAC0B,OAAO,CAAC1B,IAAI,CAAC2B,gBAAgB,EAAE;IAClCnB,SAAS,EAAE;EACb,CAAC,CAAC,EACFe,gBAAgB,EAChBX,aAAa,EACbZ,IAAI,CAAC4B,iBAAiB,EACtBrB,MAAM,EACNW,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,aAAa,CACd;EAED,MAAMQ,WAAW,GAAG,CAClBvB,OAAO,EACPK,SAAS,EACTY,gBAAgB,EAChBX,aAAa,EACbZ,IAAI,CAAC4B,iBAAiB,EACtBrB,MAAM,EACNW,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,aAAa,CACd;EACDT,aAAa,CAACG,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,GAAGc,WAAW;EAChDX,KAAK,CAACH,QAAQ,GAAGc,WAAW;EAC5BR,aAAa,CAACN,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,GAAGc,WAAW;EAEhD,MAAMC,UAAU,GAAG,CACjB,SAAS,EACT,SAAS,EACT,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EACZ,MAAM,EACN,MAAM,EACN,SAAS,EACT,UAAU,EACV,cAAc,EACd,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,WAAW,EACX,OAAO,CACR;EAED,MAAMC,MAAM,GAAG;IACbvB,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZI,GAAG,EAAE,KAAK;IACVE,QAAQ,EAAEc;EACZ,CAAC;EACD,OAAO;IACLG,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBR,QAAQ,EAAEtB,eAAe;IACzB+B,OAAO,EAAE,2CAA2C;IACpDnB,QAAQ,EAAE,CACR;MACEP,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,GAAG,GAAGR,aAAa,GAAG,SAAS;MACtCY,GAAG,EAAE,IAAI;MACTC,WAAW,EAAE,IAAI;MACjBoB,OAAO,EAAE,wBAAwB;MACjCnB,QAAQ,EAAE,CACRgB,MAAM,EACN/B,IAAI,CAAC0B,OAAO,CAAC1B,IAAI,CAACmC,UAAU,EAAE;QAC5B1B,KAAK,EAAER;MACT,CAAC,CAAC,CACH;MACDmC,MAAM,EAAE;QACNvB,GAAG,EAAE,OAAO;QACZY,QAAQ,EAAEtB,eAAe;QACzBY,QAAQ,EAAEc;MACZ;IACF,CAAC,EACDvB,OAAO,EACP;MACEG,KAAK,EAAE,IAAI;MACXI,GAAG,EAAE,KAAK;MACVH,SAAS,EAAE,CAAC;MACZ2B,UAAU,EAAE,IAAI;MAChBvB,WAAW,EAAE,IAAI;MACjBW,QAAQ,EAAE;QACRa,QAAQ,EAAE,GAAG,GAAGtC,IAAI,CAACuC,QAAQ;QAC7BnC,OAAO,EAAE0B,UAAU,CAACU,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG;MACnD,CAAC;MACD3B,QAAQ,EAAE,CAACgB,MAAM;IACnB,CAAC,EACDxB,MAAM,EACNP,IAAI,CAAC4B,iBAAiB,EACtBP,aAAa,EACbF,IAAI,EACJC,IAAI,EACJF,KAAK,EACL;MACET,KAAK,EAAE;IACT,CAAC,CAAC;IAAA;EAEN,CAAC;AACH;AAEAkC,MAAM,CAACC,OAAO,GAAG7C,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}