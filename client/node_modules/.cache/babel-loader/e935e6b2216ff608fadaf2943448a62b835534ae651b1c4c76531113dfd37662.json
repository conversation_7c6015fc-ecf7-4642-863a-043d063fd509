{"ast": null, "code": "export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"white\",\n    \"background\": \"none\",\n    \"textShadow\": \"0 -.1em .2em black\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"white\",\n    \"background\": \"hsl(30, 20%, 25%)\",\n    \"textShadow\": \"0 -.1em .2em black\",\n    \"fontFamily\": \"<PERSON>solas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\",\n    \"border\": \".3em solid hsl(30, 20%, 40%)\",\n    \"borderRadius\": \".5em\",\n    \"boxShadow\": \"1px 1px .5em black inset\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"hsl(30, 20%, 25%)\",\n    \"padding\": \".15em .2em .05em\",\n    \"borderRadius\": \".3em\",\n    \"border\": \".13em solid hsl(30, 20%, 40%)\",\n    \"boxShadow\": \"1px 1px .3em -.1em black inset\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"prolog\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"doctype\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"cdata\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"punctuation\": {\n    \"Opacity\": \".7\"\n  },\n  \"namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"tag\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"boolean\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"number\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"constant\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"symbol\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"selector\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"attr-name\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"string\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"char\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"builtin\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"inserted\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"operator\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \"entity\": {\n    \"color\": \"hsl(40, 90%, 60%)\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \".style .token.string\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \"variable\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \"atrule\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"attr-value\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"keyword\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"regex\": {\n    \"color\": \"#e90\"\n  },\n  \"important\": {\n    \"color\": \"#e90\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  },\n  \"deleted\": {\n    \"color\": \"red\"\n  }\n};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/styles/prism/dark.js"], "sourcesContent": ["export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"white\",\n    \"background\": \"none\",\n    \"textShadow\": \"0 -.1em .2em black\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"white\",\n    \"background\": \"hsl(30, 20%, 25%)\",\n    \"textShadow\": \"0 -.1em .2em black\",\n    \"fontFamily\": \"<PERSON>solas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\",\n    \"border\": \".3em solid hsl(30, 20%, 40%)\",\n    \"borderRadius\": \".5em\",\n    \"boxShadow\": \"1px 1px .5em black inset\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"hsl(30, 20%, 25%)\",\n    \"padding\": \".15em .2em .05em\",\n    \"borderRadius\": \".3em\",\n    \"border\": \".13em solid hsl(30, 20%, 40%)\",\n    \"boxShadow\": \"1px 1px .3em -.1em black inset\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"prolog\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"doctype\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"cdata\": {\n    \"color\": \"hsl(30, 20%, 50%)\"\n  },\n  \"punctuation\": {\n    \"Opacity\": \".7\"\n  },\n  \"namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"tag\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"boolean\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"number\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"constant\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"symbol\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"selector\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"attr-name\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"string\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"char\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"builtin\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"inserted\": {\n    \"color\": \"hsl(75, 70%, 60%)\"\n  },\n  \"operator\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \"entity\": {\n    \"color\": \"hsl(40, 90%, 60%)\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \".style .token.string\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \"variable\": {\n    \"color\": \"hsl(40, 90%, 60%)\"\n  },\n  \"atrule\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"attr-value\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"keyword\": {\n    \"color\": \"hsl(350, 40%, 70%)\"\n  },\n  \"regex\": {\n    \"color\": \"#e90\"\n  },\n  \"important\": {\n    \"color\": \"#e90\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  },\n  \"deleted\": {\n    \"color\": \"red\"\n  }\n};"], "mappings": "AAAA,eAAe;EACb,4BAA4B,EAAE;IAC5B,OAAO,EAAE,OAAO;IAChB,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE,oBAAoB;IAClC,YAAY,EAAE,2DAA2D;IACzE,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE;EACb,CAAC;EACD,2BAA2B,EAAE;IAC3B,OAAO,EAAE,OAAO;IAChB,YAAY,EAAE,mBAAmB;IACjC,YAAY,EAAE,oBAAoB;IAClC,YAAY,EAAE,2DAA2D;IACzE,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE,MAAM;IAClB,QAAQ,EAAE,8BAA8B;IACxC,cAAc,EAAE,MAAM;IACtB,WAAW,EAAE;EACf,CAAC;EACD,wCAAwC,EAAE;IACxC,YAAY,EAAE,mBAAmB;IACjC,SAAS,EAAE,kBAAkB;IAC7B,cAAc,EAAE,MAAM;IACtB,QAAQ,EAAE,+BAA+B;IACzC,WAAW,EAAE,gCAAgC;IAC7C,YAAY,EAAE;EAChB,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,SAAS,EAAE;EACb,CAAC;EACD,WAAW,EAAE;IACX,SAAS,EAAE;EACb,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACN,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE,mBAAmB;IAC5B,QAAQ,EAAE;EACZ,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,6BAA6B,EAAE;IAC7B,OAAO,EAAE;EACX,CAAC;EACD,sBAAsB,EAAE;IACtB,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE,MAAM;IACf,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACN,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,WAAW,EAAE;EACf,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}