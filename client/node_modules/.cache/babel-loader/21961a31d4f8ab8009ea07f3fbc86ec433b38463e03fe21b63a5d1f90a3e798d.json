{"ast": null, "code": "'use strict';\n\nmodule.exports = editorconfig;\neditorconfig.displayName = 'editorconfig';\neditorconfig.aliases = [];\nfunction editorconfig(Prism) {\n  Prism.languages.editorconfig = {\n    // https://editorconfig-specification.readthedocs.io\n    comment: /[;#].*/,\n    section: {\n      pattern: /(^[ \\t]*)\\[.+\\]/m,\n      lookbehind: true,\n      alias: 'selector',\n      inside: {\n        regex: /\\\\\\\\[\\[\\]{},!?.*]/,\n        // Escape special characters with '\\\\'\n        operator: /[!?]|\\.\\.|\\*{1,2}/,\n        punctuation: /[\\[\\]{},]/\n      }\n    },\n    key: {\n      pattern: /(^[ \\t]*)[^\\s=]+(?=[ \\t]*=)/m,\n      lookbehind: true,\n      alias: 'attr-name'\n    },\n    value: {\n      pattern: /=.*/,\n      alias: 'attr-value',\n      inside: {\n        punctuation: /^=/\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "editorconfig", "displayName", "aliases", "Prism", "languages", "comment", "section", "pattern", "lookbehind", "alias", "inside", "regex", "operator", "punctuation", "key", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/editorconfig.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = editorconfig\neditorconfig.displayName = 'editorconfig'\neditorconfig.aliases = []\nfunction editorconfig(Prism) {\n  Prism.languages.editorconfig = {\n    // https://editorconfig-specification.readthedocs.io\n    comment: /[;#].*/,\n    section: {\n      pattern: /(^[ \\t]*)\\[.+\\]/m,\n      lookbehind: true,\n      alias: 'selector',\n      inside: {\n        regex: /\\\\\\\\[\\[\\]{},!?.*]/,\n        // Escape special characters with '\\\\'\n        operator: /[!?]|\\.\\.|\\*{1,2}/,\n        punctuation: /[\\[\\]{},]/\n      }\n    },\n    key: {\n      pattern: /(^[ \\t]*)[^\\s=]+(?=[ \\t]*=)/m,\n      lookbehind: true,\n      alias: 'attr-name'\n    },\n    value: {\n      pattern: /=.*/,\n      alias: 'attr-value',\n      inside: {\n        punctuation: /^=/\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3BA,KAAK,CAACC,SAAS,CAACJ,YAAY,GAAG;IAC7B;IACAK,OAAO,EAAE,QAAQ;IACjBC,OAAO,EAAE;MACPC,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QACNC,KAAK,EAAE,mBAAmB;QAC1B;QACAC,QAAQ,EAAE,mBAAmB;QAC7BC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,GAAG,EAAE;MACHP,OAAO,EAAE,8BAA8B;MACvCC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDM,KAAK,EAAE;MACLR,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE,YAAY;MACnBC,MAAM,EAAE;QACNG,WAAW,EAAE;MACf;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}