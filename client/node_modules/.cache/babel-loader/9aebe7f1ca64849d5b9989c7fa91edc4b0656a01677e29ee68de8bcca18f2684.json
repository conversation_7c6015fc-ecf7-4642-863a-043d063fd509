{"ast": null, "code": "'use strict';\n\nvar refractorT4Templating = require('./t4-templating.js');\nvar refractorVbnet = require('./vbnet.js');\nmodule.exports = t4Vb;\nt4Vb.displayName = 't4Vb';\nt4Vb.aliases = [];\nfunction t4Vb(Prism) {\n  Prism.register(refractorT4Templating);\n  Prism.register(refractorVbnet);\n  Prism.languages['t4-vb'] = Prism.languages['t4-templating'].createT4('vbnet');\n}", "map": {"version": 3, "names": ["refractorT4Templating", "require", "refractorVbnet", "module", "exports", "t4Vb", "displayName", "aliases", "Prism", "register", "languages", "createT4"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/t4-vb.js"], "sourcesContent": ["'use strict'\nvar refractorT4Templating = require('./t4-templating.js')\nvar refractorVbnet = require('./vbnet.js')\nmodule.exports = t4Vb\nt4Vb.displayName = 't4Vb'\nt4Vb.aliases = []\nfunction t4Vb(Prism) {\n  Prism.register(refractorT4Templating)\n  Prism.register(refractorVbnet)\n  Prism.languages['t4-vb'] = Prism.languages['t4-templating'].createT4('vbnet')\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,qBAAqB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACzD,IAAIC,cAAc,GAAGD,OAAO,CAAC,YAAY,CAAC;AAC1CE,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,QAAQ,CAACT,qBAAqB,CAAC;EACrCQ,KAAK,CAACC,QAAQ,CAACP,cAAc,CAAC;EAC9BM,KAAK,CAACE,SAAS,CAAC,OAAO,CAAC,GAAGF,KAAK,CAACE,SAAS,CAAC,eAAe,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC;AAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}