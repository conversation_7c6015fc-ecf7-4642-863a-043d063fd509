{"ast": null, "code": "'use strict';\n\nmodule.exports = graphql;\ngraphql.displayName = 'graphql';\ngraphql.aliases = [];\nfunction graphql(Prism) {\n  Prism.languages.graphql = {\n    comment: /#.*/,\n    description: {\n      pattern: /(?:\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")(?=\\s*[a-z_])/i,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        'language-markdown': {\n          pattern: /(^\"(?:\"\")?)(?!\\1)[\\s\\S]+(?=\\1$)/,\n          lookbehind: true,\n          inside: Prism.languages.markdown\n        }\n      }\n    },\n    string: {\n      pattern: /\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n      greedy: true\n    },\n    number: /(?:\\B-|\\b)\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n    boolean: /\\b(?:false|true)\\b/,\n    variable: /\\$[a-z_]\\w*/i,\n    directive: {\n      pattern: /@[a-z_]\\w*/i,\n      alias: 'function'\n    },\n    'attr-name': {\n      pattern: /\\b[a-z_]\\w*(?=\\s*(?:\\((?:[^()\"]|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")*\\))?:)/i,\n      greedy: true\n    },\n    'atom-input': {\n      pattern: /\\b[A-Z]\\w*Input\\b/,\n      alias: 'class-name'\n    },\n    scalar: /\\b(?:Boolean|Float|ID|Int|String)\\b/,\n    constant: /\\b[A-Z][A-Z_\\d]*\\b/,\n    'class-name': {\n      pattern: /(\\b(?:enum|implements|interface|on|scalar|type|union)\\s+|&\\s*|:\\s*|\\[)[A-Z_]\\w*/,\n      lookbehind: true\n    },\n    fragment: {\n      pattern: /(\\bfragment\\s+|\\.{3}\\s*(?!on\\b))[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'definition-mutation': {\n      pattern: /(\\bmutation\\s+)[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'definition-query': {\n      pattern: /(\\bquery\\s+)[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    keyword: /\\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\\b/,\n    operator: /[!=|&]|\\.{3}/,\n    'property-query': /\\w+(?=\\s*\\()/,\n    object: /\\w+(?=\\s*\\{)/,\n    punctuation: /[!(){}\\[\\]:=,]/,\n    property: /\\w+/\n  };\n  Prism.hooks.add('after-tokenize', function afterTokenizeGraphql(env) {\n    if (env.language !== 'graphql') {\n      return;\n    }\n    /**\n     * get the graphql token stream that we want to customize\n     *\n     * @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n     * @type {Token[]}\n     */\n    var validTokens = env.tokens.filter(function (token) {\n      return typeof token !== 'string' && token.type !== 'comment' && token.type !== 'scalar';\n    });\n    var currentIndex = 0;\n    /**\n     * Returns whether the token relative to the current index has the given type.\n     *\n     * @param {number} offset\n     * @returns {Token | undefined}\n     */\n    function getToken(offset) {\n      return validTokens[currentIndex + offset];\n    }\n    /**\n     * Returns whether the token relative to the current index has the given type.\n     *\n     * @param {readonly string[]} types\n     * @param {number} [offset=0]\n     * @returns {boolean}\n     */\n    function isTokenType(types, offset) {\n      offset = offset || 0;\n      for (var i = 0; i < types.length; i++) {\n        var token = getToken(i + offset);\n        if (!token || token.type !== types[i]) {\n          return false;\n        }\n      }\n      return true;\n    }\n    /**\n     * Returns the index of the closing bracket to an opening bracket.\n     *\n     * It is assumed that `token[currentIndex - 1]` is an opening bracket.\n     *\n     * If no closing bracket could be found, `-1` will be returned.\n     *\n     * @param {RegExp} open\n     * @param {RegExp} close\n     * @returns {number}\n     */\n    function findClosingBracket(open, close) {\n      var stackHeight = 1;\n      for (var i = currentIndex; i < validTokens.length; i++) {\n        var token = validTokens[i];\n        var content = token.content;\n        if (token.type === 'punctuation' && typeof content === 'string') {\n          if (open.test(content)) {\n            stackHeight++;\n          } else if (close.test(content)) {\n            stackHeight--;\n            if (stackHeight === 0) {\n              return i;\n            }\n          }\n        }\n      }\n      return -1;\n    }\n    /**\n     * Adds an alias to the given token.\n     *\n     * @param {Token} token\n     * @param {string} alias\n     * @returns {void}\n     */\n    function addAlias(token, alias) {\n      var aliases = token.alias;\n      if (!aliases) {\n        token.alias = aliases = [];\n      } else if (!Array.isArray(aliases)) {\n        token.alias = aliases = [aliases];\n      }\n      aliases.push(alias);\n    }\n    for (; currentIndex < validTokens.length;) {\n      var startToken = validTokens[currentIndex++]; // add special aliases for mutation tokens\n      if (startToken.type === 'keyword' && startToken.content === 'mutation') {\n        // any array of the names of all input variables (if any)\n        var inputVariables = [];\n        if (isTokenType(['definition-mutation', 'punctuation']) && getToken(1).content === '(') {\n          // definition\n          currentIndex += 2; // skip 'definition-mutation' and 'punctuation'\n          var definitionEnd = findClosingBracket(/^\\($/, /^\\)$/);\n          if (definitionEnd === -1) {\n            continue;\n          } // find all input variables\n          for (; currentIndex < definitionEnd; currentIndex++) {\n            var t = getToken(0);\n            if (t.type === 'variable') {\n              addAlias(t, 'variable-input');\n              inputVariables.push(t.content);\n            }\n          }\n          currentIndex = definitionEnd + 1;\n        }\n        if (isTokenType(['punctuation', 'property-query']) && getToken(0).content === '{') {\n          currentIndex++; // skip opening bracket\n          addAlias(getToken(0), 'property-mutation');\n          if (inputVariables.length > 0) {\n            var mutationEnd = findClosingBracket(/^\\{$/, /^\\}$/);\n            if (mutationEnd === -1) {\n              continue;\n            } // give references to input variables a special alias\n            for (var i = currentIndex; i < mutationEnd; i++) {\n              var varToken = validTokens[i];\n              if (varToken.type === 'variable' && inputVariables.indexOf(varToken.content) >= 0) {\n                addAlias(varToken, 'variable-input');\n              }\n            }\n          }\n        }\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "graphql", "displayName", "aliases", "Prism", "languages", "comment", "description", "pattern", "greedy", "alias", "inside", "lookbehind", "markdown", "string", "number", "boolean", "variable", "directive", "scalar", "constant", "fragment", "keyword", "operator", "object", "punctuation", "property", "hooks", "add", "afterTokenizeGraphql", "env", "language", "validTokens", "tokens", "filter", "token", "type", "currentIndex", "getToken", "offset", "isTokenType", "types", "i", "length", "findClosingBracket", "open", "close", "stackHeight", "content", "test", "add<PERSON><PERSON>s", "Array", "isArray", "push", "startToken", "inputVariables", "definitionEnd", "t", "mutationEnd", "varToken", "indexOf"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/graphql.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = graphql\ngraphql.displayName = 'graphql'\ngraphql.aliases = []\nfunction graphql(Prism) {\n  Prism.languages.graphql = {\n    comment: /#.*/,\n    description: {\n      pattern:\n        /(?:\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")(?=\\s*[a-z_])/i,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        'language-markdown': {\n          pattern: /(^\"(?:\"\")?)(?!\\1)[\\s\\S]+(?=\\1$)/,\n          lookbehind: true,\n          inside: Prism.languages.markdown\n        }\n      }\n    },\n    string: {\n      pattern: /\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n      greedy: true\n    },\n    number: /(?:\\B-|\\b)\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n    boolean: /\\b(?:false|true)\\b/,\n    variable: /\\$[a-z_]\\w*/i,\n    directive: {\n      pattern: /@[a-z_]\\w*/i,\n      alias: 'function'\n    },\n    'attr-name': {\n      pattern: /\\b[a-z_]\\w*(?=\\s*(?:\\((?:[^()\"]|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")*\\))?:)/i,\n      greedy: true\n    },\n    'atom-input': {\n      pattern: /\\b[A-Z]\\w*Input\\b/,\n      alias: 'class-name'\n    },\n    scalar: /\\b(?:Boolean|Float|ID|Int|String)\\b/,\n    constant: /\\b[A-Z][A-Z_\\d]*\\b/,\n    'class-name': {\n      pattern:\n        /(\\b(?:enum|implements|interface|on|scalar|type|union)\\s+|&\\s*|:\\s*|\\[)[A-Z_]\\w*/,\n      lookbehind: true\n    },\n    fragment: {\n      pattern: /(\\bfragment\\s+|\\.{3}\\s*(?!on\\b))[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'definition-mutation': {\n      pattern: /(\\bmutation\\s+)[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'definition-query': {\n      pattern: /(\\bquery\\s+)[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    keyword:\n      /\\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\\b/,\n    operator: /[!=|&]|\\.{3}/,\n    'property-query': /\\w+(?=\\s*\\()/,\n    object: /\\w+(?=\\s*\\{)/,\n    punctuation: /[!(){}\\[\\]:=,]/,\n    property: /\\w+/\n  }\n  Prism.hooks.add('after-tokenize', function afterTokenizeGraphql(env) {\n    if (env.language !== 'graphql') {\n      return\n    }\n    /**\n     * get the graphql token stream that we want to customize\n     *\n     * @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n     * @type {Token[]}\n     */\n    var validTokens = env.tokens.filter(function (token) {\n      return (\n        typeof token !== 'string' &&\n        token.type !== 'comment' &&\n        token.type !== 'scalar'\n      )\n    })\n    var currentIndex = 0\n    /**\n     * Returns whether the token relative to the current index has the given type.\n     *\n     * @param {number} offset\n     * @returns {Token | undefined}\n     */\n    function getToken(offset) {\n      return validTokens[currentIndex + offset]\n    }\n    /**\n     * Returns whether the token relative to the current index has the given type.\n     *\n     * @param {readonly string[]} types\n     * @param {number} [offset=0]\n     * @returns {boolean}\n     */\n    function isTokenType(types, offset) {\n      offset = offset || 0\n      for (var i = 0; i < types.length; i++) {\n        var token = getToken(i + offset)\n        if (!token || token.type !== types[i]) {\n          return false\n        }\n      }\n      return true\n    }\n    /**\n     * Returns the index of the closing bracket to an opening bracket.\n     *\n     * It is assumed that `token[currentIndex - 1]` is an opening bracket.\n     *\n     * If no closing bracket could be found, `-1` will be returned.\n     *\n     * @param {RegExp} open\n     * @param {RegExp} close\n     * @returns {number}\n     */\n    function findClosingBracket(open, close) {\n      var stackHeight = 1\n      for (var i = currentIndex; i < validTokens.length; i++) {\n        var token = validTokens[i]\n        var content = token.content\n        if (token.type === 'punctuation' && typeof content === 'string') {\n          if (open.test(content)) {\n            stackHeight++\n          } else if (close.test(content)) {\n            stackHeight--\n            if (stackHeight === 0) {\n              return i\n            }\n          }\n        }\n      }\n      return -1\n    }\n    /**\n     * Adds an alias to the given token.\n     *\n     * @param {Token} token\n     * @param {string} alias\n     * @returns {void}\n     */\n    function addAlias(token, alias) {\n      var aliases = token.alias\n      if (!aliases) {\n        token.alias = aliases = []\n      } else if (!Array.isArray(aliases)) {\n        token.alias = aliases = [aliases]\n      }\n      aliases.push(alias)\n    }\n    for (; currentIndex < validTokens.length; ) {\n      var startToken = validTokens[currentIndex++] // add special aliases for mutation tokens\n      if (startToken.type === 'keyword' && startToken.content === 'mutation') {\n        // any array of the names of all input variables (if any)\n        var inputVariables = []\n        if (\n          isTokenType(['definition-mutation', 'punctuation']) &&\n          getToken(1).content === '('\n        ) {\n          // definition\n          currentIndex += 2 // skip 'definition-mutation' and 'punctuation'\n          var definitionEnd = findClosingBracket(/^\\($/, /^\\)$/)\n          if (definitionEnd === -1) {\n            continue\n          } // find all input variables\n          for (; currentIndex < definitionEnd; currentIndex++) {\n            var t = getToken(0)\n            if (t.type === 'variable') {\n              addAlias(t, 'variable-input')\n              inputVariables.push(t.content)\n            }\n          }\n          currentIndex = definitionEnd + 1\n        }\n        if (\n          isTokenType(['punctuation', 'property-query']) &&\n          getToken(0).content === '{'\n        ) {\n          currentIndex++ // skip opening bracket\n          addAlias(getToken(0), 'property-mutation')\n          if (inputVariables.length > 0) {\n            var mutationEnd = findClosingBracket(/^\\{$/, /^\\}$/)\n            if (mutationEnd === -1) {\n              continue\n            } // give references to input variables a special alias\n            for (var i = currentIndex; i < mutationEnd; i++) {\n              var varToken = validTokens[i]\n              if (\n                varToken.type === 'variable' &&\n                inputVariables.indexOf(varToken.content) >= 0\n              ) {\n                addAlias(varToken, 'variable-input')\n              }\n            }\n          }\n        }\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxBK,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE;MACXC,OAAO,EACL,kEAAkE;MACpEC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE;QACN,mBAAmB,EAAE;UACnBH,OAAO,EAAE,iCAAiC;UAC1CI,UAAU,EAAE,IAAI;UAChBD,MAAM,EAAEP,KAAK,CAACC,SAAS,CAACQ;QAC1B;MACF;IACF,CAAC;IACDC,MAAM,EAAE;MACNN,OAAO,EAAE,gDAAgD;MACzDC,MAAM,EAAE;IACV,CAAC;IACDM,MAAM,EAAE,0CAA0C;IAClDC,OAAO,EAAE,oBAAoB;IAC7BC,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE;MACTV,OAAO,EAAE,aAAa;MACtBE,KAAK,EAAE;IACT,CAAC;IACD,WAAW,EAAE;MACXF,OAAO,EAAE,gEAAgE;MACzEC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZD,OAAO,EAAE,mBAAmB;MAC5BE,KAAK,EAAE;IACT,CAAC;IACDS,MAAM,EAAE,qCAAqC;IAC7CC,QAAQ,EAAE,oBAAoB;IAC9B,YAAY,EAAE;MACZZ,OAAO,EACL,iFAAiF;MACnFI,UAAU,EAAE;IACd,CAAC;IACDS,QAAQ,EAAE;MACRb,OAAO,EAAE,8CAA8C;MACvDI,UAAU,EAAE,IAAI;MAChBF,KAAK,EAAE;IACT,CAAC;IACD,qBAAqB,EAAE;MACrBF,OAAO,EAAE,6BAA6B;MACtCI,UAAU,EAAE,IAAI;MAChBF,KAAK,EAAE;IACT,CAAC;IACD,kBAAkB,EAAE;MAClBF,OAAO,EAAE,0BAA0B;MACnCI,UAAU,EAAE,IAAI;MAChBF,KAAK,EAAE;IACT,CAAC;IACDY,OAAO,EACL,sIAAsI;IACxIC,QAAQ,EAAE,cAAc;IACxB,gBAAgB,EAAE,cAAc;IAChCC,MAAM,EAAE,cAAc;IACtBC,WAAW,EAAE,gBAAgB;IAC7BC,QAAQ,EAAE;EACZ,CAAC;EACDtB,KAAK,CAACuB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,SAASC,oBAAoBA,CAACC,GAAG,EAAE;IACnE,IAAIA,GAAG,CAACC,QAAQ,KAAK,SAAS,EAAE;MAC9B;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAIC,WAAW,GAAGF,GAAG,CAACG,MAAM,CAACC,MAAM,CAAC,UAAUC,KAAK,EAAE;MACnD,OACE,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,CAACC,IAAI,KAAK,SAAS,IACxBD,KAAK,CAACC,IAAI,KAAK,QAAQ;IAE3B,CAAC,CAAC;IACF,IAAIC,YAAY,GAAG,CAAC;IACpB;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,QAAQA,CAACC,MAAM,EAAE;MACxB,OAAOP,WAAW,CAACK,YAAY,GAAGE,MAAM,CAAC;IAC3C;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,WAAWA,CAACC,KAAK,EAAEF,MAAM,EAAE;MAClCA,MAAM,GAAGA,MAAM,IAAI,CAAC;MACpB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,IAAIP,KAAK,GAAGG,QAAQ,CAACI,CAAC,GAAGH,MAAM,CAAC;QAChC,IAAI,CAACJ,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAKK,KAAK,CAACC,CAAC,CAAC,EAAE;UACrC,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASE,kBAAkBA,CAACC,IAAI,EAAEC,KAAK,EAAE;MACvC,IAAIC,WAAW,GAAG,CAAC;MACnB,KAAK,IAAIL,CAAC,GAAGL,YAAY,EAAEK,CAAC,GAAGV,WAAW,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;QACtD,IAAIP,KAAK,GAAGH,WAAW,CAACU,CAAC,CAAC;QAC1B,IAAIM,OAAO,GAAGb,KAAK,CAACa,OAAO;QAC3B,IAAIb,KAAK,CAACC,IAAI,KAAK,aAAa,IAAI,OAAOY,OAAO,KAAK,QAAQ,EAAE;UAC/D,IAAIH,IAAI,CAACI,IAAI,CAACD,OAAO,CAAC,EAAE;YACtBD,WAAW,EAAE;UACf,CAAC,MAAM,IAAID,KAAK,CAACG,IAAI,CAACD,OAAO,CAAC,EAAE;YAC9BD,WAAW,EAAE;YACb,IAAIA,WAAW,KAAK,CAAC,EAAE;cACrB,OAAOL,CAAC;YACV;UACF;QACF;MACF;MACA,OAAO,CAAC,CAAC;IACX;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASQ,QAAQA,CAACf,KAAK,EAAEzB,KAAK,EAAE;MAC9B,IAAIP,OAAO,GAAGgC,KAAK,CAACzB,KAAK;MACzB,IAAI,CAACP,OAAO,EAAE;QACZgC,KAAK,CAACzB,KAAK,GAAGP,OAAO,GAAG,EAAE;MAC5B,CAAC,MAAM,IAAI,CAACgD,KAAK,CAACC,OAAO,CAACjD,OAAO,CAAC,EAAE;QAClCgC,KAAK,CAACzB,KAAK,GAAGP,OAAO,GAAG,CAACA,OAAO,CAAC;MACnC;MACAA,OAAO,CAACkD,IAAI,CAAC3C,KAAK,CAAC;IACrB;IACA,OAAO2B,YAAY,GAAGL,WAAW,CAACW,MAAM,GAAI;MAC1C,IAAIW,UAAU,GAAGtB,WAAW,CAACK,YAAY,EAAE,CAAC,EAAC;MAC7C,IAAIiB,UAAU,CAAClB,IAAI,KAAK,SAAS,IAAIkB,UAAU,CAACN,OAAO,KAAK,UAAU,EAAE;QACtE;QACA,IAAIO,cAAc,GAAG,EAAE;QACvB,IACEf,WAAW,CAAC,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC,IACnDF,QAAQ,CAAC,CAAC,CAAC,CAACU,OAAO,KAAK,GAAG,EAC3B;UACA;UACAX,YAAY,IAAI,CAAC,EAAC;UAClB,IAAImB,aAAa,GAAGZ,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;UACtD,IAAIY,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB;UACF,CAAC,CAAC;UACF,OAAOnB,YAAY,GAAGmB,aAAa,EAAEnB,YAAY,EAAE,EAAE;YACnD,IAAIoB,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;YACnB,IAAImB,CAAC,CAACrB,IAAI,KAAK,UAAU,EAAE;cACzBc,QAAQ,CAACO,CAAC,EAAE,gBAAgB,CAAC;cAC7BF,cAAc,CAACF,IAAI,CAACI,CAAC,CAACT,OAAO,CAAC;YAChC;UACF;UACAX,YAAY,GAAGmB,aAAa,GAAG,CAAC;QAClC;QACA,IACEhB,WAAW,CAAC,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC,IAC9CF,QAAQ,CAAC,CAAC,CAAC,CAACU,OAAO,KAAK,GAAG,EAC3B;UACAX,YAAY,EAAE,EAAC;UACfa,QAAQ,CAACZ,QAAQ,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC;UAC1C,IAAIiB,cAAc,CAACZ,MAAM,GAAG,CAAC,EAAE;YAC7B,IAAIe,WAAW,GAAGd,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;YACpD,IAAIc,WAAW,KAAK,CAAC,CAAC,EAAE;cACtB;YACF,CAAC,CAAC;YACF,KAAK,IAAIhB,CAAC,GAAGL,YAAY,EAAEK,CAAC,GAAGgB,WAAW,EAAEhB,CAAC,EAAE,EAAE;cAC/C,IAAIiB,QAAQ,GAAG3B,WAAW,CAACU,CAAC,CAAC;cAC7B,IACEiB,QAAQ,CAACvB,IAAI,KAAK,UAAU,IAC5BmB,cAAc,CAACK,OAAO,CAACD,QAAQ,CAACX,OAAO,CAAC,IAAI,CAAC,EAC7C;gBACAE,QAAQ,CAACS,QAAQ,EAAE,gBAAgB,CAAC;cACtC;YACF;UACF;QACF;MACF;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}