{"ast": null, "code": "/*\nLanguage: JSON\nDescription: JSON (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols\n*/\n\nfunction json(hljs) {\n  const LITERALS = {\n    literal: 'true false null'\n  };\n  const ALLOWED_COMMENTS = [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE];\n  const TYPES = [hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE];\n  const VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    contains: TYPES,\n    keywords: LITERALS\n  };\n  const OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [{\n      className: 'attr',\n      begin: /\"/,\n      end: /\"/,\n      contains: [hljs.BACKSLASH_ESCAPE],\n      illegal: '\\\\n'\n    }, hljs.inherit(VALUE_CONTAINER, {\n      begin: /:/\n    })].concat(ALLOWED_COMMENTS),\n    illegal: '\\\\S'\n  };\n  const ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [hljs.inherit(VALUE_CONTAINER)],\n    // inherit is a workaround for a bug that makes shared modes with endsWithParent compile only the ending of one of the parents\n    illegal: '\\\\S'\n  };\n  TYPES.push(OBJECT, ARRAY);\n  ALLOWED_COMMENTS.forEach(function (rule) {\n    TYPES.push(rule);\n  });\n  return {\n    name: 'JSON',\n    contains: TYPES,\n    keywords: LITERALS,\n    illegal: '\\\\S'\n  };\n}\nmodule.exports = json;", "map": {"version": 3, "names": ["json", "hljs", "LITERALS", "literal", "ALLOWED_COMMENTS", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "TYPES", "QUOTE_STRING_MODE", "C_NUMBER_MODE", "VALUE_CONTAINER", "end", "endsWithParent", "excludeEnd", "contains", "keywords", "OBJECT", "begin", "className", "BACKSLASH_ESCAPE", "illegal", "inherit", "concat", "ARRAY", "push", "for<PERSON>ach", "rule", "name", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/json.js"], "sourcesContent": ["/*\nLanguage: JSON\nDescription: JSON (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols\n*/\n\nfunction json(hljs) {\n  const LITERALS = {\n    literal: 'true false null'\n  };\n  const ALLOWED_COMMENTS = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  const TYPES = [\n    hljs.QUOTE_STRING_MODE,\n    hljs.C_NUMBER_MODE\n  ];\n  const VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    contains: TYPES,\n    keywords: LITERALS\n  };\n  const OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [\n      {\n        className: 'attr',\n        begin: /\"/,\n        end: /\"/,\n        contains: [hljs.BACKSLASH_ESCAPE],\n        illegal: '\\\\n'\n      },\n      hljs.inherit(VALUE_CONTAINER, {\n        begin: /:/\n      })\n    ].concat(ALLOWED_COMMENTS),\n    illegal: '\\\\S'\n  };\n  const ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [hljs.inherit(VALUE_CONTAINER)], // inherit is a workaround for a bug that makes shared modes with endsWithParent compile only the ending of one of the parents\n    illegal: '\\\\S'\n  };\n  TYPES.push(OBJECT, ARRAY);\n  ALLOWED_COMMENTS.forEach(function(rule) {\n    TYPES.push(rule);\n  });\n  return {\n    name: 'JSON',\n    contains: TYPES,\n    keywords: LITERALS,\n    illegal: '\\\\S'\n  };\n}\n\nmodule.exports = json;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE;EACX,CAAC;EACD,MAAMC,gBAAgB,GAAG,CACvBH,IAAI,CAACI,mBAAmB,EACxBJ,IAAI,CAACK,oBAAoB,CAC1B;EACD,MAAMC,KAAK,GAAG,CACZN,IAAI,CAACO,iBAAiB,EACtBP,IAAI,CAACQ,aAAa,CACnB;EACD,MAAMC,eAAe,GAAG;IACtBC,GAAG,EAAE,GAAG;IACRC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAEP,KAAK;IACfQ,QAAQ,EAAEb;EACZ,CAAC;EACD,MAAMc,MAAM,GAAG;IACbC,KAAK,EAAE,IAAI;IACXN,GAAG,EAAE,IAAI;IACTG,QAAQ,EAAE,CACR;MACEI,SAAS,EAAE,MAAM;MACjBD,KAAK,EAAE,GAAG;MACVN,GAAG,EAAE,GAAG;MACRG,QAAQ,EAAE,CAACb,IAAI,CAACkB,gBAAgB,CAAC;MACjCC,OAAO,EAAE;IACX,CAAC,EACDnB,IAAI,CAACoB,OAAO,CAACX,eAAe,EAAE;MAC5BO,KAAK,EAAE;IACT,CAAC,CAAC,CACH,CAACK,MAAM,CAAClB,gBAAgB,CAAC;IAC1BgB,OAAO,EAAE;EACX,CAAC;EACD,MAAMG,KAAK,GAAG;IACZN,KAAK,EAAE,KAAK;IACZN,GAAG,EAAE,KAAK;IACVG,QAAQ,EAAE,CAACb,IAAI,CAACoB,OAAO,CAACX,eAAe,CAAC,CAAC;IAAE;IAC3CU,OAAO,EAAE;EACX,CAAC;EACDb,KAAK,CAACiB,IAAI,CAACR,MAAM,EAAEO,KAAK,CAAC;EACzBnB,gBAAgB,CAACqB,OAAO,CAAC,UAASC,IAAI,EAAE;IACtCnB,KAAK,CAACiB,IAAI,CAACE,IAAI,CAAC;EAClB,CAAC,CAAC;EACF,OAAO;IACLC,IAAI,EAAE,MAAM;IACZb,QAAQ,EAAEP,KAAK;IACfQ,QAAQ,EAAEb,QAAQ;IAClBkB,OAAO,EAAE;EACX,CAAC;AACH;AAEAQ,MAAM,CAACC,OAAO,GAAG7B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}