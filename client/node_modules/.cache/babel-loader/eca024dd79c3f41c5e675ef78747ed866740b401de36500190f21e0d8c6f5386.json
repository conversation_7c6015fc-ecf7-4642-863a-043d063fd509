{"ast": null, "code": "export default {\n  \"code[class*='language-']\": {\n    \"color\": \"#9efeff\",\n    \"direction\": \"ltr\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"fontFamily\": \"'Operator Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontWeight\": \"400\",\n    \"fontSize\": \"17px\",\n    \"lineHeight\": \"25px\",\n    \"letterSpacing\": \"0.5px\",\n    \"textShadow\": \"0 1px #222245\"\n  },\n  \"pre[class*='language-']\": {\n    \"color\": \"#9efeff\",\n    \"direction\": \"ltr\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"fontFamily\": \"'Operator Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontWeight\": \"400\",\n    \"fontSize\": \"17px\",\n    \"lineHeight\": \"25px\",\n    \"letterSpacing\": \"0.5px\",\n    \"textShadow\": \"0 1px #222245\",\n    \"padding\": \"2em\",\n    \"margin\": \"0.5em 0\",\n    \"overflow\": \"auto\",\n    \"background\": \"#1e1e3f\"\n  },\n  \"pre[class*='language-']::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"pre[class*='language-'] ::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-']::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-'] ::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"pre[class*='language-']::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"pre[class*='language-'] ::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-']::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-'] ::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \":not(pre) > code[class*='language-']\": {\n    \"background\": \"#1e1e3f\",\n    \"padding\": \"0.1em\",\n    \"borderRadius\": \"0.3em\"\n  },\n  \"\": {\n    \"fontWeight\": \"400\"\n  },\n  \"comment\": {\n    \"color\": \"#b362ff\"\n  },\n  \"prolog\": {\n    \"color\": \"#b362ff\"\n  },\n  \"cdata\": {\n    \"color\": \"#b362ff\"\n  },\n  \"delimiter\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"keyword\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"selector\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"important\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"atrule\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"operator\": {\n    \"color\": \"rgb(255, 180, 84)\",\n    \"background\": \"none\"\n  },\n  \"attr-name\": {\n    \"color\": \"rgb(255, 180, 84)\"\n  },\n  \"punctuation\": {\n    \"color\": \"#ffffff\"\n  },\n  \"boolean\": {\n    \"color\": \"rgb(255, 98, 140)\"\n  },\n  \"tag\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"tag.punctuation\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"doctype\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"builtin\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"entity\": {\n    \"color\": \"#6897bb\",\n    \"background\": \"none\"\n  },\n  \"symbol\": {\n    \"color\": \"#6897bb\"\n  },\n  \"number\": {\n    \"color\": \"#ff628c\"\n  },\n  \"property\": {\n    \"color\": \"#ff628c\"\n  },\n  \"constant\": {\n    \"color\": \"#ff628c\"\n  },\n  \"variable\": {\n    \"color\": \"#ff628c\"\n  },\n  \"string\": {\n    \"color\": \"#a5ff90\"\n  },\n  \"char\": {\n    \"color\": \"#a5ff90\"\n  },\n  \"attr-value\": {\n    \"color\": \"#a5c261\"\n  },\n  \"attr-value.punctuation\": {\n    \"color\": \"#a5c261\"\n  },\n  \"attr-value.punctuation:first-child\": {\n    \"color\": \"#a9b7c6\"\n  },\n  \"url\": {\n    \"color\": \"#287bde\",\n    \"textDecoration\": \"underline\",\n    \"background\": \"none\"\n  },\n  \"function\": {\n    \"color\": \"rgb(250, 208, 0)\"\n  },\n  \"regex\": {\n    \"background\": \"#364135\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  },\n  \"inserted\": {\n    \"background\": \"#00ff00\"\n  },\n  \"deleted\": {\n    \"background\": \"#ff000d\"\n  },\n  \"code.language-css .token.property\": {\n    \"color\": \"#a9b7c6\"\n  },\n  \"code.language-css .token.property + .token.punctuation\": {\n    \"color\": \"#a9b7c6\"\n  },\n  \"code.language-css .token.id\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.class\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.attribute\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.pseudo-class\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.pseudo-element\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"class-name\": {\n    \"color\": \"#fb94ff\"\n  },\n  \".language-css .token.string\": {\n    \"background\": \"none\"\n  },\n  \".style .token.string\": {\n    \"background\": \"none\"\n  },\n  \".line-highlight.line-highlight\": {\n    \"marginTop\": \"36px\",\n    \"background\": \"linear-gradient(to right, rgba(179, 98, 255, 0.17), transparent)\"\n  },\n  \".line-highlight.line-highlight:before\": {\n    \"content\": \"''\"\n  },\n  \".line-highlight.line-highlight[data-end]:after\": {\n    \"content\": \"''\"\n  }\n};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/styles/prism/shades-of-purple.js"], "sourcesContent": ["export default {\n  \"code[class*='language-']\": {\n    \"color\": \"#9efeff\",\n    \"direction\": \"ltr\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"fontFamily\": \"'Operator Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontWeight\": \"400\",\n    \"fontSize\": \"17px\",\n    \"lineHeight\": \"25px\",\n    \"letterSpacing\": \"0.5px\",\n    \"textShadow\": \"0 1px #222245\"\n  },\n  \"pre[class*='language-']\": {\n    \"color\": \"#9efeff\",\n    \"direction\": \"ltr\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"fontFamily\": \"'Operator Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontWeight\": \"400\",\n    \"fontSize\": \"17px\",\n    \"lineHeight\": \"25px\",\n    \"letterSpacing\": \"0.5px\",\n    \"textShadow\": \"0 1px #222245\",\n    \"padding\": \"2em\",\n    \"margin\": \"0.5em 0\",\n    \"overflow\": \"auto\",\n    \"background\": \"#1e1e3f\"\n  },\n  \"pre[class*='language-']::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"pre[class*='language-'] ::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-']::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-'] ::-moz-selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"pre[class*='language-']::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"pre[class*='language-'] ::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-']::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \"code[class*='language-'] ::selection\": {\n    \"color\": \"inherit\",\n    \"background\": \"#a599e9\"\n  },\n  \":not(pre) > code[class*='language-']\": {\n    \"background\": \"#1e1e3f\",\n    \"padding\": \"0.1em\",\n    \"borderRadius\": \"0.3em\"\n  },\n  \"\": {\n    \"fontWeight\": \"400\"\n  },\n  \"comment\": {\n    \"color\": \"#b362ff\"\n  },\n  \"prolog\": {\n    \"color\": \"#b362ff\"\n  },\n  \"cdata\": {\n    \"color\": \"#b362ff\"\n  },\n  \"delimiter\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"keyword\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"selector\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"important\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"atrule\": {\n    \"color\": \"#ff9d00\"\n  },\n  \"operator\": {\n    \"color\": \"rgb(255, 180, 84)\",\n    \"background\": \"none\"\n  },\n  \"attr-name\": {\n    \"color\": \"rgb(255, 180, 84)\"\n  },\n  \"punctuation\": {\n    \"color\": \"#ffffff\"\n  },\n  \"boolean\": {\n    \"color\": \"rgb(255, 98, 140)\"\n  },\n  \"tag\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"tag.punctuation\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"doctype\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"builtin\": {\n    \"color\": \"rgb(255, 157, 0)\"\n  },\n  \"entity\": {\n    \"color\": \"#6897bb\",\n    \"background\": \"none\"\n  },\n  \"symbol\": {\n    \"color\": \"#6897bb\"\n  },\n  \"number\": {\n    \"color\": \"#ff628c\"\n  },\n  \"property\": {\n    \"color\": \"#ff628c\"\n  },\n  \"constant\": {\n    \"color\": \"#ff628c\"\n  },\n  \"variable\": {\n    \"color\": \"#ff628c\"\n  },\n  \"string\": {\n    \"color\": \"#a5ff90\"\n  },\n  \"char\": {\n    \"color\": \"#a5ff90\"\n  },\n  \"attr-value\": {\n    \"color\": \"#a5c261\"\n  },\n  \"attr-value.punctuation\": {\n    \"color\": \"#a5c261\"\n  },\n  \"attr-value.punctuation:first-child\": {\n    \"color\": \"#a9b7c6\"\n  },\n  \"url\": {\n    \"color\": \"#287bde\",\n    \"textDecoration\": \"underline\",\n    \"background\": \"none\"\n  },\n  \"function\": {\n    \"color\": \"rgb(250, 208, 0)\"\n  },\n  \"regex\": {\n    \"background\": \"#364135\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  },\n  \"inserted\": {\n    \"background\": \"#00ff00\"\n  },\n  \"deleted\": {\n    \"background\": \"#ff000d\"\n  },\n  \"code.language-css .token.property\": {\n    \"color\": \"#a9b7c6\"\n  },\n  \"code.language-css .token.property + .token.punctuation\": {\n    \"color\": \"#a9b7c6\"\n  },\n  \"code.language-css .token.id\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.class\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.attribute\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.pseudo-class\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"code.language-css .token.selector > .token.pseudo-element\": {\n    \"color\": \"#ffc66d\"\n  },\n  \"class-name\": {\n    \"color\": \"#fb94ff\"\n  },\n  \".language-css .token.string\": {\n    \"background\": \"none\"\n  },\n  \".style .token.string\": {\n    \"background\": \"none\"\n  },\n  \".line-highlight.line-highlight\": {\n    \"marginTop\": \"36px\",\n    \"background\": \"linear-gradient(to right, rgba(179, 98, 255, 0.17), transparent)\"\n  },\n  \".line-highlight.line-highlight:before\": {\n    \"content\": \"''\"\n  },\n  \".line-highlight.line-highlight[data-end]:after\": {\n    \"content\": \"''\"\n  }\n};"], "mappings": "AAAA,eAAe;EACb,0BAA0B,EAAE;IAC1B,OAAO,EAAE,SAAS;IAClB,WAAW,EAAE,KAAK;IAClB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,YAAY,EAAE,yFAAyF;IACvG,YAAY,EAAE,KAAK;IACnB,UAAU,EAAE,MAAM;IAClB,YAAY,EAAE,MAAM;IACpB,eAAe,EAAE,OAAO;IACxB,YAAY,EAAE;EAChB,CAAC;EACD,yBAAyB,EAAE;IACzB,OAAO,EAAE,SAAS;IAClB,WAAW,EAAE,KAAK;IAClB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,YAAY,EAAE,yFAAyF;IACvG,YAAY,EAAE,KAAK;IACnB,UAAU,EAAE,MAAM;IAClB,YAAY,EAAE,MAAM;IACpB,eAAe,EAAE,OAAO;IACxB,YAAY,EAAE,eAAe;IAC7B,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,SAAS;IACnB,UAAU,EAAE,MAAM;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,yCAAyC,EAAE;IACzC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,0CAA0C,EAAE;IAC1C,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,0CAA0C,EAAE;IAC1C,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,2CAA2C,EAAE;IAC3C,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,oCAAoC,EAAE;IACpC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,qCAAqC,EAAE;IACrC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,qCAAqC,EAAE;IACrC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,sCAAsC,EAAE;IACtC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,sCAAsC,EAAE;IACtC,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,OAAO;IAClB,cAAc,EAAE;EAClB,CAAC;EACD,EAAE,EAAE;IACF,YAAY,EAAE;EAChB,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE,mBAAmB;IAC5B,YAAY,EAAE;EAChB,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,iBAAiB,EAAE;IACjB,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACN,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,wBAAwB,EAAE;IACxB,OAAO,EAAE;EACX,CAAC;EACD,oCAAoC,EAAE;IACpC,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE,SAAS;IAClB,gBAAgB,EAAE,WAAW;IAC7B,YAAY,EAAE;EAChB,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACN,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,WAAW,EAAE;EACf,CAAC;EACD,UAAU,EAAE;IACV,YAAY,EAAE;EAChB,CAAC;EACD,SAAS,EAAE;IACT,YAAY,EAAE;EAChB,CAAC;EACD,mCAAmC,EAAE;IACnC,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,6BAA6B,EAAE;IAC7B,OAAO,EAAE;EACX,CAAC;EACD,kDAAkD,EAAE;IAClD,OAAO,EAAE;EACX,CAAC;EACD,sDAAsD,EAAE;IACtD,OAAO,EAAE;EACX,CAAC;EACD,yDAAyD,EAAE;IACzD,OAAO,EAAE;EACX,CAAC;EACD,2DAA2D,EAAE;IAC3D,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,6BAA6B,EAAE;IAC7B,YAAY,EAAE;EAChB,CAAC;EACD,sBAAsB,EAAE;IACtB,YAAY,EAAE;EAChB,CAAC;EACD,gCAAgC,EAAE;IAChC,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE;EAChB,CAAC;EACD,uCAAuC,EAAE;IACvC,SAAS,EAAE;EACb,CAAC;EACD,gDAAgD,EAAE;IAChD,SAAS,EAAE;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}