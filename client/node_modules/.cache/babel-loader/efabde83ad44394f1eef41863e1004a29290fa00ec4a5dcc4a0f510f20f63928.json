{"ast": null, "code": "'use strict';\n\nmodule.exports = java;\njava.displayName = 'java';\njava.aliases = [];\nfunction java(Prism) {\n  ;\n  (function (Prism) {\n    var keywords = /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/; // full package (optional) + parent classes (optional)\n    var classNamePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/.source; // based on the java naming conventions\n    var className = {\n      pattern: RegExp(classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /\\./\n      }\n    };\n    Prism.languages.java = Prism.languages.extend('clike', {\n      string: {\n        pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        lookbehind: true,\n        greedy: true\n      },\n      'class-name': [className, {\n        // variables and parameters\n        // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n        pattern: RegExp(classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source),\n        lookbehind: true,\n        inside: className.inside\n      }],\n      keyword: keywords,\n      function: [Prism.languages.clike.function, {\n        pattern: /(::\\s*)[a-z_]\\w*/,\n        lookbehind: true\n      }],\n      number: /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n      operator: {\n        pattern: /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n        lookbehind: true\n      }\n    });\n    Prism.languages.insertBefore('java', 'string', {\n      'triple-quoted-string': {\n        // http://openjdk.java.net/jeps/355#Description\n        pattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n        greedy: true,\n        alias: 'string'\n      },\n      char: {\n        pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n        greedy: true\n      }\n    });\n    Prism.languages.insertBefore('java', 'class-name', {\n      annotation: {\n        pattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      generics: {\n        pattern: /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      },\n      namespace: {\n        pattern: RegExp(/(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/.source.replace(/<keyword>/g, function () {\n          return keywords.source;\n        })),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "java", "displayName", "aliases", "Prism", "keywords", "classNamePrefix", "source", "className", "pattern", "RegExp", "lookbehind", "inside", "namespace", "punctuation", "languages", "extend", "string", "greedy", "keyword", "function", "clike", "number", "operator", "insertBefore", "alias", "char", "annotation", "generics", "replace"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/java.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = java\njava.displayName = 'java'\njava.aliases = []\nfunction java(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/ // full package (optional) + parent classes (optional)\n    var classNamePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/\n      .source // based on the java naming conventions\n    var className = {\n      pattern: RegExp(classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /\\./\n      }\n    }\n    Prism.languages.java = Prism.languages.extend('clike', {\n      string: {\n        pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        lookbehind: true,\n        greedy: true\n      },\n      'class-name': [\n        className,\n        {\n          // variables and parameters\n          // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n          pattern: RegExp(\n            classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source\n          ),\n          lookbehind: true,\n          inside: className.inside\n        }\n      ],\n      keyword: keywords,\n      function: [\n        Prism.languages.clike.function,\n        {\n          pattern: /(::\\s*)[a-z_]\\w*/,\n          lookbehind: true\n        }\n      ],\n      number:\n        /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n      operator: {\n        pattern:\n          /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n        lookbehind: true\n      }\n    })\n    Prism.languages.insertBefore('java', 'string', {\n      'triple-quoted-string': {\n        // http://openjdk.java.net/jeps/355#Description\n        pattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n        greedy: true,\n        alias: 'string'\n      },\n      char: {\n        pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('java', 'class-name', {\n      annotation: {\n        pattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      generics: {\n        pattern:\n          /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      },\n      namespace: {\n        pattern: RegExp(\n          /(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/.source.replace(\n            /<keyword>/g,\n            function () {\n              return keywords.source\n            }\n          )\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GACV,gdAAgd,EAAC;IACnd,IAAIC,eAAe,GAAG,sDAAsD,CACzEC,MAAM,EAAC;IACV,IAAIC,SAAS,GAAG;MACdC,OAAO,EAAEC,MAAM,CAACJ,eAAe,GAAG,+BAA+B,CAACC,MAAM,CAAC;MACzEI,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,SAAS,EAAE;UACTJ,OAAO,EAAE,0CAA0C;UACnDG,MAAM,EAAE;YACNE,WAAW,EAAE;UACf;QACF,CAAC;QACDA,WAAW,EAAE;MACf;IACF,CAAC;IACDV,KAAK,CAACW,SAAS,CAACd,IAAI,GAAGG,KAAK,CAACW,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MACrDC,MAAM,EAAE;QACNR,OAAO,EAAE,gCAAgC;QACzCE,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAE;MACV,CAAC;MACD,YAAY,EAAE,CACZV,SAAS,EACT;QACE;QACA;QACAC,OAAO,EAAEC,MAAM,CACbJ,eAAe,GAAG,8BAA8B,CAACC,MACnD,CAAC;QACDI,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEJ,SAAS,CAACI;MACpB,CAAC,CACF;MACDO,OAAO,EAAEd,QAAQ;MACjBe,QAAQ,EAAE,CACRhB,KAAK,CAACW,SAAS,CAACM,KAAK,CAACD,QAAQ,EAC9B;QACEX,OAAO,EAAE,kBAAkB;QAC3BE,UAAU,EAAE;MACd,CAAC,CACF;MACDW,MAAM,EACJ,6IAA6I;MAC/IC,QAAQ,EAAE;QACRd,OAAO,EACL,uEAAuE;QACzEE,UAAU,EAAE;MACd;IACF,CAAC,CAAC;IACFP,KAAK,CAACW,SAAS,CAACS,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE;MAC7C,sBAAsB,EAAE;QACtB;QACAf,OAAO,EAAE,gDAAgD;QACzDS,MAAM,EAAE,IAAI;QACZO,KAAK,EAAE;MACT,CAAC;MACDC,IAAI,EAAE;QACJjB,OAAO,EAAE,2BAA2B;QACpCS,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IACFd,KAAK,CAACW,SAAS,CAACS,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE;MACjDG,UAAU,EAAE;QACVlB,OAAO,EAAE,8BAA8B;QACvCE,UAAU,EAAE,IAAI;QAChBc,KAAK,EAAE;MACT,CAAC;MACDG,QAAQ,EAAE;QACRnB,OAAO,EACL,iGAAiG;QACnGG,MAAM,EAAE;UACN,YAAY,EAAEJ,SAAS;UACvBW,OAAO,EAAEd,QAAQ;UACjBS,WAAW,EAAE,WAAW;UACxBS,QAAQ,EAAE;QACZ;MACF,CAAC;MACDV,SAAS,EAAE;QACTJ,OAAO,EAAEC,MAAM,CACb,oJAAoJ,CAACH,MAAM,CAACsB,OAAO,CACjK,YAAY,EACZ,YAAY;UACV,OAAOxB,QAAQ,CAACE,MAAM;QACxB,CACF,CACF,CAAC;QACDI,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNE,WAAW,EAAE;QACf;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAEV,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}