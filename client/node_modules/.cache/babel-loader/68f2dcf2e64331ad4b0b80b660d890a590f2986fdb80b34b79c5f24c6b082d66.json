{"ast": null, "code": "'use strict';\n\nmodule.exports = agda;\nagda.displayName = 'agda';\nagda.aliases = [];\nfunction agda(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.agda = {\n      comment: /\\{-[\\s\\S]*?(?:-\\}|$)|--.*/,\n      string: {\n        pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,\n        greedy: true\n      },\n      punctuation: /[(){}⦃⦄.;@]/,\n      'class-name': {\n        pattern: /((?:data|record) +)\\S+/,\n        lookbehind: true\n      },\n      function: {\n        pattern: /(^[ \\t]*)(?!\\s)[^:\\r\\n]+(?=:)/m,\n        lookbehind: true\n      },\n      operator: {\n        pattern: /(^\\s*|\\s)(?:[=|:∀→λ\\\\?_]|->)(?=\\s)/,\n        lookbehind: true\n      },\n      keyword: /\\b(?:Set|abstract|constructor|data|eta-equality|field|forall|hiding|import|in|inductive|infix|infixl|infixr|instance|let|macro|module|mutual|no-eta-equality|open|overlap|pattern|postulate|primitive|private|public|quote|quoteContext|quoteGoal|quoteTerm|record|renaming|rewrite|syntax|tactic|unquote|unquoteDecl|unquoteDef|using|variable|where|with)\\b/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "agda", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "punctuation", "lookbehind", "function", "operator", "keyword"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/agda.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = agda\nagda.displayName = 'agda'\nagda.aliases = []\nfunction agda(Prism) {\n  ;(function (Prism) {\n    Prism.languages.agda = {\n      comment: /\\{-[\\s\\S]*?(?:-\\}|$)|--.*/,\n      string: {\n        pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,\n        greedy: true\n      },\n      punctuation: /[(){}⦃⦄.;@]/,\n      'class-name': {\n        pattern: /((?:data|record) +)\\S+/,\n        lookbehind: true\n      },\n      function: {\n        pattern: /(^[ \\t]*)(?!\\s)[^:\\r\\n]+(?=:)/m,\n        lookbehind: true\n      },\n      operator: {\n        pattern: /(^\\s*|\\s)(?:[=|:∀→λ\\\\?_]|->)(?=\\s)/,\n        lookbehind: true\n      },\n      keyword:\n        /\\b(?:Set|abstract|constructor|data|eta-equality|field|forall|hiding|import|in|inductive|infix|infixl|infixr|instance|let|macro|module|mutual|no-eta-equality|open|overlap|pattern|postulate|primitive|private|public|quote|quoteContext|quoteGoal|quoteTerm|record|renaming|rewrite|syntax|tactic|unquote|unquoteDecl|unquoteDef|using|variable|where|with)\\b/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;MACrBK,OAAO,EAAE,2BAA2B;MACpCC,MAAM,EAAE;QACNC,OAAO,EAAE,qCAAqC;QAC9CC,MAAM,EAAE;MACV,CAAC;MACDC,WAAW,EAAE,aAAa;MAC1B,YAAY,EAAE;QACZF,OAAO,EAAE,wBAAwB;QACjCG,UAAU,EAAE;MACd,CAAC;MACDC,QAAQ,EAAE;QACRJ,OAAO,EAAE,gCAAgC;QACzCG,UAAU,EAAE;MACd,CAAC;MACDE,QAAQ,EAAE;QACRL,OAAO,EAAE,oCAAoC;QAC7CG,UAAU,EAAE;MACd,CAAC;MACDG,OAAO,EACL;IACJ,CAAC;EACH,CAAC,EAAEV,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}