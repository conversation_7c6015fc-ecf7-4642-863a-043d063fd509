{"ast": null, "code": "const MODES = hljs => {\n  return {\n    IMPORTANT: {\n      className: 'meta',\n      begin: '!important'\n    },\n    HEXCOLOR: {\n      className: 'number',\n      begin: '#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})'\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      className: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n    }\n  };\n};\nconst TAGS = ['a', 'abbr', 'address', 'article', 'aside', 'audio', 'b', 'blockquote', 'body', 'button', 'canvas', 'caption', 'cite', 'code', 'dd', 'del', 'details', 'dfn', 'div', 'dl', 'dt', 'em', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hgroup', 'html', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'mark', 'menu', 'nav', 'object', 'ol', 'p', 'q', 'quote', 'samp', 'section', 'span', 'strong', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'ul', 'var', 'video'];\nconst MEDIA_FEATURES = ['any-hover', 'any-pointer', 'aspect-ratio', 'color', 'color-gamut', 'color-index', 'device-aspect-ratio', 'device-height', 'device-width', 'display-mode', 'forced-colors', 'grid', 'height', 'hover', 'inverted-colors', 'monochrome', 'orientation', 'overflow-block', 'overflow-inline', 'pointer', 'prefers-color-scheme', 'prefers-contrast', 'prefers-reduced-motion', 'prefers-reduced-transparency', 'resolution', 'scan', 'scripting', 'update', 'width',\n// TODO: find a better solution?\n'min-width', 'max-width', 'min-height', 'max-height'];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = ['active', 'any-link', 'blank', 'checked', 'current', 'default', 'defined', 'dir',\n// dir()\n'disabled', 'drop', 'empty', 'enabled', 'first', 'first-child', 'first-of-type', 'fullscreen', 'future', 'focus', 'focus-visible', 'focus-within', 'has',\n// has()\n'host',\n// host or host()\n'host-context',\n// host-context()\n'hover', 'indeterminate', 'in-range', 'invalid', 'is',\n// is()\n'lang',\n// lang()\n'last-child', 'last-of-type', 'left', 'link', 'local-link', 'not',\n// not()\n'nth-child',\n// nth-child()\n'nth-col',\n// nth-col()\n'nth-last-child',\n// nth-last-child()\n'nth-last-col',\n// nth-last-col()\n'nth-last-of-type',\n//nth-last-of-type()\n'nth-of-type',\n//nth-of-type()\n'only-child', 'only-of-type', 'optional', 'out-of-range', 'past', 'placeholder-shown', 'read-only', 'read-write', 'required', 'right', 'root', 'scope', 'target', 'target-within', 'user-invalid', 'valid', 'visited', 'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = ['after', 'backdrop', 'before', 'cue', 'cue-region', 'first-letter', 'first-line', 'grammar-error', 'marker', 'part', 'placeholder', 'selection', 'slotted', 'spelling-error'];\nconst ATTRIBUTES = ['align-content', 'align-items', 'align-self', 'animation', 'animation-delay', 'animation-direction', 'animation-duration', 'animation-fill-mode', 'animation-iteration-count', 'animation-name', 'animation-play-state', 'animation-timing-function', 'auto', 'backface-visibility', 'background', 'background-attachment', 'background-clip', 'background-color', 'background-image', 'background-origin', 'background-position', 'background-repeat', 'background-size', 'border', 'border-bottom', 'border-bottom-color', 'border-bottom-left-radius', 'border-bottom-right-radius', 'border-bottom-style', 'border-bottom-width', 'border-collapse', 'border-color', 'border-image', 'border-image-outset', 'border-image-repeat', 'border-image-slice', 'border-image-source', 'border-image-width', 'border-left', 'border-left-color', 'border-left-style', 'border-left-width', 'border-radius', 'border-right', 'border-right-color', 'border-right-style', 'border-right-width', 'border-spacing', 'border-style', 'border-top', 'border-top-color', 'border-top-left-radius', 'border-top-right-radius', 'border-top-style', 'border-top-width', 'border-width', 'bottom', 'box-decoration-break', 'box-shadow', 'box-sizing', 'break-after', 'break-before', 'break-inside', 'caption-side', 'clear', 'clip', 'clip-path', 'color', 'column-count', 'column-fill', 'column-gap', 'column-rule', 'column-rule-color', 'column-rule-style', 'column-rule-width', 'column-span', 'column-width', 'columns', 'content', 'counter-increment', 'counter-reset', 'cursor', 'direction', 'display', 'empty-cells', 'filter', 'flex', 'flex-basis', 'flex-direction', 'flex-flow', 'flex-grow', 'flex-shrink', 'flex-wrap', 'float', 'font', 'font-display', 'font-family', 'font-feature-settings', 'font-kerning', 'font-language-override', 'font-size', 'font-size-adjust', 'font-smoothing', 'font-stretch', 'font-style', 'font-variant', 'font-variant-ligatures', 'font-variation-settings', 'font-weight', 'height', 'hyphens', 'icon', 'image-orientation', 'image-rendering', 'image-resolution', 'ime-mode', 'inherit', 'initial', 'justify-content', 'left', 'letter-spacing', 'line-height', 'list-style', 'list-style-image', 'list-style-position', 'list-style-type', 'margin', 'margin-bottom', 'margin-left', 'margin-right', 'margin-top', 'marks', 'mask', 'max-height', 'max-width', 'min-height', 'min-width', 'nav-down', 'nav-index', 'nav-left', 'nav-right', 'nav-up', 'none', 'normal', 'object-fit', 'object-position', 'opacity', 'order', 'orphans', 'outline', 'outline-color', 'outline-offset', 'outline-style', 'outline-width', 'overflow', 'overflow-wrap', 'overflow-x', 'overflow-y', 'padding', 'padding-bottom', 'padding-left', 'padding-right', 'padding-top', 'page-break-after', 'page-break-before', 'page-break-inside', 'perspective', 'perspective-origin', 'pointer-events', 'position', 'quotes', 'resize', 'right', 'src',\n// @font-face\n'tab-size', 'table-layout', 'text-align', 'text-align-last', 'text-decoration', 'text-decoration-color', 'text-decoration-line', 'text-decoration-style', 'text-indent', 'text-overflow', 'text-rendering', 'text-shadow', 'text-transform', 'text-underline-position', 'top', 'transform', 'transform-origin', 'transform-style', 'transition', 'transition-delay', 'transition-duration', 'transition-property', 'transition-timing-function', 'unicode-bidi', 'vertical-align', 'visibility', 'white-space', 'widows', 'width', 'word-break', 'word-spacing', 'word-wrap', 'z-index'\n// reverse makes sure longer attributes `font-weight` are matched fully\n// instead of getting false positives on say `font`\n].reverse();\n\n// some grammars use them all as a single group\nconst PSEUDO_SELECTORS = PSEUDO_CLASSES.concat(PSEUDO_ELEMENTS);\n\n/*\nLanguage: Less\nDescription: It's CSS, with just a little more.\nAuthor:   Max Mikhailov <<EMAIL>>\nWebsite: http://lesscss.org\nCategory: common, css\n*/\n\n/** @type LanguageFn */\nfunction less(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_SELECTORS$1 = PSEUDO_SELECTORS;\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[\\\\w-]+'; // yes, Less identifiers may begin with a digit\n  const INTERP_IDENT_RE = '(' + IDENT_RE + '|@\\\\{' + IDENT_RE + '\\\\})';\n\n  /* Generic Modes */\n\n  const RULES = [];\n  const VALUE_MODES = []; // forward def. for recursive modes\n\n  const STRING_MODE = function (c) {\n    return {\n      // Less strings are not multiline (also include '~' for more consistent coloring of \"escaped\" strings)\n      className: 'string',\n      begin: '~?' + c + '.*?' + c\n    };\n  };\n  const IDENT_MODE = function (name, begin, relevance) {\n    return {\n      className: name,\n      begin: begin,\n      relevance: relevance\n    };\n  };\n  const AT_KEYWORDS = {\n    $pattern: /[a-z-]+/,\n    keyword: AT_MODIFIERS,\n    attribute: MEDIA_FEATURES.join(\" \")\n  };\n  const PARENS_MODE = {\n    // used only to properly balance nested parens inside mixin call, def. arg list\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: VALUE_MODES,\n    keywords: AT_KEYWORDS,\n    relevance: 0\n  };\n\n  // generic Less highlighter (used almost everywhere except selectors):\n  VALUE_MODES.push(hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRING_MODE(\"'\"), STRING_MODE('\"'), hljs.CSS_NUMBER_MODE,\n  // fixme: it does not include dot for numbers like .5em :(\n  {\n    begin: '(url|data-uri)\\\\(',\n    starts: {\n      className: 'string',\n      end: '[\\\\)\\\\n]',\n      excludeEnd: true\n    }\n  }, modes.HEXCOLOR, PARENS_MODE, IDENT_MODE('variable', '@@?' + IDENT_RE, 10), IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'), IDENT_MODE('built_in', '~?`[^`]*?`'),\n  // inline javascript (or whatever host language) *multiline* string\n  {\n    // @media features (it’s here to not duplicate things in AT_RULE_MODE with extra PARENS_MODE overriding):\n    className: 'attribute',\n    begin: IDENT_RE + '\\\\s*:',\n    end: ':',\n    returnBegin: true,\n    excludeEnd: true\n  }, modes.IMPORTANT);\n  const VALUE_WITH_RULESETS = VALUE_MODES.concat({\n    begin: /\\{/,\n    end: /\\}/,\n    contains: RULES\n  });\n  const MIXIN_GUARD_MODE = {\n    beginKeywords: 'when',\n    endsWithParent: true,\n    contains: [{\n      beginKeywords: 'and not'\n    }].concat(VALUE_MODES) // using this form to override VALUE’s 'function' match\n  };\n\n  /* Rule-Level Modes */\n\n  const RULE_MODE = {\n    begin: INTERP_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    end: /[;}]/,\n    relevance: 0,\n    contains: [{\n      begin: /-(webkit|moz|ms|o)-/\n    }, {\n      className: 'attribute',\n      begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b',\n      end: /(?=:)/,\n      starts: {\n        endsWithParent: true,\n        illegal: '[<=$]',\n        relevance: 0,\n        contains: VALUE_MODES\n      }\n    }]\n  };\n  const AT_RULE_MODE = {\n    className: 'keyword',\n    begin: '@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\\\b',\n    starts: {\n      end: '[;{}]',\n      keywords: AT_KEYWORDS,\n      returnEnd: true,\n      contains: VALUE_MODES,\n      relevance: 0\n    }\n  };\n\n  // variable definitions and calls\n  const VAR_RULE_MODE = {\n    className: 'variable',\n    variants: [\n    // using more strict pattern for higher relevance to increase chances of Less detection.\n    // this is *the only* Less specific statement used in most of the sources, so...\n    // (we’ll still often loose to the css-parser unless there's '//' comment,\n    // simply because 1 variable just can't beat 99 properties :)\n    {\n      begin: '@' + IDENT_RE + '\\\\s*:',\n      relevance: 15\n    }, {\n      begin: '@' + IDENT_RE\n    }],\n    starts: {\n      end: '[;}]',\n      returnEnd: true,\n      contains: VALUE_WITH_RULESETS\n    }\n  };\n  const SELECTOR_MODE = {\n    // first parse unambiguous selectors (i.e. those not starting with tag)\n    // then fall into the scary lookahead-discriminator variant.\n    // this mode also handles mixin definitions and calls\n    variants: [{\n      begin: '[\\\\.#:&\\\\[>]',\n      end: '[;{}]' // mixin calls end with ';'\n    }, {\n      begin: INTERP_IDENT_RE,\n      end: /\\{/\n    }],\n    returnBegin: true,\n    returnEnd: true,\n    illegal: '[<=\\'$\"]',\n    relevance: 0,\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, MIXIN_GUARD_MODE, IDENT_MODE('keyword', 'all\\\\b'), IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'),\n    // otherwise it’s identified as tag\n    {\n      begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n      className: 'selector-tag'\n    }, IDENT_MODE('selector-tag', INTERP_IDENT_RE + '%?', 0),\n    // '%' for more consistent coloring of @keyframes \"tags\"\n    IDENT_MODE('selector-id', '#' + INTERP_IDENT_RE), IDENT_MODE('selector-class', '\\\\.' + INTERP_IDENT_RE, 0), IDENT_MODE('selector-tag', '&', 0), modes.ATTRIBUTE_SELECTOR_MODE, {\n      className: 'selector-pseudo',\n      begin: ':(' + PSEUDO_CLASSES.join('|') + ')'\n    }, {\n      className: 'selector-pseudo',\n      begin: '::(' + PSEUDO_ELEMENTS.join('|') + ')'\n    }, {\n      begin: '\\\\(',\n      end: '\\\\)',\n      contains: VALUE_WITH_RULESETS\n    },\n    // argument list of parametric mixins\n    {\n      begin: '!important'\n    } // eat !important after mixin call or it will be colored as tag\n    ]\n  };\n  const PSEUDO_SELECTOR_MODE = {\n    begin: IDENT_RE + ':(:)?' + `(${PSEUDO_SELECTORS$1.join('|')})`,\n    returnBegin: true,\n    contains: [SELECTOR_MODE]\n  };\n  RULES.push(hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, AT_RULE_MODE, VAR_RULE_MODE, PSEUDO_SELECTOR_MODE, RULE_MODE, SELECTOR_MODE);\n  return {\n    name: 'Less',\n    case_insensitive: true,\n    illegal: '[=>\\'/<($\"]',\n    contains: RULES\n  };\n}\nmodule.exports = less;", "map": {"version": 3, "names": ["MODES", "hljs", "IMPORTANT", "className", "begin", "HEXCOLOR", "ATTRIBUTE_SELECTOR_MODE", "end", "illegal", "contains", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "TAGS", "MEDIA_FEATURES", "PSEUDO_CLASSES", "PSEUDO_ELEMENTS", "ATTRIBUTES", "reverse", "PSEUDO_SELECTORS", "concat", "less", "modes", "PSEUDO_SELECTORS$1", "AT_MODIFIERS", "IDENT_RE", "INTERP_IDENT_RE", "RULES", "VALUE_MODES", "STRING_MODE", "c", "IDENT_MODE", "name", "relevance", "AT_KEYWORDS", "$pattern", "keyword", "attribute", "join", "PARENS_MODE", "keywords", "push", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "CSS_NUMBER_MODE", "starts", "excludeEnd", "returnBegin", "VALUE_WITH_RULESETS", "MIXIN_GUARD_MODE", "beginKeywords", "endsWithParent", "RULE_MODE", "AT_RULE_MODE", "returnEnd", "VAR_RULE_MODE", "variants", "SELECTOR_MODE", "PSEUDO_SELECTOR_MODE", "case_insensitive", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/less.js"], "sourcesContent": ["const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      className: 'meta',\n      begin: '!important'\n    },\n    HEXCOLOR: {\n      className: 'number',\n      begin: '#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})'\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      className: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'auto',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'border',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'clear',\n  'clip',\n  'clip-path',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'content',\n  'counter-increment',\n  'counter-reset',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-variant-ligatures',\n  'font-variation-settings',\n  'font-weight',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inherit',\n  'initial',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-bottom',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'max-height',\n  'max-width',\n  'min-height',\n  'min-width',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-bottom',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'right',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-last',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-indent',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'white-space',\n  'widows',\n  'width',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n// some grammars use them all as a single group\nconst PSEUDO_SELECTORS = PSEUDO_CLASSES.concat(PSEUDO_ELEMENTS);\n\n/*\nLanguage: Less\nDescription: It's CSS, with just a little more.\nAuthor:   Max Mikhailov <<EMAIL>>\nWebsite: http://lesscss.org\nCategory: common, css\n*/\n\n/** @type LanguageFn */\nfunction less(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_SELECTORS$1 = PSEUDO_SELECTORS;\n\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[\\\\w-]+'; // yes, Less identifiers may begin with a digit\n  const INTERP_IDENT_RE = '(' + IDENT_RE + '|@\\\\{' + IDENT_RE + '\\\\})';\n\n  /* Generic Modes */\n\n  const RULES = []; const VALUE_MODES = []; // forward def. for recursive modes\n\n  const STRING_MODE = function(c) {\n    return {\n    // Less strings are not multiline (also include '~' for more consistent coloring of \"escaped\" strings)\n      className: 'string',\n      begin: '~?' + c + '.*?' + c\n    };\n  };\n\n  const IDENT_MODE = function(name, begin, relevance) {\n    return {\n      className: name,\n      begin: begin,\n      relevance: relevance\n    };\n  };\n\n  const AT_KEYWORDS = {\n    $pattern: /[a-z-]+/,\n    keyword: AT_MODIFIERS,\n    attribute: MEDIA_FEATURES.join(\" \")\n  };\n\n  const PARENS_MODE = {\n    // used only to properly balance nested parens inside mixin call, def. arg list\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: VALUE_MODES,\n    keywords: AT_KEYWORDS,\n    relevance: 0\n  };\n\n  // generic Less highlighter (used almost everywhere except selectors):\n  VALUE_MODES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING_MODE(\"'\"),\n    STRING_MODE('\"'),\n    hljs.CSS_NUMBER_MODE, // fixme: it does not include dot for numbers like .5em :(\n    {\n      begin: '(url|data-uri)\\\\(',\n      starts: {\n        className: 'string',\n        end: '[\\\\)\\\\n]',\n        excludeEnd: true\n      }\n    },\n    modes.HEXCOLOR,\n    PARENS_MODE,\n    IDENT_MODE('variable', '@@?' + IDENT_RE, 10),\n    IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'),\n    IDENT_MODE('built_in', '~?`[^`]*?`'), // inline javascript (or whatever host language) *multiline* string\n    { // @media features (it’s here to not duplicate things in AT_RULE_MODE with extra PARENS_MODE overriding):\n      className: 'attribute',\n      begin: IDENT_RE + '\\\\s*:',\n      end: ':',\n      returnBegin: true,\n      excludeEnd: true\n    },\n    modes.IMPORTANT\n  );\n\n  const VALUE_WITH_RULESETS = VALUE_MODES.concat({\n    begin: /\\{/,\n    end: /\\}/,\n    contains: RULES\n  });\n\n  const MIXIN_GUARD_MODE = {\n    beginKeywords: 'when',\n    endsWithParent: true,\n    contains: [\n      {\n        beginKeywords: 'and not'\n      }\n    ].concat(VALUE_MODES) // using this form to override VALUE’s 'function' match\n  };\n\n  /* Rule-Level Modes */\n\n  const RULE_MODE = {\n    begin: INTERP_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    end: /[;}]/,\n    relevance: 0,\n    contains: [\n      {\n        begin: /-(webkit|moz|ms|o)-/\n      },\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b',\n        end: /(?=:)/,\n        starts: {\n          endsWithParent: true,\n          illegal: '[<=$]',\n          relevance: 0,\n          contains: VALUE_MODES\n        }\n      }\n    ]\n  };\n\n  const AT_RULE_MODE = {\n    className: 'keyword',\n    begin: '@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\\\b',\n    starts: {\n      end: '[;{}]',\n      keywords: AT_KEYWORDS,\n      returnEnd: true,\n      contains: VALUE_MODES,\n      relevance: 0\n    }\n  };\n\n  // variable definitions and calls\n  const VAR_RULE_MODE = {\n    className: 'variable',\n    variants: [\n      // using more strict pattern for higher relevance to increase chances of Less detection.\n      // this is *the only* Less specific statement used in most of the sources, so...\n      // (we’ll still often loose to the css-parser unless there's '//' comment,\n      // simply because 1 variable just can't beat 99 properties :)\n      {\n        begin: '@' + IDENT_RE + '\\\\s*:',\n        relevance: 15\n      },\n      {\n        begin: '@' + IDENT_RE\n      }\n    ],\n    starts: {\n      end: '[;}]',\n      returnEnd: true,\n      contains: VALUE_WITH_RULESETS\n    }\n  };\n\n  const SELECTOR_MODE = {\n    // first parse unambiguous selectors (i.e. those not starting with tag)\n    // then fall into the scary lookahead-discriminator variant.\n    // this mode also handles mixin definitions and calls\n    variants: [\n      {\n        begin: '[\\\\.#:&\\\\[>]',\n        end: '[;{}]' // mixin calls end with ';'\n      },\n      {\n        begin: INTERP_IDENT_RE,\n        end: /\\{/\n      }\n    ],\n    returnBegin: true,\n    returnEnd: true,\n    illegal: '[<=\\'$\"]',\n    relevance: 0,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      MIXIN_GUARD_MODE,\n      IDENT_MODE('keyword', 'all\\\\b'),\n      IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'), // otherwise it’s identified as tag\n      {\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n        className: 'selector-tag'\n      },\n      IDENT_MODE('selector-tag', INTERP_IDENT_RE + '%?', 0), // '%' for more consistent coloring of @keyframes \"tags\"\n      IDENT_MODE('selector-id', '#' + INTERP_IDENT_RE),\n      IDENT_MODE('selector-class', '\\\\.' + INTERP_IDENT_RE, 0),\n      IDENT_MODE('selector-tag', '&', 0),\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        begin: ':(' + PSEUDO_CLASSES.join('|') + ')'\n      },\n      {\n        className: 'selector-pseudo',\n        begin: '::(' + PSEUDO_ELEMENTS.join('|') + ')'\n      },\n      {\n        begin: '\\\\(',\n        end: '\\\\)',\n        contains: VALUE_WITH_RULESETS\n      }, // argument list of parametric mixins\n      {\n        begin: '!important'\n      } // eat !important after mixin call or it will be colored as tag\n    ]\n  };\n\n  const PSEUDO_SELECTOR_MODE = {\n    begin: IDENT_RE + ':(:)?' + `(${PSEUDO_SELECTORS$1.join('|')})`,\n    returnBegin: true,\n    contains: [ SELECTOR_MODE ]\n  };\n\n  RULES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    AT_RULE_MODE,\n    VAR_RULE_MODE,\n    PSEUDO_SELECTOR_MODE,\n    RULE_MODE,\n    SELECTOR_MODE\n  );\n\n  return {\n    name: 'Less',\n    case_insensitive: true,\n    illegal: '[=>\\'/<($\"]',\n    contains: RULES\n  };\n}\n\nmodule.exports = less;\n"], "mappings": "AAAA,MAAMA,KAAK,GAAIC,IAAI,IAAK;EACtB,OAAO;IACLC,SAAS,EAAE;MACTC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC;IACDE,uBAAuB,EAAE;MACvBH,SAAS,EAAE,eAAe;MAC1BC,KAAK,EAAE,IAAI;MACXG,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,CACRR,IAAI,CAACS,gBAAgB,EACrBT,IAAI,CAACU,iBAAiB;IAE1B;EACF,CAAC;AACH,CAAC;AAED,MAAMC,IAAI,GAAG,CACX,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,GAAG,EACH,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,GAAG,EACH,GAAG,EACH,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,CACR;AAED,MAAMC,cAAc,GAAG,CACrB,WAAW,EACX,aAAa,EACb,cAAc,EACd,OAAO,EACP,aAAa,EACb,aAAa,EACb,qBAAqB,EACrB,eAAe,EACf,cAAc,EACd,cAAc,EACd,eAAe,EACf,MAAM,EACN,QAAQ,EACR,OAAO,EACP,iBAAiB,EACjB,YAAY,EACZ,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,SAAS,EACT,sBAAsB,EACtB,kBAAkB,EAClB,wBAAwB,EACxB,8BAA8B,EAC9B,YAAY,EACZ,MAAM,EACN,WAAW,EACX,QAAQ,EACR,OAAO;AACP;AACA,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,CACb;;AAED;AACA,MAAMC,cAAc,GAAG,CACrB,QAAQ,EACR,UAAU,EACV,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK;AAAE;AACP,UAAU,EACV,MAAM,EACN,OAAO,EACP,SAAS,EACT,OAAO,EACP,aAAa,EACb,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,eAAe,EACf,cAAc,EACd,KAAK;AAAE;AACP,MAAM;AAAE;AACR,cAAc;AAAE;AAChB,OAAO,EACP,eAAe,EACf,UAAU,EACV,SAAS,EACT,IAAI;AAAE;AACN,MAAM;AAAE;AACR,YAAY,EACZ,cAAc,EACd,MAAM,EACN,MAAM,EACN,YAAY,EACZ,KAAK;AAAE;AACP,WAAW;AAAE;AACb,SAAS;AAAE;AACX,gBAAgB;AAAE;AAClB,cAAc;AAAE;AAChB,kBAAkB;AAAE;AACpB,aAAa;AAAE;AACf,YAAY,EACZ,cAAc,EACd,UAAU,EACV,cAAc,EACd,MAAM,EACN,mBAAmB,EACnB,WAAW,EACX,YAAY,EACZ,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,eAAe,EACf,cAAc,EACd,OAAO,EACP,SAAS,EACT,OAAO,CAAC;AAAA,CACT;;AAED;AACA,MAAMC,eAAe,GAAG,CACtB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,MAAM,EACN,aAAa,EACb,WAAW,EACX,SAAS,EACT,gBAAgB,CACjB;AAED,MAAMC,UAAU,GAAG,CACjB,eAAe,EACf,aAAa,EACb,YAAY,EACZ,WAAW,EACX,iBAAiB,EACjB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,2BAA2B,EAC3B,gBAAgB,EAChB,sBAAsB,EACtB,2BAA2B,EAC3B,MAAM,EACN,qBAAqB,EACrB,YAAY,EACZ,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,qBAAqB,EACrB,mBAAmB,EACnB,iBAAiB,EACjB,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,2BAA2B,EAC3B,4BAA4B,EAC5B,qBAAqB,EACrB,qBAAqB,EACrB,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,oBAAoB,EACpB,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,wBAAwB,EACxB,yBAAyB,EACzB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,sBAAsB,EACtB,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,OAAO,EACP,MAAM,EACN,WAAW,EACX,OAAO,EACP,cAAc,EACd,aAAa,EACb,YAAY,EACZ,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,EACb,cAAc,EACd,SAAS,EACT,SAAS,EACT,mBAAmB,EACnB,eAAe,EACf,QAAQ,EACR,WAAW,EACX,SAAS,EACT,aAAa,EACb,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,WAAW,EACX,WAAW,EACX,aAAa,EACb,WAAW,EACX,OAAO,EACP,MAAM,EACN,cAAc,EACd,aAAa,EACb,uBAAuB,EACvB,cAAc,EACd,wBAAwB,EACxB,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,cAAc,EACd,wBAAwB,EACxB,yBAAyB,EACzB,aAAa,EACb,QAAQ,EACR,SAAS,EACT,MAAM,EACN,mBAAmB,EACnB,iBAAiB,EACjB,kBAAkB,EAClB,UAAU,EACV,SAAS,EACT,SAAS,EACT,iBAAiB,EACjB,MAAM,EACN,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,iBAAiB,EACjB,QAAQ,EACR,eAAe,EACf,aAAa,EACb,cAAc,EACd,YAAY,EACZ,OAAO,EACP,MAAM,EACN,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,WAAW,EACX,UAAU,EACV,WAAW,EACX,UAAU,EACV,WAAW,EACX,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,iBAAiB,EACjB,SAAS,EACT,OAAO,EACP,SAAS,EACT,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,UAAU,EACV,eAAe,EACf,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,EACb,oBAAoB,EACpB,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK;AAAE;AACP,UAAU,EACV,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,uBAAuB,EACvB,sBAAsB,EACtB,uBAAuB,EACvB,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,gBAAgB,EAChB,yBAAyB,EACzB,KAAK,EACL,WAAW,EACX,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,4BAA4B,EAC5B,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,cAAc,EACd,WAAW,EACX;AACA;AACA;AAAA,CACD,CAACC,OAAO,CAAC,CAAC;;AAEX;AACA,MAAMC,gBAAgB,GAAGJ,cAAc,CAACK,MAAM,CAACJ,eAAe,CAAC;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASK,IAAIA,CAACnB,IAAI,EAAE;EAClB,MAAMoB,KAAK,GAAGrB,KAAK,CAACC,IAAI,CAAC;EACzB,MAAMqB,kBAAkB,GAAGJ,gBAAgB;EAE3C,MAAMK,YAAY,GAAG,iBAAiB;EACtC,MAAMC,QAAQ,GAAG,SAAS,CAAC,CAAC;EAC5B,MAAMC,eAAe,GAAG,GAAG,GAAGD,QAAQ,GAAG,OAAO,GAAGA,QAAQ,GAAG,MAAM;;EAEpE;;EAEA,MAAME,KAAK,GAAG,EAAE;EAAE,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;;EAE1C,MAAMC,WAAW,GAAG,SAAAA,CAASC,CAAC,EAAE;IAC9B,OAAO;MACP;MACE1B,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,IAAI,GAAGyB,CAAC,GAAG,KAAK,GAAGA;IAC5B,CAAC;EACH,CAAC;EAED,MAAMC,UAAU,GAAG,SAAAA,CAASC,IAAI,EAAE3B,KAAK,EAAE4B,SAAS,EAAE;IAClD,OAAO;MACL7B,SAAS,EAAE4B,IAAI;MACf3B,KAAK,EAAEA,KAAK;MACZ4B,SAAS,EAAEA;IACb,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAEZ,YAAY;IACrBa,SAAS,EAAEvB,cAAc,CAACwB,IAAI,CAAC,GAAG;EACpC,CAAC;EAED,MAAMC,WAAW,GAAG;IAClB;IACAlC,KAAK,EAAE,KAAK;IACZG,GAAG,EAAE,KAAK;IACVE,QAAQ,EAAEkB,WAAW;IACrBY,QAAQ,EAAEN,WAAW;IACrBD,SAAS,EAAE;EACb,CAAC;;EAED;EACAL,WAAW,CAACa,IAAI,CACdvC,IAAI,CAACwC,mBAAmB,EACxBxC,IAAI,CAACyC,oBAAoB,EACzBd,WAAW,CAAC,GAAG,CAAC,EAChBA,WAAW,CAAC,GAAG,CAAC,EAChB3B,IAAI,CAAC0C,eAAe;EAAE;EACtB;IACEvC,KAAK,EAAE,mBAAmB;IAC1BwC,MAAM,EAAE;MACNzC,SAAS,EAAE,QAAQ;MACnBI,GAAG,EAAE,UAAU;MACfsC,UAAU,EAAE;IACd;EACF,CAAC,EACDxB,KAAK,CAAChB,QAAQ,EACdiC,WAAW,EACXR,UAAU,CAAC,UAAU,EAAE,KAAK,GAAGN,QAAQ,EAAE,EAAE,CAAC,EAC5CM,UAAU,CAAC,UAAU,EAAE,MAAM,GAAGN,QAAQ,GAAG,KAAK,CAAC,EACjDM,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC;EAAE;EACtC;IAAE;IACA3B,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAEoB,QAAQ,GAAG,OAAO;IACzBjB,GAAG,EAAE,GAAG;IACRuC,WAAW,EAAE,IAAI;IACjBD,UAAU,EAAE;EACd,CAAC,EACDxB,KAAK,CAACnB,SACR,CAAC;EAED,MAAM6C,mBAAmB,GAAGpB,WAAW,CAACR,MAAM,CAAC;IAC7Cf,KAAK,EAAE,IAAI;IACXG,GAAG,EAAE,IAAI;IACTE,QAAQ,EAAEiB;EACZ,CAAC,CAAC;EAEF,MAAMsB,gBAAgB,GAAG;IACvBC,aAAa,EAAE,MAAM;IACrBC,cAAc,EAAE,IAAI;IACpBzC,QAAQ,EAAE,CACR;MACEwC,aAAa,EAAE;IACjB,CAAC,CACF,CAAC9B,MAAM,CAACQ,WAAW,CAAC,CAAC;EACxB,CAAC;;EAED;;EAEA,MAAMwB,SAAS,GAAG;IAChB/C,KAAK,EAAEqB,eAAe,GAAG,OAAO;IAChCqB,WAAW,EAAE,IAAI;IACjBvC,GAAG,EAAE,MAAM;IACXyB,SAAS,EAAE,CAAC;IACZvB,QAAQ,EAAE,CACR;MACEL,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,MAAM,GAAGY,UAAU,CAACqB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;MAC7C9B,GAAG,EAAE,OAAO;MACZqC,MAAM,EAAE;QACNM,cAAc,EAAE,IAAI;QACpB1C,OAAO,EAAE,OAAO;QAChBwB,SAAS,EAAE,CAAC;QACZvB,QAAQ,EAAEkB;MACZ;IACF,CAAC;EAEL,CAAC;EAED,MAAMyB,YAAY,GAAG;IACnBjD,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,0GAA0G;IACjHwC,MAAM,EAAE;MACNrC,GAAG,EAAE,OAAO;MACZgC,QAAQ,EAAEN,WAAW;MACrBoB,SAAS,EAAE,IAAI;MACf5C,QAAQ,EAAEkB,WAAW;MACrBK,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMsB,aAAa,GAAG;IACpBnD,SAAS,EAAE,UAAU;IACrBoD,QAAQ,EAAE;IACR;IACA;IACA;IACA;IACA;MACEnD,KAAK,EAAE,GAAG,GAAGoB,QAAQ,GAAG,OAAO;MAC/BQ,SAAS,EAAE;IACb,CAAC,EACD;MACE5B,KAAK,EAAE,GAAG,GAAGoB;IACf,CAAC,CACF;IACDoB,MAAM,EAAE;MACNrC,GAAG,EAAE,MAAM;MACX8C,SAAS,EAAE,IAAI;MACf5C,QAAQ,EAAEsC;IACZ;EACF,CAAC;EAED,MAAMS,aAAa,GAAG;IACpB;IACA;IACA;IACAD,QAAQ,EAAE,CACR;MACEnD,KAAK,EAAE,cAAc;MACrBG,GAAG,EAAE,OAAO,CAAC;IACf,CAAC,EACD;MACEH,KAAK,EAAEqB,eAAe;MACtBlB,GAAG,EAAE;IACP,CAAC,CACF;IACDuC,WAAW,EAAE,IAAI;IACjBO,SAAS,EAAE,IAAI;IACf7C,OAAO,EAAE,UAAU;IACnBwB,SAAS,EAAE,CAAC;IACZvB,QAAQ,EAAE,CACRR,IAAI,CAACwC,mBAAmB,EACxBxC,IAAI,CAACyC,oBAAoB,EACzBM,gBAAgB,EAChBlB,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,EAC/BA,UAAU,CAAC,UAAU,EAAE,MAAM,GAAGN,QAAQ,GAAG,KAAK,CAAC;IAAE;IACnD;MACEpB,KAAK,EAAE,MAAM,GAAGQ,IAAI,CAACyB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;MACvClC,SAAS,EAAE;IACb,CAAC,EACD2B,UAAU,CAAC,cAAc,EAAEL,eAAe,GAAG,IAAI,EAAE,CAAC,CAAC;IAAE;IACvDK,UAAU,CAAC,aAAa,EAAE,GAAG,GAAGL,eAAe,CAAC,EAChDK,UAAU,CAAC,gBAAgB,EAAE,KAAK,GAAGL,eAAe,EAAE,CAAC,CAAC,EACxDK,UAAU,CAAC,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC,EAClCT,KAAK,CAACf,uBAAuB,EAC7B;MACEH,SAAS,EAAE,iBAAiB;MAC5BC,KAAK,EAAE,IAAI,GAAGU,cAAc,CAACuB,IAAI,CAAC,GAAG,CAAC,GAAG;IAC3C,CAAC,EACD;MACElC,SAAS,EAAE,iBAAiB;MAC5BC,KAAK,EAAE,KAAK,GAAGW,eAAe,CAACsB,IAAI,CAAC,GAAG,CAAC,GAAG;IAC7C,CAAC,EACD;MACEjC,KAAK,EAAE,KAAK;MACZG,GAAG,EAAE,KAAK;MACVE,QAAQ,EAAEsC;IACZ,CAAC;IAAE;IACH;MACE3C,KAAK,EAAE;IACT,CAAC,CAAC;IAAA;EAEN,CAAC;EAED,MAAMqD,oBAAoB,GAAG;IAC3BrD,KAAK,EAAEoB,QAAQ,GAAG,OAAO,GAAG,IAAIF,kBAAkB,CAACe,IAAI,CAAC,GAAG,CAAC,GAAG;IAC/DS,WAAW,EAAE,IAAI;IACjBrC,QAAQ,EAAE,CAAE+C,aAAa;EAC3B,CAAC;EAED9B,KAAK,CAACc,IAAI,CACRvC,IAAI,CAACwC,mBAAmB,EACxBxC,IAAI,CAACyC,oBAAoB,EACzBU,YAAY,EACZE,aAAa,EACbG,oBAAoB,EACpBN,SAAS,EACTK,aACF,CAAC;EAED,OAAO;IACLzB,IAAI,EAAE,MAAM;IACZ2B,gBAAgB,EAAE,IAAI;IACtBlD,OAAO,EAAE,aAAa;IACtBC,QAAQ,EAAEiB;EACZ,CAAC;AACH;AAEAiC,MAAM,CAACC,OAAO,GAAGxC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}