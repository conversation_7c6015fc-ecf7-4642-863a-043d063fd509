{"ast": null, "code": "import highlight from './highlight';\nimport lowlight from 'lowlight/lib/core';\nvar SyntaxHighlighter = highlight(lowlight, {});\nSyntaxHighlighter.registerLanguage = lowlight.registerLanguage;\nexport default SyntaxHighlighter;", "map": {"version": 3, "names": ["highlight", "lowlight", "Syntax<PERSON><PERSON><PERSON><PERSON>", "registerLanguage"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/light.js"], "sourcesContent": ["import highlight from './highlight';\nimport lowlight from 'lowlight/lib/core';\nvar SyntaxHighlighter = highlight(lowlight, {});\nSyntaxHighlighter.registerLanguage = lowlight.registerLanguage;\nexport default SyntaxHighlighter;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,IAAIC,iBAAiB,GAAGF,SAAS,CAACC,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC/CC,iBAAiB,CAACC,gBAAgB,GAAGF,QAAQ,CAACE,gBAAgB;AAC9D,eAAeD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}