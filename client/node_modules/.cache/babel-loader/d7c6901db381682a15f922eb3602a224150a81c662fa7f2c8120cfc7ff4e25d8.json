{"ast": null, "code": "'use strict';\n\nmodule.exports = csharp;\ncsharp.displayName = 'csharp';\ncsharp.aliases = ['dotnet', 'cs'];\nfunction csharp(Prism) {\n  ;\n  (function (Prism) {\n    /**\n     * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n     *\n     * Note: This is a simple text based replacement. Be careful when using backreferences!\n     *\n     * @param {string} pattern the given pattern.\n     * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n     * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n     * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n     */\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return '(?:' + replacements[+index] + ')';\n      });\n    }\n    /**\n     * @param {string} pattern\n     * @param {string[]} replacements\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '');\n    }\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<<self>>/g, function () {\n          return '(?:' + pattern + ')';\n        });\n      }\n      return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]');\n    } // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n    var keywordKinds = {\n      // keywords which represent a return or variable type\n      type: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n      // keywords which are used to declare a type\n      typeDeclaration: 'class enum interface record struct',\n      // contextual keywords\n      // (\"var\" and \"dynamic\" are missing because they are used like types)\n      contextual: 'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n      // all other keywords\n      other: 'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n    }; // keywords\n    function keywordsToPattern(words) {\n      return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b';\n    }\n    var typeDeclarationKeywords = keywordsToPattern(keywordKinds.typeDeclaration);\n    var keywords = RegExp(keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other));\n    var nonTypeKeywords = keywordsToPattern(keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other);\n    var nonContextualKeywords = keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.other); // types\n    var generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2); // the idea behind the other forbidden characters is to prevent false positives. Same for tupleElement.\n    var nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2);\n    var name = /@?\\b[A-Za-z_]\\w*\\b/.source;\n    var genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [name, generic]);\n    var identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [nonTypeKeywords, genericName]);\n    var array = /\\[\\s*(?:,\\s*)*\\]/.source;\n    var typeExpressionWithoutTuple = replace(/<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source, [identifier, array]);\n    var tupleElement = replace(/[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source, [generic, nestedRound, array]);\n    var tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement]);\n    var typeExpression = replace(/(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source, [tuple, identifier, array]);\n    var typeInside = {\n      keyword: keywords,\n      punctuation: /[<>()?,.:[\\]]/\n    }; // strings & characters\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#character-literals\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#string-literals\n    var character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source; // simplified pattern\n    var regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source;\n    var verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source;\n    Prism.languages.csharp = Prism.languages.extend('clike', {\n      string: [{\n        pattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n        lookbehind: true,\n        greedy: true\n      }, {\n        pattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n        lookbehind: true,\n        greedy: true\n      }],\n      'class-name': [{\n        // Using static\n        // using static System.Math;\n        pattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [identifier]),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // Using alias (type)\n        // using Project = PC.MyCompany.Project;\n        pattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [name, typeExpression]),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // Using alias (alias)\n        // using Project = PC.MyCompany.Project;\n        pattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n        lookbehind: true\n      }, {\n        // Type declarations\n        // class Foo<A, B>\n        // interface Foo<out A, B>\n        pattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [typeDeclarationKeywords, genericName]),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // Single catch exception declaration\n        // catch(Foo)\n        // (things like catch(Foo e) is covered by variable declaration)\n        pattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // Name of the type parameter of generic constraints\n        // where Foo : class\n        pattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n        lookbehind: true\n      }, {\n        // Casts and checks via as and is.\n        // as Foo<A>, is Bar<B>\n        // (things like if(a is Foo b) is covered by variable declaration)\n        pattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [typeExpressionWithoutTuple]),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // Variable, field and parameter declaration\n        // (Foo bar, Bar baz, Foo[,,] bay, Foo<Bar, FooBar<Bar>> bax)\n        pattern: re(/\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/.source, [typeExpression, nonContextualKeywords, name]),\n        inside: typeInside\n      }],\n      keyword: keywords,\n      // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#literals\n      number: /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n      operator: />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n      punctuation: /\\?\\.?|::|[{}[\\];(),.:]/\n    });\n    Prism.languages.insertBefore('csharp', 'number', {\n      range: {\n        pattern: /\\.\\./,\n        alias: 'operator'\n      }\n    });\n    Prism.languages.insertBefore('csharp', 'punctuation', {\n      'named-parameter': {\n        pattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n        lookbehind: true,\n        alias: 'punctuation'\n      }\n    });\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      namespace: {\n        // namespace Foo.Bar {}\n        // using Foo.Bar;\n        pattern: re(/(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source, [name]),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      'type-expression': {\n        // default(Foo), typeof(Foo<Bar>), sizeof(int)\n        pattern: re(/(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/.source, [nestedRound]),\n        lookbehind: true,\n        alias: 'class-name',\n        inside: typeInside\n      },\n      'return-type': {\n        // Foo<Bar> ForBar(); Foo IFoo.Bar() => 0\n        // int this[int index] => 0; T IReadOnlyList<T>.this[int index] => this[index];\n        // int Foo => 0; int Foo { get; set } = 0;\n        pattern: re(/<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source, [typeExpression, identifier]),\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      'constructor-invocation': {\n        // new List<Foo<Bar[]>> { }\n        pattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n        lookbehind: true,\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      /*'explicit-implementation': {\n      // int IFoo<Foo>.Bar => 0; void IFoo<Foo<Foo>>.Foo<T>();\n      pattern: replace(/\\b<<0>>(?=\\.<<1>>)/, className, methodOrPropertyDeclaration),\n      inside: classNameInside,\n      alias: 'class-name'\n      },*/\n      'generic-method': {\n        // foo<Bar>()\n        pattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [name, generic]),\n        inside: {\n          function: re(/^<<0>>/.source, [name]),\n          generic: {\n            pattern: RegExp(generic),\n            alias: 'class-name',\n            inside: typeInside\n          }\n        }\n      },\n      'type-list': {\n        // The list of types inherited or of generic constraints\n        // class Foo<F> : Bar, IList<FooBar>\n        // where F : Bar, IList<int>\n        pattern: re(/\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/.source, [typeDeclarationKeywords, genericName, name, typeExpression, keywords.source, nestedRound, /\\bnew\\s*\\(\\s*\\)/.source]),\n        lookbehind: true,\n        inside: {\n          'record-arguments': {\n            pattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [genericName, nestedRound]),\n            lookbehind: true,\n            greedy: true,\n            inside: Prism.languages.csharp\n          },\n          keyword: keywords,\n          'class-name': {\n            pattern: RegExp(typeExpression),\n            greedy: true,\n            inside: typeInside\n          },\n          punctuation: /[,()]/\n        }\n      },\n      preprocessor: {\n        pattern: /(^[\\t ]*)#.*/m,\n        lookbehind: true,\n        alias: 'property',\n        inside: {\n          // highlight preprocessor directives as keywords\n          directive: {\n            pattern: /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n            lookbehind: true,\n            alias: 'keyword'\n          }\n        }\n      }\n    }); // attributes\n    var regularStringOrCharacter = regularString + '|' + character;\n    var regularStringCharacterOrComment = replace(/\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source, [regularStringOrCharacter]);\n    var roundExpression = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2); // https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/attributes/#attribute-targets\n    var attrTarget = /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/.source;\n    var attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [identifier, roundExpression]);\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      attribute: {\n        // Attributes\n        // [Foo], [Foo(1), Bar(2, Prop = \"foo\")], [return: Foo(1), Bar(2)], [assembly: Foo(Bar)]\n        pattern: re(/((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/.source, [attrTarget, attr]),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          target: {\n            pattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n            alias: 'keyword'\n          },\n          'attribute-arguments': {\n            pattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n            inside: Prism.languages.csharp\n          },\n          'class-name': {\n            pattern: RegExp(identifier),\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          punctuation: /[:,]/\n        }\n      }\n    }); // string interpolation\n    var formatString = /:[^}\\r\\n]+/.source; // multi line\n    var mInterpolationRound = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n    var mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [mInterpolationRound, formatString]); // single line\n    var sInterpolationRound = nested(replace(/[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/.source, [regularStringOrCharacter]), 2);\n    var sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [sInterpolationRound, formatString]);\n    function createInterpolationInside(interpolation, interpolationRound) {\n      return {\n        interpolation: {\n          pattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n          lookbehind: true,\n          inside: {\n            'format-string': {\n              pattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [interpolationRound, formatString]),\n              lookbehind: true,\n              inside: {\n                punctuation: /^:/\n              }\n            },\n            punctuation: /^\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              alias: 'language-csharp',\n              inside: Prism.languages.csharp\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      };\n    }\n    Prism.languages.insertBefore('csharp', 'string', {\n      'interpolation-string': [{\n        pattern: re(/(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source, [mInterpolation]),\n        lookbehind: true,\n        greedy: true,\n        inside: createInterpolationInside(mInterpolation, mInterpolationRound)\n      }, {\n        pattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [sInterpolation]),\n        lookbehind: true,\n        greedy: true,\n        inside: createInterpolationInside(sInterpolation, sInterpolationRound)\n      }],\n      char: {\n        pattern: RegExp(character),\n        greedy: true\n      }\n    });\n    Prism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "csharp", "displayName", "aliases", "Prism", "replace", "pattern", "replacements", "m", "index", "re", "flags", "RegExp", "nested", "depthLog2", "i", "keywordKinds", "type", "typeDeclaration", "contextual", "other", "keywordsToPattern", "words", "trim", "typeDeclarationKeywords", "keywords", "nonTypeKeywords", "nonContextualKeywords", "generic", "source", "nestedRound", "name", "genericName", "identifier", "array", "typeExpressionWithoutTuple", "tupleElement", "tuple", "typeExpression", "typeInside", "keyword", "punctuation", "character", "regularString", "verbatimString", "languages", "extend", "string", "lookbehind", "greedy", "inside", "number", "operator", "insertBefore", "range", "alias", "namespace", "function", "preprocessor", "directive", "regularStringOrCharacter", "regularStringCharacterOrComment", "roundExpression", "attrTarget", "attr", "attribute", "target", "formatString", "mInterpolationRound", "mInterpolation", "sInterpolationRound", "sInterpolation", "createInterpolationInside", "interpolation", "interpolationRound", "expression", "char", "dotnet", "cs"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/csharp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = csharp\ncsharp.displayName = 'csharp'\ncsharp.aliases = ['dotnet', 'cs']\nfunction csharp(Prism) {\n  ;(function (Prism) {\n    /**\n     * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n     *\n     * Note: This is a simple text based replacement. Be careful when using backreferences!\n     *\n     * @param {string} pattern the given pattern.\n     * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n     * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n     * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n     */\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return '(?:' + replacements[+index] + ')'\n      })\n    }\n    /**\n     * @param {string} pattern\n     * @param {string[]} replacements\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<<self>>/g, function () {\n          return '(?:' + pattern + ')'\n        })\n      }\n      return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]')\n    } // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n    var keywordKinds = {\n      // keywords which represent a return or variable type\n      type: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n      // keywords which are used to declare a type\n      typeDeclaration: 'class enum interface record struct',\n      // contextual keywords\n      // (\"var\" and \"dynamic\" are missing because they are used like types)\n      contextual:\n        'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n      // all other keywords\n      other:\n        'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n    } // keywords\n    function keywordsToPattern(words) {\n      return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b'\n    }\n    var typeDeclarationKeywords = keywordsToPattern(\n      keywordKinds.typeDeclaration\n    )\n    var keywords = RegExp(\n      keywordsToPattern(\n        keywordKinds.type +\n          ' ' +\n          keywordKinds.typeDeclaration +\n          ' ' +\n          keywordKinds.contextual +\n          ' ' +\n          keywordKinds.other\n      )\n    )\n    var nonTypeKeywords = keywordsToPattern(\n      keywordKinds.typeDeclaration +\n        ' ' +\n        keywordKinds.contextual +\n        ' ' +\n        keywordKinds.other\n    )\n    var nonContextualKeywords = keywordsToPattern(\n      keywordKinds.type +\n        ' ' +\n        keywordKinds.typeDeclaration +\n        ' ' +\n        keywordKinds.other\n    ) // types\n    var generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2) // the idea behind the other forbidden characters is to prevent false positives. Same for tupleElement.\n    var nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2)\n    var name = /@?\\b[A-Za-z_]\\w*\\b/.source\n    var genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [name, generic])\n    var identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [\n      nonTypeKeywords,\n      genericName\n    ])\n    var array = /\\[\\s*(?:,\\s*)*\\]/.source\n    var typeExpressionWithoutTuple = replace(\n      /<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source,\n      [identifier, array]\n    )\n    var tupleElement = replace(\n      /[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source,\n      [generic, nestedRound, array]\n    )\n    var tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement])\n    var typeExpression = replace(\n      /(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source,\n      [tuple, identifier, array]\n    )\n    var typeInside = {\n      keyword: keywords,\n      punctuation: /[<>()?,.:[\\]]/\n    } // strings & characters\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#character-literals\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#string-literals\n    var character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source // simplified pattern\n    var regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source\n    var verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source\n    Prism.languages.csharp = Prism.languages.extend('clike', {\n      string: [\n        {\n          pattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'class-name': [\n        {\n          // Using static\n          // using static System.Math;\n          pattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [\n            identifier\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Using alias (type)\n          // using Project = PC.MyCompany.Project;\n          pattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [\n            name,\n            typeExpression\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Using alias (alias)\n          // using Project = PC.MyCompany.Project;\n          pattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n          lookbehind: true\n        },\n        {\n          // Type declarations\n          // class Foo<A, B>\n          // interface Foo<out A, B>\n          pattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [\n            typeDeclarationKeywords,\n            genericName\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Single catch exception declaration\n          // catch(Foo)\n          // (things like catch(Foo e) is covered by variable declaration)\n          pattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Name of the type parameter of generic constraints\n          // where Foo : class\n          pattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n          lookbehind: true\n        },\n        {\n          // Casts and checks via as and is.\n          // as Foo<A>, is Bar<B>\n          // (things like if(a is Foo b) is covered by variable declaration)\n          pattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [\n            typeExpressionWithoutTuple\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Variable, field and parameter declaration\n          // (Foo bar, Bar baz, Foo[,,] bay, Foo<Bar, FooBar<Bar>> bax)\n          pattern: re(\n            /\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/\n              .source,\n            [typeExpression, nonContextualKeywords, name]\n          ),\n          inside: typeInside\n        }\n      ],\n      keyword: keywords,\n      // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#literals\n      number:\n        /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n      operator: />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n      punctuation: /\\?\\.?|::|[{}[\\];(),.:]/\n    })\n    Prism.languages.insertBefore('csharp', 'number', {\n      range: {\n        pattern: /\\.\\./,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.insertBefore('csharp', 'punctuation', {\n      'named-parameter': {\n        pattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n        lookbehind: true,\n        alias: 'punctuation'\n      }\n    })\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      namespace: {\n        // namespace Foo.Bar {}\n        // using Foo.Bar;\n        pattern: re(\n          /(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source,\n          [name]\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      'type-expression': {\n        // default(Foo), typeof(Foo<Bar>), sizeof(int)\n        pattern: re(\n          /(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/\n            .source,\n          [nestedRound]\n        ),\n        lookbehind: true,\n        alias: 'class-name',\n        inside: typeInside\n      },\n      'return-type': {\n        // Foo<Bar> ForBar(); Foo IFoo.Bar() => 0\n        // int this[int index] => 0; T IReadOnlyList<T>.this[int index] => this[index];\n        // int Foo => 0; int Foo { get; set } = 0;\n        pattern: re(\n          /<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source,\n          [typeExpression, identifier]\n        ),\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      'constructor-invocation': {\n        // new List<Foo<Bar[]>> { }\n        pattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n        lookbehind: true,\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      /*'explicit-implementation': {\n// int IFoo<Foo>.Bar => 0; void IFoo<Foo<Foo>>.Foo<T>();\npattern: replace(/\\b<<0>>(?=\\.<<1>>)/, className, methodOrPropertyDeclaration),\ninside: classNameInside,\nalias: 'class-name'\n},*/\n      'generic-method': {\n        // foo<Bar>()\n        pattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [name, generic]),\n        inside: {\n          function: re(/^<<0>>/.source, [name]),\n          generic: {\n            pattern: RegExp(generic),\n            alias: 'class-name',\n            inside: typeInside\n          }\n        }\n      },\n      'type-list': {\n        // The list of types inherited or of generic constraints\n        // class Foo<F> : Bar, IList<FooBar>\n        // where F : Bar, IList<int>\n        pattern: re(\n          /\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/\n            .source,\n          [\n            typeDeclarationKeywords,\n            genericName,\n            name,\n            typeExpression,\n            keywords.source,\n            nestedRound,\n            /\\bnew\\s*\\(\\s*\\)/.source\n          ]\n        ),\n        lookbehind: true,\n        inside: {\n          'record-arguments': {\n            pattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [\n              genericName,\n              nestedRound\n            ]),\n            lookbehind: true,\n            greedy: true,\n            inside: Prism.languages.csharp\n          },\n          keyword: keywords,\n          'class-name': {\n            pattern: RegExp(typeExpression),\n            greedy: true,\n            inside: typeInside\n          },\n          punctuation: /[,()]/\n        }\n      },\n      preprocessor: {\n        pattern: /(^[\\t ]*)#.*/m,\n        lookbehind: true,\n        alias: 'property',\n        inside: {\n          // highlight preprocessor directives as keywords\n          directive: {\n            pattern:\n              /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n            lookbehind: true,\n            alias: 'keyword'\n          }\n        }\n      }\n    }) // attributes\n    var regularStringOrCharacter = regularString + '|' + character\n    var regularStringCharacterOrComment = replace(\n      /\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source,\n      [regularStringOrCharacter]\n    )\n    var roundExpression = nested(\n      replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [\n        regularStringCharacterOrComment\n      ]),\n      2\n    ) // https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/attributes/#attribute-targets\n    var attrTarget =\n      /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/\n        .source\n    var attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [\n      identifier,\n      roundExpression\n    ])\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      attribute: {\n        // Attributes\n        // [Foo], [Foo(1), Bar(2, Prop = \"foo\")], [return: Foo(1), Bar(2)], [assembly: Foo(Bar)]\n        pattern: re(\n          /((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/\n            .source,\n          [attrTarget, attr]\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          target: {\n            pattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n            alias: 'keyword'\n          },\n          'attribute-arguments': {\n            pattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n            inside: Prism.languages.csharp\n          },\n          'class-name': {\n            pattern: RegExp(identifier),\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          punctuation: /[:,]/\n        }\n      }\n    }) // string interpolation\n    var formatString = /:[^}\\r\\n]+/.source // multi line\n    var mInterpolationRound = nested(\n      replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [\n        regularStringCharacterOrComment\n      ]),\n      2\n    )\n    var mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n      mInterpolationRound,\n      formatString\n    ]) // single line\n    var sInterpolationRound = nested(\n      replace(\n        /[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/\n          .source,\n        [regularStringOrCharacter]\n      ),\n      2\n    )\n    var sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n      sInterpolationRound,\n      formatString\n    ])\n    function createInterpolationInside(interpolation, interpolationRound) {\n      return {\n        interpolation: {\n          pattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n          lookbehind: true,\n          inside: {\n            'format-string': {\n              pattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [\n                interpolationRound,\n                formatString\n              ]),\n              lookbehind: true,\n              inside: {\n                punctuation: /^:/\n              }\n            },\n            punctuation: /^\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              alias: 'language-csharp',\n              inside: Prism.languages.csharp\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n    Prism.languages.insertBefore('csharp', 'string', {\n      'interpolation-string': [\n        {\n          pattern: re(\n            /(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source,\n            [mInterpolation]\n          ),\n          lookbehind: true,\n          greedy: true,\n          inside: createInterpolationInside(mInterpolation, mInterpolationRound)\n        },\n        {\n          pattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [\n            sInterpolation\n          ]),\n          lookbehind: true,\n          greedy: true,\n          inside: createInterpolationInside(sInterpolation, sInterpolationRound)\n        }\n      ],\n      char: {\n        pattern: RegExp(character),\n        greedy: true\n      }\n    })\n    Prism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;AACjC,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,OAAOA,CAACC,OAAO,EAAEC,YAAY,EAAE;MACtC,OAAOD,OAAO,CAACD,OAAO,CAAC,YAAY,EAAE,UAAUG,CAAC,EAAEC,KAAK,EAAE;QACvD,OAAO,KAAK,GAAGF,YAAY,CAAC,CAACE,KAAK,CAAC,GAAG,GAAG;MAC3C,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,EAAEA,CAACJ,OAAO,EAAEC,YAAY,EAAEI,KAAK,EAAE;MACxC,OAAOC,MAAM,CAACP,OAAO,CAACC,OAAO,EAAEC,YAAY,CAAC,EAAEI,KAAK,IAAI,EAAE,CAAC;IAC5D;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASE,MAAMA,CAACP,OAAO,EAAEQ,SAAS,EAAE;MAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClCT,OAAO,GAAGA,OAAO,CAACD,OAAO,CAAC,WAAW,EAAE,YAAY;UACjD,OAAO,KAAK,GAAGC,OAAO,GAAG,GAAG;QAC9B,CAAC,CAAC;MACJ;MACA,OAAOA,OAAO,CAACD,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC;IAClD,CAAC,CAAC;IACF,IAAIW,YAAY,GAAG;MACjB;MACAC,IAAI,EAAE,2GAA2G;MACjH;MACAC,eAAe,EAAE,oCAAoC;MACrD;MACA;MACAC,UAAU,EACR,0NAA0N;MAC5N;MACAC,KAAK,EACH;IACJ,CAAC,EAAC;IACF,SAASC,iBAAiBA,CAACC,KAAK,EAAE;MAChC,OAAO,QAAQ,GAAGA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAClB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,MAAM;IAC5D;IACA,IAAImB,uBAAuB,GAAGH,iBAAiB,CAC7CL,YAAY,CAACE,eACf,CAAC;IACD,IAAIO,QAAQ,GAAGb,MAAM,CACnBS,iBAAiB,CACfL,YAAY,CAACC,IAAI,GACf,GAAG,GACHD,YAAY,CAACE,eAAe,GAC5B,GAAG,GACHF,YAAY,CAACG,UAAU,GACvB,GAAG,GACHH,YAAY,CAACI,KACjB,CACF,CAAC;IACD,IAAIM,eAAe,GAAGL,iBAAiB,CACrCL,YAAY,CAACE,eAAe,GAC1B,GAAG,GACHF,YAAY,CAACG,UAAU,GACvB,GAAG,GACHH,YAAY,CAACI,KACjB,CAAC;IACD,IAAIO,qBAAqB,GAAGN,iBAAiB,CAC3CL,YAAY,CAACC,IAAI,GACf,GAAG,GACHD,YAAY,CAACE,eAAe,GAC5B,GAAG,GACHF,YAAY,CAACI,KACjB,CAAC,EAAC;IACF,IAAIQ,OAAO,GAAGf,MAAM,CAAC,kCAAkC,CAACgB,MAAM,EAAE,CAAC,CAAC,EAAC;IACnE,IAAIC,WAAW,GAAGjB,MAAM,CAAC,yBAAyB,CAACgB,MAAM,EAAE,CAAC,CAAC;IAC7D,IAAIE,IAAI,GAAG,oBAAoB,CAACF,MAAM;IACtC,IAAIG,WAAW,GAAG3B,OAAO,CAAC,oBAAoB,CAACwB,MAAM,EAAE,CAACE,IAAI,EAAEH,OAAO,CAAC,CAAC;IACvE,IAAIK,UAAU,GAAG5B,OAAO,CAAC,kCAAkC,CAACwB,MAAM,EAAE,CAClEH,eAAe,EACfM,WAAW,CACZ,CAAC;IACF,IAAIE,KAAK,GAAG,kBAAkB,CAACL,MAAM;IACrC,IAAIM,0BAA0B,GAAG9B,OAAO,CACtC,wCAAwC,CAACwB,MAAM,EAC/C,CAACI,UAAU,EAAEC,KAAK,CACpB,CAAC;IACD,IAAIE,YAAY,GAAG/B,OAAO,CACxB,0CAA0C,CAACwB,MAAM,EACjD,CAACD,OAAO,EAAEE,WAAW,EAAEI,KAAK,CAC9B,CAAC;IACD,IAAIG,KAAK,GAAGhC,OAAO,CAAC,wBAAwB,CAACwB,MAAM,EAAE,CAACO,YAAY,CAAC,CAAC;IACpE,IAAIE,cAAc,GAAGjC,OAAO,CAC1B,kDAAkD,CAACwB,MAAM,EACzD,CAACQ,KAAK,EAAEJ,UAAU,EAAEC,KAAK,CAC3B,CAAC;IACD,IAAIK,UAAU,GAAG;MACfC,OAAO,EAAEf,QAAQ;MACjBgB,WAAW,EAAE;IACf,CAAC,EAAC;IACF;IACA;IACA,IAAIC,SAAS,GAAG,6CAA6C,CAACb,MAAM,EAAC;IACrE,IAAIc,aAAa,GAAG,uBAAuB,CAACd,MAAM;IAClD,IAAIe,cAAc,GAAG,iCAAiC,CAACf,MAAM;IAC7DzB,KAAK,CAACyC,SAAS,CAAC5C,MAAM,GAAGG,KAAK,CAACyC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MACvDC,MAAM,EAAE,CACN;QACEzC,OAAO,EAAEI,EAAE,CAAC,iBAAiB,CAACmB,MAAM,EAAE,CAACe,cAAc,CAAC,CAAC;QACvDI,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACE3C,OAAO,EAAEI,EAAE,CAAC,kBAAkB,CAACmB,MAAM,EAAE,CAACc,aAAa,CAAC,CAAC;QACvDK,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACD,YAAY,EAAE,CACZ;QACE;QACA;QACA3C,OAAO,EAAEI,EAAE,CAAC,oCAAoC,CAACmB,MAAM,EAAE,CACvDI,UAAU,CACX,CAAC;QACFe,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEX;MACV,CAAC,EACD;QACE;QACA;QACAjC,OAAO,EAAEI,EAAE,CAAC,uCAAuC,CAACmB,MAAM,EAAE,CAC1DE,IAAI,EACJO,cAAc,CACf,CAAC;QACFU,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEX;MACV,CAAC,EACD;QACE;QACA;QACAjC,OAAO,EAAEI,EAAE,CAAC,2BAA2B,CAACmB,MAAM,EAAE,CAACE,IAAI,CAAC,CAAC;QACvDiB,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACA;QACA;QACA1C,OAAO,EAAEI,EAAE,CAAC,mBAAmB,CAACmB,MAAM,EAAE,CACtCL,uBAAuB,EACvBQ,WAAW,CACZ,CAAC;QACFgB,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEX;MACV,CAAC,EACD;QACE;QACA;QACA;QACAjC,OAAO,EAAEI,EAAE,CAAC,wBAAwB,CAACmB,MAAM,EAAE,CAACI,UAAU,CAAC,CAAC;QAC1De,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEX;MACV,CAAC,EACD;QACE;QACA;QACAjC,OAAO,EAAEI,EAAE,CAAC,mBAAmB,CAACmB,MAAM,EAAE,CAACE,IAAI,CAAC,CAAC;QAC/CiB,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACA;QACA;QACA1C,OAAO,EAAEI,EAAE,CAAC,kCAAkC,CAACmB,MAAM,EAAE,CACrDM,0BAA0B,CAC3B,CAAC;QACFa,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEX;MACV,CAAC,EACD;QACE;QACA;QACAjC,OAAO,EAAEI,EAAE,CACT,0EAA0E,CACvEmB,MAAM,EACT,CAACS,cAAc,EAAEX,qBAAqB,EAAEI,IAAI,CAC9C,CAAC;QACDmB,MAAM,EAAEX;MACV,CAAC,CACF;MACDC,OAAO,EAAEf,QAAQ;MACjB;MACA0B,MAAM,EACJ,iJAAiJ;MACnJC,QAAQ,EAAE,sDAAsD;MAChEX,WAAW,EAAE;IACf,CAAC,CAAC;IACFrC,KAAK,CAACyC,SAAS,CAACQ,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;MAC/CC,KAAK,EAAE;QACLhD,OAAO,EAAE,MAAM;QACfiD,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFnD,KAAK,CAACyC,SAAS,CAACQ,YAAY,CAAC,QAAQ,EAAE,aAAa,EAAE;MACpD,iBAAiB,EAAE;QACjB/C,OAAO,EAAEI,EAAE,CAAC,wBAAwB,CAACmB,MAAM,EAAE,CAACE,IAAI,CAAC,CAAC;QACpDiB,UAAU,EAAE,IAAI;QAChBO,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFnD,KAAK,CAACyC,SAAS,CAACQ,YAAY,CAAC,QAAQ,EAAE,YAAY,EAAE;MACnDG,SAAS,EAAE;QACT;QACA;QACAlD,OAAO,EAAEI,EAAE,CACT,8DAA8D,CAACmB,MAAM,EACrE,CAACE,IAAI,CACP,CAAC;QACDiB,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNT,WAAW,EAAE;QACf;MACF,CAAC;MACD,iBAAiB,EAAE;QACjB;QACAnC,OAAO,EAAEI,EAAE,CACT,iFAAiF,CAC9EmB,MAAM,EACT,CAACC,WAAW,CACd,CAAC;QACDkB,UAAU,EAAE,IAAI;QAChBO,KAAK,EAAE,YAAY;QACnBL,MAAM,EAAEX;MACV,CAAC;MACD,aAAa,EAAE;QACb;QACA;QACA;QACAjC,OAAO,EAAEI,EAAE,CACT,8DAA8D,CAACmB,MAAM,EACrE,CAACS,cAAc,EAAEL,UAAU,CAC7B,CAAC;QACDiB,MAAM,EAAEX,UAAU;QAClBgB,KAAK,EAAE;MACT,CAAC;MACD,wBAAwB,EAAE;QACxB;QACAjD,OAAO,EAAEI,EAAE,CAAC,6BAA6B,CAACmB,MAAM,EAAE,CAACS,cAAc,CAAC,CAAC;QACnEU,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEX,UAAU;QAClBgB,KAAK,EAAE;MACT,CAAC;MACD;AACN;AACA;AACA;AACA;AACA;MACM,gBAAgB,EAAE;QAChB;QACAjD,OAAO,EAAEI,EAAE,CAAC,wBAAwB,CAACmB,MAAM,EAAE,CAACE,IAAI,EAAEH,OAAO,CAAC,CAAC;QAC7DsB,MAAM,EAAE;UACNO,QAAQ,EAAE/C,EAAE,CAAC,QAAQ,CAACmB,MAAM,EAAE,CAACE,IAAI,CAAC,CAAC;UACrCH,OAAO,EAAE;YACPtB,OAAO,EAAEM,MAAM,CAACgB,OAAO,CAAC;YACxB2B,KAAK,EAAE,YAAY;YACnBL,MAAM,EAAEX;UACV;QACF;MACF,CAAC;MACD,WAAW,EAAE;QACX;QACA;QACA;QACAjC,OAAO,EAAEI,EAAE,CACT,iKAAiK,CAC9JmB,MAAM,EACT,CACEL,uBAAuB,EACvBQ,WAAW,EACXD,IAAI,EACJO,cAAc,EACdb,QAAQ,CAACI,MAAM,EACfC,WAAW,EACX,iBAAiB,CAACD,MAAM,CAE5B,CAAC;QACDmB,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACN,kBAAkB,EAAE;YAClB5C,OAAO,EAAEI,EAAE,CAAC,8BAA8B,CAACmB,MAAM,EAAE,CACjDG,WAAW,EACXF,WAAW,CACZ,CAAC;YACFkB,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE,IAAI;YACZC,MAAM,EAAE9C,KAAK,CAACyC,SAAS,CAAC5C;UAC1B,CAAC;UACDuC,OAAO,EAAEf,QAAQ;UACjB,YAAY,EAAE;YACZnB,OAAO,EAAEM,MAAM,CAAC0B,cAAc,CAAC;YAC/BW,MAAM,EAAE,IAAI;YACZC,MAAM,EAAEX;UACV,CAAC;UACDE,WAAW,EAAE;QACf;MACF,CAAC;MACDiB,YAAY,EAAE;QACZpD,OAAO,EAAE,eAAe;QACxB0C,UAAU,EAAE,IAAI;QAChBO,KAAK,EAAE,UAAU;QACjBL,MAAM,EAAE;UACN;UACAS,SAAS,EAAE;YACTrD,OAAO,EACL,gGAAgG;YAClG0C,UAAU,EAAE,IAAI;YAChBO,KAAK,EAAE;UACT;QACF;MACF;IACF,CAAC,CAAC,EAAC;IACH,IAAIK,wBAAwB,GAAGjB,aAAa,GAAG,GAAG,GAAGD,SAAS;IAC9D,IAAImB,+BAA+B,GAAGxD,OAAO,CAC3C,gEAAgE,CAACwB,MAAM,EACvE,CAAC+B,wBAAwB,CAC3B,CAAC;IACD,IAAIE,eAAe,GAAGjD,MAAM,CAC1BR,OAAO,CAAC,8BAA8B,CAACwB,MAAM,EAAE,CAC7CgC,+BAA+B,CAChC,CAAC,EACF,CACF,CAAC,EAAC;IACF,IAAIE,UAAU,GACZ,uEAAuE,CACpElC,MAAM;IACX,IAAImC,IAAI,GAAG3D,OAAO,CAAC,yBAAyB,CAACwB,MAAM,EAAE,CACnDI,UAAU,EACV6B,eAAe,CAChB,CAAC;IACF1D,KAAK,CAACyC,SAAS,CAACQ,YAAY,CAAC,QAAQ,EAAE,YAAY,EAAE;MACnDY,SAAS,EAAE;QACT;QACA;QACA3D,OAAO,EAAEI,EAAE,CACT,4EAA4E,CACzEmB,MAAM,EACT,CAACkC,UAAU,EAAEC,IAAI,CACnB,CAAC;QACDhB,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNgB,MAAM,EAAE;YACN5D,OAAO,EAAEI,EAAE,CAAC,gBAAgB,CAACmB,MAAM,EAAE,CAACkC,UAAU,CAAC,CAAC;YAClDR,KAAK,EAAE;UACT,CAAC;UACD,qBAAqB,EAAE;YACrBjD,OAAO,EAAEI,EAAE,CAAC,YAAY,CAACmB,MAAM,EAAE,CAACiC,eAAe,CAAC,CAAC;YACnDZ,MAAM,EAAE9C,KAAK,CAACyC,SAAS,CAAC5C;UAC1B,CAAC;UACD,YAAY,EAAE;YACZK,OAAO,EAAEM,MAAM,CAACqB,UAAU,CAAC;YAC3BiB,MAAM,EAAE;cACNT,WAAW,EAAE;YACf;UACF,CAAC;UACDA,WAAW,EAAE;QACf;MACF;IACF,CAAC,CAAC,EAAC;IACH,IAAI0B,YAAY,GAAG,YAAY,CAACtC,MAAM,EAAC;IACvC,IAAIuC,mBAAmB,GAAGvD,MAAM,CAC9BR,OAAO,CAAC,8BAA8B,CAACwB,MAAM,EAAE,CAC7CgC,+BAA+B,CAChC,CAAC,EACF,CACF,CAAC;IACD,IAAIQ,cAAc,GAAGhE,OAAO,CAAC,oCAAoC,CAACwB,MAAM,EAAE,CACxEuC,mBAAmB,EACnBD,YAAY,CACb,CAAC,EAAC;IACH,IAAIG,mBAAmB,GAAGzD,MAAM,CAC9BR,OAAO,CACL,kEAAkE,CAC/DwB,MAAM,EACT,CAAC+B,wBAAwB,CAC3B,CAAC,EACD,CACF,CAAC;IACD,IAAIW,cAAc,GAAGlE,OAAO,CAAC,oCAAoC,CAACwB,MAAM,EAAE,CACxEyC,mBAAmB,EACnBH,YAAY,CACb,CAAC;IACF,SAASK,yBAAyBA,CAACC,aAAa,EAAEC,kBAAkB,EAAE;MACpE,OAAO;QACLD,aAAa,EAAE;UACbnE,OAAO,EAAEI,EAAE,CAAC,4BAA4B,CAACmB,MAAM,EAAE,CAAC4C,aAAa,CAAC,CAAC;UACjEzB,UAAU,EAAE,IAAI;UAChBE,MAAM,EAAE;YACN,eAAe,EAAE;cACf5C,OAAO,EAAEI,EAAE,CAAC,qCAAqC,CAACmB,MAAM,EAAE,CACxD6C,kBAAkB,EAClBP,YAAY,CACb,CAAC;cACFnB,UAAU,EAAE,IAAI;cAChBE,MAAM,EAAE;gBACNT,WAAW,EAAE;cACf;YACF,CAAC;YACDA,WAAW,EAAE,SAAS;YACtBkC,UAAU,EAAE;cACVrE,OAAO,EAAE,SAAS;cAClBiD,KAAK,EAAE,iBAAiB;cACxBL,MAAM,EAAE9C,KAAK,CAACyC,SAAS,CAAC5C;YAC1B;UACF;QACF,CAAC;QACD8C,MAAM,EAAE;MACV,CAAC;IACH;IACA3C,KAAK,CAACyC,SAAS,CAACQ,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;MAC/C,sBAAsB,EAAE,CACtB;QACE/C,OAAO,EAAEI,EAAE,CACT,2DAA2D,CAACmB,MAAM,EAClE,CAACwC,cAAc,CACjB,CAAC;QACDrB,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAEsB,yBAAyB,CAACH,cAAc,EAAED,mBAAmB;MACvE,CAAC,EACD;QACE9D,OAAO,EAAEI,EAAE,CAAC,2CAA2C,CAACmB,MAAM,EAAE,CAC9D0C,cAAc,CACf,CAAC;QACFvB,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAEsB,yBAAyB,CAACD,cAAc,EAAED,mBAAmB;MACvE,CAAC,CACF;MACDM,IAAI,EAAE;QACJtE,OAAO,EAAEM,MAAM,CAAC8B,SAAS,CAAC;QAC1BO,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IACF7C,KAAK,CAACyC,SAAS,CAACgC,MAAM,GAAGzE,KAAK,CAACyC,SAAS,CAACiC,EAAE,GAAG1E,KAAK,CAACyC,SAAS,CAAC5C,MAAM;EACtE,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}