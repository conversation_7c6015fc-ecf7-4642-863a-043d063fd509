{"ast": null, "code": "'use strict';\n\nvar refractorCsharp = require('./csharp.js');\nmodule.exports = cshtml;\ncshtml.displayName = 'cshtml';\ncshtml.aliases = ['razor'];\nfunction cshtml(Prism) {\n  Prism.register(refractorCsharp)\n  // Docs:\n  // https://docs.microsoft.com/en-us/aspnet/core/razor-pages/?view=aspnetcore-5.0&tabs=visual-studio\n  // https://docs.microsoft.com/en-us/aspnet/core/mvc/views/razor?view=aspnetcore-5.0\n  ;\n  (function (Prism) {\n    var commentLike = /\\/(?![/*])|\\/\\/.*[\\r\\n]|\\/\\*[^*]*(?:\\*(?!\\/)[^*]*)*\\*\\//.source;\n    var stringLike = /@(?!\")|\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|@\"(?:[^\\\\\"]|\"\"|\\\\[\\s\\S])*\"(?!\")/.source + '|' + /'(?:(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'|(?=[^\\\\](?!')))/.source;\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<self>/g, function () {\n          return '(?:' + pattern + ')';\n        });\n      }\n      return pattern.replace(/<self>/g, '[^\\\\s\\\\S]').replace(/<str>/g, '(?:' + stringLike + ')').replace(/<comment>/g, '(?:' + commentLike + ')');\n    }\n    var round = nested(/\\((?:[^()'\"@/]|<str>|<comment>|<self>)*\\)/.source, 2);\n    var square = nested(/\\[(?:[^\\[\\]'\"@/]|<str>|<comment>|<self>)*\\]/.source, 2);\n    var curly = nested(/\\{(?:[^{}'\"@/]|<str>|<comment>|<self>)*\\}/.source, 2);\n    var angle = nested(/<(?:[^<>'\"@/]|<str>|<comment>|<self>)*>/.source, 2); // Note about the above bracket patterns:\n    // They all ignore HTML expressions that might be in the C# code. This is a problem because HTML (like strings and\n    // comments) is parsed differently. This is a huge problem because HTML might contain brackets and quotes which\n    // messes up the bracket and string counting implemented by the above patterns.\n    //\n    // This problem is not fixable because 1) HTML expression are highly context sensitive and very difficult to detect\n    // and 2) they require one capturing group at every nested level. See the `tagRegion` pattern to admire the\n    // complexity of an HTML expression.\n    //\n    // To somewhat alleviate the problem a bit, the patterns for characters (e.g. 'a') is very permissive, it also\n    // allows invalid characters to support HTML expressions like this: <p>That's it!</p>.\n    var tagAttrs = /(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?/.source;\n    var tagContent = /(?!\\d)[^\\s>\\/=$<%]+/.source + tagAttrs + /\\s*\\/?>/.source;\n    var tagRegion = /\\B@?/.source + '(?:' + /<([a-zA-Z][\\w:]*)/.source + tagAttrs + /\\s*>/.source + '(?:' + (/[^<]/.source + '|' +\n    // all tags that are not the start tag\n    // eslint-disable-next-line regexp/strict\n    /<\\/?(?!\\1\\b)/.source + tagContent + '|' +\n    // nested start tag\n    nested(\n    // eslint-disable-next-line regexp/strict\n    /<\\1/.source + tagAttrs + /\\s*>/.source + '(?:' + (/[^<]/.source + '|' +\n    // all tags that are not the start tag\n    // eslint-disable-next-line regexp/strict\n    /<\\/?(?!\\1\\b)/.source + tagContent + '|' + '<self>') + ')*' +\n    // eslint-disable-next-line regexp/strict\n    /<\\/\\1\\s*>/.source, 2)) + ')*' +\n    // eslint-disable-next-line regexp/strict\n    /<\\/\\1\\s*>/.source + '|' + /</.source + tagContent + ')'; // Now for the actual language definition(s):\n    //\n    // Razor as a language has 2 parts:\n    //  1) CSHTML: A markup-like language that has been extended with inline C# code expressions and blocks.\n    //  2) C#+HTML: A variant of C# that can contain CSHTML tags as expressions.\n    //\n    // In the below code, both CSHTML and C#+HTML will be create as separate language definitions that reference each\n    // other. However, only CSHTML will be exported via `Prism.languages`.\n    Prism.languages.cshtml = Prism.languages.extend('markup', {});\n    var csharpWithHtml = Prism.languages.insertBefore('csharp', 'string', {\n      html: {\n        pattern: RegExp(tagRegion),\n        greedy: true,\n        inside: Prism.languages.cshtml\n      }\n    }, {\n      csharp: Prism.languages.extend('csharp', {})\n    });\n    var cs = {\n      pattern: /\\S[\\s\\S]*/,\n      alias: 'language-csharp',\n      inside: csharpWithHtml\n    };\n    Prism.languages.insertBefore('cshtml', 'prolog', {\n      'razor-comment': {\n        pattern: /@\\*[\\s\\S]*?\\*@/,\n        greedy: true,\n        alias: 'comment'\n      },\n      block: {\n        pattern: RegExp(/(^|[^@])@/.source + '(?:' + [\n        // @{ ... }\n        curly,\n        // @code{ ... }\n        /(?:code|functions)\\s*/.source + curly,\n        // @for (...) { ... }\n        /(?:for|foreach|lock|switch|using|while)\\s*/.source + round + /\\s*/.source + curly,\n        // @do { ... } while (...);\n        /do\\s*/.source + curly + /\\s*while\\s*/.source + round + /(?:\\s*;)?/.source,\n        // @try { ... } catch (...) { ... } finally { ... }\n        /try\\s*/.source + curly + /\\s*catch\\s*/.source + round + /\\s*/.source + curly + /\\s*finally\\s*/.source + curly,\n        // @if (...) {...} else if (...) {...} else {...}\n        /if\\s*/.source + round + /\\s*/.source + curly + '(?:' + /\\s*else/.source + '(?:' + /\\s+if\\s*/.source + round + ')?' + /\\s*/.source + curly + ')*'].join('|') + ')'),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^@\\w*/,\n          csharp: cs\n        }\n      },\n      directive: {\n        pattern: /^([ \\t]*)@(?:addTagHelper|attribute|implements|inherits|inject|layout|model|namespace|page|preservewhitespace|removeTagHelper|section|tagHelperPrefix|using)(?=\\s).*/m,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^@\\w+/,\n          csharp: cs\n        }\n      },\n      value: {\n        pattern: RegExp(/(^|[^@])@/.source + /(?:await\\b\\s*)?/.source + '(?:' + /\\w+\\b/.source + '|' + round + ')' + '(?:' + /[?!]?\\.\\w+\\b/.source + '|' + round + '|' + square + '|' + angle + round + ')*'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'variable',\n        inside: {\n          keyword: /^@/,\n          csharp: cs\n        }\n      },\n      'delegate-operator': {\n        pattern: /(^|[^@])@(?=<)/,\n        lookbehind: true,\n        alias: 'operator'\n      }\n    });\n    Prism.languages.razor = Prism.languages.cshtml;\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorCsharp", "require", "module", "exports", "cshtml", "displayName", "aliases", "Prism", "register", "commentLike", "source", "stringLike", "nested", "pattern", "depthLog2", "i", "replace", "round", "square", "curly", "angle", "tagAttrs", "tagContent", "tagRegion", "languages", "extend", "csharpWithHtml", "insertBefore", "html", "RegExp", "greedy", "inside", "csharp", "cs", "alias", "block", "join", "lookbehind", "keyword", "directive", "value", "razor"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/cshtml.js"], "sourcesContent": ["'use strict'\nvar refractorCsharp = require('./csharp.js')\nmodule.exports = cshtml\ncshtml.displayName = 'cshtml'\ncshtml.aliases = ['razor']\nfunction cshtml(Prism) {\n  Prism.register(refractorCsharp)\n  // Docs:\n  // https://docs.microsoft.com/en-us/aspnet/core/razor-pages/?view=aspnetcore-5.0&tabs=visual-studio\n  // https://docs.microsoft.com/en-us/aspnet/core/mvc/views/razor?view=aspnetcore-5.0\n  ;(function (Prism) {\n    var commentLike = /\\/(?![/*])|\\/\\/.*[\\r\\n]|\\/\\*[^*]*(?:\\*(?!\\/)[^*]*)*\\*\\//\n      .source\n    var stringLike =\n      /@(?!\")|\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|@\"(?:[^\\\\\"]|\"\"|\\\\[\\s\\S])*\"(?!\")/.source +\n      '|' +\n      /'(?:(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'|(?=[^\\\\](?!')))/.source\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<self>/g, function () {\n          return '(?:' + pattern + ')'\n        })\n      }\n      return pattern\n        .replace(/<self>/g, '[^\\\\s\\\\S]')\n        .replace(/<str>/g, '(?:' + stringLike + ')')\n        .replace(/<comment>/g, '(?:' + commentLike + ')')\n    }\n    var round = nested(/\\((?:[^()'\"@/]|<str>|<comment>|<self>)*\\)/.source, 2)\n    var square = nested(/\\[(?:[^\\[\\]'\"@/]|<str>|<comment>|<self>)*\\]/.source, 2)\n    var curly = nested(/\\{(?:[^{}'\"@/]|<str>|<comment>|<self>)*\\}/.source, 2)\n    var angle = nested(/<(?:[^<>'\"@/]|<str>|<comment>|<self>)*>/.source, 2) // Note about the above bracket patterns:\n    // They all ignore HTML expressions that might be in the C# code. This is a problem because HTML (like strings and\n    // comments) is parsed differently. This is a huge problem because HTML might contain brackets and quotes which\n    // messes up the bracket and string counting implemented by the above patterns.\n    //\n    // This problem is not fixable because 1) HTML expression are highly context sensitive and very difficult to detect\n    // and 2) they require one capturing group at every nested level. See the `tagRegion` pattern to admire the\n    // complexity of an HTML expression.\n    //\n    // To somewhat alleviate the problem a bit, the patterns for characters (e.g. 'a') is very permissive, it also\n    // allows invalid characters to support HTML expressions like this: <p>That's it!</p>.\n    var tagAttrs =\n      /(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?/\n        .source\n    var tagContent = /(?!\\d)[^\\s>\\/=$<%]+/.source + tagAttrs + /\\s*\\/?>/.source\n    var tagRegion =\n      /\\B@?/.source +\n      '(?:' +\n      /<([a-zA-Z][\\w:]*)/.source +\n      tagAttrs +\n      /\\s*>/.source +\n      '(?:' +\n      (/[^<]/.source +\n        '|' + // all tags that are not the start tag\n        // eslint-disable-next-line regexp/strict\n        /<\\/?(?!\\1\\b)/.source +\n        tagContent +\n        '|' + // nested start tag\n        nested(\n          // eslint-disable-next-line regexp/strict\n          /<\\1/.source +\n            tagAttrs +\n            /\\s*>/.source +\n            '(?:' +\n            (/[^<]/.source +\n              '|' + // all tags that are not the start tag\n              // eslint-disable-next-line regexp/strict\n              /<\\/?(?!\\1\\b)/.source +\n              tagContent +\n              '|' +\n              '<self>') +\n            ')*' + // eslint-disable-next-line regexp/strict\n            /<\\/\\1\\s*>/.source,\n          2\n        )) +\n      ')*' + // eslint-disable-next-line regexp/strict\n      /<\\/\\1\\s*>/.source +\n      '|' +\n      /</.source +\n      tagContent +\n      ')' // Now for the actual language definition(s):\n    //\n    // Razor as a language has 2 parts:\n    //  1) CSHTML: A markup-like language that has been extended with inline C# code expressions and blocks.\n    //  2) C#+HTML: A variant of C# that can contain CSHTML tags as expressions.\n    //\n    // In the below code, both CSHTML and C#+HTML will be create as separate language definitions that reference each\n    // other. However, only CSHTML will be exported via `Prism.languages`.\n    Prism.languages.cshtml = Prism.languages.extend('markup', {})\n    var csharpWithHtml = Prism.languages.insertBefore(\n      'csharp',\n      'string',\n      {\n        html: {\n          pattern: RegExp(tagRegion),\n          greedy: true,\n          inside: Prism.languages.cshtml\n        }\n      },\n      {\n        csharp: Prism.languages.extend('csharp', {})\n      }\n    )\n    var cs = {\n      pattern: /\\S[\\s\\S]*/,\n      alias: 'language-csharp',\n      inside: csharpWithHtml\n    }\n    Prism.languages.insertBefore('cshtml', 'prolog', {\n      'razor-comment': {\n        pattern: /@\\*[\\s\\S]*?\\*@/,\n        greedy: true,\n        alias: 'comment'\n      },\n      block: {\n        pattern: RegExp(\n          /(^|[^@])@/.source +\n            '(?:' +\n            [\n              // @{ ... }\n              curly, // @code{ ... }\n              /(?:code|functions)\\s*/.source + curly, // @for (...) { ... }\n              /(?:for|foreach|lock|switch|using|while)\\s*/.source +\n                round +\n                /\\s*/.source +\n                curly, // @do { ... } while (...);\n              /do\\s*/.source +\n                curly +\n                /\\s*while\\s*/.source +\n                round +\n                /(?:\\s*;)?/.source, // @try { ... } catch (...) { ... } finally { ... }\n              /try\\s*/.source +\n                curly +\n                /\\s*catch\\s*/.source +\n                round +\n                /\\s*/.source +\n                curly +\n                /\\s*finally\\s*/.source +\n                curly, // @if (...) {...} else if (...) {...} else {...}\n              /if\\s*/.source +\n                round +\n                /\\s*/.source +\n                curly +\n                '(?:' +\n                /\\s*else/.source +\n                '(?:' +\n                /\\s+if\\s*/.source +\n                round +\n                ')?' +\n                /\\s*/.source +\n                curly +\n                ')*'\n            ].join('|') +\n            ')'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^@\\w*/,\n          csharp: cs\n        }\n      },\n      directive: {\n        pattern:\n          /^([ \\t]*)@(?:addTagHelper|attribute|implements|inherits|inject|layout|model|namespace|page|preservewhitespace|removeTagHelper|section|tagHelperPrefix|using)(?=\\s).*/m,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^@\\w+/,\n          csharp: cs\n        }\n      },\n      value: {\n        pattern: RegExp(\n          /(^|[^@])@/.source +\n            /(?:await\\b\\s*)?/.source +\n            '(?:' +\n            /\\w+\\b/.source +\n            '|' +\n            round +\n            ')' +\n            '(?:' +\n            /[?!]?\\.\\w+\\b/.source +\n            '|' +\n            round +\n            '|' +\n            square +\n            '|' +\n            angle +\n            round +\n            ')*'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'variable',\n        inside: {\n          keyword: /^@/,\n          csharp: cs\n        }\n      },\n      'delegate-operator': {\n        pattern: /(^|[^@])@(?=<)/,\n        lookbehind: true,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.razor = Prism.languages.cshtml\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,aAAa,CAAC;AAC5CC,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,OAAO,CAAC;AAC1B,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACR,eAAe;EAC9B;EACA;EACA;EAAA;EACC,CAAC,UAAUO,KAAK,EAAE;IACjB,IAAIE,WAAW,GAAG,yDAAyD,CACxEC,MAAM;IACT,IAAIC,UAAU,GACZ,8DAA8D,CAACD,MAAM,GACrE,GAAG,GACH,gEAAgE,CAACA,MAAM;IACzE;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASE,MAAMA,CAACC,OAAO,EAAEC,SAAS,EAAE;MAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClCF,OAAO,GAAGA,OAAO,CAACG,OAAO,CAAC,SAAS,EAAE,YAAY;UAC/C,OAAO,KAAK,GAAGH,OAAO,GAAG,GAAG;QAC9B,CAAC,CAAC;MACJ;MACA,OAAOA,OAAO,CACXG,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAC/BA,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAGL,UAAU,GAAG,GAAG,CAAC,CAC3CK,OAAO,CAAC,YAAY,EAAE,KAAK,GAAGP,WAAW,GAAG,GAAG,CAAC;IACrD;IACA,IAAIQ,KAAK,GAAGL,MAAM,CAAC,2CAA2C,CAACF,MAAM,EAAE,CAAC,CAAC;IACzE,IAAIQ,MAAM,GAAGN,MAAM,CAAC,6CAA6C,CAACF,MAAM,EAAE,CAAC,CAAC;IAC5E,IAAIS,KAAK,GAAGP,MAAM,CAAC,2CAA2C,CAACF,MAAM,EAAE,CAAC,CAAC;IACzE,IAAIU,KAAK,GAAGR,MAAM,CAAC,yCAAyC,CAACF,MAAM,EAAE,CAAC,CAAC,EAAC;IACxE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIW,QAAQ,GACV,wFAAwF,CACrFX,MAAM;IACX,IAAIY,UAAU,GAAG,qBAAqB,CAACZ,MAAM,GAAGW,QAAQ,GAAG,SAAS,CAACX,MAAM;IAC3E,IAAIa,SAAS,GACX,MAAM,CAACb,MAAM,GACb,KAAK,GACL,mBAAmB,CAACA,MAAM,GAC1BW,QAAQ,GACR,MAAM,CAACX,MAAM,GACb,KAAK,IACJ,MAAM,CAACA,MAAM,GACZ,GAAG;IAAG;IACN;IACA,cAAc,CAACA,MAAM,GACrBY,UAAU,GACV,GAAG;IAAG;IACNV,MAAM;IACJ;IACA,KAAK,CAACF,MAAM,GACVW,QAAQ,GACR,MAAM,CAACX,MAAM,GACb,KAAK,IACJ,MAAM,CAACA,MAAM,GACZ,GAAG;IAAG;IACN;IACA,cAAc,CAACA,MAAM,GACrBY,UAAU,GACV,GAAG,GACH,QAAQ,CAAC,GACX,IAAI;IAAG;IACP,WAAW,CAACZ,MAAM,EACpB,CACF,CAAC,CAAC,GACJ,IAAI;IAAG;IACP,WAAW,CAACA,MAAM,GAClB,GAAG,GACH,GAAG,CAACA,MAAM,GACVY,UAAU,GACV,GAAG,EAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACAf,KAAK,CAACiB,SAAS,CAACpB,MAAM,GAAGG,KAAK,CAACiB,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC7D,IAAIC,cAAc,GAAGnB,KAAK,CAACiB,SAAS,CAACG,YAAY,CAC/C,QAAQ,EACR,QAAQ,EACR;MACEC,IAAI,EAAE;QACJf,OAAO,EAAEgB,MAAM,CAACN,SAAS,CAAC;QAC1BO,MAAM,EAAE,IAAI;QACZC,MAAM,EAAExB,KAAK,CAACiB,SAAS,CAACpB;MAC1B;IACF,CAAC,EACD;MACE4B,MAAM,EAAEzB,KAAK,CAACiB,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7C,CACF,CAAC;IACD,IAAIQ,EAAE,GAAG;MACPpB,OAAO,EAAE,WAAW;MACpBqB,KAAK,EAAE,iBAAiB;MACxBH,MAAM,EAAEL;IACV,CAAC;IACDnB,KAAK,CAACiB,SAAS,CAACG,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;MAC/C,eAAe,EAAE;QACfd,OAAO,EAAE,gBAAgB;QACzBiB,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE;MACT,CAAC;MACDC,KAAK,EAAE;QACLtB,OAAO,EAAEgB,MAAM,CACb,WAAW,CAACnB,MAAM,GAChB,KAAK,GACL;QACE;QACAS,KAAK;QAAE;QACP,uBAAuB,CAACT,MAAM,GAAGS,KAAK;QAAE;QACxC,4CAA4C,CAACT,MAAM,GACjDO,KAAK,GACL,KAAK,CAACP,MAAM,GACZS,KAAK;QAAE;QACT,OAAO,CAACT,MAAM,GACZS,KAAK,GACL,aAAa,CAACT,MAAM,GACpBO,KAAK,GACL,WAAW,CAACP,MAAM;QAAE;QACtB,QAAQ,CAACA,MAAM,GACbS,KAAK,GACL,aAAa,CAACT,MAAM,GACpBO,KAAK,GACL,KAAK,CAACP,MAAM,GACZS,KAAK,GACL,eAAe,CAACT,MAAM,GACtBS,KAAK;QAAE;QACT,OAAO,CAACT,MAAM,GACZO,KAAK,GACL,KAAK,CAACP,MAAM,GACZS,KAAK,GACL,KAAK,GACL,SAAS,CAACT,MAAM,GAChB,KAAK,GACL,UAAU,CAACA,MAAM,GACjBO,KAAK,GACL,IAAI,GACJ,KAAK,CAACP,MAAM,GACZS,KAAK,GACL,IAAI,CACP,CAACiB,IAAI,CAAC,GAAG,CAAC,GACX,GACJ,CAAC;QACDC,UAAU,EAAE,IAAI;QAChBP,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNO,OAAO,EAAE,OAAO;UAChBN,MAAM,EAAEC;QACV;MACF,CAAC;MACDM,SAAS,EAAE;QACT1B,OAAO,EACL,uKAAuK;QACzKwB,UAAU,EAAE,IAAI;QAChBP,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNO,OAAO,EAAE,OAAO;UAChBN,MAAM,EAAEC;QACV;MACF,CAAC;MACDO,KAAK,EAAE;QACL3B,OAAO,EAAEgB,MAAM,CACb,WAAW,CAACnB,MAAM,GAChB,iBAAiB,CAACA,MAAM,GACxB,KAAK,GACL,OAAO,CAACA,MAAM,GACd,GAAG,GACHO,KAAK,GACL,GAAG,GACH,KAAK,GACL,cAAc,CAACP,MAAM,GACrB,GAAG,GACHO,KAAK,GACL,GAAG,GACHC,MAAM,GACN,GAAG,GACHE,KAAK,GACLH,KAAK,GACL,IACJ,CAAC;QACDoB,UAAU,EAAE,IAAI;QAChBP,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE,UAAU;QACjBH,MAAM,EAAE;UACNO,OAAO,EAAE,IAAI;UACbN,MAAM,EAAEC;QACV;MACF,CAAC;MACD,mBAAmB,EAAE;QACnBpB,OAAO,EAAE,gBAAgB;QACzBwB,UAAU,EAAE,IAAI;QAChBH,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACF3B,KAAK,CAACiB,SAAS,CAACiB,KAAK,GAAGlC,KAAK,CAACiB,SAAS,CAACpB,MAAM;EAChD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}