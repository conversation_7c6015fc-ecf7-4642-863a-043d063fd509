{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = liquid;\nliquid.displayName = 'liquid';\nliquid.aliases = [];\nfunction liquid(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  Prism.languages.liquid = {\n    comment: {\n      pattern: /(^\\{%\\s*comment\\s*%\\})[\\s\\S]+(?=\\{%\\s*endcomment\\s*%\\}$)/,\n      lookbehind: true\n    },\n    delimiter: {\n      pattern: /^\\{(?:\\{\\{|[%\\{])-?|-?(?:\\}\\}|[%\\}])\\}$/,\n      alias: 'punctuation'\n    },\n    string: {\n      pattern: /\"[^\"]*\"|'[^']*'/,\n      greedy: true\n    },\n    keyword: /\\b(?:as|assign|break|(?:end)?(?:capture|case|comment|for|form|if|paginate|raw|style|tablerow|unless)|continue|cycle|decrement|echo|else|elsif|in|include|increment|limit|liquid|offset|range|render|reversed|section|when|with)\\b/,\n    object: /\\b(?:address|all_country_option_tags|article|block|blog|cart|checkout|collection|color|country|country_option_tags|currency|current_page|current_tags|customer|customer_address|date|discount_allocation|discount_application|external_video|filter|filter_value|font|forloop|fulfillment|generic_file|gift_card|group|handle|image|line_item|link|linklist|localization|location|measurement|media|metafield|model|model_source|order|page|page_description|page_image|page_title|part|policy|product|product_option|recommendations|request|robots|routes|rule|script|search|selling_plan|selling_plan_allocation|selling_plan_group|shipping_method|shop|shop_locale|sitemap|store_availability|tax_line|template|theme|transaction|unit_price_measurement|user_agent|variant|video|video_source)\\b/,\n    function: [{\n      pattern: /(\\|\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'filter'\n    }, {\n      // array functions\n      pattern: /(\\.\\s*)(?:first|last|size)/,\n      lookbehind: true\n    }],\n    boolean: /\\b(?:false|nil|true)\\b/,\n    range: {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    },\n    // https://github.com/Shopify/liquid/blob/698f5e0d967423e013f6169d9111bd969bd78337/lib/liquid/lexer.rb#L21\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    operator: /[!=]=|<>|[<>]=?|[|?:=-]|\\b(?:and|contains(?=\\s)|or)\\b/,\n    punctuation: /[.,\\[\\]()]/,\n    empty: {\n      pattern: /\\bempty\\b/,\n      alias: 'keyword'\n    }\n  };\n  Prism.hooks.add('before-tokenize', function (env) {\n    var liquidPattern = /\\{%\\s*comment\\s*%\\}[\\s\\S]*?\\{%\\s*endcomment\\s*%\\}|\\{(?:%[\\s\\S]*?%|\\{\\{[\\s\\S]*?\\}\\}|\\{[\\s\\S]*?\\})\\}/g;\n    var insideRaw = false;\n    Prism.languages['markup-templating'].buildPlaceholders(env, 'liquid', liquidPattern, function (match) {\n      var tagMatch = /^\\{%-?\\s*(\\w+)/.exec(match);\n      if (tagMatch) {\n        var tag = tagMatch[1];\n        if (tag === 'raw' && !insideRaw) {\n          insideRaw = true;\n          return true;\n        } else if (tag === 'endraw') {\n          insideRaw = false;\n          return true;\n        }\n      }\n      return !insideRaw;\n    });\n  });\n  Prism.hooks.add('after-tokenize', function (env) {\n    Prism.languages['markup-templating'].tokenizePlaceholders(env, 'liquid');\n  });\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "liquid", "displayName", "aliases", "Prism", "register", "languages", "comment", "pattern", "lookbehind", "delimiter", "alias", "string", "greedy", "keyword", "object", "function", "boolean", "range", "number", "operator", "punctuation", "empty", "hooks", "add", "env", "liquidPattern", "insideRaw", "buildPlaceholders", "match", "tagMatch", "exec", "tag", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/liquid.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = liquid\nliquid.displayName = 'liquid'\nliquid.aliases = []\nfunction liquid(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  Prism.languages.liquid = {\n    comment: {\n      pattern: /(^\\{%\\s*comment\\s*%\\})[\\s\\S]+(?=\\{%\\s*endcomment\\s*%\\}$)/,\n      lookbehind: true\n    },\n    delimiter: {\n      pattern: /^\\{(?:\\{\\{|[%\\{])-?|-?(?:\\}\\}|[%\\}])\\}$/,\n      alias: 'punctuation'\n    },\n    string: {\n      pattern: /\"[^\"]*\"|'[^']*'/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:as|assign|break|(?:end)?(?:capture|case|comment|for|form|if|paginate|raw|style|tablerow|unless)|continue|cycle|decrement|echo|else|elsif|in|include|increment|limit|liquid|offset|range|render|reversed|section|when|with)\\b/,\n    object:\n      /\\b(?:address|all_country_option_tags|article|block|blog|cart|checkout|collection|color|country|country_option_tags|currency|current_page|current_tags|customer|customer_address|date|discount_allocation|discount_application|external_video|filter|filter_value|font|forloop|fulfillment|generic_file|gift_card|group|handle|image|line_item|link|linklist|localization|location|measurement|media|metafield|model|model_source|order|page|page_description|page_image|page_title|part|policy|product|product_option|recommendations|request|robots|routes|rule|script|search|selling_plan|selling_plan_allocation|selling_plan_group|shipping_method|shop|shop_locale|sitemap|store_availability|tax_line|template|theme|transaction|unit_price_measurement|user_agent|variant|video|video_source)\\b/,\n    function: [\n      {\n        pattern: /(\\|\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'filter'\n      },\n      {\n        // array functions\n        pattern: /(\\.\\s*)(?:first|last|size)/,\n        lookbehind: true\n      }\n    ],\n    boolean: /\\b(?:false|nil|true)\\b/,\n    range: {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    },\n    // https://github.com/Shopify/liquid/blob/698f5e0d967423e013f6169d9111bd969bd78337/lib/liquid/lexer.rb#L21\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    operator: /[!=]=|<>|[<>]=?|[|?:=-]|\\b(?:and|contains(?=\\s)|or)\\b/,\n    punctuation: /[.,\\[\\]()]/,\n    empty: {\n      pattern: /\\bempty\\b/,\n      alias: 'keyword'\n    }\n  }\n  Prism.hooks.add('before-tokenize', function (env) {\n    var liquidPattern =\n      /\\{%\\s*comment\\s*%\\}[\\s\\S]*?\\{%\\s*endcomment\\s*%\\}|\\{(?:%[\\s\\S]*?%|\\{\\{[\\s\\S]*?\\}\\}|\\{[\\s\\S]*?\\})\\}/g\n    var insideRaw = false\n    Prism.languages['markup-templating'].buildPlaceholders(\n      env,\n      'liquid',\n      liquidPattern,\n      function (match) {\n        var tagMatch = /^\\{%-?\\s*(\\w+)/.exec(match)\n        if (tagMatch) {\n          var tag = tagMatch[1]\n          if (tag === 'raw' && !insideRaw) {\n            insideRaw = true\n            return true\n          } else if (tag === 'endraw') {\n            insideRaw = false\n            return true\n          }\n        }\n        return !insideRaw\n      }\n    )\n  })\n  Prism.hooks.add('after-tokenize', function (env) {\n    Prism.languages['markup-templating'].tokenizePlaceholders(env, 'liquid')\n  })\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACzCO,KAAK,CAACE,SAAS,CAACL,MAAM,GAAG;IACvBM,OAAO,EAAE;MACPC,OAAO,EAAE,0DAA0D;MACnEC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE;MACTF,OAAO,EAAE,yCAAyC;MAClDG,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNJ,OAAO,EAAE,iBAAiB;MAC1BK,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EACL,mOAAmO;IACrOC,MAAM,EACJ,wwBAAwwB;IAC1wBC,QAAQ,EAAE,CACR;MACER,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE,IAAI;MAChBE,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAH,OAAO,EAAE,4BAA4B;MACrCC,UAAU,EAAE;IACd,CAAC,CACF;IACDQ,OAAO,EAAE,wBAAwB;IACjCC,KAAK,EAAE;MACLV,OAAO,EAAE,MAAM;MACfG,KAAK,EAAE;IACT,CAAC;IACD;IACAQ,MAAM,EAAE,mBAAmB;IAC3BC,QAAQ,EAAE,uDAAuD;IACjEC,WAAW,EAAE,YAAY;IACzBC,KAAK,EAAE;MACLd,OAAO,EAAE,WAAW;MACpBG,KAAK,EAAE;IACT;EACF,CAAC;EACDP,KAAK,CAACmB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;IAChD,IAAIC,aAAa,GACf,qGAAqG;IACvG,IAAIC,SAAS,GAAG,KAAK;IACrBvB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACsB,iBAAiB,CACpDH,GAAG,EACH,QAAQ,EACRC,aAAa,EACb,UAAUG,KAAK,EAAE;MACf,IAAIC,QAAQ,GAAG,gBAAgB,CAACC,IAAI,CAACF,KAAK,CAAC;MAC3C,IAAIC,QAAQ,EAAE;QACZ,IAAIE,GAAG,GAAGF,QAAQ,CAAC,CAAC,CAAC;QACrB,IAAIE,GAAG,KAAK,KAAK,IAAI,CAACL,SAAS,EAAE;UAC/BA,SAAS,GAAG,IAAI;UAChB,OAAO,IAAI;QACb,CAAC,MAAM,IAAIK,GAAG,KAAK,QAAQ,EAAE;UAC3BL,SAAS,GAAG,KAAK;UACjB,OAAO,IAAI;QACb;MACF;MACA,OAAO,CAACA,SAAS;IACnB,CACF,CAAC;EACH,CAAC,CAAC;EACFvB,KAAK,CAACmB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;IAC/CrB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAAC2B,oBAAoB,CAACR,GAAG,EAAE,QAAQ,CAAC;EAC1E,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}