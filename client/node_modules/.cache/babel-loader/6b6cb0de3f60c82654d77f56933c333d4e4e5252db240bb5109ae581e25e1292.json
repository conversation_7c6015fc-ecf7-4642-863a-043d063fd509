{"ast": null, "code": "'use strict';\n\nmodule.exports = smali;\nsmali.displayName = 'smali';\nsmali.aliases = [];\nfunction smali(Prism) {\n  // Test files for the parser itself:\n  // https://github.com/JesusFreke/smali/tree/master/smali/src/test/resources/LexerTest\n  Prism.languages.smali = {\n    comment: /#.*/,\n    string: {\n      pattern: /\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|'(?:[^\\r\\n\\\\']|\\\\(?:.|u[\\da-fA-F]{4}))'/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(^|[^L])L(?:(?:\\w+|`[^`\\r\\n]*`)\\/)*(?:[\\w$]+|`[^`\\r\\n]*`)(?=\\s*;)/,\n      lookbehind: true,\n      inside: {\n        'class-name': {\n          pattern: /(^L|\\/)(?:[\\w$]+|`[^`\\r\\n]*`)$/,\n          lookbehind: true\n        },\n        namespace: {\n          pattern: /^(L)(?:(?:\\w+|`[^`\\r\\n]*`)\\/)+/,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\//\n          }\n        },\n        builtin: /^L/\n      }\n    },\n    builtin: [{\n      // Reference: https://github.com/JesusFreke/smali/wiki/TypesMethodsAndFields#types\n      pattern: /([();\\[])[BCDFIJSVZ]+/,\n      lookbehind: true\n    }, {\n      // e.g. .field mWifiOnUid:I\n      pattern: /([\\w$>]:)[BCDFIJSVZ]/,\n      lookbehind: true\n    }],\n    keyword: [{\n      pattern: /(\\.end\\s+)[\\w-]+/,\n      lookbehind: true\n    }, {\n      pattern: /(^|[^\\w.-])\\.(?!\\d)[\\w-]+/,\n      lookbehind: true\n    }, {\n      pattern: /(^|[^\\w.-])(?:abstract|annotation|bridge|constructor|enum|final|interface|private|protected|public|runtime|static|synthetic|system|transient)(?![\\w.-])/,\n      lookbehind: true\n    }],\n    function: {\n      pattern: /(^|[^\\w.-])(?:\\w+|<[\\w$-]+>)(?=\\()/,\n      lookbehind: true\n    },\n    field: {\n      pattern: /[\\w$]+(?=:)/,\n      alias: 'variable'\n    },\n    register: {\n      pattern: /(^|[^\\w.-])[vp]\\d(?![\\w.-])/,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    boolean: {\n      pattern: /(^|[^\\w.-])(?:false|true)(?![\\w.-])/,\n      lookbehind: true\n    },\n    number: {\n      pattern: /(^|[^/\\w.-])-?(?:NAN|INFINITY|0x(?:[\\dA-F]+(?:\\.[\\dA-F]*)?|\\.[\\dA-F]+)(?:p[+-]?[\\dA-F]+)?|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?)[dflst]?(?![\\w.-])/i,\n      lookbehind: true\n    },\n    label: {\n      pattern: /(:)\\w+/,\n      lookbehind: true,\n      alias: 'property'\n    },\n    operator: /->|\\.\\.|[\\[=]/,\n    punctuation: /[{}(),;:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "smali", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "lookbehind", "inside", "namespace", "punctuation", "builtin", "keyword", "function", "field", "alias", "register", "boolean", "number", "label", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/smali.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = smali\nsmali.displayName = 'smali'\nsmali.aliases = []\nfunction smali(Prism) {\n  // Test files for the parser itself:\n  // https://github.com/JesusFreke/smali/tree/master/smali/src/test/resources/LexerTest\n  Prism.languages.smali = {\n    comment: /#.*/,\n    string: {\n      pattern: /\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|'(?:[^\\r\\n\\\\']|\\\\(?:.|u[\\da-fA-F]{4}))'/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(^|[^L])L(?:(?:\\w+|`[^`\\r\\n]*`)\\/)*(?:[\\w$]+|`[^`\\r\\n]*`)(?=\\s*;)/,\n      lookbehind: true,\n      inside: {\n        'class-name': {\n          pattern: /(^L|\\/)(?:[\\w$]+|`[^`\\r\\n]*`)$/,\n          lookbehind: true\n        },\n        namespace: {\n          pattern: /^(L)(?:(?:\\w+|`[^`\\r\\n]*`)\\/)+/,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\//\n          }\n        },\n        builtin: /^L/\n      }\n    },\n    builtin: [\n      {\n        // Reference: https://github.com/JesusFreke/smali/wiki/TypesMethodsAndFields#types\n        pattern: /([();\\[])[BCDFIJSVZ]+/,\n        lookbehind: true\n      },\n      {\n        // e.g. .field mWifiOnUid:I\n        pattern: /([\\w$>]:)[BCDFIJSVZ]/,\n        lookbehind: true\n      }\n    ],\n    keyword: [\n      {\n        pattern: /(\\.end\\s+)[\\w-]+/,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\w.-])\\.(?!\\d)[\\w-]+/,\n        lookbehind: true\n      },\n      {\n        pattern:\n          /(^|[^\\w.-])(?:abstract|annotation|bridge|constructor|enum|final|interface|private|protected|public|runtime|static|synthetic|system|transient)(?![\\w.-])/,\n        lookbehind: true\n      }\n    ],\n    function: {\n      pattern: /(^|[^\\w.-])(?:\\w+|<[\\w$-]+>)(?=\\()/,\n      lookbehind: true\n    },\n    field: {\n      pattern: /[\\w$]+(?=:)/,\n      alias: 'variable'\n    },\n    register: {\n      pattern: /(^|[^\\w.-])[vp]\\d(?![\\w.-])/,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    boolean: {\n      pattern: /(^|[^\\w.-])(?:false|true)(?![\\w.-])/,\n      lookbehind: true\n    },\n    number: {\n      pattern:\n        /(^|[^/\\w.-])-?(?:NAN|INFINITY|0x(?:[\\dA-F]+(?:\\.[\\dA-F]*)?|\\.[\\dA-F]+)(?:p[+-]?[\\dA-F]+)?|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?)[dflst]?(?![\\w.-])/i,\n      lookbehind: true\n    },\n    label: {\n      pattern: /(:)\\w+/,\n      lookbehind: true,\n      alias: 'property'\n    },\n    operator: /->|\\.\\.|[\\[=]/,\n    punctuation: /[{}(),;:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EACA;EACAA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;MACNC,OAAO,EAAE,+DAA+D;MACxEC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZD,OAAO,EACL,mEAAmE;MACrEE,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACN,YAAY,EAAE;UACZH,OAAO,EAAE,gCAAgC;UACzCE,UAAU,EAAE;QACd,CAAC;QACDE,SAAS,EAAE;UACTJ,OAAO,EAAE,gCAAgC;UACzCE,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNE,WAAW,EAAE;UACf;QACF,CAAC;QACDC,OAAO,EAAE;MACX;IACF,CAAC;IACDA,OAAO,EAAE,CACP;MACE;MACAN,OAAO,EAAE,uBAAuB;MAChCE,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAF,OAAO,EAAE,sBAAsB;MAC/BE,UAAU,EAAE;IACd,CAAC,CACF;IACDK,OAAO,EAAE,CACP;MACEP,OAAO,EAAE,kBAAkB;MAC3BE,UAAU,EAAE;IACd,CAAC,EACD;MACEF,OAAO,EAAE,2BAA2B;MACpCE,UAAU,EAAE;IACd,CAAC,EACD;MACEF,OAAO,EACL,yJAAyJ;MAC3JE,UAAU,EAAE;IACd,CAAC,CACF;IACDM,QAAQ,EAAE;MACRR,OAAO,EAAE,oCAAoC;MAC7CE,UAAU,EAAE;IACd,CAAC;IACDO,KAAK,EAAE;MACLT,OAAO,EAAE,aAAa;MACtBU,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRX,OAAO,EAAE,6BAA6B;MACtCE,UAAU,EAAE,IAAI;MAChBQ,KAAK,EAAE;IACT,CAAC;IACDE,OAAO,EAAE;MACPZ,OAAO,EAAE,qCAAqC;MAC9CE,UAAU,EAAE;IACd,CAAC;IACDW,MAAM,EAAE;MACNb,OAAO,EACL,qJAAqJ;MACvJE,UAAU,EAAE;IACd,CAAC;IACDY,KAAK,EAAE;MACLd,OAAO,EAAE,QAAQ;MACjBE,UAAU,EAAE,IAAI;MAChBQ,KAAK,EAAE;IACT,CAAC;IACDK,QAAQ,EAAE,eAAe;IACzBV,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}