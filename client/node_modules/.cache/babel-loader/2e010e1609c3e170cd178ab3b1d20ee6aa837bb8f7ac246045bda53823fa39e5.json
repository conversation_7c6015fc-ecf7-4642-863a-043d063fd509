{"ast": null, "code": "'use strict';\n\nmodule.exports = javadoclike;\njavadoclike.displayName = 'javadoclike';\njavadoclike.aliases = [];\nfunction javadoclike(Prism) {\n  ;\n  (function (Prism) {\n    var javaDocLike = Prism.languages.javadoclike = {\n      parameter: {\n        pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m,\n        lookbehind: true\n      },\n      keyword: {\n        // keywords are the first word in a line preceded be an `@` or surrounded by curly braces.\n        // @word, {@word}\n        pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m,\n        lookbehind: true\n      },\n      punctuation: /[{}]/\n    };\n    /**\n     * Adds doc comment support to the given language and calls a given callback on each doc comment pattern.\n     *\n     * @param {string} lang the language add doc comment support to.\n     * @param {(pattern: {inside: {rest: undefined}}) => void} callback the function called with each doc comment pattern as argument.\n     */\n    function docCommentSupport(lang, callback) {\n      var tokenName = 'doc-comment';\n      var grammar = Prism.languages[lang];\n      if (!grammar) {\n        return;\n      }\n      var token = grammar[tokenName];\n      if (!token) {\n        // add doc comment: /** */\n        var definition = {};\n        definition[tokenName] = {\n          pattern: /(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          alias: 'comment'\n        };\n        grammar = Prism.languages.insertBefore(lang, 'comment', definition);\n        token = grammar[tokenName];\n      }\n      if (token instanceof RegExp) {\n        // convert regex to object\n        token = grammar[tokenName] = {\n          pattern: token\n        };\n      }\n      if (Array.isArray(token)) {\n        for (var i = 0, l = token.length; i < l; i++) {\n          if (token[i] instanceof RegExp) {\n            token[i] = {\n              pattern: token[i]\n            };\n          }\n          callback(token[i]);\n        }\n      } else {\n        callback(token);\n      }\n    }\n    /**\n     * Adds doc-comment support to the given languages for the given documentation language.\n     *\n     * @param {string[]|string} languages\n     * @param {Object} docLanguage\n     */\n    function addSupport(languages, docLanguage) {\n      if (typeof languages === 'string') {\n        languages = [languages];\n      }\n      languages.forEach(function (lang) {\n        docCommentSupport(lang, function (pattern) {\n          if (!pattern.inside) {\n            pattern.inside = {};\n          }\n          pattern.inside.rest = docLanguage;\n        });\n      });\n    }\n    Object.defineProperty(javaDocLike, 'addSupport', {\n      value: addSupport\n    });\n    javaDocLike.addSupport(['java', 'javascript', 'php'], javaDocLike);\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "javadoclike", "displayName", "aliases", "Prism", "javaDocLike", "languages", "parameter", "pattern", "lookbehind", "keyword", "punctuation", "docCommentSupport", "lang", "callback", "tokenName", "grammar", "token", "definition", "alias", "insertBefore", "RegExp", "Array", "isArray", "i", "l", "length", "addSupport", "docLanguage", "for<PERSON>ach", "inside", "rest", "Object", "defineProperty", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/javadoclike.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = javadoclike\njavadoclike.displayName = 'javadoclike'\njavadoclike.aliases = []\nfunction javadoclike(Prism) {\n  ;(function (Prism) {\n    var javaDocLike = (Prism.languages.javadoclike = {\n      parameter: {\n        pattern:\n          /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m,\n        lookbehind: true\n      },\n      keyword: {\n        // keywords are the first word in a line preceded be an `@` or surrounded by curly braces.\n        // @word, {@word}\n        pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m,\n        lookbehind: true\n      },\n      punctuation: /[{}]/\n    })\n    /**\n     * Adds doc comment support to the given language and calls a given callback on each doc comment pattern.\n     *\n     * @param {string} lang the language add doc comment support to.\n     * @param {(pattern: {inside: {rest: undefined}}) => void} callback the function called with each doc comment pattern as argument.\n     */\n    function docCommentSupport(lang, callback) {\n      var tokenName = 'doc-comment'\n      var grammar = Prism.languages[lang]\n      if (!grammar) {\n        return\n      }\n      var token = grammar[tokenName]\n      if (!token) {\n        // add doc comment: /** */\n        var definition = {}\n        definition[tokenName] = {\n          pattern: /(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          alias: 'comment'\n        }\n        grammar = Prism.languages.insertBefore(lang, 'comment', definition)\n        token = grammar[tokenName]\n      }\n      if (token instanceof RegExp) {\n        // convert regex to object\n        token = grammar[tokenName] = {\n          pattern: token\n        }\n      }\n      if (Array.isArray(token)) {\n        for (var i = 0, l = token.length; i < l; i++) {\n          if (token[i] instanceof RegExp) {\n            token[i] = {\n              pattern: token[i]\n            }\n          }\n          callback(token[i])\n        }\n      } else {\n        callback(token)\n      }\n    }\n    /**\n     * Adds doc-comment support to the given languages for the given documentation language.\n     *\n     * @param {string[]|string} languages\n     * @param {Object} docLanguage\n     */\n    function addSupport(languages, docLanguage) {\n      if (typeof languages === 'string') {\n        languages = [languages]\n      }\n      languages.forEach(function (lang) {\n        docCommentSupport(lang, function (pattern) {\n          if (!pattern.inside) {\n            pattern.inside = {}\n          }\n          pattern.inside.rest = docLanguage\n        })\n      })\n    }\n    Object.defineProperty(javaDocLike, 'addSupport', {\n      value: addSupport\n    })\n    javaDocLike.addSupport(['java', 'javascript', 'php'], javaDocLike)\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,WAAW;AAC5BA,WAAW,CAACC,WAAW,GAAG,aAAa;AACvCD,WAAW,CAACE,OAAO,GAAG,EAAE;AACxB,SAASF,WAAWA,CAACG,KAAK,EAAE;EAC1B;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,WAAW,GAAID,KAAK,CAACE,SAAS,CAACL,WAAW,GAAG;MAC/CM,SAAS,EAAE;QACTC,OAAO,EACL,gEAAgE;QAClEC,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EAAE;QACP;QACA;QACAF,OAAO,EAAE,uDAAuD;QAChEC,UAAU,EAAE;MACd,CAAC;MACDE,WAAW,EAAE;IACf,CAAE;IACF;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MACzC,IAAIC,SAAS,GAAG,aAAa;MAC7B,IAAIC,OAAO,GAAGZ,KAAK,CAACE,SAAS,CAACO,IAAI,CAAC;MACnC,IAAI,CAACG,OAAO,EAAE;QACZ;MACF;MACA,IAAIC,KAAK,GAAGD,OAAO,CAACD,SAAS,CAAC;MAC9B,IAAI,CAACE,KAAK,EAAE;QACV;QACA,IAAIC,UAAU,GAAG,CAAC,CAAC;QACnBA,UAAU,CAACH,SAAS,CAAC,GAAG;UACtBP,OAAO,EAAE,uCAAuC;UAChDC,UAAU,EAAE,IAAI;UAChBU,KAAK,EAAE;QACT,CAAC;QACDH,OAAO,GAAGZ,KAAK,CAACE,SAAS,CAACc,YAAY,CAACP,IAAI,EAAE,SAAS,EAAEK,UAAU,CAAC;QACnED,KAAK,GAAGD,OAAO,CAACD,SAAS,CAAC;MAC5B;MACA,IAAIE,KAAK,YAAYI,MAAM,EAAE;QAC3B;QACAJ,KAAK,GAAGD,OAAO,CAACD,SAAS,CAAC,GAAG;UAC3BP,OAAO,EAAES;QACX,CAAC;MACH;MACA,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;QACxB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAIP,KAAK,CAACO,CAAC,CAAC,YAAYH,MAAM,EAAE;YAC9BJ,KAAK,CAACO,CAAC,CAAC,GAAG;cACThB,OAAO,EAAES,KAAK,CAACO,CAAC;YAClB,CAAC;UACH;UACAV,QAAQ,CAACG,KAAK,CAACO,CAAC,CAAC,CAAC;QACpB;MACF,CAAC,MAAM;QACLV,QAAQ,CAACG,KAAK,CAAC;MACjB;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASU,UAAUA,CAACrB,SAAS,EAAEsB,WAAW,EAAE;MAC1C,IAAI,OAAOtB,SAAS,KAAK,QAAQ,EAAE;QACjCA,SAAS,GAAG,CAACA,SAAS,CAAC;MACzB;MACAA,SAAS,CAACuB,OAAO,CAAC,UAAUhB,IAAI,EAAE;QAChCD,iBAAiB,CAACC,IAAI,EAAE,UAAUL,OAAO,EAAE;UACzC,IAAI,CAACA,OAAO,CAACsB,MAAM,EAAE;YACnBtB,OAAO,CAACsB,MAAM,GAAG,CAAC,CAAC;UACrB;UACAtB,OAAO,CAACsB,MAAM,CAACC,IAAI,GAAGH,WAAW;QACnC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACAI,MAAM,CAACC,cAAc,CAAC5B,WAAW,EAAE,YAAY,EAAE;MAC/C6B,KAAK,EAAEP;IACT,CAAC,CAAC;IACFtB,WAAW,CAACsB,UAAU,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,EAAEtB,WAAW,CAAC;EACpE,CAAC,EAAED,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}