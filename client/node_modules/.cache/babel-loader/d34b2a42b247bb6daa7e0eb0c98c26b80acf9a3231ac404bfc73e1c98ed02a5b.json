{"ast": null, "code": "'use strict';\n\nmodule.exports = peoplecode;\npeoplecode.displayName = 'peoplecode';\npeoplecode.aliases = ['pcode'];\nfunction peoplecode(Prism) {\n  Prism.languages.peoplecode = {\n    comment: RegExp([\n    // C-style multiline comments\n    /\\/\\*[\\s\\S]*?\\*\\//.source,\n    // REM comments\n    /\\bREM[^;]*;/.source,\n    // Nested <* *> comments\n    /<\\*(?:[^<*]|\\*(?!>)|<(?!\\*)|<\\*(?:(?!\\*>)[\\s\\S])*\\*>)*\\*>/.source,\n    // /+ +/ comments\n    /\\/\\+[\\s\\S]*?\\+\\//.source].join('|')),\n    string: {\n      pattern: /'(?:''|[^'\\r\\n])*'(?!')|\"(?:\"\"|[^\"\\r\\n])*\"(?!\")/,\n      greedy: true\n    },\n    variable: /%\\w+/,\n    'function-definition': {\n      pattern: /((?:^|[^\\w-])(?:function|method)\\s+)\\w+/i,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'class-name': {\n      pattern: /((?:^|[^-\\w])(?:as|catch|class|component|create|extends|global|implements|instance|local|of|property|returns)\\s+)\\w+(?::\\w+)*/i,\n      lookbehind: true,\n      inside: {\n        punctuation: /:/\n      }\n    },\n    keyword: /\\b(?:abstract|alias|as|catch|class|component|constant|create|declare|else|end-(?:class|evaluate|for|function|get|if|method|set|try|while)|evaluate|extends|for|function|get|global|if|implements|import|instance|library|local|method|null|of|out|peopleCode|private|program|property|protected|readonly|ref|repeat|returns?|set|step|then|throw|to|try|until|value|when(?:-other)?|while)\\b/i,\n    'operator-keyword': {\n      pattern: /\\b(?:and|not|or)\\b/i,\n      alias: 'operator'\n    },\n    function: /[_a-z]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    operator: /<>|[<>]=?|!=|\\*\\*|[-+*/|=@]/,\n    punctuation: /[:.;,()[\\]]/\n  };\n  Prism.languages.pcode = Prism.languages.peoplecode;\n}", "map": {"version": 3, "names": ["module", "exports", "peoplecode", "displayName", "aliases", "Prism", "languages", "comment", "RegExp", "source", "join", "string", "pattern", "greedy", "variable", "lookbehind", "alias", "inside", "punctuation", "keyword", "function", "boolean", "number", "operator", "pcode"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/peoplecode.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = peoplecode\npeoplecode.displayName = 'peoplecode'\npeoplecode.aliases = ['pcode']\nfunction peoplecode(Prism) {\n  Prism.languages.peoplecode = {\n    comment: RegExp(\n      [\n        // C-style multiline comments\n        /\\/\\*[\\s\\S]*?\\*\\//.source, // REM comments\n        /\\bREM[^;]*;/.source, // Nested <* *> comments\n        /<\\*(?:[^<*]|\\*(?!>)|<(?!\\*)|<\\*(?:(?!\\*>)[\\s\\S])*\\*>)*\\*>/.source, // /+ +/ comments\n        /\\/\\+[\\s\\S]*?\\+\\//.source\n      ].join('|')\n    ),\n    string: {\n      pattern: /'(?:''|[^'\\r\\n])*'(?!')|\"(?:\"\"|[^\"\\r\\n])*\"(?!\")/,\n      greedy: true\n    },\n    variable: /%\\w+/,\n    'function-definition': {\n      pattern: /((?:^|[^\\w-])(?:function|method)\\s+)\\w+/i,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'class-name': {\n      pattern:\n        /((?:^|[^-\\w])(?:as|catch|class|component|create|extends|global|implements|instance|local|of|property|returns)\\s+)\\w+(?::\\w+)*/i,\n      lookbehind: true,\n      inside: {\n        punctuation: /:/\n      }\n    },\n    keyword:\n      /\\b(?:abstract|alias|as|catch|class|component|constant|create|declare|else|end-(?:class|evaluate|for|function|get|if|method|set|try|while)|evaluate|extends|for|function|get|global|if|implements|import|instance|library|local|method|null|of|out|peopleCode|private|program|property|protected|readonly|ref|repeat|returns?|set|step|then|throw|to|try|until|value|when(?:-other)?|while)\\b/i,\n    'operator-keyword': {\n      pattern: /\\b(?:and|not|or)\\b/i,\n      alias: 'operator'\n    },\n    function: /[_a-z]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    operator: /<>|[<>]=?|!=|\\*\\*|[-+*/|=@]/,\n    punctuation: /[:.;,()[\\]]/\n  }\n  Prism.languages.pcode = Prism.languages.peoplecode\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,CAAC,OAAO,CAAC;AAC9B,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAG;IAC3BK,OAAO,EAAEC,MAAM,CACb;IACE;IACA,kBAAkB,CAACC,MAAM;IAAE;IAC3B,aAAa,CAACA,MAAM;IAAE;IACtB,2DAA2D,CAACA,MAAM;IAAE;IACpE,kBAAkB,CAACA,MAAM,CAC1B,CAACC,IAAI,CAAC,GAAG,CACZ,CAAC;IACDC,MAAM,EAAE;MACNC,OAAO,EAAE,iDAAiD;MAC1DC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE,MAAM;IAChB,qBAAqB,EAAE;MACrBF,OAAO,EAAE,0CAA0C;MACnDG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE;MACZJ,OAAO,EACL,gIAAgI;MAClIG,UAAU,EAAE,IAAI;MAChBE,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EACL,+XAA+X;IACjY,kBAAkB,EAAE;MAClBP,OAAO,EAAE,qBAAqB;MAC9BI,KAAK,EAAE;IACT,CAAC;IACDI,QAAQ,EAAE,qBAAqB;IAC/BC,OAAO,EAAE,qBAAqB;IAC9BC,MAAM,EAAE,mBAAmB;IAC3BC,QAAQ,EAAE,6BAA6B;IACvCL,WAAW,EAAE;EACf,CAAC;EACDb,KAAK,CAACC,SAAS,CAACkB,KAAK,GAAGnB,KAAK,CAACC,SAAS,CAACJ,UAAU;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}