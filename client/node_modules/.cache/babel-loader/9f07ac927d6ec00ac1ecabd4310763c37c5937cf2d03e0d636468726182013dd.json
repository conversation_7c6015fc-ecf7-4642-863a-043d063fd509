{"ast": null, "code": "const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\"as\",\n// for exports\n\"in\", \"of\", \"if\", \"for\", \"while\", \"finally\", \"var\", \"new\", \"function\", \"do\", \"return\", \"void\", \"else\", \"break\", \"catch\", \"instanceof\", \"with\", \"throw\", \"case\", \"default\", \"try\", \"switch\", \"continue\", \"typeof\", \"delete\", \"let\", \"yield\", \"const\", \"class\",\n// J<PERSON> handles these with a special rule\n// \"get\",\n// \"set\",\n\"debugger\", \"async\", \"await\", \"static\", \"import\", \"from\", \"export\", \"extends\"];\nconst LITERALS = [\"true\", \"false\", \"null\", \"undefined\", \"NaN\", \"Infinity\"];\nconst TYPES = [\"Intl\", \"DataView\", \"Number\", \"Math\", \"Date\", \"String\", \"RegExp\", \"Object\", \"Function\", \"Boolean\", \"Error\", \"Symbol\", \"Set\", \"Map\", \"WeakSet\", \"WeakMap\", \"Proxy\", \"Reflect\", \"JSON\", \"Promise\", \"Float64Array\", \"Int16Array\", \"Int32Array\", \"Int8Array\", \"Uint16Array\", \"Uint32Array\", \"Float32Array\", \"Array\", \"Uint8Array\", \"Uint8ClampedArray\", \"ArrayBuffer\", \"BigInt64Array\", \"BigUint64Array\", \"BigInt\"];\nconst ERROR_TYPES = [\"EvalError\", \"InternalError\", \"RangeError\", \"ReferenceError\", \"SyntaxError\", \"TypeError\", \"URIError\"];\nconst BUILT_IN_GLOBALS = [\"setInterval\", \"setTimeout\", \"clearInterval\", \"clearTimeout\", \"require\", \"exports\", \"eval\", \"isFinite\", \"isNaN\", \"parseFloat\", \"parseInt\", \"decodeURI\", \"decodeURIComponent\", \"encodeURI\", \"encodeURIComponent\", \"escape\", \"unescape\"];\nconst BUILT_IN_VARIABLES = [\"arguments\", \"this\", \"super\", \"console\", \"window\", \"document\", \"localStorage\", \"module\", \"global\" // Node.js\n];\nconst BUILT_INS = [].concat(BUILT_IN_GLOBALS, BUILT_IN_VARIABLES, TYPES, ERROR_TYPES);\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, {\n    after\n  }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      // nested type?\n      // HTML should not include another raw `<` inside a tag\n      // But a type might: `<Array<Array<number>>`, etc.\n      if (nextChar === \"<\") {\n        response.ignoreMatch();\n        return;\n      }\n      // <something>\n      // This is now either a tag or a type.\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, {\n          after: afterMatchIndex\n        })) {\n          response.ignoreMatch();\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n    // DecimalLiteral\n    {\n      begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` + `[eE][+-]?(${decimalDigits})\\\\b`\n    }, {\n      begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b`\n    },\n    // DecimalBigIntegerLiteral\n    {\n      begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b`\n    },\n    // NonDecimalIntegerLiteral\n    {\n      begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\"\n    }, {\n      begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\"\n    }, {\n      begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\"\n    },\n    // LegacyOctalIntegerLiteral (does not include underscore separators)\n    // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n    {\n      begin: \"\\\\b0[0-7]+n?\\\\b\"\n    }],\n    relevance: 0\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: 'html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: 'css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n      subLanguage: 'css'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [hljs.BACKSLASH_ESCAPE, SUBST]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(/\\/\\*\\*(?!\\/)/, '\\\\*/', {\n    relevance: 0,\n    contains: [{\n      className: 'doctag',\n      begin: '@[A-Za-z]+',\n      contains: [{\n        className: 'type',\n        begin: '\\\\{',\n        end: '\\\\}',\n        relevance: 0\n      }, {\n        className: 'variable',\n        begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n        endsParent: true,\n        relevance: 0\n      },\n      // eat spaces (not newlines) so we can find\n      // types or variables\n      {\n        begin: /(?=[^\\n])\\s/,\n        relevance: 0\n      }]\n    }]\n  });\n  const COMMENT = {\n    className: \"comment\",\n    variants: [JSDOC_COMMENT, hljs.C_BLOCK_COMMENT_MODE, hljs.C_LINE_COMMENT_MODE]\n  };\n  const SUBST_INTERNALS = [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, HTML_TEMPLATE, CSS_TEMPLATE, TEMPLATE_STRING, NUMBER, hljs.REGEXP_MODE];\n  SUBST.contains = SUBST_INTERNALS.concat({\n    // we need to pair up {} inside our subst to prevent\n    // it from ending too early by matching another }\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS$1,\n    contains: [\"self\"].concat(SUBST_INTERNALS)\n  });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n  // eat recursive parens in sub expressions\n  {\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS$1,\n    contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n  }]);\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n  return {\n    name: 'Javascript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: {\n      PARAMS_CONTAINS\n    },\n    illegal: /#(?![$_A-z])/,\n    contains: [hljs.SHEBANG({\n      label: \"shebang\",\n      binary: \"node\",\n      relevance: 5\n    }), {\n      label: \"use_strict\",\n      className: 'meta',\n      relevance: 10,\n      begin: /^\\s*['\"]use (strict|asm)['\"]/\n    }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, HTML_TEMPLATE, CSS_TEMPLATE, TEMPLATE_STRING, COMMENT, NUMBER, {\n      // object attr container\n      begin: concat(/[{,\\n]\\s*/,\n      // we need to look ahead to make sure that we actually have an\n      // attribute coming up so we don't steal a comma from a potential\n      // \"value\" container\n      //\n      // NOTE: this might not work how you think.  We don't actually always\n      // enter this mode and stay.  Instead it might merely match `,\n      // <comments up next>` and then immediately end after the , because it\n      // fails to find any actual attrs. But this still does the job because\n      // it prevents the value contain rule from grabbing this instead and\n      // prevening this rule from firing when we actually DO have keys.\n      lookahead(concat(\n      // we also need to allow for multiple possible comments inbetween\n      // the first key:value pairing\n      /(((\\/\\/.*$)|(\\/\\*(\\*[^/]|[^*])*\\*\\/))\\s*)*/, IDENT_RE$1 + '\\\\s*:'))),\n      relevance: 0,\n      contains: [{\n        className: 'attr',\n        begin: IDENT_RE$1 + lookahead('\\\\s*:'),\n        relevance: 0\n      }]\n    }, {\n      // \"value\" container\n      begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n      keywords: 'return throw case',\n      contains: [COMMENT, hljs.REGEXP_MODE, {\n        className: 'function',\n        // we have to count the parens to make sure we actually have the\n        // correct bounding ( ) before the =>.  There could be any number of\n        // sub-expressions inside also surrounded by parens.\n        begin: '(\\\\(' + '[^()]*(\\\\(' + '[^()]*(\\\\(' + '[^()]*' + '\\\\)[^()]*)*' + '\\\\)[^()]*)*' + '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>',\n        returnBegin: true,\n        end: '\\\\s*=>',\n        contains: [{\n          className: 'params',\n          variants: [{\n            begin: hljs.UNDERSCORE_IDENT_RE,\n            relevance: 0\n          }, {\n            className: null,\n            begin: /\\(\\s*\\)/,\n            skip: true\n          }, {\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS$1,\n            contains: PARAMS_CONTAINS\n          }]\n        }]\n      }, {\n        // could be a comma delimited list of params to a function call\n        begin: /,/,\n        relevance: 0\n      }, {\n        className: '',\n        begin: /\\s/,\n        end: /\\s*/,\n        skip: true\n      }, {\n        // JSX\n        variants: [{\n          begin: FRAGMENT.begin,\n          end: FRAGMENT.end\n        }, {\n          begin: XML_TAG.begin,\n          // we carefully check the opening tag to see if it truly\n          // is a tag and not a false positive\n          'on:begin': XML_TAG.isTrulyOpeningTag,\n          end: XML_TAG.end\n        }],\n        subLanguage: 'xml',\n        contains: [{\n          begin: XML_TAG.begin,\n          end: XML_TAG.end,\n          skip: true,\n          contains: ['self']\n        }]\n      }],\n      relevance: 0\n    }, {\n      className: 'function',\n      beginKeywords: 'function',\n      end: /[{;]/,\n      excludeEnd: true,\n      keywords: KEYWORDS$1,\n      contains: ['self', hljs.inherit(hljs.TITLE_MODE, {\n        begin: IDENT_RE$1\n      }), PARAMS],\n      illegal: /%/\n    }, {\n      // prevent this from getting swallowed up by function\n      // since they appear \"function like\"\n      beginKeywords: \"while if switch catch for\"\n    }, {\n      className: 'function',\n      // we have to count the parens to make sure we actually have the correct\n      // bounding ( ).  There could be any number of sub-expressions inside\n      // also surrounded by parens.\n      begin: hljs.UNDERSCORE_IDENT_RE + '\\\\(' +\n      // first parens\n      '[^()]*(\\\\(' + '[^()]*(\\\\(' + '[^()]*' + '\\\\)[^()]*)*' + '\\\\)[^()]*)*' + '\\\\)\\\\s*\\\\{',\n      // end parens\n      returnBegin: true,\n      contains: [PARAMS, hljs.inherit(hljs.TITLE_MODE, {\n        begin: IDENT_RE$1\n      })]\n    },\n    // hack: prevents detection of keywords in some circumstances\n    // .keyword()\n    // $keyword = x\n    {\n      variants: [{\n        begin: '\\\\.' + IDENT_RE$1\n      }, {\n        begin: '\\\\$' + IDENT_RE$1\n      }],\n      relevance: 0\n    }, {\n      // ES6 class\n      className: 'class',\n      beginKeywords: 'class',\n      end: /[{;=]/,\n      excludeEnd: true,\n      illegal: /[:\"[\\]]/,\n      contains: [{\n        beginKeywords: 'extends'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      begin: /\\b(?=constructor)/,\n      end: /[{;]/,\n      excludeEnd: true,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        begin: IDENT_RE$1\n      }), 'self', PARAMS]\n    }, {\n      begin: '(get|set)\\\\s+(?=' + IDENT_RE$1 + '\\\\()',\n      end: /\\{/,\n      keywords: \"get set\",\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        begin: IDENT_RE$1\n      }), {\n        begin: /\\(\\)/\n      },\n      // eat to avoid empty params\n      PARAMS]\n    }, {\n      begin: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n    }]\n  };\n}\nmodule.exports = javascript;", "map": {"version": 3, "names": ["IDENT_RE", "KEYWORDS", "LITERALS", "TYPES", "ERROR_TYPES", "BUILT_IN_GLOBALS", "BUILT_IN_VARIABLES", "BUILT_INS", "concat", "source", "re", "<PERSON><PERSON><PERSON>", "args", "joined", "map", "x", "join", "javascript", "hljs", "hasClosingTag", "match", "after", "tag", "slice", "pos", "input", "indexOf", "IDENT_RE$1", "FRAGMENT", "begin", "end", "XML_TAG", "isTrulyOpeningTag", "response", "afterMatchIndex", "length", "index", "nextChar", "ignoreMatch", "KEYWORDS$1", "$pattern", "keyword", "literal", "built_in", "decimalDigits", "frac", "decimalInteger", "NUMBER", "className", "variants", "relevance", "SUBST", "keywords", "contains", "HTML_TEMPLATE", "starts", "returnEnd", "BACKSLASH_ESCAPE", "subLanguage", "CSS_TEMPLATE", "TEMPLATE_STRING", "JSDOC_COMMENT", "COMMENT", "endsParent", "C_BLOCK_COMMENT_MODE", "C_LINE_COMMENT_MODE", "SUBST_INTERNALS", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "REGEXP_MODE", "SUBST_AND_COMMENTS", "PARAMS_CONTAINS", "PARAMS", "excludeBegin", "excludeEnd", "name", "aliases", "exports", "illegal", "SHEBANG", "label", "binary", "RE_STARTERS_RE", "UNDERSCORE_IDENT_RE", "returnBegin", "skip", "beginKeywords", "inherit", "TITLE_MODE", "UNDERSCORE_TITLE_MODE", "module"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/javascript.js"], "sourcesContent": ["const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\nconst TYPES = [\n  \"Intl\",\n  \"DataView\",\n  \"Number\",\n  \"Math\",\n  \"Date\",\n  \"String\",\n  \"RegExp\",\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Error\",\n  \"Symbol\",\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  \"Proxy\",\n  \"Reflect\",\n  \"JSON\",\n  \"Promise\",\n  \"Float64Array\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Int8Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"Float32Array\",\n  \"Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"ArrayBuffer\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  \"BigInt\"\n];\n\nconst ERROR_TYPES = [\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  BUILT_IN_VARIABLES,\n  TYPES,\n  ERROR_TYPES\n);\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      // nested type?\n      // HTML should not include another raw `<` inside a tag\n      // But a type might: `<Array<Array<number>>`, etc.\n      if (nextChar === \"<\") {\n        response.ignoreMatch();\n        return;\n      }\n      // <something>\n      // This is now either a tag or a type.\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: 'html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: 'css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          className: 'doctag',\n          begin: '@[A-Za-z]+',\n          contains: [\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    TEMPLATE_STRING,\n    NUMBER,\n    hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  return {\n    name: 'Javascript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      {\n        label: \"use_strict\",\n        className: 'meta',\n        relevance: 10,\n        begin: /^\\s*['\"]use (strict|asm)['\"]/\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      NUMBER,\n      { // object attr container\n        begin: concat(/[{,\\n]\\s*/,\n          // we need to look ahead to make sure that we actually have an\n          // attribute coming up so we don't steal a comma from a potential\n          // \"value\" container\n          //\n          // NOTE: this might not work how you think.  We don't actually always\n          // enter this mode and stay.  Instead it might merely match `,\n          // <comments up next>` and then immediately end after the , because it\n          // fails to find any actual attrs. But this still does the job because\n          // it prevents the value contain rule from grabbing this instead and\n          // prevening this rule from firing when we actually DO have keys.\n          lookahead(concat(\n            // we also need to allow for multiple possible comments inbetween\n            // the first key:value pairing\n            /(((\\/\\/.*$)|(\\/\\*(\\*[^/]|[^*])*\\*\\/))\\s*)*/,\n            IDENT_RE$1 + '\\\\s*:'))),\n        relevance: 0,\n        contains: [\n          {\n            className: 'attr',\n            begin: IDENT_RE$1 + lookahead('\\\\s*:'),\n            relevance: 0\n          }\n        ]\n      },\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: '(\\\\(' +\n            '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n            '[^()]*' +\n            '\\\\)[^()]*)*' +\n            '\\\\)[^()]*)*' +\n            '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>',\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/, relevance: 0\n          },\n          {\n            className: '',\n            begin: /\\s/,\n            end: /\\s*/,\n            skip: true\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /[{;]/,\n        excludeEnd: true,\n        keywords: KEYWORDS$1,\n        contains: [\n          'self',\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n          PARAMS\n        ],\n        illegal: /%/\n      },\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        className: 'function',\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n        ]\n      },\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        variants: [\n          { begin: '\\\\.' + IDENT_RE$1 },\n          { begin: '\\\\$' + IDENT_RE$1 }\n        ],\n        relevance: 0\n      },\n      { // ES6 class\n        className: 'class',\n        beginKeywords: 'class',\n        end: /[{;=]/,\n        excludeEnd: true,\n        illegal: /[:\"[\\]]/,\n        contains: [\n          { beginKeywords: 'extends' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        begin: /\\b(?=constructor)/,\n        end: /[{;]/,\n        excludeEnd: true,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n          'self',\n          PARAMS\n        ]\n      },\n      {\n        begin: '(get|set)\\\\s+(?=' + IDENT_RE$1 + '\\\\()',\n        end: /\\{/,\n        keywords: \"get set\",\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n          { begin: /\\(\\)/ }, // eat to avoid empty params\n          PARAMS\n        ]\n      },\n      {\n        begin: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\nmodule.exports = javascript;\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAG,0BAA0B;AAC3C,MAAMC,QAAQ,GAAG,CACf,IAAI;AAAE;AACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,EACP,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,YAAY,EACZ,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,KAAK,EACL,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO;AACP;AACA;AACA;AACA,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,SAAS,CACV;AACD,MAAMC,QAAQ,GAAG,CACf,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACX,KAAK,EACL,UAAU,CACX;AAED,MAAMC,KAAK,GAAG,CACZ,MAAM,EACN,UAAU,EACV,QAAQ,EACR,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,OAAO,EACP,SAAS,EACT,MAAM,EACN,SAAS,EACT,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,aAAa,EACb,aAAa,EACb,cAAc,EACd,OAAO,EACP,YAAY,EACZ,mBAAmB,EACnB,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,QAAQ,CACT;AAED,MAAMC,WAAW,GAAG,CAClB,WAAW,EACX,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,WAAW,EACX,UAAU,CACX;AAED,MAAMC,gBAAgB,GAAG,CACvB,aAAa,EACb,YAAY,EACZ,eAAe,EACf,cAAc,EAEd,SAAS,EACT,SAAS,EAET,MAAM,EACN,UAAU,EACV,OAAO,EACP,YAAY,EACZ,UAAU,EACV,WAAW,EACX,oBAAoB,EACpB,WAAW,EACX,oBAAoB,EACpB,QAAQ,EACR,UAAU,CACX;AAED,MAAMC,kBAAkB,GAAG,CACzB,WAAW,EACX,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,UAAU,EACV,cAAc,EACd,QAAQ,EACR,QAAQ,CAAC;AAAA,CACV;AAED,MAAMC,SAAS,GAAG,EAAE,CAACC,MAAM,CACzBH,gBAAgB,EAChBC,kBAAkB,EAClBH,KAAK,EACLC,WACF,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASK,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOF,MAAM,CAAC,KAAK,EAAEE,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASF,MAAMA,CAAC,GAAGI,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,UAAUA,CAACC,IAAI,EAAE;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAE;IAAEC;EAAM,CAAC,KAAK;IAC1C,MAAMC,GAAG,GAAG,IAAI,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;IACpC,MAAMC,GAAG,GAAGJ,KAAK,CAACK,KAAK,CAACC,OAAO,CAACJ,GAAG,EAAED,KAAK,CAAC;IAC3C,OAAOG,GAAG,KAAK,CAAC,CAAC;EACnB,CAAC;EAED,MAAMG,UAAU,GAAG3B,QAAQ;EAC3B,MAAM4B,QAAQ,GAAG;IACfC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE;EACP,CAAC;EACD,MAAMC,OAAO,GAAG;IACdF,KAAK,EAAE,qBAAqB;IAC5BC,GAAG,EAAE,2BAA2B;IAChC;AACJ;AACA;AACA;IACIE,iBAAiB,EAAEA,CAACZ,KAAK,EAAEa,QAAQ,KAAK;MACtC,MAAMC,eAAe,GAAGd,KAAK,CAAC,CAAC,CAAC,CAACe,MAAM,GAAGf,KAAK,CAACgB,KAAK;MACrD,MAAMC,QAAQ,GAAGjB,KAAK,CAACK,KAAK,CAACS,eAAe,CAAC;MAC7C;MACA;MACA;MACA,IAAIG,QAAQ,KAAK,GAAG,EAAE;QACpBJ,QAAQ,CAACK,WAAW,CAAC,CAAC;QACtB;MACF;MACA;MACA;MACA,IAAID,QAAQ,KAAK,GAAG,EAAE;QACpB;QACA;QACA,IAAI,CAAClB,aAAa,CAACC,KAAK,EAAE;UAAEC,KAAK,EAAEa;QAAgB,CAAC,CAAC,EAAE;UACrDD,QAAQ,CAACK,WAAW,CAAC,CAAC;QACxB;MACF;IACF;EACF,CAAC;EACD,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAExC,QAAQ;IAClByC,OAAO,EAAExC,QAAQ;IACjByC,OAAO,EAAExC,QAAQ;IACjByC,QAAQ,EAAEpC;EACZ,CAAC;;EAED;EACA,MAAMqC,aAAa,GAAG,iBAAiB;EACvC,MAAMC,IAAI,GAAG,OAAOD,aAAa,GAAG;EACpC;EACA;EACA,MAAME,cAAc,GAAG,qCAAqC;EAC5D,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;IACR;IACA;MAAEpB,KAAK,EAAE,QAAQiB,cAAc,MAAMD,IAAI,YAAYA,IAAI,IAAI,GAC3D,aAAaD,aAAa;IAAO,CAAC,EACpC;MAAEf,KAAK,EAAE,OAAOiB,cAAc,SAASD,IAAI,eAAeA,IAAI;IAAO,CAAC;IAEtE;IACA;MAAEhB,KAAK,EAAE;IAA6B,CAAC;IAEvC;IACA;MAAEA,KAAK,EAAE;IAA2C,CAAC,EACrD;MAAEA,KAAK,EAAE;IAA+B,CAAC,EACzC;MAAEA,KAAK,EAAE;IAA+B,CAAC;IAEzC;IACA;IACA;MAAEA,KAAK,EAAE;IAAkB,CAAC,CAC7B;IACDqB,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,KAAK,GAAG;IACZH,SAAS,EAAE,OAAO;IAClBnB,KAAK,EAAE,QAAQ;IACfC,GAAG,EAAE,KAAK;IACVsB,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAE,EAAE,CAAC;EACf,CAAC;EACD,MAAMC,aAAa,GAAG;IACpBzB,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,EAAE;IACPyB,MAAM,EAAE;MACNzB,GAAG,EAAE,GAAG;MACR0B,SAAS,EAAE,KAAK;MAChBH,QAAQ,EAAE,CACRnC,IAAI,CAACuC,gBAAgB,EACrBN,KAAK,CACN;MACDO,WAAW,EAAE;IACf;EACF,CAAC;EACD,MAAMC,YAAY,GAAG;IACnB9B,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,EAAE;IACPyB,MAAM,EAAE;MACNzB,GAAG,EAAE,GAAG;MACR0B,SAAS,EAAE,KAAK;MAChBH,QAAQ,EAAE,CACRnC,IAAI,CAACuC,gBAAgB,EACrBN,KAAK,CACN;MACDO,WAAW,EAAE;IACf;EACF,CAAC;EACD,MAAME,eAAe,GAAG;IACtBZ,SAAS,EAAE,QAAQ;IACnBnB,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRuB,QAAQ,EAAE,CACRnC,IAAI,CAACuC,gBAAgB,EACrBN,KAAK;EAET,CAAC;EACD,MAAMU,aAAa,GAAG3C,IAAI,CAAC4C,OAAO,CAChC,cAAc,EACd,MAAM,EACN;IACEZ,SAAS,EAAE,CAAC;IACZG,QAAQ,EAAE,CACR;MACEL,SAAS,EAAE,QAAQ;MACnBnB,KAAK,EAAE,YAAY;MACnBwB,QAAQ,EAAE,CACR;QACEL,SAAS,EAAE,MAAM;QACjBnB,KAAK,EAAE,KAAK;QACZC,GAAG,EAAE,KAAK;QACVoB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,SAAS,EAAE,UAAU;QACrBnB,KAAK,EAAEF,UAAU,GAAG,eAAe;QACnCoC,UAAU,EAAE,IAAI;QAChBb,SAAS,EAAE;MACb,CAAC;MACD;MACA;MACA;QACErB,KAAK,EAAE,aAAa;QACpBqB,SAAS,EAAE;MACb,CAAC;IAEL,CAAC;EAEL,CACF,CAAC;EACD,MAAMY,OAAO,GAAG;IACdd,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,CACRY,aAAa,EACb3C,IAAI,CAAC8C,oBAAoB,EACzB9C,IAAI,CAAC+C,mBAAmB;EAE5B,CAAC;EACD,MAAMC,eAAe,GAAG,CACtBhD,IAAI,CAACiD,gBAAgB,EACrBjD,IAAI,CAACkD,iBAAiB,EACtBd,aAAa,EACbK,YAAY,EACZC,eAAe,EACfb,MAAM,EACN7B,IAAI,CAACmD,WAAW,CACjB;EACDlB,KAAK,CAACE,QAAQ,GAAGa,eAAe,CAC7B1D,MAAM,CAAC;IACN;IACA;IACAqB,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTsB,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAE,CACR,MAAM,CACP,CAAC7C,MAAM,CAAC0D,eAAe;EAC1B,CAAC,CAAC;EACJ,MAAMI,kBAAkB,GAAG,EAAE,CAAC9D,MAAM,CAACsD,OAAO,EAAEX,KAAK,CAACE,QAAQ,CAAC;EAC7D,MAAMkB,eAAe,GAAGD,kBAAkB,CAAC9D,MAAM,CAAC;EAChD;EACA;IACEqB,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTsB,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC7C,MAAM,CAAC8D,kBAAkB;EAC9C,CAAC,CACF,CAAC;EACF,MAAME,MAAM,GAAG;IACbxB,SAAS,EAAE,QAAQ;IACnBnB,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACT2C,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBtB,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAEkB;EACZ,CAAC;EAED,OAAO;IACLI,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpCxB,QAAQ,EAAEb,UAAU;IACpB;IACAsC,OAAO,EAAE;MAAEN;IAAgB,CAAC;IAC5BO,OAAO,EAAE,cAAc;IACvBzB,QAAQ,EAAE,CACRnC,IAAI,CAAC6D,OAAO,CAAC;MACXC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,MAAM;MACd/B,SAAS,EAAE;IACb,CAAC,CAAC,EACF;MACE8B,KAAK,EAAE,YAAY;MACnBhC,SAAS,EAAE,MAAM;MACjBE,SAAS,EAAE,EAAE;MACbrB,KAAK,EAAE;IACT,CAAC,EACDX,IAAI,CAACiD,gBAAgB,EACrBjD,IAAI,CAACkD,iBAAiB,EACtBd,aAAa,EACbK,YAAY,EACZC,eAAe,EACfE,OAAO,EACPf,MAAM,EACN;MAAE;MACAlB,KAAK,EAAErB,MAAM,CAAC,WAAW;MACvB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAG,SAAS,CAACH,MAAM;MACd;MACA;MACA,4CAA4C,EAC5CmB,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC;MAC3BuB,SAAS,EAAE,CAAC;MACZG,QAAQ,EAAE,CACR;QACEL,SAAS,EAAE,MAAM;QACjBnB,KAAK,EAAEF,UAAU,GAAGhB,SAAS,CAAC,OAAO,CAAC;QACtCuC,SAAS,EAAE;MACb,CAAC;IAEL,CAAC,EACD;MAAE;MACArB,KAAK,EAAE,GAAG,GAAGX,IAAI,CAACgE,cAAc,GAAG,iCAAiC;MACpE9B,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE,CACRS,OAAO,EACP5C,IAAI,CAACmD,WAAW,EAChB;QACErB,SAAS,EAAE,UAAU;QACrB;QACA;QACA;QACAnB,KAAK,EAAE,MAAM,GACb,YAAY,GACZ,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,aAAa,GACb,MAAM,GAAGX,IAAI,CAACiE,mBAAmB,GAAG,SAAS;QAC7CC,WAAW,EAAE,IAAI;QACjBtD,GAAG,EAAE,QAAQ;QACbuB,QAAQ,EAAE,CACR;UACEL,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,CACR;YACEpB,KAAK,EAAEX,IAAI,CAACiE,mBAAmB;YAC/BjC,SAAS,EAAE;UACb,CAAC,EACD;YACEF,SAAS,EAAE,IAAI;YACfnB,KAAK,EAAE,SAAS;YAChBwD,IAAI,EAAE;UACR,CAAC,EACD;YACExD,KAAK,EAAE,IAAI;YACXC,GAAG,EAAE,IAAI;YACT2C,YAAY,EAAE,IAAI;YAClBC,UAAU,EAAE,IAAI;YAChBtB,QAAQ,EAAEb,UAAU;YACpBc,QAAQ,EAAEkB;UACZ,CAAC;QAEL,CAAC;MAEL,CAAC,EACD;QAAE;QACA1C,KAAK,EAAE,GAAG;QAAEqB,SAAS,EAAE;MACzB,CAAC,EACD;QACEF,SAAS,EAAE,EAAE;QACbnB,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,KAAK;QACVuD,IAAI,EAAE;MACR,CAAC,EACD;QAAE;QACApC,QAAQ,EAAE,CACR;UAAEpB,KAAK,EAAED,QAAQ,CAACC,KAAK;UAAEC,GAAG,EAAEF,QAAQ,CAACE;QAAI,CAAC,EAC5C;UACED,KAAK,EAAEE,OAAO,CAACF,KAAK;UACpB;UACA;UACA,UAAU,EAAEE,OAAO,CAACC,iBAAiB;UACrCF,GAAG,EAAEC,OAAO,CAACD;QACf,CAAC,CACF;QACD4B,WAAW,EAAE,KAAK;QAClBL,QAAQ,EAAE,CACR;UACExB,KAAK,EAAEE,OAAO,CAACF,KAAK;UACpBC,GAAG,EAAEC,OAAO,CAACD,GAAG;UAChBuD,IAAI,EAAE,IAAI;UACVhC,QAAQ,EAAE,CAAC,MAAM;QACnB,CAAC;MAEL,CAAC,CACF;MACDH,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,UAAU;MACrBsC,aAAa,EAAE,UAAU;MACzBxD,GAAG,EAAE,MAAM;MACX4C,UAAU,EAAE,IAAI;MAChBtB,QAAQ,EAAEb,UAAU;MACpBc,QAAQ,EAAE,CACR,MAAM,EACNnC,IAAI,CAACqE,OAAO,CAACrE,IAAI,CAACsE,UAAU,EAAE;QAAE3D,KAAK,EAAEF;MAAW,CAAC,CAAC,EACpD6C,MAAM,CACP;MACDM,OAAO,EAAE;IACX,CAAC,EACD;MACE;MACA;MACAQ,aAAa,EAAE;IACjB,CAAC,EACD;MACEtC,SAAS,EAAE,UAAU;MACrB;MACA;MACA;MACAnB,KAAK,EAAEX,IAAI,CAACiE,mBAAmB,GAC7B,KAAK;MAAG;MACR,YAAY,GACV,YAAY,GACV,QAAQ,GACV,aAAa,GACf,aAAa,GACb,YAAY;MAAE;MAChBC,WAAW,EAAC,IAAI;MAChB/B,QAAQ,EAAE,CACRmB,MAAM,EACNtD,IAAI,CAACqE,OAAO,CAACrE,IAAI,CAACsE,UAAU,EAAE;QAAE3D,KAAK,EAAEF;MAAW,CAAC,CAAC;IAExD,CAAC;IACD;IACA;IACA;IACA;MACEsB,QAAQ,EAAE,CACR;QAAEpB,KAAK,EAAE,KAAK,GAAGF;MAAW,CAAC,EAC7B;QAAEE,KAAK,EAAE,KAAK,GAAGF;MAAW,CAAC,CAC9B;MACDuB,SAAS,EAAE;IACb,CAAC,EACD;MAAE;MACAF,SAAS,EAAE,OAAO;MAClBsC,aAAa,EAAE,OAAO;MACtBxD,GAAG,EAAE,OAAO;MACZ4C,UAAU,EAAE,IAAI;MAChBI,OAAO,EAAE,SAAS;MAClBzB,QAAQ,EAAE,CACR;QAAEiC,aAAa,EAAE;MAAU,CAAC,EAC5BpE,IAAI,CAACuE,qBAAqB;IAE9B,CAAC,EACD;MACE5D,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE,MAAM;MACX4C,UAAU,EAAE,IAAI;MAChBrB,QAAQ,EAAE,CACRnC,IAAI,CAACqE,OAAO,CAACrE,IAAI,CAACsE,UAAU,EAAE;QAAE3D,KAAK,EAAEF;MAAW,CAAC,CAAC,EACpD,MAAM,EACN6C,MAAM;IAEV,CAAC,EACD;MACE3C,KAAK,EAAE,kBAAkB,GAAGF,UAAU,GAAG,MAAM;MAC/CG,GAAG,EAAE,IAAI;MACTsB,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,CACRnC,IAAI,CAACqE,OAAO,CAACrE,IAAI,CAACsE,UAAU,EAAE;QAAE3D,KAAK,EAAEF;MAAW,CAAC,CAAC,EACpD;QAAEE,KAAK,EAAE;MAAO,CAAC;MAAE;MACnB2C,MAAM;IAEV,CAAC,EACD;MACE3C,KAAK,EAAE,QAAQ,CAAC;IAClB,CAAC;EAEL,CAAC;AACH;AAEA6D,MAAM,CAACb,OAAO,GAAG5D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}