{"ast": null, "code": "'use strict';\n\nmodule.exports = jsTemplates;\njsTemplates.displayName = 'jsTemplates';\njsTemplates.aliases = [];\nfunction jsTemplates(Prism) {\n  ;\n  (function (Prism) {\n    var templateString = Prism.languages.javascript['template-string']; // see the pattern in prism-javascript.js\n    var templateLiteralPattern = templateString.pattern.source;\n    var interpolationObject = templateString.inside['interpolation'];\n    var interpolationPunctuationObject = interpolationObject.inside['interpolation-punctuation'];\n    var interpolationPattern = interpolationObject.pattern.source;\n    /**\n     * Creates a new pattern to match a template string with a special tag.\n     *\n     * This will return `undefined` if there is no grammar with the given language id.\n     *\n     * @param {string} language The language id of the embedded language. E.g. `markdown`.\n     * @param {string} tag The regex pattern to match the tag.\n     * @returns {object | undefined}\n     * @example\n     * createTemplate('css', /\\bcss/.source);\n     */\n    function createTemplate(language, tag) {\n      if (!Prism.languages[language]) {\n        return undefined;\n      }\n      return {\n        pattern: RegExp('((?:' + tag + ')\\\\s*)' + templateLiteralPattern),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          'template-punctuation': {\n            pattern: /^`|`$/,\n            alias: 'string'\n          },\n          'embedded-code': {\n            pattern: /[\\s\\S]+/,\n            alias: language\n          }\n        }\n      };\n    }\n    Prism.languages.javascript['template-string'] = [\n    // styled-jsx:\n    //   css`a { color: #25F; }`\n    // styled-components:\n    //   styled.h1`color: red;`\n    createTemplate('css', /\\b(?:styled(?:\\([^)]*\\))?(?:\\s*\\.\\s*\\w+(?:\\([^)]*\\))*)*|css(?:\\s*\\.\\s*(?:global|resolve))?|createGlobalStyle|keyframes)/.source),\n    // html`<p></p>`\n    // div.innerHTML = `<p></p>`\n    createTemplate('html', /\\bhtml|\\.\\s*(?:inner|outer)HTML\\s*\\+?=/.source),\n    // svg`<path fill=\"#fff\" d=\"M55.37 ...\"/>`\n    createTemplate('svg', /\\bsvg/.source),\n    // md`# h1`, markdown`## h2`\n    createTemplate('markdown', /\\b(?:markdown|md)/.source),\n    // gql`...`, graphql`...`, graphql.experimental`...`\n    createTemplate('graphql', /\\b(?:gql|graphql(?:\\s*\\.\\s*experimental)?)/.source),\n    // sql`...`\n    createTemplate('sql', /\\bsql/.source),\n    // vanilla template string\n    templateString].filter(Boolean);\n    /**\n     * Returns a specific placeholder literal for the given language.\n     *\n     * @param {number} counter\n     * @param {string} language\n     * @returns {string}\n     */\n    function getPlaceholder(counter, language) {\n      return '___' + language.toUpperCase() + '_' + counter + '___';\n    }\n    /**\n     * Returns the tokens of `Prism.tokenize` but also runs the `before-tokenize` and `after-tokenize` hooks.\n     *\n     * @param {string} code\n     * @param {any} grammar\n     * @param {string} language\n     * @returns {(string|Token)[]}\n     */\n    function tokenizeWithHooks(code, grammar, language) {\n      var env = {\n        code: code,\n        grammar: grammar,\n        language: language\n      };\n      Prism.hooks.run('before-tokenize', env);\n      env.tokens = Prism.tokenize(env.code, env.grammar);\n      Prism.hooks.run('after-tokenize', env);\n      return env.tokens;\n    }\n    /**\n     * Returns the token of the given JavaScript interpolation expression.\n     *\n     * @param {string} expression The code of the expression. E.g. `\"${42}\"`\n     * @returns {Token}\n     */\n    function tokenizeInterpolationExpression(expression) {\n      var tempGrammar = {};\n      tempGrammar['interpolation-punctuation'] = interpolationPunctuationObject;\n      /** @type {Array} */\n      var tokens = Prism.tokenize(expression, tempGrammar);\n      if (tokens.length === 3) {\n        /**\n         * The token array will look like this\n         * [\n         *     [\"interpolation-punctuation\", \"${\"]\n         *     \"...\" // JavaScript expression of the interpolation\n         *     [\"interpolation-punctuation\", \"}\"]\n         * ]\n         */\n        var args = [1, 1];\n        args.push.apply(args, tokenizeWithHooks(tokens[1], Prism.languages.javascript, 'javascript'));\n        tokens.splice.apply(tokens, args);\n      }\n      return new Prism.Token('interpolation', tokens, interpolationObject.alias, expression);\n    }\n    /**\n     * Tokenizes the given code with support for JavaScript interpolation expressions mixed in.\n     *\n     * This function has 3 phases:\n     *\n     * 1. Replace all JavaScript interpolation expression with a placeholder.\n     *    The placeholder will have the syntax of a identify of the target language.\n     * 2. Tokenize the code with placeholders.\n     * 3. Tokenize the interpolation expressions and re-insert them into the tokenize code.\n     *    The insertion only works if a placeholder hasn't been \"ripped apart\" meaning that the placeholder has been\n     *    tokenized as two tokens by the grammar of the embedded language.\n     *\n     * @param {string} code\n     * @param {object} grammar\n     * @param {string} language\n     * @returns {Token}\n     */\n    function tokenizeEmbedded(code, grammar, language) {\n      // 1. First filter out all interpolations\n      // because they might be escaped, we need a lookbehind, so we use Prism\n      /** @type {(Token|string)[]} */\n      var _tokens = Prism.tokenize(code, {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          lookbehind: true\n        }\n      }); // replace all interpolations with a placeholder which is not in the code already\n      var placeholderCounter = 0;\n      /** @type {Object<string, string>} */\n      var placeholderMap = {};\n      var embeddedCode = _tokens.map(function (token) {\n        if (typeof token === 'string') {\n          return token;\n        } else {\n          var interpolationExpression = token.content;\n          var placeholder;\n          while (code.indexOf(placeholder = getPlaceholder(placeholderCounter++, language)) !== -1) {\n            /* noop */\n          }\n          placeholderMap[placeholder] = interpolationExpression;\n          return placeholder;\n        }\n      }).join(''); // 2. Tokenize the embedded code\n      var embeddedTokens = tokenizeWithHooks(embeddedCode, grammar, language); // 3. Re-insert the interpolation\n      var placeholders = Object.keys(placeholderMap);\n      placeholderCounter = 0;\n      /**\n       *\n       * @param {(Token|string)[]} tokens\n       * @returns {void}\n       */\n      function walkTokens(tokens) {\n        for (var i = 0; i < tokens.length; i++) {\n          if (placeholderCounter >= placeholders.length) {\n            return;\n          }\n          var token = tokens[i];\n          if (typeof token === 'string' || typeof token.content === 'string') {\n            var placeholder = placeholders[placeholderCounter];\n            var s = typeof token === 'string' ? token : /** @type {string} */\n            token.content;\n            var index = s.indexOf(placeholder);\n            if (index !== -1) {\n              ++placeholderCounter;\n              var before = s.substring(0, index);\n              var middle = tokenizeInterpolationExpression(placeholderMap[placeholder]);\n              var after = s.substring(index + placeholder.length);\n              var replacement = [];\n              if (before) {\n                replacement.push(before);\n              }\n              replacement.push(middle);\n              if (after) {\n                var afterTokens = [after];\n                walkTokens(afterTokens);\n                replacement.push.apply(replacement, afterTokens);\n              }\n              if (typeof token === 'string') {\n                tokens.splice.apply(tokens, [i, 1].concat(replacement));\n                i += replacement.length - 1;\n              } else {\n                token.content = replacement;\n              }\n            }\n          } else {\n            var content = token.content;\n            if (Array.isArray(content)) {\n              walkTokens(content);\n            } else {\n              walkTokens([content]);\n            }\n          }\n        }\n      }\n      walkTokens(embeddedTokens);\n      return new Prism.Token(language, embeddedTokens, 'language-' + language, code);\n    }\n    /**\n     * The languages for which JS templating will handle tagged template literals.\n     *\n     * JS templating isn't active for only JavaScript but also related languages like TypeScript, JSX, and TSX.\n     */\n    var supportedLanguages = {\n      javascript: true,\n      js: true,\n      typescript: true,\n      ts: true,\n      jsx: true,\n      tsx: true\n    };\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (!(env.language in supportedLanguages)) {\n        return;\n      }\n      /**\n       * Finds and tokenizes all template strings with an embedded languages.\n       *\n       * @param {(Token | string)[]} tokens\n       * @returns {void}\n       */\n      function findTemplateStrings(tokens) {\n        for (var i = 0, l = tokens.length; i < l; i++) {\n          var token = tokens[i];\n          if (typeof token === 'string') {\n            continue;\n          }\n          var content = token.content;\n          if (!Array.isArray(content)) {\n            if (typeof content !== 'string') {\n              findTemplateStrings([content]);\n            }\n            continue;\n          }\n          if (token.type === 'template-string') {\n            /**\n             * A JavaScript template-string token will look like this:\n             *\n             * [\"template-string\", [\n             *     [\"template-punctuation\", \"`\"],\n             *     (\n             *         An array of \"string\" and \"interpolation\" tokens. This is the simple string case.\n             *         or\n             *         [\"embedded-code\", \"...\"] This is the token containing the embedded code.\n             *                                  It also has an alias which is the language of the embedded code.\n             *     ),\n             *     [\"template-punctuation\", \"`\"]\n             * ]]\n             */\n            var embedded = content[1];\n            if (content.length === 3 && typeof embedded !== 'string' && embedded.type === 'embedded-code') {\n              // get string content\n              var code = stringContent(embedded);\n              var alias = embedded.alias;\n              var language = Array.isArray(alias) ? alias[0] : alias;\n              var grammar = Prism.languages[language];\n              if (!grammar) {\n                // the embedded language isn't registered.\n                continue;\n              }\n              content[1] = tokenizeEmbedded(code, grammar, language);\n            }\n          } else {\n            findTemplateStrings(content);\n          }\n        }\n      }\n      findTemplateStrings(env.tokens);\n    });\n    /**\n     * Returns the string content of a token or token stream.\n     *\n     * @param {string | Token | (string | Token)[]} value\n     * @returns {string}\n     */\n    function stringContent(value) {\n      if (typeof value === 'string') {\n        return value;\n      } else if (Array.isArray(value)) {\n        return value.map(stringContent).join('');\n      } else {\n        return stringContent(value.content);\n      }\n    }\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "jsTemplates", "displayName", "aliases", "Prism", "templateString", "languages", "javascript", "templateLiteralPattern", "pattern", "source", "interpolationObject", "inside", "interpolationPunctuationObject", "interpolationPattern", "createTemplate", "language", "tag", "undefined", "RegExp", "lookbehind", "greedy", "alias", "filter", "Boolean", "getPlaceholder", "counter", "toUpperCase", "tokenizeWithHooks", "code", "grammar", "env", "hooks", "run", "tokens", "tokenize", "tokenizeInterpolationExpression", "expression", "tempGrammar", "length", "args", "push", "apply", "splice", "Token", "tokenizeEmbedded", "_tokens", "interpolation", "placeholder<PERSON><PERSON><PERSON>", "placeholderM<PERSON>", "embeddedCode", "map", "token", "interpolationExpression", "content", "placeholder", "indexOf", "join", "embeddedTokens", "placeholders", "Object", "keys", "walkTokens", "i", "s", "index", "before", "substring", "middle", "after", "replacement", "afterTokens", "concat", "Array", "isArray", "supportedLanguages", "js", "typescript", "ts", "jsx", "tsx", "add", "findTemplateStrings", "l", "type", "embedded", "stringContent", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/js-templates.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsTemplates\njsTemplates.displayName = 'jsTemplates'\njsTemplates.aliases = []\nfunction jsTemplates(Prism) {\n  ;(function (Prism) {\n    var templateString = Prism.languages.javascript['template-string'] // see the pattern in prism-javascript.js\n    var templateLiteralPattern = templateString.pattern.source\n    var interpolationObject = templateString.inside['interpolation']\n    var interpolationPunctuationObject =\n      interpolationObject.inside['interpolation-punctuation']\n    var interpolationPattern = interpolationObject.pattern.source\n    /**\n     * Creates a new pattern to match a template string with a special tag.\n     *\n     * This will return `undefined` if there is no grammar with the given language id.\n     *\n     * @param {string} language The language id of the embedded language. E.g. `markdown`.\n     * @param {string} tag The regex pattern to match the tag.\n     * @returns {object | undefined}\n     * @example\n     * createTemplate('css', /\\bcss/.source);\n     */\n    function createTemplate(language, tag) {\n      if (!Prism.languages[language]) {\n        return undefined\n      }\n      return {\n        pattern: RegExp('((?:' + tag + ')\\\\s*)' + templateLiteralPattern),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          'template-punctuation': {\n            pattern: /^`|`$/,\n            alias: 'string'\n          },\n          'embedded-code': {\n            pattern: /[\\s\\S]+/,\n            alias: language\n          }\n        }\n      }\n    }\n    Prism.languages.javascript['template-string'] = [\n      // styled-jsx:\n      //   css`a { color: #25F; }`\n      // styled-components:\n      //   styled.h1`color: red;`\n      createTemplate(\n        'css',\n        /\\b(?:styled(?:\\([^)]*\\))?(?:\\s*\\.\\s*\\w+(?:\\([^)]*\\))*)*|css(?:\\s*\\.\\s*(?:global|resolve))?|createGlobalStyle|keyframes)/\n          .source\n      ), // html`<p></p>`\n      // div.innerHTML = `<p></p>`\n      createTemplate('html', /\\bhtml|\\.\\s*(?:inner|outer)HTML\\s*\\+?=/.source), // svg`<path fill=\"#fff\" d=\"M55.37 ...\"/>`\n      createTemplate('svg', /\\bsvg/.source), // md`# h1`, markdown`## h2`\n      createTemplate('markdown', /\\b(?:markdown|md)/.source), // gql`...`, graphql`...`, graphql.experimental`...`\n      createTemplate(\n        'graphql',\n        /\\b(?:gql|graphql(?:\\s*\\.\\s*experimental)?)/.source\n      ), // sql`...`\n      createTemplate('sql', /\\bsql/.source), // vanilla template string\n      templateString\n    ].filter(Boolean)\n    /**\n     * Returns a specific placeholder literal for the given language.\n     *\n     * @param {number} counter\n     * @param {string} language\n     * @returns {string}\n     */\n    function getPlaceholder(counter, language) {\n      return '___' + language.toUpperCase() + '_' + counter + '___'\n    }\n    /**\n     * Returns the tokens of `Prism.tokenize` but also runs the `before-tokenize` and `after-tokenize` hooks.\n     *\n     * @param {string} code\n     * @param {any} grammar\n     * @param {string} language\n     * @returns {(string|Token)[]}\n     */\n    function tokenizeWithHooks(code, grammar, language) {\n      var env = {\n        code: code,\n        grammar: grammar,\n        language: language\n      }\n      Prism.hooks.run('before-tokenize', env)\n      env.tokens = Prism.tokenize(env.code, env.grammar)\n      Prism.hooks.run('after-tokenize', env)\n      return env.tokens\n    }\n    /**\n     * Returns the token of the given JavaScript interpolation expression.\n     *\n     * @param {string} expression The code of the expression. E.g. `\"${42}\"`\n     * @returns {Token}\n     */\n    function tokenizeInterpolationExpression(expression) {\n      var tempGrammar = {}\n      tempGrammar['interpolation-punctuation'] = interpolationPunctuationObject\n      /** @type {Array} */\n      var tokens = Prism.tokenize(expression, tempGrammar)\n      if (tokens.length === 3) {\n        /**\n         * The token array will look like this\n         * [\n         *     [\"interpolation-punctuation\", \"${\"]\n         *     \"...\" // JavaScript expression of the interpolation\n         *     [\"interpolation-punctuation\", \"}\"]\n         * ]\n         */\n        var args = [1, 1]\n        args.push.apply(\n          args,\n          tokenizeWithHooks(tokens[1], Prism.languages.javascript, 'javascript')\n        )\n        tokens.splice.apply(tokens, args)\n      }\n      return new Prism.Token(\n        'interpolation',\n        tokens,\n        interpolationObject.alias,\n        expression\n      )\n    }\n    /**\n     * Tokenizes the given code with support for JavaScript interpolation expressions mixed in.\n     *\n     * This function has 3 phases:\n     *\n     * 1. Replace all JavaScript interpolation expression with a placeholder.\n     *    The placeholder will have the syntax of a identify of the target language.\n     * 2. Tokenize the code with placeholders.\n     * 3. Tokenize the interpolation expressions and re-insert them into the tokenize code.\n     *    The insertion only works if a placeholder hasn't been \"ripped apart\" meaning that the placeholder has been\n     *    tokenized as two tokens by the grammar of the embedded language.\n     *\n     * @param {string} code\n     * @param {object} grammar\n     * @param {string} language\n     * @returns {Token}\n     */\n    function tokenizeEmbedded(code, grammar, language) {\n      // 1. First filter out all interpolations\n      // because they might be escaped, we need a lookbehind, so we use Prism\n      /** @type {(Token|string)[]} */\n      var _tokens = Prism.tokenize(code, {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          lookbehind: true\n        }\n      }) // replace all interpolations with a placeholder which is not in the code already\n      var placeholderCounter = 0\n      /** @type {Object<string, string>} */\n      var placeholderMap = {}\n      var embeddedCode = _tokens\n        .map(function (token) {\n          if (typeof token === 'string') {\n            return token\n          } else {\n            var interpolationExpression = token.content\n            var placeholder\n            while (\n              code.indexOf(\n                (placeholder = getPlaceholder(placeholderCounter++, language))\n              ) !== -1\n            ) {\n              /* noop */\n            }\n            placeholderMap[placeholder] = interpolationExpression\n            return placeholder\n          }\n        })\n        .join('') // 2. Tokenize the embedded code\n      var embeddedTokens = tokenizeWithHooks(embeddedCode, grammar, language) // 3. Re-insert the interpolation\n      var placeholders = Object.keys(placeholderMap)\n      placeholderCounter = 0\n      /**\n       *\n       * @param {(Token|string)[]} tokens\n       * @returns {void}\n       */\n      function walkTokens(tokens) {\n        for (var i = 0; i < tokens.length; i++) {\n          if (placeholderCounter >= placeholders.length) {\n            return\n          }\n          var token = tokens[i]\n          if (typeof token === 'string' || typeof token.content === 'string') {\n            var placeholder = placeholders[placeholderCounter]\n            var s =\n              typeof token === 'string'\n                ? token\n                : /** @type {string} */\n                  token.content\n            var index = s.indexOf(placeholder)\n            if (index !== -1) {\n              ++placeholderCounter\n              var before = s.substring(0, index)\n              var middle = tokenizeInterpolationExpression(\n                placeholderMap[placeholder]\n              )\n              var after = s.substring(index + placeholder.length)\n              var replacement = []\n              if (before) {\n                replacement.push(before)\n              }\n              replacement.push(middle)\n              if (after) {\n                var afterTokens = [after]\n                walkTokens(afterTokens)\n                replacement.push.apply(replacement, afterTokens)\n              }\n              if (typeof token === 'string') {\n                tokens.splice.apply(tokens, [i, 1].concat(replacement))\n                i += replacement.length - 1\n              } else {\n                token.content = replacement\n              }\n            }\n          } else {\n            var content = token.content\n            if (Array.isArray(content)) {\n              walkTokens(content)\n            } else {\n              walkTokens([content])\n            }\n          }\n        }\n      }\n      walkTokens(embeddedTokens)\n      return new Prism.Token(\n        language,\n        embeddedTokens,\n        'language-' + language,\n        code\n      )\n    }\n    /**\n     * The languages for which JS templating will handle tagged template literals.\n     *\n     * JS templating isn't active for only JavaScript but also related languages like TypeScript, JSX, and TSX.\n     */\n    var supportedLanguages = {\n      javascript: true,\n      js: true,\n      typescript: true,\n      ts: true,\n      jsx: true,\n      tsx: true\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (!(env.language in supportedLanguages)) {\n        return\n      }\n      /**\n       * Finds and tokenizes all template strings with an embedded languages.\n       *\n       * @param {(Token | string)[]} tokens\n       * @returns {void}\n       */\n      function findTemplateStrings(tokens) {\n        for (var i = 0, l = tokens.length; i < l; i++) {\n          var token = tokens[i]\n          if (typeof token === 'string') {\n            continue\n          }\n          var content = token.content\n          if (!Array.isArray(content)) {\n            if (typeof content !== 'string') {\n              findTemplateStrings([content])\n            }\n            continue\n          }\n          if (token.type === 'template-string') {\n            /**\n             * A JavaScript template-string token will look like this:\n             *\n             * [\"template-string\", [\n             *     [\"template-punctuation\", \"`\"],\n             *     (\n             *         An array of \"string\" and \"interpolation\" tokens. This is the simple string case.\n             *         or\n             *         [\"embedded-code\", \"...\"] This is the token containing the embedded code.\n             *                                  It also has an alias which is the language of the embedded code.\n             *     ),\n             *     [\"template-punctuation\", \"`\"]\n             * ]]\n             */\n            var embedded = content[1]\n            if (\n              content.length === 3 &&\n              typeof embedded !== 'string' &&\n              embedded.type === 'embedded-code'\n            ) {\n              // get string content\n              var code = stringContent(embedded)\n              var alias = embedded.alias\n              var language = Array.isArray(alias) ? alias[0] : alias\n              var grammar = Prism.languages[language]\n              if (!grammar) {\n                // the embedded language isn't registered.\n                continue\n              }\n              content[1] = tokenizeEmbedded(code, grammar, language)\n            }\n          } else {\n            findTemplateStrings(content)\n          }\n        }\n      }\n      findTemplateStrings(env.tokens)\n    })\n    /**\n     * Returns the string content of a token or token stream.\n     *\n     * @param {string | Token | (string | Token)[]} value\n     * @returns {string}\n     */\n    function stringContent(value) {\n      if (typeof value === 'string') {\n        return value\n      } else if (Array.isArray(value)) {\n        return value.map(stringContent).join('')\n      } else {\n        return stringContent(value.content)\n      }\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,WAAW;AAC5BA,WAAW,CAACC,WAAW,GAAG,aAAa;AACvCD,WAAW,CAACE,OAAO,GAAG,EAAE;AACxB,SAASF,WAAWA,CAACG,KAAK,EAAE;EAC1B;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,cAAc,GAAGD,KAAK,CAACE,SAAS,CAACC,UAAU,CAAC,iBAAiB,CAAC,EAAC;IACnE,IAAIC,sBAAsB,GAAGH,cAAc,CAACI,OAAO,CAACC,MAAM;IAC1D,IAAIC,mBAAmB,GAAGN,cAAc,CAACO,MAAM,CAAC,eAAe,CAAC;IAChE,IAAIC,8BAA8B,GAChCF,mBAAmB,CAACC,MAAM,CAAC,2BAA2B,CAAC;IACzD,IAAIE,oBAAoB,GAAGH,mBAAmB,CAACF,OAAO,CAACC,MAAM;IAC7D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASK,cAAcA,CAACC,QAAQ,EAAEC,GAAG,EAAE;MACrC,IAAI,CAACb,KAAK,CAACE,SAAS,CAACU,QAAQ,CAAC,EAAE;QAC9B,OAAOE,SAAS;MAClB;MACA,OAAO;QACLT,OAAO,EAAEU,MAAM,CAAC,MAAM,GAAGF,GAAG,GAAG,QAAQ,GAAGT,sBAAsB,CAAC;QACjEY,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZT,MAAM,EAAE;UACN,sBAAsB,EAAE;YACtBH,OAAO,EAAE,OAAO;YAChBa,KAAK,EAAE;UACT,CAAC;UACD,eAAe,EAAE;YACfb,OAAO,EAAE,SAAS;YAClBa,KAAK,EAAEN;UACT;QACF;MACF,CAAC;IACH;IACAZ,KAAK,CAACE,SAAS,CAACC,UAAU,CAAC,iBAAiB,CAAC,GAAG;IAC9C;IACA;IACA;IACA;IACAQ,cAAc,CACZ,KAAK,EACL,yHAAyH,CACtHL,MACL,CAAC;IAAE;IACH;IACAK,cAAc,CAAC,MAAM,EAAE,wCAAwC,CAACL,MAAM,CAAC;IAAE;IACzEK,cAAc,CAAC,KAAK,EAAE,OAAO,CAACL,MAAM,CAAC;IAAE;IACvCK,cAAc,CAAC,UAAU,EAAE,mBAAmB,CAACL,MAAM,CAAC;IAAE;IACxDK,cAAc,CACZ,SAAS,EACT,4CAA4C,CAACL,MAC/C,CAAC;IAAE;IACHK,cAAc,CAAC,KAAK,EAAE,OAAO,CAACL,MAAM,CAAC;IAAE;IACvCL,cAAc,CACf,CAACkB,MAAM,CAACC,OAAO,CAAC;IACjB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,cAAcA,CAACC,OAAO,EAAEV,QAAQ,EAAE;MACzC,OAAO,KAAK,GAAGA,QAAQ,CAACW,WAAW,CAAC,CAAC,GAAG,GAAG,GAAGD,OAAO,GAAG,KAAK;IAC/D;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASE,iBAAiBA,CAACC,IAAI,EAAEC,OAAO,EAAEd,QAAQ,EAAE;MAClD,IAAIe,GAAG,GAAG;QACRF,IAAI,EAAEA,IAAI;QACVC,OAAO,EAAEA,OAAO;QAChBd,QAAQ,EAAEA;MACZ,CAAC;MACDZ,KAAK,CAAC4B,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAEF,GAAG,CAAC;MACvCA,GAAG,CAACG,MAAM,GAAG9B,KAAK,CAAC+B,QAAQ,CAACJ,GAAG,CAACF,IAAI,EAAEE,GAAG,CAACD,OAAO,CAAC;MAClD1B,KAAK,CAAC4B,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAEF,GAAG,CAAC;MACtC,OAAOA,GAAG,CAACG,MAAM;IACnB;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASE,+BAA+BA,CAACC,UAAU,EAAE;MACnD,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpBA,WAAW,CAAC,2BAA2B,CAAC,GAAGzB,8BAA8B;MACzE;MACA,IAAIqB,MAAM,GAAG9B,KAAK,CAAC+B,QAAQ,CAACE,UAAU,EAAEC,WAAW,CAAC;MACpD,IAAIJ,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;QACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;QACQ,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACjBA,IAAI,CAACC,IAAI,CAACC,KAAK,CACbF,IAAI,EACJZ,iBAAiB,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE9B,KAAK,CAACE,SAAS,CAACC,UAAU,EAAE,YAAY,CACvE,CAAC;QACD2B,MAAM,CAACS,MAAM,CAACD,KAAK,CAACR,MAAM,EAAEM,IAAI,CAAC;MACnC;MACA,OAAO,IAAIpC,KAAK,CAACwC,KAAK,CACpB,eAAe,EACfV,MAAM,EACNvB,mBAAmB,CAACW,KAAK,EACzBe,UACF,CAAC;IACH;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASQ,gBAAgBA,CAAChB,IAAI,EAAEC,OAAO,EAAEd,QAAQ,EAAE;MACjD;MACA;MACA;MACA,IAAI8B,OAAO,GAAG1C,KAAK,CAAC+B,QAAQ,CAACN,IAAI,EAAE;QACjCkB,aAAa,EAAE;UACbtC,OAAO,EAAEU,MAAM,CAACL,oBAAoB,CAAC;UACrCM,UAAU,EAAE;QACd;MACF,CAAC,CAAC,EAAC;MACH,IAAI4B,kBAAkB,GAAG,CAAC;MAC1B;MACA,IAAIC,cAAc,GAAG,CAAC,CAAC;MACvB,IAAIC,YAAY,GAAGJ,OAAO,CACvBK,GAAG,CAAC,UAAUC,KAAK,EAAE;QACpB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7B,OAAOA,KAAK;QACd,CAAC,MAAM;UACL,IAAIC,uBAAuB,GAAGD,KAAK,CAACE,OAAO;UAC3C,IAAIC,WAAW;UACf,OACE1B,IAAI,CAAC2B,OAAO,CACTD,WAAW,GAAG9B,cAAc,CAACuB,kBAAkB,EAAE,EAAEhC,QAAQ,CAC9D,CAAC,KAAK,CAAC,CAAC,EACR;YACA;UAAA;UAEFiC,cAAc,CAACM,WAAW,CAAC,GAAGF,uBAAuB;UACrD,OAAOE,WAAW;QACpB;MACF,CAAC,CAAC,CACDE,IAAI,CAAC,EAAE,CAAC,EAAC;MACZ,IAAIC,cAAc,GAAG9B,iBAAiB,CAACsB,YAAY,EAAEpB,OAAO,EAAEd,QAAQ,CAAC,EAAC;MACxE,IAAI2C,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACZ,cAAc,CAAC;MAC9CD,kBAAkB,GAAG,CAAC;MACtB;AACN;AACA;AACA;AACA;MACM,SAASc,UAAUA,CAAC5B,MAAM,EAAE;QAC1B,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,MAAM,CAACK,MAAM,EAAEwB,CAAC,EAAE,EAAE;UACtC,IAAIf,kBAAkB,IAAIW,YAAY,CAACpB,MAAM,EAAE;YAC7C;UACF;UACA,IAAIa,KAAK,GAAGlB,MAAM,CAAC6B,CAAC,CAAC;UACrB,IAAI,OAAOX,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACE,OAAO,KAAK,QAAQ,EAAE;YAClE,IAAIC,WAAW,GAAGI,YAAY,CAACX,kBAAkB,CAAC;YAClD,IAAIgB,CAAC,GACH,OAAOZ,KAAK,KAAK,QAAQ,GACrBA,KAAK,GACL;YACAA,KAAK,CAACE,OAAO;YACnB,IAAIW,KAAK,GAAGD,CAAC,CAACR,OAAO,CAACD,WAAW,CAAC;YAClC,IAAIU,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,EAAEjB,kBAAkB;cACpB,IAAIkB,MAAM,GAAGF,CAAC,CAACG,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC;cAClC,IAAIG,MAAM,GAAGhC,+BAA+B,CAC1Ca,cAAc,CAACM,WAAW,CAC5B,CAAC;cACD,IAAIc,KAAK,GAAGL,CAAC,CAACG,SAAS,CAACF,KAAK,GAAGV,WAAW,CAAChB,MAAM,CAAC;cACnD,IAAI+B,WAAW,GAAG,EAAE;cACpB,IAAIJ,MAAM,EAAE;gBACVI,WAAW,CAAC7B,IAAI,CAACyB,MAAM,CAAC;cAC1B;cACAI,WAAW,CAAC7B,IAAI,CAAC2B,MAAM,CAAC;cACxB,IAAIC,KAAK,EAAE;gBACT,IAAIE,WAAW,GAAG,CAACF,KAAK,CAAC;gBACzBP,UAAU,CAACS,WAAW,CAAC;gBACvBD,WAAW,CAAC7B,IAAI,CAACC,KAAK,CAAC4B,WAAW,EAAEC,WAAW,CAAC;cAClD;cACA,IAAI,OAAOnB,KAAK,KAAK,QAAQ,EAAE;gBAC7BlB,MAAM,CAACS,MAAM,CAACD,KAAK,CAACR,MAAM,EAAE,CAAC6B,CAAC,EAAE,CAAC,CAAC,CAACS,MAAM,CAACF,WAAW,CAAC,CAAC;gBACvDP,CAAC,IAAIO,WAAW,CAAC/B,MAAM,GAAG,CAAC;cAC7B,CAAC,MAAM;gBACLa,KAAK,CAACE,OAAO,GAAGgB,WAAW;cAC7B;YACF;UACF,CAAC,MAAM;YACL,IAAIhB,OAAO,GAAGF,KAAK,CAACE,OAAO;YAC3B,IAAImB,KAAK,CAACC,OAAO,CAACpB,OAAO,CAAC,EAAE;cAC1BQ,UAAU,CAACR,OAAO,CAAC;YACrB,CAAC,MAAM;cACLQ,UAAU,CAAC,CAACR,OAAO,CAAC,CAAC;YACvB;UACF;QACF;MACF;MACAQ,UAAU,CAACJ,cAAc,CAAC;MAC1B,OAAO,IAAItD,KAAK,CAACwC,KAAK,CACpB5B,QAAQ,EACR0C,cAAc,EACd,WAAW,GAAG1C,QAAQ,EACtBa,IACF,CAAC;IACH;IACA;AACJ;AACA;AACA;AACA;IACI,IAAI8C,kBAAkB,GAAG;MACvBpE,UAAU,EAAE,IAAI;MAChBqE,EAAE,EAAE,IAAI;MACRC,UAAU,EAAE,IAAI;MAChBC,EAAE,EAAE,IAAI;MACRC,GAAG,EAAE,IAAI;MACTC,GAAG,EAAE;IACP,CAAC;IACD5E,KAAK,CAAC4B,KAAK,CAACiD,GAAG,CAAC,gBAAgB,EAAE,UAAUlD,GAAG,EAAE;MAC/C,IAAI,EAAEA,GAAG,CAACf,QAAQ,IAAI2D,kBAAkB,CAAC,EAAE;QACzC;MACF;MACA;AACN;AACA;AACA;AACA;AACA;MACM,SAASO,mBAAmBA,CAAChD,MAAM,EAAE;QACnC,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEoB,CAAC,GAAGjD,MAAM,CAACK,MAAM,EAAEwB,CAAC,GAAGoB,CAAC,EAAEpB,CAAC,EAAE,EAAE;UAC7C,IAAIX,KAAK,GAAGlB,MAAM,CAAC6B,CAAC,CAAC;UACrB,IAAI,OAAOX,KAAK,KAAK,QAAQ,EAAE;YAC7B;UACF;UACA,IAAIE,OAAO,GAAGF,KAAK,CAACE,OAAO;UAC3B,IAAI,CAACmB,KAAK,CAACC,OAAO,CAACpB,OAAO,CAAC,EAAE;YAC3B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;cAC/B4B,mBAAmB,CAAC,CAAC5B,OAAO,CAAC,CAAC;YAChC;YACA;UACF;UACA,IAAIF,KAAK,CAACgC,IAAI,KAAK,iBAAiB,EAAE;YACpC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACY,IAAIC,QAAQ,GAAG/B,OAAO,CAAC,CAAC,CAAC;YACzB,IACEA,OAAO,CAACf,MAAM,KAAK,CAAC,IACpB,OAAO8C,QAAQ,KAAK,QAAQ,IAC5BA,QAAQ,CAACD,IAAI,KAAK,eAAe,EACjC;cACA;cACA,IAAIvD,IAAI,GAAGyD,aAAa,CAACD,QAAQ,CAAC;cAClC,IAAI/D,KAAK,GAAG+D,QAAQ,CAAC/D,KAAK;cAC1B,IAAIN,QAAQ,GAAGyD,KAAK,CAACC,OAAO,CAACpD,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;cACtD,IAAIQ,OAAO,GAAG1B,KAAK,CAACE,SAAS,CAACU,QAAQ,CAAC;cACvC,IAAI,CAACc,OAAO,EAAE;gBACZ;gBACA;cACF;cACAwB,OAAO,CAAC,CAAC,CAAC,GAAGT,gBAAgB,CAAChB,IAAI,EAAEC,OAAO,EAAEd,QAAQ,CAAC;YACxD;UACF,CAAC,MAAM;YACLkE,mBAAmB,CAAC5B,OAAO,CAAC;UAC9B;QACF;MACF;MACA4B,mBAAmB,CAACnD,GAAG,CAACG,MAAM,CAAC;IACjC,CAAC,CAAC;IACF;AACJ;AACA;AACA;AACA;AACA;IACI,SAASoD,aAAaA,CAACC,KAAK,EAAE;MAC5B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOA,KAAK;MACd,CAAC,MAAM,IAAId,KAAK,CAACC,OAAO,CAACa,KAAK,CAAC,EAAE;QAC/B,OAAOA,KAAK,CAACpC,GAAG,CAACmC,aAAa,CAAC,CAAC7B,IAAI,CAAC,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,OAAO6B,aAAa,CAACC,KAAK,CAACjC,OAAO,CAAC;MACrC;IACF;EACF,CAAC,EAAElD,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}