{"ast": null, "code": "/*\nLanguage: AngelScript\nAuthor: <PERSON> <<EMAIL>>\nCategory: scripting\nWebsite: https://www.angelcode.com/angelscript/\n*/\n\n/** @type LanguageFn */\nfunction angelscript(hljs) {\n  var builtInTypeMode = {\n    className: 'built_in',\n    begin: '\\\\b(void|bool|int|int8|int16|int32|int64|uint|uint8|uint16|uint32|uint64|string|ref|array|double|float|auto|dictionary)'\n  };\n  var objectHandleMode = {\n    className: 'symbol',\n    begin: '[a-zA-Z0-9_]+@'\n  };\n  var genericMode = {\n    className: 'keyword',\n    begin: '<',\n    end: '>',\n    contains: [builtInTypeMode, objectHandleMode]\n  };\n  builtInTypeMode.contains = [genericMode];\n  objectHandleMode.contains = [genericMode];\n  return {\n    name: 'AngelScript',\n    aliases: ['asc'],\n    keywords: 'for in|0 break continue while do|0 return if else case switch namespace is cast ' + 'or and xor not get|0 in inout|10 out override set|0 private public const default|0 ' + 'final shared external mixin|10 enum typedef funcdef this super import from interface ' + 'abstract|0 try catch protected explicit property',\n    // avoid close detection with C# and JS\n    illegal: '(^using\\\\s+[A-Za-z0-9_\\\\.]+;$|\\\\bfunction\\\\s*[^\\\\(])',\n    contains: [{\n      // 'strings'\n      className: 'string',\n      begin: '\\'',\n      end: '\\'',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE],\n      relevance: 0\n    },\n    // \"\"\"heredoc strings\"\"\"\n    {\n      className: 'string',\n      begin: '\"\"\"',\n      end: '\"\"\"'\n    }, {\n      // \"strings\"\n      className: 'string',\n      begin: '\"',\n      end: '\"',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE],\n      relevance: 0\n    }, hljs.C_LINE_COMMENT_MODE,\n    // single-line comments\n    hljs.C_BLOCK_COMMENT_MODE,\n    // comment blocks\n\n    {\n      // metadata\n      className: 'string',\n      begin: '^\\\\s*\\\\[',\n      end: '\\\\]'\n    }, {\n      // interface or namespace declaration\n      beginKeywords: 'interface namespace',\n      end: /\\{/,\n      illegal: '[;.\\\\-]',\n      contains: [{\n        // interface or namespace name\n        className: 'symbol',\n        begin: '[a-zA-Z0-9_]+'\n      }]\n    }, {\n      // class declaration\n      beginKeywords: 'class',\n      end: /\\{/,\n      illegal: '[;.\\\\-]',\n      contains: [{\n        // class name\n        className: 'symbol',\n        begin: '[a-zA-Z0-9_]+',\n        contains: [{\n          begin: '[:,]\\\\s*',\n          contains: [{\n            className: 'symbol',\n            begin: '[a-zA-Z0-9_]+'\n          }]\n        }]\n      }]\n    }, builtInTypeMode,\n    // built-in types\n    objectHandleMode,\n    // object handles\n\n    {\n      // literals\n      className: 'literal',\n      begin: '\\\\b(null|true|false)'\n    }, {\n      // numbers\n      className: 'number',\n      relevance: 0,\n      begin: '(-?)(\\\\b0[xXbBoOdD][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?f?|\\\\.\\\\d+f?)([eE][-+]?\\\\d+f?)?)'\n    }]\n  };\n}\nmodule.exports = angelscript;", "map": {"version": 3, "names": ["angelscript", "hljs", "builtInTypeMode", "className", "begin", "objectHandleMode", "genericMode", "end", "contains", "name", "aliases", "keywords", "illegal", "BACKSLASH_ESCAPE", "relevance", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "beginKeywords", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/angelscript.js"], "sourcesContent": ["/*\nLanguage: AngelScript\nAuthor: <PERSON> <<EMAIL>>\nCategory: scripting\nWebsite: https://www.angelcode.com/angelscript/\n*/\n\n/** @type LanguageFn */\nfunction angelscript(hljs) {\n  var builtInTypeMode = {\n    className: 'built_in',\n    begin: '\\\\b(void|bool|int|int8|int16|int32|int64|uint|uint8|uint16|uint32|uint64|string|ref|array|double|float|auto|dictionary)'\n  };\n\n  var objectHandleMode = {\n    className: 'symbol',\n    begin: '[a-zA-Z0-9_]+@'\n  };\n\n  var genericMode = {\n    className: 'keyword',\n    begin: '<', end: '>',\n    contains: [ builtInTypeMode, objectHandleMode ]\n  };\n\n  builtInTypeMode.contains = [ genericMode ];\n  objectHandleMode.contains = [ genericMode ];\n\n  return {\n    name: 'AngelScript',\n    aliases: ['asc'],\n\n    keywords:\n      'for in|0 break continue while do|0 return if else case switch namespace is cast ' +\n      'or and xor not get|0 in inout|10 out override set|0 private public const default|0 ' +\n      'final shared external mixin|10 enum typedef funcdef this super import from interface ' +\n      'abstract|0 try catch protected explicit property',\n\n    // avoid close detection with C# and JS\n    illegal: '(^using\\\\s+[A-Za-z0-9_\\\\.]+;$|\\\\bfunction\\\\s*[^\\\\(])',\n\n    contains: [\n      { // 'strings'\n        className: 'string',\n        begin: '\\'', end: '\\'',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        relevance: 0\n      },\n\n      // \"\"\"heredoc strings\"\"\"\n      {\n        className: 'string',\n        begin: '\"\"\"', end: '\"\"\"'\n      },\n\n      { // \"strings\"\n        className: 'string',\n        begin: '\"', end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        relevance: 0\n      },\n\n      hljs.C_LINE_COMMENT_MODE, // single-line comments\n      hljs.C_BLOCK_COMMENT_MODE, // comment blocks\n\n      { // metadata\n        className: 'string',\n        begin: '^\\\\s*\\\\[', end: '\\\\]',\n      },\n\n      { // interface or namespace declaration\n        beginKeywords: 'interface namespace', end: /\\{/,\n        illegal: '[;.\\\\-]',\n        contains: [\n          { // interface or namespace name\n            className: 'symbol',\n            begin: '[a-zA-Z0-9_]+'\n          }\n        ]\n      },\n\n      { // class declaration\n        beginKeywords: 'class', end: /\\{/,\n        illegal: '[;.\\\\-]',\n        contains: [\n          { // class name\n            className: 'symbol',\n            begin: '[a-zA-Z0-9_]+',\n            contains: [\n              {\n                begin: '[:,]\\\\s*',\n                contains: [\n                  {\n                    className: 'symbol',\n                    begin: '[a-zA-Z0-9_]+'\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      },\n\n      builtInTypeMode, // built-in types\n      objectHandleMode, // object handles\n\n      { // literals\n        className: 'literal',\n        begin: '\\\\b(null|true|false)'\n      },\n\n      { // numbers\n        className: 'number',\n        relevance: 0,\n        begin: '(-?)(\\\\b0[xXbBoOdD][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?f?|\\\\.\\\\d+f?)([eE][-+]?\\\\d+f?)?)'\n      }\n    ]\n  };\n}\n\nmodule.exports = angelscript;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,eAAe,GAAG;IACpBC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;EAED,IAAIC,gBAAgB,GAAG;IACrBF,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,IAAIE,WAAW,GAAG;IAChBH,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,GAAG;IAAEG,GAAG,EAAE,GAAG;IACpBC,QAAQ,EAAE,CAAEN,eAAe,EAAEG,gBAAgB;EAC/C,CAAC;EAEDH,eAAe,CAACM,QAAQ,GAAG,CAAEF,WAAW,CAAE;EAC1CD,gBAAgB,CAACG,QAAQ,GAAG,CAAEF,WAAW,CAAE;EAE3C,OAAO;IACLG,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,CAAC,KAAK,CAAC;IAEhBC,QAAQ,EACN,kFAAkF,GAClF,qFAAqF,GACrF,uFAAuF,GACvF,kDAAkD;IAEpD;IACAC,OAAO,EAAE,sDAAsD;IAE/DJ,QAAQ,EAAE,CACR;MAAE;MACAL,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,IAAI;MAAEG,GAAG,EAAE,IAAI;MACtBK,OAAO,EAAE,KAAK;MACdJ,QAAQ,EAAE,CAAEP,IAAI,CAACY,gBAAgB,CAAE;MACnCC,SAAS,EAAE;IACb,CAAC;IAED;IACA;MACEX,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,KAAK;MAAEG,GAAG,EAAE;IACrB,CAAC,EAED;MAAE;MACAJ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MAAEG,GAAG,EAAE,GAAG;MACpBK,OAAO,EAAE,KAAK;MACdJ,QAAQ,EAAE,CAAEP,IAAI,CAACY,gBAAgB,CAAE;MACnCC,SAAS,EAAE;IACb,CAAC,EAEDb,IAAI,CAACc,mBAAmB;IAAE;IAC1Bd,IAAI,CAACe,oBAAoB;IAAE;;IAE3B;MAAE;MACAb,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,UAAU;MAAEG,GAAG,EAAE;IAC1B,CAAC,EAED;MAAE;MACAU,aAAa,EAAE,qBAAqB;MAAEV,GAAG,EAAE,IAAI;MAC/CK,OAAO,EAAE,SAAS;MAClBJ,QAAQ,EAAE,CACR;QAAE;QACAL,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EAED;MAAE;MACAa,aAAa,EAAE,OAAO;MAAEV,GAAG,EAAE,IAAI;MACjCK,OAAO,EAAE,SAAS;MAClBJ,QAAQ,EAAE,CACR;QAAE;QACAL,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,eAAe;QACtBI,QAAQ,EAAE,CACR;UACEJ,KAAK,EAAE,UAAU;UACjBI,QAAQ,EAAE,CACR;YACEL,SAAS,EAAE,QAAQ;YACnBC,KAAK,EAAE;UACT,CAAC;QAEL,CAAC;MAEL,CAAC;IAEL,CAAC,EAEDF,eAAe;IAAE;IACjBG,gBAAgB;IAAE;;IAElB;MAAE;MACAF,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EAED;MAAE;MACAD,SAAS,EAAE,QAAQ;MACnBW,SAAS,EAAE,CAAC;MACZV,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAc,MAAM,CAACC,OAAO,GAAGnB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}