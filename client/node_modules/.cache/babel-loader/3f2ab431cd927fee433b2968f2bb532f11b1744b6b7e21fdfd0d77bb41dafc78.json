{"ast": null, "code": "'use strict';\n\n/* eslint-env browser */\nvar el;\nvar semicolon = 59; //  ';'\n\nmodule.exports = decodeEntity;\nfunction decodeEntity(characters) {\n  var entity = '&' + characters + ';';\n  var char;\n  el = el || document.createElement('i');\n  el.innerHTML = entity;\n  char = el.textContent;\n\n  // Some entities do not require the closing semicolon (`&not` - for instance),\n  // which leads to situations where parsing the assumed entity of &notit; will\n  // result in the string `¬it;`.  When we encounter a trailing semicolon after\n  // parsing and the entity to decode was not a semicolon (`&semi;`), we can\n  // assume that the matching was incomplete\n  if (char.charCodeAt(char.length - 1) === semicolon && characters !== 'semi') {\n    return false;\n  }\n\n  // If the decoded string is equal to the input, the entity was not valid\n  return char === entity ? false : char;\n}", "map": {"version": 3, "names": ["el", "semicolon", "module", "exports", "decodeEntity", "characters", "entity", "char", "document", "createElement", "innerHTML", "textContent", "charCodeAt", "length"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/parse-entities/decode-entity.browser.js"], "sourcesContent": ["'use strict'\n\n/* eslint-env browser */\n\nvar el\n\nvar semicolon = 59 //  ';'\n\nmodule.exports = decodeEntity\n\nfunction decodeEntity(characters) {\n  var entity = '&' + characters + ';'\n  var char\n\n  el = el || document.createElement('i')\n  el.innerHTML = entity\n  char = el.textContent\n\n  // Some entities do not require the closing semicolon (`&not` - for instance),\n  // which leads to situations where parsing the assumed entity of &notit; will\n  // result in the string `¬it;`.  When we encounter a trailing semicolon after\n  // parsing and the entity to decode was not a semicolon (`&semi;`), we can\n  // assume that the matching was incomplete\n  if (char.charCodeAt(char.length - 1) === semicolon && characters !== 'semi') {\n    return false\n  }\n\n  // If the decoded string is equal to the input, the entity was not valid\n  return char === entity ? false : char\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AAEA,IAAIA,EAAE;AAEN,IAAIC,SAAS,GAAG,EAAE,EAAC;;AAEnBC,MAAM,CAACC,OAAO,GAAGC,YAAY;AAE7B,SAASA,YAAYA,CAACC,UAAU,EAAE;EAChC,IAAIC,MAAM,GAAG,GAAG,GAAGD,UAAU,GAAG,GAAG;EACnC,IAAIE,IAAI;EAERP,EAAE,GAAGA,EAAE,IAAIQ,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACtCT,EAAE,CAACU,SAAS,GAAGJ,MAAM;EACrBC,IAAI,GAAGP,EAAE,CAACW,WAAW;;EAErB;EACA;EACA;EACA;EACA;EACA,IAAIJ,IAAI,CAACK,UAAU,CAACL,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC,KAAKZ,SAAS,IAAII,UAAU,KAAK,MAAM,EAAE;IAC3E,OAAO,KAAK;EACd;;EAEA;EACA,OAAOE,IAAI,KAAKD,MAAM,GAAG,KAAK,GAAGC,IAAI;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}