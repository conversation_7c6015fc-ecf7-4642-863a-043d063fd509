{"ast": null, "code": "/*\nLanguage: Roboconf\nAuthor: <PERSON> <v<PERSON><PERSON><PERSON>@linagora.com>\nDescription: Syntax highlighting for Roboconf's DSL\nWebsite: http://roboconf.net\nCategory: config\n*/\n\nfunction roboconf(hljs) {\n  const IDENTIFIER = '[a-zA-Z-_][^\\\\n{]+\\\\{';\n  const PROPERTY = {\n    className: 'attribute',\n    begin: /[a-zA-Z-_]+/,\n    end: /\\s*:/,\n    excludeEnd: true,\n    starts: {\n      end: ';',\n      relevance: 0,\n      contains: [{\n        className: 'variable',\n        begin: /\\.[a-zA-Z-_]+/\n      }, {\n        className: 'keyword',\n        begin: /\\(optional\\)/\n      }]\n    }\n  };\n  return {\n    name: 'Roboconf',\n    aliases: ['graph', 'instances'],\n    case_insensitive: true,\n    keywords: 'import',\n    contains: [\n    // Facet sections\n    {\n      begin: '^facet ' + IDENTIFIER,\n      end: /\\}/,\n      keywords: 'facet',\n      contains: [PROPERTY, hljs.HASH_COMMENT_MODE]\n    },\n    // Instance sections\n    {\n      begin: '^\\\\s*instance of ' + IDENTIFIER,\n      end: /\\}/,\n      keywords: 'name count channels instance-data instance-state instance of',\n      illegal: /\\S/,\n      contains: ['self', PROPERTY, hljs.HASH_COMMENT_MODE]\n    },\n    // Component sections\n    {\n      begin: '^' + IDENTIFIER,\n      end: /\\}/,\n      contains: [PROPERTY, hljs.HASH_COMMENT_MODE]\n    },\n    // Comments\n    hljs.HASH_COMMENT_MODE]\n  };\n}\nmodule.exports = roboconf;", "map": {"version": 3, "names": ["roboconf", "hljs", "IDENTIFIER", "PROPERTY", "className", "begin", "end", "excludeEnd", "starts", "relevance", "contains", "name", "aliases", "case_insensitive", "keywords", "HASH_COMMENT_MODE", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/roboconf.js"], "sourcesContent": ["/*\nLanguage: Roboconf\nAuthor: <PERSON> <v<PERSON><PERSON><PERSON>@linagora.com>\nDescription: Syntax highlighting for Roboconf's DSL\nWebsite: http://roboconf.net\nCategory: config\n*/\n\nfunction roboconf(hljs) {\n  const IDENTIFIER = '[a-zA-Z-_][^\\\\n{]+\\\\{';\n\n  const PROPERTY = {\n    className: 'attribute',\n    begin: /[a-zA-Z-_]+/,\n    end: /\\s*:/,\n    excludeEnd: true,\n    starts: {\n      end: ';',\n      relevance: 0,\n      contains: [\n        {\n          className: 'variable',\n          begin: /\\.[a-zA-Z-_]+/\n        },\n        {\n          className: 'keyword',\n          begin: /\\(optional\\)/\n        }\n      ]\n    }\n  };\n\n  return {\n    name: 'Roboconf',\n    aliases: [\n      'graph',\n      'instances'\n    ],\n    case_insensitive: true,\n    keywords: 'import',\n    contains: [\n      // Facet sections\n      {\n        begin: '^facet ' + IDENTIFIER,\n        end: /\\}/,\n        keywords: 'facet',\n        contains: [\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Instance sections\n      {\n        begin: '^\\\\s*instance of ' + IDENTIFIER,\n        end: /\\}/,\n        keywords: 'name count channels instance-data instance-state instance of',\n        illegal: /\\S/,\n        contains: [\n          'self',\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Component sections\n      {\n        begin: '^' + IDENTIFIER,\n        end: /\\}/,\n        contains: [\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Comments\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = roboconf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,UAAU,GAAG,uBAAuB;EAE1C,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE,MAAM;IACXC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE;MACNF,GAAG,EAAE,GAAG;MACRG,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CACR;QACEN,SAAS,EAAE,UAAU;QACrBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE;MACT,CAAC;IAEL;EACF,CAAC;EAED,OAAO;IACLM,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CACP,OAAO,EACP,WAAW,CACZ;IACDC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,QAAQ;IAClBJ,QAAQ,EAAE;IACR;IACA;MACEL,KAAK,EAAE,SAAS,GAAGH,UAAU;MAC7BI,GAAG,EAAE,IAAI;MACTQ,QAAQ,EAAE,OAAO;MACjBJ,QAAQ,EAAE,CACRP,QAAQ,EACRF,IAAI,CAACc,iBAAiB;IAE1B,CAAC;IAED;IACA;MACEV,KAAK,EAAE,mBAAmB,GAAGH,UAAU;MACvCI,GAAG,EAAE,IAAI;MACTQ,QAAQ,EAAE,8DAA8D;MACxEE,OAAO,EAAE,IAAI;MACbN,QAAQ,EAAE,CACR,MAAM,EACNP,QAAQ,EACRF,IAAI,CAACc,iBAAiB;IAE1B,CAAC;IAED;IACA;MACEV,KAAK,EAAE,GAAG,GAAGH,UAAU;MACvBI,GAAG,EAAE,IAAI;MACTI,QAAQ,EAAE,CACRP,QAAQ,EACRF,IAAI,CAACc,iBAAiB;IAE1B,CAAC;IAED;IACAd,IAAI,CAACc,iBAAiB;EAE1B,CAAC;AACH;AAEAE,MAAM,CAACC,OAAO,GAAGlB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}