{"ast": null, "code": "'use strict';\n\nvar refractorJsx = require('./jsx.js');\nvar refractorTypescript = require('./typescript.js');\nmodule.exports = tsx;\ntsx.displayName = 'tsx';\ntsx.aliases = [];\nfunction tsx(Prism) {\n  Prism.register(refractorJsx);\n  Prism.register(refractorTypescript);\n  (function (Prism) {\n    var typescript = Prism.util.clone(Prism.languages.typescript);\n    Prism.languages.tsx = Prism.languages.extend('jsx', typescript); // doesn't work with TS because TS is too complex\n    delete Prism.languages.tsx['parameter'];\n    delete Prism.languages.tsx['literal-property']; // This will prevent collisions between TSX tags and TS generic types.\n    // Idea by https://github.com/karlhorky\n    // Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-710666928\n    var tag = Prism.languages.tsx.tag;\n    tag.pattern = RegExp(/(^|[^\\w$]|(?=<\\/))/.source + '(?:' + tag.pattern.source + ')', tag.pattern.flags);\n    tag.lookbehind = true;\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorJsx", "require", "refractorTypescript", "module", "exports", "tsx", "displayName", "aliases", "Prism", "register", "typescript", "util", "clone", "languages", "extend", "tag", "pattern", "RegExp", "source", "flags", "lookbehind"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/tsx.js"], "sourcesContent": ["'use strict'\nvar refractorJsx = require('./jsx.js')\nvar refractorTypescript = require('./typescript.js')\nmodule.exports = tsx\ntsx.displayName = 'tsx'\ntsx.aliases = []\nfunction tsx(Prism) {\n  Prism.register(refractorJsx)\n  Prism.register(refractorTypescript)\n  ;(function (Prism) {\n    var typescript = Prism.util.clone(Prism.languages.typescript)\n    Prism.languages.tsx = Prism.languages.extend('jsx', typescript) // doesn't work with TS because TS is too complex\n    delete Prism.languages.tsx['parameter']\n    delete Prism.languages.tsx['literal-property'] // This will prevent collisions between TSX tags and TS generic types.\n    // Idea by https://github.com/karlhorky\n    // Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-710666928\n    var tag = Prism.languages.tsx.tag\n    tag.pattern = RegExp(\n      /(^|[^\\w$]|(?=<\\/))/.source + '(?:' + tag.pattern.source + ')',\n      tag.pattern.flags\n    )\n    tag.lookbehind = true\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,UAAU,CAAC;AACtC,IAAIC,mBAAmB,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AACpDE,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACT,YAAY,CAAC;EAC5BQ,KAAK,CAACC,QAAQ,CAACP,mBAAmB,CAAC;EAClC,CAAC,UAAUM,KAAK,EAAE;IACjB,IAAIE,UAAU,GAAGF,KAAK,CAACG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAACK,SAAS,CAACH,UAAU,CAAC;IAC7DF,KAAK,CAACK,SAAS,CAACR,GAAG,GAAGG,KAAK,CAACK,SAAS,CAACC,MAAM,CAAC,KAAK,EAAEJ,UAAU,CAAC,EAAC;IAChE,OAAOF,KAAK,CAACK,SAAS,CAACR,GAAG,CAAC,WAAW,CAAC;IACvC,OAAOG,KAAK,CAACK,SAAS,CAACR,GAAG,CAAC,kBAAkB,CAAC,EAAC;IAC/C;IACA;IACA,IAAIU,GAAG,GAAGP,KAAK,CAACK,SAAS,CAACR,GAAG,CAACU,GAAG;IACjCA,GAAG,CAACC,OAAO,GAAGC,MAAM,CAClB,oBAAoB,CAACC,MAAM,GAAG,KAAK,GAAGH,GAAG,CAACC,OAAO,CAACE,MAAM,GAAG,GAAG,EAC9DH,GAAG,CAACC,OAAO,CAACG,KACd,CAAC;IACDJ,GAAG,CAACK,UAAU,GAAG,IAAI;EACvB,CAAC,EAAEZ,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}