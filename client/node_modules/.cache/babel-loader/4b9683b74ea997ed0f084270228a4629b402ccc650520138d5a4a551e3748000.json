{"ast": null, "code": "'use strict';\n\nvar refractorJson = require('./json.js');\nmodule.exports = jsonp;\njsonp.displayName = 'jsonp';\njsonp.aliases = [];\nfunction jsonp(Prism) {\n  Prism.register(refractorJson);\n  Prism.languages.jsonp = Prism.languages.extend('json', {\n    punctuation: /[{}[\\]();,.]/\n  });\n  Prism.languages.insertBefore('jsonp', 'punctuation', {\n    function: /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*\\()/\n  });\n}", "map": {"version": 3, "names": ["refractor<PERSON><PERSON>", "require", "module", "exports", "jsonp", "displayName", "aliases", "Prism", "register", "languages", "extend", "punctuation", "insertBefore", "function"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/jsonp.js"], "sourcesContent": ["'use strict'\nvar refractorJson = require('./json.js')\nmodule.exports = jsonp\njsonp.displayName = 'jsonp'\njsonp.aliases = []\nfunction jsonp(Prism) {\n  Prism.register(refractorJson)\n  Prism.languages.jsonp = Prism.languages.extend('json', {\n    punctuation: /[{}[\\]();,.]/\n  })\n  Prism.languages.insertBefore('jsonp', 'punctuation', {\n    function: /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*\\()/\n  })\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxCC,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACR,aAAa,CAAC;EAC7BO,KAAK,CAACE,SAAS,CAACL,KAAK,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE;IACrDC,WAAW,EAAE;EACf,CAAC,CAAC;EACFJ,KAAK,CAACE,SAAS,CAACG,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE;IACnDC,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}