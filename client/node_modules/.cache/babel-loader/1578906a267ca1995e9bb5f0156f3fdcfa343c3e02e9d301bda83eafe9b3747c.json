{"ast": null, "code": "'use strict';\n\nmodule.exports = xeora;\nxeora.displayName = 'xeora';\nxeora.aliases = ['xeoracube'];\nfunction xeora(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.xeora = Prism.languages.extend('markup', {\n      constant: {\n        pattern: /\\$(?:DomainContents|PageRenderDuration)\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$/\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$@?(?:#+|[-+*~=^])?[\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$.]/\n          },\n          operator: {\n            pattern: /#+|[-+*~=^@]/\n          }\n        }\n      },\n      'function-inline': {\n        pattern: /\\$F:[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\$/,\n        inside: {\n          variable: {\n            pattern: /(?:[,|])@?(?:#+|[-+*~=^])?[\\w.]+/,\n            inside: {\n              punctuation: {\n                pattern: /[,.|]/\n              },\n              operator: {\n                pattern: /#+|[-+*~=^@]/\n              }\n            }\n          },\n          punctuation: {\n            pattern: /\\$\\w:|[$:?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'function-block': {\n        pattern: /\\$XF:\\{[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\}:XF\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$:{}?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-inline': {\n        pattern: /\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\/\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-open': {\n        pattern: /\\$\\w+:\\{|\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\w.]+:\\{(?:![A-Z]+)?/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          },\n          attribute: {\n            pattern: /![A-Z]+$/,\n            inside: {\n              punctuation: {\n                pattern: /!/\n              }\n            },\n            alias: 'keyword'\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-separator': {\n        pattern: /\\}:[-\\w.]+:\\{/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-close': {\n        pattern: /\\}:[-\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}$]/\n          }\n        },\n        alias: 'function'\n      }\n    });\n    Prism.languages.insertBefore('inside', 'punctuation', {\n      variable: Prism.languages.xeora['function-inline'].inside['variable']\n    }, Prism.languages.xeora['function-block']);\n    Prism.languages.xeoracube = Prism.languages.xeora;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "xeora", "displayName", "aliases", "Prism", "languages", "extend", "constant", "pattern", "inside", "punctuation", "variable", "operator", "alias", "tag", "attribute", "insertBefore", "xeoracube"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/xeora.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = xeora\nxeora.displayName = 'xeora'\nxeora.aliases = ['xeoracube']\nfunction xeora(Prism) {\n  ;(function (Prism) {\n    Prism.languages.xeora = Prism.languages.extend('markup', {\n      constant: {\n        pattern: /\\$(?:DomainContents|PageRenderDuration)\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$/\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$@?(?:#+|[-+*~=^])?[\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$.]/\n          },\n          operator: {\n            pattern: /#+|[-+*~=^@]/\n          }\n        }\n      },\n      'function-inline': {\n        pattern:\n          /\\$F:[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\$/,\n        inside: {\n          variable: {\n            pattern: /(?:[,|])@?(?:#+|[-+*~=^])?[\\w.]+/,\n            inside: {\n              punctuation: {\n                pattern: /[,.|]/\n              },\n              operator: {\n                pattern: /#+|[-+*~=^@]/\n              }\n            }\n          },\n          punctuation: {\n            pattern: /\\$\\w:|[$:?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'function-block': {\n        pattern:\n          /\\$XF:\\{[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\}:XF\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$:{}?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-inline': {\n        pattern: /\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\/\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-open': {\n        pattern:\n          /\\$\\w+:\\{|\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\w.]+:\\{(?:![A-Z]+)?/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          },\n          attribute: {\n            pattern: /![A-Z]+$/,\n            inside: {\n              punctuation: {\n                pattern: /!/\n              }\n            },\n            alias: 'keyword'\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-separator': {\n        pattern: /\\}:[-\\w.]+:\\{/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-close': {\n        pattern: /\\}:[-\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}$]/\n          }\n        },\n        alias: 'function'\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'punctuation',\n      {\n        variable: Prism.languages.xeora['function-inline'].inside['variable']\n      },\n      Prism.languages.xeora['function-block']\n    )\n    Prism.languages.xeoracube = Prism.languages.xeora\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,CAAC,WAAW,CAAC;AAC7B,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;MACvDC,QAAQ,EAAE;QACRC,OAAO,EAAE,2CAA2C;QACpDC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXF,OAAO,EAAE;UACX;QACF;MACF,CAAC;MACDG,QAAQ,EAAE;QACRH,OAAO,EAAE,8BAA8B;QACvCC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXF,OAAO,EAAE;UACX,CAAC;UACDI,QAAQ,EAAE;YACRJ,OAAO,EAAE;UACX;QACF;MACF,CAAC;MACD,iBAAiB,EAAE;QACjBA,OAAO,EACL,uMAAuM;QACzMC,MAAM,EAAE;UACNE,QAAQ,EAAE;YACRH,OAAO,EAAE,kCAAkC;YAC3CC,MAAM,EAAE;cACNC,WAAW,EAAE;gBACXF,OAAO,EAAE;cACX,CAAC;cACDI,QAAQ,EAAE;gBACRJ,OAAO,EAAE;cACX;YACF;UACF,CAAC;UACDE,WAAW,EAAE;YACXF,OAAO,EAAE;UACX;QACF,CAAC;QACDK,KAAK,EAAE;MACT,CAAC;MACD,gBAAgB,EAAE;QAChBL,OAAO,EACL,+MAA+M;QACjNC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXF,OAAO,EAAE;UACX;QACF,CAAC;QACDK,KAAK,EAAE;MACT,CAAC;MACD,kBAAkB,EAAE;QAClBL,OAAO,EAAE,8CAA8C;QACvDC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXF,OAAO,EAAE,gCAAgC;YACzCC,MAAM,EAAE;cACNK,GAAG,EAAE;gBACHN,OAAO,EAAE;cACX;YACF;UACF;QACF,CAAC;QACDK,KAAK,EAAE;MACT,CAAC;MACD,sBAAsB,EAAE;QACtBL,OAAO,EACL,kEAAkE;QACpEC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXF,OAAO,EAAE,gCAAgC;YACzCC,MAAM,EAAE;cACNK,GAAG,EAAE;gBACHN,OAAO,EAAE;cACX;YACF;UACF,CAAC;UACDO,SAAS,EAAE;YACTP,OAAO,EAAE,UAAU;YACnBC,MAAM,EAAE;cACNC,WAAW,EAAE;gBACXF,OAAO,EAAE;cACX;YACF,CAAC;YACDK,KAAK,EAAE;UACT;QACF,CAAC;QACDA,KAAK,EAAE;MACT,CAAC;MACD,2BAA2B,EAAE;QAC3BL,OAAO,EAAE,eAAe;QACxBC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXF,OAAO,EAAE;UACX;QACF,CAAC;QACDK,KAAK,EAAE;MACT,CAAC;MACD,uBAAuB,EAAE;QACvBL,OAAO,EAAE,cAAc;QACvBC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXF,OAAO,EAAE;UACX;QACF,CAAC;QACDK,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFT,KAAK,CAACC,SAAS,CAACW,YAAY,CAC1B,QAAQ,EACR,aAAa,EACb;MACEL,QAAQ,EAAEP,KAAK,CAACC,SAAS,CAACJ,KAAK,CAAC,iBAAiB,CAAC,CAACQ,MAAM,CAAC,UAAU;IACtE,CAAC,EACDL,KAAK,CAACC,SAAS,CAACJ,KAAK,CAAC,gBAAgB,CACxC,CAAC;IACDG,KAAK,CAACC,SAAS,CAACY,SAAS,GAAGb,KAAK,CAACC,SAAS,CAACJ,KAAK;EACnD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}