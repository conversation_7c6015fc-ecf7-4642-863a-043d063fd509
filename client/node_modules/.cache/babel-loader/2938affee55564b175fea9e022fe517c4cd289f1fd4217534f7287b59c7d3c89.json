{"ast": null, "code": "'use strict';\n\nvar refractorCpp = require('./cpp.js');\nmodule.exports = chaiscript;\nchaiscript.displayName = 'chaiscript';\nchaiscript.aliases = [];\nfunction chaiscript(Prism) {\n  Prism.register(refractorCpp);\n  Prism.languages.chaiscript = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': [{\n      // e.g. class Rectangle { ... }\n      pattern: /(\\bclass\\s+)\\w+/,\n      lookbehind: true\n    }, {\n      // e.g. attr Rectangle::height, def Rectangle::area() { ... }\n      pattern: /(\\b(?:attr|def)\\s+)\\w+(?=\\s*::)/,\n      lookbehind: true\n    }],\n    keyword: /\\b(?:attr|auto|break|case|catch|class|continue|def|default|else|finally|for|fun|global|if|return|switch|this|try|var|while)\\b/,\n    number: [Prism.languages.cpp.number, /\\b(?:Infinity|NaN)\\b/],\n    operator: />>=?|<<=?|\\|\\||&&|:[:=]?|--|\\+\\+|[=!<>+\\-*/%|&^]=?|[?~]|`[^`\\r\\n]{1,4}`/\n  });\n  Prism.languages.insertBefore('chaiscript', 'operator', {\n    'parameter-type': {\n      // e.g. def foo(int x, Vector y) {...}\n      pattern: /([,(]\\s*)\\w+(?=\\s+\\w)/,\n      lookbehind: true,\n      alias: 'class-name'\n    }\n  });\n  Prism.languages.insertBefore('chaiscript', 'string', {\n    'string-interpolation': {\n      pattern: /(^|[^\\\\])\"(?:[^\"$\\\\]|\\\\[\\s\\S]|\\$(?!\\{)|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\}/,\n          lookbehind: true,\n          inside: {\n            'interpolation-expression': {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              inside: Prism.languages.chaiscript\n            },\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["refractorCpp", "require", "module", "exports", "chaiscript", "displayName", "aliases", "Prism", "register", "languages", "extend", "string", "pattern", "lookbehind", "greedy", "keyword", "number", "cpp", "operator", "insertBefore", "alias", "inside", "interpolation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/chaiscript.js"], "sourcesContent": ["'use strict'\nvar refractorCpp = require('./cpp.js')\nmodule.exports = chaiscript\nchaiscript.displayName = 'chaiscript'\nchaiscript.aliases = []\nfunction chaiscript(Prism) {\n  Prism.register(refractorCpp)\n  Prism.languages.chaiscript = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': [\n      {\n        // e.g. class Rectangle { ... }\n        pattern: /(\\bclass\\s+)\\w+/,\n        lookbehind: true\n      },\n      {\n        // e.g. attr Rectangle::height, def Rectangle::area() { ... }\n        pattern: /(\\b(?:attr|def)\\s+)\\w+(?=\\s*::)/,\n        lookbehind: true\n      }\n    ],\n    keyword:\n      /\\b(?:attr|auto|break|case|catch|class|continue|def|default|else|finally|for|fun|global|if|return|switch|this|try|var|while)\\b/,\n    number: [Prism.languages.cpp.number, /\\b(?:Infinity|NaN)\\b/],\n    operator:\n      />>=?|<<=?|\\|\\||&&|:[:=]?|--|\\+\\+|[=!<>+\\-*/%|&^]=?|[?~]|`[^`\\r\\n]{1,4}`/\n  })\n  Prism.languages.insertBefore('chaiscript', 'operator', {\n    'parameter-type': {\n      // e.g. def foo(int x, Vector y) {...}\n      pattern: /([,(]\\s*)\\w+(?=\\s+\\w)/,\n      lookbehind: true,\n      alias: 'class-name'\n    }\n  })\n  Prism.languages.insertBefore('chaiscript', 'string', {\n    'string-interpolation': {\n      pattern:\n        /(^|[^\\\\])\"(?:[^\"$\\\\]|\\\\[\\s\\S]|\\$(?!\\{)|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\}/,\n          lookbehind: true,\n          inside: {\n            'interpolation-expression': {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              inside: Prism.languages.chaiscript\n            },\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,UAAU,CAAC;AACtCC,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,EAAE;AACvB,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,QAAQ,CAACR,YAAY,CAAC;EAC5BO,KAAK,CAACE,SAAS,CAACL,UAAU,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IAC3DC,MAAM,EAAE;MACNC,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE,CACZ;MACE;MACAF,OAAO,EAAE,iBAAiB;MAC1BC,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAD,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE;IACd,CAAC,CACF;IACDE,OAAO,EACL,+HAA+H;IACjIC,MAAM,EAAE,CAACT,KAAK,CAACE,SAAS,CAACQ,GAAG,CAACD,MAAM,EAAE,sBAAsB,CAAC;IAC5DE,QAAQ,EACN;EACJ,CAAC,CAAC;EACFX,KAAK,CAACE,SAAS,CAACU,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;IACrD,gBAAgB,EAAE;MAChB;MACAP,OAAO,EAAE,uBAAuB;MAChCC,UAAU,EAAE,IAAI;MAChBO,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFb,KAAK,CAACE,SAAS,CAACU,YAAY,CAAC,YAAY,EAAE,QAAQ,EAAE;IACnD,sBAAsB,EAAE;MACtBP,OAAO,EACL,sFAAsF;MACxFC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZO,MAAM,EAAE;QACNC,aAAa,EAAE;UACbV,OAAO,EACL,mEAAmE;UACrEC,UAAU,EAAE,IAAI;UAChBQ,MAAM,EAAE;YACN,0BAA0B,EAAE;cAC1BT,OAAO,EAAE,uBAAuB;cAChCC,UAAU,EAAE,IAAI;cAChBQ,MAAM,EAAEd,KAAK,CAACE,SAAS,CAACL;YAC1B,CAAC;YACD,2BAA2B,EAAE;cAC3BQ,OAAO,EAAE,WAAW;cACpBQ,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDT,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}