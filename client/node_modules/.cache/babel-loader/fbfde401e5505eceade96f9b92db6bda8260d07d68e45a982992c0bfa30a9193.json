{"ast": null, "code": "'use strict';\n\nmodule.exports = pcaxis;\npcaxis.displayName = 'pcaxis';\npcaxis.aliases = ['px'];\nfunction pcaxis(Prism) {\n  Prism.languages.pcaxis = {\n    string: /\"[^\"]*\"/,\n    keyword: {\n      pattern: /((?:^|;)\\s*)[-A-Z\\d]+(?:\\s*\\[[-\\w]+\\])?(?:\\s*\\(\"[^\"]*\"(?:,\\s*\"[^\"]*\")*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        keyword: /^[-A-Z\\d]+/,\n        language: {\n          pattern: /^(\\s*)\\[[-\\w]+\\]/,\n          lookbehind: true,\n          inside: {\n            punctuation: /^\\[|\\]$/,\n            property: /[-\\w]+/\n          }\n        },\n        'sub-key': {\n          pattern: /^(\\s*)\\S[\\s\\S]*/,\n          lookbehind: true,\n          inside: {\n            parameter: {\n              pattern: /\"[^\"]*\"/,\n              alias: 'property'\n            },\n            punctuation: /^\\(|\\)$|,/\n          }\n        }\n      }\n    },\n    operator: /=/,\n    tlist: {\n      pattern: /TLIST\\s*\\(\\s*\\w+(?:(?:\\s*,\\s*\"[^\"]*\")+|\\s*,\\s*\"[^\"]*\"-\"[^\"]*\")?\\s*\\)/,\n      greedy: true,\n      inside: {\n        function: /^TLIST/,\n        property: {\n          pattern: /^(\\s*\\(\\s*)\\w+/,\n          lookbehind: true\n        },\n        string: /\"[^\"]*\"/,\n        punctuation: /[(),]/,\n        operator: /-/\n      }\n    },\n    punctuation: /[;,]/,\n    number: {\n      pattern: /(^|\\s)\\d+(?:\\.\\d+)?(?!\\S)/,\n      lookbehind: true\n    },\n    boolean: /NO|YES/\n  };\n  Prism.languages.px = Prism.languages.pcaxis;\n}", "map": {"version": 3, "names": ["module", "exports", "pcaxis", "displayName", "aliases", "Prism", "languages", "string", "keyword", "pattern", "lookbehind", "greedy", "inside", "language", "punctuation", "property", "parameter", "alias", "operator", "tlist", "function", "number", "boolean", "px"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/pcaxis.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pcaxis\npcaxis.displayName = 'pcaxis'\npcaxis.aliases = ['px']\nfunction pcaxis(Prism) {\n  Prism.languages.pcaxis = {\n    string: /\"[^\"]*\"/,\n    keyword: {\n      pattern:\n        /((?:^|;)\\s*)[-A-Z\\d]+(?:\\s*\\[[-\\w]+\\])?(?:\\s*\\(\"[^\"]*\"(?:,\\s*\"[^\"]*\")*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        keyword: /^[-A-Z\\d]+/,\n        language: {\n          pattern: /^(\\s*)\\[[-\\w]+\\]/,\n          lookbehind: true,\n          inside: {\n            punctuation: /^\\[|\\]$/,\n            property: /[-\\w]+/\n          }\n        },\n        'sub-key': {\n          pattern: /^(\\s*)\\S[\\s\\S]*/,\n          lookbehind: true,\n          inside: {\n            parameter: {\n              pattern: /\"[^\"]*\"/,\n              alias: 'property'\n            },\n            punctuation: /^\\(|\\)$|,/\n          }\n        }\n      }\n    },\n    operator: /=/,\n    tlist: {\n      pattern:\n        /TLIST\\s*\\(\\s*\\w+(?:(?:\\s*,\\s*\"[^\"]*\")+|\\s*,\\s*\"[^\"]*\"-\"[^\"]*\")?\\s*\\)/,\n      greedy: true,\n      inside: {\n        function: /^TLIST/,\n        property: {\n          pattern: /^(\\s*\\(\\s*)\\w+/,\n          lookbehind: true\n        },\n        string: /\"[^\"]*\"/,\n        punctuation: /[(),]/,\n        operator: /-/\n      }\n    },\n    punctuation: /[;,]/,\n    number: {\n      pattern: /(^|\\s)\\d+(?:\\.\\d+)?(?!\\S)/,\n      lookbehind: true\n    },\n    boolean: /NO|YES/\n  }\n  Prism.languages.px = Prism.languages.pcaxis\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AACvB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE;MACPC,OAAO,EACL,oFAAoF;MACtFC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNJ,OAAO,EAAE,YAAY;QACrBK,QAAQ,EAAE;UACRJ,OAAO,EAAE,kBAAkB;UAC3BC,UAAU,EAAE,IAAI;UAChBE,MAAM,EAAE;YACNE,WAAW,EAAE,SAAS;YACtBC,QAAQ,EAAE;UACZ;QACF,CAAC;QACD,SAAS,EAAE;UACTN,OAAO,EAAE,iBAAiB;UAC1BC,UAAU,EAAE,IAAI;UAChBE,MAAM,EAAE;YACNI,SAAS,EAAE;cACTP,OAAO,EAAE,SAAS;cAClBQ,KAAK,EAAE;YACT,CAAC;YACDH,WAAW,EAAE;UACf;QACF;MACF;IACF,CAAC;IACDI,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE;MACLV,OAAO,EACL,sEAAsE;MACxEE,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNQ,QAAQ,EAAE,QAAQ;QAClBL,QAAQ,EAAE;UACRN,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE;QACd,CAAC;QACDH,MAAM,EAAE,SAAS;QACjBO,WAAW,EAAE,OAAO;QACpBI,QAAQ,EAAE;MACZ;IACF,CAAC;IACDJ,WAAW,EAAE,MAAM;IACnBO,MAAM,EAAE;MACNZ,OAAO,EAAE,2BAA2B;MACpCC,UAAU,EAAE;IACd,CAAC;IACDY,OAAO,EAAE;EACX,CAAC;EACDjB,KAAK,CAACC,SAAS,CAACiB,EAAE,GAAGlB,KAAK,CAACC,SAAS,CAACJ,MAAM;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}