{"ast": null, "code": "'use strict';\n\nmodule.exports = coffeescript;\ncoffeescript.displayName = 'coffeescript';\ncoffeescript.aliases = ['coffee'];\nfunction coffeescript(Prism) {\n  ;\n  (function (Prism) {\n    // Ignore comments starting with { to privilege string interpolation highlighting\n    var comment = /#(?!\\{).+/;\n    var interpolation = {\n      pattern: /#\\{[^}]+\\}/,\n      alias: 'variable'\n    };\n    Prism.languages.coffeescript = Prism.languages.extend('javascript', {\n      comment: comment,\n      string: [\n      // Strings are multiline\n      {\n        pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n        greedy: true\n      }, {\n        // Strings are multiline\n        pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n        greedy: true,\n        inside: {\n          interpolation: interpolation\n        }\n      }],\n      keyword: /\\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\\b/,\n      'class-member': {\n        pattern: /@(?!\\d)\\w+/,\n        alias: 'variable'\n      }\n    });\n    Prism.languages.insertBefore('coffeescript', 'comment', {\n      'multiline-comment': {\n        pattern: /###[\\s\\S]+?###/,\n        alias: 'comment'\n      },\n      // Block regexp can contain comments and interpolation\n      'block-regex': {\n        pattern: /\\/{3}[\\s\\S]*?\\/{3}/,\n        alias: 'regex',\n        inside: {\n          comment: comment,\n          interpolation: interpolation\n        }\n      }\n    });\n    Prism.languages.insertBefore('coffeescript', 'string', {\n      'inline-javascript': {\n        pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n        inside: {\n          delimiter: {\n            pattern: /^`|`$/,\n            alias: 'punctuation'\n          },\n          script: {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-javascript',\n            inside: Prism.languages.javascript\n          }\n        }\n      },\n      // Block strings\n      'multiline-string': [{\n        pattern: /'''[\\s\\S]*?'''/,\n        greedy: true,\n        alias: 'string'\n      }, {\n        pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          interpolation: interpolation\n        }\n      }]\n    });\n    Prism.languages.insertBefore('coffeescript', 'keyword', {\n      // Object property\n      property: /(?!\\d)\\w+(?=\\s*:(?!:))/\n    });\n    delete Prism.languages.coffeescript['template-string'];\n    Prism.languages.coffee = Prism.languages.coffeescript;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "coffeescript", "displayName", "aliases", "Prism", "comment", "interpolation", "pattern", "alias", "languages", "extend", "string", "greedy", "inside", "keyword", "insertBefore", "delimiter", "script", "javascript", "property", "coffee"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/coffeescript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = coffeescript\ncoffeescript.displayName = 'coffeescript'\ncoffeescript.aliases = ['coffee']\nfunction coffeescript(Prism) {\n  ;(function (Prism) {\n    // Ignore comments starting with { to privilege string interpolation highlighting\n    var comment = /#(?!\\{).+/\n    var interpolation = {\n      pattern: /#\\{[^}]+\\}/,\n      alias: 'variable'\n    }\n    Prism.languages.coffeescript = Prism.languages.extend('javascript', {\n      comment: comment,\n      string: [\n        // Strings are multiline\n        {\n          pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n          greedy: true\n        },\n        {\n          // Strings are multiline\n          pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n          greedy: true,\n          inside: {\n            interpolation: interpolation\n          }\n        }\n      ],\n      keyword:\n        /\\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\\b/,\n      'class-member': {\n        pattern: /@(?!\\d)\\w+/,\n        alias: 'variable'\n      }\n    })\n    Prism.languages.insertBefore('coffeescript', 'comment', {\n      'multiline-comment': {\n        pattern: /###[\\s\\S]+?###/,\n        alias: 'comment'\n      },\n      // Block regexp can contain comments and interpolation\n      'block-regex': {\n        pattern: /\\/{3}[\\s\\S]*?\\/{3}/,\n        alias: 'regex',\n        inside: {\n          comment: comment,\n          interpolation: interpolation\n        }\n      }\n    })\n    Prism.languages.insertBefore('coffeescript', 'string', {\n      'inline-javascript': {\n        pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n        inside: {\n          delimiter: {\n            pattern: /^`|`$/,\n            alias: 'punctuation'\n          },\n          script: {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-javascript',\n            inside: Prism.languages.javascript\n          }\n        }\n      },\n      // Block strings\n      'multiline-string': [\n        {\n          pattern: /'''[\\s\\S]*?'''/,\n          greedy: true,\n          alias: 'string'\n        },\n        {\n          pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n          greedy: true,\n          alias: 'string',\n          inside: {\n            interpolation: interpolation\n          }\n        }\n      ]\n    })\n    Prism.languages.insertBefore('coffeescript', 'keyword', {\n      // Object property\n      property: /(?!\\d)\\w+(?=\\s*:(?!:))/\n    })\n    delete Prism.languages.coffeescript['template-string']\n    Prism.languages.coffee = Prism.languages.coffeescript\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,CAAC,QAAQ,CAAC;AACjC,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3B;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA,IAAIC,OAAO,GAAG,WAAW;IACzB,IAAIC,aAAa,GAAG;MAClBC,OAAO,EAAE,YAAY;MACrBC,KAAK,EAAE;IACT,CAAC;IACDJ,KAAK,CAACK,SAAS,CAACR,YAAY,GAAGG,KAAK,CAACK,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE;MAClEL,OAAO,EAAEA,OAAO;MAChBM,MAAM,EAAE;MACN;MACA;QACEJ,OAAO,EAAE,wBAAwB;QACjCK,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAL,OAAO,EAAE,wBAAwB;QACjCK,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNP,aAAa,EAAEA;QACjB;MACF,CAAC,CACF;MACDQ,OAAO,EACL,kSAAkS;MACpS,cAAc,EAAE;QACdP,OAAO,EAAE,YAAY;QACrBC,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFJ,KAAK,CAACK,SAAS,CAACM,YAAY,CAAC,cAAc,EAAE,SAAS,EAAE;MACtD,mBAAmB,EAAE;QACnBR,OAAO,EAAE,gBAAgB;QACzBC,KAAK,EAAE;MACT,CAAC;MACD;MACA,aAAa,EAAE;QACbD,OAAO,EAAE,oBAAoB;QAC7BC,KAAK,EAAE,OAAO;QACdK,MAAM,EAAE;UACNR,OAAO,EAAEA,OAAO;UAChBC,aAAa,EAAEA;QACjB;MACF;IACF,CAAC,CAAC;IACFF,KAAK,CAACK,SAAS,CAACM,YAAY,CAAC,cAAc,EAAE,QAAQ,EAAE;MACrD,mBAAmB,EAAE;QACnBR,OAAO,EAAE,wBAAwB;QACjCM,MAAM,EAAE;UACNG,SAAS,EAAE;YACTT,OAAO,EAAE,OAAO;YAChBC,KAAK,EAAE;UACT,CAAC;UACDS,MAAM,EAAE;YACNV,OAAO,EAAE,SAAS;YAClBC,KAAK,EAAE,qBAAqB;YAC5BK,MAAM,EAAET,KAAK,CAACK,SAAS,CAACS;UAC1B;QACF;MACF,CAAC;MACD;MACA,kBAAkB,EAAE,CAClB;QACEX,OAAO,EAAE,gBAAgB;QACzBK,MAAM,EAAE,IAAI;QACZJ,KAAK,EAAE;MACT,CAAC,EACD;QACED,OAAO,EAAE,gBAAgB;QACzBK,MAAM,EAAE,IAAI;QACZJ,KAAK,EAAE,QAAQ;QACfK,MAAM,EAAE;UACNP,aAAa,EAAEA;QACjB;MACF,CAAC;IAEL,CAAC,CAAC;IACFF,KAAK,CAACK,SAAS,CAACM,YAAY,CAAC,cAAc,EAAE,SAAS,EAAE;MACtD;MACAI,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,OAAOf,KAAK,CAACK,SAAS,CAACR,YAAY,CAAC,iBAAiB,CAAC;IACtDG,KAAK,CAACK,SAAS,CAACW,MAAM,GAAGhB,KAAK,CAACK,SAAS,CAACR,YAAY;EACvD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}