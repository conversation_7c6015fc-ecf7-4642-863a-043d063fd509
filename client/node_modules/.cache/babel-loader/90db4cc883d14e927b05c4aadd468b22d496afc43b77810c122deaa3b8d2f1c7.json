{"ast": null, "code": "'use strict';\n\nmodule.exports = hexadecimal;\n\n// Check if the given character code, or the character code at the first\n// character, is hexadecimal.\nfunction hexadecimal(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character;\n  return code >= 97 /* a */ && code <= 102 /* z */ || code >= 65 /* A */ && code <= 70 /* Z */ || code >= 48 /* A */ && code <= 57 /* Z */;\n}", "map": {"version": 3, "names": ["module", "exports", "hexadecimal", "character", "code", "charCodeAt"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/is-hexadecimal/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = hexadecimal\n\n// Check if the given character code, or the character code at the first\n// character, is hexadecimal.\nfunction hexadecimal(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return (\n    (code >= 97 /* a */ && code <= 102) /* z */ ||\n    (code >= 65 /* A */ && code <= 70) /* Z */ ||\n    (code >= 48 /* A */ && code <= 57) /* Z */\n  )\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,WAAW;;AAE5B;AACA;AACA,SAASA,WAAWA,CAACC,SAAS,EAAE;EAC9B,IAAIC,IAAI,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,CAACE,UAAU,CAAC,CAAC,CAAC,GAAGF,SAAS;EAE9E,OACGC,IAAI,IAAI,EAAE,CAAC,WAAWA,IAAI,IAAI,GAAG,CAAE,WACnCA,IAAI,IAAI,EAAE,CAAC,WAAWA,IAAI,IAAI,EAAG,CAAC,WAClCA,IAAI,IAAI,EAAE,CAAC,WAAWA,IAAI,IAAI,EAAG,CAAC;AAEvC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}