{"ast": null, "code": "'use strict';\n\nmodule.exports = robotframework;\nrobotframework.displayName = 'robotframework';\nrobotframework.aliases = [];\nfunction robotframework(Prism) {\n  ;\n  (function (Prism) {\n    var comment = {\n      pattern: /(^[ \\t]*| {2}|\\t)#.*/m,\n      lookbehind: true,\n      greedy: true\n    };\n    var variable = {\n      pattern: /((?:^|[^\\\\])(?:\\\\{2})*)[$@&%]\\{(?:[^{}\\r\\n]|\\{[^{}\\r\\n]*\\})*\\}/,\n      lookbehind: true,\n      inside: {\n        punctuation: /^[$@&%]\\{|\\}$/\n      }\n    };\n    function createSection(name, inside) {\n      var extendecInside = {};\n      extendecInside['section-header'] = {\n        pattern: /^ ?\\*{3}.+?\\*{3}/,\n        alias: 'keyword'\n      }; // copy inside tokens\n      for (var token in inside) {\n        extendecInside[token] = inside[token];\n      }\n      extendecInside['tag'] = {\n        pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)\\[[-\\w]+\\]/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\[|\\]/\n        }\n      };\n      extendecInside['variable'] = variable;\n      extendecInside['comment'] = comment;\n      return {\n        pattern: RegExp(/^ ?\\*{3}[ \\t]*<name>[ \\t]*\\*{3}(?:.|[\\r\\n](?!\\*{3}))*/.source.replace(/<name>/g, function () {\n          return name;\n        }), 'im'),\n        alias: 'section',\n        inside: extendecInside\n      };\n    }\n    var docTag = {\n      pattern: /(\\[Documentation\\](?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n      lookbehind: true,\n      alias: 'string'\n    };\n    var testNameLike = {\n      pattern: /([\\r\\n] ?)(?!#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      alias: 'function',\n      inside: {\n        variable: variable\n      }\n    };\n    var testPropertyLike = {\n      pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)(?!\\[|\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      inside: {\n        variable: variable\n      }\n    };\n    Prism.languages['robotframework'] = {\n      settings: createSection('Settings', {\n        documentation: {\n          pattern: /([\\r\\n] ?Documentation(?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n          lookbehind: true,\n          alias: 'string'\n        },\n        property: {\n          pattern: /([\\r\\n] ?)(?!\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n          lookbehind: true\n        }\n      }),\n      variables: createSection('Variables'),\n      'test-cases': createSection('Test Cases', {\n        'test-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      keywords: createSection('Keywords', {\n        'keyword-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      tasks: createSection('Tasks', {\n        'task-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      comment: comment\n    };\n    Prism.languages.robot = Prism.languages['robotframework'];\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "robotframework", "displayName", "aliases", "Prism", "comment", "pattern", "lookbehind", "greedy", "variable", "inside", "punctuation", "createSection", "name", "extendecInside", "alias", "token", "RegExp", "source", "replace", "docTag", "testNameLike", "testPropertyLike", "languages", "settings", "documentation", "property", "variables", "keywords", "tasks", "robot"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/robotframework.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = robotframework\nrobotframework.displayName = 'robotframework'\nrobotframework.aliases = []\nfunction robotframework(Prism) {\n  ;(function (Prism) {\n    var comment = {\n      pattern: /(^[ \\t]*| {2}|\\t)#.*/m,\n      lookbehind: true,\n      greedy: true\n    }\n    var variable = {\n      pattern: /((?:^|[^\\\\])(?:\\\\{2})*)[$@&%]\\{(?:[^{}\\r\\n]|\\{[^{}\\r\\n]*\\})*\\}/,\n      lookbehind: true,\n      inside: {\n        punctuation: /^[$@&%]\\{|\\}$/\n      }\n    }\n    function createSection(name, inside) {\n      var extendecInside = {}\n      extendecInside['section-header'] = {\n        pattern: /^ ?\\*{3}.+?\\*{3}/,\n        alias: 'keyword'\n      } // copy inside tokens\n      for (var token in inside) {\n        extendecInside[token] = inside[token]\n      }\n      extendecInside['tag'] = {\n        pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)\\[[-\\w]+\\]/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\[|\\]/\n        }\n      }\n      extendecInside['variable'] = variable\n      extendecInside['comment'] = comment\n      return {\n        pattern: RegExp(\n          /^ ?\\*{3}[ \\t]*<name>[ \\t]*\\*{3}(?:.|[\\r\\n](?!\\*{3}))*/.source.replace(\n            /<name>/g,\n            function () {\n              return name\n            }\n          ),\n          'im'\n        ),\n        alias: 'section',\n        inside: extendecInside\n      }\n    }\n    var docTag = {\n      pattern:\n        /(\\[Documentation\\](?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n      lookbehind: true,\n      alias: 'string'\n    }\n    var testNameLike = {\n      pattern: /([\\r\\n] ?)(?!#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      alias: 'function',\n      inside: {\n        variable: variable\n      }\n    }\n    var testPropertyLike = {\n      pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)(?!\\[|\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      inside: {\n        variable: variable\n      }\n    }\n    Prism.languages['robotframework'] = {\n      settings: createSection('Settings', {\n        documentation: {\n          pattern:\n            /([\\r\\n] ?Documentation(?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n          lookbehind: true,\n          alias: 'string'\n        },\n        property: {\n          pattern: /([\\r\\n] ?)(?!\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n          lookbehind: true\n        }\n      }),\n      variables: createSection('Variables'),\n      'test-cases': createSection('Test Cases', {\n        'test-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      keywords: createSection('Keywords', {\n        'keyword-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      tasks: createSection('Tasks', {\n        'task-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      comment: comment\n    }\n    Prism.languages.robot = Prism.languages['robotframework']\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,cAAc;AAC/BA,cAAc,CAACC,WAAW,GAAG,gBAAgB;AAC7CD,cAAc,CAACE,OAAO,GAAG,EAAE;AAC3B,SAASF,cAAcA,CAACG,KAAK,EAAE;EAC7B;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,OAAO,GAAG;MACZC,OAAO,EAAE,uBAAuB;MAChCC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,IAAIC,QAAQ,GAAG;MACbH,OAAO,EAAE,gEAAgE;MACzEC,UAAU,EAAE,IAAI;MAChBG,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACD,SAASC,aAAaA,CAACC,IAAI,EAAEH,MAAM,EAAE;MACnC,IAAII,cAAc,GAAG,CAAC,CAAC;MACvBA,cAAc,CAAC,gBAAgB,CAAC,GAAG;QACjCR,OAAO,EAAE,kBAAkB;QAC3BS,KAAK,EAAE;MACT,CAAC,EAAC;MACF,KAAK,IAAIC,KAAK,IAAIN,MAAM,EAAE;QACxBI,cAAc,CAACE,KAAK,CAAC,GAAGN,MAAM,CAACM,KAAK,CAAC;MACvC;MACAF,cAAc,CAAC,KAAK,CAAC,GAAG;QACtBR,OAAO,EAAE,qCAAqC;QAC9CC,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACDG,cAAc,CAAC,UAAU,CAAC,GAAGL,QAAQ;MACrCK,cAAc,CAAC,SAAS,CAAC,GAAGT,OAAO;MACnC,OAAO;QACLC,OAAO,EAAEW,MAAM,CACb,uDAAuD,CAACC,MAAM,CAACC,OAAO,CACpE,SAAS,EACT,YAAY;UACV,OAAON,IAAI;QACb,CACF,CAAC,EACD,IACF,CAAC;QACDE,KAAK,EAAE,SAAS;QAChBL,MAAM,EAAEI;MACV,CAAC;IACH;IACA,IAAIM,MAAM,GAAG;MACXd,OAAO,EACL,+EAA+E;MACjFC,UAAU,EAAE,IAAI;MAChBQ,KAAK,EAAE;IACT,CAAC;IACD,IAAIM,YAAY,GAAG;MACjBf,OAAO,EAAE,oCAAoC;MAC7CC,UAAU,EAAE,IAAI;MAChBQ,KAAK,EAAE,UAAU;MACjBL,MAAM,EAAE;QACND,QAAQ,EAAEA;MACZ;IACF,CAAC;IACD,IAAIa,gBAAgB,GAAG;MACrBhB,OAAO,EAAE,4DAA4D;MACrEC,UAAU,EAAE,IAAI;MAChBG,MAAM,EAAE;QACND,QAAQ,EAAEA;MACZ;IACF,CAAC;IACDL,KAAK,CAACmB,SAAS,CAAC,gBAAgB,CAAC,GAAG;MAClCC,QAAQ,EAAEZ,aAAa,CAAC,UAAU,EAAE;QAClCa,aAAa,EAAE;UACbnB,OAAO,EACL,mFAAmF;UACrFC,UAAU,EAAE,IAAI;UAChBQ,KAAK,EAAE;QACT,CAAC;QACDW,QAAQ,EAAE;UACRpB,OAAO,EAAE,0CAA0C;UACnDC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;MACFoB,SAAS,EAAEf,aAAa,CAAC,WAAW,CAAC;MACrC,YAAY,EAAEA,aAAa,CAAC,YAAY,EAAE;QACxC,WAAW,EAAES,YAAY;QACzBI,aAAa,EAAEL,MAAM;QACrBM,QAAQ,EAAEJ;MACZ,CAAC,CAAC;MACFM,QAAQ,EAAEhB,aAAa,CAAC,UAAU,EAAE;QAClC,cAAc,EAAES,YAAY;QAC5BI,aAAa,EAAEL,MAAM;QACrBM,QAAQ,EAAEJ;MACZ,CAAC,CAAC;MACFO,KAAK,EAAEjB,aAAa,CAAC,OAAO,EAAE;QAC5B,WAAW,EAAES,YAAY;QACzBI,aAAa,EAAEL,MAAM;QACrBM,QAAQ,EAAEJ;MACZ,CAAC,CAAC;MACFjB,OAAO,EAAEA;IACX,CAAC;IACDD,KAAK,CAACmB,SAAS,CAACO,KAAK,GAAG1B,KAAK,CAACmB,SAAS,CAAC,gBAAgB,CAAC;EAC3D,CAAC,EAAEnB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}