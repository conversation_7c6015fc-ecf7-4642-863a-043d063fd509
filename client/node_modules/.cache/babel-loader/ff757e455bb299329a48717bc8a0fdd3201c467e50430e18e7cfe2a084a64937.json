{"ast": null, "code": "'use strict';\n\nvar refractorBash = require('./bash.js');\nmodule.exports = shellSession;\nshellSession.displayName = 'shellSession';\nshellSession.aliases = [];\nfunction shellSession(Prism) {\n  Prism.register(refractorBash);\n  (function (Prism) {\n    // CAREFUL!\n    // The following patterns are concatenated, so the group referenced by a back reference is non-obvious!\n    var strings = [\n    // normal string\n    /\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/.source, /'[^']*'/.source, /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/.source,\n    // here doc\n    // 2 capturing groups\n    /<<-?\\s*([\"']?)(\\w+)\\1\\s[\\s\\S]*?[\\r\\n]\\2/.source].join('|');\n    Prism.languages['shell-session'] = {\n      command: {\n        pattern: RegExp(\n        // user info\n        /^/.source + '(?:' + (\n        // <user> \":\" ( <path> )?\n        /[^\\s@:$#%*!/\\\\]+@[^\\r\\n@:$#%*!/\\\\]+(?::[^\\0-\\x1F$#%*?\"<>:;|]+)?/.source + '|' +\n        // <path>\n        // Since the path pattern is quite general, we will require it to start with a special character to\n        // prevent false positives.\n        /[/~.][^\\0-\\x1F$#%*?\"<>@:;|]*/.source) + ')?' +\n        // shell symbol\n        /[$#%](?=\\s)/.source +\n        // bash command\n        /(?:[^\\\\\\r\\n \\t'\"<$]|[ \\t](?:(?!#)|#.*$)|\\\\(?:[^\\r]|\\r\\n?)|\\$(?!')|<(?!<)|<<str>>)+/.source.replace(/<<str>>/g, function () {\n          return strings;\n        }), 'm'),\n        greedy: true,\n        inside: {\n          info: {\n            // foo@bar:~/files$ exit\n            // foo@bar$ exit\n            // ~/files$ exit\n            pattern: /^[^#$%]+/,\n            alias: 'punctuation',\n            inside: {\n              user: /^[^\\s@:$#%*!/\\\\]+@[^\\r\\n@:$#%*!/\\\\]+/,\n              punctuation: /:/,\n              path: /[\\s\\S]+/\n            }\n          },\n          bash: {\n            pattern: /(^[$#%]\\s*)\\S[\\s\\S]*/,\n            lookbehind: true,\n            alias: 'language-bash',\n            inside: Prism.languages.bash\n          },\n          'shell-symbol': {\n            pattern: /^[$#%]/,\n            alias: 'important'\n          }\n        }\n      },\n      output: /.(?:.*(?:[\\r\\n]|.$))*/\n    };\n    Prism.languages['sh-session'] = Prism.languages['shellsession'] = Prism.languages['shell-session'];\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorBash", "require", "module", "exports", "shellSession", "displayName", "aliases", "Prism", "register", "strings", "source", "join", "languages", "command", "pattern", "RegExp", "replace", "greedy", "inside", "info", "alias", "user", "punctuation", "path", "bash", "lookbehind", "output"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/shell-session.js"], "sourcesContent": ["'use strict'\nvar refractorBash = require('./bash.js')\nmodule.exports = shellSession\nshellSession.displayName = 'shellSession'\nshellSession.aliases = []\nfunction shellSession(Prism) {\n  Prism.register(refractorBash)\n  ;(function (Prism) {\n    // CAREFUL!\n    // The following patterns are concatenated, so the group referenced by a back reference is non-obvious!\n    var strings = [\n      // normal string\n      /\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/.source,\n      /'[^']*'/.source,\n      /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/.source, // here doc\n      // 2 capturing groups\n      /<<-?\\s*([\"']?)(\\w+)\\1\\s[\\s\\S]*?[\\r\\n]\\2/.source\n    ].join('|')\n    Prism.languages['shell-session'] = {\n      command: {\n        pattern: RegExp(\n          // user info\n          /^/.source +\n            '(?:' + // <user> \":\" ( <path> )?\n            (/[^\\s@:$#%*!/\\\\]+@[^\\r\\n@:$#%*!/\\\\]+(?::[^\\0-\\x1F$#%*?\"<>:;|]+)?/\n              .source +\n              '|' + // <path>\n              // Since the path pattern is quite general, we will require it to start with a special character to\n              // prevent false positives.\n              /[/~.][^\\0-\\x1F$#%*?\"<>@:;|]*/.source) +\n            ')?' + // shell symbol\n            /[$#%](?=\\s)/.source + // bash command\n            /(?:[^\\\\\\r\\n \\t'\"<$]|[ \\t](?:(?!#)|#.*$)|\\\\(?:[^\\r]|\\r\\n?)|\\$(?!')|<(?!<)|<<str>>)+/.source.replace(\n              /<<str>>/g,\n              function () {\n                return strings\n              }\n            ),\n          'm'\n        ),\n        greedy: true,\n        inside: {\n          info: {\n            // foo@bar:~/files$ exit\n            // foo@bar$ exit\n            // ~/files$ exit\n            pattern: /^[^#$%]+/,\n            alias: 'punctuation',\n            inside: {\n              user: /^[^\\s@:$#%*!/\\\\]+@[^\\r\\n@:$#%*!/\\\\]+/,\n              punctuation: /:/,\n              path: /[\\s\\S]+/\n            }\n          },\n          bash: {\n            pattern: /(^[$#%]\\s*)\\S[\\s\\S]*/,\n            lookbehind: true,\n            alias: 'language-bash',\n            inside: Prism.languages.bash\n          },\n          'shell-symbol': {\n            pattern: /^[$#%]/,\n            alias: 'important'\n          }\n        }\n      },\n      output: /.(?:.*(?:[\\r\\n]|.$))*/\n    }\n    Prism.languages['sh-session'] = Prism.languages['shellsession'] =\n      Prism.languages['shell-session']\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxCC,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3BA,KAAK,CAACC,QAAQ,CAACR,aAAa,CAAC;EAC5B,CAAC,UAAUO,KAAK,EAAE;IACjB;IACA;IACA,IAAIE,OAAO,GAAG;IACZ;IACA,uDAAuD,CAACC,MAAM,EAC9D,SAAS,CAACA,MAAM,EAChB,0BAA0B,CAACA,MAAM;IAAE;IACnC;IACA,yCAAyC,CAACA,MAAM,CACjD,CAACC,IAAI,CAAC,GAAG,CAAC;IACXJ,KAAK,CAACK,SAAS,CAAC,eAAe,CAAC,GAAG;MACjCC,OAAO,EAAE;QACPC,OAAO,EAAEC,MAAM;QACb;QACA,GAAG,CAACL,MAAM,GACR,KAAK;QAAG;QACP,iEAAiE,CAC/DA,MAAM,GACP,GAAG;QAAG;QACN;QACA;QACA,8BAA8B,CAACA,MAAM,CAAC,GACxC,IAAI;QAAG;QACP,aAAa,CAACA,MAAM;QAAG;QACvB,oFAAoF,CAACA,MAAM,CAACM,OAAO,CACjG,UAAU,EACV,YAAY;UACV,OAAOP,OAAO;QAChB,CACF,CAAC,EACH,GACF,CAAC;QACDQ,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,IAAI,EAAE;YACJ;YACA;YACA;YACAL,OAAO,EAAE,UAAU;YACnBM,KAAK,EAAE,aAAa;YACpBF,MAAM,EAAE;cACNG,IAAI,EAAE,sCAAsC;cAC5CC,WAAW,EAAE,GAAG;cAChBC,IAAI,EAAE;YACR;UACF,CAAC;UACDC,IAAI,EAAE;YACJV,OAAO,EAAE,sBAAsB;YAC/BW,UAAU,EAAE,IAAI;YAChBL,KAAK,EAAE,eAAe;YACtBF,MAAM,EAAEX,KAAK,CAACK,SAAS,CAACY;UAC1B,CAAC;UACD,cAAc,EAAE;YACdV,OAAO,EAAE,QAAQ;YACjBM,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDM,MAAM,EAAE;IACV,CAAC;IACDnB,KAAK,CAACK,SAAS,CAAC,YAAY,CAAC,GAAGL,KAAK,CAACK,SAAS,CAAC,cAAc,CAAC,GAC7DL,KAAK,CAACK,SAAS,CAAC,eAAe,CAAC;EACpC,CAAC,EAAEL,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}