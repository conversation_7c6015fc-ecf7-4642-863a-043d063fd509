{"ast": null, "code": "'use strict';\n\nmodule.exports = birb;\nbirb.displayName = 'birb';\nbirb.aliases = [];\nfunction birb(Prism) {\n  Prism.languages.birb = Prism.languages.extend('clike', {\n    string: {\n      pattern: /r?(\"|')(?:\\\\.|(?!\\1)[^\\\\])*\\1/,\n      greedy: true\n    },\n    'class-name': [/\\b[A-Z](?:[\\d_]*[a-zA-Z]\\w*)?\\b/,\n    // matches variable and function return types (parameters as well).\n    /\\b(?:[A-Z]\\w*|(?!(?:var|void)\\b)[a-z]\\w*)(?=\\s+\\w+\\s*[;,=()])/],\n    keyword: /\\b(?:assert|break|case|class|const|default|else|enum|final|follows|for|grab|if|nest|new|next|noSeeb|return|static|switch|throw|var|void|while)\\b/,\n    operator: /\\+\\+|--|&&|\\|\\||<<=?|>>=?|~(?:\\/=?)?|[+\\-*\\/%&^|=!<>]=?|\\?|:/,\n    variable: /\\b[a-z_]\\w*\\b/\n  });\n  Prism.languages.insertBefore('birb', 'function', {\n    metadata: {\n      pattern: /<\\w+>/,\n      greedy: true,\n      alias: 'symbol'\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "birb", "displayName", "aliases", "Prism", "languages", "extend", "string", "pattern", "greedy", "keyword", "operator", "variable", "insertBefore", "metadata", "alias"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/birb.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = birb\nbirb.displayName = 'birb'\nbirb.aliases = []\nfunction birb(Prism) {\n  Prism.languages.birb = Prism.languages.extend('clike', {\n    string: {\n      pattern: /r?(\"|')(?:\\\\.|(?!\\1)[^\\\\])*\\1/,\n      greedy: true\n    },\n    'class-name': [\n      /\\b[A-Z](?:[\\d_]*[a-zA-Z]\\w*)?\\b/, // matches variable and function return types (parameters as well).\n      /\\b(?:[A-Z]\\w*|(?!(?:var|void)\\b)[a-z]\\w*)(?=\\s+\\w+\\s*[;,=()])/\n    ],\n    keyword:\n      /\\b(?:assert|break|case|class|const|default|else|enum|final|follows|for|grab|if|nest|new|next|noSeeb|return|static|switch|throw|var|void|while)\\b/,\n    operator: /\\+\\+|--|&&|\\|\\||<<=?|>>=?|~(?:\\/=?)?|[+\\-*\\/%&^|=!<>]=?|\\?|:/,\n    variable: /\\b[a-z_]\\w*\\b/\n  })\n  Prism.languages.insertBefore('birb', 'function', {\n    metadata: {\n      pattern: /<\\w+>/,\n      greedy: true,\n      alias: 'symbol'\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACrDC,MAAM,EAAE;MACNC,OAAO,EAAE,+BAA+B;MACxCC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE,CACZ,iCAAiC;IAAE;IACnC,+DAA+D,CAChE;IACDC,OAAO,EACL,kJAAkJ;IACpJC,QAAQ,EAAE,8DAA8D;IACxEC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFR,KAAK,CAACC,SAAS,CAACQ,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE;IAC/CC,QAAQ,EAAE;MACRN,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,IAAI;MACZM,KAAK,EAAE;IACT;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}