{"ast": null, "code": "'use strict';\n\nmodule.exports = reason;\nreason.displayName = 'reason';\nreason.aliases = [];\nfunction reason(Prism) {\n  Prism.languages.reason = Prism.languages.extend('clike', {\n    string: {\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    },\n    // 'class-name' must be matched *after* 'constructor' defined below\n    'class-name': /\\b[A-Z]\\w*/,\n    keyword: /\\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\\b/,\n    operator: /\\.{3}|:[:=]|\\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\\-*\\/]\\.?|\\b(?:asr|land|lor|lsl|lsr|lxor|mod)\\b/\n  });\n  Prism.languages.insertBefore('reason', 'class-name', {\n    char: {\n      pattern: /'(?:\\\\x[\\da-f]{2}|\\\\o[0-3][0-7][0-7]|\\\\\\d{3}|\\\\.|[^'\\\\\\r\\n])'/,\n      greedy: true\n    },\n    // Negative look-ahead prevents from matching things like String.capitalize\n    constructor: /\\b[A-Z]\\w*\\b(?!\\s*\\.)/,\n    label: {\n      pattern: /\\b[a-z]\\w*(?=::)/,\n      alias: 'symbol'\n    }\n  }); // We can't match functions property, so let's not even try.\n  delete Prism.languages.reason.function;\n}", "map": {"version": 3, "names": ["module", "exports", "reason", "displayName", "aliases", "Prism", "languages", "extend", "string", "pattern", "greedy", "keyword", "operator", "insertBefore", "char", "constructor", "label", "alias", "function"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/reason.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = reason\nreason.displayName = 'reason'\nreason.aliases = []\nfunction reason(Prism) {\n  Prism.languages.reason = Prism.languages.extend('clike', {\n    string: {\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    },\n    // 'class-name' must be matched *after* 'constructor' defined below\n    'class-name': /\\b[A-Z]\\w*/,\n    keyword:\n      /\\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\\b/,\n    operator:\n      /\\.{3}|:[:=]|\\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\\-*\\/]\\.?|\\b(?:asr|land|lor|lsl|lsr|lxor|mod)\\b/\n  })\n  Prism.languages.insertBefore('reason', 'class-name', {\n    char: {\n      pattern: /'(?:\\\\x[\\da-f]{2}|\\\\o[0-3][0-7][0-7]|\\\\\\d{3}|\\\\.|[^'\\\\\\r\\n])'/,\n      greedy: true\n    },\n    // Negative look-ahead prevents from matching things like String.capitalize\n    constructor: /\\b[A-Z]\\w*\\b(?!\\s*\\.)/,\n    label: {\n      pattern: /\\b[a-z]\\w*(?=::)/,\n      alias: 'symbol'\n    }\n  }) // We can't match functions property, so let's not even try.\n  delete Prism.languages.reason.function\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACvDC,MAAM,EAAE;MACNC,OAAO,EAAE,qCAAqC;MAC9CC,MAAM,EAAE;IACV,CAAC;IACD;IACA,YAAY,EAAE,YAAY;IAC1BC,OAAO,EACL,2RAA2R;IAC7RC,QAAQ,EACN;EACJ,CAAC,CAAC;EACFP,KAAK,CAACC,SAAS,CAACO,YAAY,CAAC,QAAQ,EAAE,YAAY,EAAE;IACnDC,IAAI,EAAE;MACJL,OAAO,EAAE,+DAA+D;MACxEC,MAAM,EAAE;IACV,CAAC;IACD;IACAK,WAAW,EAAE,uBAAuB;IACpCC,KAAK,EAAE;MACLP,OAAO,EAAE,kBAAkB;MAC3BQ,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EAAC;EACH,OAAOZ,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACgB,QAAQ;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}