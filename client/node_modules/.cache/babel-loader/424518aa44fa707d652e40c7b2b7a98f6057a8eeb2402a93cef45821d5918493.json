{"ast": null, "code": "/*\nLanguage: Scilab\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nOrigin: matlab.js\nDescription: Scilab is a port from Matlab\nWebsite: https://www.scilab.org\nCategory: scientific\n*/\n\nfunction scilab(hljs) {\n  const COMMON_CONTAINS = [hljs.C_NUMBER_MODE, {\n    className: 'string',\n    begin: '\\'|\\\"',\n    end: '\\'|\\\"',\n    contains: [hljs.BACKSLASH_ESCAPE, {\n      begin: '\\'\\''\n    }]\n  }];\n  return {\n    name: 'Scila<PERSON>',\n    aliases: ['sci'],\n    keywords: {\n      $pattern: /%?\\w+/,\n      keyword: 'abort break case clear catch continue do elseif else endfunction end for function ' + 'global if pause return resume select try then while',\n      literal: '%f %F %t %T %pi %eps %inf %nan %e %i %z %s',\n      built_in:\n      // Scilab has more than 2000 functions. Just list the most commons\n      'abs and acos asin atan ceil cd chdir clearglobal cosh cos cumprod deff disp error ' + 'exec execstr exists exp eye gettext floor fprintf fread fsolve imag isdef isempty ' + 'isinfisnan isvector lasterror length load linspace list listfiles log10 log2 log ' + 'max min msprintf mclose mopen ones or pathconvert poly printf prod pwd rand real ' + 'round sinh sin size gsort sprintf sqrt strcat strcmps tring sum system tanh tan ' + 'type typename warning zeros matrix'\n    },\n    illegal: '(\"|#|/\\\\*|\\\\s+/\\\\w+)',\n    contains: [{\n      className: 'function',\n      beginKeywords: 'function',\n      end: '$',\n      contains: [hljs.UNDERSCORE_TITLE_MODE, {\n        className: 'params',\n        begin: '\\\\(',\n        end: '\\\\)'\n      }]\n    },\n    // seems to be a guard against [ident]' or [ident].\n    // perhaps to prevent attributes from flagging as keywords?\n    {\n      begin: '[a-zA-Z_][a-zA-Z_0-9]*[\\\\.\\']+',\n      relevance: 0\n    }, {\n      begin: '\\\\[',\n      end: '\\\\][\\\\.\\']*',\n      relevance: 0,\n      contains: COMMON_CONTAINS\n    }, hljs.COMMENT('//', '$')].concat(COMMON_CONTAINS)\n  };\n}\nmodule.exports = scilab;", "map": {"version": 3, "names": ["scilab", "hljs", "COMMON_CONTAINS", "C_NUMBER_MODE", "className", "begin", "end", "contains", "BACKSLASH_ESCAPE", "name", "aliases", "keywords", "$pattern", "keyword", "literal", "built_in", "illegal", "beginKeywords", "UNDERSCORE_TITLE_MODE", "relevance", "COMMENT", "concat", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/scilab.js"], "sourcesContent": ["/*\nLanguage: Scilab\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nOrigin: matlab.js\nDescription: Scilab is a port from Matlab\nWebsite: https://www.scilab.org\nCategory: scientific\n*/\n\nfunction scilab(hljs) {\n  const COMMON_CONTAINS = [\n    hljs.C_NUMBER_MODE,\n    {\n      className: 'string',\n      begin: '\\'|\\\"',\n      end: '\\'|\\\"',\n      contains: [ hljs.BACKSLASH_ESCAPE,\n        {\n          begin: '\\'\\''\n        } ]\n    }\n  ];\n\n  return {\n    name: 'Scila<PERSON>',\n    aliases: [ 'sci' ],\n    keywords: {\n      $pattern: /%?\\w+/,\n      keyword: 'abort break case clear catch continue do elseif else endfunction end for function ' +\n        'global if pause return resume select try then while',\n      literal:\n        '%f %F %t %T %pi %eps %inf %nan %e %i %z %s',\n      built_in: // Scilab has more than 2000 functions. Just list the most commons\n       'abs and acos asin atan ceil cd chdir clearglobal cosh cos cumprod deff disp error ' +\n       'exec execstr exists exp eye gettext floor fprintf fread fsolve imag isdef isempty ' +\n       'isinfisnan isvector lasterror length load linspace list listfiles log10 log2 log ' +\n       'max min msprintf mclose mopen ones or pathconvert poly printf prod pwd rand real ' +\n       'round sinh sin size gsort sprintf sqrt strcat strcmps tring sum system tanh tan ' +\n       'type typename warning zeros matrix'\n    },\n    illegal: '(\"|#|/\\\\*|\\\\s+/\\\\w+)',\n    contains: [\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '$',\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            className: 'params',\n            begin: '\\\\(',\n            end: '\\\\)'\n          }\n        ]\n      },\n      // seems to be a guard against [ident]' or [ident].\n      // perhaps to prevent attributes from flagging as keywords?\n      {\n        begin: '[a-zA-Z_][a-zA-Z_0-9]*[\\\\.\\']+',\n        relevance: 0\n      },\n      {\n        begin: '\\\\[',\n        end: '\\\\][\\\\.\\']*',\n        relevance: 0,\n        contains: COMMON_CONTAINS\n      },\n      hljs.COMMENT('//', '$')\n    ].concat(COMMON_CONTAINS)\n  };\n}\n\nmodule.exports = scilab;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,eAAe,GAAG,CACtBD,IAAI,CAACE,aAAa,EAClB;IACEC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,OAAO;IACZC,QAAQ,EAAE,CAAEN,IAAI,CAACO,gBAAgB,EAC/B;MACEH,KAAK,EAAE;IACT,CAAC;EACL,CAAC,CACF;EAED,OAAO;IACLI,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CAAE,KAAK,CAAE;IAClBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,oFAAoF,GAC3F,qDAAqD;MACvDC,OAAO,EACL,4CAA4C;MAC9CC,QAAQ;MAAE;MACT,oFAAoF,GACpF,oFAAoF,GACpF,mFAAmF,GACnF,mFAAmF,GACnF,kFAAkF,GAClF;IACH,CAAC;IACDC,OAAO,EAAE,sBAAsB;IAC/BT,QAAQ,EAAE,CACR;MACEH,SAAS,EAAE,UAAU;MACrBa,aAAa,EAAE,UAAU;MACzBX,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CACRN,IAAI,CAACiB,qBAAqB,EAC1B;QACEd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,KAAK;QACZC,GAAG,EAAE;MACP,CAAC;IAEL,CAAC;IACD;IACA;IACA;MACED,KAAK,EAAE,gCAAgC;MACvCc,SAAS,EAAE;IACb,CAAC,EACD;MACEd,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,aAAa;MAClBa,SAAS,EAAE,CAAC;MACZZ,QAAQ,EAAEL;IACZ,CAAC,EACDD,IAAI,CAACmB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CACxB,CAACC,MAAM,CAACnB,eAAe;EAC1B,CAAC;AACH;AAEAoB,MAAM,CAACC,OAAO,GAAGvB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}