{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\n Language: SQL\n Website: https://en.wikipedia.org/wiki/SQL\n Category: common, database\n */\n\nfunction sql(hljs) {\n  const COMMENT_MODE = hljs.COMMENT('--', '$');\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: /'/,\n      end: /'/,\n      contains: [{\n        begin: /''/\n      }]\n    }]\n  };\n  const QUOTED_IDENTIFIER = {\n    begin: /\"/,\n    end: /\"/,\n    contains: [{\n      begin: /\"\"/\n    }]\n  };\n  const LITERALS = [\"true\", \"false\",\n  // Not sure it's correct to call NULL literal, and clauses like IS [NOT] NULL look strange that way.\n  // \"null\",\n  \"unknown\"];\n  const MULTI_WORD_TYPES = [\"double precision\", \"large object\", \"with timezone\", \"without timezone\"];\n  const TYPES = ['bigint', 'binary', 'blob', 'boolean', 'char', 'character', 'clob', 'date', 'dec', 'decfloat', 'decimal', 'float', 'int', 'integer', 'interval', 'nchar', 'nclob', 'national', 'numeric', 'real', 'row', 'smallint', 'time', 'timestamp', 'varchar', 'varying',\n  // modifier (character varying)\n  'varbinary'];\n  const NON_RESERVED_WORDS = [\"add\", \"asc\", \"collation\", \"desc\", \"final\", \"first\", \"last\", \"view\"];\n\n  // https://jakewheat.github.io/sql-overview/sql-2016-foundation-grammar.html#reserved-word\n  const RESERVED_WORDS = [\"abs\", \"acos\", \"all\", \"allocate\", \"alter\", \"and\", \"any\", \"are\", \"array\", \"array_agg\", \"array_max_cardinality\", \"as\", \"asensitive\", \"asin\", \"asymmetric\", \"at\", \"atan\", \"atomic\", \"authorization\", \"avg\", \"begin\", \"begin_frame\", \"begin_partition\", \"between\", \"bigint\", \"binary\", \"blob\", \"boolean\", \"both\", \"by\", \"call\", \"called\", \"cardinality\", \"cascaded\", \"case\", \"cast\", \"ceil\", \"ceiling\", \"char\", \"char_length\", \"character\", \"character_length\", \"check\", \"classifier\", \"clob\", \"close\", \"coalesce\", \"collate\", \"collect\", \"column\", \"commit\", \"condition\", \"connect\", \"constraint\", \"contains\", \"convert\", \"copy\", \"corr\", \"corresponding\", \"cos\", \"cosh\", \"count\", \"covar_pop\", \"covar_samp\", \"create\", \"cross\", \"cube\", \"cume_dist\", \"current\", \"current_catalog\", \"current_date\", \"current_default_transform_group\", \"current_path\", \"current_role\", \"current_row\", \"current_schema\", \"current_time\", \"current_timestamp\", \"current_path\", \"current_role\", \"current_transform_group_for_type\", \"current_user\", \"cursor\", \"cycle\", \"date\", \"day\", \"deallocate\", \"dec\", \"decimal\", \"decfloat\", \"declare\", \"default\", \"define\", \"delete\", \"dense_rank\", \"deref\", \"describe\", \"deterministic\", \"disconnect\", \"distinct\", \"double\", \"drop\", \"dynamic\", \"each\", \"element\", \"else\", \"empty\", \"end\", \"end_frame\", \"end_partition\", \"end-exec\", \"equals\", \"escape\", \"every\", \"except\", \"exec\", \"execute\", \"exists\", \"exp\", \"external\", \"extract\", \"false\", \"fetch\", \"filter\", \"first_value\", \"float\", \"floor\", \"for\", \"foreign\", \"frame_row\", \"free\", \"from\", \"full\", \"function\", \"fusion\", \"get\", \"global\", \"grant\", \"group\", \"grouping\", \"groups\", \"having\", \"hold\", \"hour\", \"identity\", \"in\", \"indicator\", \"initial\", \"inner\", \"inout\", \"insensitive\", \"insert\", \"int\", \"integer\", \"intersect\", \"intersection\", \"interval\", \"into\", \"is\", \"join\", \"json_array\", \"json_arrayagg\", \"json_exists\", \"json_object\", \"json_objectagg\", \"json_query\", \"json_table\", \"json_table_primitive\", \"json_value\", \"lag\", \"language\", \"large\", \"last_value\", \"lateral\", \"lead\", \"leading\", \"left\", \"like\", \"like_regex\", \"listagg\", \"ln\", \"local\", \"localtime\", \"localtimestamp\", \"log\", \"log10\", \"lower\", \"match\", \"match_number\", \"match_recognize\", \"matches\", \"max\", \"member\", \"merge\", \"method\", \"min\", \"minute\", \"mod\", \"modifies\", \"module\", \"month\", \"multiset\", \"national\", \"natural\", \"nchar\", \"nclob\", \"new\", \"no\", \"none\", \"normalize\", \"not\", \"nth_value\", \"ntile\", \"null\", \"nullif\", \"numeric\", \"octet_length\", \"occurrences_regex\", \"of\", \"offset\", \"old\", \"omit\", \"on\", \"one\", \"only\", \"open\", \"or\", \"order\", \"out\", \"outer\", \"over\", \"overlaps\", \"overlay\", \"parameter\", \"partition\", \"pattern\", \"per\", \"percent\", \"percent_rank\", \"percentile_cont\", \"percentile_disc\", \"period\", \"portion\", \"position\", \"position_regex\", \"power\", \"precedes\", \"precision\", \"prepare\", \"primary\", \"procedure\", \"ptf\", \"range\", \"rank\", \"reads\", \"real\", \"recursive\", \"ref\", \"references\", \"referencing\", \"regr_avgx\", \"regr_avgy\", \"regr_count\", \"regr_intercept\", \"regr_r2\", \"regr_slope\", \"regr_sxx\", \"regr_sxy\", \"regr_syy\", \"release\", \"result\", \"return\", \"returns\", \"revoke\", \"right\", \"rollback\", \"rollup\", \"row\", \"row_number\", \"rows\", \"running\", \"savepoint\", \"scope\", \"scroll\", \"search\", \"second\", \"seek\", \"select\", \"sensitive\", \"session_user\", \"set\", \"show\", \"similar\", \"sin\", \"sinh\", \"skip\", \"smallint\", \"some\", \"specific\", \"specifictype\", \"sql\", \"sqlexception\", \"sqlstate\", \"sqlwarning\", \"sqrt\", \"start\", \"static\", \"stddev_pop\", \"stddev_samp\", \"submultiset\", \"subset\", \"substring\", \"substring_regex\", \"succeeds\", \"sum\", \"symmetric\", \"system\", \"system_time\", \"system_user\", \"table\", \"tablesample\", \"tan\", \"tanh\", \"then\", \"time\", \"timestamp\", \"timezone_hour\", \"timezone_minute\", \"to\", \"trailing\", \"translate\", \"translate_regex\", \"translation\", \"treat\", \"trigger\", \"trim\", \"trim_array\", \"true\", \"truncate\", \"uescape\", \"union\", \"unique\", \"unknown\", \"unnest\", \"update   \", \"upper\", \"user\", \"using\", \"value\", \"values\", \"value_of\", \"var_pop\", \"var_samp\", \"varbinary\", \"varchar\", \"varying\", \"versioning\", \"when\", \"whenever\", \"where\", \"width_bucket\", \"window\", \"with\", \"within\", \"without\", \"year\"];\n\n  // these are reserved words we have identified to be functions\n  // and should only be highlighted in a dispatch-like context\n  // ie, array_agg(...), etc.\n  const RESERVED_FUNCTIONS = [\"abs\", \"acos\", \"array_agg\", \"asin\", \"atan\", \"avg\", \"cast\", \"ceil\", \"ceiling\", \"coalesce\", \"corr\", \"cos\", \"cosh\", \"count\", \"covar_pop\", \"covar_samp\", \"cume_dist\", \"dense_rank\", \"deref\", \"element\", \"exp\", \"extract\", \"first_value\", \"floor\", \"json_array\", \"json_arrayagg\", \"json_exists\", \"json_object\", \"json_objectagg\", \"json_query\", \"json_table\", \"json_table_primitive\", \"json_value\", \"lag\", \"last_value\", \"lead\", \"listagg\", \"ln\", \"log\", \"log10\", \"lower\", \"max\", \"min\", \"mod\", \"nth_value\", \"ntile\", \"nullif\", \"percent_rank\", \"percentile_cont\", \"percentile_disc\", \"position\", \"position_regex\", \"power\", \"rank\", \"regr_avgx\", \"regr_avgy\", \"regr_count\", \"regr_intercept\", \"regr_r2\", \"regr_slope\", \"regr_sxx\", \"regr_sxy\", \"regr_syy\", \"row_number\", \"sin\", \"sinh\", \"sqrt\", \"stddev_pop\", \"stddev_samp\", \"substring\", \"substring_regex\", \"sum\", \"tan\", \"tanh\", \"translate\", \"translate_regex\", \"treat\", \"trim\", \"trim_array\", \"unnest\", \"upper\", \"value_of\", \"var_pop\", \"var_samp\", \"width_bucket\"];\n\n  // these functions can\n  const POSSIBLE_WITHOUT_PARENS = [\"current_catalog\", \"current_date\", \"current_default_transform_group\", \"current_path\", \"current_role\", \"current_schema\", \"current_transform_group_for_type\", \"current_user\", \"session_user\", \"system_time\", \"system_user\", \"current_time\", \"localtime\", \"current_timestamp\", \"localtimestamp\"];\n\n  // those exist to boost relevance making these very\n  // \"SQL like\" keyword combos worth +1 extra relevance\n  const COMBOS = [\"create table\", \"insert into\", \"primary key\", \"foreign key\", \"not null\", \"alter table\", \"add constraint\", \"grouping sets\", \"on overflow\", \"character set\", \"respect nulls\", \"ignore nulls\", \"nulls first\", \"nulls last\", \"depth first\", \"breadth first\"];\n  const FUNCTIONS = RESERVED_FUNCTIONS;\n  const KEYWORDS = [...RESERVED_WORDS, ...NON_RESERVED_WORDS].filter(keyword => {\n    return !RESERVED_FUNCTIONS.includes(keyword);\n  });\n  const VARIABLE = {\n    className: \"variable\",\n    begin: /@[a-z0-9]+/\n  };\n  const OPERATOR = {\n    className: \"operator\",\n    begin: /[-+*/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,\n    relevance: 0\n  };\n  const FUNCTION_CALL = {\n    begin: concat(/\\b/, either(...FUNCTIONS), /\\s*\\(/),\n    keywords: {\n      built_in: FUNCTIONS\n    }\n  };\n\n  // keywords with less than 3 letters are reduced in relevancy\n  function reduceRelevancy(list, {\n    exceptions,\n    when\n  } = {}) {\n    const qualifyFn = when;\n    exceptions = exceptions || [];\n    return list.map(item => {\n      if (item.match(/\\|\\d+$/) || exceptions.includes(item)) {\n        return item;\n      } else if (qualifyFn(item)) {\n        return `${item}|0`;\n      } else {\n        return item;\n      }\n    });\n  }\n  return {\n    name: 'SQL',\n    case_insensitive: true,\n    // does not include {} or HTML tags `</`\n    illegal: /[{}]|<\\//,\n    keywords: {\n      $pattern: /\\b[\\w\\.]+/,\n      keyword: reduceRelevancy(KEYWORDS, {\n        when: x => x.length < 3\n      }),\n      literal: LITERALS,\n      type: TYPES,\n      built_in: POSSIBLE_WITHOUT_PARENS\n    },\n    contains: [{\n      begin: either(...COMBOS),\n      keywords: {\n        $pattern: /[\\w\\.]+/,\n        keyword: KEYWORDS.concat(COMBOS),\n        literal: LITERALS,\n        type: TYPES\n      }\n    }, {\n      className: \"type\",\n      begin: either(...MULTI_WORD_TYPES)\n    }, FUNCTION_CALL, VARIABLE, STRING, QUOTED_IDENTIFIER, hljs.C_NUMBER_MODE, hljs.C_BLOCK_COMMENT_MODE, COMMENT_MODE, OPERATOR]\n  };\n}\nmodule.exports = sql;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "either", "sql", "hljs", "COMMENT_MODE", "COMMENT", "STRING", "className", "variants", "begin", "end", "contains", "QUOTED_IDENTIFIER", "LITERALS", "MULTI_WORD_TYPES", "TYPES", "NON_RESERVED_WORDS", "RESERVED_WORDS", "RESERVED_FUNCTIONS", "POSSIBLE_WITHOUT_PARENS", "COMBOS", "FUNCTIONS", "KEYWORDS", "filter", "keyword", "includes", "VARIABLE", "OPERATOR", "relevance", "FUNCTION_CALL", "keywords", "built_in", "reduceRelevancy", "list", "exceptions", "when", "qualifyFn", "item", "match", "name", "case_insensitive", "illegal", "$pattern", "length", "literal", "type", "C_NUMBER_MODE", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/sql.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\n Language: SQL\n Website: https://en.wikipedia.org/wiki/SQL\n Category: common, database\n */\n\nfunction sql(hljs) {\n  const COMMENT_MODE = hljs.COMMENT('--', '$');\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [\n          {begin: /''/ }\n        ]\n      }\n    ]\n  };\n  const QUOTED_IDENTIFIER = {\n    begin: /\"/,\n    end: /\"/,\n    contains: [ { begin: /\"\"/ } ]\n  };\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    // Not sure it's correct to call NULL literal, and clauses like IS [NOT] NULL look strange that way.\n    // \"null\",\n    \"unknown\"\n  ];\n\n  const MULTI_WORD_TYPES = [\n    \"double precision\",\n    \"large object\",\n    \"with timezone\",\n    \"without timezone\"\n  ];\n\n  const TYPES = [\n    'bigint',\n    'binary',\n    'blob',\n    'boolean',\n    'char',\n    'character',\n    'clob',\n    'date',\n    'dec',\n    'decfloat',\n    'decimal',\n    'float',\n    'int',\n    'integer',\n    'interval',\n    'nchar',\n    'nclob',\n    'national',\n    'numeric',\n    'real',\n    'row',\n    'smallint',\n    'time',\n    'timestamp',\n    'varchar',\n    'varying', // modifier (character varying)\n    'varbinary'\n  ];\n\n  const NON_RESERVED_WORDS = [\n    \"add\",\n    \"asc\",\n    \"collation\",\n    \"desc\",\n    \"final\",\n    \"first\",\n    \"last\",\n    \"view\"\n  ];\n\n  // https://jakewheat.github.io/sql-overview/sql-2016-foundation-grammar.html#reserved-word\n  const RESERVED_WORDS = [\n    \"abs\",\n    \"acos\",\n    \"all\",\n    \"allocate\",\n    \"alter\",\n    \"and\",\n    \"any\",\n    \"are\",\n    \"array\",\n    \"array_agg\",\n    \"array_max_cardinality\",\n    \"as\",\n    \"asensitive\",\n    \"asin\",\n    \"asymmetric\",\n    \"at\",\n    \"atan\",\n    \"atomic\",\n    \"authorization\",\n    \"avg\",\n    \"begin\",\n    \"begin_frame\",\n    \"begin_partition\",\n    \"between\",\n    \"bigint\",\n    \"binary\",\n    \"blob\",\n    \"boolean\",\n    \"both\",\n    \"by\",\n    \"call\",\n    \"called\",\n    \"cardinality\",\n    \"cascaded\",\n    \"case\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"char\",\n    \"char_length\",\n    \"character\",\n    \"character_length\",\n    \"check\",\n    \"classifier\",\n    \"clob\",\n    \"close\",\n    \"coalesce\",\n    \"collate\",\n    \"collect\",\n    \"column\",\n    \"commit\",\n    \"condition\",\n    \"connect\",\n    \"constraint\",\n    \"contains\",\n    \"convert\",\n    \"copy\",\n    \"corr\",\n    \"corresponding\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"create\",\n    \"cross\",\n    \"cube\",\n    \"cume_dist\",\n    \"current\",\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_row\",\n    \"current_schema\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"current_path\",\n    \"current_role\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"cursor\",\n    \"cycle\",\n    \"date\",\n    \"day\",\n    \"deallocate\",\n    \"dec\",\n    \"decimal\",\n    \"decfloat\",\n    \"declare\",\n    \"default\",\n    \"define\",\n    \"delete\",\n    \"dense_rank\",\n    \"deref\",\n    \"describe\",\n    \"deterministic\",\n    \"disconnect\",\n    \"distinct\",\n    \"double\",\n    \"drop\",\n    \"dynamic\",\n    \"each\",\n    \"element\",\n    \"else\",\n    \"empty\",\n    \"end\",\n    \"end_frame\",\n    \"end_partition\",\n    \"end-exec\",\n    \"equals\",\n    \"escape\",\n    \"every\",\n    \"except\",\n    \"exec\",\n    \"execute\",\n    \"exists\",\n    \"exp\",\n    \"external\",\n    \"extract\",\n    \"false\",\n    \"fetch\",\n    \"filter\",\n    \"first_value\",\n    \"float\",\n    \"floor\",\n    \"for\",\n    \"foreign\",\n    \"frame_row\",\n    \"free\",\n    \"from\",\n    \"full\",\n    \"function\",\n    \"fusion\",\n    \"get\",\n    \"global\",\n    \"grant\",\n    \"group\",\n    \"grouping\",\n    \"groups\",\n    \"having\",\n    \"hold\",\n    \"hour\",\n    \"identity\",\n    \"in\",\n    \"indicator\",\n    \"initial\",\n    \"inner\",\n    \"inout\",\n    \"insensitive\",\n    \"insert\",\n    \"int\",\n    \"integer\",\n    \"intersect\",\n    \"intersection\",\n    \"interval\",\n    \"into\",\n    \"is\",\n    \"join\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"language\",\n    \"large\",\n    \"last_value\",\n    \"lateral\",\n    \"lead\",\n    \"leading\",\n    \"left\",\n    \"like\",\n    \"like_regex\",\n    \"listagg\",\n    \"ln\",\n    \"local\",\n    \"localtime\",\n    \"localtimestamp\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"match\",\n    \"match_number\",\n    \"match_recognize\",\n    \"matches\",\n    \"max\",\n    \"member\",\n    \"merge\",\n    \"method\",\n    \"min\",\n    \"minute\",\n    \"mod\",\n    \"modifies\",\n    \"module\",\n    \"month\",\n    \"multiset\",\n    \"national\",\n    \"natural\",\n    \"nchar\",\n    \"nclob\",\n    \"new\",\n    \"no\",\n    \"none\",\n    \"normalize\",\n    \"not\",\n    \"nth_value\",\n    \"ntile\",\n    \"null\",\n    \"nullif\",\n    \"numeric\",\n    \"octet_length\",\n    \"occurrences_regex\",\n    \"of\",\n    \"offset\",\n    \"old\",\n    \"omit\",\n    \"on\",\n    \"one\",\n    \"only\",\n    \"open\",\n    \"or\",\n    \"order\",\n    \"out\",\n    \"outer\",\n    \"over\",\n    \"overlaps\",\n    \"overlay\",\n    \"parameter\",\n    \"partition\",\n    \"pattern\",\n    \"per\",\n    \"percent\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"period\",\n    \"portion\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"precedes\",\n    \"precision\",\n    \"prepare\",\n    \"primary\",\n    \"procedure\",\n    \"ptf\",\n    \"range\",\n    \"rank\",\n    \"reads\",\n    \"real\",\n    \"recursive\",\n    \"ref\",\n    \"references\",\n    \"referencing\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"release\",\n    \"result\",\n    \"return\",\n    \"returns\",\n    \"revoke\",\n    \"right\",\n    \"rollback\",\n    \"rollup\",\n    \"row\",\n    \"row_number\",\n    \"rows\",\n    \"running\",\n    \"savepoint\",\n    \"scope\",\n    \"scroll\",\n    \"search\",\n    \"second\",\n    \"seek\",\n    \"select\",\n    \"sensitive\",\n    \"session_user\",\n    \"set\",\n    \"show\",\n    \"similar\",\n    \"sin\",\n    \"sinh\",\n    \"skip\",\n    \"smallint\",\n    \"some\",\n    \"specific\",\n    \"specifictype\",\n    \"sql\",\n    \"sqlexception\",\n    \"sqlstate\",\n    \"sqlwarning\",\n    \"sqrt\",\n    \"start\",\n    \"static\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"submultiset\",\n    \"subset\",\n    \"substring\",\n    \"substring_regex\",\n    \"succeeds\",\n    \"sum\",\n    \"symmetric\",\n    \"system\",\n    \"system_time\",\n    \"system_user\",\n    \"table\",\n    \"tablesample\",\n    \"tan\",\n    \"tanh\",\n    \"then\",\n    \"time\",\n    \"timestamp\",\n    \"timezone_hour\",\n    \"timezone_minute\",\n    \"to\",\n    \"trailing\",\n    \"translate\",\n    \"translate_regex\",\n    \"translation\",\n    \"treat\",\n    \"trigger\",\n    \"trim\",\n    \"trim_array\",\n    \"true\",\n    \"truncate\",\n    \"uescape\",\n    \"union\",\n    \"unique\",\n    \"unknown\",\n    \"unnest\",\n    \"update   \",\n    \"upper\",\n    \"user\",\n    \"using\",\n    \"value\",\n    \"values\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"varbinary\",\n    \"varchar\",\n    \"varying\",\n    \"versioning\",\n    \"when\",\n    \"whenever\",\n    \"where\",\n    \"width_bucket\",\n    \"window\",\n    \"with\",\n    \"within\",\n    \"without\",\n    \"year\",\n  ];\n\n  // these are reserved words we have identified to be functions\n  // and should only be highlighted in a dispatch-like context\n  // ie, array_agg(...), etc.\n  const RESERVED_FUNCTIONS = [\n    \"abs\",\n    \"acos\",\n    \"array_agg\",\n    \"asin\",\n    \"atan\",\n    \"avg\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"coalesce\",\n    \"corr\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"cume_dist\",\n    \"dense_rank\",\n    \"deref\",\n    \"element\",\n    \"exp\",\n    \"extract\",\n    \"first_value\",\n    \"floor\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"listagg\",\n    \"ln\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"max\",\n    \"min\",\n    \"mod\",\n    \"nth_value\",\n    \"ntile\",\n    \"nullif\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"rank\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"row_number\",\n    \"sin\",\n    \"sinh\",\n    \"sqrt\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"substring\",\n    \"substring_regex\",\n    \"sum\",\n    \"tan\",\n    \"tanh\",\n    \"translate\",\n    \"translate_regex\",\n    \"treat\",\n    \"trim\",\n    \"trim_array\",\n    \"unnest\",\n    \"upper\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"width_bucket\",\n  ];\n\n  // these functions can\n  const POSSIBLE_WITHOUT_PARENS = [\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_schema\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"session_user\",\n    \"system_time\",\n    \"system_user\",\n    \"current_time\",\n    \"localtime\",\n    \"current_timestamp\",\n    \"localtimestamp\"\n  ];\n\n  // those exist to boost relevance making these very\n  // \"SQL like\" keyword combos worth +1 extra relevance\n  const COMBOS = [\n    \"create table\",\n    \"insert into\",\n    \"primary key\",\n    \"foreign key\",\n    \"not null\",\n    \"alter table\",\n    \"add constraint\",\n    \"grouping sets\",\n    \"on overflow\",\n    \"character set\",\n    \"respect nulls\",\n    \"ignore nulls\",\n    \"nulls first\",\n    \"nulls last\",\n    \"depth first\",\n    \"breadth first\"\n  ];\n\n  const FUNCTIONS = RESERVED_FUNCTIONS;\n\n  const KEYWORDS = [...RESERVED_WORDS, ...NON_RESERVED_WORDS].filter((keyword) => {\n    return !RESERVED_FUNCTIONS.includes(keyword);\n  });\n\n  const VARIABLE = {\n    className: \"variable\",\n    begin: /@[a-z0-9]+/,\n  };\n\n  const OPERATOR = {\n    className: \"operator\",\n    begin: /[-+*/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,\n    relevance: 0,\n  };\n\n  const FUNCTION_CALL = {\n    begin: concat(/\\b/, either(...FUNCTIONS), /\\s*\\(/),\n    keywords: {\n      built_in: FUNCTIONS\n    }\n  };\n\n  // keywords with less than 3 letters are reduced in relevancy\n  function reduceRelevancy(list, {exceptions, when} = {}) {\n    const qualifyFn = when;\n    exceptions = exceptions || [];\n    return list.map((item) => {\n      if (item.match(/\\|\\d+$/) || exceptions.includes(item)) {\n        return item;\n      } else if (qualifyFn(item)) {\n        return `${item}|0`;\n      } else {\n        return item;\n      }\n    });\n  }\n\n  return {\n    name: 'SQL',\n    case_insensitive: true,\n    // does not include {} or HTML tags `</`\n    illegal: /[{}]|<\\//,\n    keywords: {\n      $pattern: /\\b[\\w\\.]+/,\n      keyword:\n        reduceRelevancy(KEYWORDS, { when: (x) => x.length < 3 }),\n      literal: LITERALS,\n      type: TYPES,\n      built_in: POSSIBLE_WITHOUT_PARENS\n    },\n    contains: [\n      {\n        begin: either(...COMBOS),\n        keywords: {\n          $pattern: /[\\w\\.]+/,\n          keyword: KEYWORDS.concat(COMBOS),\n          literal: LITERALS,\n          type: TYPES\n        },\n      },\n      {\n        className: \"type\",\n        begin: either(...MULTI_WORD_TYPES)\n      },\n      FUNCTION_CALL,\n      VARIABLE,\n      STRING,\n      QUOTED_IDENTIFIER,\n      hljs.C_NUMBER_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      COMMENT_MODE,\n      OPERATOR\n    ]\n  };\n}\n\nmodule.exports = sql;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAASK,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,YAAY,GAAGD,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EAC5C,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CACR;QAACF,KAAK,EAAE;MAAK,CAAC;IAElB,CAAC;EAEL,CAAC;EACD,MAAMG,iBAAiB,GAAG;IACxBH,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CAAE;MAAEF,KAAK,EAAE;IAAK,CAAC;EAC7B,CAAC;EAED,MAAMI,QAAQ,GAAG,CACf,MAAM,EACN,OAAO;EACP;EACA;EACA,SAAS,CACV;EAED,MAAMC,gBAAgB,GAAG,CACvB,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,kBAAkB,CACnB;EAED,MAAMC,KAAK,GAAG,CACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,SAAS,EACT,MAAM,EACN,WAAW,EACX,MAAM,EACN,MAAM,EACN,KAAK,EACL,UAAU,EACV,SAAS,EACT,OAAO,EACP,KAAK,EACL,SAAS,EACT,UAAU,EACV,OAAO,EACP,OAAO,EACP,UAAU,EACV,SAAS,EACT,MAAM,EACN,KAAK,EACL,UAAU,EACV,MAAM,EACN,WAAW,EACX,SAAS,EACT,SAAS;EAAE;EACX,WAAW,CACZ;EAED,MAAMC,kBAAkB,GAAG,CACzB,KAAK,EACL,KAAK,EACL,WAAW,EACX,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,CACP;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB,KAAK,EACL,MAAM,EACN,KAAK,EACL,UAAU,EACV,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,WAAW,EACX,uBAAuB,EACvB,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,eAAe,EACf,KAAK,EACL,OAAO,EACP,aAAa,EACb,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,SAAS,EACT,MAAM,EACN,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,aAAa,EACb,UAAU,EACV,MAAM,EACN,MAAM,EACN,MAAM,EACN,SAAS,EACT,MAAM,EACN,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,OAAO,EACP,YAAY,EACZ,MAAM,EACN,OAAO,EACP,UAAU,EACV,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,UAAU,EACV,SAAS,EACT,MAAM,EACN,MAAM,EACN,eAAe,EACf,KAAK,EACL,MAAM,EACN,OAAO,EACP,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,MAAM,EACN,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,cAAc,EACd,iCAAiC,EACjC,cAAc,EACd,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,mBAAmB,EACnB,cAAc,EACd,cAAc,EACd,kCAAkC,EAClC,cAAc,EACd,QAAQ,EACR,OAAO,EACP,MAAM,EACN,KAAK,EACL,YAAY,EACZ,KAAK,EACL,SAAS,EACT,UAAU,EACV,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,UAAU,EACV,eAAe,EACf,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,MAAM,EACN,SAAS,EACT,MAAM,EACN,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,eAAe,EACf,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,UAAU,EACV,SAAS,EACT,OAAO,EACP,OAAO,EACP,QAAQ,EACR,aAAa,EACb,OAAO,EACP,OAAO,EACP,KAAK,EACL,SAAS,EACT,WAAW,EACX,MAAM,EACN,MAAM,EACN,MAAM,EACN,UAAU,EACV,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,SAAS,EACT,OAAO,EACP,OAAO,EACP,aAAa,EACb,QAAQ,EACR,KAAK,EACL,SAAS,EACT,WAAW,EACX,cAAc,EACd,UAAU,EACV,MAAM,EACN,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,eAAe,EACf,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,sBAAsB,EACtB,YAAY,EACZ,KAAK,EACL,UAAU,EACV,OAAO,EACP,YAAY,EACZ,SAAS,EACT,MAAM,EACN,SAAS,EACT,MAAM,EACN,MAAM,EACN,YAAY,EACZ,SAAS,EACT,IAAI,EACJ,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO,EACP,cAAc,EACd,iBAAiB,EACjB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,KAAK,EACL,UAAU,EACV,QAAQ,EACR,OAAO,EACP,UAAU,EACV,UAAU,EACV,SAAS,EACT,OAAO,EACP,OAAO,EACP,KAAK,EACL,IAAI,EACJ,MAAM,EACN,WAAW,EACX,KAAK,EACL,WAAW,EACX,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,cAAc,EACd,mBAAmB,EACnB,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,IAAI,EACJ,OAAO,EACP,KAAK,EACL,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,SAAS,EACT,KAAK,EACL,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,WAAW,EACX,SAAS,EACT,SAAS,EACT,WAAW,EACX,KAAK,EACL,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACX,KAAK,EACL,YAAY,EACZ,aAAa,EACb,WAAW,EACX,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACT,YAAY,EACZ,UAAU,EACV,UAAU,EACV,UAAU,EACV,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,OAAO,EACP,UAAU,EACV,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,MAAM,EACN,SAAS,EACT,WAAW,EACX,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,WAAW,EACX,cAAc,EACd,KAAK,EACL,MAAM,EACN,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,EACN,UAAU,EACV,MAAM,EACN,UAAU,EACV,cAAc,EACd,KAAK,EACL,cAAc,EACd,UAAU,EACV,YAAY,EACZ,MAAM,EACN,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,aAAa,EACb,QAAQ,EACR,WAAW,EACX,iBAAiB,EACjB,UAAU,EACV,KAAK,EACL,WAAW,EACX,QAAQ,EACR,aAAa,EACb,aAAa,EACb,OAAO,EACP,aAAa,EACb,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,WAAW,EACX,eAAe,EACf,iBAAiB,EACjB,IAAI,EACJ,UAAU,EACV,WAAW,EACX,iBAAiB,EACjB,aAAa,EACb,OAAO,EACP,SAAS,EACT,MAAM,EACN,YAAY,EACZ,MAAM,EACN,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,WAAW,EACX,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,SAAS,EACT,SAAS,EACT,YAAY,EACZ,MAAM,EACN,UAAU,EACV,OAAO,EACP,cAAc,EACd,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,SAAS,EACT,MAAM,CACP;;EAED;EACA;EACA;EACA,MAAMC,kBAAkB,GAAG,CACzB,KAAK,EACL,MAAM,EACN,WAAW,EACX,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,SAAS,EACT,UAAU,EACV,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,WAAW,EACX,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,OAAO,EACP,SAAS,EACT,KAAK,EACL,SAAS,EACT,aAAa,EACb,OAAO,EACP,YAAY,EACZ,eAAe,EACf,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,sBAAsB,EACtB,YAAY,EACZ,KAAK,EACL,YAAY,EACZ,MAAM,EACN,SAAS,EACT,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,cAAc,EACd,iBAAiB,EACjB,iBAAiB,EACjB,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,WAAW,EACX,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACT,YAAY,EACZ,UAAU,EACV,UAAU,EACV,UAAU,EACV,YAAY,EACZ,KAAK,EACL,MAAM,EACN,MAAM,EACN,YAAY,EACZ,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,KAAK,EACL,KAAK,EACL,MAAM,EACN,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,UAAU,EACV,SAAS,EACT,UAAU,EACV,cAAc,CACf;;EAED;EACA,MAAMC,uBAAuB,GAAG,CAC9B,iBAAiB,EACjB,cAAc,EACd,iCAAiC,EACjC,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,kCAAkC,EAClC,cAAc,EACd,cAAc,EACd,aAAa,EACb,aAAa,EACb,cAAc,EACd,WAAW,EACX,mBAAmB,EACnB,gBAAgB,CACjB;;EAED;EACA;EACA,MAAMC,MAAM,GAAG,CACb,cAAc,EACd,aAAa,EACb,aAAa,EACb,aAAa,EACb,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,eAAe,EACf,eAAe,EACf,cAAc,EACd,aAAa,EACb,YAAY,EACZ,aAAa,EACb,eAAe,CAChB;EAED,MAAMC,SAAS,GAAGH,kBAAkB;EAEpC,MAAMI,QAAQ,GAAG,CAAC,GAAGL,cAAc,EAAE,GAAGD,kBAAkB,CAAC,CAACO,MAAM,CAAEC,OAAO,IAAK;IAC9E,OAAO,CAACN,kBAAkB,CAACO,QAAQ,CAACD,OAAO,CAAC;EAC9C,CAAC,CAAC;EAEF,MAAME,QAAQ,GAAG;IACfnB,SAAS,EAAE,UAAU;IACrBE,KAAK,EAAE;EACT,CAAC;EAED,MAAMkB,QAAQ,GAAG;IACfpB,SAAS,EAAE,UAAU;IACrBE,KAAK,EAAE,+CAA+C;IACtDmB,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBpB,KAAK,EAAEd,MAAM,CAAC,IAAI,EAAEM,MAAM,CAAC,GAAGoB,SAAS,CAAC,EAAE,OAAO,CAAC;IAClDS,QAAQ,EAAE;MACRC,QAAQ,EAAEV;IACZ;EACF,CAAC;;EAED;EACA,SAASW,eAAeA,CAACC,IAAI,EAAE;IAACC,UAAU;IAAEC;EAAI,CAAC,GAAG,CAAC,CAAC,EAAE;IACtD,MAAMC,SAAS,GAAGD,IAAI;IACtBD,UAAU,GAAGA,UAAU,IAAI,EAAE;IAC7B,OAAOD,IAAI,CAACnC,GAAG,CAAEuC,IAAI,IAAK;MACxB,IAAIA,IAAI,CAACC,KAAK,CAAC,QAAQ,CAAC,IAAIJ,UAAU,CAACT,QAAQ,CAACY,IAAI,CAAC,EAAE;QACrD,OAAOA,IAAI;MACb,CAAC,MAAM,IAAID,SAAS,CAACC,IAAI,CAAC,EAAE;QAC1B,OAAO,GAAGA,IAAI,IAAI;MACpB,CAAC,MAAM;QACL,OAAOA,IAAI;MACb;IACF,CAAC,CAAC;EACJ;EAEA,OAAO;IACLE,IAAI,EAAE,KAAK;IACXC,gBAAgB,EAAE,IAAI;IACtB;IACAC,OAAO,EAAE,UAAU;IACnBX,QAAQ,EAAE;MACRY,QAAQ,EAAE,WAAW;MACrBlB,OAAO,EACLQ,eAAe,CAACV,QAAQ,EAAE;QAAEa,IAAI,EAAGpC,CAAC,IAAKA,CAAC,CAAC4C,MAAM,GAAG;MAAE,CAAC,CAAC;MAC1DC,OAAO,EAAE/B,QAAQ;MACjBgC,IAAI,EAAE9B,KAAK;MACXgB,QAAQ,EAAEZ;IACZ,CAAC;IACDR,QAAQ,EAAE,CACR;MACEF,KAAK,EAAER,MAAM,CAAC,GAAGmB,MAAM,CAAC;MACxBU,QAAQ,EAAE;QACRY,QAAQ,EAAE,SAAS;QACnBlB,OAAO,EAAEF,QAAQ,CAAC3B,MAAM,CAACyB,MAAM,CAAC;QAChCwB,OAAO,EAAE/B,QAAQ;QACjBgC,IAAI,EAAE9B;MACR;IACF,CAAC,EACD;MACER,SAAS,EAAE,MAAM;MACjBE,KAAK,EAAER,MAAM,CAAC,GAAGa,gBAAgB;IACnC,CAAC,EACDe,aAAa,EACbH,QAAQ,EACRpB,MAAM,EACNM,iBAAiB,EACjBT,IAAI,CAAC2C,aAAa,EAClB3C,IAAI,CAAC4C,oBAAoB,EACzB3C,YAAY,EACZuB,QAAQ;EAEZ,CAAC;AACH;AAEAqB,MAAM,CAACC,OAAO,GAAG/C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}