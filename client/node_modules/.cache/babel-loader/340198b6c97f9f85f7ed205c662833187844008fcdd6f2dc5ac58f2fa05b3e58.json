{"ast": null, "code": "'use strict';\n\nmodule.exports = pascaligo;\npascaligo.displayName = 'pascaligo';\npascaligo.aliases = [];\nfunction pascaligo(Prism) {\n  ;\n  (function (Prism) {\n    // Pascaligo is a layer 2 smart contract language for the tezos blockchain\n    var braces = /\\((?:[^()]|\\((?:[^()]|\\([^()]*\\))*\\))*\\)/.source;\n    var type = /(?:\\b\\w+(?:<braces>)?|<braces>)/.source.replace(/<braces>/g, function () {\n      return braces;\n    });\n    var pascaligo = Prism.languages.pascaligo = {\n      comment: /\\(\\*[\\s\\S]+?\\*\\)|\\/\\/.*/,\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|\\^[a-z]/i,\n        greedy: true\n      },\n      'class-name': [{\n        pattern: RegExp(/(\\btype\\s+\\w+\\s+is\\s+)<type>/.source.replace(/<type>/g, function () {\n          return type;\n        }), 'i'),\n        lookbehind: true,\n        inside: null // see below\n      }, {\n        pattern: RegExp(/<type>(?=\\s+is\\b)/.source.replace(/<type>/g, function () {\n          return type;\n        }), 'i'),\n        inside: null // see below\n      }, {\n        pattern: RegExp(/(:\\s*)<type>/.source.replace(/<type>/g, function () {\n          return type;\n        })),\n        lookbehind: true,\n        inside: null // see below\n      }],\n      keyword: {\n        pattern: /(^|[^&])\\b(?:begin|block|case|const|else|end|fail|for|from|function|if|is|nil|of|remove|return|skip|then|type|var|while|with)\\b/i,\n        lookbehind: true\n      },\n      boolean: {\n        pattern: /(^|[^&])\\b(?:False|True)\\b/i,\n        lookbehind: true\n      },\n      builtin: {\n        pattern: /(^|[^&])\\b(?:bool|int|list|map|nat|record|string|unit)\\b/i,\n        lookbehind: true\n      },\n      function: /\\b\\w+(?=\\s*\\()/,\n      number: [\n      // Hexadecimal, octal and binary\n      /%[01]+|&[0-7]+|\\$[a-f\\d]+/i,\n      // Decimal\n      /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?(?:mtz|n)?/i],\n      operator: /->|=\\/=|\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=|]|\\b(?:and|mod|or)\\b/,\n      punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.{}]/\n    };\n    var classNameInside = ['comment', 'keyword', 'builtin', 'operator', 'punctuation'].reduce(function (accum, key) {\n      accum[key] = pascaligo[key];\n      return accum;\n    }, {});\n    pascaligo['class-name'].forEach(function (p) {\n      p.inside = classNameInside;\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "pascaligo", "displayName", "aliases", "Prism", "braces", "source", "type", "replace", "languages", "comment", "string", "pattern", "greedy", "RegExp", "lookbehind", "inside", "keyword", "boolean", "builtin", "function", "number", "operator", "punctuation", "classNameInside", "reduce", "accum", "key", "for<PERSON>ach", "p"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/pascaligo.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pascaligo\npascaligo.displayName = 'pascaligo'\npascaligo.aliases = []\nfunction pascaligo(Prism) {\n  ;(function (Prism) {\n    // Pascaligo is a layer 2 smart contract language for the tezos blockchain\n    var braces = /\\((?:[^()]|\\((?:[^()]|\\([^()]*\\))*\\))*\\)/.source\n    var type = /(?:\\b\\w+(?:<braces>)?|<braces>)/.source.replace(\n      /<braces>/g,\n      function () {\n        return braces\n      }\n    )\n    var pascaligo = (Prism.languages.pascaligo = {\n      comment: /\\(\\*[\\s\\S]+?\\*\\)|\\/\\/.*/,\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|\\^[a-z]/i,\n        greedy: true\n      },\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(\\btype\\s+\\w+\\s+is\\s+)<type>/.source.replace(\n              /<type>/g,\n              function () {\n                return type\n              }\n            ),\n            'i'\n          ),\n          lookbehind: true,\n          inside: null // see below\n        },\n        {\n          pattern: RegExp(\n            /<type>(?=\\s+is\\b)/.source.replace(/<type>/g, function () {\n              return type\n            }),\n            'i'\n          ),\n          inside: null // see below\n        },\n        {\n          pattern: RegExp(\n            /(:\\s*)<type>/.source.replace(/<type>/g, function () {\n              return type\n            })\n          ),\n          lookbehind: true,\n          inside: null // see below\n        }\n      ],\n      keyword: {\n        pattern:\n          /(^|[^&])\\b(?:begin|block|case|const|else|end|fail|for|from|function|if|is|nil|of|remove|return|skip|then|type|var|while|with)\\b/i,\n        lookbehind: true\n      },\n      boolean: {\n        pattern: /(^|[^&])\\b(?:False|True)\\b/i,\n        lookbehind: true\n      },\n      builtin: {\n        pattern: /(^|[^&])\\b(?:bool|int|list|map|nat|record|string|unit)\\b/i,\n        lookbehind: true\n      },\n      function: /\\b\\w+(?=\\s*\\()/,\n      number: [\n        // Hexadecimal, octal and binary\n        /%[01]+|&[0-7]+|\\$[a-f\\d]+/i, // Decimal\n        /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?(?:mtz|n)?/i\n      ],\n      operator:\n        /->|=\\/=|\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=|]|\\b(?:and|mod|or)\\b/,\n      punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.{}]/\n    })\n    var classNameInside = [\n      'comment',\n      'keyword',\n      'builtin',\n      'operator',\n      'punctuation'\n    ].reduce(function (accum, key) {\n      accum[key] = pascaligo[key]\n      return accum\n    }, {})\n    pascaligo['class-name'].forEach(function (p) {\n      p.inside = classNameInside\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA,IAAIC,MAAM,GAAG,0CAA0C,CAACC,MAAM;IAC9D,IAAIC,IAAI,GAAG,iCAAiC,CAACD,MAAM,CAACE,OAAO,CACzD,WAAW,EACX,YAAY;MACV,OAAOH,MAAM;IACf,CACF,CAAC;IACD,IAAIJ,SAAS,GAAIG,KAAK,CAACK,SAAS,CAACR,SAAS,GAAG;MAC3CS,OAAO,EAAE,yBAAyB;MAClCC,MAAM,EAAE;QACNC,OAAO,EAAE,6CAA6C;QACtDC,MAAM,EAAE;MACV,CAAC;MACD,YAAY,EAAE,CACZ;QACED,OAAO,EAAEE,MAAM,CACb,8BAA8B,CAACR,MAAM,CAACE,OAAO,CAC3C,SAAS,EACT,YAAY;UACV,OAAOD,IAAI;QACb,CACF,CAAC,EACD,GACF,CAAC;QACDQ,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI,CAAC;MACf,CAAC,EACD;QACEJ,OAAO,EAAEE,MAAM,CACb,mBAAmB,CAACR,MAAM,CAACE,OAAO,CAAC,SAAS,EAAE,YAAY;UACxD,OAAOD,IAAI;QACb,CAAC,CAAC,EACF,GACF,CAAC;QACDS,MAAM,EAAE,IAAI,CAAC;MACf,CAAC,EACD;QACEJ,OAAO,EAAEE,MAAM,CACb,cAAc,CAACR,MAAM,CAACE,OAAO,CAAC,SAAS,EAAE,YAAY;UACnD,OAAOD,IAAI;QACb,CAAC,CACH,CAAC;QACDQ,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI,CAAC;MACf,CAAC,CACF;MACDC,OAAO,EAAE;QACPL,OAAO,EACL,kIAAkI;QACpIG,UAAU,EAAE;MACd,CAAC;MACDG,OAAO,EAAE;QACPN,OAAO,EAAE,6BAA6B;QACtCG,UAAU,EAAE;MACd,CAAC;MACDI,OAAO,EAAE;QACPP,OAAO,EAAE,2DAA2D;QACpEG,UAAU,EAAE;MACd,CAAC;MACDK,QAAQ,EAAE,gBAAgB;MAC1BC,MAAM,EAAE;MACN;MACA,4BAA4B;MAAE;MAC9B,0CAA0C,CAC3C;MACDC,QAAQ,EACN,0EAA0E;MAC5EC,WAAW,EAAE;IACf,CAAE;IACF,IAAIC,eAAe,GAAG,CACpB,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,aAAa,CACd,CAACC,MAAM,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;MAC7BD,KAAK,CAACC,GAAG,CAAC,GAAG1B,SAAS,CAAC0B,GAAG,CAAC;MAC3B,OAAOD,KAAK;IACd,CAAC,EAAE,CAAC,CAAC,CAAC;IACNzB,SAAS,CAAC,YAAY,CAAC,CAAC2B,OAAO,CAAC,UAAUC,CAAC,EAAE;MAC3CA,CAAC,CAACb,MAAM,GAAGQ,eAAe;IAC5B,CAAC,CAAC;EACJ,CAAC,EAAEpB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}