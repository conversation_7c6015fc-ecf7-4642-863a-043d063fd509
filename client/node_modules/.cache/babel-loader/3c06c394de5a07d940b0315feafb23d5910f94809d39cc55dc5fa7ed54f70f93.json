{"ast": null, "code": "/*\nLanguage: Django\nDescription: Django is a high-level Python Web framework that encourages rapid development and clean, pragmatic design.\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.djangoproject.com\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction django(hljs) {\n  const FILTER = {\n    begin: /\\|[A-Za-z]+:?/,\n    keywords: {\n      name: 'truncatewords removetags linebreaksbr yesno get_digit timesince random striptags ' + 'filesizeformat escape linebreaks length_is ljust rjust cut urlize fix_ampersands ' + 'title floatformat capfirst pprint divisibleby add make_list unordered_list urlencode ' + 'timeuntil urlizetrunc wordcount stringformat linenumbers slice date dictsort ' + 'dictsortreversed default_if_none pluralize lower join center default ' + 'truncatewords_html upper length phone2numeric wordwrap time addslashes slugify first ' + 'escapejs force_escape iriencode last safe safeseq truncatechars localize unlocalize ' + 'localtime utc timezone'\n    },\n    contains: [hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE]\n  };\n  return {\n    name: 'Django',\n    aliases: ['jinja'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [hljs.COMMENT(/\\{%\\s*comment\\s*%\\}/, /\\{%\\s*endcomment\\s*%\\}/), hljs.COMMENT(/\\{#/, /#\\}/), {\n      className: 'template-tag',\n      begin: /\\{%/,\n      end: /%\\}/,\n      contains: [{\n        className: 'name',\n        begin: /\\w+/,\n        keywords: {\n          name: 'comment endcomment load templatetag ifchanged endifchanged if endif firstof for ' + 'endfor ifnotequal endifnotequal widthratio extends include spaceless ' + 'endspaceless regroup ifequal endifequal ssi now with cycle url filter ' + 'endfilter debug block endblock else autoescape endautoescape csrf_token empty elif ' + 'endwith static trans blocktrans endblocktrans get_static_prefix get_media_prefix ' + 'plural get_current_language language get_available_languages ' + 'get_current_language_bidi get_language_info get_language_info_list localize ' + 'endlocalize localtime endlocaltime timezone endtimezone get_current_timezone ' + 'verbatim'\n        },\n        starts: {\n          endsWithParent: true,\n          keywords: 'in by as',\n          contains: [FILTER],\n          relevance: 0\n        }\n      }]\n    }, {\n      className: 'template-variable',\n      begin: /\\{\\{/,\n      end: /\\}\\}/,\n      contains: [FILTER]\n    }]\n  };\n}\nmodule.exports = django;", "map": {"version": 3, "names": ["django", "hljs", "FILTER", "begin", "keywords", "name", "contains", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "aliases", "case_insensitive", "subLanguage", "COMMENT", "className", "end", "starts", "endsWithParent", "relevance", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/django.js"], "sourcesContent": ["/*\nLanguage: Django\nDescription: Django is a high-level Python Web framework that encourages rapid development and clean, pragmatic design.\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.djangoproject.com\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction django(hljs) {\n  const FILTER = {\n    begin: /\\|[A-Za-z]+:?/,\n    keywords: {\n      name:\n        'truncatewords removetags linebreaksbr yesno get_digit timesince random striptags ' +\n        'filesizeformat escape linebreaks length_is ljust rjust cut urlize fix_ampersands ' +\n        'title floatformat capfirst pprint divisibleby add make_list unordered_list urlencode ' +\n        'timeuntil urlizetrunc wordcount stringformat linenumbers slice date dictsort ' +\n        'dictsortreversed default_if_none pluralize lower join center default ' +\n        'truncatewords_html upper length phone2numeric wordwrap time addslashes slugify first ' +\n        'escapejs force_escape iriencode last safe safeseq truncatechars localize unlocalize ' +\n        'localtime utc timezone'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE\n    ]\n  };\n\n  return {\n    name: 'Django',\n    aliases: ['jinja'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      hljs.COMMENT(/\\{%\\s*comment\\s*%\\}/, /\\{%\\s*endcomment\\s*%\\}/),\n      hljs.COMMENT(/\\{#/, /#\\}/),\n      {\n        className: 'template-tag',\n        begin: /\\{%/,\n        end: /%\\}/,\n        contains: [{\n          className: 'name',\n          begin: /\\w+/,\n          keywords: {\n            name:\n                'comment endcomment load templatetag ifchanged endifchanged if endif firstof for ' +\n                'endfor ifnotequal endifnotequal widthratio extends include spaceless ' +\n                'endspaceless regroup ifequal endifequal ssi now with cycle url filter ' +\n                'endfilter debug block endblock else autoescape endautoescape csrf_token empty elif ' +\n                'endwith static trans blocktrans endblocktrans get_static_prefix get_media_prefix ' +\n                'plural get_current_language language get_available_languages ' +\n                'get_current_language_bidi get_language_info get_language_info_list localize ' +\n                'endlocalize localtime endlocaltime timezone endtimezone get_current_timezone ' +\n                'verbatim'\n          },\n          starts: {\n            endsWithParent: true,\n            keywords: 'in by as',\n            contains: [FILTER],\n            relevance: 0\n          }\n        }]\n      },\n      {\n        className: 'template-variable',\n        begin: /\\{\\{/,\n        end: /\\}\\}/,\n        contains: [FILTER]\n      }\n    ]\n  };\n}\n\nmodule.exports = django;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,MAAM,GAAG;IACbC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE;MACRC,IAAI,EACF,mFAAmF,GACnF,mFAAmF,GACnF,uFAAuF,GACvF,+EAA+E,GAC/E,uEAAuE,GACvE,uFAAuF,GACvF,sFAAsF,GACtF;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRL,IAAI,CAACM,iBAAiB,EACtBN,IAAI,CAACO,gBAAgB;EAEzB,CAAC;EAED,OAAO;IACLH,IAAI,EAAE,QAAQ;IACdI,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBC,gBAAgB,EAAE,IAAI;IACtBC,WAAW,EAAE,KAAK;IAClBL,QAAQ,EAAE,CACRL,IAAI,CAACW,OAAO,CAAC,qBAAqB,EAAE,wBAAwB,CAAC,EAC7DX,IAAI,CAACW,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAC1B;MACEC,SAAS,EAAE,cAAc;MACzBV,KAAK,EAAE,KAAK;MACZW,GAAG,EAAE,KAAK;MACVR,QAAQ,EAAE,CAAC;QACTO,SAAS,EAAE,MAAM;QACjBV,KAAK,EAAE,KAAK;QACZC,QAAQ,EAAE;UACRC,IAAI,EACA,kFAAkF,GAClF,uEAAuE,GACvE,wEAAwE,GACxE,qFAAqF,GACrF,mFAAmF,GACnF,+DAA+D,GAC/D,8EAA8E,GAC9E,+EAA+E,GAC/E;QACN,CAAC;QACDU,MAAM,EAAE;UACNC,cAAc,EAAE,IAAI;UACpBZ,QAAQ,EAAE,UAAU;UACpBE,QAAQ,EAAE,CAACJ,MAAM,CAAC;UAClBe,SAAS,EAAE;QACb;MACF,CAAC;IACH,CAAC,EACD;MACEJ,SAAS,EAAE,mBAAmB;MAC9BV,KAAK,EAAE,MAAM;MACbW,GAAG,EAAE,MAAM;MACXR,QAAQ,EAAE,CAACJ,MAAM;IACnB,CAAC;EAEL,CAAC;AACH;AAEAgB,MAAM,CAACC,OAAO,GAAGnB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}