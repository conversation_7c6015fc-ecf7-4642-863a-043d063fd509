{"ast": null, "code": "'use strict';\n\nmodule.exports = fsharp;\nfsharp.displayName = 'fsharp';\nfsharp.aliases = [];\nfunction fsharp(Prism) {\n  Prism.languages.fsharp = Prism.languages.extend('clike', {\n    comment: [{\n      pattern: /(^|[^\\\\])\\(\\*(?!\\))[\\s\\S]*?\\*\\)/,\n      lookbehind: true,\n      greedy: true\n    }, {\n      pattern: /(^|[^\\\\:])\\/\\/.*/,\n      lookbehind: true,\n      greedy: true\n    }],\n    string: {\n      pattern: /(?:\"\"\"[\\s\\S]*?\"\"\"|@\"(?:\"\"|[^\"])*\"|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")B?/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:exception|inherit|interface|new|of|type)\\s+|\\w\\s*:\\s*|\\s:\\??>\\s*)[.\\w]+\\b(?:\\s*(?:->|\\*)\\s*[.\\w]+\\b)*(?!\\s*[:.])/,\n      lookbehind: true,\n      inside: {\n        operator: /->|\\*/,\n        punctuation: /\\./\n      }\n    },\n    keyword: /\\b(?:let|return|use|yield)(?:!\\B|\\b)|\\b(?:abstract|and|as|asr|assert|atomic|base|begin|break|checked|class|component|const|constraint|constructor|continue|default|delegate|do|done|downcast|downto|eager|elif|else|end|event|exception|extern|external|false|finally|fixed|for|fun|function|functor|global|if|in|include|inherit|inline|interface|internal|land|lazy|lor|lsl|lsr|lxor|match|member|method|mixin|mod|module|mutable|namespace|new|not|null|object|of|open|or|override|parallel|private|process|protected|public|pure|rec|sealed|select|sig|static|struct|tailcall|then|to|trait|true|try|type|upcast|val|virtual|void|volatile|when|while|with)\\b/,\n    number: [/\\b0x[\\da-fA-F]+(?:LF|lf|un)?\\b/, /\\b0b[01]+(?:uy|y)?\\b/, /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[fm]|e[+-]?\\d+)?\\b/i, /\\b\\d+(?:[IlLsy]|UL|u[lsy]?)?\\b/],\n    operator: /([<>~&^])\\1\\1|([*.:<>&])\\2|<-|->|[!=:]=|<?\\|{1,3}>?|\\??(?:<=|>=|<>|[-+*/%=<>])\\??|[!?^&]|~[+~-]|:>|:\\?>?/\n  });\n  Prism.languages.insertBefore('fsharp', 'keyword', {\n    preprocessor: {\n      pattern: /(^[\\t ]*)#.*/m,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        directive: {\n          pattern: /(^#)\\b(?:else|endif|if|light|line|nowarn)\\b/,\n          lookbehind: true,\n          alias: 'keyword'\n        }\n      }\n    }\n  });\n  Prism.languages.insertBefore('fsharp', 'punctuation', {\n    'computation-expression': {\n      pattern: /\\b[_a-z]\\w*(?=\\s*\\{)/i,\n      alias: 'keyword'\n    }\n  });\n  Prism.languages.insertBefore('fsharp', 'string', {\n    annotation: {\n      pattern: /\\[<.+?>\\]/,\n      greedy: true,\n      inside: {\n        punctuation: /^\\[<|>\\]$/,\n        'class-name': {\n          pattern: /^\\w+$|(^|;\\s*)[A-Z]\\w*(?=\\()/,\n          lookbehind: true\n        },\n        'annotation-content': {\n          pattern: /[\\s\\S]+/,\n          inside: Prism.languages.fsharp\n        }\n      }\n    },\n    char: {\n      pattern: /'(?:[^\\\\']|\\\\(?:.|\\d{3}|x[a-fA-F\\d]{2}|u[a-fA-F\\d]{4}|U[a-fA-F\\d]{8}))'B?/,\n      greedy: true\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "fsharp", "displayName", "aliases", "Prism", "languages", "extend", "comment", "pattern", "lookbehind", "greedy", "string", "inside", "operator", "punctuation", "keyword", "number", "insertBefore", "preprocessor", "alias", "directive", "annotation", "char"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/fsharp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = fsharp\nfsharp.displayName = 'fsharp'\nfsharp.aliases = []\nfunction fsharp(Prism) {\n  Prism.languages.fsharp = Prism.languages.extend('clike', {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\(\\*(?!\\))[\\s\\S]*?\\*\\)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /(?:\"\"\"[\\s\\S]*?\"\"\"|@\"(?:\"\"|[^\"])*\"|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")B?/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:exception|inherit|interface|new|of|type)\\s+|\\w\\s*:\\s*|\\s:\\??>\\s*)[.\\w]+\\b(?:\\s*(?:->|\\*)\\s*[.\\w]+\\b)*(?!\\s*[:.])/,\n      lookbehind: true,\n      inside: {\n        operator: /->|\\*/,\n        punctuation: /\\./\n      }\n    },\n    keyword:\n      /\\b(?:let|return|use|yield)(?:!\\B|\\b)|\\b(?:abstract|and|as|asr|assert|atomic|base|begin|break|checked|class|component|const|constraint|constructor|continue|default|delegate|do|done|downcast|downto|eager|elif|else|end|event|exception|extern|external|false|finally|fixed|for|fun|function|functor|global|if|in|include|inherit|inline|interface|internal|land|lazy|lor|lsl|lsr|lxor|match|member|method|mixin|mod|module|mutable|namespace|new|not|null|object|of|open|or|override|parallel|private|process|protected|public|pure|rec|sealed|select|sig|static|struct|tailcall|then|to|trait|true|try|type|upcast|val|virtual|void|volatile|when|while|with)\\b/,\n    number: [\n      /\\b0x[\\da-fA-F]+(?:LF|lf|un)?\\b/,\n      /\\b0b[01]+(?:uy|y)?\\b/,\n      /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[fm]|e[+-]?\\d+)?\\b/i,\n      /\\b\\d+(?:[IlLsy]|UL|u[lsy]?)?\\b/\n    ],\n    operator:\n      /([<>~&^])\\1\\1|([*.:<>&])\\2|<-|->|[!=:]=|<?\\|{1,3}>?|\\??(?:<=|>=|<>|[-+*/%=<>])\\??|[!?^&]|~[+~-]|:>|:\\?>?/\n  })\n  Prism.languages.insertBefore('fsharp', 'keyword', {\n    preprocessor: {\n      pattern: /(^[\\t ]*)#.*/m,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        directive: {\n          pattern: /(^#)\\b(?:else|endif|if|light|line|nowarn)\\b/,\n          lookbehind: true,\n          alias: 'keyword'\n        }\n      }\n    }\n  })\n  Prism.languages.insertBefore('fsharp', 'punctuation', {\n    'computation-expression': {\n      pattern: /\\b[_a-z]\\w*(?=\\s*\\{)/i,\n      alias: 'keyword'\n    }\n  })\n  Prism.languages.insertBefore('fsharp', 'string', {\n    annotation: {\n      pattern: /\\[<.+?>\\]/,\n      greedy: true,\n      inside: {\n        punctuation: /^\\[<|>\\]$/,\n        'class-name': {\n          pattern: /^\\w+$|(^|;\\s*)[A-Z]\\w*(?=\\()/,\n          lookbehind: true\n        },\n        'annotation-content': {\n          pattern: /[\\s\\S]+/,\n          inside: Prism.languages.fsharp\n        }\n      }\n    },\n    char: {\n      pattern:\n        /'(?:[^\\\\']|\\\\(?:.|\\d{3}|x[a-fA-F\\d]{2}|u[a-fA-F\\d]{4}|U[a-fA-F\\d]{8}))'B?/,\n      greedy: true\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACvDC,OAAO,EAAE,CACP;MACEC,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,EACD;MACEF,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,6DAA6D;MACtEE,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZF,OAAO,EACL,wHAAwH;MAC1HC,UAAU,EAAE,IAAI;MAChBG,MAAM,EAAE;QACNC,QAAQ,EAAE,OAAO;QACjBC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EACL,moBAAmoB;IACroBC,MAAM,EAAE,CACN,gCAAgC,EAChC,sBAAsB,EACtB,mDAAmD,EACnD,gCAAgC,CACjC;IACDH,QAAQ,EACN;EACJ,CAAC,CAAC;EACFT,KAAK,CAACC,SAAS,CAACY,YAAY,CAAC,QAAQ,EAAE,SAAS,EAAE;IAChDC,YAAY,EAAE;MACZV,OAAO,EAAE,eAAe;MACxBC,UAAU,EAAE,IAAI;MAChBU,KAAK,EAAE,UAAU;MACjBP,MAAM,EAAE;QACNQ,SAAS,EAAE;UACTZ,OAAO,EAAE,6CAA6C;UACtDC,UAAU,EAAE,IAAI;UAChBU,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC,CAAC;EACFf,KAAK,CAACC,SAAS,CAACY,YAAY,CAAC,QAAQ,EAAE,aAAa,EAAE;IACpD,wBAAwB,EAAE;MACxBT,OAAO,EAAE,uBAAuB;MAChCW,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFf,KAAK,CAACC,SAAS,CAACY,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;IAC/CI,UAAU,EAAE;MACVb,OAAO,EAAE,WAAW;MACpBE,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACNE,WAAW,EAAE,WAAW;QACxB,YAAY,EAAE;UACZN,OAAO,EAAE,8BAA8B;UACvCC,UAAU,EAAE;QACd,CAAC;QACD,oBAAoB,EAAE;UACpBD,OAAO,EAAE,SAAS;UAClBI,MAAM,EAAER,KAAK,CAACC,SAAS,CAACJ;QAC1B;MACF;IACF,CAAC;IACDqB,IAAI,EAAE;MACJd,OAAO,EACL,2EAA2E;MAC7EE,MAAM,EAAE;IACV;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}