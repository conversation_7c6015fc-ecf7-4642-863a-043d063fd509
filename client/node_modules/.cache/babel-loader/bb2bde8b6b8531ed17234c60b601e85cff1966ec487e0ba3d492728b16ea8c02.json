{"ast": null, "code": "'use strict';\n\nmodule.exports = csp;\ncsp.displayName = 'csp';\ncsp.aliases = [];\nfunction csp(Prism) {\n  /**\n   * Original by <PERSON>.\n   *\n   * Reference: https://scotthelme.co.uk/csp-cheat-sheet/\n   *\n   * Supports the following:\n   *  - https://www.w3.org/TR/CSP1/\n   *  - https://www.w3.org/TR/CSP2/\n   *  - https://www.w3.org/TR/CSP3/\n   */\n  ;\n  (function (Prism) {\n    /**\n     * @param {string} source\n     * @returns {RegExp}\n     */\n    function value(source) {\n      return RegExp(/([ \\t])/.source + '(?:' + source + ')' + /(?=[\\s;]|$)/.source, 'i');\n    }\n    Prism.languages.csp = {\n      directive: {\n        pattern: /(^|[\\s;])(?:base-uri|block-all-mixed-content|(?:child|connect|default|font|frame|img|manifest|media|object|prefetch|script|style|worker)-src|disown-opener|form-action|frame-(?:ancestors|options)|input-protection(?:-(?:clip|selectors))?|navigate-to|plugin-types|policy-uri|referrer|reflected-xss|report-(?:to|uri)|require-sri-for|sandbox|(?:script|style)-src-(?:attr|elem)|upgrade-insecure-requests)(?=[\\s;]|$)/i,\n        lookbehind: true,\n        alias: 'property'\n      },\n      scheme: {\n        pattern: value(/[a-z][a-z0-9.+-]*:/.source),\n        lookbehind: true\n      },\n      none: {\n        pattern: value(/'none'/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      nonce: {\n        pattern: value(/'nonce-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      hash: {\n        pattern: value(/'sha(?:256|384|512)-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      host: {\n        pattern: value(/[a-z][a-z0-9.+-]*:\\/\\/[^\\s;,']*/.source + '|' + /\\*[^\\s;,']*/.source + '|' + /[a-z0-9-]+(?:\\.[a-z0-9-]+)+(?::[\\d*]+)?(?:\\/[^\\s;,']*)?/.source),\n        lookbehind: true,\n        alias: 'url',\n        inside: {\n          important: /\\*/\n        }\n      },\n      keyword: [{\n        pattern: value(/'unsafe-[a-z-]+'/.source),\n        lookbehind: true,\n        alias: 'unsafe'\n      }, {\n        pattern: value(/'[a-z-]+'/.source),\n        lookbehind: true,\n        alias: 'safe'\n      }],\n      punctuation: /;/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "csp", "displayName", "aliases", "Prism", "value", "source", "RegExp", "languages", "directive", "pattern", "lookbehind", "alias", "scheme", "none", "nonce", "hash", "host", "inside", "important", "keyword", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/csp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = csp\ncsp.displayName = 'csp'\ncsp.aliases = []\nfunction csp(Prism) {\n  /**\n   * Original by <PERSON>.\n   *\n   * Reference: https://scotthelme.co.uk/csp-cheat-sheet/\n   *\n   * Supports the following:\n   *  - https://www.w3.org/TR/CSP1/\n   *  - https://www.w3.org/TR/CSP2/\n   *  - https://www.w3.org/TR/CSP3/\n   */\n  ;(function (Prism) {\n    /**\n     * @param {string} source\n     * @returns {RegExp}\n     */\n    function value(source) {\n      return RegExp(\n        /([ \\t])/.source + '(?:' + source + ')' + /(?=[\\s;]|$)/.source,\n        'i'\n      )\n    }\n    Prism.languages.csp = {\n      directive: {\n        pattern:\n          /(^|[\\s;])(?:base-uri|block-all-mixed-content|(?:child|connect|default|font|frame|img|manifest|media|object|prefetch|script|style|worker)-src|disown-opener|form-action|frame-(?:ancestors|options)|input-protection(?:-(?:clip|selectors))?|navigate-to|plugin-types|policy-uri|referrer|reflected-xss|report-(?:to|uri)|require-sri-for|sandbox|(?:script|style)-src-(?:attr|elem)|upgrade-insecure-requests)(?=[\\s;]|$)/i,\n        lookbehind: true,\n        alias: 'property'\n      },\n      scheme: {\n        pattern: value(/[a-z][a-z0-9.+-]*:/.source),\n        lookbehind: true\n      },\n      none: {\n        pattern: value(/'none'/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      nonce: {\n        pattern: value(/'nonce-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      hash: {\n        pattern: value(/'sha(?:256|384|512)-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      host: {\n        pattern: value(\n          /[a-z][a-z0-9.+-]*:\\/\\/[^\\s;,']*/.source +\n            '|' +\n            /\\*[^\\s;,']*/.source +\n            '|' +\n            /[a-z0-9-]+(?:\\.[a-z0-9-]+)+(?::[\\d*]+)?(?:\\/[^\\s;,']*)?/.source\n        ),\n        lookbehind: true,\n        alias: 'url',\n        inside: {\n          important: /\\*/\n        }\n      },\n      keyword: [\n        {\n          pattern: value(/'unsafe-[a-z-]+'/.source),\n          lookbehind: true,\n          alias: 'unsafe'\n        },\n        {\n          pattern: value(/'[a-z-]+'/.source),\n          lookbehind: true,\n          alias: 'safe'\n        }\n      ],\n      punctuation: /;/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;IACI,SAASC,KAAKA,CAACC,MAAM,EAAE;MACrB,OAAOC,MAAM,CACX,SAAS,CAACD,MAAM,GAAG,KAAK,GAAGA,MAAM,GAAG,GAAG,GAAG,aAAa,CAACA,MAAM,EAC9D,GACF,CAAC;IACH;IACAF,KAAK,CAACI,SAAS,CAACP,GAAG,GAAG;MACpBQ,SAAS,EAAE;QACTC,OAAO,EACL,4ZAA4Z;QAC9ZC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAE;QACNH,OAAO,EAAEL,KAAK,CAAC,oBAAoB,CAACC,MAAM,CAAC;QAC3CK,UAAU,EAAE;MACd,CAAC;MACDG,IAAI,EAAE;QACJJ,OAAO,EAAEL,KAAK,CAAC,QAAQ,CAACC,MAAM,CAAC;QAC/BK,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDG,KAAK,EAAE;QACLL,OAAO,EAAEL,KAAK,CAAC,mBAAmB,CAACC,MAAM,CAAC;QAC1CK,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDI,IAAI,EAAE;QACJN,OAAO,EAAEL,KAAK,CAAC,gCAAgC,CAACC,MAAM,CAAC;QACvDK,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDK,IAAI,EAAE;QACJP,OAAO,EAAEL,KAAK,CACZ,iCAAiC,CAACC,MAAM,GACtC,GAAG,GACH,aAAa,CAACA,MAAM,GACpB,GAAG,GACH,yDAAyD,CAACA,MAC9D,CAAC;QACDK,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,KAAK;QACZM,MAAM,EAAE;UACNC,SAAS,EAAE;QACb;MACF,CAAC;MACDC,OAAO,EAAE,CACP;QACEV,OAAO,EAAEL,KAAK,CAAC,kBAAkB,CAACC,MAAM,CAAC;QACzCK,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEF,OAAO,EAAEL,KAAK,CAAC,WAAW,CAACC,MAAM,CAAC;QAClCK,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC,CACF;MACDS,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEjB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}