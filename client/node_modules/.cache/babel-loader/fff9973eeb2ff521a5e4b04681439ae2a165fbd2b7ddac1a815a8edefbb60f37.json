{"ast": null, "code": "'use strict';\n\nmodule.exports = excelFormula;\nexcelFormula.displayName = 'excelFormula';\nexcelFormula.aliases = [];\nfunction excelFormula(Prism) {\n  Prism.languages['excel-formula'] = {\n    comment: {\n      pattern: /(\\bN\\(\\s*)\"(?:[^\"]|\"\")*\"(?=\\s*\\))/i,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    reference: {\n      // https://www.ablebits.com/office-addins-blog/2015/12/08/excel-reference-another-sheet-workbook/\n      // Sales!B2\n      // 'Winter sales'!B2\n      // [Sales.xlsx]Jan!B2:B5\n      // D:\\Reports\\[Sales.xlsx]Jan!B2:B5\n      // '[Sales.xlsx]Jan sales'!B2:B5\n      // 'D:\\Reports\\[Sales.xlsx]Jan sales'!B2:B5\n      pattern: /(?:'[^']*'|(?:[^\\s()[\\]{}<>*?\"';,$&]*\\[[^^\\s()[\\]{}<>*?\"']+\\])?\\w+)!/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        operator: /!$/,\n        punctuation: /'/,\n        sheet: {\n          pattern: /[^[\\]]+$/,\n          alias: 'function'\n        },\n        file: {\n          pattern: /\\[[^[\\]]+\\]$/,\n          inside: {\n            punctuation: /[[\\]]/\n          }\n        },\n        path: /[\\s\\S]+/\n      }\n    },\n    'function-name': {\n      pattern: /\\b[A-Z]\\w*(?=\\()/i,\n      alias: 'keyword'\n    },\n    range: {\n      pattern: /\\$?\\b(?:[A-Z]+\\$?\\d+:\\$?[A-Z]+\\$?\\d+|[A-Z]+:\\$?[A-Z]+|\\d+:\\$?\\d+)\\b/i,\n      alias: 'property',\n      inside: {\n        operator: /:/,\n        cell: /\\$?[A-Z]+\\$?\\d+/i,\n        column: /\\$?[A-Z]+/i,\n        row: /\\$?\\d+/\n      }\n    },\n    cell: {\n      // Excel is case insensitive, so the string \"foo1\" could be either a variable or a cell.\n      // To combat this, we match cells case insensitive, if the contain at least one \"$\", and case sensitive otherwise.\n      pattern: /\\b[A-Z]+\\d+\\b|\\$[A-Za-z]+\\$?\\d+\\b|\\b[A-Za-z]+\\$\\d+\\b/,\n      alias: 'property'\n    },\n    number: /(?:\\b\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[+-]?\\d+)?\\b/i,\n    boolean: /\\b(?:FALSE|TRUE)\\b/i,\n    operator: /[-+*/^%=&,]|<[=>]?|>=?/,\n    punctuation: /[[\\]();{}|]/\n  };\n  Prism.languages['xlsx'] = Prism.languages['xls'] = Prism.languages['excel-formula'];\n}", "map": {"version": 3, "names": ["module", "exports", "excelFormula", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "string", "reference", "alias", "inside", "operator", "punctuation", "sheet", "file", "path", "range", "cell", "column", "row", "number", "boolean"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/excel-formula.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = excelFormula\nexcelFormula.displayName = 'excelFormula'\nexcelFormula.aliases = []\nfunction excelFormula(Prism) {\n  Prism.languages['excel-formula'] = {\n    comment: {\n      pattern: /(\\bN\\(\\s*)\"(?:[^\"]|\"\")*\"(?=\\s*\\))/i,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    reference: {\n      // https://www.ablebits.com/office-addins-blog/2015/12/08/excel-reference-another-sheet-workbook/\n      // Sales!B2\n      // 'Winter sales'!B2\n      // [Sales.xlsx]Jan!B2:B5\n      // D:\\Reports\\[Sales.xlsx]Jan!B2:B5\n      // '[Sales.xlsx]Jan sales'!B2:B5\n      // 'D:\\Reports\\[Sales.xlsx]Jan sales'!B2:B5\n      pattern:\n        /(?:'[^']*'|(?:[^\\s()[\\]{}<>*?\"';,$&]*\\[[^^\\s()[\\]{}<>*?\"']+\\])?\\w+)!/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        operator: /!$/,\n        punctuation: /'/,\n        sheet: {\n          pattern: /[^[\\]]+$/,\n          alias: 'function'\n        },\n        file: {\n          pattern: /\\[[^[\\]]+\\]$/,\n          inside: {\n            punctuation: /[[\\]]/\n          }\n        },\n        path: /[\\s\\S]+/\n      }\n    },\n    'function-name': {\n      pattern: /\\b[A-Z]\\w*(?=\\()/i,\n      alias: 'keyword'\n    },\n    range: {\n      pattern:\n        /\\$?\\b(?:[A-Z]+\\$?\\d+:\\$?[A-Z]+\\$?\\d+|[A-Z]+:\\$?[A-Z]+|\\d+:\\$?\\d+)\\b/i,\n      alias: 'property',\n      inside: {\n        operator: /:/,\n        cell: /\\$?[A-Z]+\\$?\\d+/i,\n        column: /\\$?[A-Z]+/i,\n        row: /\\$?\\d+/\n      }\n    },\n    cell: {\n      // Excel is case insensitive, so the string \"foo1\" could be either a variable or a cell.\n      // To combat this, we match cells case insensitive, if the contain at least one \"$\", and case sensitive otherwise.\n      pattern: /\\b[A-Z]+\\d+\\b|\\$[A-Za-z]+\\$?\\d+\\b|\\b[A-Za-z]+\\$\\d+\\b/,\n      alias: 'property'\n    },\n    number: /(?:\\b\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[+-]?\\d+)?\\b/i,\n    boolean: /\\b(?:FALSE|TRUE)\\b/i,\n    operator: /[-+*/^%=&,]|<[=>]?|>=?/,\n    punctuation: /[[\\]();{}|]/\n  }\n  Prism.languages['xlsx'] = Prism.languages['xls'] =\n    Prism.languages['excel-formula']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3BA,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,GAAG;IACjCC,OAAO,EAAE;MACPC,OAAO,EAAE,oCAAoC;MAC7CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,qBAAqB;MAC9BE,MAAM,EAAE;IACV,CAAC;IACDE,SAAS,EAAE;MACT;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ,OAAO,EACL,sEAAsE;MACxEE,MAAM,EAAE,IAAI;MACZG,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE;QACNC,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,GAAG;QAChBC,KAAK,EAAE;UACLT,OAAO,EAAE,UAAU;UACnBK,KAAK,EAAE;QACT,CAAC;QACDK,IAAI,EAAE;UACJV,OAAO,EAAE,cAAc;UACvBM,MAAM,EAAE;YACNE,WAAW,EAAE;UACf;QACF,CAAC;QACDG,IAAI,EAAE;MACR;IACF,CAAC;IACD,eAAe,EAAE;MACfX,OAAO,EAAE,mBAAmB;MAC5BK,KAAK,EAAE;IACT,CAAC;IACDO,KAAK,EAAE;MACLZ,OAAO,EACL,sEAAsE;MACxEK,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QACNC,QAAQ,EAAE,GAAG;QACbM,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,YAAY;QACpBC,GAAG,EAAE;MACP;IACF,CAAC;IACDF,IAAI,EAAE;MACJ;MACA;MACAb,OAAO,EAAE,sDAAsD;MAC/DK,KAAK,EAAE;IACT,CAAC;IACDW,MAAM,EAAE,8CAA8C;IACtDC,OAAO,EAAE,qBAAqB;IAC9BV,QAAQ,EAAE,wBAAwB;IAClCC,WAAW,EAAE;EACf,CAAC;EACDX,KAAK,CAACC,SAAS,CAAC,MAAM,CAAC,GAAGD,KAAK,CAACC,SAAS,CAAC,KAAK,CAAC,GAC9CD,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}