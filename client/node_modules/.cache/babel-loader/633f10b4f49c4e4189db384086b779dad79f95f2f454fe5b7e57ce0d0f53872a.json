{"ast": null, "code": "/*\nLanguage: Puppet\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://puppet.com/docs\nCategory: config\n*/\n\nfunction puppet(hljs) {\n  const PUPPET_KEYWORDS = {\n    keyword: /* language keywords */\n    'and case default else elsif false if in import enherits node or true undef unless main settings $string ',\n    literal: /* metaparameters */\n    'alias audit before loglevel noop require subscribe tag ' + /* normal attributes */\n    'owner ensure group mode name|0 changes context force incl lens load_path onlyif provider returns root show_diff type_check ' + 'en_address ip_address realname command environment hour monute month monthday special target weekday ' + 'creates cwd ogoutput refresh refreshonly tries try_sleep umask backup checksum content ctime force ignore ' + 'links mtime purge recurse recurselimit replace selinux_ignore_defaults selrange selrole seltype seluser source ' + 'souirce_permissions sourceselect validate_cmd validate_replacement allowdupe attribute_membership auth_membership forcelocal gid ' + 'ia_load_module members system host_aliases ip allowed_trunk_vlans description device_url duplex encapsulation etherchannel ' + 'native_vlan speed principals allow_root auth_class auth_type authenticate_user k_of_n mechanisms rule session_owner shared options ' + 'device fstype enable hasrestart directory present absent link atboot blockdevice device dump pass remounts poller_tag use ' + 'message withpath adminfile allow_virtual allowcdrom category configfiles flavor install_options instance package_settings platform ' + 'responsefile status uninstall_options vendor unless_system_user unless_uid binary control flags hasstatus manifest pattern restart running ' + 'start stop allowdupe auths expiry gid groups home iterations key_membership keys managehome membership password password_max_age ' + 'password_min_age profile_membership profiles project purge_ssh_keys role_membership roles salt shell uid baseurl cost descr enabled ' + 'enablegroups exclude failovermethod gpgcheck gpgkey http_caching include includepkgs keepalive metadata_expire metalink mirrorlist ' + 'priority protect proxy proxy_password proxy_username repo_gpgcheck s3_enabled skip_if_unavailable sslcacert sslclientcert sslclientkey ' + 'sslverify mounted',\n    built_in: /* core facts */\n    'architecture augeasversion blockdevices boardmanufacturer boardproductname boardserialnumber cfkey dhcp_servers ' + 'domain ec2_ ec2_userdata facterversion filesystems ldom fqdn gid hardwareisa hardwaremodel hostname id|0 interfaces ' + 'ipaddress ipaddress_ ipaddress6 ipaddress6_ iphostnumber is_virtual kernel kernelmajversion kernelrelease kernelversion ' + 'kernelrelease kernelversion lsbdistcodename lsbdistdescription lsbdistid lsbdistrelease lsbmajdistrelease lsbminordistrelease ' + 'lsbrelease macaddress macaddress_ macosx_buildversion macosx_productname macosx_productversion macosx_productverson_major ' + 'macosx_productversion_minor manufacturer memoryfree memorysize netmask metmask_ network_ operatingsystem operatingsystemmajrelease ' + 'operatingsystemrelease osfamily partitions path physicalprocessorcount processor processorcount productname ps puppetversion ' + 'rubysitedir rubyversion selinux selinux_config_mode selinux_config_policy selinux_current_mode selinux_current_mode selinux_enforced ' + 'selinux_policyversion serialnumber sp_ sshdsakey sshecdsakey sshrsakey swapencrypted swapfree swapsize timezone type uniqueid uptime ' + 'uptime_days uptime_hours uptime_seconds uuid virtual vlans xendomains zfs_version zonenae zones zpool_version'\n  };\n  const COMMENT = hljs.COMMENT('#', '$');\n  const IDENT_RE = '([A-Za-z_]|::)(\\\\w|::)*';\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: IDENT_RE\n  });\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + IDENT_RE\n  };\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, VARIABLE],\n    variants: [{\n      begin: /'/,\n      end: /'/\n    }, {\n      begin: /\"/,\n      end: /\"/\n    }]\n  };\n  return {\n    name: 'Puppet',\n    aliases: ['pp'],\n    contains: [COMMENT, VARIABLE, STRING, {\n      beginKeywords: 'class',\n      end: '\\\\{|;',\n      illegal: /=/,\n      contains: [TITLE, COMMENT]\n    }, {\n      beginKeywords: 'define',\n      end: /\\{/,\n      contains: [{\n        className: 'section',\n        begin: hljs.IDENT_RE,\n        endsParent: true\n      }]\n    }, {\n      begin: hljs.IDENT_RE + '\\\\s+\\\\{',\n      returnBegin: true,\n      end: /\\S/,\n      contains: [{\n        className: 'keyword',\n        begin: hljs.IDENT_RE\n      }, {\n        begin: /\\{/,\n        end: /\\}/,\n        keywords: PUPPET_KEYWORDS,\n        relevance: 0,\n        contains: [STRING, COMMENT, {\n          begin: '[a-zA-Z_]+\\\\s*=>',\n          returnBegin: true,\n          end: '=>',\n          contains: [{\n            className: 'attr',\n            begin: hljs.IDENT_RE\n          }]\n        }, {\n          className: 'number',\n          begin: '(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b',\n          relevance: 0\n        }, VARIABLE]\n      }],\n      relevance: 0\n    }]\n  };\n}\nmodule.exports = puppet;", "map": {"version": 3, "names": ["puppet", "hljs", "PUPPET_KEYWORDS", "keyword", "literal", "built_in", "COMMENT", "IDENT_RE", "TITLE", "inherit", "TITLE_MODE", "begin", "VARIABLE", "className", "STRING", "contains", "BACKSLASH_ESCAPE", "variants", "end", "name", "aliases", "beginKeywords", "illegal", "endsParent", "returnBegin", "keywords", "relevance", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/puppet.js"], "sourcesContent": ["/*\nLanguage: Puppet\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://puppet.com/docs\nCategory: config\n*/\n\nfunction puppet(hljs) {\n  const PUPPET_KEYWORDS = {\n    keyword:\n    /* language keywords */\n      'and case default else elsif false if in import enherits node or true undef unless main settings $string ',\n    literal:\n    /* metaparameters */\n      'alias audit before loglevel noop require subscribe tag ' +\n      /* normal attributes */\n      'owner ensure group mode name|0 changes context force incl lens load_path onlyif provider returns root show_diff type_check ' +\n      'en_address ip_address realname command environment hour monute month monthday special target weekday ' +\n      'creates cwd ogoutput refresh refreshonly tries try_sleep umask backup checksum content ctime force ignore ' +\n      'links mtime purge recurse recurselimit replace selinux_ignore_defaults selrange selrole seltype seluser source ' +\n      'souirce_permissions sourceselect validate_cmd validate_replacement allowdupe attribute_membership auth_membership forcelocal gid ' +\n      'ia_load_module members system host_aliases ip allowed_trunk_vlans description device_url duplex encapsulation etherchannel ' +\n      'native_vlan speed principals allow_root auth_class auth_type authenticate_user k_of_n mechanisms rule session_owner shared options ' +\n      'device fstype enable hasrestart directory present absent link atboot blockdevice device dump pass remounts poller_tag use ' +\n      'message withpath adminfile allow_virtual allowcdrom category configfiles flavor install_options instance package_settings platform ' +\n      'responsefile status uninstall_options vendor unless_system_user unless_uid binary control flags hasstatus manifest pattern restart running ' +\n      'start stop allowdupe auths expiry gid groups home iterations key_membership keys managehome membership password password_max_age ' +\n      'password_min_age profile_membership profiles project purge_ssh_keys role_membership roles salt shell uid baseurl cost descr enabled ' +\n      'enablegroups exclude failovermethod gpgcheck gpgkey http_caching include includepkgs keepalive metadata_expire metalink mirrorlist ' +\n      'priority protect proxy proxy_password proxy_username repo_gpgcheck s3_enabled skip_if_unavailable sslcacert sslclientcert sslclientkey ' +\n      'sslverify mounted',\n    built_in:\n    /* core facts */\n      'architecture augeasversion blockdevices boardmanufacturer boardproductname boardserialnumber cfkey dhcp_servers ' +\n      'domain ec2_ ec2_userdata facterversion filesystems ldom fqdn gid hardwareisa hardwaremodel hostname id|0 interfaces ' +\n      'ipaddress ipaddress_ ipaddress6 ipaddress6_ iphostnumber is_virtual kernel kernelmajversion kernelrelease kernelversion ' +\n      'kernelrelease kernelversion lsbdistcodename lsbdistdescription lsbdistid lsbdistrelease lsbmajdistrelease lsbminordistrelease ' +\n      'lsbrelease macaddress macaddress_ macosx_buildversion macosx_productname macosx_productversion macosx_productverson_major ' +\n      'macosx_productversion_minor manufacturer memoryfree memorysize netmask metmask_ network_ operatingsystem operatingsystemmajrelease ' +\n      'operatingsystemrelease osfamily partitions path physicalprocessorcount processor processorcount productname ps puppetversion ' +\n      'rubysitedir rubyversion selinux selinux_config_mode selinux_config_policy selinux_current_mode selinux_current_mode selinux_enforced ' +\n      'selinux_policyversion serialnumber sp_ sshdsakey sshecdsakey sshrsakey swapencrypted swapfree swapsize timezone type uniqueid uptime ' +\n      'uptime_days uptime_hours uptime_seconds uuid virtual vlans xendomains zfs_version zonenae zones zpool_version'\n  };\n\n  const COMMENT = hljs.COMMENT('#', '$');\n\n  const IDENT_RE = '([A-Za-z_]|::)(\\\\w|::)*';\n\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: IDENT_RE\n  });\n\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + IDENT_RE\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VARIABLE\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      }\n    ]\n  };\n\n  return {\n    name: 'Puppet',\n    aliases: [ 'pp' ],\n    contains: [\n      COMMENT,\n      VARIABLE,\n      STRING,\n      {\n        beginKeywords: 'class',\n        end: '\\\\{|;',\n        illegal: /=/,\n        contains: [\n          TITLE,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'define',\n        end: /\\{/,\n        contains: [\n          {\n            className: 'section',\n            begin: hljs.IDENT_RE,\n            endsParent: true\n          }\n        ]\n      },\n      {\n        begin: hljs.IDENT_RE + '\\\\s+\\\\{',\n        returnBegin: true,\n        end: /\\S/,\n        contains: [\n          {\n            className: 'keyword',\n            begin: hljs.IDENT_RE\n          },\n          {\n            begin: /\\{/,\n            end: /\\}/,\n            keywords: PUPPET_KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              COMMENT,\n              {\n                begin: '[a-zA-Z_]+\\\\s*=>',\n                returnBegin: true,\n                end: '=>',\n                contains: [\n                  {\n                    className: 'attr',\n                    begin: hljs.IDENT_RE\n                  }\n                ]\n              },\n              {\n                className: 'number',\n                begin: '(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b',\n                relevance: 0\n              },\n              VARIABLE\n            ]\n          }\n        ],\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = puppet;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,eAAe,GAAG;IACtBC,OAAO,EACP;IACE,0GAA0G;IAC5GC,OAAO,EACP;IACE,yDAAyD,GACzD;IACA,6HAA6H,GAC7H,uGAAuG,GACvG,4GAA4G,GAC5G,iHAAiH,GACjH,mIAAmI,GACnI,6HAA6H,GAC7H,qIAAqI,GACrI,4HAA4H,GAC5H,qIAAqI,GACrI,6IAA6I,GAC7I,mIAAmI,GACnI,sIAAsI,GACtI,qIAAqI,GACrI,yIAAyI,GACzI,mBAAmB;IACrBC,QAAQ,EACR;IACE,kHAAkH,GAClH,sHAAsH,GACtH,0HAA0H,GAC1H,gIAAgI,GAChI,4HAA4H,GAC5H,qIAAqI,GACrI,+HAA+H,GAC/H,uIAAuI,GACvI,uIAAuI,GACvI;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGL,IAAI,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAEtC,MAAMC,QAAQ,GAAG,yBAAyB;EAE1C,MAAMC,KAAK,GAAGP,IAAI,CAACQ,OAAO,CAACR,IAAI,CAACS,UAAU,EAAE;IAC1CC,KAAK,EAAEJ;EACT,CAAC,CAAC;EAEF,MAAMK,QAAQ,GAAG;IACfC,SAAS,EAAE,UAAU;IACrBF,KAAK,EAAE,KAAK,GAAGJ;EACjB,CAAC;EAED,MAAMO,MAAM,GAAG;IACbD,SAAS,EAAE,QAAQ;IACnBE,QAAQ,EAAE,CACRd,IAAI,CAACe,gBAAgB,EACrBJ,QAAQ,CACT;IACDK,QAAQ,EAAE,CACR;MACEN,KAAK,EAAE,GAAG;MACVO,GAAG,EAAE;IACP,CAAC,EACD;MACEP,KAAK,EAAE,GAAG;MACVO,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CAAE,IAAI,CAAE;IACjBL,QAAQ,EAAE,CACRT,OAAO,EACPM,QAAQ,EACRE,MAAM,EACN;MACEO,aAAa,EAAE,OAAO;MACtBH,GAAG,EAAE,OAAO;MACZI,OAAO,EAAE,GAAG;MACZP,QAAQ,EAAE,CACRP,KAAK,EACLF,OAAO;IAEX,CAAC,EACD;MACEe,aAAa,EAAE,QAAQ;MACvBH,GAAG,EAAE,IAAI;MACTH,QAAQ,EAAE,CACR;QACEF,SAAS,EAAE,SAAS;QACpBF,KAAK,EAAEV,IAAI,CAACM,QAAQ;QACpBgB,UAAU,EAAE;MACd,CAAC;IAEL,CAAC,EACD;MACEZ,KAAK,EAAEV,IAAI,CAACM,QAAQ,GAAG,SAAS;MAChCiB,WAAW,EAAE,IAAI;MACjBN,GAAG,EAAE,IAAI;MACTH,QAAQ,EAAE,CACR;QACEF,SAAS,EAAE,SAAS;QACpBF,KAAK,EAAEV,IAAI,CAACM;MACd,CAAC,EACD;QACEI,KAAK,EAAE,IAAI;QACXO,GAAG,EAAE,IAAI;QACTO,QAAQ,EAAEvB,eAAe;QACzBwB,SAAS,EAAE,CAAC;QACZX,QAAQ,EAAE,CACRD,MAAM,EACNR,OAAO,EACP;UACEK,KAAK,EAAE,kBAAkB;UACzBa,WAAW,EAAE,IAAI;UACjBN,GAAG,EAAE,IAAI;UACTH,QAAQ,EAAE,CACR;YACEF,SAAS,EAAE,MAAM;YACjBF,KAAK,EAAEV,IAAI,CAACM;UACd,CAAC;QAEL,CAAC,EACD;UACEM,SAAS,EAAE,QAAQ;UACnBF,KAAK,EAAE,2EAA2E;UAClFe,SAAS,EAAE;QACb,CAAC,EACDd,QAAQ;MAEZ,CAAC,CACF;MACDc,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAG5B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}