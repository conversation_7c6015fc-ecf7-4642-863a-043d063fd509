{"ast": null, "code": "module.exports = extend;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction extend() {\n  var target = {};\n  for (var i = 0; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n}", "map": {"version": 3, "names": ["module", "exports", "extend", "hasOwnProperty", "Object", "prototype", "target", "i", "arguments", "length", "source", "key", "call"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/xtend/immutable.js"], "sourcesContent": ["module.exports = extend\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction extend() {\n    var target = {}\n\n    for (var i = 0; i < arguments.length; i++) {\n        var source = arguments[i]\n\n        for (var key in source) {\n            if (hasOwnProperty.call(source, key)) {\n                target[key] = source[key]\n            }\n        }\n    }\n\n    return target\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,MAAM;AAEvB,IAAIC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AAEpD,SAASD,MAAMA,CAAA,EAAG;EACd,IAAII,MAAM,GAAG,CAAC,CAAC;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACvC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAEzB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MACpB,IAAIP,cAAc,CAACS,IAAI,CAACF,MAAM,EAAEC,GAAG,CAAC,EAAE;QAClCL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAC7B;IACJ;EACJ;EAEA,OAAOL,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}