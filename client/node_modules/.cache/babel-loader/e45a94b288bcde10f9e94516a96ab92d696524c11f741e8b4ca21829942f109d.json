{"ast": null, "code": "/*\nLanguage: Mercury\nAuthor: m<PERSON><PERSON> <<EMAIL>>\nDescription: Mercury is a logic/functional programming language which combines the clarity and expressiveness of declarative programming with advanced static analysis and error detection features.\nWebsite: https://www.mercurylang.org\n*/\n\nfunction mercury(hljs) {\n  const KEYWORDS = {\n    keyword: 'module use_module import_module include_module end_module initialise ' + 'mutable initialize finalize finalise interface implementation pred ' + 'mode func type inst solver any_pred any_func is semidet det nondet ' + 'multi erroneous failure cc_nondet cc_multi typeclass instance where ' + 'pragma promise external trace atomic or_else require_complete_switch ' + 'require_det require_semidet require_multi require_nondet ' + 'require_cc_multi require_cc_nondet require_erroneous require_failure',\n    meta:\n    // pragma\n    'inline no_inline type_spec source_file fact_table obsolete memo ' + 'loop_check minimal_model terminates does_not_terminate ' + 'check_termination promise_equivalent_clauses ' +\n    // preprocessor\n    'foreign_proc foreign_decl foreign_code foreign_type ' + 'foreign_import_module foreign_export_enum foreign_export ' + 'foreign_enum may_call_mercury will_not_call_mercury thread_safe ' + 'not_thread_safe maybe_thread_safe promise_pure promise_semipure ' + 'tabled_for_io local untrailed trailed attach_to_io_state ' + 'can_pass_as_mercury_type stable will_not_throw_exception ' + 'may_modify_trail will_not_modify_trail may_duplicate ' + 'may_not_duplicate affects_liveness does_not_affect_liveness ' + 'doesnt_affect_liveness no_sharing unknown_sharing sharing',\n    built_in: 'some all not if then else true fail false try catch catch_any ' + 'semidet_true semidet_false semidet_fail impure_true impure semipure'\n  };\n  const COMMENT = hljs.COMMENT('%', '$');\n  const NUMCODE = {\n    className: 'number',\n    begin: \"0'.\\\\|0[box][0-9a-fA-F]*\"\n  };\n  const ATOM = hljs.inherit(hljs.APOS_STRING_MODE, {\n    relevance: 0\n  });\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    relevance: 0\n  });\n  const STRING_FMT = {\n    className: 'subst',\n    begin: '\\\\\\\\[abfnrtv]\\\\|\\\\\\\\x[0-9a-fA-F]*\\\\\\\\\\\\|%[-+# *.0-9]*[dioxXucsfeEgGp]',\n    relevance: 0\n  };\n  STRING.contains = STRING.contains.slice(); // we need our own copy of contains\n  STRING.contains.push(STRING_FMT);\n  const IMPLICATION = {\n    className: 'built_in',\n    variants: [{\n      begin: '<=>'\n    }, {\n      begin: '<=',\n      relevance: 0\n    }, {\n      begin: '=>',\n      relevance: 0\n    }, {\n      begin: '/\\\\\\\\'\n    }, {\n      begin: '\\\\\\\\/'\n    }]\n  };\n  const HEAD_BODY_CONJUNCTION = {\n    className: 'built_in',\n    variants: [{\n      begin: ':-\\\\|-->'\n    }, {\n      begin: '=',\n      relevance: 0\n    }]\n  };\n  return {\n    name: 'Mercury',\n    aliases: ['m', 'moo'],\n    keywords: KEYWORDS,\n    contains: [IMPLICATION, HEAD_BODY_CONJUNCTION, COMMENT, hljs.C_BLOCK_COMMENT_MODE, NUMCODE, hljs.NUMBER_MODE, ATOM, STRING, {\n      // relevance booster\n      begin: /:-/\n    }, {\n      // relevance booster\n      begin: /\\.$/\n    }]\n  };\n}\nmodule.exports = mercury;", "map": {"version": 3, "names": ["mercury", "hljs", "KEYWORDS", "keyword", "meta", "built_in", "COMMENT", "NUMCODE", "className", "begin", "ATOM", "inherit", "APOS_STRING_MODE", "relevance", "STRING", "QUOTE_STRING_MODE", "STRING_FMT", "contains", "slice", "push", "IMPLICATION", "variants", "HEAD_BODY_CONJUNCTION", "name", "aliases", "keywords", "C_BLOCK_COMMENT_MODE", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/mercury.js"], "sourcesContent": ["/*\nLanguage: Mercury\nAuthor: m<PERSON><PERSON> <<EMAIL>>\nDescription: Mercury is a logic/functional programming language which combines the clarity and expressiveness of declarative programming with advanced static analysis and error detection features.\nWebsite: https://www.mercurylang.org\n*/\n\nfunction mercury(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'module use_module import_module include_module end_module initialise ' +\n      'mutable initialize finalize finalise interface implementation pred ' +\n      'mode func type inst solver any_pred any_func is semidet det nondet ' +\n      'multi erroneous failure cc_nondet cc_multi typeclass instance where ' +\n      'pragma promise external trace atomic or_else require_complete_switch ' +\n      'require_det require_semidet require_multi require_nondet ' +\n      'require_cc_multi require_cc_nondet require_erroneous require_failure',\n    meta:\n      // pragma\n      'inline no_inline type_spec source_file fact_table obsolete memo ' +\n      'loop_check minimal_model terminates does_not_terminate ' +\n      'check_termination promise_equivalent_clauses ' +\n      // preprocessor\n      'foreign_proc foreign_decl foreign_code foreign_type ' +\n      'foreign_import_module foreign_export_enum foreign_export ' +\n      'foreign_enum may_call_mercury will_not_call_mercury thread_safe ' +\n      'not_thread_safe maybe_thread_safe promise_pure promise_semipure ' +\n      'tabled_for_io local untrailed trailed attach_to_io_state ' +\n      'can_pass_as_mercury_type stable will_not_throw_exception ' +\n      'may_modify_trail will_not_modify_trail may_duplicate ' +\n      'may_not_duplicate affects_liveness does_not_affect_liveness ' +\n      'doesnt_affect_liveness no_sharing unknown_sharing sharing',\n    built_in:\n      'some all not if then else true fail false try catch catch_any ' +\n      'semidet_true semidet_false semidet_fail impure_true impure semipure'\n  };\n\n  const COMMENT = hljs.COMMENT('%', '$');\n\n  const NUMCODE = {\n    className: 'number',\n    begin: \"0'.\\\\|0[box][0-9a-fA-F]*\"\n  };\n\n  const ATOM = hljs.inherit(hljs.APOS_STRING_MODE, {\n    relevance: 0\n  });\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    relevance: 0\n  });\n  const STRING_FMT = {\n    className: 'subst',\n    begin: '\\\\\\\\[abfnrtv]\\\\|\\\\\\\\x[0-9a-fA-F]*\\\\\\\\\\\\|%[-+# *.0-9]*[dioxXucsfeEgGp]',\n    relevance: 0\n  };\n  STRING.contains = STRING.contains.slice(); // we need our own copy of contains\n  STRING.contains.push(STRING_FMT);\n\n  const IMPLICATION = {\n    className: 'built_in',\n    variants: [\n      {\n        begin: '<=>'\n      },\n      {\n        begin: '<=',\n        relevance: 0\n      },\n      {\n        begin: '=>',\n        relevance: 0\n      },\n      {\n        begin: '/\\\\\\\\'\n      },\n      {\n        begin: '\\\\\\\\/'\n      }\n    ]\n  };\n\n  const HEAD_BODY_CONJUNCTION = {\n    className: 'built_in',\n    variants: [\n      {\n        begin: ':-\\\\|-->'\n      },\n      {\n        begin: '=',\n        relevance: 0\n      }\n    ]\n  };\n\n  return {\n    name: 'Mercury',\n    aliases: [\n      'm',\n      'moo'\n    ],\n    keywords: KEYWORDS,\n    contains: [\n      IMPLICATION,\n      HEAD_BODY_CONJUNCTION,\n      COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      NUMCODE,\n      hljs.NUMBER_MODE,\n      ATOM,\n      STRING,\n      { // relevance booster\n        begin: /:-/\n      },\n      { // relevance booster\n        begin: /\\.$/\n      }\n    ]\n  };\n}\n\nmodule.exports = mercury;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,QAAQ,GAAG;IACfC,OAAO,EACL,uEAAuE,GACvE,qEAAqE,GACrE,qEAAqE,GACrE,sEAAsE,GACtE,uEAAuE,GACvE,2DAA2D,GAC3D,sEAAsE;IACxEC,IAAI;IACF;IACA,kEAAkE,GAClE,yDAAyD,GACzD,+CAA+C;IAC/C;IACA,sDAAsD,GACtD,2DAA2D,GAC3D,kEAAkE,GAClE,kEAAkE,GAClE,2DAA2D,GAC3D,2DAA2D,GAC3D,uDAAuD,GACvD,8DAA8D,GAC9D,2DAA2D;IAC7DC,QAAQ,EACN,gEAAgE,GAChE;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGL,IAAI,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAEtC,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,IAAI,GAAGT,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,gBAAgB,EAAE;IAC/CC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,MAAM,GAAGb,IAAI,CAACU,OAAO,CAACV,IAAI,CAACc,iBAAiB,EAAE;IAClDF,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMG,UAAU,GAAG;IACjBR,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,uEAAuE;IAC9EI,SAAS,EAAE;EACb,CAAC;EACDC,MAAM,CAACG,QAAQ,GAAGH,MAAM,CAACG,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3CJ,MAAM,CAACG,QAAQ,CAACE,IAAI,CAACH,UAAU,CAAC;EAEhC,MAAMI,WAAW,GAAG;IAClBZ,SAAS,EAAE,UAAU;IACrBa,QAAQ,EAAE,CACR;MACEZ,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,IAAI;MACXI,SAAS,EAAE;IACb,CAAC,EACD;MACEJ,KAAK,EAAE,IAAI;MACXI,SAAS,EAAE;IACb,CAAC,EACD;MACEJ,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EAED,MAAMa,qBAAqB,GAAG;IAC5Bd,SAAS,EAAE,UAAU;IACrBa,QAAQ,EAAE,CACR;MACEZ,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,GAAG;MACVI,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EAED,OAAO;IACLU,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,CACP,GAAG,EACH,KAAK,CACN;IACDC,QAAQ,EAAEvB,QAAQ;IAClBe,QAAQ,EAAE,CACRG,WAAW,EACXE,qBAAqB,EACrBhB,OAAO,EACPL,IAAI,CAACyB,oBAAoB,EACzBnB,OAAO,EACPN,IAAI,CAAC0B,WAAW,EAChBjB,IAAI,EACJI,MAAM,EACN;MAAE;MACAL,KAAK,EAAE;IACT,CAAC,EACD;MAAE;MACAA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAmB,MAAM,CAACC,OAAO,GAAG7B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}