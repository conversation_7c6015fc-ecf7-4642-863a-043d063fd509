{"ast": null, "code": "/*\nLanguage: PHP Template\nRequires: xml.js, php.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\nfunction phpTemplate(hljs) {\n  return {\n    name: \"PHP template\",\n    subLanguage: 'xml',\n    contains: [{\n      begin: /<\\?(php|=)?/,\n      end: /\\?>/,\n      subLanguage: 'php',\n      contains: [\n      // We don't want the php closing tag ?> to close the PHP block when\n      // inside any of the following blocks:\n      {\n        begin: '/\\\\*',\n        end: '\\\\*/',\n        skip: true\n      }, {\n        begin: 'b\"',\n        end: '\"',\n        skip: true\n      }, {\n        begin: 'b\\'',\n        end: '\\'',\n        skip: true\n      }, hljs.inherit(hljs.APOS_STRING_MODE, {\n        illegal: null,\n        className: null,\n        contains: null,\n        skip: true\n      }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null,\n        className: null,\n        contains: null,\n        skip: true\n      })]\n    }]\n  };\n}\nmodule.exports = phpTemplate;", "map": {"version": 3, "names": ["phpTemplate", "hljs", "name", "subLanguage", "contains", "begin", "end", "skip", "inherit", "APOS_STRING_MODE", "illegal", "className", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/php-template.js"], "sourcesContent": ["/*\nLanguage: PHP Template\nRequires: xml.js, php.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\nfunction phpTemplate(hljs) {\n  return {\n    name: \"PHP template\",\n    subLanguage: 'xml',\n    contains: [\n      {\n        begin: /<\\?(php|=)?/,\n        end: /\\?>/,\n        subLanguage: 'php',\n        contains: [\n          // We don't want the php closing tag ?> to close the PHP block when\n          // inside any of the following blocks:\n          {\n            begin: '/\\\\*',\n            end: '\\\\*/',\n            skip: true\n          },\n          {\n            begin: 'b\"',\n            end: '\"',\n            skip: true\n          },\n          {\n            begin: 'b\\'',\n            end: '\\'',\n            skip: true\n          },\n          hljs.inherit(hljs.APOS_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          }),\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          })\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = phpTemplate;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAWA,CAACC,IAAI,EAAE;EACzB,OAAO;IACLC,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,aAAa;MACpBC,GAAG,EAAE,KAAK;MACVH,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE;MACR;MACA;MACA;QACEC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,KAAK,EAAE,KAAK;QACZC,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE;MACR,CAAC,EACDN,IAAI,CAACO,OAAO,CAACP,IAAI,CAACQ,gBAAgB,EAAE;QAClCC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,IAAI;QACfP,QAAQ,EAAE,IAAI;QACdG,IAAI,EAAE;MACR,CAAC,CAAC,EACFN,IAAI,CAACO,OAAO,CAACP,IAAI,CAACW,iBAAiB,EAAE;QACnCF,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,IAAI;QACfP,QAAQ,EAAE,IAAI;QACdG,IAAI,EAAE;MACR,CAAC,CAAC;IAEN,CAAC;EAEL,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGd,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}