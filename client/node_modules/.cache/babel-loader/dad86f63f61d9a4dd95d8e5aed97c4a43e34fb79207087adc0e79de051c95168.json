{"ast": null, "code": "'use strict';\n\nmodule.exports = goModule;\ngoModule.displayName = 'goModule';\ngoModule.aliases = [];\nfunction goModule(Prism) {\n  // https://go.dev/ref/mod#go-mod-file-module\n  Prism.languages['go-mod'] = Prism.languages['go-module'] = {\n    comment: {\n      pattern: /\\/\\/.*/,\n      greedy: true\n    },\n    version: {\n      pattern: /(^|[\\s()[\\],])v\\d+\\.\\d+\\.\\d+(?:[+-][-+.\\w]*)?(?![^\\s()[\\],])/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    'go-version': {\n      pattern: /((?:^|\\s)go\\s+)\\d+(?:\\.\\d+){1,2}/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    keyword: {\n      pattern: /^([ \\t]*)(?:exclude|go|module|replace|require|retract)\\b/m,\n      lookbehind: true\n    },\n    operator: /=>/,\n    punctuation: /[()[\\],]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "goModule", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "version", "lookbehind", "alias", "keyword", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/go-module.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = goModule\ngoModule.displayName = 'goModule'\ngoModule.aliases = []\nfunction goModule(Prism) {\n  // https://go.dev/ref/mod#go-mod-file-module\n  Prism.languages['go-mod'] = Prism.languages['go-module'] = {\n    comment: {\n      pattern: /\\/\\/.*/,\n      greedy: true\n    },\n    version: {\n      pattern: /(^|[\\s()[\\],])v\\d+\\.\\d+\\.\\d+(?:[+-][-+.\\w]*)?(?![^\\s()[\\],])/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    'go-version': {\n      pattern: /((?:^|\\s)go\\s+)\\d+(?:\\.\\d+){1,2}/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    keyword: {\n      pattern: /^([ \\t]*)(?:exclude|go|module|replace|require|retract)\\b/m,\n      lookbehind: true\n    },\n    operator: /=>/,\n    punctuation: /[()[\\],]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EACAA,KAAK,CAACC,SAAS,CAAC,QAAQ,CAAC,GAAGD,KAAK,CAACC,SAAS,CAAC,WAAW,CAAC,GAAG;IACzDC,OAAO,EAAE;MACPC,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPF,OAAO,EAAE,8DAA8D;MACvEG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE;MACZJ,OAAO,EAAE,kCAAkC;MAC3CG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPL,OAAO,EAAE,2DAA2D;MACpEG,UAAU,EAAE;IACd,CAAC;IACDG,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}