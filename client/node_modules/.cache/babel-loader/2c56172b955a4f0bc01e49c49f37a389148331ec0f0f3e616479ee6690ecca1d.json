{"ast": null, "code": "/*\nLanguage: RenderMan RSL\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://renderman.pixar.com/resources/RenderMan_20/shadingLanguage.html\nCategory: graphics\n*/\n\nfunction rsl(hljs) {\n  return {\n    name: 'RenderMan RSL',\n    keywords: {\n      keyword: 'float color point normal vector matrix while for if do return else break extern continue',\n      built_in: 'abs acos ambient area asin atan atmosphere attribute calculatenormal ceil cellnoise ' + 'clamp comp concat cos degrees depth Deriv diffuse distance Du Dv environment exp ' + 'faceforward filterstep floor format fresnel incident length lightsource log match ' + 'max min mod noise normalize ntransform opposite option phong pnoise pow printf ' + 'ptlined radians random reflect refract renderinfo round setcomp setxcomp setycomp ' + 'setzcomp shadow sign sin smoothstep specular specularbrdf spline sqrt step tan ' + 'texture textureinfo trace transform vtransform xcomp ycomp zcomp'\n    },\n    illegal: '</',\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, hljs.C_NUMBER_MODE, {\n      className: 'meta',\n      begin: '#',\n      end: '$'\n    }, {\n      className: 'class',\n      beginKeywords: 'surface displacement light volume imager',\n      end: '\\\\('\n    }, {\n      beginKeywords: 'illuminate illuminance gather',\n      end: '\\\\('\n    }]\n  };\n}\nmodule.exports = rsl;", "map": {"version": 3, "names": ["rsl", "hljs", "name", "keywords", "keyword", "built_in", "illegal", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "C_NUMBER_MODE", "className", "begin", "end", "beginKeywords", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/rsl.js"], "sourcesContent": ["/*\nLanguage: RenderMan RSL\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://renderman.pixar.com/resources/RenderMan_20/shadingLanguage.html\nCategory: graphics\n*/\n\nfunction rsl(hljs) {\n  return {\n    name: 'RenderMan RSL',\n    keywords: {\n      keyword:\n        'float color point normal vector matrix while for if do return else break extern continue',\n      built_in:\n        'abs acos ambient area asin atan atmosphere attribute calculatenormal ceil cellnoise ' +\n        'clamp comp concat cos degrees depth Deriv diffuse distance Du Dv environment exp ' +\n        'faceforward filterstep floor format fresnel incident length lightsource log match ' +\n        'max min mod noise normalize ntransform opposite option phong pnoise pow printf ' +\n        'ptlined radians random reflect refract renderinfo round setcomp setxcomp setycomp ' +\n        'setzcomp shadow sign sin smoothstep specular specularbrdf spline sqrt step tan ' +\n        'texture textureinfo trace transform vtransform xcomp ycomp zcomp'\n    },\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$'\n      },\n      {\n        className: 'class',\n        beginKeywords: 'surface displacement light volume imager',\n        end: '\\\\('\n      },\n      {\n        beginKeywords: 'illuminate illuminance gather',\n        end: '\\\\('\n      }\n    ]\n  };\n}\n\nmodule.exports = rsl;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,eAAe;IACrBC,QAAQ,EAAE;MACRC,OAAO,EACL,0FAA0F;MAC5FC,QAAQ,EACN,sFAAsF,GACtF,mFAAmF,GACnF,oFAAoF,GACpF,iFAAiF,GACjF,oFAAoF,GACpF,iFAAiF,GACjF;IACJ,CAAC;IACDC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRN,IAAI,CAACO,mBAAmB,EACxBP,IAAI,CAACQ,oBAAoB,EACzBR,IAAI,CAACS,iBAAiB,EACtBT,IAAI,CAACU,gBAAgB,EACrBV,IAAI,CAACW,aAAa,EAClB;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,EACD;MACEF,SAAS,EAAE,OAAO;MAClBG,aAAa,EAAE,0CAA0C;MACzDD,GAAG,EAAE;IACP,CAAC,EACD;MACEC,aAAa,EAAE,+BAA+B;MAC9CD,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;AACH;AAEAE,MAAM,CAACC,OAAO,GAAGlB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}