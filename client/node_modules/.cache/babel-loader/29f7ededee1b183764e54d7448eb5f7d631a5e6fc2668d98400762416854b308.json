{"ast": null, "code": "'use strict';\n\nmodule.exports = toml;\ntoml.displayName = 'toml';\ntoml.aliases = [];\nfunction toml(Prism) {\n  ;\n  (function (Prism) {\n    var key = /(?:[\\w-]+|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/.source;\n    /**\n     * @param {string} pattern\n     */\n    function insertKey(pattern) {\n      return pattern.replace(/__/g, function () {\n        return key;\n      });\n    }\n    Prism.languages.toml = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      table: {\n        pattern: RegExp(insertKey(/(^[\\t ]*\\[\\s*(?:\\[\\s*)?)__(?:\\s*\\.\\s*__)*(?=\\s*\\])/.source), 'm'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name'\n      },\n      key: {\n        pattern: RegExp(insertKey(/(^[\\t ]*|[{,]\\s*)__(?:\\s*\\.\\s*__)*(?=\\s*=)/.source), 'm'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'property'\n      },\n      string: {\n        pattern: /\"\"\"(?:\\\\[\\s\\S]|[^\\\\])*?\"\"\"|'''[\\s\\S]*?'''|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      date: [{\n        // Offset Date-Time, Local Date-Time, Local Date\n        pattern: /\\b\\d{4}-\\d{2}-\\d{2}(?:[T\\s]\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|[+-]\\d{2}:\\d{2})?)?\\b/i,\n        alias: 'number'\n      }, {\n        // Local Time\n        pattern: /\\b\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?\\b/,\n        alias: 'number'\n      }],\n      number: /(?:\\b0(?:x[\\da-zA-Z]+(?:_[\\da-zA-Z]+)*|o[0-7]+(?:_[0-7]+)*|b[10]+(?:_[10]+)*))\\b|[-+]?\\b\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?\\b|[-+]?\\b(?:inf|nan)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      punctuation: /[.,=[\\]{}]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "toml", "displayName", "aliases", "Prism", "key", "source", "<PERSON><PERSON><PERSON>", "pattern", "replace", "languages", "comment", "greedy", "table", "RegExp", "lookbehind", "alias", "string", "date", "number", "boolean", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/toml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = toml\ntoml.displayName = 'toml'\ntoml.aliases = []\nfunction toml(Prism) {\n  ;(function (Prism) {\n    var key = /(?:[\\w-]+|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/.source\n    /**\n     * @param {string} pattern\n     */\n    function insertKey(pattern) {\n      return pattern.replace(/__/g, function () {\n        return key\n      })\n    }\n    Prism.languages.toml = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      table: {\n        pattern: RegExp(\n          insertKey(\n            /(^[\\t ]*\\[\\s*(?:\\[\\s*)?)__(?:\\s*\\.\\s*__)*(?=\\s*\\])/.source\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name'\n      },\n      key: {\n        pattern: RegExp(\n          insertKey(/(^[\\t ]*|[{,]\\s*)__(?:\\s*\\.\\s*__)*(?=\\s*=)/.source),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'property'\n      },\n      string: {\n        pattern:\n          /\"\"\"(?:\\\\[\\s\\S]|[^\\\\])*?\"\"\"|'''[\\s\\S]*?'''|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      date: [\n        {\n          // Offset Date-Time, Local Date-Time, Local Date\n          pattern:\n            /\\b\\d{4}-\\d{2}-\\d{2}(?:[T\\s]\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|[+-]\\d{2}:\\d{2})?)?\\b/i,\n          alias: 'number'\n        },\n        {\n          // Local Time\n          pattern: /\\b\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?\\b/,\n          alias: 'number'\n        }\n      ],\n      number:\n        /(?:\\b0(?:x[\\da-zA-Z]+(?:_[\\da-zA-Z]+)*|o[0-7]+(?:_[0-7]+)*|b[10]+(?:_[10]+)*))\\b|[-+]?\\b\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?\\b|[-+]?\\b(?:inf|nan)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      punctuation: /[.,=[\\]{}]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,GAAG,GAAG,8CAA8C,CAACC,MAAM;IAC/D;AACJ;AACA;IACI,SAASC,SAASA,CAACC,OAAO,EAAE;MAC1B,OAAOA,OAAO,CAACC,OAAO,CAAC,KAAK,EAAE,YAAY;QACxC,OAAOJ,GAAG;MACZ,CAAC,CAAC;IACJ;IACAD,KAAK,CAACM,SAAS,CAACT,IAAI,GAAG;MACrBU,OAAO,EAAE;QACPH,OAAO,EAAE,KAAK;QACdI,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLL,OAAO,EAAEM,MAAM,CACbP,SAAS,CACP,oDAAoD,CAACD,MACvD,CAAC,EACD,GACF,CAAC;QACDS,UAAU,EAAE,IAAI;QAChBH,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE;MACT,CAAC;MACDX,GAAG,EAAE;QACHG,OAAO,EAAEM,MAAM,CACbP,SAAS,CAAC,4CAA4C,CAACD,MAAM,CAAC,EAC9D,GACF,CAAC;QACDS,UAAU,EAAE,IAAI;QAChBH,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAE;QACNT,OAAO,EACL,6EAA6E;QAC/EI,MAAM,EAAE;MACV,CAAC;MACDM,IAAI,EAAE,CACJ;QACE;QACAV,OAAO,EACL,mFAAmF;QACrFQ,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACAR,OAAO,EAAE,iCAAiC;QAC1CQ,KAAK,EAAE;MACT,CAAC,CACF;MACDG,MAAM,EACJ,0KAA0K;MAC5KC,OAAO,EAAE,oBAAoB;MAC7BC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEjB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}