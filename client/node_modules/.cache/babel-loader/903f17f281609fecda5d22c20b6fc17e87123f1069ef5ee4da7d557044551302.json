{"ast": null, "code": "'use strict';\n\nvar refractorC = require('./c.js');\nmodule.exports = cpp;\ncpp.displayName = 'cpp';\ncpp.aliases = [];\nfunction cpp(Prism) {\n  Prism.register(refractorC);\n  (function (Prism) {\n    var keyword = /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/;\n    var modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g, function () {\n      return keyword.source;\n    });\n    Prism.languages.cpp = Prism.languages.extend('c', {\n      'class-name': [{\n        pattern: RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(/<keyword>/g, function () {\n          return keyword.source;\n        })),\n        lookbehind: true\n      },\n      // This is intended to capture the class name of method implementations like:\n      //   void foo::bar() const {}\n      // However! The `foo` in the above example could also be a namespace, so we only capture the class name if\n      // it starts with an uppercase letter. This approximation should give decent results.\n      /\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/,\n      // This will capture the class name before destructors like:\n      //   Foo::~Foo() {}\n      /\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i,\n      // This also intends to capture the class name of method implementations but here the class has template\n      // parameters, so it can't be a namespace (until C++ adds generic namespaces).\n      /\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/],\n      keyword: keyword,\n      number: {\n        pattern: /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n        greedy: true\n      },\n      operator: />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n      boolean: /\\b(?:false|true)\\b/\n    });\n    Prism.languages.insertBefore('cpp', 'string', {\n      module: {\n        // https://en.cppreference.com/w/cpp/language/modules\n        pattern: RegExp(/(\\b(?:import|module)\\s+)/.source + '(?:' +\n        // header-name\n        /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source + '|' +\n        // module name or partition or both\n        /<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g, function () {\n          return modName;\n        }) + ')'),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          string: /^[<\"][\\s\\S]+/,\n          operator: /:/,\n          punctuation: /\\./\n        }\n      },\n      'raw-string': {\n        pattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n        alias: 'string',\n        greedy: true\n      }\n    });\n    Prism.languages.insertBefore('cpp', 'keyword', {\n      'generic-function': {\n        pattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n        inside: {\n          function: /^\\w+/,\n          generic: {\n            pattern: /<[\\s\\S]+/,\n            alias: 'class-name',\n            inside: Prism.languages.cpp\n          }\n        }\n      }\n    });\n    Prism.languages.insertBefore('cpp', 'operator', {\n      'double-colon': {\n        pattern: /::/,\n        alias: 'punctuation'\n      }\n    });\n    Prism.languages.insertBefore('cpp', 'class-name', {\n      // the base clause is an optional list of parent classes\n      // https://en.cppreference.com/w/cpp/language/class\n      'base-clause': {\n        pattern: /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n        lookbehind: true,\n        greedy: true,\n        inside: Prism.languages.extend('cpp', {})\n      }\n    });\n    Prism.languages.insertBefore('inside', 'double-colon', {\n      // All untokenized words that are not namespaces should be class names\n      'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i\n    }, Prism.languages.cpp['base-clause']);\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorC", "require", "module", "exports", "cpp", "displayName", "aliases", "Prism", "register", "keyword", "modName", "source", "replace", "languages", "extend", "pattern", "RegExp", "lookbehind", "number", "greedy", "operator", "boolean", "insertBefore", "inside", "string", "punctuation", "alias", "function", "generic"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/cpp.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = cpp\ncpp.displayName = 'cpp'\ncpp.aliases = []\nfunction cpp(Prism) {\n  Prism.register(refractorC)\n  ;(function (Prism) {\n    var keyword =\n      /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/\n    var modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(\n      /<keyword>/g,\n      function () {\n        return keyword.source\n      }\n    )\n    Prism.languages.cpp = Prism.languages.extend('c', {\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(\n              /<keyword>/g,\n              function () {\n                return keyword.source\n              }\n            )\n          ),\n          lookbehind: true\n        }, // This is intended to capture the class name of method implementations like:\n        //   void foo::bar() const {}\n        // However! The `foo` in the above example could also be a namespace, so we only capture the class name if\n        // it starts with an uppercase letter. This approximation should give decent results.\n        /\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/, // This will capture the class name before destructors like:\n        //   Foo::~Foo() {}\n        /\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i, // This also intends to capture the class name of method implementations but here the class has template\n        // parameters, so it can't be a namespace (until C++ adds generic namespaces).\n        /\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/\n      ],\n      keyword: keyword,\n      number: {\n        pattern:\n          /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n        greedy: true\n      },\n      operator:\n        />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n      boolean: /\\b(?:false|true)\\b/\n    })\n    Prism.languages.insertBefore('cpp', 'string', {\n      module: {\n        // https://en.cppreference.com/w/cpp/language/modules\n        pattern: RegExp(\n          /(\\b(?:import|module)\\s+)/.source +\n            '(?:' + // header-name\n            /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source +\n            '|' + // module name or partition or both\n            /<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(\n              /<mod-name>/g,\n              function () {\n                return modName\n              }\n            ) +\n            ')'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          string: /^[<\"][\\s\\S]+/,\n          operator: /:/,\n          punctuation: /\\./\n        }\n      },\n      'raw-string': {\n        pattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n        alias: 'string',\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('cpp', 'keyword', {\n      'generic-function': {\n        pattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n        inside: {\n          function: /^\\w+/,\n          generic: {\n            pattern: /<[\\s\\S]+/,\n            alias: 'class-name',\n            inside: Prism.languages.cpp\n          }\n        }\n      }\n    })\n    Prism.languages.insertBefore('cpp', 'operator', {\n      'double-colon': {\n        pattern: /::/,\n        alias: 'punctuation'\n      }\n    })\n    Prism.languages.insertBefore('cpp', 'class-name', {\n      // the base clause is an optional list of parent classes\n      // https://en.cppreference.com/w/cpp/language/class\n      'base-clause': {\n        pattern:\n          /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n        lookbehind: true,\n        greedy: true,\n        inside: Prism.languages.extend('cpp', {})\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'double-colon',\n      {\n        // All untokenized words that are not namespaces should be class names\n        'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i\n      },\n      Prism.languages.cpp['base-clause']\n    )\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClCC,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACR,UAAU,CAAC;EACzB,CAAC,UAAUO,KAAK,EAAE;IACjB,IAAIE,OAAO,GACT,msBAAmsB;IACrsB,IAAIC,OAAO,GAAG,sCAAsC,CAACC,MAAM,CAACC,OAAO,CACjE,YAAY,EACZ,YAAY;MACV,OAAOH,OAAO,CAACE,MAAM;IACvB,CACF,CAAC;IACDJ,KAAK,CAACM,SAAS,CAACT,GAAG,GAAGG,KAAK,CAACM,SAAS,CAACC,MAAM,CAAC,GAAG,EAAE;MAChD,YAAY,EAAE,CACZ;QACEC,OAAO,EAAEC,MAAM,CACb,+DAA+D,CAACL,MAAM,CAACC,OAAO,CAC5E,YAAY,EACZ,YAAY;UACV,OAAOH,OAAO,CAACE,MAAM;QACvB,CACF,CACF,CAAC;QACDM,UAAU,EAAE;MACd,CAAC;MAAE;MACH;MACA;MACA;MACA,gCAAgC;MAAE;MAClC;MACA,mCAAmC;MAAE;MACrC;MACA,gEAAgE,CACjE;MACDR,OAAO,EAAEA,OAAO;MAChBS,MAAM,EAAE;QACNH,OAAO,EACL,gJAAgJ;QAClJI,MAAM,EAAE;MACV,CAAC;MACDC,QAAQ,EACN,wHAAwH;MAC1HC,OAAO,EAAE;IACX,CAAC,CAAC;IACFd,KAAK,CAACM,SAAS,CAACS,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE;MAC5CpB,MAAM,EAAE;QACN;QACAa,OAAO,EAAEC,MAAM,CACb,0BAA0B,CAACL,MAAM,GAC/B,KAAK;QAAG;QACR,kDAAkD,CAACA,MAAM,GACzD,GAAG;QAAG;QACN,iDAAiD,CAACA,MAAM,CAACC,OAAO,CAC9D,aAAa,EACb,YAAY;UACV,OAAOF,OAAO;QAChB,CACF,CAAC,GACD,GACJ,CAAC;QACDO,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZI,MAAM,EAAE;UACNC,MAAM,EAAE,cAAc;UACtBJ,QAAQ,EAAE,GAAG;UACbK,WAAW,EAAE;QACf;MACF,CAAC;MACD,YAAY,EAAE;QACZV,OAAO,EAAE,mCAAmC;QAC5CW,KAAK,EAAE,QAAQ;QACfP,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IACFZ,KAAK,CAACM,SAAS,CAACS,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE;MAC7C,kBAAkB,EAAE;QAClBP,OAAO,EAAE,6DAA6D;QACtEQ,MAAM,EAAE;UACNI,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE;YACPb,OAAO,EAAE,UAAU;YACnBW,KAAK,EAAE,YAAY;YACnBH,MAAM,EAAEhB,KAAK,CAACM,SAAS,CAACT;UAC1B;QACF;MACF;IACF,CAAC,CAAC;IACFG,KAAK,CAACM,SAAS,CAACS,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE;MAC9C,cAAc,EAAE;QACdP,OAAO,EAAE,IAAI;QACbW,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFnB,KAAK,CAACM,SAAS,CAACS,YAAY,CAAC,KAAK,EAAE,YAAY,EAAE;MAChD;MACA;MACA,aAAa,EAAE;QACbP,OAAO,EACL,4EAA4E;QAC9EE,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZI,MAAM,EAAEhB,KAAK,CAACM,SAAS,CAACC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;MAC1C;IACF,CAAC,CAAC;IACFP,KAAK,CAACM,SAAS,CAACS,YAAY,CAC1B,QAAQ,EACR,cAAc,EACd;MACE;MACA,YAAY,EAAE;IAChB,CAAC,EACDf,KAAK,CAACM,SAAS,CAACT,GAAG,CAAC,aAAa,CACnC,CAAC;EACH,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}