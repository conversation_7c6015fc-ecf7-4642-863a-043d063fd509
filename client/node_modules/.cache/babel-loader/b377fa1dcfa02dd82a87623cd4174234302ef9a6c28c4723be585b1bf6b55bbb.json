{"ast": null, "code": "'use strict';\n\nmodule.exports = solidity;\nsolidity.displayName = 'solidity';\nsolidity.aliases = ['sol'];\nfunction solidity(Prism) {\n  Prism.languages.solidity = Prism.languages.extend('clike', {\n    'class-name': {\n      pattern: /(\\b(?:contract|enum|interface|library|new|struct|using)\\s+)(?!\\d)[\\w$]+/,\n      lookbehind: true\n    },\n    keyword: /\\b(?:_|anonymous|as|assembly|assert|break|calldata|case|constant|constructor|continue|contract|default|delete|do|else|emit|enum|event|external|for|from|function|if|import|indexed|inherited|interface|internal|is|let|library|mapping|memory|modifier|new|payable|pragma|private|public|pure|require|returns?|revert|selfdestruct|solidity|storage|struct|suicide|switch|this|throw|using|var|view|while)\\b/,\n    operator: /=>|->|:=|=:|\\*\\*|\\+\\+|--|\\|\\||&&|<<=?|>>=?|[-+*/%^&|<>!=]=?|[~?]/\n  });\n  Prism.languages.insertBefore('solidity', 'keyword', {\n    builtin: /\\b(?:address|bool|byte|u?int(?:8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?|string|bytes(?:[1-9]|[12]\\d|3[0-2])?)\\b/\n  });\n  Prism.languages.insertBefore('solidity', 'number', {\n    version: {\n      pattern: /([<>]=?|\\^)\\d+\\.\\d+\\.\\d+\\b/,\n      lookbehind: true,\n      alias: 'number'\n    }\n  });\n  Prism.languages.sol = Prism.languages.solidity;\n}", "map": {"version": 3, "names": ["module", "exports", "solidity", "displayName", "aliases", "Prism", "languages", "extend", "pattern", "lookbehind", "keyword", "operator", "insertBefore", "builtin", "version", "alias", "sol"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/solidity.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = solidity\nsolidity.displayName = 'solidity'\nsolidity.aliases = ['sol']\nfunction solidity(Prism) {\n  Prism.languages.solidity = Prism.languages.extend('clike', {\n    'class-name': {\n      pattern:\n        /(\\b(?:contract|enum|interface|library|new|struct|using)\\s+)(?!\\d)[\\w$]+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:_|anonymous|as|assembly|assert|break|calldata|case|constant|constructor|continue|contract|default|delete|do|else|emit|enum|event|external|for|from|function|if|import|indexed|inherited|interface|internal|is|let|library|mapping|memory|modifier|new|payable|pragma|private|public|pure|require|returns?|revert|selfdestruct|solidity|storage|struct|suicide|switch|this|throw|using|var|view|while)\\b/,\n    operator: /=>|->|:=|=:|\\*\\*|\\+\\+|--|\\|\\||&&|<<=?|>>=?|[-+*/%^&|<>!=]=?|[~?]/\n  })\n  Prism.languages.insertBefore('solidity', 'keyword', {\n    builtin:\n      /\\b(?:address|bool|byte|u?int(?:8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?|string|bytes(?:[1-9]|[12]\\d|3[0-2])?)\\b/\n  })\n  Prism.languages.insertBefore('solidity', 'number', {\n    version: {\n      pattern: /([<>]=?|\\^)\\d+\\.\\d+\\.\\d+\\b/,\n      lookbehind: true,\n      alias: 'number'\n    }\n  })\n  Prism.languages.sol = Prism.languages.solidity\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AAC1B,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvBA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACzD,YAAY,EAAE;MACZC,OAAO,EACL,yEAAyE;MAC3EC,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EACL,8YAA8Y;IAChZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFN,KAAK,CAACC,SAAS,CAACM,YAAY,CAAC,UAAU,EAAE,SAAS,EAAE;IAClDC,OAAO,EACL;EACJ,CAAC,CAAC;EACFR,KAAK,CAACC,SAAS,CAACM,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE;IACjDE,OAAO,EAAE;MACPN,OAAO,EAAE,4BAA4B;MACrCC,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFV,KAAK,CAACC,SAAS,CAACU,GAAG,GAAGX,KAAK,CAACC,SAAS,CAACJ,QAAQ;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}