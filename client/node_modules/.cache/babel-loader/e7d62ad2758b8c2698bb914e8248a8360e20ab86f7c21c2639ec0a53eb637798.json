{"ast": null, "code": "/*\n Language: Flix\n Category: functional\n Author: <PERSON> <mm<PERSON><EMAIL>>\n Website: https://flix.dev/\n */\n\n/** @type LanguageFn */\nfunction flix(hljs) {\n  const CHAR = {\n    className: 'string',\n    begin: /'(.|\\\\[xXuU][a-zA-Z0-9]+)'/\n  };\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: '\"',\n      end: '\"'\n    }]\n  };\n  const NAME = {\n    className: 'title',\n    relevance: 0,\n    begin: /[^0-9\\n\\t \"'(),.`{}\\[\\]:;][^\\n\\t \"'(),.`{}\\[\\]:;]+|[^0-9\\n\\t \"'(),.`{}\\[\\]:;=]/\n  };\n  const METHOD = {\n    className: 'function',\n    beginKeywords: 'def',\n    end: /[:={\\[(\\n;]/,\n    excludeEnd: true,\n    contains: [NAME]\n  };\n  return {\n    name: 'Flix',\n    keywords: {\n      literal: 'true false',\n      keyword: 'case class def else enum if impl import in lat rel index let match namespace switch type yield with'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, CHAR, STRING, METHOD, hljs.C_NUMBER_MODE]\n  };\n}\nmodule.exports = flix;", "map": {"version": 3, "names": ["flix", "hljs", "CHAR", "className", "begin", "STRING", "variants", "end", "NAME", "relevance", "METHOD", "beginKeywords", "excludeEnd", "contains", "name", "keywords", "literal", "keyword", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/flix.js"], "sourcesContent": ["/*\n Language: Flix\n Category: functional\n Author: <PERSON> <mm<PERSON><EMAIL>>\n Website: https://flix.dev/\n */\n\n /** @type LanguageFn */\nfunction flix(hljs) {\n  const CHAR = {\n    className: 'string',\n    begin: /'(.|\\\\[xXuU][a-zA-Z0-9]+)'/\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: '\"',\n      end: '\"'\n    }]\n  };\n\n  const NAME = {\n    className: 'title',\n    relevance: 0,\n    begin: /[^0-9\\n\\t \"'(),.`{}\\[\\]:;][^\\n\\t \"'(),.`{}\\[\\]:;]+|[^0-9\\n\\t \"'(),.`{}\\[\\]:;=]/\n  };\n\n  const METHOD = {\n    className: 'function',\n    beginKeywords: 'def',\n    end: /[:={\\[(\\n;]/,\n    excludeEnd: true,\n    contains: [NAME]\n  };\n\n  return {\n    name: 'Flix',\n    keywords: {\n      literal: 'true false',\n      keyword: 'case class def else enum if impl import in lat rel index let match namespace switch type yield with'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      CHAR,\n      STRING,\n      METHOD,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = flix;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEC;AACD,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,IAAI,GAAG;IACXC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,MAAM,GAAG;IACbF,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CAAC;MACTF,KAAK,EAAE,GAAG;MACVG,GAAG,EAAE;IACP,CAAC;EACH,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,SAAS,EAAE,OAAO;IAClBM,SAAS,EAAE,CAAC;IACZL,KAAK,EAAE;EACT,CAAC;EAED,MAAMM,MAAM,GAAG;IACbP,SAAS,EAAE,UAAU;IACrBQ,aAAa,EAAE,KAAK;IACpBJ,GAAG,EAAE,aAAa;IAClBK,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,CAACL,IAAI;EACjB,CAAC;EAED,OAAO;IACLM,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;MACRC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE;IACX,CAAC;IACDJ,QAAQ,EAAE,CACRZ,IAAI,CAACiB,mBAAmB,EACxBjB,IAAI,CAACkB,oBAAoB,EACzBjB,IAAI,EACJG,MAAM,EACNK,MAAM,EACNT,IAAI,CAACmB,aAAa;EAEtB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGtB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}