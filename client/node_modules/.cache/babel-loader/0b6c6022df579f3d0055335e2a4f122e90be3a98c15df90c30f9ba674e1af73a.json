{"ast": null, "code": "/*\nLanguage: Oxygene\nAuthor: <PERSON> <<EMAIL>>\nDescription: Oxygene is built on the foundation of Object Pascal, revamped and extended to be a modern language for the twenty-first century.\nWebsite: https://www.elementscompiler.com/elements/default.aspx\n*/\n\nfunction oxygene(hljs) {\n  const OXYGENE_KEYWORDS = {\n    $pattern: /\\.?\\w+/,\n    keyword: 'abstract add and array as asc aspect assembly async begin break block by case class concat const copy constructor continue ' + 'create default delegate desc distinct div do downto dynamic each else empty end ensure enum equals event except exit extension external false ' + 'final finalize finalizer finally flags for forward from function future global group has if implementation implements implies in index inherited ' + 'inline interface into invariants is iterator join locked locking loop matching method mod module namespace nested new nil not notify nullable of ' + 'old on operator or order out override parallel params partial pinned private procedure property protected public queryable raise read readonly ' + 'record reintroduce remove repeat require result reverse sealed select self sequence set shl shr skip static step soft take then to true try tuple ' + 'type union unit unsafe until uses using var virtual raises volatile where while with write xor yield await mapped deprecated stdcall cdecl pascal ' + 'register safecall overload library platform reference packed strict published autoreleasepool selector strong weak unretained'\n  };\n  const CURLY_COMMENT = hljs.COMMENT(/\\{/, /\\}/, {\n    relevance: 0\n  });\n  const PAREN_COMMENT = hljs.COMMENT('\\\\(\\\\*', '\\\\*\\\\)', {\n    relevance: 10\n  });\n  const STRING = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    contains: [{\n      begin: '\\'\\''\n    }]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: '(#\\\\d+)+'\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'function constructor destructor procedure method',\n    end: '[:;]',\n    keywords: 'function constructor|10 destructor|10 procedure|10 method|10',\n    contains: [hljs.TITLE_MODE, {\n      className: 'params',\n      begin: '\\\\(',\n      end: '\\\\)',\n      keywords: OXYGENE_KEYWORDS,\n      contains: [STRING, CHAR_STRING]\n    }, CURLY_COMMENT, PAREN_COMMENT]\n  };\n  return {\n    name: 'Oxygene',\n    case_insensitive: true,\n    keywords: OXYGENE_KEYWORDS,\n    illegal: '(\"|\\\\$[G-Zg-z]|\\\\/\\\\*|</|=>|->)',\n    contains: [CURLY_COMMENT, PAREN_COMMENT, hljs.C_LINE_COMMENT_MODE, STRING, CHAR_STRING, hljs.NUMBER_MODE, FUNCTION, {\n      className: 'class',\n      begin: '=\\\\bclass\\\\b',\n      end: 'end;',\n      keywords: OXYGENE_KEYWORDS,\n      contains: [STRING, CHAR_STRING, CURLY_COMMENT, PAREN_COMMENT, hljs.C_LINE_COMMENT_MODE, FUNCTION]\n    }]\n  };\n}\nmodule.exports = oxygene;", "map": {"version": 3, "names": ["oxygene", "hljs", "OXYGENE_KEYWORDS", "$pattern", "keyword", "CURLY_COMMENT", "COMMENT", "relevance", "PAREN_COMMENT", "STRING", "className", "begin", "end", "contains", "CHAR_STRING", "FUNCTION", "beginKeywords", "keywords", "TITLE_MODE", "name", "case_insensitive", "illegal", "C_LINE_COMMENT_MODE", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/oxygene.js"], "sourcesContent": ["/*\nLanguage: Oxygene\nAuthor: <PERSON> <<EMAIL>>\nDescription: Oxygene is built on the foundation of Object Pascal, revamped and extended to be a modern language for the twenty-first century.\nWebsite: https://www.elementscompiler.com/elements/default.aspx\n*/\n\nfunction oxygene(hljs) {\n  const OXYGENE_KEYWORDS = {\n    $pattern: /\\.?\\w+/,\n    keyword:\n      'abstract add and array as asc aspect assembly async begin break block by case class concat const copy constructor continue ' +\n      'create default delegate desc distinct div do downto dynamic each else empty end ensure enum equals event except exit extension external false ' +\n      'final finalize finalizer finally flags for forward from function future global group has if implementation implements implies in index inherited ' +\n      'inline interface into invariants is iterator join locked locking loop matching method mod module namespace nested new nil not notify nullable of ' +\n      'old on operator or order out override parallel params partial pinned private procedure property protected public queryable raise read readonly ' +\n      'record reintroduce remove repeat require result reverse sealed select self sequence set shl shr skip static step soft take then to true try tuple ' +\n      'type union unit unsafe until uses using var virtual raises volatile where while with write xor yield await mapped deprecated stdcall cdecl pascal ' +\n      'register safecall overload library platform reference packed strict published autoreleasepool selector strong weak unretained'\n  };\n  const CURLY_COMMENT = hljs.COMMENT(\n    /\\{/,\n    /\\}/,\n    {\n      relevance: 0\n    }\n  );\n  const PAREN_COMMENT = hljs.COMMENT(\n    '\\\\(\\\\*',\n    '\\\\*\\\\)',\n    {\n      relevance: 10\n    }\n  );\n  const STRING = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    contains: [\n      {\n        begin: '\\'\\''\n      }\n    ]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: '(#\\\\d+)+'\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'function constructor destructor procedure method',\n    end: '[:;]',\n    keywords: 'function constructor|10 destructor|10 procedure|10 method|10',\n    contains: [\n      hljs.TITLE_MODE,\n      {\n        className: 'params',\n        begin: '\\\\(',\n        end: '\\\\)',\n        keywords: OXYGENE_KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING\n        ]\n      },\n      CURLY_COMMENT,\n      PAREN_COMMENT\n    ]\n  };\n  return {\n    name: 'Oxygene',\n    case_insensitive: true,\n    keywords: OXYGENE_KEYWORDS,\n    illegal: '(\"|\\\\$[G-Zg-z]|\\\\/\\\\*|</|=>|->)',\n    contains: [\n      CURLY_COMMENT,\n      PAREN_COMMENT,\n      hljs.C_LINE_COMMENT_MODE,\n      STRING,\n      CHAR_STRING,\n      hljs.NUMBER_MODE,\n      FUNCTION,\n      {\n        className: 'class',\n        begin: '=\\\\bclass\\\\b',\n        end: 'end;',\n        keywords: OXYGENE_KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING,\n          CURLY_COMMENT,\n          PAREN_COMMENT,\n          hljs.C_LINE_COMMENT_MODE,\n          FUNCTION\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = oxygene;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,gBAAgB,GAAG;IACvBC,QAAQ,EAAE,QAAQ;IAClBC,OAAO,EACL,6HAA6H,GAC7H,gJAAgJ,GAChJ,mJAAmJ,GACnJ,mJAAmJ,GACnJ,iJAAiJ,GACjJ,oJAAoJ,GACpJ,oJAAoJ,GACpJ;EACJ,CAAC;EACD,MAAMC,aAAa,GAAGJ,IAAI,CAACK,OAAO,CAChC,IAAI,EACJ,IAAI,EACJ;IACEC,SAAS,EAAE;EACb,CACF,CAAC;EACD,MAAMC,aAAa,GAAGP,IAAI,CAACK,OAAO,CAChC,QAAQ,EACR,QAAQ,EACR;IACEC,SAAS,EAAE;EACb,CACF,CAAC;EACD,MAAME,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMG,WAAW,GAAG;IAClBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMI,QAAQ,GAAG;IACfL,SAAS,EAAE,UAAU;IACrBM,aAAa,EAAE,kDAAkD;IACjEJ,GAAG,EAAE,MAAM;IACXK,QAAQ,EAAE,8DAA8D;IACxEJ,QAAQ,EAAE,CACRZ,IAAI,CAACiB,UAAU,EACf;MACER,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,KAAK;MACVK,QAAQ,EAAEf,gBAAgB;MAC1BW,QAAQ,EAAE,CACRJ,MAAM,EACNK,WAAW;IAEf,CAAC,EACDT,aAAa,EACbG,aAAa;EAEjB,CAAC;EACD,OAAO;IACLW,IAAI,EAAE,SAAS;IACfC,gBAAgB,EAAE,IAAI;IACtBH,QAAQ,EAAEf,gBAAgB;IAC1BmB,OAAO,EAAE,iCAAiC;IAC1CR,QAAQ,EAAE,CACRR,aAAa,EACbG,aAAa,EACbP,IAAI,CAACqB,mBAAmB,EACxBb,MAAM,EACNK,WAAW,EACXb,IAAI,CAACsB,WAAW,EAChBR,QAAQ,EACR;MACEL,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,cAAc;MACrBC,GAAG,EAAE,MAAM;MACXK,QAAQ,EAAEf,gBAAgB;MAC1BW,QAAQ,EAAE,CACRJ,MAAM,EACNK,WAAW,EACXT,aAAa,EACbG,aAAa,EACbP,IAAI,CAACqB,mBAAmB,EACxBP,QAAQ;IAEZ,CAAC;EAEL,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGzB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}