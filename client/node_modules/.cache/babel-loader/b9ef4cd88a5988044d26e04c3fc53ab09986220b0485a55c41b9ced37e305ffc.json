{"ast": null, "code": "'use strict';\n\nmodule.exports = go;\ngo.displayName = 'go';\ngo.aliases = [];\nfunction go(Prism) {\n  Prism.languages.go = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword: /\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/,\n    boolean: /\\b(?:_|false|iota|nil|true)\\b/,\n    number: [\n    // binary and octal integers\n    /\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i,\n    // hexadecimal integers and floats\n    /\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i,\n    // decimal integers and floats\n    /(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i],\n    operator: /[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./,\n    builtin: /\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/\n  });\n  Prism.languages.insertBefore('go', 'string', {\n    char: {\n      pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/,\n      greedy: true\n    }\n  });\n  delete Prism.languages.go['class-name'];\n}", "map": {"version": 3, "names": ["module", "exports", "go", "displayName", "aliases", "Prism", "languages", "extend", "string", "pattern", "lookbehind", "greedy", "keyword", "boolean", "number", "operator", "builtin", "insertBefore", "char"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/go.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = go\ngo.displayName = 'go'\ngo.aliases = []\nfunction go(Prism) {\n  Prism.languages.go = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/,\n    boolean: /\\b(?:_|false|iota|nil|true)\\b/,\n    number: [\n      // binary and octal integers\n      /\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i, // hexadecimal integers and floats\n      /\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i, // decimal integers and floats\n      /(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i\n    ],\n    operator:\n      /[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./,\n    builtin:\n      /\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/\n  })\n  Prism.languages.insertBefore('go', 'string', {\n    char: {\n      pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/,\n      greedy: true\n    }\n  })\n  delete Prism.languages.go['class-name']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,EAAE;AACnBA,EAAE,CAACC,WAAW,GAAG,IAAI;AACrBD,EAAE,CAACE,OAAO,GAAG,EAAE;AACf,SAASF,EAAEA,CAACG,KAAK,EAAE;EACjBA,KAAK,CAACC,SAAS,CAACJ,EAAE,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACnDC,MAAM,EAAE;MACNC,OAAO,EAAE,wCAAwC;MACjDC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EACL,qKAAqK;IACvKC,OAAO,EAAE,+BAA+B;IACxCC,MAAM,EAAE;IACN;IACA,8BAA8B;IAAE;IAChC,+EAA+E;IAAE;IACjF,oEAAoE,CACrE;IACDC,QAAQ,EACN,uFAAuF;IACzFC,OAAO,EACL;EACJ,CAAC,CAAC;EACFX,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE;IAC3CC,IAAI,EAAE;MACJT,OAAO,EAAE,4BAA4B;MACrCE,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACF,OAAON,KAAK,CAACC,SAAS,CAACJ,EAAE,CAAC,YAAY,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}