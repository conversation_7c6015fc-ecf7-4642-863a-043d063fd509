{"ast": null, "code": "/*\nLanguage: Elixir\nAuthor: <PERSON> <<EMAIL>>\nDescription: language definition for Elixir source code files (.ex and .exs).  Based on ruby language support.\nCategory: functional\nWebsite: https://elixir-lang.org\n*/\n\n/** @type LanguageFn */\nfunction elixir(hljs) {\n  const ELIXIR_IDENT_RE = '[a-zA-Z_][a-zA-Z0-9_.]*(!|\\\\?)?';\n  const ELIXIR_METHOD_RE = '[a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?';\n  const ELIXIR_KEYWORDS = {\n    $pattern: ELIXIR_IDENT_RE,\n    keyword: 'and false then defined module in return redo retry end for true self when ' + 'next until do begin unless nil break not case cond alias while ensure or ' + 'include use alias fn quote require import with|0'\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: ELIXIR_KEYWORDS\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: '(\\\\b0o[0-7_]+)|(\\\\b0b[01_]+)|(\\\\b0x[0-9a-fA-F_]+)|(-?\\\\b[1-9][0-9_]*(\\\\.[0-9_]+([eE][-+]?[0-9]+)?)?)',\n    relevance: 0\n  };\n  const SIGIL_DELIMITERS = '[/|([{<\"\\']';\n  const LOWERCASE_SIGIL = {\n    className: 'string',\n    begin: '~[a-z]' + '(?=' + SIGIL_DELIMITERS + ')',\n    contains: [{\n      endsParent: true,\n      contains: [{\n        contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n        variants: [{\n          begin: /\"/,\n          end: /\"/\n        }, {\n          begin: /'/,\n          end: /'/\n        }, {\n          begin: /\\//,\n          end: /\\//\n        }, {\n          begin: /\\|/,\n          end: /\\|/\n        }, {\n          begin: /\\(/,\n          end: /\\)/\n        }, {\n          begin: /\\[/,\n          end: /\\]/\n        }, {\n          begin: /\\{/,\n          end: /\\}/\n        }, {\n          begin: /</,\n          end: />/\n        }]\n      }]\n    }]\n  };\n  const UPCASE_SIGIL = {\n    className: 'string',\n    begin: '~[A-Z]' + '(?=' + SIGIL_DELIMITERS + ')',\n    contains: [{\n      begin: /\"/,\n      end: /\"/\n    }, {\n      begin: /'/,\n      end: /'/\n    }, {\n      begin: /\\//,\n      end: /\\//\n    }, {\n      begin: /\\|/,\n      end: /\\|/\n    }, {\n      begin: /\\(/,\n      end: /\\)/\n    }, {\n      begin: /\\[/,\n      end: /\\]/\n    }, {\n      begin: /\\{/,\n      end: /\\}/\n    }, {\n      begin: /</,\n      end: />/\n    }]\n  };\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n    variants: [{\n      begin: /\"\"\"/,\n      end: /\"\"\"/\n    }, {\n      begin: /'''/,\n      end: /'''/\n    }, {\n      begin: /~S\"\"\"/,\n      end: /\"\"\"/,\n      contains: [] // override default\n    }, {\n      begin: /~S\"/,\n      end: /\"/,\n      contains: [] // override default\n    }, {\n      begin: /~S'''/,\n      end: /'''/,\n      contains: [] // override default\n    }, {\n      begin: /~S'/,\n      end: /'/,\n      contains: [] // override default\n    }, {\n      begin: /'/,\n      end: /'/\n    }, {\n      begin: /\"/,\n      end: /\"/\n    }]\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'def defp defmacro',\n    end: /\\B\\b/,\n    // the mode is ended by the title\n    contains: [hljs.inherit(hljs.TITLE_MODE, {\n      begin: ELIXIR_IDENT_RE,\n      endsParent: true\n    })]\n  };\n  const CLASS = hljs.inherit(FUNCTION, {\n    className: 'class',\n    beginKeywords: 'defimpl defmodule defprotocol defrecord',\n    end: /\\bdo\\b|$|;/\n  });\n  const ELIXIR_DEFAULT_CONTAINS = [STRING, UPCASE_SIGIL, LOWERCASE_SIGIL, hljs.HASH_COMMENT_MODE, CLASS, FUNCTION, {\n    begin: '::'\n  }, {\n    className: 'symbol',\n    begin: ':(?![\\\\s:])',\n    contains: [STRING, {\n      begin: ELIXIR_METHOD_RE\n    }],\n    relevance: 0\n  }, {\n    className: 'symbol',\n    begin: ELIXIR_IDENT_RE + ':(?!:)',\n    relevance: 0\n  }, NUMBER, {\n    className: 'variable',\n    begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))'\n  }, {\n    begin: '->'\n  }, {\n    // regexp container\n    begin: '(' + hljs.RE_STARTERS_RE + ')\\\\s*',\n    contains: [hljs.HASH_COMMENT_MODE, {\n      // to prevent false regex triggers for the division function:\n      // /:\n      begin: /\\/: (?=\\d+\\s*[,\\]])/,\n      relevance: 0,\n      contains: [NUMBER]\n    }, {\n      className: 'regexp',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n      variants: [{\n        begin: '/',\n        end: '/[a-z]*'\n      }, {\n        begin: '%r\\\\[',\n        end: '\\\\][a-z]*'\n      }]\n    }],\n    relevance: 0\n  }];\n  SUBST.contains = ELIXIR_DEFAULT_CONTAINS;\n  return {\n    name: 'Elixir',\n    keywords: ELIXIR_KEYWORDS,\n    contains: ELIXIR_DEFAULT_CONTAINS\n  };\n}\nmodule.exports = elixir;", "map": {"version": 3, "names": ["elixir", "hljs", "ELIXIR_IDENT_RE", "ELIXIR_METHOD_RE", "ELIXIR_KEYWORDS", "$pattern", "keyword", "SUBST", "className", "begin", "end", "keywords", "NUMBER", "relevance", "SIGIL_DELIMITERS", "LOWERCASE_SIGIL", "contains", "endsParent", "BACKSLASH_ESCAPE", "variants", "UPCASE_SIGIL", "STRING", "FUNCTION", "beginKeywords", "inherit", "TITLE_MODE", "CLASS", "ELIXIR_DEFAULT_CONTAINS", "HASH_COMMENT_MODE", "RE_STARTERS_RE", "illegal", "name", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/elixir.js"], "sourcesContent": ["/*\nLanguage: Elixir\nAuthor: <PERSON> <<EMAIL>>\nDescription: language definition for Elixir source code files (.ex and .exs).  Based on ruby language support.\nCategory: functional\nWebsite: https://elixir-lang.org\n*/\n\n/** @type LanguageFn */\nfunction elixir(hljs) {\n  const ELIXIR_IDENT_RE = '[a-zA-Z_][a-zA-Z0-9_.]*(!|\\\\?)?';\n  const ELIXIR_METHOD_RE = '[a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?';\n  const ELIXIR_KEYWORDS = {\n    $pattern: ELIXIR_IDENT_RE,\n    keyword: 'and false then defined module in return redo retry end for true self when ' +\n    'next until do begin unless nil break not case cond alias while ensure or ' +\n    'include use alias fn quote require import with|0'\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: ELIXIR_KEYWORDS\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: '(\\\\b0o[0-7_]+)|(\\\\b0b[01_]+)|(\\\\b0x[0-9a-fA-F_]+)|(-?\\\\b[1-9][0-9_]*(\\\\.[0-9_]+([eE][-+]?[0-9]+)?)?)',\n    relevance: 0\n  };\n  const SIGIL_DELIMITERS = '[/|([{<\"\\']';\n  const LOWERCASE_SIGIL = {\n    className: 'string',\n    begin: '~[a-z]' + '(?=' + SIGIL_DELIMITERS + ')',\n    contains: [\n      {\n        endsParent: true,\n        contains: [\n          {\n            contains: [\n              hljs.BACKSLASH_ESCAPE,\n              SUBST\n            ],\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/\n              },\n              {\n                begin: /'/,\n                end: /'/\n              },\n              {\n                begin: /\\//,\n                end: /\\//\n              },\n              {\n                begin: /\\|/,\n                end: /\\|/\n              },\n              {\n                begin: /\\(/,\n                end: /\\)/\n              },\n              {\n                begin: /\\[/,\n                end: /\\]/\n              },\n              {\n                begin: /\\{/,\n                end: /\\}/\n              },\n              {\n                begin: /</,\n                end: />/\n              }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n\n  const UPCASE_SIGIL = {\n    className: 'string',\n    begin: '~[A-Z]' + '(?=' + SIGIL_DELIMITERS + ')',\n    contains: [\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\\//,\n        end: /\\//\n      },\n      {\n        begin: /\\|/,\n        end: /\\|/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        begin: /\\[/,\n        end: /\\]/\n      },\n      {\n        begin: /\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /</,\n        end: />/\n      }\n    ]\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /\"\"\"/,\n        end: /\"\"\"/\n      },\n      {\n        begin: /'''/,\n        end: /'''/\n      },\n      {\n        begin: /~S\"\"\"/,\n        end: /\"\"\"/,\n        contains: [] // override default\n      },\n      {\n        begin: /~S\"/,\n        end: /\"/,\n        contains: [] // override default\n      },\n      {\n        begin: /~S'''/,\n        end: /'''/,\n        contains: [] // override default\n      },\n      {\n        begin: /~S'/,\n        end: /'/,\n        contains: [] // override default\n      },\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      }\n    ]\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'def defp defmacro',\n    end: /\\B\\b/, // the mode is ended by the title\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: ELIXIR_IDENT_RE,\n        endsParent: true\n      })\n    ]\n  };\n  const CLASS = hljs.inherit(FUNCTION, {\n    className: 'class',\n    beginKeywords: 'defimpl defmodule defprotocol defrecord',\n    end: /\\bdo\\b|$|;/\n  });\n  const ELIXIR_DEFAULT_CONTAINS = [\n    STRING,\n    UPCASE_SIGIL,\n    LOWERCASE_SIGIL,\n    hljs.HASH_COMMENT_MODE,\n    CLASS,\n    FUNCTION,\n    {\n      begin: '::'\n    },\n    {\n      className: 'symbol',\n      begin: ':(?![\\\\s:])',\n      contains: [\n        STRING,\n        {\n          begin: ELIXIR_METHOD_RE\n        }\n      ],\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ELIXIR_IDENT_RE + ':(?!:)',\n      relevance: 0\n    },\n    NUMBER,\n    {\n      className: 'variable',\n      begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))'\n    },\n    {\n      begin: '->'\n    },\n    { // regexp container\n      begin: '(' + hljs.RE_STARTERS_RE + ')\\\\s*',\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        {\n          // to prevent false regex triggers for the division function:\n          // /:\n          begin: /\\/: (?=\\d+\\s*[,\\]])/,\n          relevance: 0,\n          contains: [NUMBER]\n        },\n        {\n          className: 'regexp',\n          illegal: '\\\\n',\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ],\n          variants: [\n            {\n              begin: '/',\n              end: '/[a-z]*'\n            },\n            {\n              begin: '%r\\\\[',\n              end: '\\\\][a-z]*'\n            }\n          ]\n        }\n      ],\n      relevance: 0\n    }\n  ];\n  SUBST.contains = ELIXIR_DEFAULT_CONTAINS;\n\n  return {\n    name: 'Elixir',\n    keywords: ELIXIR_KEYWORDS,\n    contains: ELIXIR_DEFAULT_CONTAINS\n  };\n}\n\nmodule.exports = elixir;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,eAAe,GAAG,iCAAiC;EACzD,MAAMC,gBAAgB,GAAG,kFAAkF;EAC3G,MAAMC,eAAe,GAAG;IACtBC,QAAQ,EAAEH,eAAe;IACzBI,OAAO,EAAE,4EAA4E,GACrF,2EAA2E,GAC3E;EACF,CAAC;EACD,MAAMC,KAAK,GAAG;IACZC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAEP;EACZ,CAAC;EACD,MAAMQ,MAAM,GAAG;IACbJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,sGAAsG;IAC7GI,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,gBAAgB,GAAG,aAAa;EACtC,MAAMC,eAAe,GAAG;IACtBP,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,QAAQ,GAAG,KAAK,GAAGK,gBAAgB,GAAG,GAAG;IAChDE,QAAQ,EAAE,CACR;MACEC,UAAU,EAAE,IAAI;MAChBD,QAAQ,EAAE,CACR;QACEA,QAAQ,EAAE,CACRf,IAAI,CAACiB,gBAAgB,EACrBX,KAAK,CACN;QACDY,QAAQ,EAAE,CACR;UACEV,KAAK,EAAE,GAAG;UACVC,GAAG,EAAE;QACP,CAAC,EACD;UACED,KAAK,EAAE,GAAG;UACVC,GAAG,EAAE;QACP,CAAC,EACD;UACED,KAAK,EAAE,IAAI;UACXC,GAAG,EAAE;QACP,CAAC,EACD;UACED,KAAK,EAAE,IAAI;UACXC,GAAG,EAAE;QACP,CAAC,EACD;UACED,KAAK,EAAE,IAAI;UACXC,GAAG,EAAE;QACP,CAAC,EACD;UACED,KAAK,EAAE,IAAI;UACXC,GAAG,EAAE;QACP,CAAC,EACD;UACED,KAAK,EAAE,IAAI;UACXC,GAAG,EAAE;QACP,CAAC,EACD;UACED,KAAK,EAAE,GAAG;UACVC,GAAG,EAAE;QACP,CAAC;MAEL,CAAC;IAEL,CAAC;EAEL,CAAC;EAED,MAAMU,YAAY,GAAG;IACnBZ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,QAAQ,GAAG,KAAK,GAAGK,gBAAgB,GAAG,GAAG;IAChDE,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EAED,MAAMW,MAAM,GAAG;IACbb,SAAS,EAAE,QAAQ;IACnBQ,QAAQ,EAAE,CACRf,IAAI,CAACiB,gBAAgB,EACrBX,KAAK,CACN;IACDY,QAAQ,EAAE,CACR;MACEV,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,KAAK;MACVM,QAAQ,EAAE,EAAE,CAAC;IACf,CAAC,EACD;MACEP,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,GAAG;MACRM,QAAQ,EAAE,EAAE,CAAC;IACf,CAAC,EACD;MACEP,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,KAAK;MACVM,QAAQ,EAAE,EAAE,CAAC;IACf,CAAC,EACD;MACEP,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,GAAG;MACRM,QAAQ,EAAE,EAAE,CAAC;IACf,CAAC,EACD;MACEP,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EACD,MAAMY,QAAQ,GAAG;IACfd,SAAS,EAAE,UAAU;IACrBe,aAAa,EAAE,mBAAmB;IAClCb,GAAG,EAAE,MAAM;IAAE;IACbM,QAAQ,EAAE,CACRf,IAAI,CAACuB,OAAO,CAACvB,IAAI,CAACwB,UAAU,EAAE;MAC5BhB,KAAK,EAAEP,eAAe;MACtBe,UAAU,EAAE;IACd,CAAC,CAAC;EAEN,CAAC;EACD,MAAMS,KAAK,GAAGzB,IAAI,CAACuB,OAAO,CAACF,QAAQ,EAAE;IACnCd,SAAS,EAAE,OAAO;IAClBe,aAAa,EAAE,yCAAyC;IACxDb,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAMiB,uBAAuB,GAAG,CAC9BN,MAAM,EACND,YAAY,EACZL,eAAe,EACfd,IAAI,CAAC2B,iBAAiB,EACtBF,KAAK,EACLJ,QAAQ,EACR;IACEb,KAAK,EAAE;EACT,CAAC,EACD;IACED,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,aAAa;IACpBO,QAAQ,EAAE,CACRK,MAAM,EACN;MACEZ,KAAK,EAAEN;IACT,CAAC,CACF;IACDU,SAAS,EAAE;EACb,CAAC,EACD;IACEL,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAEP,eAAe,GAAG,QAAQ;IACjCW,SAAS,EAAE;EACb,CAAC,EACDD,MAAM,EACN;IACEJ,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEA,KAAK,EAAE;EACT,CAAC,EACD;IAAE;IACAA,KAAK,EAAE,GAAG,GAAGR,IAAI,CAAC4B,cAAc,GAAG,OAAO;IAC1Cb,QAAQ,EAAE,CACRf,IAAI,CAAC2B,iBAAiB,EACtB;MACE;MACA;MACAnB,KAAK,EAAE,qBAAqB;MAC5BI,SAAS,EAAE,CAAC;MACZG,QAAQ,EAAE,CAACJ,MAAM;IACnB,CAAC,EACD;MACEJ,SAAS,EAAE,QAAQ;MACnBsB,OAAO,EAAE,KAAK;MACdd,QAAQ,EAAE,CACRf,IAAI,CAACiB,gBAAgB,EACrBX,KAAK,CACN;MACDY,QAAQ,EAAE,CACR;QACEV,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,CACF;IACDG,SAAS,EAAE;EACb,CAAC,CACF;EACDN,KAAK,CAACS,QAAQ,GAAGW,uBAAuB;EAExC,OAAO;IACLI,IAAI,EAAE,QAAQ;IACdpB,QAAQ,EAAEP,eAAe;IACzBY,QAAQ,EAAEW;EACZ,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGjC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}