{"ast": null, "code": "'use strict';\n\nmodule.exports = mermaid;\nmermaid.displayName = 'mermaid';\nmermaid.aliases = [];\nfunction mermaid(Prism) {\n  Prism.languages.mermaid = {\n    comment: {\n      pattern: /%%.*/,\n      greedy: true\n    },\n    style: {\n      pattern: /^([ \\t]*(?:classDef|linkStyle|style)[ \\t]+[\\w$-]+[ \\t]+)\\w.*[^\\s;]/m,\n      lookbehind: true,\n      inside: {\n        property: /\\b\\w[\\w-]*(?=[ \\t]*:)/,\n        operator: /:/,\n        punctuation: /,/\n      }\n    },\n    'inter-arrow-label': {\n      pattern: /([^<>ox.=-])(?:-[-.]|==)(?![<>ox.=-])[ \\t]*(?:\"[^\"\\r\\n]*\"|[^\\s\".=-](?:[^\\r\\n.=-]*[^\\s.=-])?)[ \\t]*(?:\\.+->?|--+[->]|==+[=>])(?![<>ox.=-])/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        arrow: {\n          pattern: /(?:\\.+->?|--+[->]|==+[=>])$/,\n          alias: 'operator'\n        },\n        label: {\n          pattern: /^([\\s\\S]{2}[ \\t]*)\\S(?:[\\s\\S]*\\S)?/,\n          lookbehind: true,\n          alias: 'property'\n        },\n        'arrow-head': {\n          pattern: /^\\S+/,\n          alias: ['arrow', 'operator']\n        }\n      }\n    },\n    arrow: [\n    // This might look complex but it really isn't.\n    // There are many possible arrows (see tests) and it's impossible to fit all of them into one pattern. The\n    // problem is that we only have one lookbehind per pattern. However, we cannot disallow too many arrow\n    // characters in the one lookbehind because that would create too many false negatives. So we have to split the\n    // arrows into different patterns.\n    {\n      // ER diagram\n      pattern: /(^|[^{}|o.-])[|}][|o](?:--|\\.\\.)[|o][|{](?![{}|o.-])/,\n      lookbehind: true,\n      alias: 'operator'\n    }, {\n      // flow chart\n      // (?:==+|--+|-\\.*-)\n      pattern: /(^|[^<>ox.=-])(?:[<ox](?:==+|--+|-\\.*-)[>ox]?|(?:==+|--+|-\\.*-)[>ox]|===+|---+|-\\.+-)(?![<>ox.=-])/,\n      lookbehind: true,\n      alias: 'operator'\n    }, {\n      // sequence diagram\n      pattern: /(^|[^<>()x-])(?:--?(?:>>|[x>)])(?![<>()x])|(?:<<|[x<(])--?(?!-))/,\n      lookbehind: true,\n      alias: 'operator'\n    }, {\n      // class diagram\n      pattern: /(^|[^<>|*o.-])(?:[*o]--|--[*o]|<\\|?(?:--|\\.\\.)|(?:--|\\.\\.)\\|?>|--|\\.\\.)(?![<>|*o.-])/,\n      lookbehind: true,\n      alias: 'operator'\n    }],\n    label: {\n      pattern: /(^|[^|<])\\|(?:[^\\r\\n\"|]|\"[^\"\\r\\n]*\")+\\|/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    text: {\n      pattern: /(?:[(\\[{]+|\\b>)(?:[^\\r\\n\"()\\[\\]{}]|\"[^\"\\r\\n]*\")+(?:[)\\]}]+|>)/,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"/,\n      greedy: true\n    },\n    annotation: {\n      pattern: /<<(?:abstract|choice|enumeration|fork|interface|join|service)>>|\\[\\[(?:choice|fork|join)\\]\\]/i,\n      alias: 'important'\n    },\n    keyword: [\n    // This language has both case-sensitive and case-insensitive keywords\n    {\n      pattern: /(^[ \\t]*)(?:action|callback|class|classDef|classDiagram|click|direction|erDiagram|flowchart|gantt|gitGraph|graph|journey|link|linkStyle|pie|requirementDiagram|sequenceDiagram|stateDiagram|stateDiagram-v2|style|subgraph)(?![\\w$-])/m,\n      lookbehind: true,\n      greedy: true\n    }, {\n      pattern: /(^[ \\t]*)(?:activate|alt|and|as|autonumber|deactivate|else|end(?:[ \\t]+note)?|loop|opt|par|participant|rect|state|note[ \\t]+(?:over|(?:left|right)[ \\t]+of))(?![\\w$-])/im,\n      lookbehind: true,\n      greedy: true\n    }],\n    entity: /#[a-z0-9]+;/,\n    operator: {\n      pattern: /(\\w[ \\t]*)&(?=[ \\t]*\\w)|:::|:/,\n      lookbehind: true\n    },\n    punctuation: /[(){};]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "mermaid", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "style", "lookbehind", "inside", "property", "operator", "punctuation", "arrow", "alias", "label", "text", "string", "annotation", "keyword", "entity"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/mermaid.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = mermaid\nmermaid.displayName = 'mermaid'\nmermaid.aliases = []\nfunction mermaid(Prism) {\n  Prism.languages.mermaid = {\n    comment: {\n      pattern: /%%.*/,\n      greedy: true\n    },\n    style: {\n      pattern:\n        /^([ \\t]*(?:classDef|linkStyle|style)[ \\t]+[\\w$-]+[ \\t]+)\\w.*[^\\s;]/m,\n      lookbehind: true,\n      inside: {\n        property: /\\b\\w[\\w-]*(?=[ \\t]*:)/,\n        operator: /:/,\n        punctuation: /,/\n      }\n    },\n    'inter-arrow-label': {\n      pattern:\n        /([^<>ox.=-])(?:-[-.]|==)(?![<>ox.=-])[ \\t]*(?:\"[^\"\\r\\n]*\"|[^\\s\".=-](?:[^\\r\\n.=-]*[^\\s.=-])?)[ \\t]*(?:\\.+->?|--+[->]|==+[=>])(?![<>ox.=-])/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        arrow: {\n          pattern: /(?:\\.+->?|--+[->]|==+[=>])$/,\n          alias: 'operator'\n        },\n        label: {\n          pattern: /^([\\s\\S]{2}[ \\t]*)\\S(?:[\\s\\S]*\\S)?/,\n          lookbehind: true,\n          alias: 'property'\n        },\n        'arrow-head': {\n          pattern: /^\\S+/,\n          alias: ['arrow', 'operator']\n        }\n      }\n    },\n    arrow: [\n      // This might look complex but it really isn't.\n      // There are many possible arrows (see tests) and it's impossible to fit all of them into one pattern. The\n      // problem is that we only have one lookbehind per pattern. However, we cannot disallow too many arrow\n      // characters in the one lookbehind because that would create too many false negatives. So we have to split the\n      // arrows into different patterns.\n      {\n        // ER diagram\n        pattern: /(^|[^{}|o.-])[|}][|o](?:--|\\.\\.)[|o][|{](?![{}|o.-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // flow chart\n        // (?:==+|--+|-\\.*-)\n        pattern:\n          /(^|[^<>ox.=-])(?:[<ox](?:==+|--+|-\\.*-)[>ox]?|(?:==+|--+|-\\.*-)[>ox]|===+|---+|-\\.+-)(?![<>ox.=-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // sequence diagram\n        pattern:\n          /(^|[^<>()x-])(?:--?(?:>>|[x>)])(?![<>()x])|(?:<<|[x<(])--?(?!-))/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // class diagram\n        pattern:\n          /(^|[^<>|*o.-])(?:[*o]--|--[*o]|<\\|?(?:--|\\.\\.)|(?:--|\\.\\.)\\|?>|--|\\.\\.)(?![<>|*o.-])/,\n        lookbehind: true,\n        alias: 'operator'\n      }\n    ],\n    label: {\n      pattern: /(^|[^|<])\\|(?:[^\\r\\n\"|]|\"[^\"\\r\\n]*\")+\\|/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    text: {\n      pattern: /(?:[(\\[{]+|\\b>)(?:[^\\r\\n\"()\\[\\]{}]|\"[^\"\\r\\n]*\")+(?:[)\\]}]+|>)/,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"/,\n      greedy: true\n    },\n    annotation: {\n      pattern:\n        /<<(?:abstract|choice|enumeration|fork|interface|join|service)>>|\\[\\[(?:choice|fork|join)\\]\\]/i,\n      alias: 'important'\n    },\n    keyword: [\n      // This language has both case-sensitive and case-insensitive keywords\n      {\n        pattern:\n          /(^[ \\t]*)(?:action|callback|class|classDef|classDiagram|click|direction|erDiagram|flowchart|gantt|gitGraph|graph|journey|link|linkStyle|pie|requirementDiagram|sequenceDiagram|stateDiagram|stateDiagram-v2|style|subgraph)(?![\\w$-])/m,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern:\n          /(^[ \\t]*)(?:activate|alt|and|as|autonumber|deactivate|else|end(?:[ \\t]+note)?|loop|opt|par|participant|rect|state|note[ \\t]+(?:over|(?:left|right)[ \\t]+of))(?![\\w$-])/im,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    entity: /#[a-z0-9]+;/,\n    operator: {\n      pattern: /(\\w[ \\t]*)&(?=[ \\t]*\\w)|:::|:/,\n      lookbehind: true\n    },\n    punctuation: /[(){};]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxBK,OAAO,EAAE;MACPC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLF,OAAO,EACL,qEAAqE;MACvEG,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,QAAQ,EAAE,uBAAuB;QACjCC,QAAQ,EAAE,GAAG;QACbC,WAAW,EAAE;MACf;IACF,CAAC;IACD,mBAAmB,EAAE;MACnBP,OAAO,EACL,2IAA2I;MAC7IG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACNI,KAAK,EAAE;UACLR,OAAO,EAAE,6BAA6B;UACtCS,KAAK,EAAE;QACT,CAAC;QACDC,KAAK,EAAE;UACLV,OAAO,EAAE,oCAAoC;UAC7CG,UAAU,EAAE,IAAI;UAChBM,KAAK,EAAE;QACT,CAAC;QACD,YAAY,EAAE;UACZT,OAAO,EAAE,MAAM;UACfS,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU;QAC7B;MACF;IACF,CAAC;IACDD,KAAK,EAAE;IACL;IACA;IACA;IACA;IACA;IACA;MACE;MACAR,OAAO,EAAE,sDAAsD;MAC/DG,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACA;MACAT,OAAO,EACL,oGAAoG;MACtGG,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAT,OAAO,EACL,kEAAkE;MACpEG,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAT,OAAO,EACL,sFAAsF;MACxFG,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE;IACT,CAAC,CACF;IACDC,KAAK,EAAE;MACLV,OAAO,EAAE,yCAAyC;MAClDG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZQ,KAAK,EAAE;IACT,CAAC;IACDE,IAAI,EAAE;MACJX,OAAO,EAAE,+DAA+D;MACxES,KAAK,EAAE;IACT,CAAC;IACDG,MAAM,EAAE;MACNZ,OAAO,EAAE,aAAa;MACtBC,MAAM,EAAE;IACV,CAAC;IACDY,UAAU,EAAE;MACVb,OAAO,EACL,+FAA+F;MACjGS,KAAK,EAAE;IACT,CAAC;IACDK,OAAO,EAAE;IACP;IACA;MACEd,OAAO,EACL,wOAAwO;MAC1OG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;IACV,CAAC,EACD;MACED,OAAO,EACL,0KAA0K;MAC5KG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;IACV,CAAC,CACF;IACDc,MAAM,EAAE,aAAa;IACrBT,QAAQ,EAAE;MACRN,OAAO,EAAE,+BAA+B;MACxCG,UAAU,EAAE;IACd,CAAC;IACDI,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}