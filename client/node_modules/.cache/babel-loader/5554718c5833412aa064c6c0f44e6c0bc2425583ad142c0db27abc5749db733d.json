{"ast": null, "code": "'use strict';\n\nmodule.exports = parser;\nparser.displayName = 'parser';\nparser.aliases = [];\nfunction parser(Prism) {\n  ;\n  (function (Prism) {\n    var parser = Prism.languages.parser = Prism.languages.extend('markup', {\n      keyword: {\n        pattern: /(^|[^^])(?:\\^(?:case|eval|for|if|switch|throw)\\b|@(?:BASE|CLASS|GET(?:_DEFAULT)?|OPTIONS|SET_DEFAULT|USE)\\b)/,\n        lookbehind: true\n      },\n      variable: {\n        pattern: /(^|[^^])\\B\\$(?:\\w+|(?=[.{]))(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\.|:+/\n        }\n      },\n      function: {\n        pattern: /(^|[^^])\\B[@^]\\w+(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          keyword: {\n            pattern: /(^@)(?:GET_|SET_)/,\n            lookbehind: true\n          },\n          punctuation: /\\.|:+/\n        }\n      },\n      escape: {\n        pattern: /\\^(?:[$^;@()\\[\\]{}\"':]|#[a-f\\d]*)/i,\n        alias: 'builtin'\n      },\n      punctuation: /[\\[\\](){};]/\n    });\n    parser = Prism.languages.insertBefore('parser', 'keyword', {\n      'parser-comment': {\n        pattern: /(\\s)#.*/,\n        lookbehind: true,\n        alias: 'comment'\n      },\n      expression: {\n        // Allow for 3 levels of depth\n        pattern: /(^|[^^])\\((?:[^()]|\\((?:[^()]|\\((?:[^()])*\\))*\\))*\\)/,\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          string: {\n            pattern: /(^|[^^])([\"'])(?:(?!\\2)[^^]|\\^[\\s\\S])*\\2/,\n            lookbehind: true\n          },\n          keyword: parser.keyword,\n          variable: parser.variable,\n          function: parser.function,\n          boolean: /\\b(?:false|true)\\b/,\n          number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?)\\b/i,\n          escape: parser.escape,\n          operator: /[~+*\\/\\\\%]|!(?:\\|\\|?|=)?|&&?|\\|\\|?|==|<[<=]?|>[>=]?|-[fd]?|\\b(?:def|eq|ge|gt|in|is|le|lt|ne)\\b/,\n          punctuation: parser.punctuation\n        }\n      }\n    });\n    Prism.languages.insertBefore('inside', 'punctuation', {\n      expression: parser.expression,\n      keyword: parser.keyword,\n      variable: parser.variable,\n      function: parser.function,\n      escape: parser.escape,\n      'parser-punctuation': {\n        pattern: parser.punctuation,\n        alias: 'punctuation'\n      }\n    }, parser['tag'].inside['attr-value']);\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "parser", "displayName", "aliases", "Prism", "languages", "extend", "keyword", "pattern", "lookbehind", "variable", "inside", "punctuation", "function", "escape", "alias", "insertBefore", "expression", "greedy", "string", "boolean", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/parser.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = parser\nparser.displayName = 'parser'\nparser.aliases = []\nfunction parser(Prism) {\n  ;(function (Prism) {\n    var parser = (Prism.languages.parser = Prism.languages.extend('markup', {\n      keyword: {\n        pattern:\n          /(^|[^^])(?:\\^(?:case|eval|for|if|switch|throw)\\b|@(?:BASE|CLASS|GET(?:_DEFAULT)?|OPTIONS|SET_DEFAULT|USE)\\b)/,\n        lookbehind: true\n      },\n      variable: {\n        pattern: /(^|[^^])\\B\\$(?:\\w+|(?=[.{]))(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\.|:+/\n        }\n      },\n      function: {\n        pattern: /(^|[^^])\\B[@^]\\w+(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          keyword: {\n            pattern: /(^@)(?:GET_|SET_)/,\n            lookbehind: true\n          },\n          punctuation: /\\.|:+/\n        }\n      },\n      escape: {\n        pattern: /\\^(?:[$^;@()\\[\\]{}\"':]|#[a-f\\d]*)/i,\n        alias: 'builtin'\n      },\n      punctuation: /[\\[\\](){};]/\n    }))\n    parser = Prism.languages.insertBefore('parser', 'keyword', {\n      'parser-comment': {\n        pattern: /(\\s)#.*/,\n        lookbehind: true,\n        alias: 'comment'\n      },\n      expression: {\n        // Allow for 3 levels of depth\n        pattern: /(^|[^^])\\((?:[^()]|\\((?:[^()]|\\((?:[^()])*\\))*\\))*\\)/,\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          string: {\n            pattern: /(^|[^^])([\"'])(?:(?!\\2)[^^]|\\^[\\s\\S])*\\2/,\n            lookbehind: true\n          },\n          keyword: parser.keyword,\n          variable: parser.variable,\n          function: parser.function,\n          boolean: /\\b(?:false|true)\\b/,\n          number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?)\\b/i,\n          escape: parser.escape,\n          operator:\n            /[~+*\\/\\\\%]|!(?:\\|\\|?|=)?|&&?|\\|\\|?|==|<[<=]?|>[>=]?|-[fd]?|\\b(?:def|eq|ge|gt|in|is|le|lt|ne)\\b/,\n          punctuation: parser.punctuation\n        }\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'punctuation',\n      {\n        expression: parser.expression,\n        keyword: parser.keyword,\n        variable: parser.variable,\n        function: parser.function,\n        escape: parser.escape,\n        'parser-punctuation': {\n          pattern: parser.punctuation,\n          alias: 'punctuation'\n        }\n      },\n      parser['tag'].inside['attr-value']\n    )\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIH,MAAM,GAAIG,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;MACtEC,OAAO,EAAE;QACPC,OAAO,EACL,8GAA8G;QAChHC,UAAU,EAAE;MACd,CAAC;MACDC,QAAQ,EAAE;QACRF,OAAO,EAAE,2DAA2D;QACpEC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,QAAQ,EAAE;QACRL,OAAO,EAAE,gDAAgD;QACzDC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNJ,OAAO,EAAE;YACPC,OAAO,EAAE,mBAAmB;YAC5BC,UAAU,EAAE;UACd,CAAC;UACDG,WAAW,EAAE;QACf;MACF,CAAC;MACDE,MAAM,EAAE;QACNN,OAAO,EAAE,oCAAoC;QAC7CO,KAAK,EAAE;MACT,CAAC;MACDH,WAAW,EAAE;IACf,CAAC,CAAE;IACHX,MAAM,GAAGG,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,QAAQ,EAAE,SAAS,EAAE;MACzD,gBAAgB,EAAE;QAChBR,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE,IAAI;QAChBM,KAAK,EAAE;MACT,CAAC;MACDE,UAAU,EAAE;QACV;QACAT,OAAO,EAAE,sDAAsD;QAC/DU,MAAM,EAAE,IAAI;QACZT,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNQ,MAAM,EAAE;YACNX,OAAO,EAAE,0CAA0C;YACnDC,UAAU,EAAE;UACd,CAAC;UACDF,OAAO,EAAEN,MAAM,CAACM,OAAO;UACvBG,QAAQ,EAAET,MAAM,CAACS,QAAQ;UACzBG,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;UACzBO,OAAO,EAAE,oBAAoB;UAC7BC,MAAM,EAAE,iDAAiD;UACzDP,MAAM,EAAEb,MAAM,CAACa,MAAM;UACrBQ,QAAQ,EACN,gGAAgG;UAClGV,WAAW,EAAEX,MAAM,CAACW;QACtB;MACF;IACF,CAAC,CAAC;IACFR,KAAK,CAACC,SAAS,CAACW,YAAY,CAC1B,QAAQ,EACR,aAAa,EACb;MACEC,UAAU,EAAEhB,MAAM,CAACgB,UAAU;MAC7BV,OAAO,EAAEN,MAAM,CAACM,OAAO;MACvBG,QAAQ,EAAET,MAAM,CAACS,QAAQ;MACzBG,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;MACzBC,MAAM,EAAEb,MAAM,CAACa,MAAM;MACrB,oBAAoB,EAAE;QACpBN,OAAO,EAAEP,MAAM,CAACW,WAAW;QAC3BG,KAAK,EAAE;MACT;IACF,CAAC,EACDd,MAAM,CAAC,KAAK,CAAC,CAACU,MAAM,CAAC,YAAY,CACnC,CAAC;EACH,CAAC,EAAEP,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}