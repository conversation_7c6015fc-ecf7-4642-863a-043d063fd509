{"ast": null, "code": "'use strict';\n\nmodule.exports = lolcode;\nlolcode.displayName = 'lolcode';\nlolcode.aliases = [];\nfunction lolcode(Prism) {\n  Prism.languages.lolcode = {\n    comment: [/\\bOBTW\\s[\\s\\S]*?\\sTLDR\\b/, /\\bBTW.+/],\n    string: {\n      pattern: /\"(?::.|[^\":])*\"/,\n      inside: {\n        variable: /:\\{[^}]+\\}/,\n        symbol: [/:\\([a-f\\d]+\\)/i, /:\\[[^\\]]+\\]/, /:[)>o\":]/]\n      },\n      greedy: true\n    },\n    number: /(?:\\B-)?(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)/,\n    symbol: {\n      pattern: /(^|\\s)(?:A )?(?:BUKKIT|NOOB|NUMBAR|NUMBR|TROOF|YARN)(?=\\s|,|$)/,\n      lookbehind: true,\n      inside: {\n        keyword: /A(?=\\s)/\n      }\n    },\n    label: {\n      pattern: /((?:^|\\s)(?:IM IN YR|IM OUTTA YR) )[a-zA-Z]\\w*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    function: {\n      pattern: /((?:^|\\s)(?:HOW IZ I|I IZ|IZ) )[a-zA-Z]\\w*/,\n      lookbehind: true\n    },\n    keyword: [{\n      pattern: /(^|\\s)(?:AN|FOUND YR|GIMMEH|GTFO|HAI|HAS A|HOW IZ I|I HAS A|I IZ|IF U SAY SO|IM IN YR|IM OUTTA YR|IS NOW(?: A)?|ITZ(?: A)?|IZ|KTHX|KTHXBYE|LIEK(?: A)?|MAEK|MEBBE|MKAY|NERFIN|NO WAI|O HAI IM|O RLY\\?|OIC|OMG|OMGWTF|R|SMOOSH|SRS|TIL|UPPIN|VISIBLE|WILE|WTF\\?|YA RLY|YR)(?=\\s|,|$)/,\n      lookbehind: true\n    }, /'Z(?=\\s|,|$)/],\n    boolean: {\n      pattern: /(^|\\s)(?:FAIL|WIN)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    variable: {\n      pattern: /(^|\\s)IT(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    operator: {\n      pattern: /(^|\\s)(?:NOT|BOTH SAEM|DIFFRINT|(?:ALL|ANY|BIGGR|BOTH|DIFF|EITHER|MOD|PRODUKT|QUOSHUNT|SMALLR|SUM|WON) OF)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    punctuation: /\\.{3}|…|,|!/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "lolcode", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "inside", "variable", "symbol", "greedy", "number", "lookbehind", "keyword", "label", "alias", "function", "boolean", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/lolcode.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = lolcode\nlolcode.displayName = 'lolcode'\nlolcode.aliases = []\nfunction lolcode(Prism) {\n  Prism.languages.lolcode = {\n    comment: [/\\bOBTW\\s[\\s\\S]*?\\sTLDR\\b/, /\\bBTW.+/],\n    string: {\n      pattern: /\"(?::.|[^\":])*\"/,\n      inside: {\n        variable: /:\\{[^}]+\\}/,\n        symbol: [/:\\([a-f\\d]+\\)/i, /:\\[[^\\]]+\\]/, /:[)>o\":]/]\n      },\n      greedy: true\n    },\n    number: /(?:\\B-)?(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)/,\n    symbol: {\n      pattern: /(^|\\s)(?:A )?(?:BUKKIT|NOOB|NUMBAR|NUMBR|TROOF|YARN)(?=\\s|,|$)/,\n      lookbehind: true,\n      inside: {\n        keyword: /A(?=\\s)/\n      }\n    },\n    label: {\n      pattern: /((?:^|\\s)(?:IM IN YR|IM OUTTA YR) )[a-zA-Z]\\w*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    function: {\n      pattern: /((?:^|\\s)(?:HOW IZ I|I IZ|IZ) )[a-zA-Z]\\w*/,\n      lookbehind: true\n    },\n    keyword: [\n      {\n        pattern:\n          /(^|\\s)(?:AN|FOUND YR|GIMMEH|GTFO|HAI|HAS A|HOW IZ I|I HAS A|I IZ|IF U SAY SO|IM IN YR|IM OUTTA YR|IS NOW(?: A)?|ITZ(?: A)?|IZ|KTHX|KTHXBYE|LIEK(?: A)?|MAEK|MEBBE|MKAY|NERFIN|NO WAI|O HAI IM|O RLY\\?|OIC|OMG|OMGWTF|R|SMOOSH|SRS|TIL|UPPIN|VISIBLE|WILE|WTF\\?|YA RLY|YR)(?=\\s|,|$)/,\n        lookbehind: true\n      },\n      /'Z(?=\\s|,|$)/\n    ],\n    boolean: {\n      pattern: /(^|\\s)(?:FAIL|WIN)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    variable: {\n      pattern: /(^|\\s)IT(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    operator: {\n      pattern:\n        /(^|\\s)(?:NOT|BOTH SAEM|DIFFRINT|(?:ALL|ANY|BIGGR|BOTH|DIFF|EITHER|MOD|PRODUKT|QUOSHUNT|SMALLR|SUM|WON) OF)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    punctuation: /\\.{3}|…|,|!/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxBK,OAAO,EAAE,CAAC,0BAA0B,EAAE,SAAS,CAAC;IAChDC,MAAM,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;QACNC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE,CAAC,gBAAgB,EAAE,aAAa,EAAE,UAAU;MACtD,CAAC;MACDC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE,qCAAqC;IAC7CF,MAAM,EAAE;MACNH,OAAO,EAAE,gEAAgE;MACzEM,UAAU,EAAE,IAAI;MAChBL,MAAM,EAAE;QACNM,OAAO,EAAE;MACX;IACF,CAAC;IACDC,KAAK,EAAE;MACLR,OAAO,EAAE,gDAAgD;MACzDM,UAAU,EAAE,IAAI;MAChBG,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRV,OAAO,EAAE,4CAA4C;MACrDM,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EAAE,CACP;MACEP,OAAO,EACL,qRAAqR;MACvRM,UAAU,EAAE;IACd,CAAC,EACD,cAAc,CACf;IACDK,OAAO,EAAE;MACPX,OAAO,EAAE,8BAA8B;MACvCM,UAAU,EAAE;IACd,CAAC;IACDJ,QAAQ,EAAE;MACRF,OAAO,EAAE,oBAAoB;MAC7BM,UAAU,EAAE;IACd,CAAC;IACDM,QAAQ,EAAE;MACRZ,OAAO,EACL,sHAAsH;MACxHM,UAAU,EAAE;IACd,CAAC;IACDO,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}