{"ast": null, "code": "/*\nLanguage: XL\nAuthor: <PERSON> <<EMAIL>>\nDescription: An extensible programming language, based on parse tree rewriting\nWebsite: http://xlr.sf.net\n*/\n\nfunction xl(hljs) {\n  const BUILTIN_MODULES = 'ObjectLoader Animate MovieCredits Slides Filters Shading Materials LensFlare Mapping VLCAudioVideo ' + 'StereoDecoder PointCloud NetworkAccess RemoteControl RegExp ChromaKey Snowfall NodeJS Speech Charts';\n  const XL_KEYWORDS = {\n    $pattern: /[a-zA-Z][a-zA-Z0-9_?]*/,\n    keyword: 'if then else do while until for loop import with is as where when by data constant ' + 'integer real text name boolean symbol infix prefix postfix block tree',\n    literal: 'true false nil',\n    built_in: 'in mod rem and or xor not abs sign floor ceil sqrt sin cos tan asin ' + 'acos atan exp expm1 log log2 log10 log1p pi at text_length text_range ' + 'text_find text_replace contains page slide basic_slide title_slide ' + 'title subtitle fade_in fade_out fade_at clear_color color line_color ' + 'line_width texture_wrap texture_transform texture scale_?x scale_?y ' + 'scale_?z? translate_?x translate_?y translate_?z? rotate_?x rotate_?y ' + 'rotate_?z? rectangle circle ellipse sphere path line_to move_to ' + 'quad_to curve_to theme background contents locally time mouse_?x ' + 'mouse_?y mouse_buttons ' + BUILTIN_MODULES\n  };\n  const DOUBLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    illegal: '\\\\n'\n  };\n  const SINGLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    illegal: '\\\\n'\n  };\n  const LONG_TEXT = {\n    className: 'string',\n    begin: '<<',\n    end: '>>'\n  };\n  const BASED_NUMBER = {\n    className: 'number',\n    begin: '[0-9]+#[0-9A-Z_]+(\\\\.[0-9-A-Z_]+)?#?([Ee][+-]?[0-9]+)?'\n  };\n  const IMPORT = {\n    beginKeywords: 'import',\n    end: '$',\n    keywords: XL_KEYWORDS,\n    contains: [DOUBLE_QUOTE_TEXT]\n  };\n  const FUNCTION_DEFINITION = {\n    className: 'function',\n    begin: /[a-z][^\\n]*->/,\n    returnBegin: true,\n    end: /->/,\n    contains: [hljs.inherit(hljs.TITLE_MODE, {\n      starts: {\n        endsWithParent: true,\n        keywords: XL_KEYWORDS\n      }\n    })]\n  };\n  return {\n    name: 'XL',\n    aliases: ['tao'],\n    keywords: XL_KEYWORDS,\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, DOUBLE_QUOTE_TEXT, SINGLE_QUOTE_TEXT, LONG_TEXT, FUNCTION_DEFINITION, IMPORT, BASED_NUMBER, hljs.NUMBER_MODE]\n  };\n}\nmodule.exports = xl;", "map": {"version": 3, "names": ["xl", "hljs", "BUILTIN_MODULES", "XL_KEYWORDS", "$pattern", "keyword", "literal", "built_in", "DOUBLE_QUOTE_TEXT", "className", "begin", "end", "illegal", "SINGLE_QUOTE_TEXT", "LONG_TEXT", "BASED_NUMBER", "IMPORT", "beginKeywords", "keywords", "contains", "FUNCTION_DEFINITION", "returnBegin", "inherit", "TITLE_MODE", "starts", "endsWithParent", "name", "aliases", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/xl.js"], "sourcesContent": ["/*\nLanguage: XL\nAuthor: <PERSON> <<EMAIL>>\nDescription: An extensible programming language, based on parse tree rewriting\nWebsite: http://xlr.sf.net\n*/\n\nfunction xl(hljs) {\n  const BUILTIN_MODULES =\n    'ObjectLoader Animate MovieCredits Slides Filters Shading Materials LensFlare Mapping VLCAudioVideo ' +\n    'StereoDecoder PointCloud NetworkAccess RemoteControl RegExp ChromaKey Snowfall NodeJS Speech Charts';\n\n  const XL_KEYWORDS = {\n    $pattern: /[a-zA-Z][a-zA-Z0-9_?]*/,\n    keyword:\n      'if then else do while until for loop import with is as where when by data constant ' +\n      'integer real text name boolean symbol infix prefix postfix block tree',\n    literal:\n      'true false nil',\n    built_in:\n      'in mod rem and or xor not abs sign floor ceil sqrt sin cos tan asin ' +\n      'acos atan exp expm1 log log2 log10 log1p pi at text_length text_range ' +\n      'text_find text_replace contains page slide basic_slide title_slide ' +\n      'title subtitle fade_in fade_out fade_at clear_color color line_color ' +\n      'line_width texture_wrap texture_transform texture scale_?x scale_?y ' +\n      'scale_?z? translate_?x translate_?y translate_?z? rotate_?x rotate_?y ' +\n      'rotate_?z? rectangle circle ellipse sphere path line_to move_to ' +\n      'quad_to curve_to theme background contents locally time mouse_?x ' +\n      'mouse_?y mouse_buttons ' +\n      BUILTIN_MODULES\n  };\n\n  const DOUBLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    illegal: '\\\\n'\n  };\n  const SINGLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    illegal: '\\\\n'\n  };\n  const LONG_TEXT = {\n    className: 'string',\n    begin: '<<',\n    end: '>>'\n  };\n  const BASED_NUMBER = {\n    className: 'number',\n    begin: '[0-9]+#[0-9A-Z_]+(\\\\.[0-9-A-Z_]+)?#?([Ee][+-]?[0-9]+)?'\n  };\n  const IMPORT = {\n    beginKeywords: 'import',\n    end: '$',\n    keywords: XL_KEYWORDS,\n    contains: [ DOUBLE_QUOTE_TEXT ]\n  };\n  const FUNCTION_DEFINITION = {\n    className: 'function',\n    begin: /[a-z][^\\n]*->/,\n    returnBegin: true,\n    end: /->/,\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        starts: {\n          endsWithParent: true,\n          keywords: XL_KEYWORDS\n        }\n      })\n    ]\n  };\n  return {\n    name: 'XL',\n    aliases: [ 'tao' ],\n    keywords: XL_KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      DOUBLE_QUOTE_TEXT,\n      SINGLE_QUOTE_TEXT,\n      LONG_TEXT,\n      FUNCTION_DEFINITION,\n      IMPORT,\n      BASED_NUMBER,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = xl;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,EAAEA,CAACC,IAAI,EAAE;EAChB,MAAMC,eAAe,GACnB,qGAAqG,GACrG,qGAAqG;EAEvG,MAAMC,WAAW,GAAG;IAClBC,QAAQ,EAAE,wBAAwB;IAClCC,OAAO,EACL,qFAAqF,GACrF,uEAAuE;IACzEC,OAAO,EACL,gBAAgB;IAClBC,QAAQ,EACN,sEAAsE,GACtE,wEAAwE,GACxE,qEAAqE,GACrE,uEAAuE,GACvE,sEAAsE,GACtE,wEAAwE,GACxE,kEAAkE,GAClE,mEAAmE,GACnE,yBAAyB,GACzBL;EACJ,CAAC;EAED,MAAMM,iBAAiB,GAAG;IACxBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRC,OAAO,EAAE;EACX,CAAC;EACD,MAAMC,iBAAiB,GAAG;IACxBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE;EACX,CAAC;EACD,MAAME,SAAS,GAAG;IAChBL,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE;EACP,CAAC;EACD,MAAMI,YAAY,GAAG;IACnBN,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMM,MAAM,GAAG;IACbC,aAAa,EAAE,QAAQ;IACvBN,GAAG,EAAE,GAAG;IACRO,QAAQ,EAAEf,WAAW;IACrBgB,QAAQ,EAAE,CAAEX,iBAAiB;EAC/B,CAAC;EACD,MAAMY,mBAAmB,GAAG;IAC1BX,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,eAAe;IACtBW,WAAW,EAAE,IAAI;IACjBV,GAAG,EAAE,IAAI;IACTQ,QAAQ,EAAE,CACRlB,IAAI,CAACqB,OAAO,CAACrB,IAAI,CAACsB,UAAU,EAAE;MAC5BC,MAAM,EAAE;QACNC,cAAc,EAAE,IAAI;QACpBP,QAAQ,EAAEf;MACZ;IACF,CAAC,CAAC;EAEN,CAAC;EACD,OAAO;IACLuB,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,CAAE,KAAK,CAAE;IAClBT,QAAQ,EAAEf,WAAW;IACrBgB,QAAQ,EAAE,CACRlB,IAAI,CAAC2B,mBAAmB,EACxB3B,IAAI,CAAC4B,oBAAoB,EACzBrB,iBAAiB,EACjBK,iBAAiB,EACjBC,SAAS,EACTM,mBAAmB,EACnBJ,MAAM,EACND,YAAY,EACZd,IAAI,CAAC6B,WAAW;EAEpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGhC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}