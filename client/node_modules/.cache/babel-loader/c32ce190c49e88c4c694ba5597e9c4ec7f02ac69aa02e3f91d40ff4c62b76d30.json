{"ast": null, "code": "'use strict';\n\nvar refractorRuby = require('./ruby.js');\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = erb;\nerb.displayName = 'erb';\nerb.aliases = [];\nfunction erb(Prism) {\n  Prism.register(refractorRuby);\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    Prism.languages.erb = {\n      delimiter: {\n        pattern: /^(\\s*)<%=?|%>(?=\\s*$)/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      ruby: {\n        pattern: /\\s*\\S[\\s\\S]*/,\n        alias: 'language-ruby',\n        inside: Prism.languages.ruby\n      }\n    };\n    Prism.hooks.add('before-tokenize', function (env) {\n      var erbPattern = /<%=?(?:[^\\r\\n]|[\\r\\n](?!=begin)|[\\r\\n]=begin\\s(?:[^\\r\\n]|[\\r\\n](?!=end))*[\\r\\n]=end)+?%>/g;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'erb', erbPattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'erb');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractor<PERSON>uby", "require", "refractorMarkupTemplating", "module", "exports", "erb", "displayName", "aliases", "Prism", "register", "languages", "delimiter", "pattern", "lookbehind", "alias", "ruby", "inside", "hooks", "add", "env", "erbPattern", "buildPlaceholders", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/erb.js"], "sourcesContent": ["'use strict'\nvar refractorRuby = require('./ruby.js')\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = erb\nerb.displayName = 'erb'\nerb.aliases = []\nfunction erb(Prism) {\n  Prism.register(refractorRuby)\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.erb = {\n      delimiter: {\n        pattern: /^(\\s*)<%=?|%>(?=\\s*$)/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      ruby: {\n        pattern: /\\s*\\S[\\s\\S]*/,\n        alias: 'language-ruby',\n        inside: Prism.languages.ruby\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var erbPattern =\n        /<%=?(?:[^\\r\\n]|[\\r\\n](?!=begin)|[\\r\\n]=begin\\s(?:[^\\r\\n]|[\\r\\n](?!=end))*[\\r\\n]=end)+?%>/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'erb',\n        erbPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'erb')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxC,IAAIC,yBAAyB,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACjEE,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACT,aAAa,CAAC;EAC7BQ,KAAK,CAACC,QAAQ,CAACP,yBAAyB,CAAC;EACxC,CAAC,UAAUM,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,GAAG,GAAG;MACpBM,SAAS,EAAE;QACTC,OAAO,EAAE,uBAAuB;QAChCC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,IAAI,EAAE;QACJH,OAAO,EAAE,cAAc;QACvBE,KAAK,EAAE,eAAe;QACtBE,MAAM,EAAER,KAAK,CAACE,SAAS,CAACK;MAC1B;IACF,CAAC;IACDP,KAAK,CAACS,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIC,UAAU,GACZ,2FAA2F;MAC7FZ,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACW,iBAAiB,CACpDF,GAAG,EACH,KAAK,EACLC,UACF,CAAC;IACH,CAAC,CAAC;IACFZ,KAAK,CAACS,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/CX,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACY,oBAAoB,CAACH,GAAG,EAAE,KAAK,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,EAAEX,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}