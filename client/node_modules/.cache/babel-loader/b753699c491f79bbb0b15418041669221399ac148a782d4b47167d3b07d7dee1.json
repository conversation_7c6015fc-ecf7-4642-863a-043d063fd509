{"ast": null, "code": "/*\nLanguage: Tagger Script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax Highlighting for the Tagger Script as used by MusicBrainz Picard.\nWebsite: https://picard.musicbrainz.org\n */\nfunction taggerscript(hljs) {\n  const COMMENT = {\n    className: 'comment',\n    begin: /\\$noop\\(/,\n    end: /\\)/,\n    contains: [{\n      begin: /\\(/,\n      end: /\\)/,\n      contains: ['self', {\n        begin: /\\\\./\n      }]\n    }],\n    relevance: 10\n  };\n  const FUNCTION = {\n    className: 'keyword',\n    begin: /\\$(?!noop)[a-zA-Z][_a-zA-Z0-9]*/,\n    end: /\\(/,\n    excludeEnd: true\n  };\n  const VARIABLE = {\n    className: 'variable',\n    begin: /%[_a-zA-Z0-9:]*/,\n    end: '%'\n  };\n  const ESCAPE_SEQUENCE = {\n    className: 'symbol',\n    begin: /\\\\./\n  };\n  return {\n    name: 'Tagger Script',\n    contains: [COMMENT, FUNCTION, VARIABLE, ESCAPE_SEQUENCE]\n  };\n}\nmodule.exports = taggerscript;", "map": {"version": 3, "names": ["taggerscript", "hljs", "COMMENT", "className", "begin", "end", "contains", "relevance", "FUNCTION", "excludeEnd", "VARIABLE", "ESCAPE_SEQUENCE", "name", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/taggerscript.js"], "sourcesContent": ["/*\nLanguage: Tagger Script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax Highlighting for the Tagger Script as used by MusicBrainz Picard.\nWebsite: https://picard.musicbrainz.org\n */\nfunction taggerscript(hljs) {\n  const COMMENT = {\n    className: 'comment',\n    begin: /\\$noop\\(/,\n    end: /\\)/,\n    contains: [ {\n      begin: /\\(/,\n      end: /\\)/,\n      contains: [ 'self',\n        {\n          begin: /\\\\./\n        } ]\n    } ],\n    relevance: 10\n  };\n\n  const FUNCTION = {\n    className: 'keyword',\n    begin: /\\$(?!noop)[a-zA-Z][_a-zA-Z0-9]*/,\n    end: /\\(/,\n    excludeEnd: true\n  };\n\n  const VARIABLE = {\n    className: 'variable',\n    begin: /%[_a-zA-Z0-9:]*/,\n    end: '%'\n  };\n\n  const ESCAPE_SEQUENCE = {\n    className: 'symbol',\n    begin: /\\\\./\n  };\n\n  return {\n    name: 'Tagger Script',\n    contains: [\n      COMMENT,\n      FUNCTION,\n      VARIABLE,\n      ESCAPE_SEQUENCE\n    ]\n  };\n}\n\nmodule.exports = taggerscript;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC1B,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAE,CAAE;MACVF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,CAAE,MAAM,EAChB;QACEF,KAAK,EAAE;MACT,CAAC;IACL,CAAC,CAAE;IACHG,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,QAAQ,GAAG;IACfL,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,iCAAiC;IACxCC,GAAG,EAAE,IAAI;IACTI,UAAU,EAAE;EACd,CAAC;EAED,MAAMC,QAAQ,GAAG;IACfP,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,GAAG,EAAE;EACP,CAAC;EAED,MAAMM,eAAe,GAAG;IACtBR,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,OAAO;IACLQ,IAAI,EAAE,eAAe;IACrBN,QAAQ,EAAE,CACRJ,OAAO,EACPM,QAAQ,EACRE,QAAQ,EACRC,eAAe;EAEnB,CAAC;AACH;AAEAE,MAAM,CAACC,OAAO,GAAGd,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}