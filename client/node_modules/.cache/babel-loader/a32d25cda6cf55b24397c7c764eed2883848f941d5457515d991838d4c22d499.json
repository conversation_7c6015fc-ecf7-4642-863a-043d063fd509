{"ast": null, "code": "'use strict';\n\nmodule.exports = r;\nr.displayName = 'r';\nr.aliases = [];\nfunction r(Prism) {\n  Prism.languages.r = {\n    comment: /#.*/,\n    string: {\n      pattern: /(['\"])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'percent-operator': {\n      // Includes user-defined operators\n      // and %%, %*%, %/%, %in%, %o%, %x%\n      pattern: /%[^%\\s]*%/,\n      alias: 'operator'\n    },\n    boolean: /\\b(?:FALSE|TRUE)\\b/,\n    ellipsis: /\\.\\.(?:\\.|\\d+)/,\n    number: [/\\b(?:Inf|NaN)\\b/, /(?:\\b0x[\\dA-Fa-f]+(?:\\.\\d*)?|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[EePp][+-]?\\d+)?[iL]?/],\n    keyword: /\\b(?:NA|NA_character_|NA_complex_|NA_integer_|NA_real_|NULL|break|else|for|function|if|in|next|repeat|while)\\b/,\n    operator: /->?>?|<(?:=|<?-)?|[>=!]=?|::?|&&?|\\|\\|?|[+*\\/^$@~]/,\n    punctuation: /[(){}\\[\\],;]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "r", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "alias", "boolean", "ellipsis", "number", "keyword", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/r.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = r\nr.displayName = 'r'\nr.aliases = []\nfunction r(Prism) {\n  Prism.languages.r = {\n    comment: /#.*/,\n    string: {\n      pattern: /(['\"])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'percent-operator': {\n      // Includes user-defined operators\n      // and %%, %*%, %/%, %in%, %o%, %x%\n      pattern: /%[^%\\s]*%/,\n      alias: 'operator'\n    },\n    boolean: /\\b(?:FALSE|TRUE)\\b/,\n    ellipsis: /\\.\\.(?:\\.|\\d+)/,\n    number: [\n      /\\b(?:Inf|NaN)\\b/,\n      /(?:\\b0x[\\dA-Fa-f]+(?:\\.\\d*)?|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[EePp][+-]?\\d+)?[iL]?/\n    ],\n    keyword:\n      /\\b(?:NA|NA_character_|NA_complex_|NA_integer_|NA_real_|NULL|break|else|for|function|if|in|next|repeat|while)\\b/,\n    operator: /->?>?|<(?:=|<?-)?|[>=!]=?|::?|&&?|\\|\\|?|[+*\\/^$@~]/,\n    punctuation: /[(){}\\[\\],;]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,CAAC;AAClBA,CAAC,CAACC,WAAW,GAAG,GAAG;AACnBD,CAAC,CAACE,OAAO,GAAG,EAAE;AACd,SAASF,CAACA,CAACG,KAAK,EAAE;EAChBA,KAAK,CAACC,SAAS,CAACJ,CAAC,GAAG;IAClBK,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;MACNC,OAAO,EAAE,kCAAkC;MAC3CC,MAAM,EAAE;IACV,CAAC;IACD,kBAAkB,EAAE;MAClB;MACA;MACAD,OAAO,EAAE,WAAW;MACpBE,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE,oBAAoB;IAC7BC,QAAQ,EAAE,gBAAgB;IAC1BC,MAAM,EAAE,CACN,iBAAiB,EACjB,+EAA+E,CAChF;IACDC,OAAO,EACL,gHAAgH;IAClHC,QAAQ,EAAE,oDAAoD;IAC9DC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}