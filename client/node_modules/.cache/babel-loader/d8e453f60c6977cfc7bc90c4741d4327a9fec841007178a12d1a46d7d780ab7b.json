{"ast": null, "code": "export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"black\",\n    \"background\": \"none\",\n    \"textShadow\": \"0 1px white\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"black\",\n    \"background\": \"#f5f2f0\",\n    \"textShadow\": \"0 1px white\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\"\n  },\n  \"pre[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"pre[class*=\\\"language-\\\"]::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"]::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"] ::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#f5f2f0\",\n    \"padding\": \".1em\",\n    \"borderRadius\": \".3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"slategray\"\n  },\n  \"prolog\": {\n    \"color\": \"slategray\"\n  },\n  \"doctype\": {\n    \"color\": \"slategray\"\n  },\n  \"cdata\": {\n    \"color\": \"slategray\"\n  },\n  \"punctuation\": {\n    \"color\": \"#999\"\n  },\n  \"namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"#905\"\n  },\n  \"tag\": {\n    \"color\": \"#905\"\n  },\n  \"boolean\": {\n    \"color\": \"#905\"\n  },\n  \"number\": {\n    \"color\": \"#905\"\n  },\n  \"constant\": {\n    \"color\": \"#905\"\n  },\n  \"symbol\": {\n    \"color\": \"#905\"\n  },\n  \"deleted\": {\n    \"color\": \"#905\"\n  },\n  \"selector\": {\n    \"color\": \"#690\"\n  },\n  \"attr-name\": {\n    \"color\": \"#690\"\n  },\n  \"string\": {\n    \"color\": \"#690\"\n  },\n  \"char\": {\n    \"color\": \"#690\"\n  },\n  \"builtin\": {\n    \"color\": \"#690\"\n  },\n  \"inserted\": {\n    \"color\": \"#690\"\n  },\n  \"operator\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \"entity\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \".style .token.string\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \"atrule\": {\n    \"color\": \"#07a\"\n  },\n  \"attr-value\": {\n    \"color\": \"#07a\"\n  },\n  \"keyword\": {\n    \"color\": \"#07a\"\n  },\n  \"function\": {\n    \"color\": \"#DD4A68\"\n  },\n  \"class-name\": {\n    \"color\": \"#DD4A68\"\n  },\n  \"regex\": {\n    \"color\": \"#e90\"\n  },\n  \"important\": {\n    \"color\": \"#e90\",\n    \"fontWeight\": \"bold\"\n  },\n  \"variable\": {\n    \"color\": \"#e90\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  }\n};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/styles/prism/prism.js"], "sourcesContent": ["export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"black\",\n    \"background\": \"none\",\n    \"textShadow\": \"0 1px white\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"black\",\n    \"background\": \"#f5f2f0\",\n    \"textShadow\": \"0 1px white\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\"\n  },\n  \"pre[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"pre[class*=\\\"language-\\\"]::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"]::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \"code[class*=\\\"language-\\\"] ::selection\": {\n    \"textShadow\": \"none\",\n    \"background\": \"#b3d4fc\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#f5f2f0\",\n    \"padding\": \".1em\",\n    \"borderRadius\": \".3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"slategray\"\n  },\n  \"prolog\": {\n    \"color\": \"slategray\"\n  },\n  \"doctype\": {\n    \"color\": \"slategray\"\n  },\n  \"cdata\": {\n    \"color\": \"slategray\"\n  },\n  \"punctuation\": {\n    \"color\": \"#999\"\n  },\n  \"namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"#905\"\n  },\n  \"tag\": {\n    \"color\": \"#905\"\n  },\n  \"boolean\": {\n    \"color\": \"#905\"\n  },\n  \"number\": {\n    \"color\": \"#905\"\n  },\n  \"constant\": {\n    \"color\": \"#905\"\n  },\n  \"symbol\": {\n    \"color\": \"#905\"\n  },\n  \"deleted\": {\n    \"color\": \"#905\"\n  },\n  \"selector\": {\n    \"color\": \"#690\"\n  },\n  \"attr-name\": {\n    \"color\": \"#690\"\n  },\n  \"string\": {\n    \"color\": \"#690\"\n  },\n  \"char\": {\n    \"color\": \"#690\"\n  },\n  \"builtin\": {\n    \"color\": \"#690\"\n  },\n  \"inserted\": {\n    \"color\": \"#690\"\n  },\n  \"operator\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \"entity\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \".style .token.string\": {\n    \"color\": \"#9a6e3a\",\n    \"background\": \"hsla(0, 0%, 100%, .5)\"\n  },\n  \"atrule\": {\n    \"color\": \"#07a\"\n  },\n  \"attr-value\": {\n    \"color\": \"#07a\"\n  },\n  \"keyword\": {\n    \"color\": \"#07a\"\n  },\n  \"function\": {\n    \"color\": \"#DD4A68\"\n  },\n  \"class-name\": {\n    \"color\": \"#DD4A68\"\n  },\n  \"regex\": {\n    \"color\": \"#e90\"\n  },\n  \"important\": {\n    \"color\": \"#e90\",\n    \"fontWeight\": \"bold\"\n  },\n  \"variable\": {\n    \"color\": \"#e90\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  }\n};"], "mappings": "AAAA,eAAe;EACb,4BAA4B,EAAE;IAC5B,OAAO,EAAE,OAAO;IAChB,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE,aAAa;IAC3B,YAAY,EAAE,2DAA2D;IACzE,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE;EACb,CAAC;EACD,2BAA2B,EAAE;IAC3B,OAAO,EAAE,OAAO;IAChB,YAAY,EAAE,SAAS;IACvB,YAAY,EAAE,aAAa;IAC3B,YAAY,EAAE,2DAA2D;IACzE,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE;EACd,CAAC;EACD,2CAA2C,EAAE;IAC3C,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,4CAA4C,EAAE;IAC5C,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,4CAA4C,EAAE;IAC5C,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,6CAA6C,EAAE;IAC7C,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,sCAAsC,EAAE;IACtC,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,uCAAuC,EAAE;IACvC,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,uCAAuC,EAAE;IACvC,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,wCAAwC,EAAE;IACxC,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE;EAChB,CAAC;EACD,wCAAwC,EAAE;IACxC,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,MAAM;IACjB,cAAc,EAAE,MAAM;IACtB,YAAY,EAAE;EAChB,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,SAAS,EAAE;EACb,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACN,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,uBAAuB;IACrC,QAAQ,EAAE;EACZ,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,6BAA6B,EAAE;IAC7B,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,sBAAsB,EAAE;IACtB,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE,MAAM;IACf,YAAY,EAAE;EAChB,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACN,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,WAAW,EAAE;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}