{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = ejs;\nejs.displayName = 'ejs';\nejs.aliases = ['eta'];\nfunction ejs(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    Prism.languages.ejs = {\n      delimiter: {\n        pattern: /^<%[-_=]?|[-_]?%>$/,\n        alias: 'punctuation'\n      },\n      comment: /^#[\\s\\S]*/,\n      'language-javascript': {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.javascript\n      }\n    };\n    Prism.hooks.add('before-tokenize', function (env) {\n      var ejsPattern = /<%(?!%)[\\s\\S]+?%>/g;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'ejs', ejsPattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'ejs');\n    });\n    Prism.languages.eta = Prism.languages.ejs;\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "ejs", "displayName", "aliases", "Prism", "register", "languages", "delimiter", "pattern", "alias", "comment", "inside", "javascript", "hooks", "add", "env", "ejsPattern", "buildPlaceholders", "tokenizePlaceholders", "eta"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/ejs.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = ejs\nejs.displayName = 'ejs'\nejs.aliases = ['eta']\nfunction ejs(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.ejs = {\n      delimiter: {\n        pattern: /^<%[-_=]?|[-_]?%>$/,\n        alias: 'punctuation'\n      },\n      comment: /^#[\\s\\S]*/,\n      'language-javascript': {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.javascript\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var ejsPattern = /<%(?!%)[\\s\\S]+?%>/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'ejs',\n        ejsPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'ejs')\n    })\n    Prism.languages.eta = Prism.languages.ejs\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AACrB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACxC,CAAC,UAAUO,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,GAAG,GAAG;MACpBM,SAAS,EAAE;QACTC,OAAO,EAAE,oBAAoB;QAC7BC,KAAK,EAAE;MACT,CAAC;MACDC,OAAO,EAAE,WAAW;MACpB,qBAAqB,EAAE;QACrBF,OAAO,EAAE,SAAS;QAClBG,MAAM,EAAEP,KAAK,CAACE,SAAS,CAACM;MAC1B;IACF,CAAC;IACDR,KAAK,CAACS,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIC,UAAU,GAAG,oBAAoB;MACrCZ,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACW,iBAAiB,CACpDF,GAAG,EACH,KAAK,EACLC,UACF,CAAC;IACH,CAAC,CAAC;IACFZ,KAAK,CAACS,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/CX,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACY,oBAAoB,CAACH,GAAG,EAAE,KAAK,CAAC;IACvE,CAAC,CAAC;IACFX,KAAK,CAACE,SAAS,CAACa,GAAG,GAAGf,KAAK,CAACE,SAAS,CAACL,GAAG;EAC3C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}