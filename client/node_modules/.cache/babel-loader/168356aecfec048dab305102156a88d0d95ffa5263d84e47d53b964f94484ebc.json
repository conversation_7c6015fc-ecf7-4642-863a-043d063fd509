{"ast": null, "code": "'use strict';\n\nmodule.exports = jsExtras;\njsExtras.displayName = 'jsExtras';\njsExtras.aliases = [];\nfunction jsExtras(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.insertBefore('javascript', 'function-variable', {\n      'method-variable': {\n        pattern: RegExp('(\\\\.\\\\s*)' + Prism.languages.javascript['function-variable'].pattern.source),\n        lookbehind: true,\n        alias: ['function-variable', 'method', 'function', 'property-access']\n      }\n    });\n    Prism.languages.insertBefore('javascript', 'function', {\n      method: {\n        pattern: RegExp('(\\\\.\\\\s*)' + Prism.languages.javascript['function'].source),\n        lookbehind: true,\n        alias: ['function', 'property-access']\n      }\n    });\n    Prism.languages.insertBefore('javascript', 'constant', {\n      'known-class-name': [{\n        // standard built-ins\n        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\n        pattern: /\\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\\b/,\n        alias: 'class-name'\n      }, {\n        // errors\n        pattern: /\\b(?:[A-Z]\\w*)Error\\b/,\n        alias: 'class-name'\n      }]\n    });\n    /**\n     * Replaces the `<ID>` placeholder in the given pattern with a pattern for general JS identifiers.\n     *\n     * @param {string} source\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function withId(source, flags) {\n      return RegExp(source.replace(/<ID>/g, function () {\n        return /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/.source;\n      }), flags);\n    }\n    Prism.languages.insertBefore('javascript', 'keyword', {\n      imports: {\n        // https://tc39.es/ecma262/#sec-imports\n        pattern: withId(/(\\bimport\\b\\s*)(?:<ID>(?:\\s*,\\s*(?:\\*\\s*as\\s+<ID>|\\{[^{}]*\\}))?|\\*\\s*as\\s+<ID>|\\{[^{}]*\\})(?=\\s*\\bfrom\\b)/.source),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      exports: {\n        // https://tc39.es/ecma262/#sec-exports\n        pattern: withId(/(\\bexport\\b\\s*)(?:\\*(?:\\s*as\\s+<ID>)?(?=\\s*\\bfrom\\b)|\\{[^{}]*\\})/.source),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }\n    });\n    Prism.languages.javascript['keyword'].unshift({\n      pattern: /\\b(?:as|default|export|from|import)\\b/,\n      alias: 'module'\n    }, {\n      pattern: /\\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\\b/,\n      alias: 'control-flow'\n    }, {\n      pattern: /\\bnull\\b/,\n      alias: ['null', 'nil']\n    }, {\n      pattern: /\\bundefined\\b/,\n      alias: 'nil'\n    });\n    Prism.languages.insertBefore('javascript', 'operator', {\n      spread: {\n        pattern: /\\.{3}/,\n        alias: 'operator'\n      },\n      arrow: {\n        pattern: /=>/,\n        alias: 'operator'\n      }\n    });\n    Prism.languages.insertBefore('javascript', 'punctuation', {\n      'property-access': {\n        pattern: withId(/(\\.\\s*)#?<ID>/.source),\n        lookbehind: true\n      },\n      'maybe-class-name': {\n        pattern: /(^|[^$\\w\\xA0-\\uFFFF])[A-Z][$\\w\\xA0-\\uFFFF]+/,\n        lookbehind: true\n      },\n      dom: {\n        // this contains only a few commonly used DOM variables\n        pattern: /\\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\\b/,\n        alias: 'variable'\n      },\n      console: {\n        pattern: /\\bconsole(?=\\s*\\.)/,\n        alias: 'class-name'\n      }\n    }); // add 'maybe-class-name' to tokens which might be a class name\n    var maybeClassNameTokens = ['function', 'function-variable', 'method', 'method-variable', 'property-access'];\n    for (var i = 0; i < maybeClassNameTokens.length; i++) {\n      var token = maybeClassNameTokens[i];\n      var value = Prism.languages.javascript[token]; // convert regex to object\n      if (Prism.util.type(value) === 'RegExp') {\n        value = Prism.languages.javascript[token] = {\n          pattern: value\n        };\n      } // keep in mind that we don't support arrays\n      var inside = value.inside || {};\n      value.inside = inside;\n      inside['maybe-class-name'] = /^[A-Z][\\s\\S]*/;\n    }\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "jsExtras", "displayName", "aliases", "Prism", "languages", "insertBefore", "pattern", "RegExp", "javascript", "source", "lookbehind", "alias", "method", "withId", "flags", "replace", "imports", "inside", "unshift", "spread", "arrow", "dom", "console", "maybeClassNameTokens", "i", "length", "token", "value", "util", "type"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/js-extras.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsExtras\njsExtras.displayName = 'jsExtras'\njsExtras.aliases = []\nfunction jsExtras(Prism) {\n  ;(function (Prism) {\n    Prism.languages.insertBefore('javascript', 'function-variable', {\n      'method-variable': {\n        pattern: RegExp(\n          '(\\\\.\\\\s*)' +\n            Prism.languages.javascript['function-variable'].pattern.source\n        ),\n        lookbehind: true,\n        alias: ['function-variable', 'method', 'function', 'property-access']\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'function', {\n      method: {\n        pattern: RegExp(\n          '(\\\\.\\\\s*)' + Prism.languages.javascript['function'].source\n        ),\n        lookbehind: true,\n        alias: ['function', 'property-access']\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'constant', {\n      'known-class-name': [\n        {\n          // standard built-ins\n          // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\n          pattern:\n            /\\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\\b/,\n          alias: 'class-name'\n        },\n        {\n          // errors\n          pattern: /\\b(?:[A-Z]\\w*)Error\\b/,\n          alias: 'class-name'\n        }\n      ]\n    })\n    /**\n     * Replaces the `<ID>` placeholder in the given pattern with a pattern for general JS identifiers.\n     *\n     * @param {string} source\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function withId(source, flags) {\n      return RegExp(\n        source.replace(/<ID>/g, function () {\n          return /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/.source\n        }),\n        flags\n      )\n    }\n    Prism.languages.insertBefore('javascript', 'keyword', {\n      imports: {\n        // https://tc39.es/ecma262/#sec-imports\n        pattern: withId(\n          /(\\bimport\\b\\s*)(?:<ID>(?:\\s*,\\s*(?:\\*\\s*as\\s+<ID>|\\{[^{}]*\\}))?|\\*\\s*as\\s+<ID>|\\{[^{}]*\\})(?=\\s*\\bfrom\\b)/\n            .source\n        ),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      exports: {\n        // https://tc39.es/ecma262/#sec-exports\n        pattern: withId(\n          /(\\bexport\\b\\s*)(?:\\*(?:\\s*as\\s+<ID>)?(?=\\s*\\bfrom\\b)|\\{[^{}]*\\})/\n            .source\n        ),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }\n    })\n    Prism.languages.javascript['keyword'].unshift(\n      {\n        pattern: /\\b(?:as|default|export|from|import)\\b/,\n        alias: 'module'\n      },\n      {\n        pattern:\n          /\\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\\b/,\n        alias: 'control-flow'\n      },\n      {\n        pattern: /\\bnull\\b/,\n        alias: ['null', 'nil']\n      },\n      {\n        pattern: /\\bundefined\\b/,\n        alias: 'nil'\n      }\n    )\n    Prism.languages.insertBefore('javascript', 'operator', {\n      spread: {\n        pattern: /\\.{3}/,\n        alias: 'operator'\n      },\n      arrow: {\n        pattern: /=>/,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'punctuation', {\n      'property-access': {\n        pattern: withId(/(\\.\\s*)#?<ID>/.source),\n        lookbehind: true\n      },\n      'maybe-class-name': {\n        pattern: /(^|[^$\\w\\xA0-\\uFFFF])[A-Z][$\\w\\xA0-\\uFFFF]+/,\n        lookbehind: true\n      },\n      dom: {\n        // this contains only a few commonly used DOM variables\n        pattern:\n          /\\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\\b/,\n        alias: 'variable'\n      },\n      console: {\n        pattern: /\\bconsole(?=\\s*\\.)/,\n        alias: 'class-name'\n      }\n    }) // add 'maybe-class-name' to tokens which might be a class name\n    var maybeClassNameTokens = [\n      'function',\n      'function-variable',\n      'method',\n      'method-variable',\n      'property-access'\n    ]\n    for (var i = 0; i < maybeClassNameTokens.length; i++) {\n      var token = maybeClassNameTokens[i]\n      var value = Prism.languages.javascript[token] // convert regex to object\n      if (Prism.util.type(value) === 'RegExp') {\n        value = Prism.languages.javascript[token] = {\n          pattern: value\n        }\n      } // keep in mind that we don't support arrays\n      var inside = value.inside || {}\n      value.inside = inside\n      inside['maybe-class-name'] = /^[A-Z][\\s\\S]*/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACC,YAAY,CAAC,YAAY,EAAE,mBAAmB,EAAE;MAC9D,iBAAiB,EAAE;QACjBC,OAAO,EAAEC,MAAM,CACb,WAAW,GACTJ,KAAK,CAACC,SAAS,CAACI,UAAU,CAAC,mBAAmB,CAAC,CAACF,OAAO,CAACG,MAC5D,CAAC;QACDC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,UAAU,EAAE,iBAAiB;MACtE;IACF,CAAC,CAAC;IACFR,KAAK,CAACC,SAAS,CAACC,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;MACrDO,MAAM,EAAE;QACNN,OAAO,EAAEC,MAAM,CACb,WAAW,GAAGJ,KAAK,CAACC,SAAS,CAACI,UAAU,CAAC,UAAU,CAAC,CAACC,MACvD,CAAC;QACDC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,CAAC,UAAU,EAAE,iBAAiB;MACvC;IACF,CAAC,CAAC;IACFR,KAAK,CAACC,SAAS,CAACC,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;MACrD,kBAAkB,EAAE,CAClB;QACE;QACA;QACAC,OAAO,EACL,wOAAwO;QAC1OK,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACAL,OAAO,EAAE,uBAAuB;QAChCK,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,CAAC;IACF;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASE,MAAMA,CAACJ,MAAM,EAAEK,KAAK,EAAE;MAC7B,OAAOP,MAAM,CACXE,MAAM,CAACM,OAAO,CAAC,OAAO,EAAE,YAAY;QAClC,OAAO,wDAAwD,CAACN,MAAM;MACxE,CAAC,CAAC,EACFK,KACF,CAAC;IACH;IACAX,KAAK,CAACC,SAAS,CAACC,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE;MACpDW,OAAO,EAAE;QACP;QACAV,OAAO,EAAEO,MAAM,CACb,2GAA2G,CACxGJ,MACL,CAAC;QACDC,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAEd,KAAK,CAACC,SAAS,CAACI;MAC1B,CAAC;MACDT,OAAO,EAAE;QACP;QACAO,OAAO,EAAEO,MAAM,CACb,kEAAkE,CAC/DJ,MACL,CAAC;QACDC,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAEd,KAAK,CAACC,SAAS,CAACI;MAC1B;IACF,CAAC,CAAC;IACFL,KAAK,CAACC,SAAS,CAACI,UAAU,CAAC,SAAS,CAAC,CAACU,OAAO,CAC3C;MACEZ,OAAO,EAAE,uCAAuC;MAChDK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EACL,+FAA+F;MACjGK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,UAAU;MACnBK,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK;IACvB,CAAC,EACD;MACEL,OAAO,EAAE,eAAe;MACxBK,KAAK,EAAE;IACT,CACF,CAAC;IACDR,KAAK,CAACC,SAAS,CAACC,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;MACrDc,MAAM,EAAE;QACNb,OAAO,EAAE,OAAO;QAChBK,KAAK,EAAE;MACT,CAAC;MACDS,KAAK,EAAE;QACLd,OAAO,EAAE,IAAI;QACbK,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFR,KAAK,CAACC,SAAS,CAACC,YAAY,CAAC,YAAY,EAAE,aAAa,EAAE;MACxD,iBAAiB,EAAE;QACjBC,OAAO,EAAEO,MAAM,CAAC,eAAe,CAACJ,MAAM,CAAC;QACvCC,UAAU,EAAE;MACd,CAAC;MACD,kBAAkB,EAAE;QAClBJ,OAAO,EAAE,6CAA6C;QACtDI,UAAU,EAAE;MACd,CAAC;MACDW,GAAG,EAAE;QACH;QACAf,OAAO,EACL,iFAAiF;QACnFK,KAAK,EAAE;MACT,CAAC;MACDW,OAAO,EAAE;QACPhB,OAAO,EAAE,oBAAoB;QAC7BK,KAAK,EAAE;MACT;IACF,CAAC,CAAC,EAAC;IACH,IAAIY,oBAAoB,GAAG,CACzB,UAAU,EACV,mBAAmB,EACnB,QAAQ,EACR,iBAAiB,EACjB,iBAAiB,CAClB;IACD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,oBAAoB,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACpD,IAAIE,KAAK,GAAGH,oBAAoB,CAACC,CAAC,CAAC;MACnC,IAAIG,KAAK,GAAGxB,KAAK,CAACC,SAAS,CAACI,UAAU,CAACkB,KAAK,CAAC,EAAC;MAC9C,IAAIvB,KAAK,CAACyB,IAAI,CAACC,IAAI,CAACF,KAAK,CAAC,KAAK,QAAQ,EAAE;QACvCA,KAAK,GAAGxB,KAAK,CAACC,SAAS,CAACI,UAAU,CAACkB,KAAK,CAAC,GAAG;UAC1CpB,OAAO,EAAEqB;QACX,CAAC;MACH,CAAC,CAAC;MACF,IAAIV,MAAM,GAAGU,KAAK,CAACV,MAAM,IAAI,CAAC,CAAC;MAC/BU,KAAK,CAACV,MAAM,GAAGA,MAAM;MACrBA,MAAM,CAAC,kBAAkB,CAAC,GAAG,eAAe;IAC9C;EACF,CAAC,EAAEd,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}