{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Visual Basic .NET\nDescription: Visual Basic .NET (VB.NET) is a multi-paradigm, object-oriented programming language, implemented on the .NET Framework.\nAuthors: <AUTHORS>\nWebsite: https://docs.microsoft.com/dotnet/visual-basic/getting-started\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction vbnet(hljs) {\n  /**\n   * Character Literal\n   * Either a single character (\"a\"C) or an escaped double quote (\"\"\"\"C).\n   */\n  const CHARACTER = {\n    className: 'string',\n    begin: /\"(\"\"|[^/n])\"C\\b/\n  };\n  const STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    illegal: /\\n/,\n    contains: [{\n      // double quote escape\n      begin: /\"\"/\n    }]\n  };\n\n  /** Date Literals consist of a date, a time, or both separated by whitespace, surrounded by # */\n  const MM_DD_YYYY = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}/;\n  const YYYY_MM_DD = /\\d{4}-\\d{1,2}-\\d{1,2}/;\n  const TIME_12H = /(\\d|1[012])(:\\d+){0,2} *(AM|PM)/;\n  const TIME_24H = /\\d{1,2}(:\\d{1,2}){1,2}/;\n  const DATE = {\n    className: 'literal',\n    variants: [{\n      // #YYYY-MM-DD# (ISO-Date) or #M/D/YYYY# (US-Date)\n      begin: concat(/# */, either(YYYY_MM_DD, MM_DD_YYYY), / *#/)\n    }, {\n      // #H:mm[:ss]# (24h Time)\n      begin: concat(/# */, TIME_24H, / *#/)\n    }, {\n      // #h[:mm[:ss]] A# (12h Time)\n      begin: concat(/# */, TIME_12H, / *#/)\n    }, {\n      // date plus time\n      begin: concat(/# */, either(YYYY_MM_DD, MM_DD_YYYY), / +/, either(TIME_12H, TIME_24H), / *#/)\n    }]\n  };\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [{\n      // Float\n      begin: /\\b\\d[\\d_]*((\\.[\\d_]+(E[+-]?[\\d_]+)?)|(E[+-]?[\\d_]+))[RFD@!#]?/\n    }, {\n      // Integer (base 10)\n      begin: /\\b\\d[\\d_]*((U?[SIL])|[%&])?/\n    }, {\n      // Integer (base 16)\n      begin: /&H[\\dA-F_]+((U?[SIL])|[%&])?/\n    }, {\n      // Integer (base 8)\n      begin: /&O[0-7_]+((U?[SIL])|[%&])?/\n    }, {\n      // Integer (base 2)\n      begin: /&B[01_]+((U?[SIL])|[%&])?/\n    }]\n  };\n  const LABEL = {\n    className: 'label',\n    begin: /^\\w+:/\n  };\n  const DOC_COMMENT = hljs.COMMENT(/'''/, /$/, {\n    contains: [{\n      className: 'doctag',\n      begin: /<\\/?/,\n      end: />/\n    }]\n  });\n  const COMMENT = hljs.COMMENT(null, /$/, {\n    variants: [{\n      begin: /'/\n    }, {\n      // TODO: Use `beforeMatch:` for leading spaces\n      begin: /([\\t ]|^)REM(?=\\s)/\n    }]\n  });\n  const DIRECTIVES = {\n    className: 'meta',\n    // TODO: Use `beforeMatch:` for indentation once available\n    begin: /[\\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword': 'const disable else elseif enable end externalsource if region then'\n    },\n    contains: [COMMENT]\n  };\n  return {\n    name: 'Visual Basic .NET',\n    aliases: ['vb'],\n    case_insensitive: true,\n    classNameAliases: {\n      label: 'symbol'\n    },\n    keywords: {\n      keyword: 'addhandler alias aggregate ansi as async assembly auto binary by byref byval ' + /* a-b */\n      'call case catch class compare const continue custom declare default delegate dim distinct do ' + /* c-d */\n      'each equals else elseif end enum erase error event exit explicit finally for friend from function ' + /* e-f */\n      'get global goto group handles if implements imports in inherits interface into iterator ' + /* g-i */\n      'join key let lib loop me mid module mustinherit mustoverride mybase myclass ' + /* j-m */\n      'namespace narrowing new next notinheritable notoverridable ' + /* n */\n      'of off on operator option optional order overloads overridable overrides ' + /* o */\n      'paramarray partial preserve private property protected public ' + /* p */\n      'raiseevent readonly redim removehandler resume return ' + /* r */\n      'select set shadows shared skip static step stop structure strict sub synclock ' + /* s */\n      'take text then throw to try unicode until using when where while widening with withevents writeonly yield' /* t-y */,\n      built_in:\n      // Operators https://docs.microsoft.com/dotnet/visual-basic/language-reference/operators\n      'addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor ' +\n      // Type Conversion Functions https://docs.microsoft.com/dotnet/visual-basic/language-reference/functions/type-conversion-functions\n      'cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort',\n      type:\n      // Data types https://docs.microsoft.com/dotnet/visual-basic/language-reference/data-types\n      'boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort',\n      literal: 'true false nothing'\n    },\n    illegal: '//|\\\\{|\\\\}|endif|gosub|variant|wend|^\\\\$ ' /* reserved deprecated keywords */,\n    contains: [CHARACTER, STRING, DATE, NUMBER, LABEL, DOC_COMMENT, COMMENT, DIRECTIVES]\n  };\n}\nmodule.exports = vbnet;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "either", "vbnet", "hljs", "CHARACTER", "className", "begin", "STRING", "end", "illegal", "contains", "MM_DD_YYYY", "YYYY_MM_DD", "TIME_12H", "TIME_24H", "DATE", "variants", "NUMBER", "relevance", "LABEL", "DOC_COMMENT", "COMMENT", "DIRECTIVES", "keywords", "name", "aliases", "case_insensitive", "classNameAliases", "label", "keyword", "built_in", "type", "literal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/vbnet.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Visual Basic .NET\nDescription: Visual Basic .NET (VB.NET) is a multi-paradigm, object-oriented programming language, implemented on the .NET Framework.\nAuthors: <AUTHORS>\nWebsite: https://docs.microsoft.com/dotnet/visual-basic/getting-started\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction vbnet(hljs) {\n  /**\n   * Character Literal\n   * Either a single character (\"a\"C) or an escaped double quote (\"\"\"\"C).\n   */\n  const CHARACTER = {\n    className: 'string',\n    begin: /\"(\"\"|[^/n])\"C\\b/\n  };\n\n  const STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    illegal: /\\n/,\n    contains: [\n      {\n        // double quote escape\n        begin: /\"\"/\n      }\n    ]\n  };\n\n  /** Date Literals consist of a date, a time, or both separated by whitespace, surrounded by # */\n  const MM_DD_YYYY = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}/;\n  const YYYY_MM_DD = /\\d{4}-\\d{1,2}-\\d{1,2}/;\n  const TIME_12H = /(\\d|1[012])(:\\d+){0,2} *(AM|PM)/;\n  const TIME_24H = /\\d{1,2}(:\\d{1,2}){1,2}/;\n  const DATE = {\n    className: 'literal',\n    variants: [\n      {\n        // #YYYY-MM-DD# (ISO-Date) or #M/D/YYYY# (US-Date)\n        begin: concat(/# */, either(YYYY_MM_DD, MM_DD_YYYY), / *#/)\n      },\n      {\n        // #H:mm[:ss]# (24h Time)\n        begin: concat(/# */, TIME_24H, / *#/)\n      },\n      {\n        // #h[:mm[:ss]] A# (12h Time)\n        begin: concat(/# */, TIME_12H, / *#/)\n      },\n      {\n        // date plus time\n        begin: concat(\n          /# */,\n          either(YYYY_MM_DD, MM_DD_YYYY),\n          / +/,\n          either(TIME_12H, TIME_24H),\n          / *#/\n        )\n      }\n    ]\n  };\n\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        // Float\n        begin: /\\b\\d[\\d_]*((\\.[\\d_]+(E[+-]?[\\d_]+)?)|(E[+-]?[\\d_]+))[RFD@!#]?/\n      },\n      {\n        // Integer (base 10)\n        begin: /\\b\\d[\\d_]*((U?[SIL])|[%&])?/\n      },\n      {\n        // Integer (base 16)\n        begin: /&H[\\dA-F_]+((U?[SIL])|[%&])?/\n      },\n      {\n        // Integer (base 8)\n        begin: /&O[0-7_]+((U?[SIL])|[%&])?/\n      },\n      {\n        // Integer (base 2)\n        begin: /&B[01_]+((U?[SIL])|[%&])?/\n      }\n    ]\n  };\n\n  const LABEL = {\n    className: 'label',\n    begin: /^\\w+:/\n  };\n\n  const DOC_COMMENT = hljs.COMMENT(/'''/, /$/, {\n    contains: [\n      {\n        className: 'doctag',\n        begin: /<\\/?/,\n        end: />/\n      }\n    ]\n  });\n\n  const COMMENT = hljs.COMMENT(null, /$/, {\n    variants: [\n      {\n        begin: /'/\n      },\n      {\n        // TODO: Use `beforeMatch:` for leading spaces\n        begin: /([\\t ]|^)REM(?=\\s)/\n      }\n    ]\n  });\n\n  const DIRECTIVES = {\n    className: 'meta',\n    // TODO: Use `beforeMatch:` for indentation once available\n    begin: /[\\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword':\n        'const disable else elseif enable end externalsource if region then'\n    },\n    contains: [ COMMENT ]\n  };\n\n  return {\n    name: 'Visual Basic .NET',\n    aliases: [ 'vb' ],\n    case_insensitive: true,\n    classNameAliases: {\n      label: 'symbol'\n    },\n    keywords: {\n      keyword:\n        'addhandler alias aggregate ansi as async assembly auto binary by byref byval ' + /* a-b */\n        'call case catch class compare const continue custom declare default delegate dim distinct do ' + /* c-d */\n        'each equals else elseif end enum erase error event exit explicit finally for friend from function ' + /* e-f */\n        'get global goto group handles if implements imports in inherits interface into iterator ' + /* g-i */\n        'join key let lib loop me mid module mustinherit mustoverride mybase myclass ' + /* j-m */\n        'namespace narrowing new next notinheritable notoverridable ' + /* n */\n        'of off on operator option optional order overloads overridable overrides ' + /* o */\n        'paramarray partial preserve private property protected public ' + /* p */\n        'raiseevent readonly redim removehandler resume return ' + /* r */\n        'select set shadows shared skip static step stop structure strict sub synclock ' + /* s */\n        'take text then throw to try unicode until using when where while widening with withevents writeonly yield' /* t-y */,\n      built_in:\n        // Operators https://docs.microsoft.com/dotnet/visual-basic/language-reference/operators\n        'addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor ' +\n        // Type Conversion Functions https://docs.microsoft.com/dotnet/visual-basic/language-reference/functions/type-conversion-functions\n        'cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort',\n      type:\n        // Data types https://docs.microsoft.com/dotnet/visual-basic/language-reference/data-types\n        'boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort',\n      literal: 'true false nothing'\n    },\n    illegal:\n      '//|\\\\{|\\\\}|endif|gosub|variant|wend|^\\\\$ ' /* reserved deprecated keywords */,\n    contains: [\n      CHARACTER,\n      STRING,\n      DATE,\n      NUMBER,\n      LABEL,\n      DOC_COMMENT,\n      COMMENT,\n      DIRECTIVES\n    ]\n  };\n}\n\nmodule.exports = vbnet;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASK,KAAKA,CAACC,IAAI,EAAE;EACnB;AACF;AACA;AACA;EACE,MAAMC,SAAS,GAAG;IAChBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,MAAM,GAAG;IACbF,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVE,GAAG,EAAE,GAAG;IACRC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACR;MACE;MACAJ,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;;EAED;EACA,MAAMK,UAAU,GAAG,yBAAyB;EAC5C,MAAMC,UAAU,GAAG,uBAAuB;EAC1C,MAAMC,QAAQ,GAAG,iCAAiC;EAClD,MAAMC,QAAQ,GAAG,wBAAwB;EACzC,MAAMC,IAAI,GAAG;IACXV,SAAS,EAAE,SAAS;IACpBW,QAAQ,EAAE,CACR;MACE;MACAV,KAAK,EAAEX,MAAM,CAAC,KAAK,EAAEM,MAAM,CAACW,UAAU,EAAED,UAAU,CAAC,EAAE,KAAK;IAC5D,CAAC,EACD;MACE;MACAL,KAAK,EAAEX,MAAM,CAAC,KAAK,EAAEmB,QAAQ,EAAE,KAAK;IACtC,CAAC,EACD;MACE;MACAR,KAAK,EAAEX,MAAM,CAAC,KAAK,EAAEkB,QAAQ,EAAE,KAAK;IACtC,CAAC,EACD;MACE;MACAP,KAAK,EAAEX,MAAM,CACX,KAAK,EACLM,MAAM,CAACW,UAAU,EAAED,UAAU,CAAC,EAC9B,IAAI,EACJV,MAAM,CAACY,QAAQ,EAAEC,QAAQ,CAAC,EAC1B,KACF;IACF,CAAC;EAEL,CAAC;EAED,MAAMG,MAAM,GAAG;IACbZ,SAAS,EAAE,QAAQ;IACnBa,SAAS,EAAE,CAAC;IACZF,QAAQ,EAAE,CACR;MACE;MACAV,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EAED,MAAMa,KAAK,GAAG;IACZd,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMc,WAAW,GAAGjB,IAAI,CAACkB,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE;IAC3CX,QAAQ,EAAE,CACR;MACEL,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MACbE,GAAG,EAAE;IACP,CAAC;EAEL,CAAC,CAAC;EAEF,MAAMa,OAAO,GAAGlB,IAAI,CAACkB,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;IACtCL,QAAQ,EAAE,CACR;MACEV,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CAAC;EAEF,MAAMgB,UAAU,GAAG;IACjBjB,SAAS,EAAE,MAAM;IACjB;IACAC,KAAK,EAAE,0EAA0E;IACjFE,GAAG,EAAE,GAAG;IACRe,QAAQ,EAAE;MACR,cAAc,EACZ;IACJ,CAAC;IACDb,QAAQ,EAAE,CAAEW,OAAO;EACrB,CAAC;EAED,OAAO;IACLG,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,CAAE,IAAI,CAAE;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE;MAChBC,KAAK,EAAE;IACT,CAAC;IACDL,QAAQ,EAAE;MACRM,OAAO,EACL,+EAA+E,GAAG;MAClF,+FAA+F,GAAG;MAClG,oGAAoG,GAAG;MACvG,0FAA0F,GAAG;MAC7F,8EAA8E,GAAG;MACjF,6DAA6D,GAAG;MAChE,2EAA2E,GAAG;MAC9E,gEAAgE,GAAG;MACnE,wDAAwD,GAAG;MAC3D,gFAAgF,GAAG;MACnF,2GAA2G,CAAC;MAC9GC,QAAQ;MACN;MACA,8IAA8I;MAC9I;MACA,8FAA8F;MAChGC,IAAI;MACF;MACA,2GAA2G;MAC7GC,OAAO,EAAE;IACX,CAAC;IACDvB,OAAO,EACL,2CAA2C,CAAC;IAC9CC,QAAQ,EAAE,CACRN,SAAS,EACTG,MAAM,EACNQ,IAAI,EACJE,MAAM,EACNE,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,UAAU;EAEd,CAAC;AACH;AAEAW,MAAM,CAACC,OAAO,GAAGhC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}