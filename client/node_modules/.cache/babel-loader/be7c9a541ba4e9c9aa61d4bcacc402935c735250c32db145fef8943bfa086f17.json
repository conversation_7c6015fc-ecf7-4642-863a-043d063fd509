{"ast": null, "code": "'use strict';\n\nmodule.exports = velocity;\nvelocity.displayName = 'velocity';\nvelocity.aliases = [];\nfunction velocity(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.velocity = Prism.languages.extend('markup', {});\n    var velocity = {\n      variable: {\n        pattern: /(^|[^\\\\](?:\\\\\\\\)*)\\$!?(?:[a-z][\\w-]*(?:\\([^)]*\\))?(?:\\.[a-z][\\w-]*(?:\\([^)]*\\))?|\\[[^\\]]+\\])*|\\{[^}]+\\})/i,\n        lookbehind: true,\n        inside: {} // See below\n      },\n      string: {\n        pattern: /\"[^\"]*\"|'[^']*'/,\n        greedy: true\n      },\n      number: /\\b\\d+\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: /[=!<>]=?|[+*/%-]|&&|\\|\\||\\.\\.|\\b(?:eq|g[et]|l[et]|n(?:e|ot))\\b/,\n      punctuation: /[(){}[\\]:,.]/\n    };\n    velocity.variable.inside = {\n      string: velocity['string'],\n      function: {\n        pattern: /([^\\w-])[a-z][\\w-]*(?=\\()/,\n        lookbehind: true\n      },\n      number: velocity['number'],\n      boolean: velocity['boolean'],\n      punctuation: velocity['punctuation']\n    };\n    Prism.languages.insertBefore('velocity', 'comment', {\n      unparsed: {\n        pattern: /(^|[^\\\\])#\\[\\[[\\s\\S]*?\\]\\]#/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          punctuation: /^#\\[\\[|\\]\\]#$/\n        }\n      },\n      'velocity-comment': [{\n        pattern: /(^|[^\\\\])#\\*[\\s\\S]*?\\*#/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'comment'\n      }, {\n        pattern: /(^|[^\\\\])##.*/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'comment'\n      }],\n      directive: {\n        pattern: /(^|[^\\\\](?:\\\\\\\\)*)#@?(?:[a-z][\\w-]*|\\{[a-z][\\w-]*\\})(?:\\s*\\((?:[^()]|\\([^()]*\\))*\\))?/i,\n        lookbehind: true,\n        inside: {\n          keyword: {\n            pattern: /^#@?(?:[a-z][\\w-]*|\\{[a-z][\\w-]*\\})|\\bin\\b/,\n            inside: {\n              punctuation: /[{}]/\n            }\n          },\n          rest: velocity\n        }\n      },\n      variable: velocity['variable']\n    });\n    Prism.languages.velocity['tag'].inside['attr-value'].inside.rest = Prism.languages.velocity;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "velocity", "displayName", "aliases", "Prism", "languages", "extend", "variable", "pattern", "lookbehind", "inside", "string", "greedy", "number", "boolean", "operator", "punctuation", "function", "insertBefore", "unparsed", "alias", "directive", "keyword", "rest"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/velocity.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = velocity\nvelocity.displayName = 'velocity'\nvelocity.aliases = []\nfunction velocity(Prism) {\n  ;(function (Prism) {\n    Prism.languages.velocity = Prism.languages.extend('markup', {})\n    var velocity = {\n      variable: {\n        pattern:\n          /(^|[^\\\\](?:\\\\\\\\)*)\\$!?(?:[a-z][\\w-]*(?:\\([^)]*\\))?(?:\\.[a-z][\\w-]*(?:\\([^)]*\\))?|\\[[^\\]]+\\])*|\\{[^}]+\\})/i,\n        lookbehind: true,\n        inside: {} // See below\n      },\n      string: {\n        pattern: /\"[^\"]*\"|'[^']*'/,\n        greedy: true\n      },\n      number: /\\b\\d+\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      operator:\n        /[=!<>]=?|[+*/%-]|&&|\\|\\||\\.\\.|\\b(?:eq|g[et]|l[et]|n(?:e|ot))\\b/,\n      punctuation: /[(){}[\\]:,.]/\n    }\n    velocity.variable.inside = {\n      string: velocity['string'],\n      function: {\n        pattern: /([^\\w-])[a-z][\\w-]*(?=\\()/,\n        lookbehind: true\n      },\n      number: velocity['number'],\n      boolean: velocity['boolean'],\n      punctuation: velocity['punctuation']\n    }\n    Prism.languages.insertBefore('velocity', 'comment', {\n      unparsed: {\n        pattern: /(^|[^\\\\])#\\[\\[[\\s\\S]*?\\]\\]#/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          punctuation: /^#\\[\\[|\\]\\]#$/\n        }\n      },\n      'velocity-comment': [\n        {\n          pattern: /(^|[^\\\\])#\\*[\\s\\S]*?\\*#/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'comment'\n        },\n        {\n          pattern: /(^|[^\\\\])##.*/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'comment'\n        }\n      ],\n      directive: {\n        pattern:\n          /(^|[^\\\\](?:\\\\\\\\)*)#@?(?:[a-z][\\w-]*|\\{[a-z][\\w-]*\\})(?:\\s*\\((?:[^()]|\\([^()]*\\))*\\))?/i,\n        lookbehind: true,\n        inside: {\n          keyword: {\n            pattern: /^#@?(?:[a-z][\\w-]*|\\{[a-z][\\w-]*\\})|\\bin\\b/,\n            inside: {\n              punctuation: /[{}]/\n            }\n          },\n          rest: velocity\n        }\n      },\n      variable: velocity['variable']\n    })\n    Prism.languages.velocity['tag'].inside['attr-value'].inside.rest =\n      Prism.languages.velocity\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC/D,IAAIL,QAAQ,GAAG;MACbM,QAAQ,EAAE;QACRC,OAAO,EACL,2GAA2G;QAC7GC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,CAAC,CAAC,CAAC;MACb,CAAC;MACDC,MAAM,EAAE;QACNH,OAAO,EAAE,iBAAiB;QAC1BI,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EACN,gEAAgE;MAClEC,WAAW,EAAE;IACf,CAAC;IACDf,QAAQ,CAACM,QAAQ,CAACG,MAAM,GAAG;MACzBC,MAAM,EAAEV,QAAQ,CAAC,QAAQ,CAAC;MAC1BgB,QAAQ,EAAE;QACRT,OAAO,EAAE,2BAA2B;QACpCC,UAAU,EAAE;MACd,CAAC;MACDI,MAAM,EAAEZ,QAAQ,CAAC,QAAQ,CAAC;MAC1Ba,OAAO,EAAEb,QAAQ,CAAC,SAAS,CAAC;MAC5Be,WAAW,EAAEf,QAAQ,CAAC,aAAa;IACrC,CAAC;IACDG,KAAK,CAACC,SAAS,CAACa,YAAY,CAAC,UAAU,EAAE,SAAS,EAAE;MAClDC,QAAQ,EAAE;QACRX,OAAO,EAAE,6BAA6B;QACtCC,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE,IAAI;QACZF,MAAM,EAAE;UACNM,WAAW,EAAE;QACf;MACF,CAAC;MACD,kBAAkB,EAAE,CAClB;QACER,OAAO,EAAE,yBAAyB;QAClCC,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE,IAAI;QACZQ,KAAK,EAAE;MACT,CAAC,EACD;QACEZ,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE,IAAI;QACZQ,KAAK,EAAE;MACT,CAAC,CACF;MACDC,SAAS,EAAE;QACTb,OAAO,EACL,wFAAwF;QAC1FC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNY,OAAO,EAAE;YACPd,OAAO,EAAE,4CAA4C;YACrDE,MAAM,EAAE;cACNM,WAAW,EAAE;YACf;UACF,CAAC;UACDO,IAAI,EAAEtB;QACR;MACF,CAAC;MACDM,QAAQ,EAAEN,QAAQ,CAAC,UAAU;IAC/B,CAAC,CAAC;IACFG,KAAK,CAACC,SAAS,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAACS,MAAM,CAAC,YAAY,CAAC,CAACA,MAAM,CAACa,IAAI,GAC9DnB,KAAK,CAACC,SAAS,CAACJ,QAAQ;EAC5B,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}