{"ast": null, "code": "/*\nLanguage: Extended Backus-Naur Form\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Extended_Backus–Naur_form\n*/\n\n/** @type LanguageFn */\nfunction ebnf(hljs) {\n  const commentMode = hljs.COMMENT(/\\(\\*/, /\\*\\)/);\n  const nonTerminalMode = {\n    className: \"attribute\",\n    begin: /^[ ]*[a-zA-Z]+([\\s_-]+[a-zA-Z]+)*/\n  };\n  const specialSequenceMode = {\n    className: \"meta\",\n    begin: /\\?.*\\?/\n  };\n  const ruleBodyMode = {\n    begin: /=/,\n    end: /[.;]/,\n    contains: [commentMode, specialSequenceMode, {\n      // terminals\n      className: 'string',\n      variants: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n        begin: '`',\n        end: '`'\n      }]\n    }]\n  };\n  return {\n    name: 'Extended Backus-Naur Form',\n    illegal: /\\S/,\n    contains: [commentMode, nonTerminalMode, ruleBodyMode]\n  };\n}\nmodule.exports = ebnf;", "map": {"version": 3, "names": ["ebnf", "hljs", "commentMode", "COMMENT", "nonTerminalMode", "className", "begin", "specialSequenceMode", "ruleBodyMode", "end", "contains", "variants", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "name", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/ebnf.js"], "sourcesContent": ["/*\nLanguage: Extended Backus-Naur Form\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Extended_Backus–Naur_form\n*/\n\n/** @type LanguageFn */\nfunction ebnf(hljs) {\n  const commentMode = hljs.COMMENT(/\\(\\*/, /\\*\\)/);\n\n  const nonTerminalMode = {\n    className: \"attribute\",\n    begin: /^[ ]*[a-zA-Z]+([\\s_-]+[a-zA-Z]+)*/\n  };\n\n  const specialSequenceMode = {\n    className: \"meta\",\n    begin: /\\?.*\\?/\n  };\n\n  const ruleBodyMode = {\n    begin: /=/,\n    end: /[.;]/,\n    contains: [\n      commentMode,\n      specialSequenceMode,\n      {\n        // terminals\n        className: 'string',\n        variants: [\n          hljs.APOS_STRING_MODE,\n          hljs.QUOTE_STRING_MODE,\n          {\n            begin: '`',\n            end: '`'\n          }\n        ]\n      }\n    ]\n  };\n\n  return {\n    name: 'Extended Backus-Naur Form',\n    illegal: /\\S/,\n    contains: [\n      commentMode,\n      nonTerminalMode,\n      ruleBodyMode\n    ]\n  };\n}\n\nmodule.exports = ebnf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,WAAW,GAAGD,IAAI,CAACE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;EAEhD,MAAMC,eAAe,GAAG;IACtBC,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,mBAAmB,GAAG;IAC1BF,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,MAAME,YAAY,GAAG;IACnBF,KAAK,EAAE,GAAG;IACVG,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE,CACRR,WAAW,EACXK,mBAAmB,EACnB;MACE;MACAF,SAAS,EAAE,QAAQ;MACnBM,QAAQ,EAAE,CACRV,IAAI,CAACW,gBAAgB,EACrBX,IAAI,CAACY,iBAAiB,EACtB;QACEP,KAAK,EAAE,GAAG;QACVG,GAAG,EAAE;MACP,CAAC;IAEL,CAAC;EAEL,CAAC;EAED,OAAO;IACLK,IAAI,EAAE,2BAA2B;IACjCC,OAAO,EAAE,IAAI;IACbL,QAAQ,EAAE,CACRR,WAAW,EACXE,eAAe,EACfI,YAAY;EAEhB,CAAC;AACH;AAEAQ,MAAM,CAACC,OAAO,GAAGjB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}