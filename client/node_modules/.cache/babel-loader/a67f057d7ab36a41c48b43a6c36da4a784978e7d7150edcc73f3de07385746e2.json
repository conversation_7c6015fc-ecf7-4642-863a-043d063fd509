{"ast": null, "code": "'use strict';\n\nmodule.exports = oz;\noz.displayName = 'oz';\noz.aliases = [];\nfunction oz(Prism) {\n  Prism.languages.oz = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|%.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"/,\n      greedy: true\n    },\n    atom: {\n      pattern: /'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      alias: 'builtin'\n    },\n    keyword: /\\$|\\[\\]|\\b(?:_|at|attr|case|catch|choice|class|cond|declare|define|dis|else(?:case|if)?|end|export|fail|false|feat|finally|from|fun|functor|if|import|in|local|lock|meth|nil|not|of|or|prepare|proc|prop|raise|require|self|skip|then|thread|true|try|unit)\\b/,\n    function: [/\\b[a-z][A-Za-z\\d]*(?=\\()/, {\n      pattern: /(\\{)[A-Z][A-Za-z\\d]*\\b/,\n      lookbehind: true\n    }],\n    number: /\\b(?:0[bx][\\da-f]+|\\d+(?:\\.\\d*)?(?:e~?\\d+)?)\\b|&(?:[^\\\\]|\\\\(?:\\d{3}|.))/i,\n    variable: /`(?:[^`\\\\]|\\\\.)+`/,\n    'attr-name': /\\b\\w+(?=[ \\t]*:(?![:=]))/,\n    operator: /:(?:=|::?)|<[-:=]?|=(?:=|<?:?)|>=?:?|\\\\=:?|!!?|[|#+\\-*\\/,~^@]|\\b(?:andthen|div|mod|orelse)\\b/,\n    punctuation: /[\\[\\](){}.:;?]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "oz", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "atom", "alias", "keyword", "function", "lookbehind", "number", "variable", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/oz.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = oz\noz.displayName = 'oz'\noz.aliases = []\nfunction oz(Prism) {\n  Prism.languages.oz = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|%.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"/,\n      greedy: true\n    },\n    atom: {\n      pattern: /'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      alias: 'builtin'\n    },\n    keyword:\n      /\\$|\\[\\]|\\b(?:_|at|attr|case|catch|choice|class|cond|declare|define|dis|else(?:case|if)?|end|export|fail|false|feat|finally|from|fun|functor|if|import|in|local|lock|meth|nil|not|of|or|prepare|proc|prop|raise|require|self|skip|then|thread|true|try|unit)\\b/,\n    function: [\n      /\\b[a-z][A-Za-z\\d]*(?=\\()/,\n      {\n        pattern: /(\\{)[A-Z][A-Za-z\\d]*\\b/,\n        lookbehind: true\n      }\n    ],\n    number:\n      /\\b(?:0[bx][\\da-f]+|\\d+(?:\\.\\d*)?(?:e~?\\d+)?)\\b|&(?:[^\\\\]|\\\\(?:\\d{3}|.))/i,\n    variable: /`(?:[^`\\\\]|\\\\.)+`/,\n    'attr-name': /\\b\\w+(?=[ \\t]*:(?![:=]))/,\n    operator:\n      /:(?:=|::?)|<[-:=]?|=(?:=|<?:?)|>=?:?|\\\\=:?|!!?|[|#+\\-*\\/,~^@]|\\b(?:andthen|div|mod|orelse)\\b/,\n    punctuation: /[\\[\\](){}.:;?]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,EAAE;AACnBA,EAAE,CAACC,WAAW,GAAG,IAAI;AACrBD,EAAE,CAACE,OAAO,GAAG,EAAE;AACf,SAASF,EAAEA,CAACG,KAAK,EAAE;EACjBA,KAAK,CAACC,SAAS,CAACJ,EAAE,GAAG;IACnBK,OAAO,EAAE;MACPC,OAAO,EAAE,sBAAsB;MAC/BC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,wBAAwB;MACjCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJH,OAAO,EAAE,wBAAwB;MACjCC,MAAM,EAAE,IAAI;MACZG,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EACL,+PAA+P;IACjQC,QAAQ,EAAE,CACR,0BAA0B,EAC1B;MACEN,OAAO,EAAE,wBAAwB;MACjCO,UAAU,EAAE;IACd,CAAC,CACF;IACDC,MAAM,EACJ,0EAA0E;IAC5EC,QAAQ,EAAE,mBAAmB;IAC7B,WAAW,EAAE,0BAA0B;IACvCC,QAAQ,EACN,8FAA8F;IAChGC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}