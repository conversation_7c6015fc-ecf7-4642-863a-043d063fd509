{"ast": null, "code": "'use strict';\n\nmodule.exports = jsstacktrace;\njsstacktrace.displayName = 'jsstacktrace';\njsstacktrace.aliases = [];\nfunction jsstacktrace(Prism) {\n  Prism.languages.jsstacktrace = {\n    'error-message': {\n      pattern: /^\\S.*/m,\n      alias: 'string'\n    },\n    'stack-frame': {\n      pattern: /(^[ \\t]+)at[ \\t].*/m,\n      lookbehind: true,\n      inside: {\n        'not-my-code': {\n          pattern: /^at[ \\t]+(?!\\s)(?:node\\.js|<unknown>|.*(?:node_modules|\\(<anonymous>\\)|\\(<unknown>|<anonymous>$|\\(internal\\/|\\(node\\.js)).*/m,\n          alias: 'comment'\n        },\n        filename: {\n          pattern: /(\\bat\\s+(?!\\s)|\\()(?:[a-zA-Z]:)?[^():]+(?=:)/,\n          lookbehind: true,\n          alias: 'url'\n        },\n        function: {\n          pattern: /(\\bat\\s+(?:new\\s+)?)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF<][.$\\w\\xA0-\\uFFFF<>]*/,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /[()]/,\n        keyword: /\\b(?:at|new)\\b/,\n        alias: {\n          pattern: /\\[(?:as\\s+)?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF][$\\w\\xA0-\\uFFFF]*\\]/,\n          alias: 'variable'\n        },\n        'line-number': {\n          pattern: /:\\d+(?::\\d+)?\\b/,\n          alias: 'number',\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "jsstacktrace", "displayName", "aliases", "Prism", "languages", "pattern", "alias", "lookbehind", "inside", "filename", "function", "punctuation", "keyword"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/jsstacktrace.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsstacktrace\njsstacktrace.displayName = 'jsstacktrace'\njsstacktrace.aliases = []\nfunction jsstacktrace(Prism) {\n  Prism.languages.jsstacktrace = {\n    'error-message': {\n      pattern: /^\\S.*/m,\n      alias: 'string'\n    },\n    'stack-frame': {\n      pattern: /(^[ \\t]+)at[ \\t].*/m,\n      lookbehind: true,\n      inside: {\n        'not-my-code': {\n          pattern:\n            /^at[ \\t]+(?!\\s)(?:node\\.js|<unknown>|.*(?:node_modules|\\(<anonymous>\\)|\\(<unknown>|<anonymous>$|\\(internal\\/|\\(node\\.js)).*/m,\n          alias: 'comment'\n        },\n        filename: {\n          pattern: /(\\bat\\s+(?!\\s)|\\()(?:[a-zA-Z]:)?[^():]+(?=:)/,\n          lookbehind: true,\n          alias: 'url'\n        },\n        function: {\n          pattern:\n            /(\\bat\\s+(?:new\\s+)?)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF<][.$\\w\\xA0-\\uFFFF<>]*/,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /[()]/,\n        keyword: /\\b(?:at|new)\\b/,\n        alias: {\n          pattern: /\\[(?:as\\s+)?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF][$\\w\\xA0-\\uFFFF]*\\]/,\n          alias: 'variable'\n        },\n        'line-number': {\n          pattern: /:\\d+(?::\\d+)?\\b/,\n          alias: 'number',\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3BA,KAAK,CAACC,SAAS,CAACJ,YAAY,GAAG;IAC7B,eAAe,EAAE;MACfK,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE;IACT,CAAC;IACD,aAAa,EAAE;MACbD,OAAO,EAAE,qBAAqB;MAC9BE,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACN,aAAa,EAAE;UACbH,OAAO,EACL,8HAA8H;UAChIC,KAAK,EAAE;QACT,CAAC;QACDG,QAAQ,EAAE;UACRJ,OAAO,EAAE,8CAA8C;UACvDE,UAAU,EAAE,IAAI;UAChBD,KAAK,EAAE;QACT,CAAC;QACDI,QAAQ,EAAE;UACRL,OAAO,EACL,sEAAsE;UACxEE,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNG,WAAW,EAAE;UACf;QACF,CAAC;QACDA,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,gBAAgB;QACzBN,KAAK,EAAE;UACLD,OAAO,EAAE,4DAA4D;UACrEC,KAAK,EAAE;QACT,CAAC;QACD,aAAa,EAAE;UACbD,OAAO,EAAE,iBAAiB;UAC1BC,KAAK,EAAE,QAAQ;UACfE,MAAM,EAAE;YACNG,WAAW,EAAE;UACf;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}