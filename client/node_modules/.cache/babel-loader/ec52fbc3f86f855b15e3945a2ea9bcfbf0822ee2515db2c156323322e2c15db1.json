{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/src/components/FileModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiX, FiCopy, FiDownload, FiMaximize2, FiMinimize2 } from 'react-icons/fi';\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';\nimport { utils } from '../services/api';\nimport './FileModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileModal = ({\n  file,\n  onClose\n}) => {\n  _s();\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [copySuccess, setCopySuccess] = useState(false);\n  const [viewMode, setViewMode] = useState('formatted'); // 'formatted' | 'raw'\n\n  // 检测文本类型\n  const textType = utils.detectTextType(file.content);\n\n  // 处理ESC键关闭\n  useEffect(() => {\n    const handleEscape = event => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [onClose]);\n\n  // 复制到剪贴板\n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(file.content);\n      setCopySuccess(true);\n      setTimeout(() => setCopySuccess(false), 2000);\n    } catch (error) {\n      console.error('Failed to copy:', error);\n    }\n  };\n\n  // 下载文件\n  const handleDownload = () => {\n    const blob = new Blob([file.content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = file.filename;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  // 切换全屏\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // 获取语言类型（用于语法高亮）\n  const getLanguage = () => {\n    switch (textType) {\n      case 'json':\n        return 'json';\n      case 'code':\n        // 简单的语言检测\n        if (file.content.includes('function') || file.content.includes('const ') || file.content.includes('let ')) {\n          return 'javascript';\n        }\n        if (file.content.includes('def ') || file.content.includes('import ')) {\n          return 'python';\n        }\n        if (file.content.includes('<html') || file.content.includes('<div')) {\n          return 'html';\n        }\n        return 'javascript';\n      case 'markdown':\n        return 'markdown';\n      default:\n        return 'text';\n    }\n  };\n\n  // 渲染内容\n  const renderContent = () => {\n    if (viewMode === 'raw') {\n      return /*#__PURE__*/_jsxDEV(\"pre\", {\n        className: \"raw-content\",\n        children: file.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 格式化显示\n    if (textType === 'code' || textType === 'json') {\n      return /*#__PURE__*/_jsxDEV(SyntaxHighlighter, {\n        language: getLanguage(),\n        style: vscDarkPlus,\n        showLineNumbers: true,\n        wrapLines: true,\n        customStyle: {\n          margin: 0,\n          background: 'transparent',\n          fontSize: '14px',\n          lineHeight: '1.5'\n        },\n        children: file.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 普通文本\n    return /*#__PURE__*/_jsxDEV(\"pre\", {\n      className: \"formatted-content\",\n      children: file.content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `file-modal-overlay ${isFullscreen ? 'fullscreen' : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"file-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"file-icon\",\n            children: textType === 'json' ? '📋' : textType === 'code' ? '💻' : textType === 'markdown' ? '📝' : '📄'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: file.filename\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: file.size\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2022\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [file.lines, \" lines\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2022\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: utils.formatTimestamp(file.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"view-mode-toggle\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `toggle-button ${viewMode === 'formatted' ? 'active' : ''}`,\n              onClick: () => setViewMode('formatted'),\n              title: \"Formatted view\",\n              children: \"Formatted\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `toggle-button ${viewMode === 'raw' ? 'active' : ''}`,\n              onClick: () => setViewMode('raw'),\n              title: \"Raw view\",\n              children: \"Raw\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button\",\n            onClick: handleCopy,\n            title: \"Copy to clipboard\",\n            children: [/*#__PURE__*/_jsxDEV(FiCopy, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), copySuccess && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"copy-success\",\n              children: \"Copied!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button\",\n            onClick: handleDownload,\n            title: \"Download file\",\n            children: /*#__PURE__*/_jsxDEV(FiDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button\",\n            onClick: toggleFullscreen,\n            title: isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\",\n            children: isFullscreen ? /*#__PURE__*/_jsxDEV(FiMinimize2, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(FiMaximize2, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button close-button\",\n            onClick: onClose,\n            title: \"Close\",\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-wrapper\",\n          children: renderContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Type: \", textType.toUpperCase()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Characters: \", file.content.length.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Words: \", file.content.split(/\\s+/).length.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"formatting-note\",\n            children: viewMode === 'formatted' ? 'Formatting may be inconsistent from source' : 'Raw text view'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(FileModal, \"WDWY7BAG7ymH7x6ITZnpMVbTCTI=\");\n_c = FileModal;\nexport default FileModal;\nvar _c;\n$RefreshReg$(_c, \"FileModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiX", "FiCopy", "FiDownload", "FiMaximize2", "FiMinimize2", "Prism", "Syntax<PERSON><PERSON><PERSON><PERSON>", "vscDarkPlus", "utils", "jsxDEV", "_jsxDEV", "FileModal", "file", "onClose", "_s", "isFullscreen", "setIsFullscreen", "copySuccess", "setCopySuccess", "viewMode", "setViewMode", "textType", "detectTextType", "content", "handleEscape", "event", "key", "document", "addEventListener", "removeEventListener", "handleCopy", "navigator", "clipboard", "writeText", "setTimeout", "error", "console", "handleDownload", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "createElement", "href", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "toggleFullscreen", "getLanguage", "includes", "renderContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "language", "style", "showLineNumbers", "wrapLines", "customStyle", "margin", "background", "fontSize", "lineHeight", "size", "lines", "formatTimestamp", "timestamp", "onClick", "title", "toUpperCase", "length", "toLocaleString", "split", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/src/components/FileModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FiX, FiCopy, FiDownload, FiMaximize2, FiMinimize2 } from 'react-icons/fi';\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';\nimport { utils } from '../services/api';\nimport './FileModal.css';\n\nconst FileModal = ({ file, onClose }) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [copySuccess, setCopySuccess] = useState(false);\n  const [viewMode, setViewMode] = useState('formatted'); // 'formatted' | 'raw'\n\n  // 检测文本类型\n  const textType = utils.detectTextType(file.content);\n\n  // 处理ESC键关闭\n  useEffect(() => {\n    const handleEscape = (event) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [onClose]);\n\n  // 复制到剪贴板\n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(file.content);\n      setCopySuccess(true);\n      setTimeout(() => setCopySuccess(false), 2000);\n    } catch (error) {\n      console.error('Failed to copy:', error);\n    }\n  };\n\n  // 下载文件\n  const handleDownload = () => {\n    const blob = new Blob([file.content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = file.filename;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  // 切换全屏\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // 获取语言类型（用于语法高亮）\n  const getLanguage = () => {\n    switch (textType) {\n      case 'json':\n        return 'json';\n      case 'code':\n        // 简单的语言检测\n        if (file.content.includes('function') || file.content.includes('const ') || file.content.includes('let ')) {\n          return 'javascript';\n        }\n        if (file.content.includes('def ') || file.content.includes('import ')) {\n          return 'python';\n        }\n        if (file.content.includes('<html') || file.content.includes('<div')) {\n          return 'html';\n        }\n        return 'javascript';\n      case 'markdown':\n        return 'markdown';\n      default:\n        return 'text';\n    }\n  };\n\n  // 渲染内容\n  const renderContent = () => {\n    if (viewMode === 'raw') {\n      return (\n        <pre className=\"raw-content\">\n          {file.content}\n        </pre>\n      );\n    }\n\n    // 格式化显示\n    if (textType === 'code' || textType === 'json') {\n      return (\n        <SyntaxHighlighter\n          language={getLanguage()}\n          style={vscDarkPlus}\n          showLineNumbers={true}\n          wrapLines={true}\n          customStyle={{\n            margin: 0,\n            background: 'transparent',\n            fontSize: '14px',\n            lineHeight: '1.5'\n          }}\n        >\n          {file.content}\n        </SyntaxHighlighter>\n      );\n    }\n\n    // 普通文本\n    return (\n      <pre className=\"formatted-content\">\n        {file.content}\n      </pre>\n    );\n  };\n\n  return (\n    <div className={`file-modal-overlay ${isFullscreen ? 'fullscreen' : ''}`}>\n      <div className=\"file-modal\">\n        {/* 模态框头部 */}\n        <div className=\"modal-header\">\n          <div className=\"modal-title\">\n            <span className=\"file-icon\">\n              {textType === 'json' ? '📋' : textType === 'code' ? '💻' : textType === 'markdown' ? '📝' : '📄'}\n            </span>\n            <div className=\"title-info\">\n              <h3>{file.filename}</h3>\n              <div className=\"file-meta\">\n                <span>{file.size}</span>\n                <span>•</span>\n                <span>{file.lines} lines</span>\n                <span>•</span>\n                <span>{utils.formatTimestamp(file.timestamp)}</span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"modal-actions\">\n            {/* 视图模式切换 */}\n            <div className=\"view-mode-toggle\">\n              <button\n                className={`toggle-button ${viewMode === 'formatted' ? 'active' : ''}`}\n                onClick={() => setViewMode('formatted')}\n                title=\"Formatted view\"\n              >\n                Formatted\n              </button>\n              <button\n                className={`toggle-button ${viewMode === 'raw' ? 'active' : ''}`}\n                onClick={() => setViewMode('raw')}\n                title=\"Raw view\"\n              >\n                Raw\n              </button>\n            </div>\n            \n            {/* 操作按钮 */}\n            <button\n              className=\"action-button\"\n              onClick={handleCopy}\n              title=\"Copy to clipboard\"\n            >\n              <FiCopy />\n              {copySuccess && <span className=\"copy-success\">Copied!</span>}\n            </button>\n            \n            <button\n              className=\"action-button\"\n              onClick={handleDownload}\n              title=\"Download file\"\n            >\n              <FiDownload />\n            </button>\n            \n            <button\n              className=\"action-button\"\n              onClick={toggleFullscreen}\n              title={isFullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n            >\n              {isFullscreen ? <FiMinimize2 /> : <FiMaximize2 />}\n            </button>\n            \n            <button\n              className=\"action-button close-button\"\n              onClick={onClose}\n              title=\"Close\"\n            >\n              <FiX />\n            </button>\n          </div>\n        </div>\n\n        {/* 模态框内容 */}\n        <div className=\"modal-content\">\n          <div className=\"content-wrapper\">\n            {renderContent()}\n          </div>\n        </div>\n\n        {/* 模态框底部 */}\n        <div className=\"modal-footer\">\n          <div className=\"footer-info\">\n            <span>Type: {textType.toUpperCase()}</span>\n            <span>•</span>\n            <span>Characters: {file.content.length.toLocaleString()}</span>\n            <span>•</span>\n            <span>Words: {file.content.split(/\\s+/).length.toLocaleString()}</span>\n          </div>\n          \n          <div className=\"footer-actions\">\n            <span className=\"formatting-note\">\n              {viewMode === 'formatted' ? 'Formatting may be inconsistent from source' : 'Raw text view'}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FileModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,QAAQ,gBAAgB;AAClF,SAASC,KAAK,IAAIC,iBAAiB,QAAQ,0BAA0B;AACrE,SAASC,WAAW,QAAQ,gDAAgD;AAC5E,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAMuB,QAAQ,GAAGb,KAAK,CAACc,cAAc,CAACV,IAAI,CAACW,OAAO,CAAC;;EAEnD;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMyB,YAAY,GAAIC,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;QAC1Bb,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAEDc,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;IAClD,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEL,YAAY,CAAC;EACpE,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMiB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACrB,IAAI,CAACW,OAAO,CAAC;MACjDL,cAAc,CAAC,IAAI,CAAC;MACpBgB,UAAU,CAAC,MAAMhB,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC/C,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC3B,IAAI,CAACW,OAAO,CAAC,EAAE;MAAEiB,IAAI,EAAE;IAAa,CAAC,CAAC;IAC7D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,GAAG,CAAC;IACrCD,CAAC,CAACE,IAAI,GAAGL,GAAG;IACZG,CAAC,CAACG,QAAQ,GAAGnC,IAAI,CAACoC,QAAQ;IAC1BrB,QAAQ,CAACsB,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTxB,QAAQ,CAACsB,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtC,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACA,MAAMwC,WAAW,GAAGA,CAAA,KAAM;IACxB,QAAQlC,QAAQ;MACd,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,MAAM;QACT;QACA,IAAIT,IAAI,CAACW,OAAO,CAACiC,QAAQ,CAAC,UAAU,CAAC,IAAI5C,IAAI,CAACW,OAAO,CAACiC,QAAQ,CAAC,QAAQ,CAAC,IAAI5C,IAAI,CAACW,OAAO,CAACiC,QAAQ,CAAC,MAAM,CAAC,EAAE;UACzG,OAAO,YAAY;QACrB;QACA,IAAI5C,IAAI,CAACW,OAAO,CAACiC,QAAQ,CAAC,MAAM,CAAC,IAAI5C,IAAI,CAACW,OAAO,CAACiC,QAAQ,CAAC,SAAS,CAAC,EAAE;UACrE,OAAO,QAAQ;QACjB;QACA,IAAI5C,IAAI,CAACW,OAAO,CAACiC,QAAQ,CAAC,OAAO,CAAC,IAAI5C,IAAI,CAACW,OAAO,CAACiC,QAAQ,CAAC,MAAM,CAAC,EAAE;UACnE,OAAO,MAAM;QACf;QACA,OAAO,YAAY;MACrB,KAAK,UAAU;QACb,OAAO,UAAU;MACnB;QACE,OAAO,MAAM;IACjB;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAItC,QAAQ,KAAK,KAAK,EAAE;MACtB,oBACET,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB/C,IAAI,CAACW;MAAO;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEV;;IAEA;IACA,IAAI1C,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,MAAM,EAAE;MAC9C,oBACEX,OAAA,CAACJ,iBAAiB;QAChB0D,QAAQ,EAAET,WAAW,CAAC,CAAE;QACxBU,KAAK,EAAE1D,WAAY;QACnB2D,eAAe,EAAE,IAAK;QACtBC,SAAS,EAAE,IAAK;QAChBC,WAAW,EAAE;UACXC,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,aAAa;UACzBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAb,QAAA,EAED/C,IAAI,CAACW;MAAO;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAExB;;IAEA;IACA,oBACErD,OAAA;MAAKgD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC/B/C,IAAI,CAACW;IAAO;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV,CAAC;EAED,oBACErD,OAAA;IAAKgD,SAAS,EAAE,sBAAsB3C,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;IAAA4C,QAAA,eACvEjD,OAAA;MAAKgD,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAEzBjD,OAAA;QAAKgD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjD,OAAA;UAAKgD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjD,OAAA;YAAMgD,SAAS,EAAC,WAAW;YAAAC,QAAA,EACxBtC,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAG;UAAI;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eACPrD,OAAA;YAAKgD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjD,OAAA;cAAAiD,QAAA,EAAK/C,IAAI,CAACoC;YAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBrD,OAAA;cAAKgD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjD,OAAA;gBAAAiD,QAAA,EAAO/C,IAAI,CAAC6D;cAAI;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBrD,OAAA;gBAAAiD,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdrD,OAAA;gBAAAiD,QAAA,GAAO/C,IAAI,CAAC8D,KAAK,EAAC,QAAM;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BrD,OAAA;gBAAAiD,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdrD,OAAA;gBAAAiD,QAAA,EAAOnD,KAAK,CAACmE,eAAe,CAAC/D,IAAI,CAACgE,SAAS;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrD,OAAA;UAAKgD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAE5BjD,OAAA;YAAKgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BjD,OAAA;cACEgD,SAAS,EAAE,iBAAiBvC,QAAQ,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;cACvE0D,OAAO,EAAEA,CAAA,KAAMzD,WAAW,CAAC,WAAW,CAAE;cACxC0D,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrD,OAAA;cACEgD,SAAS,EAAE,iBAAiBvC,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;cACjE0D,OAAO,EAAEA,CAAA,KAAMzD,WAAW,CAAC,KAAK,CAAE;cAClC0D,KAAK,EAAC,UAAU;cAAAnB,QAAA,EACjB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrD,OAAA;YACEgD,SAAS,EAAC,eAAe;YACzBmB,OAAO,EAAE/C,UAAW;YACpBgD,KAAK,EAAC,mBAAmB;YAAAnB,QAAA,gBAEzBjD,OAAA,CAACT,MAAM;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACT9C,WAAW,iBAAIP,OAAA;cAAMgD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAETrD,OAAA;YACEgD,SAAS,EAAC,eAAe;YACzBmB,OAAO,EAAExC,cAAe;YACxByC,KAAK,EAAC,eAAe;YAAAnB,QAAA,eAErBjD,OAAA,CAACR,UAAU;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAETrD,OAAA;YACEgD,SAAS,EAAC,eAAe;YACzBmB,OAAO,EAAEvB,gBAAiB;YAC1BwB,KAAK,EAAE/D,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;YAAA4C,QAAA,EAE5D5C,YAAY,gBAAGL,OAAA,CAACN,WAAW;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrD,OAAA,CAACP,WAAW;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAETrD,OAAA;YACEgD,SAAS,EAAC,4BAA4B;YACtCmB,OAAO,EAAEhE,OAAQ;YACjBiE,KAAK,EAAC,OAAO;YAAAnB,QAAA,eAEbjD,OAAA,CAACV,GAAG;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrD,OAAA;QAAKgD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BjD,OAAA;UAAKgD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BF,aAAa,CAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrD,OAAA;QAAKgD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjD,OAAA;UAAKgD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjD,OAAA;YAAAiD,QAAA,GAAM,QAAM,EAACtC,QAAQ,CAAC0D,WAAW,CAAC,CAAC;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CrD,OAAA;YAAAiD,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACdrD,OAAA;YAAAiD,QAAA,GAAM,cAAY,EAAC/C,IAAI,CAACW,OAAO,CAACyD,MAAM,CAACC,cAAc,CAAC,CAAC;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/DrD,OAAA;YAAAiD,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACdrD,OAAA;YAAAiD,QAAA,GAAM,SAAO,EAAC/C,IAAI,CAACW,OAAO,CAAC2D,KAAK,CAAC,KAAK,CAAC,CAACF,MAAM,CAACC,cAAc,CAAC,CAAC;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENrD,OAAA;UAAKgD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BjD,OAAA;YAAMgD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC9BxC,QAAQ,KAAK,WAAW,GAAG,4CAA4C,GAAG;UAAe;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CArNIH,SAAS;AAAAwE,EAAA,GAATxE,SAAS;AAuNf,eAAeA,SAAS;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}