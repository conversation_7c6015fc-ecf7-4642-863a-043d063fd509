{"ast": null, "code": "'use strict';\n\nmodule.exports = typoscript;\ntyposcript.displayName = 'typoscript';\ntyposcript.aliases = ['tsconfig'];\nfunction typoscript(Prism) {\n  ;\n  (function (Prism) {\n    var keywords = /\\b(?:ACT|ACTIFSUB|CARRAY|CASE|CLEARGIF|COA|COA_INT|CONSTANTS|CONTENT|CUR|EDITPANEL|EFFECT|EXT|FILE|FLUIDTEMPLATE|FORM|FRAME|FRAMESET|GIFBUILDER|GMENU|GMENU_FOLDOUT|GMENU_LAYERS|GP|HMENU|HRULER|HTML|IENV|IFSUB|IMAGE|IMGMENU|IMGMENUITEM|IMGTEXT|IMG_RESOURCE|INCLUDE_TYPOSCRIPT|JSMENU|JSMENUITEM|LLL|LOAD_REGISTER|NO|PAGE|RECORDS|RESTORE_REGISTER|TEMPLATE|TEXT|TMENU|TMENUITEM|TMENU_LAYERS|USER|USER_INT|_GIFBUILDER|global|globalString|globalVar)\\b/;\n    Prism.languages.typoscript = {\n      comment: [{\n        // multiline comments /* */\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true\n      }, {\n        // double-slash comments - ignored when backslashes or colon is found in front\n        // also ignored whenever directly after an equal-sign, because it would probably be an url without protocol\n        pattern: /(^|[^\\\\:= \\t]|(?:^|[^= \\t])[ \\t]+)\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }, {\n        // hash comments - ignored when leading quote is found for hex colors in strings\n        pattern: /(^|[^\"'])#.*/,\n        lookbehind: true,\n        greedy: true\n      }],\n      function: [{\n        // old include style\n        pattern: /<INCLUDE_TYPOSCRIPT:\\s*source\\s*=\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*>/,\n        inside: {\n          string: {\n            pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n            inside: {\n              keyword: keywords\n            }\n          },\n          keyword: {\n            pattern: /INCLUDE_TYPOSCRIPT/\n          }\n        }\n      }, {\n        // new include style\n        pattern: /@import\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')/,\n        inside: {\n          string: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/\n        }\n      }],\n      string: {\n        pattern: /^([^=]*=[< ]?)(?:(?!\\]\\n).)*/,\n        lookbehind: true,\n        inside: {\n          function: /\\{\\$.*\\}/,\n          // constants include\n          keyword: keywords,\n          number: /^\\d+$/,\n          punctuation: /[,|:]/\n        }\n      },\n      keyword: keywords,\n      number: {\n        // special highlighting for indexes of arrays in tags\n        pattern: /\\b\\d+\\s*[.{=]/,\n        inside: {\n          operator: /[.{=]/\n        }\n      },\n      tag: {\n        pattern: /\\.?[-\\w\\\\]+\\.?/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      punctuation: /[{}[\\];(),.:|]/,\n      operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/\n    };\n    Prism.languages.tsconfig = Prism.languages.typoscript;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "typoscript", "displayName", "aliases", "Prism", "keywords", "languages", "comment", "pattern", "lookbehind", "greedy", "function", "inside", "string", "keyword", "number", "punctuation", "operator", "tag", "tsconfig"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/typoscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = typoscript\ntyposcript.displayName = 'typoscript'\ntyposcript.aliases = ['tsconfig']\nfunction typoscript(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:ACT|ACTIFSUB|CARRAY|CASE|CLEARGIF|COA|COA_INT|CONSTANTS|CONTENT|CUR|EDITPANEL|EFFECT|EXT|FILE|FLUIDTEMPLATE|FORM|FRAME|FRAMESET|GIFBUILDER|GMENU|GMENU_FOLDOUT|GMENU_LAYERS|GP|HMENU|HRULER|HTML|IENV|IFSUB|IMAGE|IMGMENU|IMGMENUITEM|IMGTEXT|IMG_RESOURCE|INCLUDE_TYPOSCRIPT|JSMENU|JSMENUITEM|LLL|LOAD_REGISTER|NO|PAGE|RECORDS|RESTORE_REGISTER|TEMPLATE|TEXT|TMENU|TMENUITEM|TMENU_LAYERS|USER|USER_INT|_GIFBUILDER|global|globalString|globalVar)\\b/\n    Prism.languages.typoscript = {\n      comment: [\n        {\n          // multiline comments /* */\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true\n        },\n        {\n          // double-slash comments - ignored when backslashes or colon is found in front\n          // also ignored whenever directly after an equal-sign, because it would probably be an url without protocol\n          pattern: /(^|[^\\\\:= \\t]|(?:^|[^= \\t])[ \\t]+)\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // hash comments - ignored when leading quote is found for hex colors in strings\n          pattern: /(^|[^\"'])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      function: [\n        {\n          // old include style\n          pattern:\n            /<INCLUDE_TYPOSCRIPT:\\s*source\\s*=\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*>/,\n          inside: {\n            string: {\n              pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n              inside: {\n                keyword: keywords\n              }\n            },\n            keyword: {\n              pattern: /INCLUDE_TYPOSCRIPT/\n            }\n          }\n        },\n        {\n          // new include style\n          pattern: /@import\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')/,\n          inside: {\n            string: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/\n          }\n        }\n      ],\n      string: {\n        pattern: /^([^=]*=[< ]?)(?:(?!\\]\\n).)*/,\n        lookbehind: true,\n        inside: {\n          function: /\\{\\$.*\\}/,\n          // constants include\n          keyword: keywords,\n          number: /^\\d+$/,\n          punctuation: /[,|:]/\n        }\n      },\n      keyword: keywords,\n      number: {\n        // special highlighting for indexes of arrays in tags\n        pattern: /\\b\\d+\\s*[.{=]/,\n        inside: {\n          operator: /[.{=]/\n        }\n      },\n      tag: {\n        pattern: /\\.?[-\\w\\\\]+\\.?/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      punctuation: /[{}[\\];(),.:|]/,\n      operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/\n    }\n    Prism.languages.tsconfig = Prism.languages.typoscript\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,CAAC,UAAU,CAAC;AACjC,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GACV,+bAA+b;IACjcD,KAAK,CAACE,SAAS,CAACL,UAAU,GAAG;MAC3BM,OAAO,EAAE,CACP;QACE;QACAC,OAAO,EAAE,iCAAiC;QAC1CC,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACA;QACAD,OAAO,EAAE,0CAA0C;QACnDC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAF,OAAO,EAAE,cAAc;QACvBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,QAAQ,EAAE,CACR;QACE;QACAH,OAAO,EACL,qEAAqE;QACvEI,MAAM,EAAE;UACNC,MAAM,EAAE;YACNL,OAAO,EAAE,yBAAyB;YAClCI,MAAM,EAAE;cACNE,OAAO,EAAET;YACX;UACF,CAAC;UACDS,OAAO,EAAE;YACPN,OAAO,EAAE;UACX;QACF;MACF,CAAC,EACD;QACE;QACAA,OAAO,EAAE,uCAAuC;QAChDI,MAAM,EAAE;UACNC,MAAM,EAAE;QACV;MACF,CAAC,CACF;MACDA,MAAM,EAAE;QACNL,OAAO,EAAE,8BAA8B;QACvCC,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE;UACND,QAAQ,EAAE,UAAU;UACpB;UACAG,OAAO,EAAET,QAAQ;UACjBU,MAAM,EAAE,OAAO;UACfC,WAAW,EAAE;QACf;MACF,CAAC;MACDF,OAAO,EAAET,QAAQ;MACjBU,MAAM,EAAE;QACN;QACAP,OAAO,EAAE,eAAe;QACxBI,MAAM,EAAE;UACNK,QAAQ,EAAE;QACZ;MACF,CAAC;MACDC,GAAG,EAAE;QACHV,OAAO,EAAE,gBAAgB;QACzBI,MAAM,EAAE;UACNI,WAAW,EAAE;QACf;MACF,CAAC;MACDA,WAAW,EAAE,gBAAgB;MAC7BC,QAAQ,EAAE;IACZ,CAAC;IACDb,KAAK,CAACE,SAAS,CAACa,QAAQ,GAAGf,KAAK,CAACE,SAAS,CAACL,UAAU;EACvD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}