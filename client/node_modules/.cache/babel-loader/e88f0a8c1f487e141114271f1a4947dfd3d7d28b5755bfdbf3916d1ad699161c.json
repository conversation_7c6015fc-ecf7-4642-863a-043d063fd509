{"ast": null, "code": "'use strict';\n\nmodule.exports = icon;\nicon.displayName = 'icon';\nicon.aliases = [];\nfunction icon(Prism) {\n  Prism.languages.icon = {\n    comment: /#.*/,\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n_]|\\\\.|_(?!\\1)(?:\\r\\n|[\\s\\S]))*\\1/,\n      greedy: true\n    },\n    number: /\\b(?:\\d+r[a-z\\d]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b|\\.\\d+\\b/i,\n    'builtin-keyword': {\n      pattern: /&(?:allocated|ascii|clock|collections|cset|current|date|dateline|digits|dump|e|error(?:number|text|value)?|errout|fail|features|file|host|input|lcase|letters|level|line|main|null|output|phi|pi|pos|progname|random|regions|source|storage|subject|time|trace|ucase|version)\\b/,\n      alias: 'variable'\n    },\n    directive: {\n      pattern: /\\$\\w+/,\n      alias: 'builtin'\n    },\n    keyword: /\\b(?:break|by|case|create|default|do|else|end|every|fail|global|if|initial|invocable|link|local|next|not|of|procedure|record|repeat|return|static|suspend|then|to|until|while)\\b/,\n    function: /\\b(?!\\d)\\w+(?=\\s*[({]|\\s*!\\s*\\[)/,\n    operator: /[+-]:(?!=)|(?:[\\/?@^%&]|\\+\\+?|--?|==?=?|~==?=?|\\*\\*?|\\|\\|\\|?|<(?:->?|<?=?)|>>?=?)(?::=)?|:(?:=:?)?|[!.\\\\|~]/,\n    punctuation: /[\\[\\](){},;]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "icon", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "number", "alias", "directive", "keyword", "function", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/icon.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = icon\nicon.displayName = 'icon'\nicon.aliases = []\nfunction icon(Prism) {\n  Prism.languages.icon = {\n    comment: /#.*/,\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n_]|\\\\.|_(?!\\1)(?:\\r\\n|[\\s\\S]))*\\1/,\n      greedy: true\n    },\n    number: /\\b(?:\\d+r[a-z\\d]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b|\\.\\d+\\b/i,\n    'builtin-keyword': {\n      pattern:\n        /&(?:allocated|ascii|clock|collections|cset|current|date|dateline|digits|dump|e|error(?:number|text|value)?|errout|fail|features|file|host|input|lcase|letters|level|line|main|null|output|phi|pi|pos|progname|random|regions|source|storage|subject|time|trace|ucase|version)\\b/,\n      alias: 'variable'\n    },\n    directive: {\n      pattern: /\\$\\w+/,\n      alias: 'builtin'\n    },\n    keyword:\n      /\\b(?:break|by|case|create|default|do|else|end|every|fail|global|if|initial|invocable|link|local|next|not|of|procedure|record|repeat|return|static|suspend|then|to|until|while)\\b/,\n    function: /\\b(?!\\d)\\w+(?=\\s*[({]|\\s*!\\s*\\[)/,\n    operator:\n      /[+-]:(?!=)|(?:[\\/?@^%&]|\\+\\+?|--?|==?=?|~==?=?|\\*\\*?|\\|\\|\\|?|<(?:->?|<?=?)|>>?=?)(?::=)?|:(?:=:?)?|[!.\\\\|~]/,\n    punctuation: /[\\[\\](){},;]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;MACNC,OAAO,EAAE,0DAA0D;MACnEC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE,2DAA2D;IACnE,iBAAiB,EAAE;MACjBF,OAAO,EACL,iRAAiR;MACnRG,KAAK,EAAE;IACT,CAAC;IACDC,SAAS,EAAE;MACTJ,OAAO,EAAE,OAAO;MAChBG,KAAK,EAAE;IACT,CAAC;IACDE,OAAO,EACL,kLAAkL;IACpLC,QAAQ,EAAE,kCAAkC;IAC5CC,QAAQ,EACN,6GAA6G;IAC/GC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}