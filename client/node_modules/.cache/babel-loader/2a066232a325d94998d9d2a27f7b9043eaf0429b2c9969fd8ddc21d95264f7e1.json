{"ast": null, "code": "'use strict';\n\nmodule.exports = squirrel;\nsquirrel.displayName = 'squirrel';\nsquirrel.aliases = [];\nfunction squirrel(Prism) {\n  Prism.languages.squirrel = Prism.languages.extend('clike', {\n    comment: [Prism.languages.clike['comment'][0], {\n      pattern: /(^|[^\\\\:])(?:\\/\\/|#).*/,\n      lookbehind: true,\n      greedy: true\n    }],\n    string: {\n      pattern: /(^|[^\\\\\"'@])(?:@\"(?:[^\"]|\"\")*\"(?!\")|\"(?:[^\\\\\\r\\n\"]|\\\\.)*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:class|enum|extends|instanceof)\\s+)\\w+(?:\\.\\w+)*/,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    keyword: /\\b(?:__FILE__|__LINE__|base|break|case|catch|class|clone|const|constructor|continue|default|delete|else|enum|extends|for|foreach|function|if|in|instanceof|local|null|resume|return|static|switch|this|throw|try|typeof|while|yield)\\b/,\n    number: /\\b(?:0x[0-9a-fA-F]+|\\d+(?:\\.(?:\\d+|[eE][+-]?\\d+))?)\\b/,\n    operator: /\\+\\+|--|<=>|<[-<]|>>>?|&&?|\\|\\|?|[-+*/%!=<>]=?|[~^]|::?/,\n    punctuation: /[(){}\\[\\],;.]/\n  });\n  Prism.languages.insertBefore('squirrel', 'string', {\n    char: {\n      pattern: /(^|[^\\\\\"'])'(?:[^\\\\']|\\\\(?:[xuU][0-9a-fA-F]{0,8}|[\\s\\S]))'/,\n      lookbehind: true,\n      greedy: true\n    }\n  });\n  Prism.languages.insertBefore('squirrel', 'operator', {\n    'attribute-punctuation': {\n      pattern: /<\\/|\\/>/,\n      alias: 'important'\n    },\n    lambda: {\n      pattern: /@(?=\\()/,\n      alias: 'operator'\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "squirrel", "displayName", "aliases", "Prism", "languages", "extend", "comment", "clike", "pattern", "lookbehind", "greedy", "string", "inside", "punctuation", "keyword", "number", "operator", "insertBefore", "char", "alias", "lambda"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/squirrel.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = squirrel\nsquirrel.displayName = 'squirrel'\nsquirrel.aliases = []\nfunction squirrel(Prism) {\n  Prism.languages.squirrel = Prism.languages.extend('clike', {\n    comment: [\n      Prism.languages.clike['comment'][0],\n      {\n        pattern: /(^|[^\\\\:])(?:\\/\\/|#).*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /(^|[^\\\\\"'@])(?:@\"(?:[^\"]|\"\")*\"(?!\")|\"(?:[^\\\\\\r\\n\"]|\\\\.)*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:class|enum|extends|instanceof)\\s+)\\w+(?:\\.\\w+)*/,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    keyword:\n      /\\b(?:__FILE__|__LINE__|base|break|case|catch|class|clone|const|constructor|continue|default|delete|else|enum|extends|for|foreach|function|if|in|instanceof|local|null|resume|return|static|switch|this|throw|try|typeof|while|yield)\\b/,\n    number: /\\b(?:0x[0-9a-fA-F]+|\\d+(?:\\.(?:\\d+|[eE][+-]?\\d+))?)\\b/,\n    operator: /\\+\\+|--|<=>|<[-<]|>>>?|&&?|\\|\\|?|[-+*/%!=<>]=?|[~^]|::?/,\n    punctuation: /[(){}\\[\\],;.]/\n  })\n  Prism.languages.insertBefore('squirrel', 'string', {\n    char: {\n      pattern: /(^|[^\\\\\"'])'(?:[^\\\\']|\\\\(?:[xuU][0-9a-fA-F]{0,8}|[\\s\\S]))'/,\n      lookbehind: true,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('squirrel', 'operator', {\n    'attribute-punctuation': {\n      pattern: /<\\/|\\/>/,\n      alias: 'important'\n    },\n    lambda: {\n      pattern: /@(?=\\()/,\n      alias: 'operator'\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvBA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACzDC,OAAO,EAAE,CACPH,KAAK,CAACC,SAAS,CAACG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EACnC;MACEC,OAAO,EAAE,wBAAwB;MACjCC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,4DAA4D;MACrEC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZF,OAAO,EAAE,uDAAuD;MAChEC,UAAU,EAAE,IAAI;MAChBG,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EACL,wOAAwO;IAC1OC,MAAM,EAAE,uDAAuD;IAC/DC,QAAQ,EAAE,yDAAyD;IACnEH,WAAW,EAAE;EACf,CAAC,CAAC;EACFV,KAAK,CAACC,SAAS,CAACa,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE;IACjDC,IAAI,EAAE;MACJV,OAAO,EAAE,4DAA4D;MACrEC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACFP,KAAK,CAACC,SAAS,CAACa,YAAY,CAAC,UAAU,EAAE,UAAU,EAAE;IACnD,uBAAuB,EAAE;MACvBT,OAAO,EAAE,SAAS;MAClBW,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNZ,OAAO,EAAE,SAAS;MAClBW,KAAK,EAAE;IACT;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}