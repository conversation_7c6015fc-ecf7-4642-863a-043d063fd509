{"ast": null, "code": "/*\nLanguage: GAUSS\nAuthor: <PERSON> <<EMAIL>>\nDescription: GAUSS Mathematical and Statistical language\nWebsite: https://www.aptech.com\nCategory: scientific\n*/\nfunction gauss(hljs) {\n  const KEYWORDS = {\n    keyword: 'bool break call callexe checkinterrupt clear clearg closeall cls comlog compile ' + 'continue create debug declare delete disable dlibrary dllcall do dos ed edit else ' + 'elseif enable end endfor endif endp endo errorlog errorlogat expr external fn ' + 'for format goto gosub graph if keyword let lib library line load loadarray loadexe ' + 'loadf loadk loadm loadp loads loadx local locate loopnextindex lprint lpwidth lshow ' + 'matrix msym ndpclex new open output outwidth plot plotsym pop prcsn print ' + 'printdos proc push retp return rndcon rndmod rndmult rndseed run save saveall screen ' + 'scroll setarray show sparse stop string struct system trace trap threadfor ' + 'threadendfor threadbegin threadjoin threadstat threadend until use while winprint ' + 'ne ge le gt lt and xor or not eq eqv',\n    built_in: 'abs acf aconcat aeye amax amean AmericanBinomCall AmericanBinomCall_Greeks AmericanBinomCall_ImpVol ' + 'AmericanBinomPut AmericanBinomPut_Greeks AmericanBinomPut_ImpVol AmericanBSCall AmericanBSCall_Greeks ' + 'AmericanBSCall_ImpVol AmericanBSPut AmericanBSPut_Greeks AmericanBSPut_ImpVol amin amult annotationGetDefaults ' + 'annotationSetBkd annotationSetFont annotationSetLineColor annotationSetLineStyle annotationSetLineThickness ' + 'annualTradingDays arccos arcsin areshape arrayalloc arrayindex arrayinit arraytomat asciiload asclabel astd ' + 'astds asum atan atan2 atranspose axmargin balance band bandchol bandcholsol bandltsol bandrv bandsolpd bar ' + 'base10 begwind besselj bessely beta box boxcox cdfBeta cdfBetaInv cdfBinomial cdfBinomialInv cdfBvn cdfBvn2 ' + 'cdfBvn2e cdfCauchy cdfCauchyInv cdfChic cdfChii cdfChinc cdfChincInv cdfExp cdfExpInv cdfFc cdfFnc cdfFncInv ' + 'cdfGam cdfGenPareto cdfHyperGeo cdfLaplace cdfLaplaceInv cdfLogistic cdfLogisticInv cdfmControlCreate cdfMvn ' + 'cdfMvn2e cdfMvnce cdfMvne cdfMvt2e cdfMvtce cdfMvte cdfN cdfN2 cdfNc cdfNegBinomial cdfNegBinomialInv cdfNi ' + 'cdfPoisson cdfPoissonInv cdfRayleigh cdfRayleighInv cdfTc cdfTci cdfTnc cdfTvn cdfWeibull cdfWeibullInv cdir ' + 'ceil ChangeDir chdir chiBarSquare chol choldn cholsol cholup chrs close code cols colsf combinate combinated ' + 'complex con cond conj cons ConScore contour conv convertsatostr convertstrtosa corrm corrms corrvc corrx corrxs ' + 'cos cosh counts countwts crossprd crout croutp csrcol csrlin csvReadM csvReadSA cumprodc cumsumc curve cvtos ' + 'datacreate datacreatecomplex datalist dataload dataloop dataopen datasave date datestr datestring datestrymd ' + 'dayinyr dayofweek dbAddDatabase dbClose dbCommit dbCreateQuery dbExecQuery dbGetConnectOptions dbGetDatabaseName ' + 'dbGetDriverName dbGetDrivers dbGetHostName dbGetLastErrorNum dbGetLastErrorText dbGetNumericalPrecPolicy ' + 'dbGetPassword dbGetPort dbGetTableHeaders dbGetTables dbGetUserName dbHasFeature dbIsDriverAvailable dbIsOpen ' + 'dbIsOpenError dbOpen dbQueryBindValue dbQueryClear dbQueryCols dbQueryExecPrepared dbQueryFetchAllM dbQueryFetchAllSA ' + 'dbQueryFetchOneM dbQueryFetchOneSA dbQueryFinish dbQueryGetBoundValue dbQueryGetBoundValues dbQueryGetField ' + 'dbQueryGetLastErrorNum dbQueryGetLastErrorText dbQueryGetLastInsertID dbQueryGetLastQuery dbQueryGetPosition ' + 'dbQueryIsActive dbQueryIsForwardOnly dbQueryIsNull dbQueryIsSelect dbQueryIsValid dbQueryPrepare dbQueryRows ' + 'dbQuerySeek dbQuerySeekFirst dbQuerySeekLast dbQuerySeekNext dbQuerySeekPrevious dbQuerySetForwardOnly ' + 'dbRemoveDatabase dbRollback dbSetConnectOptions dbSetDatabaseName dbSetHostName dbSetNumericalPrecPolicy ' + 'dbSetPort dbSetUserName dbTransaction DeleteFile delif delrows denseToSp denseToSpRE denToZero design det detl ' + 'dfft dffti diag diagrv digamma doswin DOSWinCloseall DOSWinOpen dotfeq dotfeqmt dotfge dotfgemt dotfgt dotfgtmt ' + 'dotfle dotflemt dotflt dotfltmt dotfne dotfnemt draw drop dsCreate dstat dstatmt dstatmtControlCreate dtdate dtday ' + 'dttime dttodtv dttostr dttoutc dtvnormal dtvtodt dtvtoutc dummy dummybr dummydn eig eigh eighv eigv elapsedTradingDays ' + 'endwind envget eof eqSolve eqSolvemt eqSolvemtControlCreate eqSolvemtOutCreate eqSolveset erf erfc erfccplx erfcplx error ' + 'etdays ethsec etstr EuropeanBinomCall EuropeanBinomCall_Greeks EuropeanBinomCall_ImpVol EuropeanBinomPut ' + 'EuropeanBinomPut_Greeks EuropeanBinomPut_ImpVol EuropeanBSCall EuropeanBSCall_Greeks EuropeanBSCall_ImpVol ' + 'EuropeanBSPut EuropeanBSPut_Greeks EuropeanBSPut_ImpVol exctsmpl exec execbg exp extern eye fcheckerr fclearerr feq ' + 'feqmt fflush fft ffti fftm fftmi fftn fge fgemt fgets fgetsa fgetsat fgetst fgt fgtmt fileinfo filesa fle flemt ' + 'floor flt fltmt fmod fne fnemt fonts fopen formatcv formatnv fputs fputst fseek fstrerror ftell ftocv ftos ftostrC ' + 'gamma gammacplx gammaii gausset gdaAppend gdaCreate gdaDStat gdaDStatMat gdaGetIndex gdaGetName gdaGetNames gdaGetOrders ' + 'gdaGetType gdaGetTypes gdaGetVarInfo gdaIsCplx gdaLoad gdaPack gdaRead gdaReadByIndex gdaReadSome gdaReadSparse ' + 'gdaReadStruct gdaReportVarInfo gdaSave gdaUpdate gdaUpdateAndPack gdaVars gdaWrite gdaWrite32 gdaWriteSome getarray ' + 'getdims getf getGAUSShome getmatrix getmatrix4D getname getnamef getNextTradingDay getNextWeekDay getnr getorders ' + 'getpath getPreviousTradingDay getPreviousWeekDay getRow getscalar3D getscalar4D getTrRow getwind glm gradcplx gradMT ' + 'gradMTm gradMTT gradMTTm gradp graphprt graphset hasimag header headermt hess hessMT hessMTg hessMTgw hessMTm ' + 'hessMTmw hessMTT hessMTTg hessMTTgw hessMTTm hessMTw hessp hist histf histp hsec imag indcv indexcat indices indices2 ' + 'indicesf indicesfn indnv indsav integrate1d integrateControlCreate intgrat2 intgrat3 inthp1 inthp2 inthp3 inthp4 ' + 'inthpControlCreate intquad1 intquad2 intquad3 intrleav intrleavsa intrsect intsimp inv invpd invswp iscplx iscplxf ' + 'isden isinfnanmiss ismiss key keyav keyw lag lag1 lagn lapEighb lapEighi lapEighvb lapEighvi lapgEig lapgEigh lapgEighv ' + 'lapgEigv lapgSchur lapgSvdcst lapgSvds lapgSvdst lapSvdcusv lapSvds lapSvdusv ldlp ldlsol linSolve listwise ln lncdfbvn ' + 'lncdfbvn2 lncdfmvn lncdfn lncdfn2 lncdfnc lnfact lngammacplx lnpdfmvn lnpdfmvt lnpdfn lnpdft loadd loadstruct loadwind ' + 'loess loessmt loessmtControlCreate log loglog logx logy lower lowmat lowmat1 ltrisol lu lusol machEpsilon make makevars ' + 'makewind margin matalloc matinit mattoarray maxbytes maxc maxindc maxv maxvec mbesselei mbesselei0 mbesselei1 mbesseli ' + 'mbesseli0 mbesseli1 meanc median mergeby mergevar minc minindc minv miss missex missrv moment momentd movingave ' + 'movingaveExpwgt movingaveWgt nextindex nextn nextnevn nextwind ntos null null1 numCombinations ols olsmt olsmtControlCreate ' + 'olsqr olsqr2 olsqrmt ones optn optnevn orth outtyp pacf packedToSp packr parse pause pdfCauchy pdfChi pdfExp pdfGenPareto ' + 'pdfHyperGeo pdfLaplace pdfLogistic pdfn pdfPoisson pdfRayleigh pdfWeibull pi pinv pinvmt plotAddArrow plotAddBar plotAddBox ' + 'plotAddHist plotAddHistF plotAddHistP plotAddPolar plotAddScatter plotAddShape plotAddTextbox plotAddTS plotAddXY plotArea ' + 'plotBar plotBox plotClearLayout plotContour plotCustomLayout plotGetDefaults plotHist plotHistF plotHistP plotLayout ' + 'plotLogLog plotLogX plotLogY plotOpenWindow plotPolar plotSave plotScatter plotSetAxesPen plotSetBar plotSetBarFill ' + 'plotSetBarStacked plotSetBkdColor plotSetFill plotSetGrid plotSetLegend plotSetLineColor plotSetLineStyle plotSetLineSymbol ' + 'plotSetLineThickness plotSetNewWindow plotSetTitle plotSetWhichYAxis plotSetXAxisShow plotSetXLabel plotSetXRange ' + 'plotSetXTicInterval plotSetXTicLabel plotSetYAxisShow plotSetYLabel plotSetYRange plotSetZAxisShow plotSetZLabel ' + 'plotSurface plotTS plotXY polar polychar polyeval polygamma polyint polymake polymat polymroot polymult polyroot ' + 'pqgwin previousindex princomp printfm printfmt prodc psi putarray putf putvals pvCreate pvGetIndex pvGetParNames ' + 'pvGetParVector pvLength pvList pvPack pvPacki pvPackm pvPackmi pvPacks pvPacksi pvPacksm pvPacksmi pvPutParVector ' + 'pvTest pvUnpack QNewton QNewtonmt QNewtonmtControlCreate QNewtonmtOutCreate QNewtonSet QProg QProgmt QProgmtInCreate ' + 'qqr qqre qqrep qr qre qrep qrsol qrtsol qtyr qtyre qtyrep quantile quantiled qyr qyre qyrep qz rank rankindx readr ' + 'real reclassify reclassifyCuts recode recserar recsercp recserrc rerun rescale reshape rets rev rfft rffti rfftip rfftn ' + 'rfftnp rfftp rndBernoulli rndBeta rndBinomial rndCauchy rndChiSquare rndCon rndCreateState rndExp rndGamma rndGeo rndGumbel ' + 'rndHyperGeo rndi rndKMbeta rndKMgam rndKMi rndKMn rndKMnb rndKMp rndKMu rndKMvm rndLaplace rndLCbeta rndLCgam rndLCi rndLCn ' + 'rndLCnb rndLCp rndLCu rndLCvm rndLogNorm rndMTu rndMVn rndMVt rndn rndnb rndNegBinomial rndp rndPoisson rndRayleigh ' + 'rndStateSkip rndu rndvm rndWeibull rndWishart rotater round rows rowsf rref sampleData satostrC saved saveStruct savewind ' + 'scale scale3d scalerr scalinfnanmiss scalmiss schtoc schur searchsourcepath seekr select selif seqa seqm setdif setdifsa ' + 'setvars setvwrmode setwind shell shiftr sin singleindex sinh sleep solpd sortc sortcc sortd sorthc sorthcc sortind ' + 'sortindc sortmc sortr sortrc spBiconjGradSol spChol spConjGradSol spCreate spDenseSubmat spDiagRvMat spEigv spEye spLDL ' + 'spline spLU spNumNZE spOnes spreadSheetReadM spreadSheetReadSA spreadSheetWrite spScale spSubmat spToDense spTrTDense ' + 'spTScalar spZeros sqpSolve sqpSolveMT sqpSolveMTControlCreate sqpSolveMTlagrangeCreate sqpSolveMToutCreate sqpSolveSet ' + 'sqrt statements stdc stdsc stocv stof strcombine strindx strlen strput strrindx strsect strsplit strsplitPad strtodt ' + 'strtof strtofcplx strtriml strtrimr strtrunc strtruncl strtruncpad strtruncr submat subscat substute subvec sumc sumr ' + 'surface svd svd1 svd2 svdcusv svds svdusv sysstate tab tan tanh tempname ' + 'time timedt timestr timeutc title tkf2eps tkf2ps tocart todaydt toeplitz token topolar trapchk ' + 'trigamma trimr trunc type typecv typef union unionsa uniqindx uniqindxsa unique uniquesa upmat upmat1 upper utctodt ' + 'utctodtv utrisol vals varCovMS varCovXS varget vargetl varmall varmares varput varputl vartypef vcm vcms vcx vcxs ' + 'vec vech vecr vector vget view viewxyz vlist vnamecv volume vput vread vtypecv wait waitc walkindex where window ' + 'writer xlabel xlsGetSheetCount xlsGetSheetSize xlsGetSheetTypes xlsMakeRange xlsReadM xlsReadSA xlsWrite xlsWriteM ' + 'xlsWriteSA xpnd xtics xy xyz ylabel ytics zeros zeta zlabel ztics cdfEmpirical dot h5create h5open h5read h5readAttribute ' + 'h5write h5writeAttribute ldl plotAddErrorBar plotAddSurface plotCDFEmpirical plotSetColormap plotSetContourLabels ' + 'plotSetLegendFont plotSetTextInterpreter plotSetXTicCount plotSetYTicCount plotSetZLevels powerm strjoin sylvester ' + 'strtrim',\n    literal: 'DB_AFTER_LAST_ROW DB_ALL_TABLES DB_BATCH_OPERATIONS DB_BEFORE_FIRST_ROW DB_BLOB DB_EVENT_NOTIFICATIONS ' + 'DB_FINISH_QUERY DB_HIGH_PRECISION DB_LAST_INSERT_ID DB_LOW_PRECISION_DOUBLE DB_LOW_PRECISION_INT32 ' + 'DB_LOW_PRECISION_INT64 DB_LOW_PRECISION_NUMBERS DB_MULTIPLE_RESULT_SETS DB_NAMED_PLACEHOLDERS ' + 'DB_POSITIONAL_PLACEHOLDERS DB_PREPARED_QUERIES DB_QUERY_SIZE DB_SIMPLE_LOCKING DB_SYSTEM_TABLES DB_TABLES ' + 'DB_TRANSACTIONS DB_UNICODE DB_VIEWS __STDIN __STDOUT __STDERR __FILE_DIR'\n  };\n  const AT_COMMENT_MODE = hljs.COMMENT('@', '@');\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: {\n      'meta-keyword': 'define definecs|10 undef ifdef ifndef iflight ifdllcall ifmac ifos2win ifunix else endif lineson linesoff srcfile srcline'\n    },\n    contains: [{\n      begin: /\\\\\\n/,\n      relevance: 0\n    }, {\n      beginKeywords: 'include',\n      end: '$',\n      keywords: {\n        'meta-keyword': 'include'\n      },\n      contains: [{\n        className: 'meta-string',\n        begin: '\"',\n        end: '\"',\n        illegal: '\\\\n'\n      }]\n    }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, AT_COMMENT_MODE]\n  };\n  const STRUCT_TYPE = {\n    begin: /\\bstruct\\s+/,\n    end: /\\s/,\n    keywords: \"struct\",\n    contains: [{\n      className: \"type\",\n      begin: hljs.UNDERSCORE_IDENT_RE,\n      relevance: 0\n    }]\n  };\n\n  // only for definitions\n  const PARSE_PARAMS = [{\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    endsWithParent: true,\n    relevance: 0,\n    contains: [{\n      // dots\n      className: 'literal',\n      begin: /\\.\\.\\./\n    }, hljs.C_NUMBER_MODE, hljs.C_BLOCK_COMMENT_MODE, AT_COMMENT_MODE, STRUCT_TYPE]\n  }];\n  const FUNCTION_DEF = {\n    className: \"title\",\n    begin: hljs.UNDERSCORE_IDENT_RE,\n    relevance: 0\n  };\n  const DEFINITION = function (beginKeywords, end, inherits) {\n    const mode = hljs.inherit({\n      className: \"function\",\n      beginKeywords: beginKeywords,\n      end: end,\n      excludeEnd: true,\n      contains: [].concat(PARSE_PARAMS)\n    }, inherits || {});\n    mode.contains.push(FUNCTION_DEF);\n    mode.contains.push(hljs.C_NUMBER_MODE);\n    mode.contains.push(hljs.C_BLOCK_COMMENT_MODE);\n    mode.contains.push(AT_COMMENT_MODE);\n    return mode;\n  };\n  const BUILT_IN_REF = {\n    // these are explicitly named internal function calls\n    className: 'built_in',\n    begin: '\\\\b(' + KEYWORDS.built_in.split(' ').join('|') + ')\\\\b'\n  };\n  const STRING_REF = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    relevance: 0\n  };\n  const FUNCTION_REF = {\n    // className: \"fn_ref\",\n    begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n    returnBegin: true,\n    keywords: KEYWORDS,\n    relevance: 0,\n    contains: [{\n      beginKeywords: KEYWORDS.keyword\n    }, BUILT_IN_REF, {\n      // ambiguously named function calls get a relevance of 0\n      className: 'built_in',\n      begin: hljs.UNDERSCORE_IDENT_RE,\n      relevance: 0\n    }]\n  };\n  const FUNCTION_REF_PARAMS = {\n    // className: \"fn_ref_params\",\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: {\n      built_in: KEYWORDS.built_in,\n      literal: KEYWORDS.literal\n    },\n    contains: [hljs.C_NUMBER_MODE, hljs.C_BLOCK_COMMENT_MODE, AT_COMMENT_MODE, BUILT_IN_REF, FUNCTION_REF, STRING_REF, 'self']\n  };\n  FUNCTION_REF.contains.push(FUNCTION_REF_PARAMS);\n  return {\n    name: 'GAUSS',\n    aliases: ['gss'],\n    case_insensitive: true,\n    // language is case-insensitive\n    keywords: KEYWORDS,\n    illegal: /(\\{[%#]|[%#]\\}| <- )/,\n    contains: [hljs.C_NUMBER_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, AT_COMMENT_MODE, STRING_REF, PREPROCESSOR, {\n      className: 'keyword',\n      begin: /\\bexternal (matrix|string|array|sparse matrix|struct|proc|keyword|fn)/\n    }, DEFINITION('proc keyword', ';'), DEFINITION('fn', '='), {\n      beginKeywords: 'for threadfor',\n      end: /;/,\n      // end: /\\(/,\n      relevance: 0,\n      contains: [hljs.C_BLOCK_COMMENT_MODE, AT_COMMENT_MODE, FUNCTION_REF_PARAMS]\n    }, {\n      // custom method guard\n      // excludes method names from keyword processing\n      variants: [{\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\.' + hljs.UNDERSCORE_IDENT_RE\n      }, {\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*='\n      }],\n      relevance: 0\n    }, FUNCTION_REF, STRUCT_TYPE]\n  };\n}\nmodule.exports = gauss;", "map": {"version": 3, "names": ["gauss", "hljs", "KEYWORDS", "keyword", "built_in", "literal", "AT_COMMENT_MODE", "COMMENT", "PREPROCESSOR", "className", "begin", "end", "keywords", "contains", "relevance", "beginKeywords", "illegal", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "STRUCT_TYPE", "UNDERSCORE_IDENT_RE", "PARSE_PARAMS", "excludeBegin", "excludeEnd", "endsWithParent", "C_NUMBER_MODE", "FUNCTION_DEF", "DEFINITION", "inherits", "mode", "inherit", "concat", "push", "BUILT_IN_REF", "split", "join", "STRING_REF", "BACKSLASH_ESCAPE", "FUNCTION_REF", "returnBegin", "FUNCTION_REF_PARAMS", "name", "aliases", "case_insensitive", "variants", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/gauss.js"], "sourcesContent": ["/*\nLanguage: GAUSS\nAuthor: <PERSON> <<EMAIL>>\nDescription: GAUSS Mathematical and Statistical language\nWebsite: https://www.aptech.com\nCategory: scientific\n*/\nfunction gauss(hljs) {\n  const KEYWORDS = {\n    keyword: 'bool break call callexe checkinterrupt clear clearg closeall cls comlog compile ' +\n              'continue create debug declare delete disable dlibrary dllcall do dos ed edit else ' +\n              'elseif enable end endfor endif endp endo errorlog errorlogat expr external fn ' +\n              'for format goto gosub graph if keyword let lib library line load loadarray loadexe ' +\n              'loadf loadk loadm loadp loads loadx local locate loopnextindex lprint lpwidth lshow ' +\n              'matrix msym ndpclex new open output outwidth plot plotsym pop prcsn print ' +\n              'printdos proc push retp return rndcon rndmod rndmult rndseed run save saveall screen ' +\n              'scroll setarray show sparse stop string struct system trace trap threadfor ' +\n              'threadendfor threadbegin threadjoin threadstat threadend until use while winprint ' +\n              'ne ge le gt lt and xor or not eq eqv',\n    built_in: 'abs acf aconcat aeye amax amean AmericanBinomCall AmericanBinomCall_Greeks AmericanBinomCall_ImpVol ' +\n              'AmericanBinomPut AmericanBinomPut_Greeks AmericanBinomPut_ImpVol AmericanBSCall AmericanBSCall_Greeks ' +\n              'AmericanBSCall_ImpVol AmericanBSPut AmericanBSPut_Greeks AmericanBSPut_ImpVol amin amult annotationGetDefaults ' +\n              'annotationSetBkd annotationSetFont annotationSetLineColor annotationSetLineStyle annotationSetLineThickness ' +\n              'annualTradingDays arccos arcsin areshape arrayalloc arrayindex arrayinit arraytomat asciiload asclabel astd ' +\n              'astds asum atan atan2 atranspose axmargin balance band bandchol bandcholsol bandltsol bandrv bandsolpd bar ' +\n              'base10 begwind besselj bessely beta box boxcox cdfBeta cdfBetaInv cdfBinomial cdfBinomialInv cdfBvn cdfBvn2 ' +\n              'cdfBvn2e cdfCauchy cdfCauchyInv cdfChic cdfChii cdfChinc cdfChincInv cdfExp cdfExpInv cdfFc cdfFnc cdfFncInv ' +\n              'cdfGam cdfGenPareto cdfHyperGeo cdfLaplace cdfLaplaceInv cdfLogistic cdfLogisticInv cdfmControlCreate cdfMvn ' +\n              'cdfMvn2e cdfMvnce cdfMvne cdfMvt2e cdfMvtce cdfMvte cdfN cdfN2 cdfNc cdfNegBinomial cdfNegBinomialInv cdfNi ' +\n              'cdfPoisson cdfPoissonInv cdfRayleigh cdfRayleighInv cdfTc cdfTci cdfTnc cdfTvn cdfWeibull cdfWeibullInv cdir ' +\n              'ceil ChangeDir chdir chiBarSquare chol choldn cholsol cholup chrs close code cols colsf combinate combinated ' +\n              'complex con cond conj cons ConScore contour conv convertsatostr convertstrtosa corrm corrms corrvc corrx corrxs ' +\n              'cos cosh counts countwts crossprd crout croutp csrcol csrlin csvReadM csvReadSA cumprodc cumsumc curve cvtos ' +\n              'datacreate datacreatecomplex datalist dataload dataloop dataopen datasave date datestr datestring datestrymd ' +\n              'dayinyr dayofweek dbAddDatabase dbClose dbCommit dbCreateQuery dbExecQuery dbGetConnectOptions dbGetDatabaseName ' +\n              'dbGetDriverName dbGetDrivers dbGetHostName dbGetLastErrorNum dbGetLastErrorText dbGetNumericalPrecPolicy ' +\n              'dbGetPassword dbGetPort dbGetTableHeaders dbGetTables dbGetUserName dbHasFeature dbIsDriverAvailable dbIsOpen ' +\n              'dbIsOpenError dbOpen dbQueryBindValue dbQueryClear dbQueryCols dbQueryExecPrepared dbQueryFetchAllM dbQueryFetchAllSA ' +\n              'dbQueryFetchOneM dbQueryFetchOneSA dbQueryFinish dbQueryGetBoundValue dbQueryGetBoundValues dbQueryGetField ' +\n              'dbQueryGetLastErrorNum dbQueryGetLastErrorText dbQueryGetLastInsertID dbQueryGetLastQuery dbQueryGetPosition ' +\n              'dbQueryIsActive dbQueryIsForwardOnly dbQueryIsNull dbQueryIsSelect dbQueryIsValid dbQueryPrepare dbQueryRows ' +\n              'dbQuerySeek dbQuerySeekFirst dbQuerySeekLast dbQuerySeekNext dbQuerySeekPrevious dbQuerySetForwardOnly ' +\n              'dbRemoveDatabase dbRollback dbSetConnectOptions dbSetDatabaseName dbSetHostName dbSetNumericalPrecPolicy ' +\n              'dbSetPort dbSetUserName dbTransaction DeleteFile delif delrows denseToSp denseToSpRE denToZero design det detl ' +\n              'dfft dffti diag diagrv digamma doswin DOSWinCloseall DOSWinOpen dotfeq dotfeqmt dotfge dotfgemt dotfgt dotfgtmt ' +\n              'dotfle dotflemt dotflt dotfltmt dotfne dotfnemt draw drop dsCreate dstat dstatmt dstatmtControlCreate dtdate dtday ' +\n              'dttime dttodtv dttostr dttoutc dtvnormal dtvtodt dtvtoutc dummy dummybr dummydn eig eigh eighv eigv elapsedTradingDays ' +\n              'endwind envget eof eqSolve eqSolvemt eqSolvemtControlCreate eqSolvemtOutCreate eqSolveset erf erfc erfccplx erfcplx error ' +\n              'etdays ethsec etstr EuropeanBinomCall EuropeanBinomCall_Greeks EuropeanBinomCall_ImpVol EuropeanBinomPut ' +\n              'EuropeanBinomPut_Greeks EuropeanBinomPut_ImpVol EuropeanBSCall EuropeanBSCall_Greeks EuropeanBSCall_ImpVol ' +\n              'EuropeanBSPut EuropeanBSPut_Greeks EuropeanBSPut_ImpVol exctsmpl exec execbg exp extern eye fcheckerr fclearerr feq ' +\n              'feqmt fflush fft ffti fftm fftmi fftn fge fgemt fgets fgetsa fgetsat fgetst fgt fgtmt fileinfo filesa fle flemt ' +\n              'floor flt fltmt fmod fne fnemt fonts fopen formatcv formatnv fputs fputst fseek fstrerror ftell ftocv ftos ftostrC ' +\n              'gamma gammacplx gammaii gausset gdaAppend gdaCreate gdaDStat gdaDStatMat gdaGetIndex gdaGetName gdaGetNames gdaGetOrders ' +\n              'gdaGetType gdaGetTypes gdaGetVarInfo gdaIsCplx gdaLoad gdaPack gdaRead gdaReadByIndex gdaReadSome gdaReadSparse ' +\n              'gdaReadStruct gdaReportVarInfo gdaSave gdaUpdate gdaUpdateAndPack gdaVars gdaWrite gdaWrite32 gdaWriteSome getarray ' +\n              'getdims getf getGAUSShome getmatrix getmatrix4D getname getnamef getNextTradingDay getNextWeekDay getnr getorders ' +\n              'getpath getPreviousTradingDay getPreviousWeekDay getRow getscalar3D getscalar4D getTrRow getwind glm gradcplx gradMT ' +\n              'gradMTm gradMTT gradMTTm gradp graphprt graphset hasimag header headermt hess hessMT hessMTg hessMTgw hessMTm ' +\n              'hessMTmw hessMTT hessMTTg hessMTTgw hessMTTm hessMTw hessp hist histf histp hsec imag indcv indexcat indices indices2 ' +\n              'indicesf indicesfn indnv indsav integrate1d integrateControlCreate intgrat2 intgrat3 inthp1 inthp2 inthp3 inthp4 ' +\n              'inthpControlCreate intquad1 intquad2 intquad3 intrleav intrleavsa intrsect intsimp inv invpd invswp iscplx iscplxf ' +\n              'isden isinfnanmiss ismiss key keyav keyw lag lag1 lagn lapEighb lapEighi lapEighvb lapEighvi lapgEig lapgEigh lapgEighv ' +\n              'lapgEigv lapgSchur lapgSvdcst lapgSvds lapgSvdst lapSvdcusv lapSvds lapSvdusv ldlp ldlsol linSolve listwise ln lncdfbvn ' +\n              'lncdfbvn2 lncdfmvn lncdfn lncdfn2 lncdfnc lnfact lngammacplx lnpdfmvn lnpdfmvt lnpdfn lnpdft loadd loadstruct loadwind ' +\n              'loess loessmt loessmtControlCreate log loglog logx logy lower lowmat lowmat1 ltrisol lu lusol machEpsilon make makevars ' +\n              'makewind margin matalloc matinit mattoarray maxbytes maxc maxindc maxv maxvec mbesselei mbesselei0 mbesselei1 mbesseli ' +\n              'mbesseli0 mbesseli1 meanc median mergeby mergevar minc minindc minv miss missex missrv moment momentd movingave ' +\n              'movingaveExpwgt movingaveWgt nextindex nextn nextnevn nextwind ntos null null1 numCombinations ols olsmt olsmtControlCreate ' +\n              'olsqr olsqr2 olsqrmt ones optn optnevn orth outtyp pacf packedToSp packr parse pause pdfCauchy pdfChi pdfExp pdfGenPareto ' +\n              'pdfHyperGeo pdfLaplace pdfLogistic pdfn pdfPoisson pdfRayleigh pdfWeibull pi pinv pinvmt plotAddArrow plotAddBar plotAddBox ' +\n              'plotAddHist plotAddHistF plotAddHistP plotAddPolar plotAddScatter plotAddShape plotAddTextbox plotAddTS plotAddXY plotArea ' +\n              'plotBar plotBox plotClearLayout plotContour plotCustomLayout plotGetDefaults plotHist plotHistF plotHistP plotLayout ' +\n              'plotLogLog plotLogX plotLogY plotOpenWindow plotPolar plotSave plotScatter plotSetAxesPen plotSetBar plotSetBarFill ' +\n              'plotSetBarStacked plotSetBkdColor plotSetFill plotSetGrid plotSetLegend plotSetLineColor plotSetLineStyle plotSetLineSymbol ' +\n              'plotSetLineThickness plotSetNewWindow plotSetTitle plotSetWhichYAxis plotSetXAxisShow plotSetXLabel plotSetXRange ' +\n              'plotSetXTicInterval plotSetXTicLabel plotSetYAxisShow plotSetYLabel plotSetYRange plotSetZAxisShow plotSetZLabel ' +\n              'plotSurface plotTS plotXY polar polychar polyeval polygamma polyint polymake polymat polymroot polymult polyroot ' +\n              'pqgwin previousindex princomp printfm printfmt prodc psi putarray putf putvals pvCreate pvGetIndex pvGetParNames ' +\n              'pvGetParVector pvLength pvList pvPack pvPacki pvPackm pvPackmi pvPacks pvPacksi pvPacksm pvPacksmi pvPutParVector ' +\n              'pvTest pvUnpack QNewton QNewtonmt QNewtonmtControlCreate QNewtonmtOutCreate QNewtonSet QProg QProgmt QProgmtInCreate ' +\n              'qqr qqre qqrep qr qre qrep qrsol qrtsol qtyr qtyre qtyrep quantile quantiled qyr qyre qyrep qz rank rankindx readr ' +\n              'real reclassify reclassifyCuts recode recserar recsercp recserrc rerun rescale reshape rets rev rfft rffti rfftip rfftn ' +\n              'rfftnp rfftp rndBernoulli rndBeta rndBinomial rndCauchy rndChiSquare rndCon rndCreateState rndExp rndGamma rndGeo rndGumbel ' +\n              'rndHyperGeo rndi rndKMbeta rndKMgam rndKMi rndKMn rndKMnb rndKMp rndKMu rndKMvm rndLaplace rndLCbeta rndLCgam rndLCi rndLCn ' +\n              'rndLCnb rndLCp rndLCu rndLCvm rndLogNorm rndMTu rndMVn rndMVt rndn rndnb rndNegBinomial rndp rndPoisson rndRayleigh ' +\n              'rndStateSkip rndu rndvm rndWeibull rndWishart rotater round rows rowsf rref sampleData satostrC saved saveStruct savewind ' +\n              'scale scale3d scalerr scalinfnanmiss scalmiss schtoc schur searchsourcepath seekr select selif seqa seqm setdif setdifsa ' +\n              'setvars setvwrmode setwind shell shiftr sin singleindex sinh sleep solpd sortc sortcc sortd sorthc sorthcc sortind ' +\n              'sortindc sortmc sortr sortrc spBiconjGradSol spChol spConjGradSol spCreate spDenseSubmat spDiagRvMat spEigv spEye spLDL ' +\n              'spline spLU spNumNZE spOnes spreadSheetReadM spreadSheetReadSA spreadSheetWrite spScale spSubmat spToDense spTrTDense ' +\n              'spTScalar spZeros sqpSolve sqpSolveMT sqpSolveMTControlCreate sqpSolveMTlagrangeCreate sqpSolveMToutCreate sqpSolveSet ' +\n              'sqrt statements stdc stdsc stocv stof strcombine strindx strlen strput strrindx strsect strsplit strsplitPad strtodt ' +\n              'strtof strtofcplx strtriml strtrimr strtrunc strtruncl strtruncpad strtruncr submat subscat substute subvec sumc sumr ' +\n              'surface svd svd1 svd2 svdcusv svds svdusv sysstate tab tan tanh tempname ' +\n              'time timedt timestr timeutc title tkf2eps tkf2ps tocart todaydt toeplitz token topolar trapchk ' +\n              'trigamma trimr trunc type typecv typef union unionsa uniqindx uniqindxsa unique uniquesa upmat upmat1 upper utctodt ' +\n              'utctodtv utrisol vals varCovMS varCovXS varget vargetl varmall varmares varput varputl vartypef vcm vcms vcx vcxs ' +\n              'vec vech vecr vector vget view viewxyz vlist vnamecv volume vput vread vtypecv wait waitc walkindex where window ' +\n              'writer xlabel xlsGetSheetCount xlsGetSheetSize xlsGetSheetTypes xlsMakeRange xlsReadM xlsReadSA xlsWrite xlsWriteM ' +\n              'xlsWriteSA xpnd xtics xy xyz ylabel ytics zeros zeta zlabel ztics cdfEmpirical dot h5create h5open h5read h5readAttribute ' +\n              'h5write h5writeAttribute ldl plotAddErrorBar plotAddSurface plotCDFEmpirical plotSetColormap plotSetContourLabels ' +\n              'plotSetLegendFont plotSetTextInterpreter plotSetXTicCount plotSetYTicCount plotSetZLevels powerm strjoin sylvester ' +\n              'strtrim',\n    literal: 'DB_AFTER_LAST_ROW DB_ALL_TABLES DB_BATCH_OPERATIONS DB_BEFORE_FIRST_ROW DB_BLOB DB_EVENT_NOTIFICATIONS ' +\n             'DB_FINISH_QUERY DB_HIGH_PRECISION DB_LAST_INSERT_ID DB_LOW_PRECISION_DOUBLE DB_LOW_PRECISION_INT32 ' +\n             'DB_LOW_PRECISION_INT64 DB_LOW_PRECISION_NUMBERS DB_MULTIPLE_RESULT_SETS DB_NAMED_PLACEHOLDERS ' +\n             'DB_POSITIONAL_PLACEHOLDERS DB_PREPARED_QUERIES DB_QUERY_SIZE DB_SIMPLE_LOCKING DB_SYSTEM_TABLES DB_TABLES ' +\n             'DB_TRANSACTIONS DB_UNICODE DB_VIEWS __STDIN __STDOUT __STDERR __FILE_DIR'\n  };\n\n  const AT_COMMENT_MODE = hljs.COMMENT('@', '@');\n\n  const PREPROCESSOR =\n  {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: {\n      'meta-keyword': 'define definecs|10 undef ifdef ifndef iflight ifdllcall ifmac ifos2win ifunix else endif lineson linesoff srcfile srcline'\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      {\n        beginKeywords: 'include',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'include'\n        },\n        contains: [\n          {\n            className: 'meta-string',\n            begin: '\"',\n            end: '\"',\n            illegal: '\\\\n'\n          }\n        ]\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      AT_COMMENT_MODE\n    ]\n  };\n\n  const STRUCT_TYPE =\n  {\n    begin: /\\bstruct\\s+/,\n    end: /\\s/,\n    keywords: \"struct\",\n    contains: [\n      {\n        className: \"type\",\n        begin: hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n\n  // only for definitions\n  const PARSE_PARAMS = [\n    {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      endsWithParent: true,\n      relevance: 0,\n      contains: [\n        { // dots\n          className: 'literal',\n          begin: /\\.\\.\\./\n        },\n        hljs.C_NUMBER_MODE,\n        hljs.C_BLOCK_COMMENT_MODE,\n        AT_COMMENT_MODE,\n        STRUCT_TYPE\n      ]\n    }\n  ];\n\n  const FUNCTION_DEF =\n  {\n    className: \"title\",\n    begin: hljs.UNDERSCORE_IDENT_RE,\n    relevance: 0\n  };\n\n  const DEFINITION = function(beginKeywords, end, inherits) {\n    const mode = hljs.inherit(\n      {\n        className: \"function\",\n        beginKeywords: beginKeywords,\n        end: end,\n        excludeEnd: true,\n        contains: [].concat(PARSE_PARAMS)\n      },\n      inherits || {}\n    );\n    mode.contains.push(FUNCTION_DEF);\n    mode.contains.push(hljs.C_NUMBER_MODE);\n    mode.contains.push(hljs.C_BLOCK_COMMENT_MODE);\n    mode.contains.push(AT_COMMENT_MODE);\n    return mode;\n  };\n\n  const BUILT_IN_REF =\n  { // these are explicitly named internal function calls\n    className: 'built_in',\n    begin: '\\\\b(' + KEYWORDS.built_in.split(' ').join('|') + ')\\\\b'\n  };\n\n  const STRING_REF =\n  {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    relevance: 0\n  };\n\n  const FUNCTION_REF =\n  {\n    // className: \"fn_ref\",\n    begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n    returnBegin: true,\n    keywords: KEYWORDS,\n    relevance: 0,\n    contains: [\n      {\n        beginKeywords: KEYWORDS.keyword\n      },\n      BUILT_IN_REF,\n      { // ambiguously named function calls get a relevance of 0\n        className: 'built_in',\n        begin: hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n\n  const FUNCTION_REF_PARAMS =\n  {\n    // className: \"fn_ref_params\",\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: {\n      built_in: KEYWORDS.built_in,\n      literal: KEYWORDS.literal\n    },\n    contains: [\n      hljs.C_NUMBER_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      AT_COMMENT_MODE,\n      BUILT_IN_REF,\n      FUNCTION_REF,\n      STRING_REF,\n      'self'\n    ]\n  };\n\n  FUNCTION_REF.contains.push(FUNCTION_REF_PARAMS);\n\n  return {\n    name: 'GAUSS',\n    aliases: ['gss'],\n    case_insensitive: true, // language is case-insensitive\n    keywords: KEYWORDS,\n    illegal: /(\\{[%#]|[%#]\\}| <- )/,\n    contains: [\n      hljs.C_NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      AT_COMMENT_MODE,\n      STRING_REF,\n      PREPROCESSOR,\n      {\n        className: 'keyword',\n        begin: /\\bexternal (matrix|string|array|sparse matrix|struct|proc|keyword|fn)/\n      },\n      DEFINITION('proc keyword', ';'),\n      DEFINITION('fn', '='),\n      {\n        beginKeywords: 'for threadfor',\n        end: /;/,\n        // end: /\\(/,\n        relevance: 0,\n        contains: [\n          hljs.C_BLOCK_COMMENT_MODE,\n          AT_COMMENT_MODE,\n          FUNCTION_REF_PARAMS\n        ]\n      },\n      { // custom method guard\n        // excludes method names from keyword processing\n        variants: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\.' + hljs.UNDERSCORE_IDENT_RE\n          },\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*='\n          }\n        ],\n        relevance: 0\n      },\n      FUNCTION_REF,\n      STRUCT_TYPE\n    ]\n  };\n}\n\nmodule.exports = gauss;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,kFAAkF,GACjF,oFAAoF,GACpF,gFAAgF,GAChF,qFAAqF,GACrF,sFAAsF,GACtF,4EAA4E,GAC5E,uFAAuF,GACvF,6EAA6E,GAC7E,oFAAoF,GACpF,sCAAsC;IAChDC,QAAQ,EAAE,sGAAsG,GACtG,wGAAwG,GACxG,iHAAiH,GACjH,8GAA8G,GAC9G,8GAA8G,GAC9G,6GAA6G,GAC7G,8GAA8G,GAC9G,+GAA+G,GAC/G,+GAA+G,GAC/G,8GAA8G,GAC9G,+GAA+G,GAC/G,+GAA+G,GAC/G,kHAAkH,GAClH,+GAA+G,GAC/G,+GAA+G,GAC/G,mHAAmH,GACnH,2GAA2G,GAC3G,gHAAgH,GAChH,wHAAwH,GACxH,8GAA8G,GAC9G,+GAA+G,GAC/G,+GAA+G,GAC/G,yGAAyG,GACzG,2GAA2G,GAC3G,iHAAiH,GACjH,kHAAkH,GAClH,qHAAqH,GACrH,yHAAyH,GACzH,4HAA4H,GAC5H,2GAA2G,GAC3G,6GAA6G,GAC7G,sHAAsH,GACtH,kHAAkH,GAClH,qHAAqH,GACrH,2HAA2H,GAC3H,kHAAkH,GAClH,sHAAsH,GACtH,oHAAoH,GACpH,uHAAuH,GACvH,gHAAgH,GAChH,wHAAwH,GACxH,mHAAmH,GACnH,qHAAqH,GACrH,0HAA0H,GAC1H,0HAA0H,GAC1H,yHAAyH,GACzH,0HAA0H,GAC1H,yHAAyH,GACzH,kHAAkH,GAClH,8HAA8H,GAC9H,4HAA4H,GAC5H,8HAA8H,GAC9H,6HAA6H,GAC7H,uHAAuH,GACvH,sHAAsH,GACtH,8HAA8H,GAC9H,oHAAoH,GACpH,mHAAmH,GACnH,mHAAmH,GACnH,mHAAmH,GACnH,oHAAoH,GACpH,uHAAuH,GACvH,qHAAqH,GACrH,0HAA0H,GAC1H,8HAA8H,GAC9H,8HAA8H,GAC9H,sHAAsH,GACtH,4HAA4H,GAC5H,2HAA2H,GAC3H,qHAAqH,GACrH,0HAA0H,GAC1H,wHAAwH,GACxH,yHAAyH,GACzH,uHAAuH,GACvH,wHAAwH,GACxH,2EAA2E,GAC3E,iGAAiG,GACjG,sHAAsH,GACtH,oHAAoH,GACpH,mHAAmH,GACnH,qHAAqH,GACrH,4HAA4H,GAC5H,oHAAoH,GACpH,qHAAqH,GACrH,SAAS;IACnBC,OAAO,EAAE,yGAAyG,GACzG,qGAAqG,GACrG,gGAAgG,GAChG,4GAA4G,GAC5G;EACX,CAAC;EAED,MAAMC,eAAe,GAAGL,IAAI,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAE9C,MAAMC,YAAY,GAClB;IACEC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE;MACR,cAAc,EAAE;IAClB,CAAC;IACDC,QAAQ,EAAE,CACR;MACEH,KAAK,EAAE,MAAM;MACbI,SAAS,EAAE;IACb,CAAC,EACD;MACEC,aAAa,EAAE,SAAS;MACxBJ,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE;QACR,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE,CACR;QACEJ,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE,GAAG;QACRK,OAAO,EAAE;MACX,CAAC;IAEL,CAAC,EACDf,IAAI,CAACgB,mBAAmB,EACxBhB,IAAI,CAACiB,oBAAoB,EACzBZ,eAAe;EAEnB,CAAC;EAED,MAAMa,WAAW,GACjB;IACET,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,CACR;MACEJ,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAET,IAAI,CAACmB,mBAAmB;MAC/BN,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;;EAED;EACA,MAAMO,YAAY,GAAG,CACnB;IACEZ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTW,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,IAAI;IACpBV,SAAS,EAAE,CAAC;IACZD,QAAQ,EAAE,CACR;MAAE;MACAJ,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACDT,IAAI,CAACwB,aAAa,EAClBxB,IAAI,CAACiB,oBAAoB,EACzBZ,eAAe,EACfa,WAAW;EAEf,CAAC,CACF;EAED,MAAMO,YAAY,GAClB;IACEjB,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAET,IAAI,CAACmB,mBAAmB;IAC/BN,SAAS,EAAE;EACb,CAAC;EAED,MAAMa,UAAU,GAAG,SAAAA,CAASZ,aAAa,EAAEJ,GAAG,EAAEiB,QAAQ,EAAE;IACxD,MAAMC,IAAI,GAAG5B,IAAI,CAAC6B,OAAO,CACvB;MACErB,SAAS,EAAE,UAAU;MACrBM,aAAa,EAAEA,aAAa;MAC5BJ,GAAG,EAAEA,GAAG;MACRY,UAAU,EAAE,IAAI;MAChBV,QAAQ,EAAE,EAAE,CAACkB,MAAM,CAACV,YAAY;IAClC,CAAC,EACDO,QAAQ,IAAI,CAAC,CACf,CAAC;IACDC,IAAI,CAAChB,QAAQ,CAACmB,IAAI,CAACN,YAAY,CAAC;IAChCG,IAAI,CAAChB,QAAQ,CAACmB,IAAI,CAAC/B,IAAI,CAACwB,aAAa,CAAC;IACtCI,IAAI,CAAChB,QAAQ,CAACmB,IAAI,CAAC/B,IAAI,CAACiB,oBAAoB,CAAC;IAC7CW,IAAI,CAAChB,QAAQ,CAACmB,IAAI,CAAC1B,eAAe,CAAC;IACnC,OAAOuB,IAAI;EACb,CAAC;EAED,MAAMI,YAAY,GAClB;IAAE;IACAxB,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,MAAM,GAAGR,QAAQ,CAACE,QAAQ,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG;EAC3D,CAAC;EAED,MAAMC,UAAU,GAChB;IACE3B,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRE,QAAQ,EAAE,CAACZ,IAAI,CAACoC,gBAAgB,CAAC;IACjCvB,SAAS,EAAE;EACb,CAAC;EAED,MAAMwB,YAAY,GAClB;IACE;IACA5B,KAAK,EAAET,IAAI,CAACmB,mBAAmB,GAAG,SAAS;IAC3CmB,WAAW,EAAE,IAAI;IACjB3B,QAAQ,EAAEV,QAAQ;IAClBY,SAAS,EAAE,CAAC;IACZD,QAAQ,EAAE,CACR;MACEE,aAAa,EAAEb,QAAQ,CAACC;IAC1B,CAAC,EACD8B,YAAY,EACZ;MAAE;MACAxB,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAET,IAAI,CAACmB,mBAAmB;MAC/BN,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EAED,MAAM0B,mBAAmB,GACzB;IACE;IACA9B,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTG,SAAS,EAAE,CAAC;IACZF,QAAQ,EAAE;MACRR,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;MAC3BC,OAAO,EAAEH,QAAQ,CAACG;IACpB,CAAC;IACDQ,QAAQ,EAAE,CACRZ,IAAI,CAACwB,aAAa,EAClBxB,IAAI,CAACiB,oBAAoB,EACzBZ,eAAe,EACf2B,YAAY,EACZK,YAAY,EACZF,UAAU,EACV,MAAM;EAEV,CAAC;EAEDE,YAAY,CAACzB,QAAQ,CAACmB,IAAI,CAACQ,mBAAmB,CAAC;EAE/C,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,gBAAgB,EAAE,IAAI;IAAE;IACxB/B,QAAQ,EAAEV,QAAQ;IAClBc,OAAO,EAAE,sBAAsB;IAC/BH,QAAQ,EAAE,CACRZ,IAAI,CAACwB,aAAa,EAClBxB,IAAI,CAACgB,mBAAmB,EACxBhB,IAAI,CAACiB,oBAAoB,EACzBZ,eAAe,EACf8B,UAAU,EACV5B,YAAY,EACZ;MACEC,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACDiB,UAAU,CAAC,cAAc,EAAE,GAAG,CAAC,EAC/BA,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,EACrB;MACEZ,aAAa,EAAE,eAAe;MAC9BJ,GAAG,EAAE,GAAG;MACR;MACAG,SAAS,EAAE,CAAC;MACZD,QAAQ,EAAE,CACRZ,IAAI,CAACiB,oBAAoB,EACzBZ,eAAe,EACfkC,mBAAmB;IAEvB,CAAC,EACD;MAAE;MACA;MACAI,QAAQ,EAAE,CACR;QACElC,KAAK,EAAET,IAAI,CAACmB,mBAAmB,GAAG,KAAK,GAAGnB,IAAI,CAACmB;MACjD,CAAC,EACD;QACEV,KAAK,EAAET,IAAI,CAACmB,mBAAmB,GAAG;MACpC,CAAC,CACF;MACDN,SAAS,EAAE;IACb,CAAC,EACDwB,YAAY,EACZnB,WAAW;EAEf,CAAC;AACH;AAEA0B,MAAM,CAACC,OAAO,GAAG9C,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}