{"ast": null, "code": "'use strict';\n\nvar refractorC = require('./c.js');\nmodule.exports = hlsl;\nhlsl.displayName = 'hlsl';\nhlsl.aliases = [];\nfunction hlsl(Prism) {\n  Prism.register(refractorC);\n  Prism.languages.hlsl = Prism.languages.extend('c', {\n    // Regarding keywords and class names:\n    // The list of all keywords was split into 'keyword' and 'class-name' tokens based on whether they are capitalized.\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-keywords\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-reserved-words\n    'class-name': [Prism.languages.c['class-name'], /\\b(?:AppendStructuredBuffer|BlendState|Buffer|ByteAddressBuffer|CompileShader|ComputeShader|ConsumeStructuredBuffer|DepthStencilState|DepthStencilView|DomainShader|GeometryShader|Hullshader|InputPatch|LineStream|OutputPatch|PixelShader|PointStream|RWBuffer|RWByteAddressBuffer|RWStructuredBuffer|RWTexture(?:1D|1DArray|2D|2DArray|3D)|RasterizerState|RenderTargetView|SamplerComparisonState|SamplerState|StructuredBuffer|Texture(?:1D|1DArray|2D|2DArray|2DMS|2DMSArray|3D|Cube|CubeArray)|TriangleStream|VertexShader)\\b/],\n    keyword: [\n    // HLSL keyword\n    /\\b(?:asm|asm_fragment|auto|break|case|catch|cbuffer|centroid|char|class|column_major|compile|compile_fragment|const|const_cast|continue|default|delete|discard|do|dynamic_cast|else|enum|explicit|export|extern|for|friend|fxgroup|goto|groupshared|if|in|inline|inout|interface|line|lineadj|linear|long|matrix|mutable|namespace|new|nointerpolation|noperspective|operator|out|packoffset|pass|pixelfragment|point|precise|private|protected|public|register|reinterpret_cast|return|row_major|sample|sampler|shared|short|signed|sizeof|snorm|stateblock|stateblock_state|static|static_cast|string|struct|switch|tbuffer|technique|technique10|technique11|template|texture|this|throw|triangle|triangleadj|try|typedef|typename|uniform|union|unorm|unsigned|using|vector|vertexfragment|virtual|void|volatile|while)\\b/,\n    // scalar, vector, and matrix types\n    /\\b(?:bool|double|dword|float|half|int|min(?:10float|12int|16(?:float|int|uint))|uint)(?:[1-4](?:x[1-4])?)?\\b/],\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-grammar#floating-point-numbers\n    number: /(?:(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?|\\b0x[\\da-fA-F]+)[fFhHlLuU]?\\b/,\n    boolean: /\\b(?:false|true)\\b/\n  });\n}", "map": {"version": 3, "names": ["refractorC", "require", "module", "exports", "hlsl", "displayName", "aliases", "Prism", "register", "languages", "extend", "c", "keyword", "number", "boolean"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/hlsl.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = hlsl\nhlsl.displayName = 'hlsl'\nhlsl.aliases = []\nfunction hlsl(Prism) {\n  Prism.register(refractorC)\n  Prism.languages.hlsl = Prism.languages.extend('c', {\n    // Regarding keywords and class names:\n    // The list of all keywords was split into 'keyword' and 'class-name' tokens based on whether they are capitalized.\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-keywords\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-reserved-words\n    'class-name': [\n      Prism.languages.c['class-name'],\n      /\\b(?:AppendStructuredBuffer|BlendState|Buffer|ByteAddressBuffer|CompileShader|ComputeShader|ConsumeStructuredBuffer|DepthStencilState|DepthStencilView|DomainShader|GeometryShader|Hullshader|InputPatch|LineStream|OutputPatch|PixelShader|PointStream|RWBuffer|RWByteAddressBuffer|RWStructuredBuffer|RWTexture(?:1D|1DArray|2D|2DArray|3D)|RasterizerState|RenderTargetView|SamplerComparisonState|SamplerState|StructuredBuffer|Texture(?:1D|1DArray|2D|2DArray|2DMS|2DMSArray|3D|Cube|CubeArray)|TriangleStream|VertexShader)\\b/\n    ],\n    keyword: [\n      // HLSL keyword\n      /\\b(?:asm|asm_fragment|auto|break|case|catch|cbuffer|centroid|char|class|column_major|compile|compile_fragment|const|const_cast|continue|default|delete|discard|do|dynamic_cast|else|enum|explicit|export|extern|for|friend|fxgroup|goto|groupshared|if|in|inline|inout|interface|line|lineadj|linear|long|matrix|mutable|namespace|new|nointerpolation|noperspective|operator|out|packoffset|pass|pixelfragment|point|precise|private|protected|public|register|reinterpret_cast|return|row_major|sample|sampler|shared|short|signed|sizeof|snorm|stateblock|stateblock_state|static|static_cast|string|struct|switch|tbuffer|technique|technique10|technique11|template|texture|this|throw|triangle|triangleadj|try|typedef|typename|uniform|union|unorm|unsigned|using|vector|vertexfragment|virtual|void|volatile|while)\\b/, // scalar, vector, and matrix types\n      /\\b(?:bool|double|dword|float|half|int|min(?:10float|12int|16(?:float|int|uint))|uint)(?:[1-4](?:x[1-4])?)?\\b/\n    ],\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-grammar#floating-point-numbers\n    number:\n      /(?:(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?|\\b0x[\\da-fA-F]+)[fFhHlLuU]?\\b/,\n    boolean: /\\b(?:false|true)\\b/\n  })\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClCC,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,QAAQ,CAACR,UAAU,CAAC;EAC1BO,KAAK,CAACE,SAAS,CAACL,IAAI,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,GAAG,EAAE;IACjD;IACA;IACA;IACA;IACA,YAAY,EAAE,CACZH,KAAK,CAACE,SAAS,CAACE,CAAC,CAAC,YAAY,CAAC,EAC/B,sgBAAsgB,CACvgB;IACDC,OAAO,EAAE;IACP;IACA,+xBAA+xB;IAAE;IACjyB,8GAA8G,CAC/G;IACD;IACAC,MAAM,EACJ,+EAA+E;IACjFC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}