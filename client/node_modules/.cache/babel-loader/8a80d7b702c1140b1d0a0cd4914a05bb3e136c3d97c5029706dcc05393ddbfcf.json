{"ast": null, "code": "'use strict';\n\nmodule.exports = clike;\nclike.displayName = 'clike';\nclike.aliases = [];\nfunction clike(Prism) {\n  Prism.languages.clike = {\n    comment: [{\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      lookbehind: true,\n      greedy: true\n    }, {\n      pattern: /(^|[^\\\\:])\\/\\/.*/,\n      lookbehind: true,\n      greedy: true\n    }],\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n      lookbehind: true,\n      inside: {\n        punctuation: /[.\\\\]/\n      }\n    },\n    keyword: /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b\\w+(?=\\()/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n    punctuation: /[{}[\\];(),.:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "clike", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "string", "inside", "punctuation", "keyword", "boolean", "function", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/clike.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = clike\nclike.displayName = 'clike'\nclike.aliases = []\nfunction clike(Prism) {\n  Prism.languages.clike = {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n      lookbehind: true,\n      inside: {\n        punctuation: /[.\\\\]/\n      }\n    },\n    keyword:\n      /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b\\w+(?=\\()/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE,CACP;MACEC,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,EACD;MACEF,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,gDAAgD;MACzDE,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZF,OAAO,EACL,0FAA0F;MAC5FC,UAAU,EAAE,IAAI;MAChBG,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EACL,4GAA4G;IAC9GC,OAAO,EAAE,oBAAoB;IAC7BC,QAAQ,EAAE,aAAa;IACvBC,MAAM,EAAE,2DAA2D;IACnEC,QAAQ,EAAE,8CAA8C;IACxDL,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}