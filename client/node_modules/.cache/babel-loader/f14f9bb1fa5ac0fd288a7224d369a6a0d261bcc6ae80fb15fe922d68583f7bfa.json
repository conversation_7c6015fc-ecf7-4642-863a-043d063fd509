{"ast": null, "code": "'use strict';\n\nmodule.exports = Schema;\nvar proto = Schema.prototype;\nproto.space = null;\nproto.normal = {};\nproto.property = {};\nfunction Schema(property, normal, space) {\n  this.property = property;\n  this.normal = normal;\n  if (space) {\n    this.space = space;\n  }\n}", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON>", "proto", "prototype", "space", "normal", "property"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/lib/util/schema.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = Schema\n\nvar proto = Schema.prototype\n\nproto.space = null\nproto.normal = {}\nproto.property = {}\n\nfunction Schema(property, normal, space) {\n  this.property = property\n  this.normal = normal\n\n  if (space) {\n    this.space = space\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AAEvB,IAAIC,KAAK,GAAGD,MAAM,CAACE,SAAS;AAE5BD,KAAK,CAACE,KAAK,GAAG,IAAI;AAClBF,KAAK,CAACG,MAAM,GAAG,CAAC,CAAC;AACjBH,KAAK,CAACI,QAAQ,GAAG,CAAC,CAAC;AAEnB,SAASL,MAAMA,CAACK,QAAQ,EAAED,MAAM,EAAED,KAAK,EAAE;EACvC,IAAI,CAACE,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACD,MAAM,GAAGA,MAAM;EAEpB,IAAID,KAAK,EAAE;IACT,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}