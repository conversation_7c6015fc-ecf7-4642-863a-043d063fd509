{"ast": null, "code": "/*\nLanguage: Device Tree\nDescription: *.dts files used in the Linux kernel\nAuthor: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://elinux.org/Device_Tree_Reference\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction dts(hljs) {\n  const STRINGS = {\n    className: 'string',\n    variants: [hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      begin: '((u8?|U)|L)?\"'\n    }), {\n      begin: '(u8?|U)?R\"',\n      end: '\"',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: '\\'\\\\\\\\?.',\n      end: '\\'',\n      illegal: '.'\n    }]\n  };\n  const NUMBERS = {\n    className: 'number',\n    variants: [{\n      begin: '\\\\b(\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)(u|U|l|L|ul|UL|f|F)'\n    }, {\n      begin: hljs.C_NUMBER_RE\n    }],\n    relevance: 0\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: {\n      'meta-keyword': 'if else elif endif define undef ifdef ifndef'\n    },\n    contains: [{\n      begin: /\\\\\\n/,\n      relevance: 0\n    }, {\n      beginKeywords: 'include',\n      end: '$',\n      keywords: {\n        'meta-keyword': 'include'\n      },\n      contains: [hljs.inherit(STRINGS, {\n        className: 'meta-string'\n      }), {\n        className: 'meta-string',\n        begin: '<',\n        end: '>',\n        illegal: '\\\\n'\n      }]\n    }, STRINGS, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n  };\n  const DTS_REFERENCE = {\n    className: 'variable',\n    begin: /&[a-z\\d_]*\\b/\n  };\n  const DTS_KEYWORD = {\n    className: 'meta-keyword',\n    begin: '/[a-z][a-z\\\\d-]*/'\n  };\n  const DTS_LABEL = {\n    className: 'symbol',\n    begin: '^\\\\s*[a-zA-Z_][a-zA-Z\\\\d_]*:'\n  };\n  const DTS_CELL_PROPERTY = {\n    className: 'params',\n    begin: '<',\n    end: '>',\n    contains: [NUMBERS, DTS_REFERENCE]\n  };\n  const DTS_NODE = {\n    className: 'class',\n    begin: /[a-zA-Z_][a-zA-Z\\d_@]*\\s\\{/,\n    end: /[{;=]/,\n    returnBegin: true,\n    excludeEnd: true\n  };\n  const DTS_ROOT_NODE = {\n    className: 'class',\n    begin: '/\\\\s*\\\\{',\n    end: /\\};/,\n    relevance: 10,\n    contains: [DTS_REFERENCE, DTS_KEYWORD, DTS_LABEL, DTS_NODE, DTS_CELL_PROPERTY, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, NUMBERS, STRINGS]\n  };\n  return {\n    name: 'Device Tree',\n    keywords: \"\",\n    contains: [DTS_ROOT_NODE, DTS_REFERENCE, DTS_KEYWORD, DTS_LABEL, DTS_NODE, DTS_CELL_PROPERTY, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, NUMBERS, STRINGS, PREPROCESSOR, {\n      begin: hljs.IDENT_RE + '::',\n      keywords: \"\"\n    }]\n  };\n}\nmodule.exports = dts;", "map": {"version": 3, "names": ["dts", "hljs", "STRINGS", "className", "variants", "inherit", "QUOTE_STRING_MODE", "begin", "end", "contains", "BACKSLASH_ESCAPE", "illegal", "NUMBERS", "C_NUMBER_RE", "relevance", "PREPROCESSOR", "keywords", "beginKeywords", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "DTS_REFERENCE", "DTS_KEYWORD", "DTS_LABEL", "DTS_CELL_PROPERTY", "DTS_NODE", "returnBegin", "excludeEnd", "DTS_ROOT_NODE", "name", "IDENT_RE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/dts.js"], "sourcesContent": ["/*\nLanguage: Device Tree\nDescription: *.dts files used in the Linux kernel\nAuthor: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://elinux.org/Device_Tree_Reference\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction dts(hljs) {\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        begin: '((u8?|U)|L)?\"'\n      }),\n      {\n        begin: '(u8?|U)?R\"',\n        end: '\"',\n        contains: [hljs.BACKSLASH_ESCAPE]\n      },\n      {\n        begin: '\\'\\\\\\\\?.',\n        end: '\\'',\n        illegal: '.'\n      }\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)(u|U|l|L|ul|UL|f|F)'\n      },\n      {\n        begin: hljs.C_NUMBER_RE\n      }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: {\n      'meta-keyword': 'if else elif endif define undef ifdef ifndef'\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      {\n        beginKeywords: 'include',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'include'\n        },\n        contains: [\n          hljs.inherit(STRINGS, {\n            className: 'meta-string'\n          }),\n          {\n            className: 'meta-string',\n            begin: '<',\n            end: '>',\n            illegal: '\\\\n'\n          }\n        ]\n      },\n      STRINGS,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const DTS_REFERENCE = {\n    className: 'variable',\n    begin: /&[a-z\\d_]*\\b/\n  };\n\n  const DTS_KEYWORD = {\n    className: 'meta-keyword',\n    begin: '/[a-z][a-z\\\\d-]*/'\n  };\n\n  const DTS_LABEL = {\n    className: 'symbol',\n    begin: '^\\\\s*[a-zA-Z_][a-zA-Z\\\\d_]*:'\n  };\n\n  const DTS_CELL_PROPERTY = {\n    className: 'params',\n    begin: '<',\n    end: '>',\n    contains: [\n      NUMBERS,\n      DTS_REFERENCE\n    ]\n  };\n\n  const DTS_NODE = {\n    className: 'class',\n    begin: /[a-zA-Z_][a-zA-Z\\d_@]*\\s\\{/,\n    end: /[{;=]/,\n    returnBegin: true,\n    excludeEnd: true\n  };\n\n  const DTS_ROOT_NODE = {\n    className: 'class',\n    begin: '/\\\\s*\\\\{',\n    end: /\\};/,\n    relevance: 10,\n    contains: [\n      DTS_REFERENCE,\n      DTS_KEYWORD,\n      DTS_LABEL,\n      DTS_NODE,\n      DTS_CELL_PROPERTY,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      NUMBERS,\n      STRINGS\n    ]\n  };\n\n  return {\n    name: 'Device Tree',\n    keywords: \"\",\n    contains: [\n      DTS_ROOT_NODE,\n      DTS_REFERENCE,\n      DTS_KEYWORD,\n      DTS_LABEL,\n      DTS_NODE,\n      DTS_CELL_PROPERTY,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      NUMBERS,\n      STRINGS,\n      PREPROCESSOR,\n      {\n        begin: hljs.IDENT_RE + '::',\n        keywords: \"\"\n      }\n    ]\n  };\n}\n\nmodule.exports = dts;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACRH,IAAI,CAACI,OAAO,CAACJ,IAAI,CAACK,iBAAiB,EAAE;MACnCC,KAAK,EAAE;IACT,CAAC,CAAC,EACF;MACEA,KAAK,EAAE,YAAY;MACnBC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CAACR,IAAI,CAACS,gBAAgB;IAClC,CAAC,EACD;MACEH,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE,IAAI;MACTG,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;EAED,MAAMC,OAAO,GAAG;IACdT,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MACEG,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAEN,IAAI,CAACY;IACd,CAAC,CACF;IACDC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBZ,SAAS,EAAE,MAAM;IACjBI,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRQ,QAAQ,EAAE;MACR,cAAc,EAAE;IAClB,CAAC;IACDP,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE,MAAM;MACbO,SAAS,EAAE;IACb,CAAC,EACD;MACEG,aAAa,EAAE,SAAS;MACxBT,GAAG,EAAE,GAAG;MACRQ,QAAQ,EAAE;QACR,cAAc,EAAE;MAClB,CAAC;MACDP,QAAQ,EAAE,CACRR,IAAI,CAACI,OAAO,CAACH,OAAO,EAAE;QACpBC,SAAS,EAAE;MACb,CAAC,CAAC,EACF;QACEA,SAAS,EAAE,aAAa;QACxBI,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE,GAAG;QACRG,OAAO,EAAE;MACX,CAAC;IAEL,CAAC,EACDT,OAAO,EACPD,IAAI,CAACiB,mBAAmB,EACxBjB,IAAI,CAACkB,oBAAoB;EAE7B,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBjB,SAAS,EAAE,UAAU;IACrBI,KAAK,EAAE;EACT,CAAC;EAED,MAAMc,WAAW,GAAG;IAClBlB,SAAS,EAAE,cAAc;IACzBI,KAAK,EAAE;EACT,CAAC;EAED,MAAMe,SAAS,GAAG;IAChBnB,SAAS,EAAE,QAAQ;IACnBI,KAAK,EAAE;EACT,CAAC;EAED,MAAMgB,iBAAiB,GAAG;IACxBpB,SAAS,EAAE,QAAQ;IACnBI,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACRG,OAAO,EACPQ,aAAa;EAEjB,CAAC;EAED,MAAMI,QAAQ,GAAG;IACfrB,SAAS,EAAE,OAAO;IAClBI,KAAK,EAAE,4BAA4B;IACnCC,GAAG,EAAE,OAAO;IACZiB,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE;EACd,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBxB,SAAS,EAAE,OAAO;IAClBI,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,KAAK;IACVM,SAAS,EAAE,EAAE;IACbL,QAAQ,EAAE,CACRW,aAAa,EACbC,WAAW,EACXC,SAAS,EACTE,QAAQ,EACRD,iBAAiB,EACjBtB,IAAI,CAACiB,mBAAmB,EACxBjB,IAAI,CAACkB,oBAAoB,EACzBP,OAAO,EACPV,OAAO;EAEX,CAAC;EAED,OAAO;IACL0B,IAAI,EAAE,aAAa;IACnBZ,QAAQ,EAAE,EAAE;IACZP,QAAQ,EAAE,CACRkB,aAAa,EACbP,aAAa,EACbC,WAAW,EACXC,SAAS,EACTE,QAAQ,EACRD,iBAAiB,EACjBtB,IAAI,CAACiB,mBAAmB,EACxBjB,IAAI,CAACkB,oBAAoB,EACzBP,OAAO,EACPV,OAAO,EACPa,YAAY,EACZ;MACER,KAAK,EAAEN,IAAI,CAAC4B,QAAQ,GAAG,IAAI;MAC3Bb,QAAQ,EAAE;IACZ,CAAC;EAEL,CAAC;AACH;AAEAc,MAAM,CAACC,OAAO,GAAG/B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}