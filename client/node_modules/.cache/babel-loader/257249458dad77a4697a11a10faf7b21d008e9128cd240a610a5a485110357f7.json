{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: ActionScript\nAuthor: <PERSON> <<EMAIL>>\nCategory: scripting\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction actionscript(hljs) {\n  const IDENT_RE = /[a-zA-Z_$][a-zA-Z0-9_$]*/;\n  const IDENT_FUNC_RETURN_TYPE_RE = /([*]|[a-zA-Z_$][a-zA-Z0-9_$]*)/;\n  const AS3_REST_ARG_MODE = {\n    className: 'rest_arg',\n    begin: /[.]{3}/,\n    end: IDENT_RE,\n    relevance: 10\n  };\n  return {\n    name: 'ActionScript',\n    aliases: ['as'],\n    keywords: {\n      keyword: 'as break case catch class const continue default delete do dynamic each ' + 'else extends final finally for function get if implements import in include ' + 'instanceof interface internal is namespace native new override package private ' + 'protected public return set static super switch this throw try typeof use var void ' + 'while with',\n      literal: 'true false null undefined'\n    },\n    contains: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.C_NUMBER_MODE, {\n      className: 'class',\n      beginKeywords: 'package',\n      end: /\\{/,\n      contains: [hljs.TITLE_MODE]\n    }, {\n      className: 'class',\n      beginKeywords: 'class interface',\n      end: /\\{/,\n      excludeEnd: true,\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.TITLE_MODE]\n    }, {\n      className: 'meta',\n      beginKeywords: 'import include',\n      end: /;/,\n      keywords: {\n        'meta-keyword': 'import include'\n      }\n    }, {\n      className: 'function',\n      beginKeywords: 'function',\n      end: /[{;]/,\n      excludeEnd: true,\n      illegal: /\\S/,\n      contains: [hljs.TITLE_MODE, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, AS3_REST_ARG_MODE]\n      }, {\n        begin: concat(/:\\s*/, IDENT_FUNC_RETURN_TYPE_RE)\n      }]\n    }, hljs.METHOD_GUARD],\n    illegal: /#/\n  };\n}\nmodule.exports = actionscript;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "actionscript", "hljs", "IDENT_RE", "IDENT_FUNC_RETURN_TYPE_RE", "AS3_REST_ARG_MODE", "className", "begin", "end", "relevance", "name", "aliases", "keywords", "keyword", "literal", "contains", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "C_NUMBER_MODE", "beginKeywords", "TITLE_MODE", "excludeEnd", "illegal", "METHOD_GUARD", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/actionscript.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: ActionScript\nAuthor: <PERSON> <<EMAIL>>\nCategory: scripting\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction actionscript(hljs) {\n  const IDENT_RE = /[a-zA-Z_$][a-zA-Z0-9_$]*/;\n  const IDENT_FUNC_RETURN_TYPE_RE = /([*]|[a-zA-Z_$][a-zA-Z0-9_$]*)/;\n\n  const AS3_REST_ARG_MODE = {\n    className: 'rest_arg',\n    begin: /[.]{3}/,\n    end: IDENT_RE,\n    relevance: 10\n  };\n\n  return {\n    name: 'ActionScript',\n    aliases: [ 'as' ],\n    keywords: {\n      keyword: 'as break case catch class const continue default delete do dynamic each ' +\n        'else extends final finally for function get if implements import in include ' +\n        'instanceof interface internal is namespace native new override package private ' +\n        'protected public return set static super switch this throw try typeof use var void ' +\n        'while with',\n      literal: 'true false null undefined'\n    },\n    contains: [\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'package',\n        end: /\\{/,\n        contains: [ hljs.TITLE_MODE ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.TITLE_MODE\n        ]\n      },\n      {\n        className: 'meta',\n        beginKeywords: 'import include',\n        end: /;/,\n        keywords: { 'meta-keyword': 'import include' }\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /[{;]/,\n        excludeEnd: true,\n        illegal: /\\S/,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            contains: [\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              hljs.C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              AS3_REST_ARG_MODE\n            ]\n          },\n          { begin: concat(/:\\s*/, IDENT_FUNC_RETURN_TYPE_RE) }\n        ]\n      },\n      hljs.METHOD_GUARD\n    ],\n    illegal: /#/\n  };\n}\n\nmodule.exports = actionscript;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,YAAYA,CAACC,IAAI,EAAE;EAC1B,MAAMC,QAAQ,GAAG,0BAA0B;EAC3C,MAAMC,yBAAyB,GAAG,gCAAgC;EAElE,MAAMC,iBAAiB,GAAG;IACxBC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,QAAQ;IACfC,GAAG,EAAEL,QAAQ;IACbM,SAAS,EAAE;EACb,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,CAAE,IAAI,CAAE;IACjBC,QAAQ,EAAE;MACRC,OAAO,EAAE,0EAA0E,GACjF,8EAA8E,GAC9E,iFAAiF,GACjF,qFAAqF,GACrF,YAAY;MACdC,OAAO,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE,CACRb,IAAI,CAACc,gBAAgB,EACrBd,IAAI,CAACe,iBAAiB,EACtBf,IAAI,CAACgB,mBAAmB,EACxBhB,IAAI,CAACiB,oBAAoB,EACzBjB,IAAI,CAACkB,aAAa,EAClB;MACEd,SAAS,EAAE,OAAO;MAClBe,aAAa,EAAE,SAAS;MACxBb,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAE,CAAEb,IAAI,CAACoB,UAAU;IAC7B,CAAC,EACD;MACEhB,SAAS,EAAE,OAAO;MAClBe,aAAa,EAAE,iBAAiB;MAChCb,GAAG,EAAE,IAAI;MACTe,UAAU,EAAE,IAAI;MAChBR,QAAQ,EAAE,CACR;QAAEM,aAAa,EAAE;MAAqB,CAAC,EACvCnB,IAAI,CAACoB,UAAU;IAEnB,CAAC,EACD;MACEhB,SAAS,EAAE,MAAM;MACjBe,aAAa,EAAE,gBAAgB;MAC/Bb,GAAG,EAAE,GAAG;MACRI,QAAQ,EAAE;QAAE,cAAc,EAAE;MAAiB;IAC/C,CAAC,EACD;MACEN,SAAS,EAAE,UAAU;MACrBe,aAAa,EAAE,UAAU;MACzBb,GAAG,EAAE,MAAM;MACXe,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,IAAI;MACbT,QAAQ,EAAE,CACRb,IAAI,CAACoB,UAAU,EACf;QACEhB,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,IAAI;QACTO,QAAQ,EAAE,CACRb,IAAI,CAACc,gBAAgB,EACrBd,IAAI,CAACe,iBAAiB,EACtBf,IAAI,CAACgB,mBAAmB,EACxBhB,IAAI,CAACiB,oBAAoB,EACzBd,iBAAiB;MAErB,CAAC,EACD;QAAEE,KAAK,EAAEZ,MAAM,CAAC,MAAM,EAAES,yBAAyB;MAAE,CAAC;IAExD,CAAC,EACDF,IAAI,CAACuB,YAAY,CAClB;IACDD,OAAO,EAAE;EACX,CAAC;AACH;AAEAE,MAAM,CAACC,OAAO,GAAG1B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}