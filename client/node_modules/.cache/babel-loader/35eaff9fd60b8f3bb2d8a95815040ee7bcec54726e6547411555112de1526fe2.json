{"ast": null, "code": "/*\nLanguage: Processing\nDescription: Processing is a flexible software sketchbook and a language for learning how to code within the context of the visual arts.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://processing.org\nCategory: graphics\n*/\n\nfunction processing(hljs) {\n  return {\n    name: 'Processing',\n    keywords: {\n      keyword: 'BufferedReader PVector PFont PImage PGraphics HashMap boolean byte char color ' + 'double float int long String Array FloatDict FloatList IntDict IntList JSONArray JSONObject ' + 'Object StringDict StringList Table TableRow XML ' +\n      // Java keywords\n      'false synchronized int abstract float private char boolean static null if const ' + 'for true while long throw strictfp finally protected import native final return void ' + 'enum else break transient new catch instanceof byte super volatile case assert short ' + 'package default double public try this switch continue throws protected public private',\n      literal: 'P2D P3D HALF_PI PI QUARTER_PI TAU TWO_PI',\n      title: 'setup draw',\n      built_in: 'displayHeight displayWidth mouseY mouseX mousePressed pmouseX pmouseY key ' + 'keyCode pixels focused frameCount frameRate height width ' + 'size createGraphics beginDraw createShape loadShape PShape arc ellipse line point ' + 'quad rect triangle bezier bezierDetail bezierPoint bezierTangent curve curveDetail curvePoint ' + 'curveTangent curveTightness shape shapeMode beginContour beginShape bezierVertex curveVertex ' + 'endContour endShape quadraticVertex vertex ellipseMode noSmooth rectMode smooth strokeCap ' + 'strokeJoin strokeWeight mouseClicked mouseDragged mouseMoved mousePressed mouseReleased ' + 'mouseWheel keyPressed keyPressedkeyReleased keyTyped print println save saveFrame day hour ' + 'millis minute month second year background clear colorMode fill noFill noStroke stroke alpha ' + 'blue brightness color green hue lerpColor red saturation modelX modelY modelZ screenX screenY ' + 'screenZ ambient emissive shininess specular add createImage beginCamera camera endCamera frustum ' + 'ortho perspective printCamera printProjection cursor frameRate noCursor exit loop noLoop popStyle ' + 'pushStyle redraw binary boolean byte char float hex int str unbinary unhex join match matchAll nf ' + 'nfc nfp nfs split splitTokens trim append arrayCopy concat expand reverse shorten sort splice subset ' + 'box sphere sphereDetail createInput createReader loadBytes loadJSONArray loadJSONObject loadStrings ' + 'loadTable loadXML open parseXML saveTable selectFolder selectInput beginRaw beginRecord createOutput ' + 'createWriter endRaw endRecord PrintWritersaveBytes saveJSONArray saveJSONObject saveStream saveStrings ' + 'saveXML selectOutput popMatrix printMatrix pushMatrix resetMatrix rotate rotateX rotateY rotateZ scale ' + 'shearX shearY translate ambientLight directionalLight lightFalloff lights lightSpecular noLights normal ' + 'pointLight spotLight image imageMode loadImage noTint requestImage tint texture textureMode textureWrap ' + 'blend copy filter get loadPixels set updatePixels blendMode loadShader PShaderresetShader shader createFont ' + 'loadFont text textFont textAlign textLeading textMode textSize textWidth textAscent textDescent abs ceil ' + 'constrain dist exp floor lerp log mag map max min norm pow round sq sqrt acos asin atan atan2 cos degrees ' + 'radians sin tan noise noiseDetail noiseSeed random randomGaussian randomSeed'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE]\n  };\n}\nmodule.exports = processing;", "map": {"version": 3, "names": ["processing", "hljs", "name", "keywords", "keyword", "literal", "title", "built_in", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/processing.js"], "sourcesContent": ["/*\nLanguage: Processing\nDescription: Processing is a flexible software sketchbook and a language for learning how to code within the context of the visual arts.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://processing.org\nCategory: graphics\n*/\n\nfunction processing(hljs) {\n  return {\n    name: 'Processing',\n    keywords: {\n      keyword: 'BufferedReader PVector PFont PImage PGraphics HashMap boolean byte char color ' +\n        'double float int long String Array FloatDict FloatList IntDict IntList JSONArray JSONObject ' +\n        'Object StringDict StringList Table TableRow XML ' +\n        // Java keywords\n        'false synchronized int abstract float private char boolean static null if const ' +\n        'for true while long throw strictfp finally protected import native final return void ' +\n        'enum else break transient new catch instanceof byte super volatile case assert short ' +\n        'package default double public try this switch continue throws protected public private',\n      literal: 'P2D P3D HALF_PI PI QUARTER_PI TAU TWO_PI',\n      title: 'setup draw',\n      built_in: 'displayHeight displayWidth mouseY mouseX mousePressed pmouseX pmouseY key ' +\n        'keyCode pixels focused frameCount frameRate height width ' +\n        'size createGraphics beginDraw createShape loadShape PShape arc ellipse line point ' +\n        'quad rect triangle bezier bezierDetail bezierPoint bezierTangent curve curveDetail curvePoint ' +\n        'curveTangent curveTightness shape shapeMode beginContour beginShape bezierVertex curveVertex ' +\n        'endContour endShape quadraticVertex vertex ellipseMode noSmooth rectMode smooth strokeCap ' +\n        'strokeJoin strokeWeight mouseClicked mouseDragged mouseMoved mousePressed mouseReleased ' +\n        'mouseWheel keyPressed keyPressedkeyReleased keyTyped print println save saveFrame day hour ' +\n        'millis minute month second year background clear colorMode fill noFill noStroke stroke alpha ' +\n        'blue brightness color green hue lerpColor red saturation modelX modelY modelZ screenX screenY ' +\n        'screenZ ambient emissive shininess specular add createImage beginCamera camera endCamera frustum ' +\n        'ortho perspective printCamera printProjection cursor frameRate noCursor exit loop noLoop popStyle ' +\n        'pushStyle redraw binary boolean byte char float hex int str unbinary unhex join match matchAll nf ' +\n        'nfc nfp nfs split splitTokens trim append arrayCopy concat expand reverse shorten sort splice subset ' +\n        'box sphere sphereDetail createInput createReader loadBytes loadJSONArray loadJSONObject loadStrings ' +\n        'loadTable loadXML open parseXML saveTable selectFolder selectInput beginRaw beginRecord createOutput ' +\n        'createWriter endRaw endRecord PrintWritersaveBytes saveJSONArray saveJSONObject saveStream saveStrings ' +\n        'saveXML selectOutput popMatrix printMatrix pushMatrix resetMatrix rotate rotateX rotateY rotateZ scale ' +\n        'shearX shearY translate ambientLight directionalLight lightFalloff lights lightSpecular noLights normal ' +\n        'pointLight spotLight image imageMode loadImage noTint requestImage tint texture textureMode textureWrap ' +\n        'blend copy filter get loadPixels set updatePixels blendMode loadShader PShaderresetShader shader createFont ' +\n        'loadFont text textFont textAlign textLeading textMode textSize textWidth textAscent textDescent abs ceil ' +\n        'constrain dist exp floor lerp log mag map max min norm pow round sq sqrt acos asin atan atan2 cos degrees ' +\n        'radians sin tan noise noiseDetail noiseSeed random randomGaussian randomSeed'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = processing;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO;IACLC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE;MACRC,OAAO,EAAE,gFAAgF,GACvF,8FAA8F,GAC9F,kDAAkD;MAClD;MACA,kFAAkF,GAClF,uFAAuF,GACvF,uFAAuF,GACvF,wFAAwF;MAC1FC,OAAO,EAAE,0CAA0C;MACnDC,KAAK,EAAE,YAAY;MACnBC,QAAQ,EAAE,4EAA4E,GACpF,2DAA2D,GAC3D,oFAAoF,GACpF,gGAAgG,GAChG,+FAA+F,GAC/F,4FAA4F,GAC5F,0FAA0F,GAC1F,6FAA6F,GAC7F,+FAA+F,GAC/F,gGAAgG,GAChG,mGAAmG,GACnG,oGAAoG,GACpG,oGAAoG,GACpG,uGAAuG,GACvG,sGAAsG,GACtG,uGAAuG,GACvG,yGAAyG,GACzG,yGAAyG,GACzG,0GAA0G,GAC1G,0GAA0G,GAC1G,8GAA8G,GAC9G,2GAA2G,GAC3G,4GAA4G,GAC5G;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRP,IAAI,CAACQ,mBAAmB,EACxBR,IAAI,CAACS,oBAAoB,EACzBT,IAAI,CAACU,gBAAgB,EACrBV,IAAI,CAACW,iBAAiB,EACtBX,IAAI,CAACY,aAAa;EAEtB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGf,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}