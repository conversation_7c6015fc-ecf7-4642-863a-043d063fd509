{"ast": null, "code": "/*\nLanguage: SML (Standard ML)\nAuthor: <PERSON> <<EMAIL>>\nDescription: SML language definition.\nWebsite: https://www.smlnj.org\nOrigin: ocaml.js\nCategory: functional\n*/\nfunction sml(hljs) {\n  return {\n    name: 'SML (Standard ML)',\n    aliases: ['ml'],\n    keywords: {\n      $pattern: '[a-z_]\\\\w*!?',\n      keyword: /* according to Definition of Standard ML 97  */\n      'abstype and andalso as case datatype do else end eqtype ' + 'exception fn fun functor handle if in include infix infixr ' + 'let local nonfix of op open orelse raise rec sharing sig ' + 'signature struct structure then type val with withtype where while',\n      built_in: /* built-in types according to basis library */\n      'array bool char exn int list option order real ref string substring vector unit word',\n      literal: 'true false NONE SOME LESS EQUAL GREATER nil'\n    },\n    illegal: /\\/\\/|>>/,\n    contains: [{\n      className: 'literal',\n      begin: /\\[(\\|\\|)?\\]|\\(\\)/,\n      relevance: 0\n    }, hljs.COMMENT('\\\\(\\\\*', '\\\\*\\\\)', {\n      contains: ['self']\n    }), {\n      /* type variable */\n      className: 'symbol',\n      begin: '\\'[A-Za-z_](?!\\')[\\\\w\\']*'\n      /* the grammar is ambiguous on how 'a'b should be interpreted but not the compiler */\n    }, {\n      /* polymorphic variant */\n      className: 'type',\n      begin: '`[A-Z][\\\\w\\']*'\n    }, {\n      /* module or constructor */\n      className: 'type',\n      begin: '\\\\b[A-Z][\\\\w\\']*',\n      relevance: 0\n    }, {\n      /* don't color identifiers, but safely catch all identifiers with ' */\n      begin: '[a-z_]\\\\w*\\'[\\\\w\\']*'\n    }, hljs.inherit(hljs.APOS_STRING_MODE, {\n      className: 'string',\n      relevance: 0\n    }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    }), {\n      className: 'number',\n      begin: '\\\\b(0[xX][a-fA-F0-9_]+[Lln]?|' + '0[oO][0-7_]+[Lln]?|' + '0[bB][01_]+[Lln]?|' + '[0-9][0-9_]*([Lln]|(\\\\.[0-9_]*)?([eE][-+]?[0-9_]+)?)?)',\n      relevance: 0\n    }, {\n      begin: /[-=]>/ // relevance booster\n    }]\n  };\n}\nmodule.exports = sml;", "map": {"version": 3, "names": ["sml", "hljs", "name", "aliases", "keywords", "$pattern", "keyword", "built_in", "literal", "illegal", "contains", "className", "begin", "relevance", "COMMENT", "inherit", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/sml.js"], "sourcesContent": ["/*\nLanguage: SML (Standard ML)\nAuthor: <PERSON> <<EMAIL>>\nDescription: SML language definition.\nWebsite: https://www.smlnj.org\nOrigin: ocaml.js\nCategory: functional\n*/\nfunction sml(hljs) {\n  return {\n    name: 'SML (Standard ML)',\n    aliases: [ 'ml' ],\n    keywords: {\n      $pattern: '[a-z_]\\\\w*!?',\n      keyword:\n        /* according to Definition of Standard ML 97  */\n        'abstype and andalso as case datatype do else end eqtype ' +\n        'exception fn fun functor handle if in include infix infixr ' +\n        'let local nonfix of op open orelse raise rec sharing sig ' +\n        'signature struct structure then type val with withtype where while',\n      built_in:\n        /* built-in types according to basis library */\n        'array bool char exn int list option order real ref string substring vector unit word',\n      literal:\n        'true false NONE SOME LESS EQUAL GREATER nil'\n    },\n    illegal: /\\/\\/|>>/,\n    contains: [\n      {\n        className: 'literal',\n        begin: /\\[(\\|\\|)?\\]|\\(\\)/,\n        relevance: 0\n      },\n      hljs.COMMENT(\n        '\\\\(\\\\*',\n        '\\\\*\\\\)',\n        {\n          contains: [ 'self' ]\n        }\n      ),\n      { /* type variable */\n        className: 'symbol',\n        begin: '\\'[A-Za-z_](?!\\')[\\\\w\\']*'\n        /* the grammar is ambiguous on how 'a'b should be interpreted but not the compiler */\n      },\n      { /* polymorphic variant */\n        className: 'type',\n        begin: '`[A-Z][\\\\w\\']*'\n      },\n      { /* module or constructor */\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w\\']*',\n        relevance: 0\n      },\n      { /* don't color identifiers, but safely catch all identifiers with ' */\n        begin: '[a-z_]\\\\w*\\'[\\\\w\\']*'\n      },\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        className: 'string',\n        relevance: 0\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      }),\n      {\n        className: 'number',\n        begin:\n          '\\\\b(0[xX][a-fA-F0-9_]+[Lln]?|' +\n          '0[oO][0-7_]+[Lln]?|' +\n          '0[bB][01_]+[Lln]?|' +\n          '[0-9][0-9_]*([Lln]|(\\\\.[0-9_]*)?([eE][-+]?[0-9_]+)?)?)',\n        relevance: 0\n      },\n      {\n        begin: /[-=]>/ // relevance booster\n      }\n    ]\n  };\n}\n\nmodule.exports = sml;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,CAAE,IAAI,CAAE;IACjBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EACL;MACA,0DAA0D,GAC1D,6DAA6D,GAC7D,2DAA2D,GAC3D,oEAAoE;MACtEC,QAAQ,EACN;MACA,sFAAsF;MACxFC,OAAO,EACL;IACJ,CAAC;IACDC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,kBAAkB;MACzBC,SAAS,EAAE;IACb,CAAC,EACDZ,IAAI,CAACa,OAAO,CACV,QAAQ,EACR,QAAQ,EACR;MACEJ,QAAQ,EAAE,CAAE,MAAM;IACpB,CACF,CAAC,EACD;MAAE;MACAC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;MACP;IACF,CAAC,EACD;MAAE;MACAD,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC,EACD;MAAE;MACAD,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,kBAAkB;MACzBC,SAAS,EAAE;IACb,CAAC,EACD;MAAE;MACAD,KAAK,EAAE;IACT,CAAC,EACDX,IAAI,CAACc,OAAO,CAACd,IAAI,CAACe,gBAAgB,EAAE;MAClCL,SAAS,EAAE,QAAQ;MACnBE,SAAS,EAAE;IACb,CAAC,CAAC,EACFZ,IAAI,CAACc,OAAO,CAACd,IAAI,CAACgB,iBAAiB,EAAE;MACnCR,OAAO,EAAE;IACX,CAAC,CAAC,EACF;MACEE,SAAS,EAAE,QAAQ;MACnBC,KAAK,EACH,+BAA+B,GAC/B,qBAAqB,GACrB,oBAAoB,GACpB,wDAAwD;MAC1DC,SAAS,EAAE;IACb,CAAC,EACD;MACED,KAAK,EAAE,OAAO,CAAC;IACjB,CAAC;EAEL,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGnB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}