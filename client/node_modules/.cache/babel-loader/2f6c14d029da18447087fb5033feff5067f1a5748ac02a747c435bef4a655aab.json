{"ast": null, "code": "'use strict';\n\nmodule.exports = http;\nhttp.displayName = 'http';\nhttp.aliases = [];\nfunction http(Prism) {\n  ;\n  (function (Prism) {\n    /**\n     * @param {string} name\n     * @returns {RegExp}\n     */\n    function headerValueOf(name) {\n      return RegExp('(^(?:' + name + '):[ \\t]*(?![ \\t]))[^]+', 'i');\n    }\n    Prism.languages.http = {\n      'request-line': {\n        pattern: /^(?:CONNECT|DELETE|GET|HEAD|OPTIONS|PATCH|POST|PRI|PUT|SEARCH|TRACE)\\s(?:https?:\\/\\/|\\/)\\S*\\sHTTP\\/[\\d.]+/m,\n        inside: {\n          // HTTP Method\n          method: {\n            pattern: /^[A-Z]+\\b/,\n            alias: 'property'\n          },\n          // Request Target e.g. http://example.com, /path/to/file\n          'request-target': {\n            pattern: /^(\\s)(?:https?:\\/\\/|\\/)\\S*(?=\\s)/,\n            lookbehind: true,\n            alias: 'url',\n            inside: Prism.languages.uri\n          },\n          // HTTP Version\n          'http-version': {\n            pattern: /^(\\s)HTTP\\/[\\d.]+/,\n            lookbehind: true,\n            alias: 'property'\n          }\n        }\n      },\n      'response-status': {\n        pattern: /^HTTP\\/[\\d.]+ \\d+ .+/m,\n        inside: {\n          // HTTP Version\n          'http-version': {\n            pattern: /^HTTP\\/[\\d.]+/,\n            alias: 'property'\n          },\n          // Status Code\n          'status-code': {\n            pattern: /^(\\s)\\d+(?=\\s)/,\n            lookbehind: true,\n            alias: 'number'\n          },\n          // Reason Phrase\n          'reason-phrase': {\n            pattern: /^(\\s).+/,\n            lookbehind: true,\n            alias: 'string'\n          }\n        }\n      },\n      header: {\n        pattern: /^[\\w-]+:.+(?:(?:\\r\\n?|\\n)[ \\t].+)*/m,\n        inside: {\n          'header-value': [{\n            pattern: headerValueOf(/Content-Security-Policy/.source),\n            lookbehind: true,\n            alias: ['csp', 'languages-csp'],\n            inside: Prism.languages.csp\n          }, {\n            pattern: headerValueOf(/Public-Key-Pins(?:-Report-Only)?/.source),\n            lookbehind: true,\n            alias: ['hpkp', 'languages-hpkp'],\n            inside: Prism.languages.hpkp\n          }, {\n            pattern: headerValueOf(/Strict-Transport-Security/.source),\n            lookbehind: true,\n            alias: ['hsts', 'languages-hsts'],\n            inside: Prism.languages.hsts\n          }, {\n            pattern: headerValueOf(/[^:]+/.source),\n            lookbehind: true\n          }],\n          'header-name': {\n            pattern: /^[^:]+/,\n            alias: 'keyword'\n          },\n          punctuation: /^:/\n        }\n      }\n    }; // Create a mapping of Content-Type headers to language definitions\n    var langs = Prism.languages;\n    var httpLanguages = {\n      'application/javascript': langs.javascript,\n      'application/json': langs.json || langs.javascript,\n      'application/xml': langs.xml,\n      'text/xml': langs.xml,\n      'text/html': langs.html,\n      'text/css': langs.css,\n      'text/plain': langs.plain\n    }; // Declare which types can also be suffixes\n    var suffixTypes = {\n      'application/json': true,\n      'application/xml': true\n    };\n    /**\n     * Returns a pattern for the given content type which matches it and any type which has it as a suffix.\n     *\n     * @param {string} contentType\n     * @returns {string}\n     */\n    function getSuffixPattern(contentType) {\n      var suffix = contentType.replace(/^[a-z]+\\//, '');\n      var suffixPattern = '\\\\w+/(?:[\\\\w.-]+\\\\+)+' + suffix + '(?![+\\\\w.-])';\n      return '(?:' + contentType + '|' + suffixPattern + ')';\n    } // Insert each content type parser that has its associated language\n    // currently loaded.\n    var options;\n    for (var contentType in httpLanguages) {\n      if (httpLanguages[contentType]) {\n        options = options || {};\n        var pattern = suffixTypes[contentType] ? getSuffixPattern(contentType) : contentType;\n        options[contentType.replace(/\\//g, '-')] = {\n          pattern: RegExp('(' + /content-type:\\s*/.source + pattern + /(?:(?:\\r\\n?|\\n)[\\w-].*)*(?:\\r(?:\\n|(?!\\n))|\\n)/.source + ')' +\n          // This is a little interesting:\n          // The HTTP format spec required 1 empty line before the body to make everything unambiguous.\n          // However, when writing code by hand (e.g. to display on a website) people can forget about this,\n          // so we want to be liberal here. We will allow the empty line to be omitted if the first line of\n          // the body does not start with a [\\w-] character (as headers do).\n          /[^ \\t\\w-][\\s\\S]*/.source, 'i'),\n          lookbehind: true,\n          inside: httpLanguages[contentType]\n        };\n      }\n    }\n    if (options) {\n      Prism.languages.insertBefore('http', 'header', options);\n    }\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "http", "displayName", "aliases", "Prism", "headerValueOf", "name", "RegExp", "languages", "pattern", "inside", "method", "alias", "lookbehind", "uri", "header", "source", "csp", "hpkp", "hsts", "punctuation", "langs", "httpLanguages", "javascript", "json", "xml", "html", "css", "plain", "suffixTypes", "getSuffixPattern", "contentType", "suffix", "replace", "suffixPattern", "options", "insertBefore"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/http.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = http\nhttp.displayName = 'http'\nhttp.aliases = []\nfunction http(Prism) {\n  ;(function (Prism) {\n    /**\n     * @param {string} name\n     * @returns {RegExp}\n     */\n    function headerValueOf(name) {\n      return RegExp('(^(?:' + name + '):[ \\t]*(?![ \\t]))[^]+', 'i')\n    }\n    Prism.languages.http = {\n      'request-line': {\n        pattern:\n          /^(?:CONNECT|DELETE|GET|HEAD|OPTIONS|PATCH|POST|PRI|PUT|SEARCH|TRACE)\\s(?:https?:\\/\\/|\\/)\\S*\\sHTTP\\/[\\d.]+/m,\n        inside: {\n          // HTTP Method\n          method: {\n            pattern: /^[A-Z]+\\b/,\n            alias: 'property'\n          },\n          // Request Target e.g. http://example.com, /path/to/file\n          'request-target': {\n            pattern: /^(\\s)(?:https?:\\/\\/|\\/)\\S*(?=\\s)/,\n            lookbehind: true,\n            alias: 'url',\n            inside: Prism.languages.uri\n          },\n          // HTTP Version\n          'http-version': {\n            pattern: /^(\\s)HTTP\\/[\\d.]+/,\n            lookbehind: true,\n            alias: 'property'\n          }\n        }\n      },\n      'response-status': {\n        pattern: /^HTTP\\/[\\d.]+ \\d+ .+/m,\n        inside: {\n          // HTTP Version\n          'http-version': {\n            pattern: /^HTTP\\/[\\d.]+/,\n            alias: 'property'\n          },\n          // Status Code\n          'status-code': {\n            pattern: /^(\\s)\\d+(?=\\s)/,\n            lookbehind: true,\n            alias: 'number'\n          },\n          // Reason Phrase\n          'reason-phrase': {\n            pattern: /^(\\s).+/,\n            lookbehind: true,\n            alias: 'string'\n          }\n        }\n      },\n      header: {\n        pattern: /^[\\w-]+:.+(?:(?:\\r\\n?|\\n)[ \\t].+)*/m,\n        inside: {\n          'header-value': [\n            {\n              pattern: headerValueOf(/Content-Security-Policy/.source),\n              lookbehind: true,\n              alias: ['csp', 'languages-csp'],\n              inside: Prism.languages.csp\n            },\n            {\n              pattern: headerValueOf(/Public-Key-Pins(?:-Report-Only)?/.source),\n              lookbehind: true,\n              alias: ['hpkp', 'languages-hpkp'],\n              inside: Prism.languages.hpkp\n            },\n            {\n              pattern: headerValueOf(/Strict-Transport-Security/.source),\n              lookbehind: true,\n              alias: ['hsts', 'languages-hsts'],\n              inside: Prism.languages.hsts\n            },\n            {\n              pattern: headerValueOf(/[^:]+/.source),\n              lookbehind: true\n            }\n          ],\n          'header-name': {\n            pattern: /^[^:]+/,\n            alias: 'keyword'\n          },\n          punctuation: /^:/\n        }\n      }\n    } // Create a mapping of Content-Type headers to language definitions\n    var langs = Prism.languages\n    var httpLanguages = {\n      'application/javascript': langs.javascript,\n      'application/json': langs.json || langs.javascript,\n      'application/xml': langs.xml,\n      'text/xml': langs.xml,\n      'text/html': langs.html,\n      'text/css': langs.css,\n      'text/plain': langs.plain\n    } // Declare which types can also be suffixes\n    var suffixTypes = {\n      'application/json': true,\n      'application/xml': true\n    }\n    /**\n     * Returns a pattern for the given content type which matches it and any type which has it as a suffix.\n     *\n     * @param {string} contentType\n     * @returns {string}\n     */\n    function getSuffixPattern(contentType) {\n      var suffix = contentType.replace(/^[a-z]+\\//, '')\n      var suffixPattern = '\\\\w+/(?:[\\\\w.-]+\\\\+)+' + suffix + '(?![+\\\\w.-])'\n      return '(?:' + contentType + '|' + suffixPattern + ')'\n    } // Insert each content type parser that has its associated language\n    // currently loaded.\n    var options\n    for (var contentType in httpLanguages) {\n      if (httpLanguages[contentType]) {\n        options = options || {}\n        var pattern = suffixTypes[contentType]\n          ? getSuffixPattern(contentType)\n          : contentType\n        options[contentType.replace(/\\//g, '-')] = {\n          pattern: RegExp(\n            '(' +\n              /content-type:\\s*/.source +\n              pattern +\n              /(?:(?:\\r\\n?|\\n)[\\w-].*)*(?:\\r(?:\\n|(?!\\n))|\\n)/.source +\n              ')' + // This is a little interesting:\n              // The HTTP format spec required 1 empty line before the body to make everything unambiguous.\n              // However, when writing code by hand (e.g. to display on a website) people can forget about this,\n              // so we want to be liberal here. We will allow the empty line to be omitted if the first line of\n              // the body does not start with a [\\w-] character (as headers do).\n              /[^ \\t\\w-][\\s\\S]*/.source,\n            'i'\n          ),\n          lookbehind: true,\n          inside: httpLanguages[contentType]\n        }\n      }\n    }\n    if (options) {\n      Prism.languages.insertBefore('http', 'header', options)\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;IACI,SAASC,aAAaA,CAACC,IAAI,EAAE;MAC3B,OAAOC,MAAM,CAAC,OAAO,GAAGD,IAAI,GAAG,wBAAwB,EAAE,GAAG,CAAC;IAC/D;IACAF,KAAK,CAACI,SAAS,CAACP,IAAI,GAAG;MACrB,cAAc,EAAE;QACdQ,OAAO,EACL,4GAA4G;QAC9GC,MAAM,EAAE;UACN;UACAC,MAAM,EAAE;YACNF,OAAO,EAAE,WAAW;YACpBG,KAAK,EAAE;UACT,CAAC;UACD;UACA,gBAAgB,EAAE;YAChBH,OAAO,EAAE,kCAAkC;YAC3CI,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE,KAAK;YACZF,MAAM,EAAEN,KAAK,CAACI,SAAS,CAACM;UAC1B,CAAC;UACD;UACA,cAAc,EAAE;YACdL,OAAO,EAAE,mBAAmB;YAC5BI,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD,iBAAiB,EAAE;QACjBH,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE;UACN;UACA,cAAc,EAAE;YACdD,OAAO,EAAE,eAAe;YACxBG,KAAK,EAAE;UACT,CAAC;UACD;UACA,aAAa,EAAE;YACbH,OAAO,EAAE,gBAAgB;YACzBI,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE;UACT,CAAC;UACD;UACA,eAAe,EAAE;YACfH,OAAO,EAAE,SAAS;YAClBI,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDG,MAAM,EAAE;QACNN,OAAO,EAAE,qCAAqC;QAC9CC,MAAM,EAAE;UACN,cAAc,EAAE,CACd;YACED,OAAO,EAAEJ,aAAa,CAAC,yBAAyB,CAACW,MAAM,CAAC;YACxDH,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC;YAC/BF,MAAM,EAAEN,KAAK,CAACI,SAAS,CAACS;UAC1B,CAAC,EACD;YACER,OAAO,EAAEJ,aAAa,CAAC,kCAAkC,CAACW,MAAM,CAAC;YACjEH,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;YACjCF,MAAM,EAAEN,KAAK,CAACI,SAAS,CAACU;UAC1B,CAAC,EACD;YACET,OAAO,EAAEJ,aAAa,CAAC,2BAA2B,CAACW,MAAM,CAAC;YAC1DH,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;YACjCF,MAAM,EAAEN,KAAK,CAACI,SAAS,CAACW;UAC1B,CAAC,EACD;YACEV,OAAO,EAAEJ,aAAa,CAAC,OAAO,CAACW,MAAM,CAAC;YACtCH,UAAU,EAAE;UACd,CAAC,CACF;UACD,aAAa,EAAE;YACbJ,OAAO,EAAE,QAAQ;YACjBG,KAAK,EAAE;UACT,CAAC;UACDQ,WAAW,EAAE;QACf;MACF;IACF,CAAC,EAAC;IACF,IAAIC,KAAK,GAAGjB,KAAK,CAACI,SAAS;IAC3B,IAAIc,aAAa,GAAG;MAClB,wBAAwB,EAAED,KAAK,CAACE,UAAU;MAC1C,kBAAkB,EAAEF,KAAK,CAACG,IAAI,IAAIH,KAAK,CAACE,UAAU;MAClD,iBAAiB,EAAEF,KAAK,CAACI,GAAG;MAC5B,UAAU,EAAEJ,KAAK,CAACI,GAAG;MACrB,WAAW,EAAEJ,KAAK,CAACK,IAAI;MACvB,UAAU,EAAEL,KAAK,CAACM,GAAG;MACrB,YAAY,EAAEN,KAAK,CAACO;IACtB,CAAC,EAAC;IACF,IAAIC,WAAW,GAAG;MAChB,kBAAkB,EAAE,IAAI;MACxB,iBAAiB,EAAE;IACrB,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,gBAAgBA,CAACC,WAAW,EAAE;MACrC,IAAIC,MAAM,GAAGD,WAAW,CAACE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACjD,IAAIC,aAAa,GAAG,uBAAuB,GAAGF,MAAM,GAAG,cAAc;MACrE,OAAO,KAAK,GAAGD,WAAW,GAAG,GAAG,GAAGG,aAAa,GAAG,GAAG;IACxD,CAAC,CAAC;IACF;IACA,IAAIC,OAAO;IACX,KAAK,IAAIJ,WAAW,IAAIT,aAAa,EAAE;MACrC,IAAIA,aAAa,CAACS,WAAW,CAAC,EAAE;QAC9BI,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;QACvB,IAAI1B,OAAO,GAAGoB,WAAW,CAACE,WAAW,CAAC,GAClCD,gBAAgB,CAACC,WAAW,CAAC,GAC7BA,WAAW;QACfI,OAAO,CAACJ,WAAW,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG;UACzCxB,OAAO,EAAEF,MAAM,CACb,GAAG,GACD,kBAAkB,CAACS,MAAM,GACzBP,OAAO,GACP,gDAAgD,CAACO,MAAM,GACvD,GAAG;UAAG;UACN;UACA;UACA;UACA;UACA,kBAAkB,CAACA,MAAM,EAC3B,GACF,CAAC;UACDH,UAAU,EAAE,IAAI;UAChBH,MAAM,EAAEY,aAAa,CAACS,WAAW;QACnC,CAAC;MACH;IACF;IACA,IAAII,OAAO,EAAE;MACX/B,KAAK,CAACI,SAAS,CAAC4B,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAED,OAAO,CAAC;IACzD;EACF,CAAC,EAAE/B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}