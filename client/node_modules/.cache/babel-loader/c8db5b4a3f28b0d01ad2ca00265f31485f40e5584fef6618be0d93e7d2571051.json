{"ast": null, "code": "'use strict';\n\nmodule.exports = javastacktrace;\njavastacktrace.displayName = 'javastacktrace';\njavastacktrace.aliases = [];\nfunction javastacktrace(Prism) {\n  // Specification:\n  // https://docs.oracle.com/en/java/javase/13/docs/api/java.base/java/lang/Throwable.html#printStackTrace()\n  Prism.languages.javastacktrace = {\n    // java.sql.SQLException: Violation of unique constraint MY_ENTITY_UK_1: duplicate value(s) for column(s) MY_COLUMN in statement [...]\n    // Caused by: java.sql.SQLException: Violation of unique constraint MY_ENTITY_UK_1: duplicate value(s) for column(s) MY_COLUMN in statement [...]\n    // Caused by: com.example.myproject.MyProjectServletException\n    // Caused by: MidLevelException: LowLevelException\n    // Suppressed: Resource$CloseFailException: Resource ID = 0\n    summary: {\n      pattern: /^([\\t ]*)(?:(?:Caused by:|Suppressed:|Exception in thread \"[^\"]*\")[\\t ]+)?[\\w$.]+(?::.*)?$/m,\n      lookbehind: true,\n      inside: {\n        keyword: {\n          pattern: /^([\\t ]*)(?:(?:Caused by|Suppressed)(?=:)|Exception in thread)/m,\n          lookbehind: true\n        },\n        // the current thread if the summary starts with 'Exception in thread'\n        string: {\n          pattern: /^(\\s*)\"[^\"]*\"/,\n          lookbehind: true\n        },\n        exceptions: {\n          pattern: /^(:?\\s*)[\\w$.]+(?=:|$)/,\n          lookbehind: true,\n          inside: {\n            'class-name': /[\\w$]+$/,\n            namespace: /\\b[a-z]\\w*\\b/,\n            punctuation: /\\./\n          }\n        },\n        message: {\n          pattern: /(:\\s*)\\S.*/,\n          lookbehind: true,\n          alias: 'string'\n        },\n        punctuation: /:/\n      }\n    },\n    // at org.mortbay.jetty.servlet.ServletHandler$CachedChain.doFilter(ServletHandler.java:1166)\n    // at org.hsqldb.jdbc.Util.throwError(Unknown Source) here could be some notes\n    // at java.base/java.lang.Class.forName0(Native Method)\n    // at Util.<init>(Unknown Source)\n    // at com.foo.loader/foo@9.0/com.foo.Main.run(Main.java:101)\n    // at com.foo.loader//com.foo.bar.App.run(App.java:12)\n    // at acme@2.1/org.acme.Lib.test(Lib.java:80)\n    // at MyClass.mash(MyClass.java:9)\n    //\n    // More information:\n    // https://docs.oracle.com/en/java/javase/13/docs/api/java.base/java/lang/StackTraceElement.html#toString()\n    //\n    // A valid Java module name is defined as:\n    //   \"A module name consists of one or more Java identifiers (§3.8) separated by \".\" tokens.\"\n    // https://docs.oracle.com/javase/specs/jls/se9/html/jls-6.html#jls-ModuleName\n    //\n    // A Java module version is defined by this class:\n    // https://docs.oracle.com/javase/9/docs/api/java/lang/module/ModuleDescriptor.Version.html\n    // This is the implementation of the `parse` method in JDK13:\n    // https://github.com/matcdac/jdk/blob/2305df71d1b7710266ae0956d73927a225132c0f/src/java.base/share/classes/java/lang/module/ModuleDescriptor.java#L1108\n    // However, to keep this simple, a version will be matched by the pattern /@[\\w$.+-]*/.\n    'stack-frame': {\n      pattern: /^([\\t ]*)at (?:[\\w$./]|@[\\w$.+-]*\\/)+(?:<init>)?\\([^()]*\\)/m,\n      lookbehind: true,\n      inside: {\n        keyword: {\n          pattern: /^(\\s*)at(?= )/,\n          lookbehind: true\n        },\n        source: [\n        // (Main.java:15)\n        // (Main.scala:15)\n        {\n          pattern: /(\\()\\w+\\.\\w+:\\d+(?=\\))/,\n          lookbehind: true,\n          inside: {\n            file: /^\\w+\\.\\w+/,\n            punctuation: /:/,\n            'line-number': {\n              pattern: /\\b\\d+\\b/,\n              alias: 'number'\n            }\n          }\n        },\n        // (Unknown Source)\n        // (Native Method)\n        // (...something...)\n        {\n          pattern: /(\\()[^()]*(?=\\))/,\n          lookbehind: true,\n          inside: {\n            keyword: /^(?:Native Method|Unknown Source)$/\n          }\n        }],\n        'class-name': /[\\w$]+(?=\\.(?:<init>|[\\w$]+)\\()/,\n        function: /(?:<init>|[\\w$]+)(?=\\()/,\n        'class-loader': {\n          pattern: /(\\s)[a-z]\\w*(?:\\.[a-z]\\w*)*(?=\\/[\\w@$.]*\\/)/,\n          lookbehind: true,\n          alias: 'namespace',\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        module: {\n          pattern: /([\\s/])[a-z]\\w*(?:\\.[a-z]\\w*)*(?:@[\\w$.+-]*)?(?=\\/)/,\n          lookbehind: true,\n          inside: {\n            version: {\n              pattern: /(@)[\\s\\S]+/,\n              lookbehind: true,\n              alias: 'number'\n            },\n            punctuation: /[@.]/\n          }\n        },\n        namespace: {\n          pattern: /(?:\\b[a-z]\\w*\\.)+/,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /[()/.]/\n      }\n    },\n    // ... 32 more\n    // ... 32 common frames omitted\n    more: {\n      pattern: /^([\\t ]*)\\.{3} \\d+ [a-z]+(?: [a-z]+)*/m,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\.{3}/,\n        number: /\\d+/,\n        keyword: /\\b[a-z]+(?: [a-z]+)*\\b/\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "javastacktrace", "displayName", "aliases", "Prism", "languages", "summary", "pattern", "lookbehind", "inside", "keyword", "string", "exceptions", "namespace", "punctuation", "message", "alias", "source", "file", "function", "version", "more", "number"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/javastacktrace.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = javastacktrace\njavastacktrace.displayName = 'javastacktrace'\njavastacktrace.aliases = []\nfunction javastacktrace(Prism) {\n  // Specification:\n  // https://docs.oracle.com/en/java/javase/13/docs/api/java.base/java/lang/Throwable.html#printStackTrace()\n  Prism.languages.javastacktrace = {\n    // java.sql.SQLException: Violation of unique constraint MY_ENTITY_UK_1: duplicate value(s) for column(s) MY_COLUMN in statement [...]\n    // Caused by: java.sql.SQLException: Violation of unique constraint MY_ENTITY_UK_1: duplicate value(s) for column(s) MY_COLUMN in statement [...]\n    // Caused by: com.example.myproject.MyProjectServletException\n    // Caused by: MidLevelException: LowLevelException\n    // Suppressed: Resource$CloseFailException: Resource ID = 0\n    summary: {\n      pattern:\n        /^([\\t ]*)(?:(?:Caused by:|Suppressed:|Exception in thread \"[^\"]*\")[\\t ]+)?[\\w$.]+(?::.*)?$/m,\n      lookbehind: true,\n      inside: {\n        keyword: {\n          pattern:\n            /^([\\t ]*)(?:(?:Caused by|Suppressed)(?=:)|Exception in thread)/m,\n          lookbehind: true\n        },\n        // the current thread if the summary starts with 'Exception in thread'\n        string: {\n          pattern: /^(\\s*)\"[^\"]*\"/,\n          lookbehind: true\n        },\n        exceptions: {\n          pattern: /^(:?\\s*)[\\w$.]+(?=:|$)/,\n          lookbehind: true,\n          inside: {\n            'class-name': /[\\w$]+$/,\n            namespace: /\\b[a-z]\\w*\\b/,\n            punctuation: /\\./\n          }\n        },\n        message: {\n          pattern: /(:\\s*)\\S.*/,\n          lookbehind: true,\n          alias: 'string'\n        },\n        punctuation: /:/\n      }\n    },\n    // at org.mortbay.jetty.servlet.ServletHandler$CachedChain.doFilter(ServletHandler.java:1166)\n    // at org.hsqldb.jdbc.Util.throwError(Unknown Source) here could be some notes\n    // at java.base/java.lang.Class.forName0(Native Method)\n    // at Util.<init>(Unknown Source)\n    // at com.foo.loader/foo@9.0/com.foo.Main.run(Main.java:101)\n    // at com.foo.loader//com.foo.bar.App.run(App.java:12)\n    // at acme@2.1/org.acme.Lib.test(Lib.java:80)\n    // at MyClass.mash(MyClass.java:9)\n    //\n    // More information:\n    // https://docs.oracle.com/en/java/javase/13/docs/api/java.base/java/lang/StackTraceElement.html#toString()\n    //\n    // A valid Java module name is defined as:\n    //   \"A module name consists of one or more Java identifiers (§3.8) separated by \".\" tokens.\"\n    // https://docs.oracle.com/javase/specs/jls/se9/html/jls-6.html#jls-ModuleName\n    //\n    // A Java module version is defined by this class:\n    // https://docs.oracle.com/javase/9/docs/api/java/lang/module/ModuleDescriptor.Version.html\n    // This is the implementation of the `parse` method in JDK13:\n    // https://github.com/matcdac/jdk/blob/2305df71d1b7710266ae0956d73927a225132c0f/src/java.base/share/classes/java/lang/module/ModuleDescriptor.java#L1108\n    // However, to keep this simple, a version will be matched by the pattern /@[\\w$.+-]*/.\n    'stack-frame': {\n      pattern: /^([\\t ]*)at (?:[\\w$./]|@[\\w$.+-]*\\/)+(?:<init>)?\\([^()]*\\)/m,\n      lookbehind: true,\n      inside: {\n        keyword: {\n          pattern: /^(\\s*)at(?= )/,\n          lookbehind: true\n        },\n        source: [\n          // (Main.java:15)\n          // (Main.scala:15)\n          {\n            pattern: /(\\()\\w+\\.\\w+:\\d+(?=\\))/,\n            lookbehind: true,\n            inside: {\n              file: /^\\w+\\.\\w+/,\n              punctuation: /:/,\n              'line-number': {\n                pattern: /\\b\\d+\\b/,\n                alias: 'number'\n              }\n            }\n          }, // (Unknown Source)\n          // (Native Method)\n          // (...something...)\n          {\n            pattern: /(\\()[^()]*(?=\\))/,\n            lookbehind: true,\n            inside: {\n              keyword: /^(?:Native Method|Unknown Source)$/\n            }\n          }\n        ],\n        'class-name': /[\\w$]+(?=\\.(?:<init>|[\\w$]+)\\()/,\n        function: /(?:<init>|[\\w$]+)(?=\\()/,\n        'class-loader': {\n          pattern: /(\\s)[a-z]\\w*(?:\\.[a-z]\\w*)*(?=\\/[\\w@$.]*\\/)/,\n          lookbehind: true,\n          alias: 'namespace',\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        module: {\n          pattern: /([\\s/])[a-z]\\w*(?:\\.[a-z]\\w*)*(?:@[\\w$.+-]*)?(?=\\/)/,\n          lookbehind: true,\n          inside: {\n            version: {\n              pattern: /(@)[\\s\\S]+/,\n              lookbehind: true,\n              alias: 'number'\n            },\n            punctuation: /[@.]/\n          }\n        },\n        namespace: {\n          pattern: /(?:\\b[a-z]\\w*\\.)+/,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /[()/.]/\n      }\n    },\n    // ... 32 more\n    // ... 32 common frames omitted\n    more: {\n      pattern: /^([\\t ]*)\\.{3} \\d+ [a-z]+(?: [a-z]+)*/m,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\.{3}/,\n        number: /\\d+/,\n        keyword: /\\b[a-z]+(?: [a-z]+)*\\b/\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,cAAc;AAC/BA,cAAc,CAACC,WAAW,GAAG,gBAAgB;AAC7CD,cAAc,CAACE,OAAO,GAAG,EAAE;AAC3B,SAASF,cAAcA,CAACG,KAAK,EAAE;EAC7B;EACA;EACAA,KAAK,CAACC,SAAS,CAACJ,cAAc,GAAG;IAC/B;IACA;IACA;IACA;IACA;IACAK,OAAO,EAAE;MACPC,OAAO,EACL,6FAA6F;MAC/FC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,OAAO,EAAE;UACPH,OAAO,EACL,iEAAiE;UACnEC,UAAU,EAAE;QACd,CAAC;QACD;QACAG,MAAM,EAAE;UACNJ,OAAO,EAAE,eAAe;UACxBC,UAAU,EAAE;QACd,CAAC;QACDI,UAAU,EAAE;UACVL,OAAO,EAAE,wBAAwB;UACjCC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACN,YAAY,EAAE,SAAS;YACvBI,SAAS,EAAE,cAAc;YACzBC,WAAW,EAAE;UACf;QACF,CAAC;QACDC,OAAO,EAAE;UACPR,OAAO,EAAE,YAAY;UACrBC,UAAU,EAAE,IAAI;UAChBQ,KAAK,EAAE;QACT,CAAC;QACDF,WAAW,EAAE;MACf;IACF,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAa,EAAE;MACbP,OAAO,EAAE,6DAA6D;MACtEC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,OAAO,EAAE;UACPH,OAAO,EAAE,eAAe;UACxBC,UAAU,EAAE;QACd,CAAC;QACDS,MAAM,EAAE;QACN;QACA;QACA;UACEV,OAAO,EAAE,wBAAwB;UACjCC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNS,IAAI,EAAE,WAAW;YACjBJ,WAAW,EAAE,GAAG;YAChB,aAAa,EAAE;cACbP,OAAO,EAAE,SAAS;cAClBS,KAAK,EAAE;YACT;UACF;QACF,CAAC;QAAE;QACH;QACA;QACA;UACET,OAAO,EAAE,kBAAkB;UAC3BC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,OAAO,EAAE;UACX;QACF,CAAC,CACF;QACD,YAAY,EAAE,iCAAiC;QAC/CS,QAAQ,EAAE,yBAAyB;QACnC,cAAc,EAAE;UACdZ,OAAO,EAAE,6CAA6C;UACtDC,UAAU,EAAE,IAAI;UAChBQ,KAAK,EAAE,WAAW;UAClBP,MAAM,EAAE;YACNK,WAAW,EAAE;UACf;QACF,CAAC;QACDf,MAAM,EAAE;UACNQ,OAAO,EAAE,qDAAqD;UAC9DC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNW,OAAO,EAAE;cACPb,OAAO,EAAE,YAAY;cACrBC,UAAU,EAAE,IAAI;cAChBQ,KAAK,EAAE;YACT,CAAC;YACDF,WAAW,EAAE;UACf;QACF,CAAC;QACDD,SAAS,EAAE;UACTN,OAAO,EAAE,mBAAmB;UAC5BE,MAAM,EAAE;YACNK,WAAW,EAAE;UACf;QACF,CAAC;QACDA,WAAW,EAAE;MACf;IACF,CAAC;IACD;IACA;IACAO,IAAI,EAAE;MACJd,OAAO,EAAE,wCAAwC;MACjDC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNK,WAAW,EAAE,OAAO;QACpBQ,MAAM,EAAE,KAAK;QACbZ,OAAO,EAAE;MACX;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}