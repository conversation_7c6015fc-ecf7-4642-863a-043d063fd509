{"ast": null, "code": "'use strict';\n\nmodule.exports = promql;\npromql.displayName = 'promql';\npromql.aliases = [];\nfunction promql(Prism) {\n  // Thanks to: https://github.com/prometheus-community/monaco-promql/blob/master/src/promql/promql.ts\n  // As well as: https://kausal.co/blog/slate-prism-add-new-syntax-promql/\n  ;\n  (function (Prism) {\n    // PromQL Aggregation Operators\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#aggregation-operators)\n    var aggregations = ['sum', 'min', 'max', 'avg', 'group', 'stddev', 'stdvar', 'count', 'count_values', 'bottomk', 'topk', 'quantile']; // PromQL vector matching + the by and without clauses\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#vector-matching)\n    var vectorMatching = ['on', 'ignoring', 'group_right', 'group_left', 'by', 'without']; // PromQL offset modifier\n    // (https://prometheus.io/docs/prometheus/latest/querying/basics/#offset-modifier)\n    var offsetModifier = ['offset'];\n    var keywords = aggregations.concat(vectorMatching, offsetModifier);\n    Prism.languages.promql = {\n      comment: {\n        pattern: /(^[ \\t]*)#.*/m,\n        lookbehind: true\n      },\n      'vector-match': {\n        // Match the comma-separated label lists inside vector matching:\n        pattern: new RegExp('((?:' + vectorMatching.join('|') + ')\\\\s*)\\\\([^)]*\\\\)'),\n        lookbehind: true,\n        inside: {\n          'label-key': {\n            pattern: /\\b[^,]+\\b/,\n            alias: 'attr-name'\n          },\n          punctuation: /[(),]/\n        }\n      },\n      'context-labels': {\n        pattern: /\\{[^{}]*\\}/,\n        inside: {\n          'label-key': {\n            pattern: /\\b[a-z_]\\w*(?=\\s*(?:=|![=~]))/,\n            alias: 'attr-name'\n          },\n          'label-value': {\n            pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n            greedy: true,\n            alias: 'attr-value'\n          },\n          punctuation: /\\{|\\}|=~?|![=~]|,/\n        }\n      },\n      'context-range': [{\n        pattern: /\\[[\\w\\s:]+\\]/,\n        // [1m]\n        inside: {\n          punctuation: /\\[|\\]|:/,\n          'range-duration': {\n            pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n            alias: 'number'\n          }\n        }\n      }, {\n        pattern: /(\\boffset\\s+)\\w+/,\n        // offset 1m\n        lookbehind: true,\n        inside: {\n          'range-duration': {\n            pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n            alias: 'number'\n          }\n        }\n      }],\n      keyword: new RegExp('\\\\b(?:' + keywords.join('|') + ')\\\\b', 'i'),\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      number: /[-+]?(?:(?:\\b\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[-+]?\\d+)?\\b|\\b(?:0x[0-9a-f]+|nan|inf)\\b)/i,\n      operator: /[\\^*/%+-]|==|!=|<=|<|>=|>|\\b(?:and|or|unless)\\b/i,\n      punctuation: /[{};()`,.[\\]]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "promql", "displayName", "aliases", "Prism", "aggregations", "vectorMatching", "offsetModifier", "keywords", "concat", "languages", "comment", "pattern", "lookbehind", "RegExp", "join", "inside", "alias", "punctuation", "greedy", "keyword", "function", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/promql.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = promql\npromql.displayName = 'promql'\npromql.aliases = []\nfunction promql(Prism) {\n  // Thanks to: https://github.com/prometheus-community/monaco-promql/blob/master/src/promql/promql.ts\n  // As well as: https://kausal.co/blog/slate-prism-add-new-syntax-promql/\n  ;(function (Prism) {\n    // PromQL Aggregation Operators\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#aggregation-operators)\n    var aggregations = [\n      'sum',\n      'min',\n      'max',\n      'avg',\n      'group',\n      'stddev',\n      'stdvar',\n      'count',\n      'count_values',\n      'bottomk',\n      'topk',\n      'quantile'\n    ] // PromQL vector matching + the by and without clauses\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#vector-matching)\n    var vectorMatching = [\n      'on',\n      'ignoring',\n      'group_right',\n      'group_left',\n      'by',\n      'without'\n    ] // PromQL offset modifier\n    // (https://prometheus.io/docs/prometheus/latest/querying/basics/#offset-modifier)\n    var offsetModifier = ['offset']\n    var keywords = aggregations.concat(vectorMatching, offsetModifier)\n    Prism.languages.promql = {\n      comment: {\n        pattern: /(^[ \\t]*)#.*/m,\n        lookbehind: true\n      },\n      'vector-match': {\n        // Match the comma-separated label lists inside vector matching:\n        pattern: new RegExp(\n          '((?:' + vectorMatching.join('|') + ')\\\\s*)\\\\([^)]*\\\\)'\n        ),\n        lookbehind: true,\n        inside: {\n          'label-key': {\n            pattern: /\\b[^,]+\\b/,\n            alias: 'attr-name'\n          },\n          punctuation: /[(),]/\n        }\n      },\n      'context-labels': {\n        pattern: /\\{[^{}]*\\}/,\n        inside: {\n          'label-key': {\n            pattern: /\\b[a-z_]\\w*(?=\\s*(?:=|![=~]))/,\n            alias: 'attr-name'\n          },\n          'label-value': {\n            pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n            greedy: true,\n            alias: 'attr-value'\n          },\n          punctuation: /\\{|\\}|=~?|![=~]|,/\n        }\n      },\n      'context-range': [\n        {\n          pattern: /\\[[\\w\\s:]+\\]/,\n          // [1m]\n          inside: {\n            punctuation: /\\[|\\]|:/,\n            'range-duration': {\n              pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n              alias: 'number'\n            }\n          }\n        },\n        {\n          pattern: /(\\boffset\\s+)\\w+/,\n          // offset 1m\n          lookbehind: true,\n          inside: {\n            'range-duration': {\n              pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n              alias: 'number'\n            }\n          }\n        }\n      ],\n      keyword: new RegExp('\\\\b(?:' + keywords.join('|') + ')\\\\b', 'i'),\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      number:\n        /[-+]?(?:(?:\\b\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[-+]?\\d+)?\\b|\\b(?:0x[0-9a-f]+|nan|inf)\\b)/i,\n      operator: /[\\^*/%+-]|==|!=|<=|<|>=|>|\\b(?:and|or|unless)\\b/i,\n      punctuation: /[{};()`,.[\\]]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EACA;EACA;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA;IACA,IAAIC,YAAY,GAAG,CACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,cAAc,EACd,SAAS,EACT,MAAM,EACN,UAAU,CACX,EAAC;IACF;IACA,IAAIC,cAAc,GAAG,CACnB,IAAI,EACJ,UAAU,EACV,aAAa,EACb,YAAY,EACZ,IAAI,EACJ,SAAS,CACV,EAAC;IACF;IACA,IAAIC,cAAc,GAAG,CAAC,QAAQ,CAAC;IAC/B,IAAIC,QAAQ,GAAGH,YAAY,CAACI,MAAM,CAACH,cAAc,EAAEC,cAAc,CAAC;IAClEH,KAAK,CAACM,SAAS,CAACT,MAAM,GAAG;MACvBU,OAAO,EAAE;QACPC,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE;MACd,CAAC;MACD,cAAc,EAAE;QACd;QACAD,OAAO,EAAE,IAAIE,MAAM,CACjB,MAAM,GAAGR,cAAc,CAACS,IAAI,CAAC,GAAG,CAAC,GAAG,mBACtC,CAAC;QACDF,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE;UACN,WAAW,EAAE;YACXJ,OAAO,EAAE,WAAW;YACpBK,KAAK,EAAE;UACT,CAAC;UACDC,WAAW,EAAE;QACf;MACF,CAAC;MACD,gBAAgB,EAAE;QAChBN,OAAO,EAAE,YAAY;QACrBI,MAAM,EAAE;UACN,WAAW,EAAE;YACXJ,OAAO,EAAE,+BAA+B;YACxCK,KAAK,EAAE;UACT,CAAC;UACD,aAAa,EAAE;YACbL,OAAO,EAAE,oCAAoC;YAC7CO,MAAM,EAAE,IAAI;YACZF,KAAK,EAAE;UACT,CAAC;UACDC,WAAW,EAAE;QACf;MACF,CAAC;MACD,eAAe,EAAE,CACf;QACEN,OAAO,EAAE,cAAc;QACvB;QACAI,MAAM,EAAE;UACNE,WAAW,EAAE,SAAS;UACtB,gBAAgB,EAAE;YAChBN,OAAO,EAAE,8BAA8B;YACvCK,KAAK,EAAE;UACT;QACF;MACF,CAAC,EACD;QACEL,OAAO,EAAE,kBAAkB;QAC3B;QACAC,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAE;UACN,gBAAgB,EAAE;YAChBJ,OAAO,EAAE,8BAA8B;YACvCK,KAAK,EAAE;UACT;QACF;MACF,CAAC,CACF;MACDG,OAAO,EAAE,IAAIN,MAAM,CAAC,QAAQ,GAAGN,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,CAAC;MAChEM,QAAQ,EAAE,uBAAuB;MACjCC,MAAM,EACJ,mFAAmF;MACrFC,QAAQ,EAAE,kDAAkD;MAC5DL,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEd,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}