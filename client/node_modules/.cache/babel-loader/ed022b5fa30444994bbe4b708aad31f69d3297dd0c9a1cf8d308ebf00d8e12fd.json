{"ast": null, "code": "/*\n Language: Zephir\n Description: Zephir, an open source, high-level language designed to ease the creation and maintainability of extensions for PHP with a focus on type and memory safety.\n Author: <PERSON><PERSON> <<EMAIL>>\n Website: https://zephir-lang.com/en\n Audit: 2020\n */\n\n/** @type LanguageFn */\nfunction zephir(hljs) {\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [hljs.inherit(hljs.APOS_STRING_MODE, {\n      illegal: null\n    }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    })]\n  };\n  const TITLE_MODE = hljs.UNDERSCORE_TITLE_MODE;\n  const NUMBER = {\n    variants: [hljs.BINARY_NUMBER_MODE, hljs.C_NUMBER_MODE]\n  };\n  const KEYWORDS =\n  // classes and objects\n  'namespace class interface use extends ' + 'function return ' + 'abstract final public protected private static deprecated ' +\n  // error handling\n  'throw try catch Exception ' +\n  // keyword-ish things their website does NOT seem to highlight (in their own snippets)\n  // 'typeof fetch in ' +\n  // operators/helpers\n  'echo empty isset instanceof unset ' +\n  // assignment/variables\n  'let var new const self ' +\n  // control\n  'require ' + 'if else elseif switch case default ' + 'do while loop for continue break ' + 'likely unlikely ' +\n  // magic constants\n  // https://github.com/phalcon/zephir/blob/master/Library/Expression/Constants.php\n  '__LINE__ __FILE__ __DIR__ __FUNCTION__ __CLASS__ __TRAIT__ __METHOD__ __NAMESPACE__ ' +\n  // types - https://docs.zephir-lang.com/0.12/en/types\n  'array boolean float double integer object resource string ' + 'char long unsigned bool int uint ulong uchar ' +\n  // built-ins\n  'true false null undefined';\n  return {\n    name: 'Zephir',\n    aliases: ['zep'],\n    keywords: KEYWORDS,\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.COMMENT(/\\/\\*/, /\\*\\//, {\n      contains: [{\n        className: 'doctag',\n        begin: /@[A-Za-z]+/\n      }]\n    }), {\n      className: 'string',\n      begin: /<<<['\"]?\\w+['\"]?$/,\n      end: /^\\w+;/,\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      // swallow composed identifiers to avoid parsing them as keywords\n      begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n    }, {\n      className: 'function',\n      beginKeywords: 'function fn',\n      end: /[;{]/,\n      excludeEnd: true,\n      illegal: /\\$|\\[|%/,\n      contains: [TITLE_MODE, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: ['self', hljs.C_BLOCK_COMMENT_MODE, STRING, NUMBER]\n      }]\n    }, {\n      className: 'class',\n      beginKeywords: 'class interface',\n      end: /\\{/,\n      excludeEnd: true,\n      illegal: /[:($\"]/,\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, TITLE_MODE]\n    }, {\n      beginKeywords: 'namespace',\n      end: /;/,\n      illegal: /[.']/,\n      contains: [TITLE_MODE]\n    }, {\n      beginKeywords: 'use',\n      end: /;/,\n      contains: [TITLE_MODE]\n    }, {\n      begin: /=>/ // No markup, just a relevance booster\n    }, STRING, NUMBER]\n  };\n}\nmodule.exports = zephir;", "map": {"version": 3, "names": ["zephir", "hljs", "STRING", "className", "contains", "BACKSLASH_ESCAPE", "variants", "inherit", "APOS_STRING_MODE", "illegal", "QUOTE_STRING_MODE", "TITLE_MODE", "UNDERSCORE_TITLE_MODE", "NUMBER", "BINARY_NUMBER_MODE", "C_NUMBER_MODE", "KEYWORDS", "name", "aliases", "keywords", "C_LINE_COMMENT_MODE", "COMMENT", "begin", "end", "beginKeywords", "excludeEnd", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/zephir.js"], "sourcesContent": ["/*\n Language: Zephir\n Description: Zephir, an open source, high-level language designed to ease the creation and maintainability of extensions for PHP with a focus on type and memory safety.\n Author: <PERSON><PERSON> <<EMAIL>>\n Website: https://zephir-lang.com/en\n Audit: 2020\n */\n\n/** @type LanguageFn */\nfunction zephir(hljs) {\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        illegal: null\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      })\n    ]\n  };\n  const TITLE_MODE = hljs.UNDERSCORE_TITLE_MODE;\n  const NUMBER = {\n    variants: [\n      hljs.BINARY_NUMBER_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n  const KEYWORDS =\n    // classes and objects\n    'namespace class interface use extends ' +\n    'function return ' +\n    'abstract final public protected private static deprecated ' +\n    // error handling\n    'throw try catch Exception ' +\n    // keyword-ish things their website does NOT seem to highlight (in their own snippets)\n    // 'typeof fetch in ' +\n    // operators/helpers\n    'echo empty isset instanceof unset ' +\n    // assignment/variables\n    'let var new const self ' +\n    // control\n    'require ' +\n    'if else elseif switch case default ' +\n    'do while loop for continue break ' +\n    'likely unlikely ' +\n    // magic constants\n    // https://github.com/phalcon/zephir/blob/master/Library/Expression/Constants.php\n    '__LINE__ __FILE__ __DIR__ __FUNCTION__ __CLASS__ __TRAIT__ __METHOD__ __NAMESPACE__ ' +\n    // types - https://docs.zephir-lang.com/0.12/en/types\n    'array boolean float double integer object resource string ' +\n    'char long unsigned bool int uint ulong uchar ' +\n    // built-ins\n    'true false null undefined';\n\n  return {\n    name: 'Zephir',\n    aliases: [ 'zep' ],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT(\n        /\\/\\*/,\n        /\\*\\//,\n        {\n          contains: [\n            {\n              className: 'doctag',\n              begin: /@[A-Za-z]+/\n            }\n          ]\n        }\n      ),\n      {\n        className: 'string',\n        begin: /<<<['\"]?\\w+['\"]?$/,\n        end: /^\\w+;/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        // swallow composed identifiers to avoid parsing them as keywords\n        begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function fn',\n        end: /[;{]/,\n        excludeEnd: true,\n        illegal: /\\$|\\[|%/,\n        contains: [\n          TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        illegal: /[:($\"]/,\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          TITLE_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        end: /;/,\n        illegal: /[.']/,\n        contains: [ TITLE_MODE ]\n      },\n      {\n        beginKeywords: 'use',\n        end: /;/,\n        contains: [ TITLE_MODE ]\n      },\n      {\n        begin: /=>/ // No markup, just a relevance booster\n      },\n      STRING,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = zephir;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CAAEH,IAAI,CAACI,gBAAgB,CAAE;IACnCC,QAAQ,EAAE,CACRL,IAAI,CAACM,OAAO,CAACN,IAAI,CAACO,gBAAgB,EAAE;MAClCC,OAAO,EAAE;IACX,CAAC,CAAC,EACFR,IAAI,CAACM,OAAO,CAACN,IAAI,CAACS,iBAAiB,EAAE;MACnCD,OAAO,EAAE;IACX,CAAC,CAAC;EAEN,CAAC;EACD,MAAME,UAAU,GAAGV,IAAI,CAACW,qBAAqB;EAC7C,MAAMC,MAAM,GAAG;IACbP,QAAQ,EAAE,CACRL,IAAI,CAACa,kBAAkB,EACvBb,IAAI,CAACc,aAAa;EAEtB,CAAC;EACD,MAAMC,QAAQ;EACZ;EACA,wCAAwC,GACxC,kBAAkB,GAClB,4DAA4D;EAC5D;EACA,4BAA4B;EAC5B;EACA;EACA;EACA,oCAAoC;EACpC;EACA,yBAAyB;EACzB;EACA,UAAU,GACV,qCAAqC,GACrC,mCAAmC,GACnC,kBAAkB;EAClB;EACA;EACA,sFAAsF;EACtF;EACA,4DAA4D,GAC5D,+CAA+C;EAC/C;EACA,2BAA2B;EAE7B,OAAO;IACLC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CAAE,KAAK,CAAE;IAClBC,QAAQ,EAAEH,QAAQ;IAClBZ,QAAQ,EAAE,CACRH,IAAI,CAACmB,mBAAmB,EACxBnB,IAAI,CAACoB,OAAO,CACV,MAAM,EACN,MAAM,EACN;MACEjB,QAAQ,EAAE,CACR;QACED,SAAS,EAAE,QAAQ;QACnBmB,KAAK,EAAE;MACT,CAAC;IAEL,CACF,CAAC,EACD;MACEnB,SAAS,EAAE,QAAQ;MACnBmB,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE,OAAO;MACZnB,QAAQ,EAAE,CAAEH,IAAI,CAACI,gBAAgB;IACnC,CAAC,EACD;MACE;MACAiB,KAAK,EAAE;IACT,CAAC,EACD;MACEnB,SAAS,EAAE,UAAU;MACrBqB,aAAa,EAAE,aAAa;MAC5BD,GAAG,EAAE,MAAM;MACXE,UAAU,EAAE,IAAI;MAChBhB,OAAO,EAAE,SAAS;MAClBL,QAAQ,EAAE,CACRO,UAAU,EACV;QACER,SAAS,EAAE,QAAQ;QACnBmB,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,IAAI;QACTJ,QAAQ,EAAEH,QAAQ;QAClBZ,QAAQ,EAAE,CACR,MAAM,EACNH,IAAI,CAACyB,oBAAoB,EACzBxB,MAAM,EACNW,MAAM;MAEV,CAAC;IAEL,CAAC,EACD;MACEV,SAAS,EAAE,OAAO;MAClBqB,aAAa,EAAE,iBAAiB;MAChCD,GAAG,EAAE,IAAI;MACTE,UAAU,EAAE,IAAI;MAChBhB,OAAO,EAAE,QAAQ;MACjBL,QAAQ,EAAE,CACR;QACEoB,aAAa,EAAE;MACjB,CAAC,EACDb,UAAU;IAEd,CAAC,EACD;MACEa,aAAa,EAAE,WAAW;MAC1BD,GAAG,EAAE,GAAG;MACRd,OAAO,EAAE,MAAM;MACfL,QAAQ,EAAE,CAAEO,UAAU;IACxB,CAAC,EACD;MACEa,aAAa,EAAE,KAAK;MACpBD,GAAG,EAAE,GAAG;MACRnB,QAAQ,EAAE,CAAEO,UAAU;IACxB,CAAC,EACD;MACEW,KAAK,EAAE,IAAI,CAAC;IACd,CAAC,EACDpB,MAAM,EACNW,MAAM;EAEV,CAAC;AACH;AAEAc,MAAM,CAACC,OAAO,GAAG5B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}