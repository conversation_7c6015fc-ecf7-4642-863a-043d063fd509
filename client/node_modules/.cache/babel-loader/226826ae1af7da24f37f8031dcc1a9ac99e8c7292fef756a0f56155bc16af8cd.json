{"ast": null, "code": "'use strict';\n\nvar refractorPhp = require('./php.js');\nvar refractorJavadoclike = require('./javadoclike.js');\nmodule.exports = phpdoc;\nphpdoc.displayName = 'phpdoc';\nphpdoc.aliases = [];\nfunction phpdoc(Prism) {\n  Prism.register(refractorPhp);\n  Prism.register(refractorJavadoclike);\n  (function (Prism) {\n    var typeExpression = /(?:\\b[a-zA-Z]\\w*|[|\\\\[\\]])+/.source;\n    Prism.languages.phpdoc = Prism.languages.extend('javadoclike', {\n      parameter: {\n        pattern: RegExp('(@(?:global|param|property(?:-read|-write)?|var)\\\\s+(?:' + typeExpression + '\\\\s+)?)\\\\$\\\\w+'),\n        lookbehind: true\n      }\n    });\n    Prism.languages.insertBefore('phpdoc', 'keyword', {\n      'class-name': [{\n        pattern: RegExp('(@(?:global|package|param|property(?:-read|-write)?|return|subpackage|throws|var)\\\\s+)' + typeExpression),\n        lookbehind: true,\n        inside: {\n          keyword: /\\b(?:array|bool|boolean|callback|double|false|float|int|integer|mixed|null|object|resource|self|string|true|void)\\b/,\n          punctuation: /[|\\\\[\\]()]/\n        }\n      }]\n    });\n    Prism.languages.javadoclike.addSupport('php', Prism.languages.phpdoc);\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorPhp", "require", "refractorJavadoclike", "module", "exports", "phpdoc", "displayName", "aliases", "Prism", "register", "typeExpression", "source", "languages", "extend", "parameter", "pattern", "RegExp", "lookbehind", "insertBefore", "inside", "keyword", "punctuation", "javadoclike", "addSupport"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/phpdoc.js"], "sourcesContent": ["'use strict'\nvar refractorPhp = require('./php.js')\nvar refractorJavadoclike = require('./javadoclike.js')\nmodule.exports = phpdoc\nphpdoc.displayName = 'phpdoc'\nphpdoc.aliases = []\nfunction phpdoc(Prism) {\n  Prism.register(refractorPhp)\n  Prism.register(refractorJavadoclike)\n  ;(function (Prism) {\n    var typeExpression = /(?:\\b[a-zA-Z]\\w*|[|\\\\[\\]])+/.source\n    Prism.languages.phpdoc = Prism.languages.extend('javadoclike', {\n      parameter: {\n        pattern: RegExp(\n          '(@(?:global|param|property(?:-read|-write)?|var)\\\\s+(?:' +\n            typeExpression +\n            '\\\\s+)?)\\\\$\\\\w+'\n        ),\n        lookbehind: true\n      }\n    })\n    Prism.languages.insertBefore('phpdoc', 'keyword', {\n      'class-name': [\n        {\n          pattern: RegExp(\n            '(@(?:global|package|param|property(?:-read|-write)?|return|subpackage|throws|var)\\\\s+)' +\n              typeExpression\n          ),\n          lookbehind: true,\n          inside: {\n            keyword:\n              /\\b(?:array|bool|boolean|callback|double|false|float|int|integer|mixed|null|object|resource|self|string|true|void)\\b/,\n            punctuation: /[|\\\\[\\]()]/\n          }\n        }\n      ]\n    })\n    Prism.languages.javadoclike.addSupport('php', Prism.languages.phpdoc)\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,UAAU,CAAC;AACtC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AACtDE,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACT,YAAY,CAAC;EAC5BQ,KAAK,CAACC,QAAQ,CAACP,oBAAoB,CAAC;EACnC,CAAC,UAAUM,KAAK,EAAE;IACjB,IAAIE,cAAc,GAAG,6BAA6B,CAACC,MAAM;IACzDH,KAAK,CAACI,SAAS,CAACP,MAAM,GAAGG,KAAK,CAACI,SAAS,CAACC,MAAM,CAAC,aAAa,EAAE;MAC7DC,SAAS,EAAE;QACTC,OAAO,EAAEC,MAAM,CACb,yDAAyD,GACvDN,cAAc,GACd,gBACJ,CAAC;QACDO,UAAU,EAAE;MACd;IACF,CAAC,CAAC;IACFT,KAAK,CAACI,SAAS,CAACM,YAAY,CAAC,QAAQ,EAAE,SAAS,EAAE;MAChD,YAAY,EAAE,CACZ;QACEH,OAAO,EAAEC,MAAM,CACb,wFAAwF,GACtFN,cACJ,CAAC;QACDO,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNC,OAAO,EACL,qHAAqH;UACvHC,WAAW,EAAE;QACf;MACF,CAAC;IAEL,CAAC,CAAC;IACFb,KAAK,CAACI,SAAS,CAACU,WAAW,CAACC,UAAU,CAAC,KAAK,EAAEf,KAAK,CAACI,SAAS,CAACP,MAAM,CAAC;EACvE,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}