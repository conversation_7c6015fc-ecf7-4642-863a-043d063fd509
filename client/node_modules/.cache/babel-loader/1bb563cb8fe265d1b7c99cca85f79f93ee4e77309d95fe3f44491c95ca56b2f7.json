{"ast": null, "code": "/*\nLanguage: CSP\nDescription: Content Security Policy definition highlighting\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP\n\nvim: ts=2 sw=2 st=2\n*/\n\n/** @type LanguageFn */\nfunction csp(hljs) {\n  return {\n    name: 'CSP',\n    case_insensitive: false,\n    keywords: {\n      $pattern: '[a-zA-Z][a-zA-Z0-9_-]*',\n      keyword: 'base-uri child-src connect-src default-src font-src form-action ' + 'frame-ancestors frame-src img-src media-src object-src plugin-types ' + 'report-uri sandbox script-src style-src'\n    },\n    contains: [{\n      className: 'string',\n      begin: \"'\",\n      end: \"'\"\n    }, {\n      className: 'attribute',\n      begin: '^Content',\n      end: ':',\n      excludeEnd: true\n    }]\n  };\n}\nmodule.exports = csp;", "map": {"version": 3, "names": ["csp", "hljs", "name", "case_insensitive", "keywords", "$pattern", "keyword", "contains", "className", "begin", "end", "excludeEnd", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/csp.js"], "sourcesContent": ["/*\nLanguage: CSP\nDescription: Content Security Policy definition highlighting\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP\n\nvim: ts=2 sw=2 st=2\n*/\n\n/** @type LanguageFn */\nfunction csp(hljs) {\n  return {\n    name: 'CSP',\n    case_insensitive: false,\n    keywords: {\n      $pattern: '[a-zA-Z][a-zA-Z0-9_-]*',\n      keyword: 'base-uri child-src connect-src default-src font-src form-action ' +\n        'frame-ancestors frame-src img-src media-src object-src plugin-types ' +\n        'report-uri sandbox script-src style-src'\n    },\n    contains: [\n      {\n        className: 'string',\n        begin: \"'\",\n        end: \"'\"\n      },\n      {\n        className: 'attribute',\n        begin: '^Content',\n        end: ':',\n        excludeEnd: true\n      }\n    ]\n  };\n}\n\nmodule.exports = csp;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,gBAAgB,EAAE,KAAK;IACvBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,wBAAwB;MAClCC,OAAO,EAAE,kEAAkE,GACzE,sEAAsE,GACtE;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,EACD;MACEF,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE,GAAG;MACRC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}