{"ast": null, "code": "/*\nLanguage: Parser3\nRequires: xml.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.parser.ru/en/\nCategory: template\n*/\n\nfunction parser3(hljs) {\n  const CURLY_SUBCOMMENT = hljs.COMMENT(/\\{/, /\\}/, {\n    contains: ['self']\n  });\n  return {\n    name: 'Parser3',\n    subLanguage: 'xml',\n    relevance: 0,\n    contains: [hljs.COMMENT('^#', '$'), hljs.COMMENT(/\\^rem\\{/, /\\}/, {\n      relevance: 10,\n      contains: [CURLY_SUBCOMMENT]\n    }), {\n      className: 'meta',\n      begin: '^@(?:BASE|USE|CLASS|OPTIONS)$',\n      relevance: 10\n    }, {\n      className: 'title',\n      begin: '@[\\\\w\\\\-]+\\\\[[\\\\w^;\\\\-]*\\\\](?:\\\\[[\\\\w^;\\\\-]*\\\\])?(?:.*)$'\n    }, {\n      className: 'variable',\n      begin: /\\$\\{?[\\w\\-.:]+\\}?/\n    }, {\n      className: 'keyword',\n      begin: /\\^[\\w\\-.:]+/\n    }, {\n      className: 'number',\n      begin: '\\\\^#[0-9a-fA-F]+'\n    }, hljs.C_NUMBER_MODE]\n  };\n}\nmodule.exports = parser3;", "map": {"version": 3, "names": ["parser3", "hljs", "CURLY_SUBCOMMENT", "COMMENT", "contains", "name", "subLanguage", "relevance", "className", "begin", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/parser3.js"], "sourcesContent": ["/*\nLanguage: Parser3\nRequires: xml.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.parser.ru/en/\nCategory: template\n*/\n\nfunction parser3(hljs) {\n  const CURLY_SUBCOMMENT = hljs.COMMENT(\n    /\\{/,\n    /\\}/,\n    {\n      contains: [ 'self' ]\n    }\n  );\n  return {\n    name: 'Parser3',\n    subLanguage: 'xml',\n    relevance: 0,\n    contains: [\n      hljs.COMMENT('^#', '$'),\n      hljs.COMMENT(\n        /\\^rem\\{/,\n        /\\}/,\n        {\n          relevance: 10,\n          contains: [ CURLY_SUBCOMMENT ]\n        }\n      ),\n      {\n        className: 'meta',\n        begin: '^@(?:BASE|USE|CLASS|OPTIONS)$',\n        relevance: 10\n      },\n      {\n        className: 'title',\n        begin: '@[\\\\w\\\\-]+\\\\[[\\\\w^;\\\\-]*\\\\](?:\\\\[[\\\\w^;\\\\-]*\\\\])?(?:.*)$'\n      },\n      {\n        className: 'variable',\n        begin: /\\$\\{?[\\w\\-.:]+\\}?/\n      },\n      {\n        className: 'keyword',\n        begin: /\\^[\\w\\-.:]+/\n      },\n      {\n        className: 'number',\n        begin: '\\\\^#[0-9a-fA-F]+'\n      },\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = parser3;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,gBAAgB,GAAGD,IAAI,CAACE,OAAO,CACnC,IAAI,EACJ,IAAI,EACJ;IACEC,QAAQ,EAAE,CAAE,MAAM;EACpB,CACF,CAAC;EACD,OAAO;IACLC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,CAAC;IACZH,QAAQ,EAAE,CACRH,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EACvBF,IAAI,CAACE,OAAO,CACV,SAAS,EACT,IAAI,EACJ;MACEI,SAAS,EAAE,EAAE;MACbH,QAAQ,EAAE,CAAEF,gBAAgB;IAC9B,CACF,CAAC,EACD;MACEM,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,+BAA+B;MACtCF,SAAS,EAAE;IACb,CAAC,EACD;MACEC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC,EACDR,IAAI,CAACS,aAAa;EAEtB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}