{"ast": null, "code": "'use strict';\n\nmodule.exports = qml;\nqml.displayName = 'qml';\nqml.aliases = [];\nfunction qml(Prism) {\n  ;\n  (function (Prism) {\n    var jsString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|'(?:\\\\.|[^\\\\'\\r\\n])*'/.source;\n    var jsComment = /\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\//.source;\n    var jsExpr = /(?:[^\\\\()[\\]{}\"'/]|<string>|\\/(?![*/])|<comment>|\\(<expr>*\\)|\\[<expr>*\\]|\\{<expr>*\\}|\\\\[\\s\\S])/.source.replace(/<string>/g, function () {\n      return jsString;\n    }).replace(/<comment>/g, function () {\n      return jsComment;\n    }); // the pattern will blow up, so only a few iterations\n    for (var i = 0; i < 2; i++) {\n      jsExpr = jsExpr.replace(/<expr>/g, function () {\n        return jsExpr;\n      });\n    }\n    jsExpr = jsExpr.replace(/<expr>/g, '[^\\\\s\\\\S]');\n    Prism.languages.qml = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      'javascript-function': {\n        pattern: RegExp(/((?:^|;)[ \\t]*)function\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*\\(<js>*\\)\\s*\\{<js>*\\}/.source.replace(/<js>/g, function () {\n          return jsExpr;\n        }), 'm'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      'class-name': {\n        pattern: /((?:^|[:;])[ \\t]*)(?!\\d)\\w+(?=[ \\t]*\\{|[ \\t]+on\\b)/m,\n        lookbehind: true\n      },\n      property: [{\n        pattern: /((?:^|[;{])[ \\t]*)(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n        lookbehind: true\n      }, {\n        pattern: /((?:^|[;{])[ \\t]*)property[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n        lookbehind: true,\n        inside: {\n          keyword: /^property/,\n          property: /\\w+(?:\\.\\w+)*/\n        }\n      }],\n      'javascript-expression': {\n        pattern: RegExp(/(:[ \\t]*)(?![\\s;}[])(?:(?!$|[;}])<js>)+/.source.replace(/<js>/g, function () {\n          return jsExpr;\n        }), 'm'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      keyword: /\\b(?:as|import|on)\\b/,\n      punctuation: /[{}[\\]:;,]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "qml", "displayName", "aliases", "Prism", "jsString", "source", "jsComment", "jsExpr", "replace", "i", "languages", "comment", "pattern", "greedy", "RegExp", "lookbehind", "alias", "inside", "javascript", "property", "keyword", "string", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/qml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = qml\nqml.displayName = 'qml'\nqml.aliases = []\nfunction qml(Prism) {\n  ;(function (Prism) {\n    var jsString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|'(?:\\\\.|[^\\\\'\\r\\n])*'/.source\n    var jsComment = /\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\//.source\n    var jsExpr =\n      /(?:[^\\\\()[\\]{}\"'/]|<string>|\\/(?![*/])|<comment>|\\(<expr>*\\)|\\[<expr>*\\]|\\{<expr>*\\}|\\\\[\\s\\S])/.source\n        .replace(/<string>/g, function () {\n          return jsString\n        })\n        .replace(/<comment>/g, function () {\n          return jsComment\n        }) // the pattern will blow up, so only a few iterations\n    for (var i = 0; i < 2; i++) {\n      jsExpr = jsExpr.replace(/<expr>/g, function () {\n        return jsExpr\n      })\n    }\n    jsExpr = jsExpr.replace(/<expr>/g, '[^\\\\s\\\\S]')\n    Prism.languages.qml = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      'javascript-function': {\n        pattern: RegExp(\n          /((?:^|;)[ \\t]*)function\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*\\(<js>*\\)\\s*\\{<js>*\\}/.source.replace(\n            /<js>/g,\n            function () {\n              return jsExpr\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      'class-name': {\n        pattern: /((?:^|[:;])[ \\t]*)(?!\\d)\\w+(?=[ \\t]*\\{|[ \\t]+on\\b)/m,\n        lookbehind: true\n      },\n      property: [\n        {\n          pattern: /((?:^|[;{])[ \\t]*)(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /((?:^|[;{])[ \\t]*)property[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^property/,\n            property: /\\w+(?:\\.\\w+)*/\n          }\n        }\n      ],\n      'javascript-expression': {\n        pattern: RegExp(\n          /(:[ \\t]*)(?![\\s;}[])(?:(?!$|[;}])<js>)+/.source.replace(\n            /<js>/g,\n            function () {\n              return jsExpr\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      keyword: /\\b(?:as|import|on)\\b/,\n      punctuation: /[{}[\\]:;,]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GAAG,6CAA6C,CAACC,MAAM;IACnE,IAAIC,SAAS,GAAG,wCAAwC,CAACD,MAAM;IAC/D,IAAIE,MAAM,GACR,gGAAgG,CAACF,MAAM,CACpGG,OAAO,CAAC,WAAW,EAAE,YAAY;MAChC,OAAOJ,QAAQ;IACjB,CAAC,CAAC,CACDI,OAAO,CAAC,YAAY,EAAE,YAAY;MACjC,OAAOF,SAAS;IAClB,CAAC,CAAC,EAAC;IACP,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BF,MAAM,GAAGA,MAAM,CAACC,OAAO,CAAC,SAAS,EAAE,YAAY;QAC7C,OAAOD,MAAM;MACf,CAAC,CAAC;IACJ;IACAA,MAAM,GAAGA,MAAM,CAACC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC;IAC/CL,KAAK,CAACO,SAAS,CAACV,GAAG,GAAG;MACpBW,OAAO,EAAE;QACPC,OAAO,EAAE,yBAAyB;QAClCC,MAAM,EAAE;MACV,CAAC;MACD,qBAAqB,EAAE;QACrBD,OAAO,EAAEE,MAAM,CACb,0GAA0G,CAACT,MAAM,CAACG,OAAO,CACvH,OAAO,EACP,YAAY;UACV,OAAOD,MAAM;QACf,CACF,CAAC,EACD,GACF,CAAC;QACDQ,UAAU,EAAE,IAAI;QAChBF,MAAM,EAAE,IAAI;QACZG,KAAK,EAAE,qBAAqB;QAC5BC,MAAM,EAAEd,KAAK,CAACO,SAAS,CAACQ;MAC1B,CAAC;MACD,YAAY,EAAE;QACZN,OAAO,EAAE,qDAAqD;QAC9DG,UAAU,EAAE;MACd,CAAC;MACDI,QAAQ,EAAE,CACR;QACEP,OAAO,EAAE,mDAAmD;QAC5DG,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EACL,0FAA0F;QAC5FG,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNG,OAAO,EAAE,WAAW;UACpBD,QAAQ,EAAE;QACZ;MACF,CAAC,CACF;MACD,uBAAuB,EAAE;QACvBP,OAAO,EAAEE,MAAM,CACb,yCAAyC,CAACT,MAAM,CAACG,OAAO,CACtD,OAAO,EACP,YAAY;UACV,OAAOD,MAAM;QACf,CACF,CAAC,EACD,GACF,CAAC;QACDQ,UAAU,EAAE,IAAI;QAChBF,MAAM,EAAE,IAAI;QACZG,KAAK,EAAE,qBAAqB;QAC5BC,MAAM,EAAEd,KAAK,CAACO,SAAS,CAACQ;MAC1B,CAAC;MACDG,MAAM,EAAE;QACNT,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE;MACV,CAAC;MACDO,OAAO,EAAE,sBAAsB;MAC/BE,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEnB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}