{"ast": null, "code": "/*\nLanguage: Smali\nAuthor: <PERSON> <<EMAIL>>\nDescription: Basic Smali highlighting\nWebsite: https://github.com/JesusFreke/smali\n*/\n\nfunction smali(hljs) {\n  const smali_instr_low_prio = ['add', 'and', 'cmp', 'cmpg', 'cmpl', 'const', 'div', 'double', 'float', 'goto', 'if', 'int', 'long', 'move', 'mul', 'neg', 'new', 'nop', 'not', 'or', 'rem', 'return', 'shl', 'shr', 'sput', 'sub', 'throw', 'ushr', 'xor'];\n  const smali_instr_high_prio = ['aget', 'aput', 'array', 'check', 'execute', 'fill', 'filled', 'goto/16', 'goto/32', 'iget', 'instance', 'invoke', 'iput', 'monitor', 'packed', 'sget', 'sparse'];\n  const smali_keywords = ['transient', 'constructor', 'abstract', 'final', 'synthetic', 'public', 'private', 'protected', 'static', 'bridge', 'system'];\n  return {\n    name: 'Smali',\n    contains: [{\n      className: 'string',\n      begin: '\"',\n      end: '\"',\n      relevance: 0\n    }, hljs.COMMENT('#', '$', {\n      relevance: 0\n    }), {\n      className: 'keyword',\n      variants: [{\n        begin: '\\\\s*\\\\.end\\\\s[a-zA-Z0-9]*'\n      }, {\n        begin: '^[ ]*\\\\.[a-zA-Z]*',\n        relevance: 0\n      }, {\n        begin: '\\\\s:[a-zA-Z_0-9]*',\n        relevance: 0\n      }, {\n        begin: '\\\\s(' + smali_keywords.join('|') + ')'\n      }]\n    }, {\n      className: 'built_in',\n      variants: [{\n        begin: '\\\\s(' + smali_instr_low_prio.join('|') + ')\\\\s'\n      }, {\n        begin: '\\\\s(' + smali_instr_low_prio.join('|') + ')((-|/)[a-zA-Z0-9]+)+\\\\s',\n        relevance: 10\n      }, {\n        begin: '\\\\s(' + smali_instr_high_prio.join('|') + ')((-|/)[a-zA-Z0-9]+)*\\\\s',\n        relevance: 10\n      }]\n    }, {\n      className: 'class',\n      begin: 'L[^\\(;:\\n]*;',\n      relevance: 0\n    }, {\n      begin: '[vp][0-9]+'\n    }]\n  };\n}\nmodule.exports = smali;", "map": {"version": 3, "names": ["smali", "hljs", "smali_instr_low_prio", "smali_instr_high_prio", "smali_keywords", "name", "contains", "className", "begin", "end", "relevance", "COMMENT", "variants", "join", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/smali.js"], "sourcesContent": ["/*\nLanguage: Smali\nAuthor: <PERSON> <<EMAIL>>\nDescription: Basic Smali highlighting\nWebsite: https://github.com/JesusFreke/smali\n*/\n\nfunction smali(hljs) {\n  const smali_instr_low_prio = [\n    'add',\n    'and',\n    'cmp',\n    'cmpg',\n    'cmpl',\n    'const',\n    'div',\n    'double',\n    'float',\n    'goto',\n    'if',\n    'int',\n    'long',\n    'move',\n    'mul',\n    'neg',\n    'new',\n    'nop',\n    'not',\n    'or',\n    'rem',\n    'return',\n    'shl',\n    'shr',\n    'sput',\n    'sub',\n    'throw',\n    'ushr',\n    'xor'\n  ];\n  const smali_instr_high_prio = [\n    'aget',\n    'aput',\n    'array',\n    'check',\n    'execute',\n    'fill',\n    'filled',\n    'goto/16',\n    'goto/32',\n    'iget',\n    'instance',\n    'invoke',\n    'iput',\n    'monitor',\n    'packed',\n    'sget',\n    'sparse'\n  ];\n  const smali_keywords = [\n    'transient',\n    'constructor',\n    'abstract',\n    'final',\n    'synthetic',\n    'public',\n    'private',\n    'protected',\n    'static',\n    'bridge',\n    'system'\n  ];\n  return {\n    name: 'Smali',\n    contains: [\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        relevance: 0\n      },\n      hljs.COMMENT(\n        '#',\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      {\n        className: 'keyword',\n        variants: [\n          {\n            begin: '\\\\s*\\\\.end\\\\s[a-zA-Z0-9]*'\n          },\n          {\n            begin: '^[ ]*\\\\.[a-zA-Z]*',\n            relevance: 0\n          },\n          {\n            begin: '\\\\s:[a-zA-Z_0-9]*',\n            relevance: 0\n          },\n          {\n            begin: '\\\\s(' + smali_keywords.join('|') + ')'\n          }\n        ]\n      },\n      {\n        className: 'built_in',\n        variants: [\n          {\n            begin: '\\\\s(' + smali_instr_low_prio.join('|') + ')\\\\s'\n          },\n          {\n            begin: '\\\\s(' + smali_instr_low_prio.join('|') + ')((-|/)[a-zA-Z0-9]+)+\\\\s',\n            relevance: 10\n          },\n          {\n            begin: '\\\\s(' + smali_instr_high_prio.join('|') + ')((-|/)[a-zA-Z0-9]+)*\\\\s',\n            relevance: 10\n          }\n        ]\n      },\n      {\n        className: 'class',\n        begin: 'L[^\\(;:\\n]*;',\n        relevance: 0\n      },\n      {\n        begin: '[vp][0-9]+'\n      }\n    ]\n  };\n}\n\nmodule.exports = smali;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,oBAAoB,GAAG,CAC3B,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,EACP,MAAM,EACN,IAAI,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,OAAO,EACP,MAAM,EACN,KAAK,CACN;EACD,MAAMC,qBAAqB,GAAG,CAC5B,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,EACT,MAAM,EACN,QAAQ,EACR,SAAS,EACT,SAAS,EACT,MAAM,EACN,UAAU,EACV,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,EACR,MAAM,EACN,QAAQ,CACT;EACD,MAAMC,cAAc,GAAG,CACrB,WAAW,EACX,aAAa,EACb,UAAU,EACV,OAAO,EACP,WAAW,EACX,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT;EACD,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE;IACb,CAAC,EACDT,IAAI,CAACU,OAAO,CACV,GAAG,EACH,GAAG,EACH;MACED,SAAS,EAAE;IACb,CACF,CAAC,EACD;MACEH,SAAS,EAAE,SAAS;MACpBK,QAAQ,EAAE,CACR;QACEJ,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE,mBAAmB;QAC1BE,SAAS,EAAE;MACb,CAAC,EACD;QACEF,KAAK,EAAE,mBAAmB;QAC1BE,SAAS,EAAE;MACb,CAAC,EACD;QACEF,KAAK,EAAE,MAAM,GAAGJ,cAAc,CAACS,IAAI,CAAC,GAAG,CAAC,GAAG;MAC7C,CAAC;IAEL,CAAC,EACD;MACEN,SAAS,EAAE,UAAU;MACrBK,QAAQ,EAAE,CACR;QACEJ,KAAK,EAAE,MAAM,GAAGN,oBAAoB,CAACW,IAAI,CAAC,GAAG,CAAC,GAAG;MACnD,CAAC,EACD;QACEL,KAAK,EAAE,MAAM,GAAGN,oBAAoB,CAACW,IAAI,CAAC,GAAG,CAAC,GAAG,0BAA0B;QAC3EH,SAAS,EAAE;MACb,CAAC,EACD;QACEF,KAAK,EAAE,MAAM,GAAGL,qBAAqB,CAACU,IAAI,CAAC,GAAG,CAAC,GAAG,0BAA0B;QAC5EH,SAAS,EAAE;MACb,CAAC;IAEL,CAAC,EACD;MACEH,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,cAAc;MACrBE,SAAS,EAAE;IACb,CAAC,EACD;MACEF,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGf,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}