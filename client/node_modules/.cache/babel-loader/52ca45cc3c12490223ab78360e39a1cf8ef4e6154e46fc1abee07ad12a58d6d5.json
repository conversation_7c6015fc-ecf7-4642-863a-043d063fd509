{"ast": null, "code": "import createAsyncLoading<PERSON>ighlighter from './async-syntax-highlighter';\nimport languageLoaders from './async-languages/prism';\nexport default createAsyncLoadingHighlighter({\n  loader: function loader() {\n    return import(/* webpackChunkName:\"react-syntax-highlighter/refractor-core-import\" */\n    'refractor/core').then(function (module) {\n      // Webpack 3 returns module.exports as default as module, but webpack 4 returns module.exports as module.default\n      return module[\"default\"] || module;\n    });\n  },\n  isLanguageRegistered: function isLanguageRegistered(instance, language) {\n    return instance.registered(language);\n  },\n  languageLoaders: languageLoaders,\n  registerLanguage: function registerLanguage(instance, name, language) {\n    return instance.register(language);\n  }\n});", "map": {"version": 3, "names": ["createAsyncLoadingHighlighter", "languageLoaders", "loader", "then", "module", "isLanguageRegistered", "instance", "language", "registered", "registerLanguage", "name", "register"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/prism-async-light.js"], "sourcesContent": ["import createAsyncLoading<PERSON>ighlighter from './async-syntax-highlighter';\nimport languageLoaders from './async-languages/prism';\nexport default createAsyncLoadingHighlighter({\n  loader: function loader() {\n    return import( /* webpackChunkName:\"react-syntax-highlighter/refractor-core-import\" */\n    'refractor/core').then(function (module) {\n      // Webpack 3 returns module.exports as default as module, but webpack 4 returns module.exports as module.default\n      return module[\"default\"] || module;\n    });\n  },\n  isLanguageRegistered: function isLanguageRegistered(instance, language) {\n    return instance.registered(language);\n  },\n  languageLoaders: languageLoaders,\n  registerLanguage: function registerLanguage(instance, name, language) {\n    return instance.register(language);\n  }\n});"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,4BAA4B;AACtE,OAAOC,eAAe,MAAM,yBAAyB;AACrD,eAAeD,6BAA6B,CAAC;EAC3CE,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO,MAAM,CAAE;IACf,gBAAgB,CAAC,CAACC,IAAI,CAAC,UAAUC,MAAM,EAAE;MACvC;MACA,OAAOA,MAAM,CAAC,SAAS,CAAC,IAAIA,MAAM;IACpC,CAAC,CAAC;EACJ,CAAC;EACDC,oBAAoB,EAAE,SAASA,oBAAoBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IACtE,OAAOD,QAAQ,CAACE,UAAU,CAACD,QAAQ,CAAC;EACtC,CAAC;EACDN,eAAe,EAAEA,eAAe;EAChCQ,gBAAgB,EAAE,SAASA,gBAAgBA,CAACH,QAAQ,EAAEI,IAAI,EAAEH,QAAQ,EAAE;IACpE,OAAOD,QAAQ,CAACK,QAAQ,CAACJ,QAAQ,CAAC;EACpC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}