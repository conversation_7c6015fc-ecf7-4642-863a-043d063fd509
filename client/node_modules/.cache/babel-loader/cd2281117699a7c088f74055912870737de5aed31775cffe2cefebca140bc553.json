{"ast": null, "code": "'use strict';\n\nmodule.exports = antlr4;\nantlr4.displayName = 'antlr4';\nantlr4.aliases = ['g4'];\nfunction antlr4(Prism) {\n  Prism.languages.antlr4 = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    string: {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])*'/,\n      greedy: true\n    },\n    'character-class': {\n      pattern: /\\[(?:\\\\.|[^\\\\\\]\\r\\n])*\\]/,\n      greedy: true,\n      alias: 'regex',\n      inside: {\n        range: {\n          pattern: /([^[]|(?:^|[^\\\\])(?:\\\\\\\\)*\\\\\\[)-(?!\\])/,\n          lookbehind: true,\n          alias: 'punctuation'\n        },\n        escape: /\\\\(?:u(?:[a-fA-F\\d]{4}|\\{[a-fA-F\\d]+\\})|[pP]\\{[=\\w-]+\\}|[^\\r\\nupP])/,\n        punctuation: /[\\[\\]]/\n      }\n    },\n    action: {\n      pattern: /\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\}/,\n      greedy: true,\n      inside: {\n        content: {\n          // this might be C, C++, Python, Java, C#, or any other language ANTLR4 compiles to\n          pattern: /(\\{)[\\s\\S]+(?=\\})/,\n          lookbehind: true\n        },\n        punctuation: /[{}]/\n      }\n    },\n    command: {\n      pattern: /(->\\s*(?!\\s))(?:\\s*(?:,\\s*)?\\b[a-z]\\w*(?:\\s*\\([^()\\r\\n]*\\))?)+(?=\\s*;)/i,\n      lookbehind: true,\n      inside: {\n        function: /\\b\\w+(?=\\s*(?:[,(]|$))/,\n        punctuation: /[,()]/\n      }\n    },\n    annotation: {\n      pattern: /@\\w+(?:::\\w+)*/,\n      alias: 'keyword'\n    },\n    label: {\n      pattern: /#[ \\t]*\\w+/,\n      alias: 'punctuation'\n    },\n    keyword: /\\b(?:catch|channels|finally|fragment|grammar|import|lexer|locals|mode|options|parser|returns|throws|tokens)\\b/,\n    definition: [{\n      pattern: /\\b[a-z]\\w*(?=\\s*:)/,\n      alias: ['rule', 'class-name']\n    }, {\n      pattern: /\\b[A-Z]\\w*(?=\\s*:)/,\n      alias: ['token', 'constant']\n    }],\n    constant: /\\b[A-Z][A-Z_]*\\b/,\n    operator: /\\.\\.|->|[|~]|[*+?]\\??/,\n    punctuation: /[;:()=]/\n  };\n  Prism.languages.g4 = Prism.languages.antlr4;\n}", "map": {"version": 3, "names": ["module", "exports", "antlr4", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "alias", "inside", "range", "lookbehind", "escape", "punctuation", "action", "content", "command", "function", "annotation", "label", "keyword", "definition", "constant", "operator", "g4"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/antlr4.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = antlr4\nantlr4.displayName = 'antlr4'\nantlr4.aliases = ['g4']\nfunction antlr4(Prism) {\n  Prism.languages.antlr4 = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    string: {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])*'/,\n      greedy: true\n    },\n    'character-class': {\n      pattern: /\\[(?:\\\\.|[^\\\\\\]\\r\\n])*\\]/,\n      greedy: true,\n      alias: 'regex',\n      inside: {\n        range: {\n          pattern: /([^[]|(?:^|[^\\\\])(?:\\\\\\\\)*\\\\\\[)-(?!\\])/,\n          lookbehind: true,\n          alias: 'punctuation'\n        },\n        escape:\n          /\\\\(?:u(?:[a-fA-F\\d]{4}|\\{[a-fA-F\\d]+\\})|[pP]\\{[=\\w-]+\\}|[^\\r\\nupP])/,\n        punctuation: /[\\[\\]]/\n      }\n    },\n    action: {\n      pattern: /\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\}/,\n      greedy: true,\n      inside: {\n        content: {\n          // this might be C, C++, Python, Java, C#, or any other language ANTLR4 compiles to\n          pattern: /(\\{)[\\s\\S]+(?=\\})/,\n          lookbehind: true\n        },\n        punctuation: /[{}]/\n      }\n    },\n    command: {\n      pattern:\n        /(->\\s*(?!\\s))(?:\\s*(?:,\\s*)?\\b[a-z]\\w*(?:\\s*\\([^()\\r\\n]*\\))?)+(?=\\s*;)/i,\n      lookbehind: true,\n      inside: {\n        function: /\\b\\w+(?=\\s*(?:[,(]|$))/,\n        punctuation: /[,()]/\n      }\n    },\n    annotation: {\n      pattern: /@\\w+(?:::\\w+)*/,\n      alias: 'keyword'\n    },\n    label: {\n      pattern: /#[ \\t]*\\w+/,\n      alias: 'punctuation'\n    },\n    keyword:\n      /\\b(?:catch|channels|finally|fragment|grammar|import|lexer|locals|mode|options|parser|returns|throws|tokens)\\b/,\n    definition: [\n      {\n        pattern: /\\b[a-z]\\w*(?=\\s*:)/,\n        alias: ['rule', 'class-name']\n      },\n      {\n        pattern: /\\b[A-Z]\\w*(?=\\s*:)/,\n        alias: ['token', 'constant']\n      }\n    ],\n    constant: /\\b[A-Z][A-Z_]*\\b/,\n    operator: /\\.\\.|->|[|~]|[*+?]\\??/,\n    punctuation: /[;:()=]/\n  }\n  Prism.languages.g4 = Prism.languages.antlr4\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AACvB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE,+BAA+B;IACxCC,MAAM,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,MAAM,EAAE;IACV,CAAC;IACD,iBAAiB,EAAE;MACjBD,OAAO,EAAE,0BAA0B;MACnCC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE;QACNC,KAAK,EAAE;UACLJ,OAAO,EAAE,wCAAwC;UACjDK,UAAU,EAAE,IAAI;UAChBH,KAAK,EAAE;QACT,CAAC;QACDI,MAAM,EACJ,qEAAqE;QACvEC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,MAAM,EAAE;MACNR,OAAO,EAAE,yDAAyD;MAClEC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACNM,OAAO,EAAE;UACP;UACAT,OAAO,EAAE,mBAAmB;UAC5BK,UAAU,EAAE;QACd,CAAC;QACDE,WAAW,EAAE;MACf;IACF,CAAC;IACDG,OAAO,EAAE;MACPV,OAAO,EACL,yEAAyE;MAC3EK,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;QACNQ,QAAQ,EAAE,wBAAwB;QAClCJ,WAAW,EAAE;MACf;IACF,CAAC;IACDK,UAAU,EAAE;MACVZ,OAAO,EAAE,gBAAgB;MACzBE,KAAK,EAAE;IACT,CAAC;IACDW,KAAK,EAAE;MACLb,OAAO,EAAE,YAAY;MACrBE,KAAK,EAAE;IACT,CAAC;IACDY,OAAO,EACL,+GAA+G;IACjHC,UAAU,EAAE,CACV;MACEf,OAAO,EAAE,oBAAoB;MAC7BE,KAAK,EAAE,CAAC,MAAM,EAAE,YAAY;IAC9B,CAAC,EACD;MACEF,OAAO,EAAE,oBAAoB;MAC7BE,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU;IAC7B,CAAC,CACF;IACDc,QAAQ,EAAE,kBAAkB;IAC5BC,QAAQ,EAAE,uBAAuB;IACjCV,WAAW,EAAE;EACf,CAAC;EACDX,KAAK,CAACC,SAAS,CAACqB,EAAE,GAAGtB,KAAK,CAACC,SAAS,CAACJ,MAAM;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}