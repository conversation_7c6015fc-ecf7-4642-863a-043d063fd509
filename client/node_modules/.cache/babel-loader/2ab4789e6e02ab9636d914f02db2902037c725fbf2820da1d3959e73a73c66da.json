{"ast": null, "code": "'use strict';\n\nmodule.exports = qsharp;\nqsharp.displayName = 'qsharp';\nqsharp.aliases = ['qs'];\nfunction qsharp(Prism) {\n  ;\n  (function (Prism) {\n    /**\n     * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n     *\n     * Note: This is a simple text based replacement. Be careful when using backreferences!\n     *\n     * @param {string} pattern the given pattern.\n     * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n     * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n     * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n     */\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return '(?:' + replacements[+index] + ')';\n      });\n    }\n    /**\n     * @param {string} pattern\n     * @param {string[]} replacements\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '');\n    }\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<<self>>/g, function () {\n          return '(?:' + pattern + ')';\n        });\n      }\n      return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]');\n    } // https://docs.microsoft.com/en-us/azure/quantum/user-guide/language/typesystem/\n    // https://github.com/microsoft/qsharp-language/tree/main/Specifications/Language/5_Grammar\n    var keywordKinds = {\n      // keywords which represent a return or variable type\n      type: 'Adj BigInt Bool Ctl Double false Int One Pauli PauliI PauliX PauliY PauliZ Qubit Range Result String true Unit Zero',\n      // all other keywords\n      other: 'Adjoint adjoint apply as auto body borrow borrowing Controlled controlled distribute elif else fail fixup for function if in internal intrinsic invert is let mutable namespace new newtype open operation repeat return self set until use using while within'\n    }; // keywords\n    function keywordsToPattern(words) {\n      return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b';\n    }\n    var keywords = RegExp(keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.other)); // types\n    var identifier = /\\b[A-Za-z_]\\w*\\b/.source;\n    var qualifiedName = replace(/<<0>>(?:\\s*\\.\\s*<<0>>)*/.source, [identifier]);\n    var typeInside = {\n      keyword: keywords,\n      punctuation: /[<>()?,.:[\\]]/\n    }; // strings\n    var regularString = /\"(?:\\\\.|[^\\\\\"])*\"/.source;\n    Prism.languages.qsharp = Prism.languages.extend('clike', {\n      comment: /\\/\\/.*/,\n      string: [{\n        pattern: re(/(^|[^$\\\\])<<0>>/.source, [regularString]),\n        lookbehind: true,\n        greedy: true\n      }],\n      'class-name': [{\n        // open Microsoft.Quantum.Canon;\n        // open Microsoft.Quantum.Canon as CN;\n        pattern: re(/(\\b(?:as|open)\\s+)<<0>>(?=\\s*(?:;|as\\b))/.source, [qualifiedName]),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // namespace Quantum.App1;\n        pattern: re(/(\\bnamespace\\s+)<<0>>(?=\\s*\\{)/.source, [qualifiedName]),\n        lookbehind: true,\n        inside: typeInside\n      }],\n      keyword: keywords,\n      number: /(?:\\b0(?:x[\\da-f]+|b[01]+|o[0-7]+)|(?:\\B\\.\\d+|\\b\\d+(?:\\.\\d*)?)(?:e[-+]?\\d+)?)l?\\b/i,\n      operator: /\\band=|\\bor=|\\band\\b|\\bnot\\b|\\bor\\b|<[-=]|[-=]>|>>>=?|<<<=?|\\^\\^\\^=?|\\|\\|\\|=?|&&&=?|w\\/=?|~~~|[*\\/+\\-^=!%]=?/,\n      punctuation: /::|[{}[\\];(),.:]/\n    });\n    Prism.languages.insertBefore('qsharp', 'number', {\n      range: {\n        pattern: /\\.\\./,\n        alias: 'operator'\n      }\n    }); // single line\n    var interpolationExpr = nested(replace(/\\{(?:[^\"{}]|<<0>>|<<self>>)*\\}/.source, [regularString]), 2);\n    Prism.languages.insertBefore('qsharp', 'string', {\n      'interpolation-string': {\n        pattern: re(/\\$\"(?:\\\\.|<<0>>|[^\\\\\"{])*\"/.source, [interpolationExpr]),\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: re(/((?:^|[^\\\\])(?:\\\\\\\\)*)<<0>>/.source, [interpolationExpr]),\n            lookbehind: true,\n            inside: {\n              punctuation: /^\\{|\\}$/,\n              expression: {\n                pattern: /[\\s\\S]+/,\n                alias: 'language-qsharp',\n                inside: Prism.languages.qsharp\n              }\n            }\n          },\n          string: /[\\s\\S]+/\n        }\n      }\n    });\n  })(Prism);\n  Prism.languages.qs = Prism.languages.qsharp;\n}", "map": {"version": 3, "names": ["module", "exports", "qsharp", "displayName", "aliases", "Prism", "replace", "pattern", "replacements", "m", "index", "re", "flags", "RegExp", "nested", "depthLog2", "i", "keywordKinds", "type", "other", "keywordsToPattern", "words", "trim", "keywords", "identifier", "source", "qualifiedName", "typeInside", "keyword", "punctuation", "regularString", "languages", "extend", "comment", "string", "lookbehind", "greedy", "inside", "number", "operator", "insertBefore", "range", "alias", "interpolationExpr", "interpolation", "expression", "qs"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/qsharp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = qsharp\nqsharp.displayName = 'qsharp'\nqsharp.aliases = ['qs']\nfunction qsharp(Prism) {\n  ;(function (Prism) {\n    /**\n     * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n     *\n     * Note: This is a simple text based replacement. Be careful when using backreferences!\n     *\n     * @param {string} pattern the given pattern.\n     * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n     * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n     * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n     */\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return '(?:' + replacements[+index] + ')'\n      })\n    }\n    /**\n     * @param {string} pattern\n     * @param {string[]} replacements\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<<self>>/g, function () {\n          return '(?:' + pattern + ')'\n        })\n      }\n      return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]')\n    } // https://docs.microsoft.com/en-us/azure/quantum/user-guide/language/typesystem/\n    // https://github.com/microsoft/qsharp-language/tree/main/Specifications/Language/5_Grammar\n    var keywordKinds = {\n      // keywords which represent a return or variable type\n      type: 'Adj BigInt Bool Ctl Double false Int One Pauli PauliI PauliX PauliY PauliZ Qubit Range Result String true Unit Zero',\n      // all other keywords\n      other:\n        'Adjoint adjoint apply as auto body borrow borrowing Controlled controlled distribute elif else fail fixup for function if in internal intrinsic invert is let mutable namespace new newtype open operation repeat return self set until use using while within'\n    } // keywords\n    function keywordsToPattern(words) {\n      return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b'\n    }\n    var keywords = RegExp(\n      keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.other)\n    ) // types\n    var identifier = /\\b[A-Za-z_]\\w*\\b/.source\n    var qualifiedName = replace(/<<0>>(?:\\s*\\.\\s*<<0>>)*/.source, [identifier])\n    var typeInside = {\n      keyword: keywords,\n      punctuation: /[<>()?,.:[\\]]/\n    } // strings\n    var regularString = /\"(?:\\\\.|[^\\\\\"])*\"/.source\n    Prism.languages.qsharp = Prism.languages.extend('clike', {\n      comment: /\\/\\/.*/,\n      string: [\n        {\n          pattern: re(/(^|[^$\\\\])<<0>>/.source, [regularString]),\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'class-name': [\n        {\n          // open Microsoft.Quantum.Canon;\n          // open Microsoft.Quantum.Canon as CN;\n          pattern: re(/(\\b(?:as|open)\\s+)<<0>>(?=\\s*(?:;|as\\b))/.source, [\n            qualifiedName\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // namespace Quantum.App1;\n          pattern: re(/(\\bnamespace\\s+)<<0>>(?=\\s*\\{)/.source, [qualifiedName]),\n          lookbehind: true,\n          inside: typeInside\n        }\n      ],\n      keyword: keywords,\n      number:\n        /(?:\\b0(?:x[\\da-f]+|b[01]+|o[0-7]+)|(?:\\B\\.\\d+|\\b\\d+(?:\\.\\d*)?)(?:e[-+]?\\d+)?)l?\\b/i,\n      operator:\n        /\\band=|\\bor=|\\band\\b|\\bnot\\b|\\bor\\b|<[-=]|[-=]>|>>>=?|<<<=?|\\^\\^\\^=?|\\|\\|\\|=?|&&&=?|w\\/=?|~~~|[*\\/+\\-^=!%]=?/,\n      punctuation: /::|[{}[\\];(),.:]/\n    })\n    Prism.languages.insertBefore('qsharp', 'number', {\n      range: {\n        pattern: /\\.\\./,\n        alias: 'operator'\n      }\n    }) // single line\n    var interpolationExpr = nested(\n      replace(/\\{(?:[^\"{}]|<<0>>|<<self>>)*\\}/.source, [regularString]),\n      2\n    )\n    Prism.languages.insertBefore('qsharp', 'string', {\n      'interpolation-string': {\n        pattern: re(/\\$\"(?:\\\\.|<<0>>|[^\\\\\"{])*\"/.source, [interpolationExpr]),\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: re(/((?:^|[^\\\\])(?:\\\\\\\\)*)<<0>>/.source, [\n              interpolationExpr\n            ]),\n            lookbehind: true,\n            inside: {\n              punctuation: /^\\{|\\}$/,\n              expression: {\n                pattern: /[\\s\\S]+/,\n                alias: 'language-qsharp',\n                inside: Prism.languages.qsharp\n              }\n            }\n          },\n          string: /[\\s\\S]+/\n        }\n      }\n    })\n  })(Prism)\n  Prism.languages.qs = Prism.languages.qsharp\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AACvB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,OAAOA,CAACC,OAAO,EAAEC,YAAY,EAAE;MACtC,OAAOD,OAAO,CAACD,OAAO,CAAC,YAAY,EAAE,UAAUG,CAAC,EAAEC,KAAK,EAAE;QACvD,OAAO,KAAK,GAAGF,YAAY,CAAC,CAACE,KAAK,CAAC,GAAG,GAAG;MAC3C,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,EAAEA,CAACJ,OAAO,EAAEC,YAAY,EAAEI,KAAK,EAAE;MACxC,OAAOC,MAAM,CAACP,OAAO,CAACC,OAAO,EAAEC,YAAY,CAAC,EAAEI,KAAK,IAAI,EAAE,CAAC;IAC5D;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASE,MAAMA,CAACP,OAAO,EAAEQ,SAAS,EAAE;MAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClCT,OAAO,GAAGA,OAAO,CAACD,OAAO,CAAC,WAAW,EAAE,YAAY;UACjD,OAAO,KAAK,GAAGC,OAAO,GAAG,GAAG;QAC9B,CAAC,CAAC;MACJ;MACA,OAAOA,OAAO,CAACD,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC;IAClD,CAAC,CAAC;IACF;IACA,IAAIW,YAAY,GAAG;MACjB;MACAC,IAAI,EAAE,qHAAqH;MAC3H;MACAC,KAAK,EACH;IACJ,CAAC,EAAC;IACF,SAASC,iBAAiBA,CAACC,KAAK,EAAE;MAChC,OAAO,QAAQ,GAAGA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAChB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,MAAM;IAC5D;IACA,IAAIiB,QAAQ,GAAGV,MAAM,CACnBO,iBAAiB,CAACH,YAAY,CAACC,IAAI,GAAG,GAAG,GAAGD,YAAY,CAACE,KAAK,CAChE,CAAC,EAAC;IACF,IAAIK,UAAU,GAAG,kBAAkB,CAACC,MAAM;IAC1C,IAAIC,aAAa,GAAGpB,OAAO,CAAC,yBAAyB,CAACmB,MAAM,EAAE,CAACD,UAAU,CAAC,CAAC;IAC3E,IAAIG,UAAU,GAAG;MACfC,OAAO,EAAEL,QAAQ;MACjBM,WAAW,EAAE;IACf,CAAC,EAAC;IACF,IAAIC,aAAa,GAAG,mBAAmB,CAACL,MAAM;IAC9CpB,KAAK,CAAC0B,SAAS,CAAC7B,MAAM,GAAGG,KAAK,CAAC0B,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MACvDC,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,CACN;QACE3B,OAAO,EAAEI,EAAE,CAAC,iBAAiB,CAACc,MAAM,EAAE,CAACK,aAAa,CAAC,CAAC;QACtDK,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACD,YAAY,EAAE,CACZ;QACE;QACA;QACA7B,OAAO,EAAEI,EAAE,CAAC,0CAA0C,CAACc,MAAM,EAAE,CAC7DC,aAAa,CACd,CAAC;QACFS,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEV;MACV,CAAC,EACD;QACE;QACApB,OAAO,EAAEI,EAAE,CAAC,gCAAgC,CAACc,MAAM,EAAE,CAACC,aAAa,CAAC,CAAC;QACrES,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEV;MACV,CAAC,CACF;MACDC,OAAO,EAAEL,QAAQ;MACjBe,MAAM,EACJ,oFAAoF;MACtFC,QAAQ,EACN,8GAA8G;MAChHV,WAAW,EAAE;IACf,CAAC,CAAC;IACFxB,KAAK,CAAC0B,SAAS,CAACS,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;MAC/CC,KAAK,EAAE;QACLlC,OAAO,EAAE,MAAM;QACfmC,KAAK,EAAE;MACT;IACF,CAAC,CAAC,EAAC;IACH,IAAIC,iBAAiB,GAAG7B,MAAM,CAC5BR,OAAO,CAAC,gCAAgC,CAACmB,MAAM,EAAE,CAACK,aAAa,CAAC,CAAC,EACjE,CACF,CAAC;IACDzB,KAAK,CAAC0B,SAAS,CAACS,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;MAC/C,sBAAsB,EAAE;QACtBjC,OAAO,EAAEI,EAAE,CAAC,4BAA4B,CAACc,MAAM,EAAE,CAACkB,iBAAiB,CAAC,CAAC;QACrEP,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNO,aAAa,EAAE;YACbrC,OAAO,EAAEI,EAAE,CAAC,6BAA6B,CAACc,MAAM,EAAE,CAChDkB,iBAAiB,CAClB,CAAC;YACFR,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAE;cACNR,WAAW,EAAE,SAAS;cACtBgB,UAAU,EAAE;gBACVtC,OAAO,EAAE,SAAS;gBAClBmC,KAAK,EAAE,iBAAiB;gBACxBL,MAAM,EAAEhC,KAAK,CAAC0B,SAAS,CAAC7B;cAC1B;YACF;UACF,CAAC;UACDgC,MAAM,EAAE;QACV;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE7B,KAAK,CAAC;EACTA,KAAK,CAAC0B,SAAS,CAACe,EAAE,GAAGzC,KAAK,CAAC0B,SAAS,CAAC7B,MAAM;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}