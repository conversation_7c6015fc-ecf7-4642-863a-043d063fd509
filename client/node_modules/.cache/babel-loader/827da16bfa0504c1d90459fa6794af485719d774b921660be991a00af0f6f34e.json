{"ast": null, "code": "/*\nLanguage: Batch file (DOS)\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Batch_file\n*/\n\n/** @type LanguageFn */\nfunction dos(hljs) {\n  const COMMENT = hljs.COMMENT(/^\\s*@?rem\\b/, /$/, {\n    relevance: 10\n  });\n  const LABEL = {\n    className: 'symbol',\n    begin: '^\\\\s*[A-Za-z._?][A-Za-z0-9_$#@~.?]*(:|\\\\s+label)',\n    relevance: 0\n  };\n  return {\n    name: 'Batch file (DOS)',\n    aliases: ['bat', 'cmd'],\n    case_insensitive: true,\n    illegal: /\\/\\*/,\n    keywords: {\n      keyword: 'if else goto for in do call exit not exist errorlevel defined ' + 'equ neq lss leq gtr geq',\n      built_in: 'prn nul lpt3 lpt2 lpt1 con com4 com3 com2 com1 aux ' + 'shift cd dir echo setlocal endlocal set pause copy ' + 'append assoc at attrib break cacls cd chcp chdir chkdsk chkntfs cls cmd color ' + 'comp compact convert date dir diskcomp diskcopy doskey erase fs ' + 'find findstr format ftype graftabl help keyb label md mkdir mode more move path ' + 'pause print popd pushd promt rd recover rem rename replace restore rmdir shift ' + 'sort start subst time title tree type ver verify vol ' +\n      // winutils\n      'ping net ipconfig taskkill xcopy ren del'\n    },\n    contains: [{\n      className: 'variable',\n      begin: /%%[^ ]|%[^ ]+?%|![^ ]+?!/\n    }, {\n      className: 'function',\n      begin: LABEL.begin,\n      end: 'goto:eof',\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*'\n      }), COMMENT]\n    }, {\n      className: 'number',\n      begin: '\\\\b\\\\d+',\n      relevance: 0\n    }, COMMENT]\n  };\n}\nmodule.exports = dos;", "map": {"version": 3, "names": ["dos", "hljs", "COMMENT", "relevance", "LABEL", "className", "begin", "name", "aliases", "case_insensitive", "illegal", "keywords", "keyword", "built_in", "contains", "end", "inherit", "TITLE_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/dos.js"], "sourcesContent": ["/*\nLanguage: Batch file (DOS)\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Batch_file\n*/\n\n/** @type LanguageFn */\nfunction dos(hljs) {\n  const COMMENT = hljs.COMMENT(\n    /^\\s*@?rem\\b/, /$/,\n    {\n      relevance: 10\n    }\n  );\n  const LABEL = {\n    className: 'symbol',\n    begin: '^\\\\s*[A-Za-z._?][A-Za-z0-9_$#@~.?]*(:|\\\\s+label)',\n    relevance: 0\n  };\n  return {\n    name: 'Batch file (DOS)',\n    aliases: [\n      'bat',\n      'cmd'\n    ],\n    case_insensitive: true,\n    illegal: /\\/\\*/,\n    keywords: {\n      keyword:\n        'if else goto for in do call exit not exist errorlevel defined ' +\n        'equ neq lss leq gtr geq',\n      built_in:\n        'prn nul lpt3 lpt2 lpt1 con com4 com3 com2 com1 aux ' +\n        'shift cd dir echo setlocal endlocal set pause copy ' +\n        'append assoc at attrib break cacls cd chcp chdir chkdsk chkntfs cls cmd color ' +\n        'comp compact convert date dir diskcomp diskcopy doskey erase fs ' +\n        'find findstr format ftype graftabl help keyb label md mkdir mode more move path ' +\n        'pause print popd pushd promt rd recover rem rename replace restore rmdir shift ' +\n        'sort start subst time title tree type ver verify vol ' +\n        // winutils\n        'ping net ipconfig taskkill xcopy ren del'\n    },\n    contains: [\n      {\n        className: 'variable',\n        begin: /%%[^ ]|%[^ ]+?%|![^ ]+?!/\n      },\n      {\n        className: 'function',\n        begin: LABEL.begin,\n        end: 'goto:eof',\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*'\n          }),\n          COMMENT\n        ]\n      },\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d+',\n        relevance: 0\n      },\n      COMMENT\n    ]\n  };\n}\n\nmodule.exports = dos;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,OAAO,GAAGD,IAAI,CAACC,OAAO,CAC1B,aAAa,EAAE,GAAG,EAClB;IACEC,SAAS,EAAE;EACb,CACF,CAAC;EACD,MAAMC,KAAK,GAAG;IACZC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,kDAAkD;IACzDH,SAAS,EAAE;EACb,CAAC;EACD,OAAO;IACLI,IAAI,EAAE,kBAAkB;IACxBC,OAAO,EAAE,CACP,KAAK,EACL,KAAK,CACN;IACDC,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE;MACRC,OAAO,EACL,gEAAgE,GAChE,yBAAyB;MAC3BC,QAAQ,EACN,qDAAqD,GACrD,qDAAqD,GACrD,gFAAgF,GAChF,kEAAkE,GAClE,kFAAkF,GAClF,iFAAiF,GACjF,uDAAuD;MACvD;MACA;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACET,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAEF,KAAK,CAACE,KAAK;MAClBS,GAAG,EAAE,UAAU;MACfD,QAAQ,EAAE,CACRb,IAAI,CAACe,OAAO,CAACf,IAAI,CAACgB,UAAU,EAAE;QAC5BX,KAAK,EAAE;MACT,CAAC,CAAC,EACFJ,OAAO;IAEX,CAAC,EACD;MACEG,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,SAAS;MAChBH,SAAS,EAAE;IACb,CAAC,EACDD,OAAO;EAEX,CAAC;AACH;AAEAgB,MAAM,CAACC,OAAO,GAAGnB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}