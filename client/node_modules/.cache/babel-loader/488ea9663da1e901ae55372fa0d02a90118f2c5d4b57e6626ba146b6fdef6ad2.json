{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Erlang REPL\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.erlang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction erlangRepl(hljs) {\n  return {\n    name: 'Erlang REPL',\n    keywords: {\n      built_in: 'spawn spawn_link self',\n      keyword: 'after and andalso|10 band begin bnot bor bsl bsr bxor case catch cond div end fun if ' + 'let not of or orelse|10 query receive rem try when xor'\n    },\n    contains: [{\n      className: 'meta',\n      begin: '^[0-9]+> ',\n      relevance: 10\n    }, hljs.COMMENT('%', '$'), {\n      className: 'number',\n      begin: '\\\\b(\\\\d+(_\\\\d+)*#[a-fA-F0-9]+(_[a-fA-F0-9]+)*|\\\\d+(_\\\\d+)*(\\\\.\\\\d+(_\\\\d+)*)?([eE][-+]?\\\\d+)?)',\n      relevance: 0\n    }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n      begin: concat(/\\?(::)?/, /([A-Z]\\w*)/,\n      // at least one identifier\n      /((::)[A-Z]\\w*)*/ // perhaps more\n      )\n    }, {\n      begin: '->'\n    }, {\n      begin: 'ok'\n    }, {\n      begin: '!'\n    }, {\n      begin: '(\\\\b[a-z\\'][a-zA-Z0-9_\\']*:[a-z\\'][a-zA-Z0-9_\\']*)|(\\\\b[a-z\\'][a-zA-Z0-9_\\']*)',\n      relevance: 0\n    }, {\n      begin: '[A-Z][a-zA-Z0-9_\\']*',\n      relevance: 0\n    }]\n  };\n}\nmodule.exports = erlangRepl;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "erlangRepl", "hljs", "name", "keywords", "built_in", "keyword", "contains", "className", "begin", "relevance", "COMMENT", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/erlang-repl.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Erlang REPL\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.erlang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction erlangRepl(hljs) {\n  return {\n    name: 'Erlang REPL',\n    keywords: {\n      built_in:\n        'spawn spawn_link self',\n      keyword:\n        'after and andalso|10 band begin bnot bor bsl bsr bxor case catch cond div end fun if ' +\n        'let not of or orelse|10 query receive rem try when xor'\n    },\n    contains: [\n      {\n        className: 'meta',\n        begin: '^[0-9]+> ',\n        relevance: 10\n      },\n      hljs.COMMENT('%', '$'),\n      {\n        className: 'number',\n        begin: '\\\\b(\\\\d+(_\\\\d+)*#[a-fA-F0-9]+(_[a-fA-F0-9]+)*|\\\\d+(_\\\\d+)*(\\\\.\\\\d+(_\\\\d+)*)?([eE][-+]?\\\\d+)?)',\n        relevance: 0\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        begin: concat(\n          /\\?(::)?/,\n          /([A-Z]\\w*)/, // at least one identifier\n          /((::)[A-Z]\\w*)*/ // perhaps more\n        )\n      },\n      {\n        begin: '->'\n      },\n      {\n        begin: 'ok'\n      },\n      {\n        begin: '!'\n      },\n      {\n        begin: '(\\\\b[a-z\\'][a-zA-Z0-9_\\']*:[a-z\\'][a-zA-Z0-9_\\']*)|(\\\\b[a-z\\'][a-zA-Z0-9_\\']*)',\n        relevance: 0\n      },\n      {\n        begin: '[A-Z][a-zA-Z0-9_\\']*',\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = erlangRepl;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO;IACLC,IAAI,EAAE,aAAa;IACnBC,QAAQ,EAAE;MACRC,QAAQ,EACN,uBAAuB;MACzBC,OAAO,EACL,uFAAuF,GACvF;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,WAAW;MAClBC,SAAS,EAAE;IACb,CAAC,EACDR,IAAI,CAACS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EACtB;MACEH,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,+FAA+F;MACtGC,SAAS,EAAE;IACb,CAAC,EACDR,IAAI,CAACU,gBAAgB,EACrBV,IAAI,CAACW,iBAAiB,EACtB;MACEJ,KAAK,EAAEd,MAAM,CACX,SAAS,EACT,YAAY;MAAE;MACd,iBAAiB,CAAC;MACpB;IACF,CAAC,EACD;MACEc,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,gFAAgF;MACvFC,SAAS,EAAE;IACb,CAAC,EACD;MACED,KAAK,EAAE,sBAAsB;MAC7BC,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAI,MAAM,CAACC,OAAO,GAAGd,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}