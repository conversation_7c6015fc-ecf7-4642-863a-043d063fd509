{"ast": null, "code": "'use strict';\n\nmodule.exports = factor;\nfactor.displayName = 'factor';\nfactor.aliases = [];\nfunction factor(Prism) {\n  ;\n  (function (Prism) {\n    var comment_inside = {\n      function: /\\b(?:BUGS?|FIX(?:MES?)?|NOTES?|TODOS?|XX+|HACKS?|WARN(?:ING)?|\\?{2,}|!{2,})\\b/\n    };\n    var string_inside = {\n      number: /\\\\[^\\s']|%\\w/\n    };\n    var factor = {\n      comment: [{\n        // ! single-line exclamation point comments with whitespace after/around the !\n        pattern: /(^|\\s)(?:! .*|!$)/,\n        lookbehind: true,\n        inside: comment_inside\n      }, /* from basis/multiline: */\n      {\n        // /* comment */, /* comment*/\n        pattern: /(^|\\s)\\/\\*\\s[\\s\\S]*?\\*\\/(?=\\s|$)/,\n        lookbehind: true,\n        greedy: true,\n        inside: comment_inside\n      }, {\n        // ![[ comment ]] , ![===[ comment]===]\n        pattern: /(^|\\s)!\\[(={0,6})\\[\\s[\\s\\S]*?\\]\\2\\](?=\\s|$)/,\n        lookbehind: true,\n        greedy: true,\n        inside: comment_inside\n      }],\n      number: [{\n        // basic base 10 integers 9, -9\n        pattern: /(^|\\s)[+-]?\\d+(?=\\s|$)/,\n        lookbehind: true\n      }, {\n        // base prefix integers 0b010 0o70 0xad 0d10 0XAD -0xa9\n        pattern: /(^|\\s)[+-]?0(?:b[01]+|o[0-7]+|d\\d+|x[\\dA-F]+)(?=\\s|$)/i,\n        lookbehind: true\n      }, {\n        // fractional ratios 1/5 -1/5 and the literal float approximations 1/5. -1/5.\n        pattern: /(^|\\s)[+-]?\\d+\\/\\d+\\.?(?=\\s|$)/,\n        lookbehind: true\n      }, {\n        // positive mixed numbers 23+1/5 +23+1/5\n        pattern: /(^|\\s)\\+?\\d+\\+\\d+\\/\\d+(?=\\s|$)/,\n        lookbehind: true\n      }, {\n        // negative mixed numbers -23-1/5\n        pattern: /(^|\\s)-\\d+-\\d+\\/\\d+(?=\\s|$)/,\n        lookbehind: true\n      }, {\n        // basic decimal floats -0.01 0. .0 .1 -.1 -1. -12.13 +12.13\n        // and scientific notation with base 10 exponents 3e4 3e-4 .3e-4\n        pattern: /(^|\\s)[+-]?(?:\\d*\\.\\d+|\\d+\\.\\d*|\\d+)(?:e[+-]?\\d+)?(?=\\s|$)/i,\n        lookbehind: true\n      }, {\n        // NAN literal syntax NAN: 80000deadbeef, NAN: a\n        pattern: /(^|\\s)NAN:\\s+[\\da-fA-F]+(?=\\s|$)/,\n        lookbehind: true\n      }, {\n        /*\n        base prefix floats 0x1.0p3 (8.0) 0b1.010p2 (5.0) 0x1.p1 0b1.11111111p11111...\n        \"The normalized hex form ±0x1.MMMMMMMMMMMMM[pP]±EEEE allows any floating-point number to be specified precisely.\n        The values of MMMMMMMMMMMMM and EEEE map directly to the mantissa and exponent fields of the binary IEEE 754 representation.\"\n        <https://docs.factorcode.org/content/article-syntax-floats.html>\n        */\n        pattern: /(^|\\s)[+-]?0(?:b1\\.[01]*|o1\\.[0-7]*|d1\\.\\d*|x1\\.[\\dA-F]*)p\\d+(?=\\s|$)/i,\n        lookbehind: true\n      }],\n      // R/ regexp?\\/\\\\/\n      regexp: {\n        pattern: /(^|\\s)R\\/\\s(?:\\\\\\S|[^\\\\/])*\\/(?:[idmsr]*|[idmsr]+-[idmsr]+)(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'number',\n        inside: {\n          variable: /\\\\\\S/,\n          keyword: /[+?*\\[\\]^$(){}.|]/,\n          operator: {\n            pattern: /(\\/)[idmsr]+(?:-[idmsr]+)?/,\n            lookbehind: true\n          }\n        }\n      },\n      boolean: {\n        pattern: /(^|\\s)[tf](?=\\s|$)/,\n        lookbehind: true\n      },\n      // SBUF\" asd\", URL\" ://...\", P\" /etc/\"\n      'custom-string': {\n        pattern: /(^|\\s)[A-Z0-9\\-]+\"\\s(?:\\\\\\S|[^\"\\\\])*\"/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          number: /\\\\\\S|%\\w|\\//\n        }\n      },\n      'multiline-string': [{\n        // STRING: name \\n content \\n ; -> CONSTANT: name \"content\" (symbol)\n        pattern: /(^|\\s)STRING:\\s+\\S+(?:\\n|\\r\\n).*(?:\\n|\\r\\n)\\s*;(?=\\s|$)/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          number: string_inside.number,\n          // trailing semicolon on its own line\n          'semicolon-or-setlocal': {\n            pattern: /([\\r\\n][ \\t]*);(?=\\s|$)/,\n            lookbehind: true,\n            alias: 'function'\n          }\n        }\n      }, {\n        // HEREDOC: marker \\n content \\n marker ; -> \"content\" (immediate)\n        pattern: /(^|\\s)HEREDOC:\\s+\\S+(?:\\n|\\r\\n).*(?:\\n|\\r\\n)\\s*\\S+(?=\\s|$)/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'string',\n        inside: string_inside\n      }, {\n        // [[ string ]], [==[ string]==]\n        pattern: /(^|\\s)\\[(={0,6})\\[\\s[\\s\\S]*?\\]\\2\\](?=\\s|$)/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'string',\n        inside: string_inside\n      }],\n      'special-using': {\n        pattern: /(^|\\s)USING:(?:\\s\\S+)*(?=\\s+;(?:\\s|$))/,\n        lookbehind: true,\n        alias: 'function',\n        inside: {\n          // this is essentially a regex for vocab names, which i don't want to specify\n          // but the USING: gets picked up as a vocab name\n          string: {\n            pattern: /(\\s)[^:\\s]+/,\n            lookbehind: true\n          }\n        }\n      },\n      /* this description of stack effect literal syntax is not complete and not as specific as theoretically possible\n      trying to do better is more work and regex-computation-time than it's worth though.\n      - we'd like to have the \"delimiter\" parts of the stack effect [ (, --, and ) ] be a different (less-important or comment-like) colour to the stack effect contents\n      - we'd like if nested stack effects were treated as such rather than just appearing flat (with `inside`)\n      - we'd like if the following variable name conventions were recognised specifically:\n      special row variables = ..a b..\n      type and stack effect annotations end with a colon = ( quot: ( a: ( -- ) -- b ) -- x ), ( x: number -- )\n      word throws unconditional error = *\n      any other word-like variable name = a ? q' etc\n      https://docs.factorcode.org/content/article-effects.html\n      these are pretty complicated to highlight properly without a real parser, and therefore out of scope\n      the old pattern, which may be later useful, was: (^|\\s)(?:call|execute|eval)?\\((?:\\s+[^\"\\r\\n\\t ]\\S*)*?\\s+--(?:\\s+[^\"\\n\\t ]\\S*)*?\\s+\\)(?=\\s|$)\n      */\n      // current solution is not great\n      'stack-effect-delimiter': [{\n        // opening parenthesis\n        pattern: /(^|\\s)(?:call|eval|execute)?\\((?=\\s)/,\n        lookbehind: true,\n        alias: 'operator'\n      }, {\n        // middle --\n        pattern: /(\\s)--(?=\\s)/,\n        lookbehind: true,\n        alias: 'operator'\n      }, {\n        // closing parenthesis\n        pattern: /(\\s)\\)(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'operator'\n      }],\n      combinators: {\n        pattern: null,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'kernel-builtin': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      'sequences-builtin': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      'math-builtin': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      'constructor-word': {\n        // <array> but not <=>\n        pattern: /(^|\\s)<(?!=+>|-+>)\\S+>(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'other-builtin-syntax': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      /*\n      full list of supported word naming conventions: (the convention appears outside of the [brackets])\n      set-[x]\n      change-[x]\n      with-[x]\n      new-[x]\n      >[string]\n      [base]>\n      [string]>[number]\n      +[symbol]+\n      [boolean-word]?\n      ?[of]\n      [slot-reader]>>\n      >>[slot-setter]\n      [slot-writer]<<\n      ([implementation-detail])\n      [mutater]!\n      [variant]*\n      [prettyprint].\n      $[help-markup]\n      <constructors>, SYNTAX:, etc are supported by their own patterns.\n      `with` and `new` from `kernel` are their own builtins.\n      see <https://docs.factorcode.org/content/article-conventions.html>\n      */\n      'conventionally-named-word': {\n        pattern: /(^|\\s)(?!\")(?:(?:change|new|set|with)-\\S+|\\$\\S+|>[^>\\s]+|[^:>\\s]+>|[^>\\s]+>[^>\\s]+|\\+[^+\\s]+\\+|[^?\\s]+\\?|\\?[^?\\s]+|[^>\\s]+>>|>>[^>\\s]+|[^<\\s]+<<|\\([^()\\s]+\\)|[^!\\s]+!|[^*\\s]\\S*\\*|[^.\\s]\\S*\\.)(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'colon-syntax': {\n        pattern: /(^|\\s)(?:[A-Z0-9\\-]+#?)?:{1,2}\\s+(?:;\\S+|(?!;)\\S+)(?=\\s|$)/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'function'\n      },\n      'semicolon-or-setlocal': {\n        pattern: /(\\s)(?:;|:>)(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      // do not highlight leading } or trailing X{ at the begin/end of the file as it's invalid syntax\n      'curly-brace-literal-delimiter': [{\n        // opening\n        pattern: /(^|\\s)[a-z]*\\{(?=\\s)/i,\n        lookbehind: true,\n        alias: 'operator'\n      }, {\n        // closing\n        pattern: /(\\s)\\}(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'operator'\n      }],\n      // do not highlight leading ] or trailing [ at the begin/end of the file as it's invalid syntax\n      'quotation-delimiter': [{\n        // opening\n        pattern: /(^|\\s)\\[(?=\\s)/,\n        lookbehind: true,\n        alias: 'operator'\n      }, {\n        // closing\n        pattern: /(\\s)\\](?=\\s|$)/,\n        lookbehind: true,\n        alias: 'operator'\n      }],\n      'normal-word': {\n        pattern: /(^|\\s)[^\"\\s]\\S*(?=\\s|$)/,\n        lookbehind: true\n      },\n      /*\n      basic first-class string \"a\"\n      with escaped double-quote \"a\\\"\"\n      escaped backslash \"\\\\\"\n      and general escapes since Factor has so many \"\\N\"\n      syntax that works in the reference implementation that isn't fully\n      supported because it's an implementation detail:\n      \"string 1\"\"string 2\" -> 2 strings (works anyway)\n      \"string\"5 -> string, 5\n      \"string\"[ ] -> string, quotation\n      { \"a\"} -> array<string>\n      the rest of those examples all properly recognise the string, but not\n      the other object (number, quotation, etc)\n      this is fine for a regex-only implementation.\n      */\n      string: {\n        pattern: /\"(?:\\\\\\S|[^\"\\\\])*\"/,\n        greedy: true,\n        inside: string_inside\n      }\n    };\n    var escape = function (str) {\n      return (str + '').replace(/([.?*+\\^$\\[\\]\\\\(){}|\\-])/g, '\\\\$1');\n    };\n    var arrToWordsRegExp = function (arr) {\n      return new RegExp('(^|\\\\s)(?:' + arr.map(escape).join('|') + ')(?=\\\\s|$)');\n    };\n    var builtins = {\n      'kernel-builtin': ['or', '2nipd', '4drop', 'tuck', 'wrapper', 'nip', 'wrapper?', 'callstack>array', 'die', 'dupd', 'callstack', 'callstack?', '3dup', 'hashcode', 'pick', '4nip', 'build', '>boolean', 'nipd', 'clone', '5nip', 'eq?', '?', '=', 'swapd', '2over', 'clear', '2dup', 'get-retainstack', 'not', 'tuple?', 'dup', '3nipd', 'call', '-rotd', 'object', 'drop', 'assert=', 'assert?', '-rot', 'execute', 'boa', 'get-callstack', 'curried?', '3drop', 'pickd', 'overd', 'over', 'roll', '3nip', 'swap', 'and', '2nip', 'rotd', 'throw', '(clone)', 'hashcode*', 'spin', 'reach', '4dup', 'equal?', 'get-datastack', 'assert', '2drop', '<wrapper>', 'boolean?', 'identity-hashcode', 'identity-tuple?', 'null', 'composed?', 'new', '5drop', 'rot', '-roll', 'xor', 'identity-tuple', 'boolean'],\n      'other-builtin-syntax': [\n      // syntax\n      '=======', 'recursive', 'flushable', '>>', '<<<<<<', 'M\\\\', 'B', 'PRIVATE>', '\\\\', '======', 'final', 'inline', 'delimiter', 'deprecated', '<PRIVATE', '>>>>>>', '<<<<<<<', 'parse-complex', 'malformed-complex', 'read-only', '>>>>>>>', 'call-next-method', '<<', 'foldable',\n      // literals\n      '$', '$[', '${'],\n      'sequences-builtin': ['member-eq?', 'mismatch', 'append', 'assert-sequence=', 'longer', 'repetition', 'clone-like', '3sequence', 'assert-sequence?', 'last-index-from', 'reversed', 'index-from', 'cut*', 'pad-tail', 'join-as', 'remove-eq!', 'concat-as', 'but-last', 'snip', 'nths', 'nth', 'sequence', 'longest', 'slice?', '<slice>', 'remove-nth', 'tail-slice', 'empty?', 'tail*', 'member?', 'virtual-sequence?', 'set-length', 'drop-prefix', 'iota', 'unclip', 'bounds-error?', 'unclip-last-slice', 'non-negative-integer-expected', 'non-negative-integer-expected?', 'midpoint@', 'longer?', '?set-nth', '?first', 'rest-slice', 'prepend-as', 'prepend', 'fourth', 'sift', 'subseq-start', 'new-sequence', '?last', 'like', 'first4', '1sequence', 'reverse', 'slice', 'virtual@', 'repetition?', 'set-last', 'index', '4sequence', 'max-length', 'set-second', 'immutable-sequence', 'first2', 'first3', 'supremum', 'unclip-slice', 'suffix!', 'insert-nth', 'tail', '3append', 'short', 'suffix', 'concat', 'flip', 'immutable?', 'reverse!', '2sequence', 'sum', 'delete-all', 'indices', 'snip-slice', '<iota>', 'check-slice', 'sequence?', 'head', 'append-as', 'halves', 'sequence=', 'collapse-slice', '?second', 'slice-error?', 'product', 'bounds-check?', 'bounds-check', 'immutable', 'virtual-exemplar', 'harvest', 'remove', 'pad-head', 'last', 'set-fourth', 'cartesian-product', 'remove-eq', 'shorten', 'shorter', 'reversed?', 'shorter?', 'shortest', 'head-slice', 'pop*', 'tail-slice*', 'but-last-slice', 'iota?', 'append!', 'cut-slice', 'new-resizable', 'head-slice*', 'sequence-hashcode', 'pop', 'set-nth', '?nth', 'second', 'join', 'immutable-sequence?', '<reversed>', '3append-as', 'virtual-sequence', 'subseq?', 'remove-nth!', 'length', 'last-index', 'lengthen', 'assert-sequence', 'copy', 'move', 'third', 'first', 'tail?', 'set-first', 'prefix', 'bounds-error', '<repetition>', 'exchange', 'surround', 'cut', 'min-length', 'set-third', 'push-all', 'head?', 'subseq-start-from', 'delete-slice', 'rest', 'sum-lengths', 'head*', 'infimum', 'remove!', 'glue', 'slice-error', 'subseq', 'push', 'replace-slice', 'subseq-as', 'unclip-last'],\n      'math-builtin': ['number=', 'next-power-of-2', '?1+', 'fp-special?', 'imaginary-part', 'float>bits', 'number?', 'fp-infinity?', 'bignum?', 'fp-snan?', 'denominator', 'gcd', '*', '+', 'fp-bitwise=', '-', 'u>=', '/', '>=', 'bitand', 'power-of-2?', 'log2-expects-positive', 'neg?', '<', 'log2', '>', 'integer?', 'number', 'bits>double', '2/', 'zero?', 'bits>float', 'float?', 'shift', 'ratio?', 'rect>', 'even?', 'ratio', 'fp-sign', 'bitnot', '>fixnum', 'complex?', '/i', 'integer>fixnum', '/f', 'sgn', '>bignum', 'next-float', 'u<', 'u>', 'mod', 'recip', 'rational', '>float', '2^', 'integer', 'fixnum?', 'neg', 'fixnum', 'sq', 'bignum', '>rect', 'bit?', 'fp-qnan?', 'simple-gcd', 'complex', '<fp-nan>', 'real', '>fraction', 'double>bits', 'bitor', 'rem', 'fp-nan-payload', 'real-part', 'log2-expects-positive?', 'prev-float', 'align', 'unordered?', 'float', 'fp-nan?', 'abs', 'bitxor', 'integer>fixnum-strict', 'u<=', 'odd?', '<=', '/mod', '>integer', 'real?', 'rational?', 'numerator'] // that's all for now\n    };\n    Object.keys(builtins).forEach(function (k) {\n      factor[k].pattern = arrToWordsRegExp(builtins[k]);\n    });\n    var combinators = [\n    // kernel\n    '2bi', 'while', '2tri', 'bi*', '4dip', 'both?', 'same?', 'tri@', 'curry', 'prepose', '3bi', '?if', 'tri*', '2keep', '3keep', 'curried', '2keepd', 'when', '2bi*', '2tri*', '4keep', 'bi@', 'keepdd', 'do', 'unless*', 'tri-curry', 'if*', 'loop', 'bi-curry*', 'when*', '2bi@', '2tri@', 'with', '2with', 'either?', 'bi', 'until', '3dip', '3curry', 'tri-curry*', 'tri-curry@', 'bi-curry', 'keepd', 'compose', '2dip', 'if', '3tri', 'unless', 'tuple', 'keep', '2curry', 'tri', 'most', 'while*', 'dip', 'composed', 'bi-curry@',\n    // sequences\n    'find-last-from', 'trim-head-slice', 'map-as', 'each-from', 'none?', 'trim-tail', 'partition', 'if-empty', 'accumulate*', 'reject!', 'find-from', 'accumulate-as', 'collector-for-as', 'reject', 'map', 'map-sum', 'accumulate!', '2each-from', 'follow', 'supremum-by', 'map!', 'unless-empty', 'collector', 'padding', 'reduce-index', 'replicate-as', 'infimum-by', 'trim-tail-slice', 'count', 'find-index', 'filter', 'accumulate*!', 'reject-as', 'map-integers', 'map-find', 'reduce', 'selector', 'interleave', '2map', 'filter-as', 'binary-reduce', 'map-index-as', 'find', 'produce', 'filter!', 'replicate', 'cartesian-map', 'cartesian-each', 'find-index-from', 'map-find-last', '3map-as', '3map', 'find-last', 'selector-as', '2map-as', '2map-reduce', 'accumulate', 'each', 'each-index', 'accumulate*-as', 'when-empty', 'all?', 'collector-as', 'push-either', 'new-like', 'collector-for', '2selector', 'push-if', '2all?', 'map-reduce', '3each', 'any?', 'trim-slice', '2reduce', 'change-nth', 'produce-as', '2each', 'trim', 'trim-head', 'cartesian-find', 'map-index',\n    // math\n    'if-zero', 'each-integer', 'unless-zero', '(find-integer)', 'when-zero', 'find-last-integer', '(all-integers?)', 'times', '(each-integer)', 'find-integer', 'all-integers?',\n    // math.combinators\n    'unless-negative', 'if-positive', 'when-positive', 'when-negative', 'unless-positive', 'if-negative',\n    // combinators\n    'case', '2cleave', 'cond>quot', 'case>quot', '3cleave', 'wrong-values', 'to-fixed-point', 'alist>quot', 'cond', 'cleave', 'call-effect', 'recursive-hashcode', 'spread', 'deep-spread>quot',\n    // combinators.short-circuit\n    '2||', '0||', 'n||', '0&&', '2&&', '3||', '1||', '1&&', 'n&&', '3&&',\n    // combinators.smart\n    'smart-unless*', 'keep-inputs', 'reduce-outputs', 'smart-when*', 'cleave>array', 'smart-with', 'smart-apply', 'smart-if', 'inputs/outputs', 'output>sequence-n', 'map-outputs', 'map-reduce-outputs', 'dropping', 'output>array', 'smart-map-reduce', 'smart-2map-reduce', 'output>array-n', 'nullary', 'input<sequence', 'append-outputs', 'drop-inputs', 'inputs', 'smart-2reduce', 'drop-outputs', 'smart-reduce', 'preserving', 'smart-when', 'outputs', 'append-outputs-as', 'smart-unless', 'smart-if*', 'sum-outputs', 'input<sequence-unsafe', 'output>sequence' // tafn\n    ];\n    factor.combinators.pattern = arrToWordsRegExp(combinators);\n    Prism.languages.factor = factor;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "factor", "displayName", "aliases", "Prism", "comment_inside", "function", "string_inside", "number", "comment", "pattern", "lookbehind", "inside", "greedy", "regexp", "alias", "variable", "keyword", "operator", "boolean", "string", "combinators", "escape", "str", "replace", "arrToWordsRegExp", "arr", "RegExp", "map", "join", "builtins", "Object", "keys", "for<PERSON>ach", "k", "languages"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/factor.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = factor\nfactor.displayName = 'factor'\nfactor.aliases = []\nfunction factor(Prism) {\n  ;(function (Prism) {\n    var comment_inside = {\n      function:\n        /\\b(?:BUGS?|FIX(?:MES?)?|NOTES?|TODOS?|XX+|HACKS?|WARN(?:ING)?|\\?{2,}|!{2,})\\b/\n    }\n    var string_inside = {\n      number: /\\\\[^\\s']|%\\w/\n    }\n    var factor = {\n      comment: [\n        {\n          // ! single-line exclamation point comments with whitespace after/around the !\n          pattern: /(^|\\s)(?:! .*|!$)/,\n          lookbehind: true,\n          inside: comment_inside\n        },\n        /* from basis/multiline: */\n        {\n          // /* comment */, /* comment*/\n          pattern: /(^|\\s)\\/\\*\\s[\\s\\S]*?\\*\\/(?=\\s|$)/,\n          lookbehind: true,\n          greedy: true,\n          inside: comment_inside\n        },\n        {\n          // ![[ comment ]] , ![===[ comment]===]\n          pattern: /(^|\\s)!\\[(={0,6})\\[\\s[\\s\\S]*?\\]\\2\\](?=\\s|$)/,\n          lookbehind: true,\n          greedy: true,\n          inside: comment_inside\n        }\n      ],\n      number: [\n        {\n          // basic base 10 integers 9, -9\n          pattern: /(^|\\s)[+-]?\\d+(?=\\s|$)/,\n          lookbehind: true\n        },\n        {\n          // base prefix integers 0b010 0o70 0xad 0d10 0XAD -0xa9\n          pattern: /(^|\\s)[+-]?0(?:b[01]+|o[0-7]+|d\\d+|x[\\dA-F]+)(?=\\s|$)/i,\n          lookbehind: true\n        },\n        {\n          // fractional ratios 1/5 -1/5 and the literal float approximations 1/5. -1/5.\n          pattern: /(^|\\s)[+-]?\\d+\\/\\d+\\.?(?=\\s|$)/,\n          lookbehind: true\n        },\n        {\n          // positive mixed numbers 23+1/5 +23+1/5\n          pattern: /(^|\\s)\\+?\\d+\\+\\d+\\/\\d+(?=\\s|$)/,\n          lookbehind: true\n        },\n        {\n          // negative mixed numbers -23-1/5\n          pattern: /(^|\\s)-\\d+-\\d+\\/\\d+(?=\\s|$)/,\n          lookbehind: true\n        },\n        {\n          // basic decimal floats -0.01 0. .0 .1 -.1 -1. -12.13 +12.13\n          // and scientific notation with base 10 exponents 3e4 3e-4 .3e-4\n          pattern:\n            /(^|\\s)[+-]?(?:\\d*\\.\\d+|\\d+\\.\\d*|\\d+)(?:e[+-]?\\d+)?(?=\\s|$)/i,\n          lookbehind: true\n        },\n        {\n          // NAN literal syntax NAN: 80000deadbeef, NAN: a\n          pattern: /(^|\\s)NAN:\\s+[\\da-fA-F]+(?=\\s|$)/,\n          lookbehind: true\n        },\n        {\n          /*\nbase prefix floats 0x1.0p3 (8.0) 0b1.010p2 (5.0) 0x1.p1 0b1.11111111p11111...\n\"The normalized hex form ±0x1.MMMMMMMMMMMMM[pP]±EEEE allows any floating-point number to be specified precisely.\nThe values of MMMMMMMMMMMMM and EEEE map directly to the mantissa and exponent fields of the binary IEEE 754 representation.\"\n<https://docs.factorcode.org/content/article-syntax-floats.html>\n*/\n          pattern:\n            /(^|\\s)[+-]?0(?:b1\\.[01]*|o1\\.[0-7]*|d1\\.\\d*|x1\\.[\\dA-F]*)p\\d+(?=\\s|$)/i,\n          lookbehind: true\n        }\n      ],\n      // R/ regexp?\\/\\\\/\n      regexp: {\n        pattern:\n          /(^|\\s)R\\/\\s(?:\\\\\\S|[^\\\\/])*\\/(?:[idmsr]*|[idmsr]+-[idmsr]+)(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'number',\n        inside: {\n          variable: /\\\\\\S/,\n          keyword: /[+?*\\[\\]^$(){}.|]/,\n          operator: {\n            pattern: /(\\/)[idmsr]+(?:-[idmsr]+)?/,\n            lookbehind: true\n          }\n        }\n      },\n      boolean: {\n        pattern: /(^|\\s)[tf](?=\\s|$)/,\n        lookbehind: true\n      },\n      // SBUF\" asd\", URL\" ://...\", P\" /etc/\"\n      'custom-string': {\n        pattern: /(^|\\s)[A-Z0-9\\-]+\"\\s(?:\\\\\\S|[^\"\\\\])*\"/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          number: /\\\\\\S|%\\w|\\//\n        }\n      },\n      'multiline-string': [\n        {\n          // STRING: name \\n content \\n ; -> CONSTANT: name \"content\" (symbol)\n          pattern: /(^|\\s)STRING:\\s+\\S+(?:\\n|\\r\\n).*(?:\\n|\\r\\n)\\s*;(?=\\s|$)/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'string',\n          inside: {\n            number: string_inside.number,\n            // trailing semicolon on its own line\n            'semicolon-or-setlocal': {\n              pattern: /([\\r\\n][ \\t]*);(?=\\s|$)/,\n              lookbehind: true,\n              alias: 'function'\n            }\n          }\n        },\n        {\n          // HEREDOC: marker \\n content \\n marker ; -> \"content\" (immediate)\n          pattern: /(^|\\s)HEREDOC:\\s+\\S+(?:\\n|\\r\\n).*(?:\\n|\\r\\n)\\s*\\S+(?=\\s|$)/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'string',\n          inside: string_inside\n        },\n        {\n          // [[ string ]], [==[ string]==]\n          pattern: /(^|\\s)\\[(={0,6})\\[\\s[\\s\\S]*?\\]\\2\\](?=\\s|$)/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'string',\n          inside: string_inside\n        }\n      ],\n      'special-using': {\n        pattern: /(^|\\s)USING:(?:\\s\\S+)*(?=\\s+;(?:\\s|$))/,\n        lookbehind: true,\n        alias: 'function',\n        inside: {\n          // this is essentially a regex for vocab names, which i don't want to specify\n          // but the USING: gets picked up as a vocab name\n          string: {\n            pattern: /(\\s)[^:\\s]+/,\n            lookbehind: true\n          }\n        }\n      },\n      /* this description of stack effect literal syntax is not complete and not as specific as theoretically possible\ntrying to do better is more work and regex-computation-time than it's worth though.\n- we'd like to have the \"delimiter\" parts of the stack effect [ (, --, and ) ] be a different (less-important or comment-like) colour to the stack effect contents\n- we'd like if nested stack effects were treated as such rather than just appearing flat (with `inside`)\n- we'd like if the following variable name conventions were recognised specifically:\nspecial row variables = ..a b..\ntype and stack effect annotations end with a colon = ( quot: ( a: ( -- ) -- b ) -- x ), ( x: number -- )\nword throws unconditional error = *\nany other word-like variable name = a ? q' etc\nhttps://docs.factorcode.org/content/article-effects.html\nthese are pretty complicated to highlight properly without a real parser, and therefore out of scope\nthe old pattern, which may be later useful, was: (^|\\s)(?:call|execute|eval)?\\((?:\\s+[^\"\\r\\n\\t ]\\S*)*?\\s+--(?:\\s+[^\"\\n\\t ]\\S*)*?\\s+\\)(?=\\s|$)\n*/\n      // current solution is not great\n      'stack-effect-delimiter': [\n        {\n          // opening parenthesis\n          pattern: /(^|\\s)(?:call|eval|execute)?\\((?=\\s)/,\n          lookbehind: true,\n          alias: 'operator'\n        },\n        {\n          // middle --\n          pattern: /(\\s)--(?=\\s)/,\n          lookbehind: true,\n          alias: 'operator'\n        },\n        {\n          // closing parenthesis\n          pattern: /(\\s)\\)(?=\\s|$)/,\n          lookbehind: true,\n          alias: 'operator'\n        }\n      ],\n      combinators: {\n        pattern: null,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'kernel-builtin': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      'sequences-builtin': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      'math-builtin': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      'constructor-word': {\n        // <array> but not <=>\n        pattern: /(^|\\s)<(?!=+>|-+>)\\S+>(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'other-builtin-syntax': {\n        pattern: null,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      /*\nfull list of supported word naming conventions: (the convention appears outside of the [brackets])\nset-[x]\nchange-[x]\nwith-[x]\nnew-[x]\n>[string]\n[base]>\n[string]>[number]\n+[symbol]+\n[boolean-word]?\n?[of]\n[slot-reader]>>\n>>[slot-setter]\n[slot-writer]<<\n([implementation-detail])\n[mutater]!\n[variant]*\n[prettyprint].\n$[help-markup]\n<constructors>, SYNTAX:, etc are supported by their own patterns.\n`with` and `new` from `kernel` are their own builtins.\nsee <https://docs.factorcode.org/content/article-conventions.html>\n*/\n      'conventionally-named-word': {\n        pattern:\n          /(^|\\s)(?!\")(?:(?:change|new|set|with)-\\S+|\\$\\S+|>[^>\\s]+|[^:>\\s]+>|[^>\\s]+>[^>\\s]+|\\+[^+\\s]+\\+|[^?\\s]+\\?|\\?[^?\\s]+|[^>\\s]+>>|>>[^>\\s]+|[^<\\s]+<<|\\([^()\\s]+\\)|[^!\\s]+!|[^*\\s]\\S*\\*|[^.\\s]\\S*\\.)(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'colon-syntax': {\n        pattern: /(^|\\s)(?:[A-Z0-9\\-]+#?)?:{1,2}\\s+(?:;\\S+|(?!;)\\S+)(?=\\s|$)/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'function'\n      },\n      'semicolon-or-setlocal': {\n        pattern: /(\\s)(?:;|:>)(?=\\s|$)/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      // do not highlight leading } or trailing X{ at the begin/end of the file as it's invalid syntax\n      'curly-brace-literal-delimiter': [\n        {\n          // opening\n          pattern: /(^|\\s)[a-z]*\\{(?=\\s)/i,\n          lookbehind: true,\n          alias: 'operator'\n        },\n        {\n          // closing\n          pattern: /(\\s)\\}(?=\\s|$)/,\n          lookbehind: true,\n          alias: 'operator'\n        }\n      ],\n      // do not highlight leading ] or trailing [ at the begin/end of the file as it's invalid syntax\n      'quotation-delimiter': [\n        {\n          // opening\n          pattern: /(^|\\s)\\[(?=\\s)/,\n          lookbehind: true,\n          alias: 'operator'\n        },\n        {\n          // closing\n          pattern: /(\\s)\\](?=\\s|$)/,\n          lookbehind: true,\n          alias: 'operator'\n        }\n      ],\n      'normal-word': {\n        pattern: /(^|\\s)[^\"\\s]\\S*(?=\\s|$)/,\n        lookbehind: true\n      },\n      /*\nbasic first-class string \"a\"\nwith escaped double-quote \"a\\\"\"\nescaped backslash \"\\\\\"\nand general escapes since Factor has so many \"\\N\"\nsyntax that works in the reference implementation that isn't fully\nsupported because it's an implementation detail:\n\"string 1\"\"string 2\" -> 2 strings (works anyway)\n\"string\"5 -> string, 5\n\"string\"[ ] -> string, quotation\n{ \"a\"} -> array<string>\nthe rest of those examples all properly recognise the string, but not\nthe other object (number, quotation, etc)\nthis is fine for a regex-only implementation.\n*/\n      string: {\n        pattern: /\"(?:\\\\\\S|[^\"\\\\])*\"/,\n        greedy: true,\n        inside: string_inside\n      }\n    }\n    var escape = function (str) {\n      return (str + '').replace(/([.?*+\\^$\\[\\]\\\\(){}|\\-])/g, '\\\\$1')\n    }\n    var arrToWordsRegExp = function (arr) {\n      return new RegExp('(^|\\\\s)(?:' + arr.map(escape).join('|') + ')(?=\\\\s|$)')\n    }\n    var builtins = {\n      'kernel-builtin': [\n        'or',\n        '2nipd',\n        '4drop',\n        'tuck',\n        'wrapper',\n        'nip',\n        'wrapper?',\n        'callstack>array',\n        'die',\n        'dupd',\n        'callstack',\n        'callstack?',\n        '3dup',\n        'hashcode',\n        'pick',\n        '4nip',\n        'build',\n        '>boolean',\n        'nipd',\n        'clone',\n        '5nip',\n        'eq?',\n        '?',\n        '=',\n        'swapd',\n        '2over',\n        'clear',\n        '2dup',\n        'get-retainstack',\n        'not',\n        'tuple?',\n        'dup',\n        '3nipd',\n        'call',\n        '-rotd',\n        'object',\n        'drop',\n        'assert=',\n        'assert?',\n        '-rot',\n        'execute',\n        'boa',\n        'get-callstack',\n        'curried?',\n        '3drop',\n        'pickd',\n        'overd',\n        'over',\n        'roll',\n        '3nip',\n        'swap',\n        'and',\n        '2nip',\n        'rotd',\n        'throw',\n        '(clone)',\n        'hashcode*',\n        'spin',\n        'reach',\n        '4dup',\n        'equal?',\n        'get-datastack',\n        'assert',\n        '2drop',\n        '<wrapper>',\n        'boolean?',\n        'identity-hashcode',\n        'identity-tuple?',\n        'null',\n        'composed?',\n        'new',\n        '5drop',\n        'rot',\n        '-roll',\n        'xor',\n        'identity-tuple',\n        'boolean'\n      ],\n      'other-builtin-syntax': [\n        // syntax\n        '=======',\n        'recursive',\n        'flushable',\n        '>>',\n        '<<<<<<',\n        'M\\\\',\n        'B',\n        'PRIVATE>',\n        '\\\\',\n        '======',\n        'final',\n        'inline',\n        'delimiter',\n        'deprecated',\n        '<PRIVATE',\n        '>>>>>>',\n        '<<<<<<<',\n        'parse-complex',\n        'malformed-complex',\n        'read-only',\n        '>>>>>>>',\n        'call-next-method',\n        '<<',\n        'foldable', // literals\n        '$',\n        '$[',\n        '${'\n      ],\n      'sequences-builtin': [\n        'member-eq?',\n        'mismatch',\n        'append',\n        'assert-sequence=',\n        'longer',\n        'repetition',\n        'clone-like',\n        '3sequence',\n        'assert-sequence?',\n        'last-index-from',\n        'reversed',\n        'index-from',\n        'cut*',\n        'pad-tail',\n        'join-as',\n        'remove-eq!',\n        'concat-as',\n        'but-last',\n        'snip',\n        'nths',\n        'nth',\n        'sequence',\n        'longest',\n        'slice?',\n        '<slice>',\n        'remove-nth',\n        'tail-slice',\n        'empty?',\n        'tail*',\n        'member?',\n        'virtual-sequence?',\n        'set-length',\n        'drop-prefix',\n        'iota',\n        'unclip',\n        'bounds-error?',\n        'unclip-last-slice',\n        'non-negative-integer-expected',\n        'non-negative-integer-expected?',\n        'midpoint@',\n        'longer?',\n        '?set-nth',\n        '?first',\n        'rest-slice',\n        'prepend-as',\n        'prepend',\n        'fourth',\n        'sift',\n        'subseq-start',\n        'new-sequence',\n        '?last',\n        'like',\n        'first4',\n        '1sequence',\n        'reverse',\n        'slice',\n        'virtual@',\n        'repetition?',\n        'set-last',\n        'index',\n        '4sequence',\n        'max-length',\n        'set-second',\n        'immutable-sequence',\n        'first2',\n        'first3',\n        'supremum',\n        'unclip-slice',\n        'suffix!',\n        'insert-nth',\n        'tail',\n        '3append',\n        'short',\n        'suffix',\n        'concat',\n        'flip',\n        'immutable?',\n        'reverse!',\n        '2sequence',\n        'sum',\n        'delete-all',\n        'indices',\n        'snip-slice',\n        '<iota>',\n        'check-slice',\n        'sequence?',\n        'head',\n        'append-as',\n        'halves',\n        'sequence=',\n        'collapse-slice',\n        '?second',\n        'slice-error?',\n        'product',\n        'bounds-check?',\n        'bounds-check',\n        'immutable',\n        'virtual-exemplar',\n        'harvest',\n        'remove',\n        'pad-head',\n        'last',\n        'set-fourth',\n        'cartesian-product',\n        'remove-eq',\n        'shorten',\n        'shorter',\n        'reversed?',\n        'shorter?',\n        'shortest',\n        'head-slice',\n        'pop*',\n        'tail-slice*',\n        'but-last-slice',\n        'iota?',\n        'append!',\n        'cut-slice',\n        'new-resizable',\n        'head-slice*',\n        'sequence-hashcode',\n        'pop',\n        'set-nth',\n        '?nth',\n        'second',\n        'join',\n        'immutable-sequence?',\n        '<reversed>',\n        '3append-as',\n        'virtual-sequence',\n        'subseq?',\n        'remove-nth!',\n        'length',\n        'last-index',\n        'lengthen',\n        'assert-sequence',\n        'copy',\n        'move',\n        'third',\n        'first',\n        'tail?',\n        'set-first',\n        'prefix',\n        'bounds-error',\n        '<repetition>',\n        'exchange',\n        'surround',\n        'cut',\n        'min-length',\n        'set-third',\n        'push-all',\n        'head?',\n        'subseq-start-from',\n        'delete-slice',\n        'rest',\n        'sum-lengths',\n        'head*',\n        'infimum',\n        'remove!',\n        'glue',\n        'slice-error',\n        'subseq',\n        'push',\n        'replace-slice',\n        'subseq-as',\n        'unclip-last'\n      ],\n      'math-builtin': [\n        'number=',\n        'next-power-of-2',\n        '?1+',\n        'fp-special?',\n        'imaginary-part',\n        'float>bits',\n        'number?',\n        'fp-infinity?',\n        'bignum?',\n        'fp-snan?',\n        'denominator',\n        'gcd',\n        '*',\n        '+',\n        'fp-bitwise=',\n        '-',\n        'u>=',\n        '/',\n        '>=',\n        'bitand',\n        'power-of-2?',\n        'log2-expects-positive',\n        'neg?',\n        '<',\n        'log2',\n        '>',\n        'integer?',\n        'number',\n        'bits>double',\n        '2/',\n        'zero?',\n        'bits>float',\n        'float?',\n        'shift',\n        'ratio?',\n        'rect>',\n        'even?',\n        'ratio',\n        'fp-sign',\n        'bitnot',\n        '>fixnum',\n        'complex?',\n        '/i',\n        'integer>fixnum',\n        '/f',\n        'sgn',\n        '>bignum',\n        'next-float',\n        'u<',\n        'u>',\n        'mod',\n        'recip',\n        'rational',\n        '>float',\n        '2^',\n        'integer',\n        'fixnum?',\n        'neg',\n        'fixnum',\n        'sq',\n        'bignum',\n        '>rect',\n        'bit?',\n        'fp-qnan?',\n        'simple-gcd',\n        'complex',\n        '<fp-nan>',\n        'real',\n        '>fraction',\n        'double>bits',\n        'bitor',\n        'rem',\n        'fp-nan-payload',\n        'real-part',\n        'log2-expects-positive?',\n        'prev-float',\n        'align',\n        'unordered?',\n        'float',\n        'fp-nan?',\n        'abs',\n        'bitxor',\n        'integer>fixnum-strict',\n        'u<=',\n        'odd?',\n        '<=',\n        '/mod',\n        '>integer',\n        'real?',\n        'rational?',\n        'numerator'\n      ] // that's all for now\n    }\n    Object.keys(builtins).forEach(function (k) {\n      factor[k].pattern = arrToWordsRegExp(builtins[k])\n    })\n    var combinators = [\n      // kernel\n      '2bi',\n      'while',\n      '2tri',\n      'bi*',\n      '4dip',\n      'both?',\n      'same?',\n      'tri@',\n      'curry',\n      'prepose',\n      '3bi',\n      '?if',\n      'tri*',\n      '2keep',\n      '3keep',\n      'curried',\n      '2keepd',\n      'when',\n      '2bi*',\n      '2tri*',\n      '4keep',\n      'bi@',\n      'keepdd',\n      'do',\n      'unless*',\n      'tri-curry',\n      'if*',\n      'loop',\n      'bi-curry*',\n      'when*',\n      '2bi@',\n      '2tri@',\n      'with',\n      '2with',\n      'either?',\n      'bi',\n      'until',\n      '3dip',\n      '3curry',\n      'tri-curry*',\n      'tri-curry@',\n      'bi-curry',\n      'keepd',\n      'compose',\n      '2dip',\n      'if',\n      '3tri',\n      'unless',\n      'tuple',\n      'keep',\n      '2curry',\n      'tri',\n      'most',\n      'while*',\n      'dip',\n      'composed',\n      'bi-curry@', // sequences\n      'find-last-from',\n      'trim-head-slice',\n      'map-as',\n      'each-from',\n      'none?',\n      'trim-tail',\n      'partition',\n      'if-empty',\n      'accumulate*',\n      'reject!',\n      'find-from',\n      'accumulate-as',\n      'collector-for-as',\n      'reject',\n      'map',\n      'map-sum',\n      'accumulate!',\n      '2each-from',\n      'follow',\n      'supremum-by',\n      'map!',\n      'unless-empty',\n      'collector',\n      'padding',\n      'reduce-index',\n      'replicate-as',\n      'infimum-by',\n      'trim-tail-slice',\n      'count',\n      'find-index',\n      'filter',\n      'accumulate*!',\n      'reject-as',\n      'map-integers',\n      'map-find',\n      'reduce',\n      'selector',\n      'interleave',\n      '2map',\n      'filter-as',\n      'binary-reduce',\n      'map-index-as',\n      'find',\n      'produce',\n      'filter!',\n      'replicate',\n      'cartesian-map',\n      'cartesian-each',\n      'find-index-from',\n      'map-find-last',\n      '3map-as',\n      '3map',\n      'find-last',\n      'selector-as',\n      '2map-as',\n      '2map-reduce',\n      'accumulate',\n      'each',\n      'each-index',\n      'accumulate*-as',\n      'when-empty',\n      'all?',\n      'collector-as',\n      'push-either',\n      'new-like',\n      'collector-for',\n      '2selector',\n      'push-if',\n      '2all?',\n      'map-reduce',\n      '3each',\n      'any?',\n      'trim-slice',\n      '2reduce',\n      'change-nth',\n      'produce-as',\n      '2each',\n      'trim',\n      'trim-head',\n      'cartesian-find',\n      'map-index', // math\n      'if-zero',\n      'each-integer',\n      'unless-zero',\n      '(find-integer)',\n      'when-zero',\n      'find-last-integer',\n      '(all-integers?)',\n      'times',\n      '(each-integer)',\n      'find-integer',\n      'all-integers?', // math.combinators\n      'unless-negative',\n      'if-positive',\n      'when-positive',\n      'when-negative',\n      'unless-positive',\n      'if-negative', // combinators\n      'case',\n      '2cleave',\n      'cond>quot',\n      'case>quot',\n      '3cleave',\n      'wrong-values',\n      'to-fixed-point',\n      'alist>quot',\n      'cond',\n      'cleave',\n      'call-effect',\n      'recursive-hashcode',\n      'spread',\n      'deep-spread>quot', // combinators.short-circuit\n      '2||',\n      '0||',\n      'n||',\n      '0&&',\n      '2&&',\n      '3||',\n      '1||',\n      '1&&',\n      'n&&',\n      '3&&', // combinators.smart\n      'smart-unless*',\n      'keep-inputs',\n      'reduce-outputs',\n      'smart-when*',\n      'cleave>array',\n      'smart-with',\n      'smart-apply',\n      'smart-if',\n      'inputs/outputs',\n      'output>sequence-n',\n      'map-outputs',\n      'map-reduce-outputs',\n      'dropping',\n      'output>array',\n      'smart-map-reduce',\n      'smart-2map-reduce',\n      'output>array-n',\n      'nullary',\n      'input<sequence',\n      'append-outputs',\n      'drop-inputs',\n      'inputs',\n      'smart-2reduce',\n      'drop-outputs',\n      'smart-reduce',\n      'preserving',\n      'smart-when',\n      'outputs',\n      'append-outputs-as',\n      'smart-unless',\n      'smart-if*',\n      'sum-outputs',\n      'input<sequence-unsafe',\n      'output>sequence' // tafn\n    ]\n    factor.combinators.pattern = arrToWordsRegExp(combinators)\n    Prism.languages.factor = factor\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,cAAc,GAAG;MACnBC,QAAQ,EACN;IACJ,CAAC;IACD,IAAIC,aAAa,GAAG;MAClBC,MAAM,EAAE;IACV,CAAC;IACD,IAAIP,MAAM,GAAG;MACXQ,OAAO,EAAE,CACP;QACE;QACAC,OAAO,EAAE,mBAAmB;QAC5BC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEP;MACV,CAAC,EACD;MACA;QACE;QACAK,OAAO,EAAE,kCAAkC;QAC3CC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZD,MAAM,EAAEP;MACV,CAAC,EACD;QACE;QACAK,OAAO,EAAE,6CAA6C;QACtDC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZD,MAAM,EAAEP;MACV,CAAC,CACF;MACDG,MAAM,EAAE,CACN;QACE;QACAE,OAAO,EAAE,wBAAwB;QACjCC,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACAD,OAAO,EAAE,wDAAwD;QACjEC,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACAD,OAAO,EAAE,gCAAgC;QACzCC,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACAD,OAAO,EAAE,gCAAgC;QACzCC,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACAD,OAAO,EAAE,6BAA6B;QACtCC,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACA;QACAD,OAAO,EACL,6DAA6D;QAC/DC,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACAD,OAAO,EAAE,kCAAkC;QAC3CC,UAAU,EAAE;MACd,CAAC,EACD;QACE;AACV;AACA;AACA;AACA;AACA;QACUD,OAAO,EACL,wEAAwE;QAC1EC,UAAU,EAAE;MACd,CAAC,CACF;MACD;MACAG,MAAM,EAAE;QACNJ,OAAO,EACL,qEAAqE;QACvEC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE,QAAQ;QACfH,MAAM,EAAE;UACNI,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,mBAAmB;UAC5BC,QAAQ,EAAE;YACRR,OAAO,EAAE,4BAA4B;YACrCC,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACDQ,OAAO,EAAE;QACPT,OAAO,EAAE,oBAAoB;QAC7BC,UAAU,EAAE;MACd,CAAC;MACD;MACA,eAAe,EAAE;QACfD,OAAO,EAAE,uCAAuC;QAChDC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE,QAAQ;QACfH,MAAM,EAAE;UACNJ,MAAM,EAAE;QACV;MACF,CAAC;MACD,kBAAkB,EAAE,CAClB;QACE;QACAE,OAAO,EAAE,yDAAyD;QAClEC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE,QAAQ;QACfH,MAAM,EAAE;UACNJ,MAAM,EAAED,aAAa,CAACC,MAAM;UAC5B;UACA,uBAAuB,EAAE;YACvBE,OAAO,EAAE,yBAAyB;YAClCC,UAAU,EAAE,IAAI;YAChBI,KAAK,EAAE;UACT;QACF;MACF,CAAC,EACD;QACE;QACAL,OAAO,EAAE,4DAA4D;QACrEC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE,QAAQ;QACfH,MAAM,EAAEL;MACV,CAAC,EACD;QACE;QACAG,OAAO,EAAE,4CAA4C;QACrDC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE,QAAQ;QACfH,MAAM,EAAEL;MACV,CAAC,CACF;MACD,eAAe,EAAE;QACfG,OAAO,EAAE,wCAAwC;QACjDC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE,UAAU;QACjBH,MAAM,EAAE;UACN;UACA;UACAQ,MAAM,EAAE;YACNV,OAAO,EAAE,aAAa;YACtBC,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM;MACA,wBAAwB,EAAE,CACxB;QACE;QACAD,OAAO,EAAE,sCAAsC;QAC/CC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACAL,OAAO,EAAE,cAAc;QACvBC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACAL,OAAO,EAAE,gBAAgB;QACzBC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,CACF;MACDM,WAAW,EAAE;QACXX,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD,gBAAgB,EAAE;QAChBL,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD,mBAAmB,EAAE;QACnBL,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD,cAAc,EAAE;QACdL,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD,kBAAkB,EAAE;QAClB;QACAL,OAAO,EAAE,gCAAgC;QACzCC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD,sBAAsB,EAAE;QACtBL,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM,2BAA2B,EAAE;QAC3BL,OAAO,EACL,yMAAyM;QAC3MC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD,cAAc,EAAE;QACdL,OAAO,EAAE,4DAA4D;QACrEC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE;MACT,CAAC;MACD,uBAAuB,EAAE;QACvBL,OAAO,EAAE,sBAAsB;QAC/BC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD;MACA,+BAA+B,EAAE,CAC/B;QACE;QACAL,OAAO,EAAE,uBAAuB;QAChCC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACAL,OAAO,EAAE,gBAAgB;QACzBC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,CACF;MACD;MACA,qBAAqB,EAAE,CACrB;QACE;QACAL,OAAO,EAAE,gBAAgB;QACzBC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACAL,OAAO,EAAE,gBAAgB;QACzBC,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,CACF;MACD,aAAa,EAAE;QACbL,OAAO,EAAE,yBAAyB;QAClCC,UAAU,EAAE;MACd,CAAC;MACD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACMS,MAAM,EAAE;QACNV,OAAO,EAAE,oBAAoB;QAC7BG,MAAM,EAAE,IAAI;QACZD,MAAM,EAAEL;MACV;IACF,CAAC;IACD,IAAIe,MAAM,GAAG,SAAAA,CAAUC,GAAG,EAAE;MAC1B,OAAO,CAACA,GAAG,GAAG,EAAE,EAAEC,OAAO,CAAC,2BAA2B,EAAE,MAAM,CAAC;IAChE,CAAC;IACD,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACpC,OAAO,IAAIC,MAAM,CAAC,YAAY,GAAGD,GAAG,CAACE,GAAG,CAACN,MAAM,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;IAC5E,CAAC;IACD,IAAIC,QAAQ,GAAG;MACb,gBAAgB,EAAE,CAChB,IAAI,EACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,KAAK,EACL,UAAU,EACV,iBAAiB,EACjB,KAAK,EACL,MAAM,EACN,WAAW,EACX,YAAY,EACZ,MAAM,EACN,UAAU,EACV,MAAM,EACN,MAAM,EACN,OAAO,EACP,UAAU,EACV,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,EACL,GAAG,EACH,GAAG,EACH,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,iBAAiB,EACjB,KAAK,EACL,QAAQ,EACR,KAAK,EACL,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,SAAS,EACT,KAAK,EACL,eAAe,EACf,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,SAAS,EACT,WAAW,EACX,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,OAAO,EACP,WAAW,EACX,UAAU,EACV,mBAAmB,EACnB,iBAAiB,EACjB,MAAM,EACN,WAAW,EACX,KAAK,EACL,OAAO,EACP,KAAK,EACL,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,SAAS,CACV;MACD,sBAAsB,EAAE;MACtB;MACA,SAAS,EACT,WAAW,EACX,WAAW,EACX,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,GAAG,EACH,UAAU,EACV,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,SAAS,EACT,eAAe,EACf,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,kBAAkB,EAClB,IAAI,EACJ,UAAU;MAAE;MACZ,GAAG,EACH,IAAI,EACJ,IAAI,CACL;MACD,mBAAmB,EAAE,CACnB,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,kBAAkB,EAClB,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,kBAAkB,EAClB,iBAAiB,EACjB,UAAU,EACV,YAAY,EACZ,MAAM,EACN,UAAU,EACV,SAAS,EACT,YAAY,EACZ,WAAW,EACX,UAAU,EACV,MAAM,EACN,MAAM,EACN,KAAK,EACL,UAAU,EACV,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,SAAS,EACT,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,MAAM,EACN,QAAQ,EACR,eAAe,EACf,mBAAmB,EACnB,+BAA+B,EAC/B,gCAAgC,EAChC,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,MAAM,EACN,cAAc,EACd,cAAc,EACd,OAAO,EACP,MAAM,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,OAAO,EACP,UAAU,EACV,aAAa,EACb,UAAU,EACV,OAAO,EACP,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,oBAAoB,EACpB,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,cAAc,EACd,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,UAAU,EACV,WAAW,EACX,KAAK,EACL,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,WAAW,EACX,MAAM,EACN,WAAW,EACX,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,SAAS,EACT,cAAc,EACd,SAAS,EACT,eAAe,EACf,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,SAAS,EACT,QAAQ,EACR,UAAU,EACV,MAAM,EACN,YAAY,EACZ,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,SAAS,EACT,WAAW,EACX,UAAU,EACV,UAAU,EACV,YAAY,EACZ,MAAM,EACN,aAAa,EACb,gBAAgB,EAChB,OAAO,EACP,SAAS,EACT,WAAW,EACX,eAAe,EACf,aAAa,EACb,mBAAmB,EACnB,KAAK,EACL,SAAS,EACT,MAAM,EACN,QAAQ,EACR,MAAM,EACN,qBAAqB,EACrB,YAAY,EACZ,YAAY,EACZ,kBAAkB,EAClB,SAAS,EACT,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,iBAAiB,EACjB,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,WAAW,EACX,QAAQ,EACR,cAAc,EACd,cAAc,EACd,UAAU,EACV,UAAU,EACV,KAAK,EACL,YAAY,EACZ,WAAW,EACX,UAAU,EACV,OAAO,EACP,mBAAmB,EACnB,cAAc,EACd,MAAM,EACN,aAAa,EACb,OAAO,EACP,SAAS,EACT,SAAS,EACT,MAAM,EACN,aAAa,EACb,QAAQ,EACR,MAAM,EACN,eAAe,EACf,WAAW,EACX,aAAa,CACd;MACD,cAAc,EAAE,CACd,SAAS,EACT,iBAAiB,EACjB,KAAK,EACL,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,SAAS,EACT,cAAc,EACd,SAAS,EACT,UAAU,EACV,aAAa,EACb,KAAK,EACL,GAAG,EACH,GAAG,EACH,aAAa,EACb,GAAG,EACH,KAAK,EACL,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,aAAa,EACb,uBAAuB,EACvB,MAAM,EACN,GAAG,EACH,MAAM,EACN,GAAG,EACH,UAAU,EACV,QAAQ,EACR,aAAa,EACb,IAAI,EACJ,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,IAAI,EACJ,gBAAgB,EAChB,IAAI,EACJ,KAAK,EACL,SAAS,EACT,YAAY,EACZ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,EACP,UAAU,EACV,QAAQ,EACR,IAAI,EACJ,SAAS,EACT,SAAS,EACT,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,MAAM,EACN,UAAU,EACV,YAAY,EACZ,SAAS,EACT,UAAU,EACV,MAAM,EACN,WAAW,EACX,aAAa,EACb,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,WAAW,EACX,wBAAwB,EACxB,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,OAAO,EACP,SAAS,EACT,KAAK,EACL,QAAQ,EACR,uBAAuB,EACvB,KAAK,EACL,MAAM,EACN,IAAI,EACJ,MAAM,EACN,UAAU,EACV,OAAO,EACP,WAAW,EACX,WAAW,CACZ,CAAC;IACJ,CAAC;IACDC,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAE;MACzCjC,MAAM,CAACiC,CAAC,CAAC,CAACxB,OAAO,GAAGe,gBAAgB,CAACK,QAAQ,CAACI,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;IACF,IAAIb,WAAW,GAAG;IAChB;IACA,KAAK,EACL,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,SAAS,EACT,WAAW,EACX,KAAK,EACL,MAAM,EACN,WAAW,EACX,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,IAAI,EACJ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,OAAO,EACP,SAAS,EACT,MAAM,EACN,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,OAAO,EACP,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,UAAU,EACV,WAAW;IAAE;IACb,gBAAgB,EAChB,iBAAiB,EACjB,QAAQ,EACR,WAAW,EACX,OAAO,EACP,WAAW,EACX,WAAW,EACX,UAAU,EACV,aAAa,EACb,SAAS,EACT,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,QAAQ,EACR,KAAK,EACL,SAAS,EACT,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,MAAM,EACN,cAAc,EACd,WAAW,EACX,SAAS,EACT,cAAc,EACd,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,WAAW,EACX,cAAc,EACd,UAAU,EACV,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,MAAM,EACN,WAAW,EACX,eAAe,EACf,cAAc,EACd,MAAM,EACN,SAAS,EACT,SAAS,EACT,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,eAAe,EACf,SAAS,EACT,MAAM,EACN,WAAW,EACX,aAAa,EACb,SAAS,EACT,aAAa,EACb,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,cAAc,EACd,aAAa,EACb,UAAU,EACV,eAAe,EACf,WAAW,EACX,SAAS,EACT,OAAO,EACP,YAAY,EACZ,OAAO,EACP,MAAM,EACN,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,WAAW,EACX,gBAAgB,EAChB,WAAW;IAAE;IACb,SAAS,EACT,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,mBAAmB,EACnB,iBAAiB,EACjB,OAAO,EACP,gBAAgB,EAChB,cAAc,EACd,eAAe;IAAE;IACjB,iBAAiB,EACjB,aAAa,EACb,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,aAAa;IAAE;IACf,MAAM,EACN,SAAS,EACT,WAAW,EACX,WAAW,EACX,SAAS,EACT,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,aAAa,EACb,oBAAoB,EACpB,QAAQ,EACR,kBAAkB;IAAE;IACpB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;IAAE;IACP,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,cAAc,EACd,YAAY,EACZ,aAAa,EACb,UAAU,EACV,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,oBAAoB,EACpB,UAAU,EACV,cAAc,EACd,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,SAAS,EACT,gBAAgB,EAChB,gBAAgB,EAChB,aAAa,EACb,QAAQ,EACR,eAAe,EACf,cAAc,EACd,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,mBAAmB,EACnB,cAAc,EACd,WAAW,EACX,aAAa,EACb,uBAAuB,EACvB,iBAAiB,CAAC;IAAA,CACnB;IACDpB,MAAM,CAACoB,WAAW,CAACX,OAAO,GAAGe,gBAAgB,CAACJ,WAAW,CAAC;IAC1DjB,KAAK,CAAC+B,SAAS,CAAClC,MAAM,GAAGA,MAAM;EACjC,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}