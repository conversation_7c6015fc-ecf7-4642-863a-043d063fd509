{"ast": null, "code": "'use strict';\n\nmodule.exports = protobuf;\nprotobuf.displayName = 'protobuf';\nprotobuf.aliases = [];\nfunction protobuf(Prism) {\n  ;\n  (function (Prism) {\n    var builtinTypes = /\\b(?:bool|bytes|double|s?fixed(?:32|64)|float|[su]?int(?:32|64)|string)\\b/;\n    Prism.languages.protobuf = Prism.languages.extend('clike', {\n      'class-name': [{\n        pattern: /(\\b(?:enum|extend|message|service)\\s+)[A-Za-z_]\\w*(?=\\s*\\{)/,\n        lookbehind: true\n      }, {\n        pattern: /(\\b(?:rpc\\s+\\w+|returns)\\s*\\(\\s*(?:stream\\s+)?)\\.?[A-Za-z_]\\w*(?:\\.[A-Za-z_]\\w*)*(?=\\s*\\))/,\n        lookbehind: true\n      }],\n      keyword: /\\b(?:enum|extend|extensions|import|message|oneof|option|optional|package|public|repeated|required|reserved|returns|rpc(?=\\s+\\w)|service|stream|syntax|to)\\b(?!\\s*=\\s*\\d)/,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i\n    });\n    Prism.languages.insertBefore('protobuf', 'operator', {\n      map: {\n        pattern: /\\bmap<\\s*[\\w.]+\\s*,\\s*[\\w.]+\\s*>(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /[<>.,]/,\n          builtin: builtinTypes\n        }\n      },\n      builtin: builtinTypes,\n      'positional-class-name': {\n        pattern: /(?:\\b|\\B\\.)[a-z_]\\w*(?:\\.[a-z_]\\w*)*(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      annotation: {\n        pattern: /(\\[\\s*)[a-z_]\\w*(?=\\s*=)/i,\n        lookbehind: true\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "protobuf", "displayName", "aliases", "Prism", "builtinTypes", "languages", "extend", "pattern", "lookbehind", "keyword", "function", "insertBefore", "map", "alias", "inside", "punctuation", "builtin", "annotation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/protobuf.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = protobuf\nprotobuf.displayName = 'protobuf'\nprotobuf.aliases = []\nfunction protobuf(Prism) {\n  ;(function (Prism) {\n    var builtinTypes =\n      /\\b(?:bool|bytes|double|s?fixed(?:32|64)|float|[su]?int(?:32|64)|string)\\b/\n    Prism.languages.protobuf = Prism.languages.extend('clike', {\n      'class-name': [\n        {\n          pattern:\n            /(\\b(?:enum|extend|message|service)\\s+)[A-Za-z_]\\w*(?=\\s*\\{)/,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /(\\b(?:rpc\\s+\\w+|returns)\\s*\\(\\s*(?:stream\\s+)?)\\.?[A-Za-z_]\\w*(?:\\.[A-Za-z_]\\w*)*(?=\\s*\\))/,\n          lookbehind: true\n        }\n      ],\n      keyword:\n        /\\b(?:enum|extend|extensions|import|message|oneof|option|optional|package|public|repeated|required|reserved|returns|rpc(?=\\s+\\w)|service|stream|syntax|to)\\b(?!\\s*=\\s*\\d)/,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i\n    })\n    Prism.languages.insertBefore('protobuf', 'operator', {\n      map: {\n        pattern: /\\bmap<\\s*[\\w.]+\\s*,\\s*[\\w.]+\\s*>(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /[<>.,]/,\n          builtin: builtinTypes\n        }\n      },\n      builtin: builtinTypes,\n      'positional-class-name': {\n        pattern: /(?:\\b|\\B\\.)[a-z_]\\w*(?:\\.[a-z_]\\w*)*(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      annotation: {\n        pattern: /(\\[\\s*)[a-z_]\\w*(?=\\s*=)/i,\n        lookbehind: true\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,YAAY,GACd,2EAA2E;IAC7ED,KAAK,CAACE,SAAS,CAACL,QAAQ,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MACzD,YAAY,EAAE,CACZ;QACEC,OAAO,EACL,6DAA6D;QAC/DC,UAAU,EAAE;MACd,CAAC,EACD;QACED,OAAO,EACL,4FAA4F;QAC9FC,UAAU,EAAE;MACd,CAAC,CACF;MACDC,OAAO,EACL,0KAA0K;MAC5KC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFP,KAAK,CAACE,SAAS,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,EAAE;MACnDC,GAAG,EAAE;QACHL,OAAO,EAAE,0DAA0D;QACnEM,KAAK,EAAE,YAAY;QACnBC,MAAM,EAAE;UACNC,WAAW,EAAE,QAAQ;UACrBC,OAAO,EAAEZ;QACX;MACF,CAAC;MACDY,OAAO,EAAEZ,YAAY;MACrB,uBAAuB,EAAE;QACvBG,OAAO,EAAE,8DAA8D;QACvEM,KAAK,EAAE,YAAY;QACnBC,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACDE,UAAU,EAAE;QACVV,OAAO,EAAE,2BAA2B;QACpCC,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ,CAAC,EAAEL,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}