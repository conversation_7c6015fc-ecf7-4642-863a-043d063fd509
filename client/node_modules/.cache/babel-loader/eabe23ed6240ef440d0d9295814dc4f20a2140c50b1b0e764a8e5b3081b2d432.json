{"ast": null, "code": "'use strict';\n\nvar refractorJava = require('./java.js');\nvar refractorJavadoclike = require('./javadoclike.js');\nmodule.exports = javadoc;\njavadoc.displayName = 'javadoc';\njavadoc.aliases = [];\nfunction javadoc(Prism) {\n  Prism.register(refractorJava);\n  Prism.register(refractorJavadoclike);\n  (function (Prism) {\n    var codeLinePattern = /(^(?:[\\t ]*(?:\\*\\s*)*))[^*\\s].*$/m;\n    var memberReference = /#\\s*\\w+(?:\\s*\\([^()]*\\))?/.source;\n    var reference = /(?:\\b[a-zA-Z]\\w+\\s*\\.\\s*)*\\b[A-Z]\\w*(?:\\s*<mem>)?|<mem>/.source.replace(/<mem>/g, function () {\n      return memberReference;\n    });\n    Prism.languages.javadoc = Prism.languages.extend('javadoclike', {});\n    Prism.languages.insertBefore('javadoc', 'keyword', {\n      reference: {\n        pattern: RegExp(/(@(?:exception|link|linkplain|see|throws|value)\\s+(?:\\*\\s*)?)/.source + '(?:' + reference + ')'),\n        lookbehind: true,\n        inside: {\n          function: {\n            pattern: /(#\\s*)\\w+(?=\\s*\\()/,\n            lookbehind: true\n          },\n          field: {\n            pattern: /(#\\s*)\\w+/,\n            lookbehind: true\n          },\n          namespace: {\n            pattern: /\\b(?:[a-z]\\w*\\s*\\.\\s*)+/,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          'class-name': /\\b[A-Z]\\w*/,\n          keyword: Prism.languages.java.keyword,\n          punctuation: /[#()[\\],.]/\n        }\n      },\n      'class-name': {\n        // @param <T> the first generic type parameter\n        pattern: /(@param\\s+)<[A-Z]\\w*>/,\n        lookbehind: true,\n        inside: {\n          punctuation: /[.<>]/\n        }\n      },\n      'code-section': [{\n        pattern: /(\\{@code\\s+(?!\\s))(?:[^\\s{}]|\\s+(?![\\s}])|\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\})+(?=\\s*\\})/,\n        lookbehind: true,\n        inside: {\n          code: {\n            // there can't be any HTML inside of {@code} tags\n            pattern: codeLinePattern,\n            lookbehind: true,\n            inside: Prism.languages.java,\n            alias: 'language-java'\n          }\n        }\n      }, {\n        pattern: /(<(code|pre|tt)>(?!<code>)\\s*)\\S(?:\\S|\\s+\\S)*?(?=\\s*<\\/\\2>)/,\n        lookbehind: true,\n        inside: {\n          line: {\n            pattern: codeLinePattern,\n            lookbehind: true,\n            inside: {\n              // highlight HTML tags and entities\n              tag: Prism.languages.markup.tag,\n              entity: Prism.languages.markup.entity,\n              code: {\n                // everything else is Java code\n                pattern: /.+/,\n                inside: Prism.languages.java,\n                alias: 'language-java'\n              }\n            }\n          }\n        }\n      }],\n      tag: Prism.languages.markup.tag,\n      entity: Prism.languages.markup.entity\n    });\n    Prism.languages.javadoclike.addSupport('java', Prism.languages.javadoc);\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorJava", "require", "refractorJavadoclike", "module", "exports", "javadoc", "displayName", "aliases", "Prism", "register", "codeLinePattern", "memberReference", "source", "reference", "replace", "languages", "extend", "insertBefore", "pattern", "RegExp", "lookbehind", "inside", "function", "field", "namespace", "punctuation", "keyword", "java", "code", "alias", "line", "tag", "markup", "entity", "javadoclike", "addSupport"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/javadoc.js"], "sourcesContent": ["'use strict'\nvar refractorJava = require('./java.js')\nvar refractorJavadoclike = require('./javadoclike.js')\nmodule.exports = javadoc\njavadoc.displayName = 'javadoc'\njavadoc.aliases = []\nfunction javadoc(Prism) {\n  Prism.register(refractorJava)\n  Prism.register(refractorJavadoclike)\n  ;(function (Prism) {\n    var codeLinePattern = /(^(?:[\\t ]*(?:\\*\\s*)*))[^*\\s].*$/m\n    var memberReference = /#\\s*\\w+(?:\\s*\\([^()]*\\))?/.source\n    var reference =\n      /(?:\\b[a-zA-Z]\\w+\\s*\\.\\s*)*\\b[A-Z]\\w*(?:\\s*<mem>)?|<mem>/.source.replace(\n        /<mem>/g,\n        function () {\n          return memberReference\n        }\n      )\n    Prism.languages.javadoc = Prism.languages.extend('javadoclike', {})\n    Prism.languages.insertBefore('javadoc', 'keyword', {\n      reference: {\n        pattern: RegExp(\n          /(@(?:exception|link|linkplain|see|throws|value)\\s+(?:\\*\\s*)?)/\n            .source +\n            '(?:' +\n            reference +\n            ')'\n        ),\n        lookbehind: true,\n        inside: {\n          function: {\n            pattern: /(#\\s*)\\w+(?=\\s*\\()/,\n            lookbehind: true\n          },\n          field: {\n            pattern: /(#\\s*)\\w+/,\n            lookbehind: true\n          },\n          namespace: {\n            pattern: /\\b(?:[a-z]\\w*\\s*\\.\\s*)+/,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          'class-name': /\\b[A-Z]\\w*/,\n          keyword: Prism.languages.java.keyword,\n          punctuation: /[#()[\\],.]/\n        }\n      },\n      'class-name': {\n        // @param <T> the first generic type parameter\n        pattern: /(@param\\s+)<[A-Z]\\w*>/,\n        lookbehind: true,\n        inside: {\n          punctuation: /[.<>]/\n        }\n      },\n      'code-section': [\n        {\n          pattern:\n            /(\\{@code\\s+(?!\\s))(?:[^\\s{}]|\\s+(?![\\s}])|\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\})+(?=\\s*\\})/,\n          lookbehind: true,\n          inside: {\n            code: {\n              // there can't be any HTML inside of {@code} tags\n              pattern: codeLinePattern,\n              lookbehind: true,\n              inside: Prism.languages.java,\n              alias: 'language-java'\n            }\n          }\n        },\n        {\n          pattern:\n            /(<(code|pre|tt)>(?!<code>)\\s*)\\S(?:\\S|\\s+\\S)*?(?=\\s*<\\/\\2>)/,\n          lookbehind: true,\n          inside: {\n            line: {\n              pattern: codeLinePattern,\n              lookbehind: true,\n              inside: {\n                // highlight HTML tags and entities\n                tag: Prism.languages.markup.tag,\n                entity: Prism.languages.markup.entity,\n                code: {\n                  // everything else is Java code\n                  pattern: /.+/,\n                  inside: Prism.languages.java,\n                  alias: 'language-java'\n                }\n              }\n            }\n          }\n        }\n      ],\n      tag: Prism.languages.markup.tag,\n      entity: Prism.languages.markup.entity\n    })\n    Prism.languages.javadoclike.addSupport('java', Prism.languages.javadoc)\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AACtDE,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,QAAQ,CAACT,aAAa,CAAC;EAC7BQ,KAAK,CAACC,QAAQ,CAACP,oBAAoB,CAAC;EACnC,CAAC,UAAUM,KAAK,EAAE;IACjB,IAAIE,eAAe,GAAG,mCAAmC;IACzD,IAAIC,eAAe,GAAG,2BAA2B,CAACC,MAAM;IACxD,IAAIC,SAAS,GACX,yDAAyD,CAACD,MAAM,CAACE,OAAO,CACtE,QAAQ,EACR,YAAY;MACV,OAAOH,eAAe;IACxB,CACF,CAAC;IACHH,KAAK,CAACO,SAAS,CAACV,OAAO,GAAGG,KAAK,CAACO,SAAS,CAACC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IACnER,KAAK,CAACO,SAAS,CAACE,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE;MACjDJ,SAAS,EAAE;QACTK,OAAO,EAAEC,MAAM,CACb,+DAA+D,CAC5DP,MAAM,GACP,KAAK,GACLC,SAAS,GACT,GACJ,CAAC;QACDO,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,QAAQ,EAAE;YACRJ,OAAO,EAAE,oBAAoB;YAC7BE,UAAU,EAAE;UACd,CAAC;UACDG,KAAK,EAAE;YACLL,OAAO,EAAE,WAAW;YACpBE,UAAU,EAAE;UACd,CAAC;UACDI,SAAS,EAAE;YACTN,OAAO,EAAE,yBAAyB;YAClCG,MAAM,EAAE;cACNI,WAAW,EAAE;YACf;UACF,CAAC;UACD,YAAY,EAAE,YAAY;UAC1BC,OAAO,EAAElB,KAAK,CAACO,SAAS,CAACY,IAAI,CAACD,OAAO;UACrCD,WAAW,EAAE;QACf;MACF,CAAC;MACD,YAAY,EAAE;QACZ;QACAP,OAAO,EAAE,uBAAuB;QAChCE,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNI,WAAW,EAAE;QACf;MACF,CAAC;MACD,cAAc,EAAE,CACd;QACEP,OAAO,EACL,8GAA8G;QAChHE,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNO,IAAI,EAAE;YACJ;YACAV,OAAO,EAAER,eAAe;YACxBU,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAEb,KAAK,CAACO,SAAS,CAACY,IAAI;YAC5BE,KAAK,EAAE;UACT;QACF;MACF,CAAC,EACD;QACEX,OAAO,EACL,6DAA6D;QAC/DE,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNS,IAAI,EAAE;YACJZ,OAAO,EAAER,eAAe;YACxBU,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;cACN;cACAU,GAAG,EAAEvB,KAAK,CAACO,SAAS,CAACiB,MAAM,CAACD,GAAG;cAC/BE,MAAM,EAAEzB,KAAK,CAACO,SAAS,CAACiB,MAAM,CAACC,MAAM;cACrCL,IAAI,EAAE;gBACJ;gBACAV,OAAO,EAAE,IAAI;gBACbG,MAAM,EAAEb,KAAK,CAACO,SAAS,CAACY,IAAI;gBAC5BE,KAAK,EAAE;cACT;YACF;UACF;QACF;MACF,CAAC,CACF;MACDE,GAAG,EAAEvB,KAAK,CAACO,SAAS,CAACiB,MAAM,CAACD,GAAG;MAC/BE,MAAM,EAAEzB,KAAK,CAACO,SAAS,CAACiB,MAAM,CAACC;IACjC,CAAC,CAAC;IACFzB,KAAK,CAACO,SAAS,CAACmB,WAAW,CAACC,UAAU,CAAC,MAAM,EAAE3B,KAAK,CAACO,SAAS,CAACV,OAAO,CAAC;EACzE,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}