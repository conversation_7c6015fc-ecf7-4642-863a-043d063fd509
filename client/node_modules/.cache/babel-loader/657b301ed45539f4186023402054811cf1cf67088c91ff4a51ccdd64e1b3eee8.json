{"ast": null, "code": "'use strict';\n\nmodule.exports = batch;\nbatch.displayName = 'batch';\nbatch.aliases = [];\nfunction batch(Prism) {\n  ;\n  (function (Prism) {\n    var variable = /%%?[~:\\w]+%?|!\\S+!/;\n    var parameter = {\n      pattern: /\\/[a-z?]+(?=[ :]|$):?|-[a-z]\\b|--[a-z-]+\\b/im,\n      alias: 'attr-name',\n      inside: {\n        punctuation: /:/\n      }\n    };\n    var string = /\"(?:[\\\\\"]\"|[^\"])*\"(?!\")/;\n    var number = /(?:\\b|-)\\d+\\b/;\n    Prism.languages.batch = {\n      comment: [/^::.*/m, {\n        pattern: /((?:^|[&(])[ \\t]*)rem\\b(?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n        lookbehind: true\n      }],\n      label: {\n        pattern: /^:.*/m,\n        alias: 'property'\n      },\n      command: [{\n        // FOR command\n        pattern: /((?:^|[&(])[ \\t]*)for(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* \\S+ in \\([^)]+\\) do/im,\n        lookbehind: true,\n        inside: {\n          keyword: /\\b(?:do|in)\\b|^for\\b/i,\n          string: string,\n          parameter: parameter,\n          variable: variable,\n          number: number,\n          punctuation: /[()',]/\n        }\n      }, {\n        // IF command\n        pattern: /((?:^|[&(])[ \\t]*)if(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:not )?(?:cmdextversion \\d+|defined \\w+|errorlevel \\d+|exist \\S+|(?:\"[^\"]*\"|(?!\")(?:(?!==)\\S)+)?(?:==| (?:equ|geq|gtr|leq|lss|neq) )(?:\"[^\"]*\"|[^\\s\"]\\S*))/im,\n        lookbehind: true,\n        inside: {\n          keyword: /\\b(?:cmdextversion|defined|errorlevel|exist|not)\\b|^if\\b/i,\n          string: string,\n          parameter: parameter,\n          variable: variable,\n          number: number,\n          operator: /\\^|==|\\b(?:equ|geq|gtr|leq|lss|neq)\\b/i\n        }\n      }, {\n        // ELSE command\n        pattern: /((?:^|[&()])[ \\t]*)else\\b/im,\n        lookbehind: true,\n        inside: {\n          keyword: /^else\\b/i\n        }\n      }, {\n        // SET command\n        pattern: /((?:^|[&(])[ \\t]*)set(?: \\/[a-z](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n        lookbehind: true,\n        inside: {\n          keyword: /^set\\b/i,\n          string: string,\n          parameter: parameter,\n          variable: [variable, /\\w+(?=(?:[*\\/%+\\-&^|]|<<|>>)?=)/],\n          number: number,\n          operator: /[*\\/%+\\-&^|]=?|<<=?|>>=?|[!~_=]/,\n          punctuation: /[()',]/\n        }\n      }, {\n        // Other commands\n        pattern: /((?:^|[&(])[ \\t]*@?)\\w+\\b(?:\"(?:[\\\\\"]\"|[^\"])*\"(?!\")|[^\"^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/m,\n        lookbehind: true,\n        inside: {\n          keyword: /^\\w+\\b/,\n          string: string,\n          parameter: parameter,\n          label: {\n            pattern: /(^\\s*):\\S+/m,\n            lookbehind: true,\n            alias: 'property'\n          },\n          variable: variable,\n          number: number,\n          operator: /\\^/\n        }\n      }],\n      operator: /[&@]/,\n      punctuation: /[()']/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "batch", "displayName", "aliases", "Prism", "variable", "parameter", "pattern", "alias", "inside", "punctuation", "string", "number", "languages", "comment", "lookbehind", "label", "command", "keyword", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/batch.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = batch\nbatch.displayName = 'batch'\nbatch.aliases = []\nfunction batch(Prism) {\n  ;(function (Prism) {\n    var variable = /%%?[~:\\w]+%?|!\\S+!/\n    var parameter = {\n      pattern: /\\/[a-z?]+(?=[ :]|$):?|-[a-z]\\b|--[a-z-]+\\b/im,\n      alias: 'attr-name',\n      inside: {\n        punctuation: /:/\n      }\n    }\n    var string = /\"(?:[\\\\\"]\"|[^\"])*\"(?!\")/\n    var number = /(?:\\b|-)\\d+\\b/\n    Prism.languages.batch = {\n      comment: [\n        /^::.*/m,\n        {\n          pattern: /((?:^|[&(])[ \\t]*)rem\\b(?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n          lookbehind: true\n        }\n      ],\n      label: {\n        pattern: /^:.*/m,\n        alias: 'property'\n      },\n      command: [\n        {\n          // FOR command\n          pattern:\n            /((?:^|[&(])[ \\t]*)for(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* \\S+ in \\([^)]+\\) do/im,\n          lookbehind: true,\n          inside: {\n            keyword: /\\b(?:do|in)\\b|^for\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: variable,\n            number: number,\n            punctuation: /[()',]/\n          }\n        },\n        {\n          // IF command\n          pattern:\n            /((?:^|[&(])[ \\t]*)if(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:not )?(?:cmdextversion \\d+|defined \\w+|errorlevel \\d+|exist \\S+|(?:\"[^\"]*\"|(?!\")(?:(?!==)\\S)+)?(?:==| (?:equ|geq|gtr|leq|lss|neq) )(?:\"[^\"]*\"|[^\\s\"]\\S*))/im,\n          lookbehind: true,\n          inside: {\n            keyword:\n              /\\b(?:cmdextversion|defined|errorlevel|exist|not)\\b|^if\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: variable,\n            number: number,\n            operator: /\\^|==|\\b(?:equ|geq|gtr|leq|lss|neq)\\b/i\n          }\n        },\n        {\n          // ELSE command\n          pattern: /((?:^|[&()])[ \\t]*)else\\b/im,\n          lookbehind: true,\n          inside: {\n            keyword: /^else\\b/i\n          }\n        },\n        {\n          // SET command\n          pattern:\n            /((?:^|[&(])[ \\t]*)set(?: \\/[a-z](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n          lookbehind: true,\n          inside: {\n            keyword: /^set\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: [variable, /\\w+(?=(?:[*\\/%+\\-&^|]|<<|>>)?=)/],\n            number: number,\n            operator: /[*\\/%+\\-&^|]=?|<<=?|>>=?|[!~_=]/,\n            punctuation: /[()',]/\n          }\n        },\n        {\n          // Other commands\n          pattern:\n            /((?:^|[&(])[ \\t]*@?)\\w+\\b(?:\"(?:[\\\\\"]\"|[^\"])*\"(?!\")|[^\"^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^\\w+\\b/,\n            string: string,\n            parameter: parameter,\n            label: {\n              pattern: /(^\\s*):\\S+/m,\n              lookbehind: true,\n              alias: 'property'\n            },\n            variable: variable,\n            number: number,\n            operator: /\\^/\n          }\n        }\n      ],\n      operator: /[&@]/,\n      punctuation: /[()']/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GAAG,oBAAoB;IACnC,IAAIC,SAAS,GAAG;MACdC,OAAO,EAAE,8CAA8C;MACvDC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACD,IAAIC,MAAM,GAAG,yBAAyB;IACtC,IAAIC,MAAM,GAAG,eAAe;IAC5BR,KAAK,CAACS,SAAS,CAACZ,KAAK,GAAG;MACtBa,OAAO,EAAE,CACP,QAAQ,EACR;QACEP,OAAO,EAAE,4DAA4D;QACrEQ,UAAU,EAAE;MACd,CAAC,CACF;MACDC,KAAK,EAAE;QACLT,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE;MACT,CAAC;MACDS,OAAO,EAAE,CACP;QACE;QACAV,OAAO,EACL,0FAA0F;QAC5FQ,UAAU,EAAE,IAAI;QAChBN,MAAM,EAAE;UACNS,OAAO,EAAE,uBAAuB;UAChCP,MAAM,EAAEA,MAAM;UACdL,SAAS,EAAEA,SAAS;UACpBD,QAAQ,EAAEA,QAAQ;UAClBO,MAAM,EAAEA,MAAM;UACdF,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACE;QACAH,OAAO,EACL,kOAAkO;QACpOQ,UAAU,EAAE,IAAI;QAChBN,MAAM,EAAE;UACNS,OAAO,EACL,2DAA2D;UAC7DP,MAAM,EAAEA,MAAM;UACdL,SAAS,EAAEA,SAAS;UACpBD,QAAQ,EAAEA,QAAQ;UAClBO,MAAM,EAAEA,MAAM;UACdO,QAAQ,EAAE;QACZ;MACF,CAAC,EACD;QACE;QACAZ,OAAO,EAAE,6BAA6B;QACtCQ,UAAU,EAAE,IAAI;QAChBN,MAAM,EAAE;UACNS,OAAO,EAAE;QACX;MACF,CAAC,EACD;QACE;QACAX,OAAO,EACL,uGAAuG;QACzGQ,UAAU,EAAE,IAAI;QAChBN,MAAM,EAAE;UACNS,OAAO,EAAE,SAAS;UAClBP,MAAM,EAAEA,MAAM;UACdL,SAAS,EAAEA,SAAS;UACpBD,QAAQ,EAAE,CAACA,QAAQ,EAAE,iCAAiC,CAAC;UACvDO,MAAM,EAAEA,MAAM;UACdO,QAAQ,EAAE,iCAAiC;UAC3CT,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACE;QACAH,OAAO,EACL,sFAAsF;QACxFQ,UAAU,EAAE,IAAI;QAChBN,MAAM,EAAE;UACNS,OAAO,EAAE,QAAQ;UACjBP,MAAM,EAAEA,MAAM;UACdL,SAAS,EAAEA,SAAS;UACpBU,KAAK,EAAE;YACLT,OAAO,EAAE,aAAa;YACtBQ,UAAU,EAAE,IAAI;YAChBP,KAAK,EAAE;UACT,CAAC;UACDH,QAAQ,EAAEA,QAAQ;UAClBO,MAAM,EAAEA,MAAM;UACdO,QAAQ,EAAE;QACZ;MACF,CAAC,CACF;MACDA,QAAQ,EAAE,MAAM;MAChBT,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEN,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}