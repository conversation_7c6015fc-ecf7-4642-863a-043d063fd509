{"ast": null, "code": "'use strict';\n\nmodule.exports = $false;\n$false.displayName = '$false';\n$false.aliases = [];\nfunction $false(Prism) {\n  ;\n  (function (Prism) {\n    /**\n     * Based on the manual by <PERSON><PERSON><PERSON>.\n     *\n     * @see {@link https://github.com/PrismJS/prism/issues/2801#issue-829717504}\n     */\n    Prism.languages['false'] = {\n      comment: {\n        pattern: /\\{[^}]*\\}/\n      },\n      string: {\n        pattern: /\"[^\"]*\"/,\n        greedy: true\n      },\n      'character-code': {\n        pattern: /'(?:[^\\r]|\\r\\n?)/,\n        alias: 'number'\n      },\n      'assembler-code': {\n        pattern: /\\d+`/,\n        alias: 'important'\n      },\n      number: /\\d+/,\n      operator: /[-!#$%&'*+,./:;=>?@\\\\^_`|~ßø]/,\n      punctuation: /\\[|\\]/,\n      variable: /[a-z]/,\n      'non-standard': {\n        pattern: /[()<BDO®]/,\n        alias: 'bold'\n      }\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "$false", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "string", "greedy", "alias", "number", "operator", "punctuation", "variable"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/false.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = $false\n$false.displayName = '$false'\n$false.aliases = []\nfunction $false(Prism) {\n  ;(function (Prism) {\n    /**\n     * Based on the manual by <PERSON><PERSON><PERSON>.\n     *\n     * @see {@link https://github.com/PrismJS/prism/issues/2801#issue-829717504}\n     */\n    Prism.languages['false'] = {\n      comment: {\n        pattern: /\\{[^}]*\\}/\n      },\n      string: {\n        pattern: /\"[^\"]*\"/,\n        greedy: true\n      },\n      'character-code': {\n        pattern: /'(?:[^\\r]|\\r\\n?)/,\n        alias: 'number'\n      },\n      'assembler-code': {\n        pattern: /\\d+`/,\n        alias: 'important'\n      },\n      number: /\\d+/,\n      operator: /[-!#$%&'*+,./:;=>?@\\\\^_`|~ßø]/,\n      punctuation: /\\[|\\]/,\n      variable: /[a-z]/,\n      'non-standard': {\n        pattern: /[()<BDO®]/,\n        alias: 'bold'\n      }\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACIA,KAAK,CAACC,SAAS,CAAC,OAAO,CAAC,GAAG;MACzBC,OAAO,EAAE;QACPC,OAAO,EAAE;MACX,CAAC;MACDC,MAAM,EAAE;QACND,OAAO,EAAE,SAAS;QAClBE,MAAM,EAAE;MACV,CAAC;MACD,gBAAgB,EAAE;QAChBF,OAAO,EAAE,kBAAkB;QAC3BG,KAAK,EAAE;MACT,CAAC;MACD,gBAAgB,EAAE;QAChBH,OAAO,EAAE,MAAM;QACfG,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,+BAA+B;MACzCC,WAAW,EAAE,OAAO;MACpBC,QAAQ,EAAE,OAAO;MACjB,cAAc,EAAE;QACdP,OAAO,EAAE,WAAW;QACpBG,KAAK,EAAE;MACT;IACF,CAAC;EACH,CAAC,EAAEN,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}