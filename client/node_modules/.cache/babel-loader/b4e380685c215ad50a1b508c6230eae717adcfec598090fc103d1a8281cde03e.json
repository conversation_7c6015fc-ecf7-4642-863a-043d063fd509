{"ast": null, "code": "'use strict';\n\nmodule.exports = smalltalk;\nsmalltalk.displayName = 'smalltalk';\nsmalltalk.aliases = [];\nfunction smalltalk(Prism) {\n  Prism.languages.smalltalk = {\n    comment: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    char: {\n      pattern: /\\$./,\n      greedy: true\n    },\n    string: {\n      pattern: /'(?:''|[^'])*'/,\n      greedy: true\n    },\n    symbol: /#[\\da-z]+|#(?:-|([+\\/\\\\*~<>=@%|&?!])\\1?)|#(?=\\()/i,\n    'block-arguments': {\n      pattern: /(\\[\\s*):[^\\[|]*\\|/,\n      lookbehind: true,\n      inside: {\n        variable: /:[\\da-z]+/i,\n        punctuation: /\\|/\n      }\n    },\n    'temporary-variables': {\n      pattern: /\\|[^|]+\\|/,\n      inside: {\n        variable: /[\\da-z]+/i,\n        punctuation: /\\|/\n      }\n    },\n    keyword: /\\b(?:new|nil|self|super)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: [/\\d+r-?[\\dA-Z]+(?:\\.[\\dA-Z]+)?(?:e-?\\d+)?/, /\\b\\d+(?:\\.\\d+)?(?:e-?\\d+)?/],\n    operator: /[<=]=?|:=|~[~=]|\\/\\/?|\\\\\\\\|>[>=]?|[!^+\\-*&|,@]/,\n    punctuation: /[.;:?\\[\\](){}]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "smalltalk", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "char", "string", "symbol", "lookbehind", "inside", "variable", "punctuation", "keyword", "boolean", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/smalltalk.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = smalltalk\nsmalltalk.displayName = 'smalltalk'\nsmalltalk.aliases = []\nfunction smalltalk(Prism) {\n  Prism.languages.smalltalk = {\n    comment: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    char: {\n      pattern: /\\$./,\n      greedy: true\n    },\n    string: {\n      pattern: /'(?:''|[^'])*'/,\n      greedy: true\n    },\n    symbol: /#[\\da-z]+|#(?:-|([+\\/\\\\*~<>=@%|&?!])\\1?)|#(?=\\()/i,\n    'block-arguments': {\n      pattern: /(\\[\\s*):[^\\[|]*\\|/,\n      lookbehind: true,\n      inside: {\n        variable: /:[\\da-z]+/i,\n        punctuation: /\\|/\n      }\n    },\n    'temporary-variables': {\n      pattern: /\\|[^|]+\\|/,\n      inside: {\n        variable: /[\\da-z]+/i,\n        punctuation: /\\|/\n      }\n    },\n    keyword: /\\b(?:new|nil|self|super)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: [\n      /\\d+r-?[\\dA-Z]+(?:\\.[\\dA-Z]+)?(?:e-?\\d+)?/,\n      /\\b\\d+(?:\\.\\d+)?(?:e-?\\d+)?/\n    ],\n    operator: /[<=]=?|:=|~[~=]|\\/\\/?|\\\\\\\\|>[>=]?|[!^+\\-*&|,@]/,\n    punctuation: /[.;:?\\[\\](){}]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxBA,KAAK,CAACC,SAAS,CAACJ,SAAS,GAAG;IAC1BK,OAAO,EAAE;MACPC,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE;IACV,CAAC;IACDC,IAAI,EAAE;MACJF,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACDE,MAAM,EAAE;MACNH,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE;IACV,CAAC;IACDG,MAAM,EAAE,mDAAmD;IAC3D,iBAAiB,EAAE;MACjBJ,OAAO,EAAE,mBAAmB;MAC5BK,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,QAAQ,EAAE,YAAY;QACtBC,WAAW,EAAE;MACf;IACF,CAAC;IACD,qBAAqB,EAAE;MACrBR,OAAO,EAAE,WAAW;MACpBM,MAAM,EAAE;QACNC,QAAQ,EAAE,WAAW;QACrBC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EAAE,4BAA4B;IACrCC,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EAAE,CACN,0CAA0C,EAC1C,4BAA4B,CAC7B;IACDC,QAAQ,EAAE,gDAAgD;IAC1DJ,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}