{"ast": null, "code": "'use strict';\n\nmodule.exports = json;\njson.displayName = 'json';\njson.aliases = ['webmanifest'];\nfunction json(Prism) {\n  // https://www.json.org/json-en.html\n  Prism.languages.json = {\n    property: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      greedy: true\n    },\n    number: /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n    punctuation: /[{}[\\],]/,\n    operator: /:/,\n    boolean: /\\b(?:false|true)\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    }\n  };\n  Prism.languages.webmanifest = Prism.languages.json;\n}", "map": {"version": 3, "names": ["module", "exports", "json", "displayName", "aliases", "Prism", "languages", "property", "pattern", "lookbehind", "greedy", "string", "comment", "number", "punctuation", "operator", "boolean", "null", "alias", "webmanifest"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/json.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = json\njson.displayName = 'json'\njson.aliases = ['webmanifest']\nfunction json(Prism) {\n  // https://www.json.org/json-en.html\n  Prism.languages.json = {\n    property: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      greedy: true\n    },\n    number: /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n    punctuation: /[{}[\\],]/,\n    operator: /:/,\n    boolean: /\\b(?:false|true)\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    }\n  }\n  Prism.languages.webmanifest = Prism.languages.json\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,CAAC,aAAa,CAAC;AAC9B,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EACAA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,QAAQ,EAAE;MACRC,OAAO,EAAE,wCAAwC;MACjDC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,wCAAwC;MACjDC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACDE,OAAO,EAAE;MACPJ,OAAO,EAAE,+BAA+B;MACxCE,MAAM,EAAE;IACV,CAAC;IACDG,MAAM,EAAE,oCAAoC;IAC5CC,WAAW,EAAE,UAAU;IACvBC,QAAQ,EAAE,GAAG;IACbC,OAAO,EAAE,oBAAoB;IAC7BC,IAAI,EAAE;MACJT,OAAO,EAAE,UAAU;MACnBU,KAAK,EAAE;IACT;EACF,CAAC;EACDb,KAAK,CAACC,SAAS,CAACa,WAAW,GAAGd,KAAK,CAACC,SAAS,CAACJ,IAAI;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}