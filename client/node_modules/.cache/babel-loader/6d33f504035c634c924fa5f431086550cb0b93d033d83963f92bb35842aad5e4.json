{"ast": null, "code": "'use strict';\n\nvar xtend = require('xtend');\nvar Schema = require('./schema');\nmodule.exports = merge;\nfunction merge(definitions) {\n  var length = definitions.length;\n  var property = [];\n  var normal = [];\n  var index = -1;\n  var info;\n  var space;\n  while (++index < length) {\n    info = definitions[index];\n    property.push(info.property);\n    normal.push(info.normal);\n    space = info.space;\n  }\n  return new Schema(xtend.apply(null, property), xtend.apply(null, normal), space);\n}", "map": {"version": 3, "names": ["xtend", "require", "<PERSON><PERSON><PERSON>", "module", "exports", "merge", "definitions", "length", "property", "normal", "index", "info", "space", "push", "apply"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/lib/util/merge.js"], "sourcesContent": ["'use strict'\n\nvar xtend = require('xtend')\nvar Schema = require('./schema')\n\nmodule.exports = merge\n\nfunction merge(definitions) {\n  var length = definitions.length\n  var property = []\n  var normal = []\n  var index = -1\n  var info\n  var space\n\n  while (++index < length) {\n    info = definitions[index]\n    property.push(info.property)\n    normal.push(info.normal)\n    space = info.space\n  }\n\n  return new Schema(\n    xtend.apply(null, property),\n    xtend.apply(null, normal),\n    space\n  )\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;AAEhCE,MAAM,CAACC,OAAO,GAAGC,KAAK;AAEtB,SAASA,KAAKA,CAACC,WAAW,EAAE;EAC1B,IAAIC,MAAM,GAAGD,WAAW,CAACC,MAAM;EAC/B,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,IAAI;EACR,IAAIC,KAAK;EAET,OAAO,EAAEF,KAAK,GAAGH,MAAM,EAAE;IACvBI,IAAI,GAAGL,WAAW,CAACI,KAAK,CAAC;IACzBF,QAAQ,CAACK,IAAI,CAACF,IAAI,CAACH,QAAQ,CAAC;IAC5BC,MAAM,CAACI,IAAI,CAACF,IAAI,CAACF,MAAM,CAAC;IACxBG,KAAK,GAAGD,IAAI,CAACC,KAAK;EACpB;EAEA,OAAO,IAAIV,MAAM,CACfF,KAAK,CAACc,KAAK,CAAC,IAAI,EAAEN,QAAQ,CAAC,EAC3BR,KAAK,CAACc,KAAK,CAAC,IAAI,EAAEL,MAAM,CAAC,EACzBG,KACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}