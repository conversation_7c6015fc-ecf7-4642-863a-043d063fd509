{"ast": null, "code": "'use strict';\n\nmodule.exports = io;\nio.displayName = 'io';\nio.aliases = [];\nfunction io(Prism) {\n  Prism.languages.io = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\/\\/.*|#.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'triple-quoted-string': {\n      pattern: /\"\"\"(?:\\\\[\\s\\S]|(?!\"\"\")[^\\\\])*\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    },\n    keyword: /\\b(?:activate|activeCoroCount|asString|block|break|call|catch|clone|collectGarbage|compileString|continue|do|doFile|doMessage|doString|else|elseif|exit|for|foreach|forward|getEnvironmentVariable|getSlot|hasSlot|if|ifFalse|ifNil|ifNilEval|ifTrue|isActive|isNil|isResumable|list|message|method|parent|pass|pause|perform|performWithArgList|print|println|proto|raise|raiseResumable|removeSlot|resend|resume|schedulerSleepSeconds|self|sender|setSchedulerSleepSeconds|setSlot|shallowCopy|slotNames|super|system|then|thisBlock|thisContext|try|type|uniqueId|updateSlot|wait|while|write|yield)\\b/,\n    builtin: /\\b(?:Array|AudioDevice|AudioMixer|BigNum|Block|Box|Buffer|CFunction|CGI|Color|Curses|DBM|DNSResolver|DOConnection|DOProxy|DOServer|Date|Directory|Duration|DynLib|Error|Exception|FFT|File|Fnmatch|Font|Future|GL|GLE|GLScissor|GLU|GLUCylinder|GLUQuadric|GLUSphere|GLUT|Host|Image|Importer|LinkList|List|Lobby|Locals|MD5|MP3Decoder|MP3Encoder|Map|Message|Movie|Notification|Number|Object|OpenGL|Point|Protos|Random|Regex|SGML|SGMLElement|SGMLParser|SQLite|Sequence|Server|ShowMessage|SleepyCat|SleepyCatCursor|Socket|SocketManager|Sound|Soup|Store|String|Tree|UDPSender|UPDReceiver|URL|User|Warning|WeakLink)\\b/,\n    boolean: /\\b(?:false|nil|true)\\b/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e-?\\d+)?/i,\n    operator: /[=!*/%+\\-^&|]=|>>?=?|<<?=?|:?:?=|\\+\\+?|--?|\\*\\*?|\\/\\/?|%|\\|\\|?|&&?|\\b(?:and|not|or|return)\\b|@@?|\\?\\??|\\.\\./,\n    punctuation: /[{}[\\];(),.:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "io", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "alias", "string", "keyword", "builtin", "boolean", "number", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/io.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = io\nio.displayName = 'io'\nio.aliases = []\nfunction io(Prism) {\n  Prism.languages.io = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\/\\/.*|#.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'triple-quoted-string': {\n      pattern: /\"\"\"(?:\\\\[\\s\\S]|(?!\"\"\")[^\\\\])*\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:activate|activeCoroCount|asString|block|break|call|catch|clone|collectGarbage|compileString|continue|do|doFile|doMessage|doString|else|elseif|exit|for|foreach|forward|getEnvironmentVariable|getSlot|hasSlot|if|ifFalse|ifNil|ifNilEval|ifTrue|isActive|isNil|isResumable|list|message|method|parent|pass|pause|perform|performWithArgList|print|println|proto|raise|raiseResumable|removeSlot|resend|resume|schedulerSleepSeconds|self|sender|setSchedulerSleepSeconds|setSlot|shallowCopy|slotNames|super|system|then|thisBlock|thisContext|try|type|uniqueId|updateSlot|wait|while|write|yield)\\b/,\n    builtin:\n      /\\b(?:Array|AudioDevice|AudioMixer|BigNum|Block|Box|Buffer|CFunction|CGI|Color|Curses|DBM|DNSResolver|DOConnection|DOProxy|DOServer|Date|Directory|Duration|DynLib|Error|Exception|FFT|File|Fnmatch|Font|Future|GL|GLE|GLScissor|GLU|GLUCylinder|GLUQuadric|GLUSphere|GLUT|Host|Image|Importer|LinkList|List|Lobby|Locals|MD5|MP3Decoder|MP3Encoder|Map|Message|Movie|Notification|Number|Object|OpenGL|Point|Protos|Random|Regex|SGML|SGMLElement|SGMLParser|SQLite|Sequence|Server|ShowMessage|SleepyCat|SleepyCatCursor|Socket|SocketManager|Sound|Soup|Store|String|Tree|UDPSender|UPDReceiver|URL|User|Warning|WeakLink)\\b/,\n    boolean: /\\b(?:false|nil|true)\\b/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e-?\\d+)?/i,\n    operator:\n      /[=!*/%+\\-^&|]=|>>?=?|<<?=?|:?:?=|\\+\\+?|--?|\\*\\*?|\\/\\/?|%|\\|\\|?|&&?|\\b(?:and|not|or|return)\\b|@@?|\\?\\??|\\.\\./,\n    punctuation: /[{}[\\];(),.:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,EAAE;AACnBA,EAAE,CAACC,WAAW,GAAG,IAAI;AACrBD,EAAE,CAACE,OAAO,GAAG,EAAE;AACf,SAASF,EAAEA,CAACG,KAAK,EAAE;EACjBA,KAAK,CAACC,SAAS,CAACJ,EAAE,GAAG;IACnBK,OAAO,EAAE;MACPC,OAAO,EAAE,gDAAgD;MACzDC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,sBAAsB,EAAE;MACtBF,OAAO,EAAE,kCAAkC;MAC3CE,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNJ,OAAO,EAAE,uBAAuB;MAChCE,MAAM,EAAE;IACV,CAAC;IACDG,OAAO,EACL,4kBAA4kB;IAC9kBC,OAAO,EACL,gmBAAgmB;IAClmBC,OAAO,EAAE,wBAAwB;IACjCC,MAAM,EAAE,wDAAwD;IAChEC,QAAQ,EACN,6GAA6G;IAC/GC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}