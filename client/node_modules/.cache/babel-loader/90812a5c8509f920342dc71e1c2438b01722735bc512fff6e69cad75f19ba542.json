{"ast": null, "code": "'use strict';\n\nmodule.exports = javascript;\njavascript.displayName = 'javascript';\njavascript.aliases = ['js'];\nfunction javascript(Prism) {\n  Prism.languages.javascript = Prism.languages.extend('clike', {\n    'class-name': [Prism.languages.clike['class-name'], {\n      pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n      lookbehind: true\n    }],\n    keyword: [{\n      pattern: /((?:^|\\})\\s*)catch\\b/,\n      lookbehind: true\n    }, {\n      pattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n      lookbehind: true\n    }],\n    // Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n    function: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n    number: {\n      pattern: RegExp(/(^|[^\\w$])/.source + '(?:' + (\n      // constant\n      /NaN|Infinity/.source + '|' +\n      // binary integer\n      /0[bB][01]+(?:_[01]+)*n?/.source + '|' +\n      // octal integer\n      /0[oO][0-7]+(?:_[0-7]+)*n?/.source + '|' +\n      // hexadecimal integer\n      /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source + '|' +\n      // decimal bigint\n      /\\d+(?:_\\d+)*n/.source + '|' +\n      // decimal number (integer or float) but no bigint\n      /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source) + ')' + /(?![\\w$])/.source),\n      lookbehind: true\n    },\n    operator: /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n  });\n  Prism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\n  Prism.languages.insertBefore('javascript', 'keyword', {\n    regex: {\n      // eslint-disable-next-line regexp/no-dupe-characters-character-class\n      pattern: /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\/|\\/$/,\n        'regex-flags': /^[a-z]+$/\n      }\n    },\n    // This must be declared before keyword because we use \"function\" inside the look-forward\n    'function-variable': {\n      pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    },\n    parameter: [{\n      pattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n      lookbehind: true,\n      inside: Prism.languages.javascript\n    }, {\n      pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n      lookbehind: true,\n      inside: Prism.languages.javascript\n    }, {\n      pattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n      lookbehind: true,\n      inside: Prism.languages.javascript\n    }, {\n      pattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n      lookbehind: true,\n      inside: Prism.languages.javascript\n    }],\n    constant: /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n  });\n  Prism.languages.insertBefore('javascript', 'string', {\n    hashbang: {\n      pattern: /^#!.*/,\n      greedy: true,\n      alias: 'comment'\n    },\n    'template-string': {\n      pattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n      greedy: true,\n      inside: {\n        'template-punctuation': {\n          pattern: /^`|`$/,\n          alias: 'string'\n        },\n        interpolation: {\n          pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.javascript\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    'string-property': {\n      pattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    }\n  });\n  Prism.languages.insertBefore('javascript', 'operator', {\n    'literal-property': {\n      pattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n      lookbehind: true,\n      alias: 'property'\n    }\n  });\n  if (Prism.languages.markup) {\n    Prism.languages.markup.tag.addInlined('script', 'javascript'); // add attribute support for all DOM events.\n    // https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n    Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source, 'javascript');\n  }\n  Prism.languages.js = Prism.languages.javascript;\n}", "map": {"version": 3, "names": ["module", "exports", "javascript", "displayName", "aliases", "Prism", "languages", "extend", "clike", "pattern", "lookbehind", "keyword", "function", "number", "RegExp", "source", "operator", "insertBefore", "regex", "greedy", "inside", "alias", "parameter", "constant", "hashbang", "interpolation", "rest", "string", "markup", "tag", "addInlined", "addAttribute", "js"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/javascript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = javascript\njavascript.displayName = 'javascript'\njavascript.aliases = ['js']\nfunction javascript(Prism) {\n  Prism.languages.javascript = Prism.languages.extend('clike', {\n    'class-name': [\n      Prism.languages.clike['class-name'],\n      {\n        pattern:\n          /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n        lookbehind: true\n      }\n    ],\n    keyword: [\n      {\n        pattern: /((?:^|\\})\\s*)catch\\b/,\n        lookbehind: true\n      },\n      {\n        pattern:\n          /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n        lookbehind: true\n      }\n    ],\n    // Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n    function:\n      /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n    number: {\n      pattern: RegExp(\n        /(^|[^\\w$])/.source +\n          '(?:' + // constant\n          (/NaN|Infinity/.source +\n            '|' + // binary integer\n            /0[bB][01]+(?:_[01]+)*n?/.source +\n            '|' + // octal integer\n            /0[oO][0-7]+(?:_[0-7]+)*n?/.source +\n            '|' + // hexadecimal integer\n            /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source +\n            '|' + // decimal bigint\n            /\\d+(?:_\\d+)*n/.source +\n            '|' + // decimal number (integer or float) but no bigint\n            /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/\n              .source) +\n          ')' +\n          /(?![\\w$])/.source\n      ),\n      lookbehind: true\n    },\n    operator:\n      /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n  })\n  Prism.languages.javascript['class-name'][0].pattern =\n    /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/\n  Prism.languages.insertBefore('javascript', 'keyword', {\n    regex: {\n      // eslint-disable-next-line regexp/no-dupe-characters-character-class\n      pattern:\n        /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\/|\\/$/,\n        'regex-flags': /^[a-z]+$/\n      }\n    },\n    // This must be declared before keyword because we use \"function\" inside the look-forward\n    'function-variable': {\n      pattern:\n        /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    },\n    parameter: [\n      {\n        pattern:\n          /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      {\n        pattern:\n          /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }\n    ],\n    constant: /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n  })\n  Prism.languages.insertBefore('javascript', 'string', {\n    hashbang: {\n      pattern: /^#!.*/,\n      greedy: true,\n      alias: 'comment'\n    },\n    'template-string': {\n      pattern:\n        /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n      greedy: true,\n      inside: {\n        'template-punctuation': {\n          pattern: /^`|`$/,\n          alias: 'string'\n        },\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.javascript\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    'string-property': {\n      pattern:\n        /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    }\n  })\n  Prism.languages.insertBefore('javascript', 'operator', {\n    'literal-property': {\n      pattern:\n        /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n      lookbehind: true,\n      alias: 'property'\n    }\n  })\n  if (Prism.languages.markup) {\n    Prism.languages.markup.tag.addInlined('script', 'javascript') // add attribute support for all DOM events.\n    // https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n    Prism.languages.markup.tag.addAttribute(\n      /on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/\n        .source,\n      'javascript'\n    )\n  }\n  Prism.languages.js = Prism.languages.javascript\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AAC3B,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IAC3D,YAAY,EAAE,CACZF,KAAK,CAACC,SAAS,CAACE,KAAK,CAAC,YAAY,CAAC,EACnC;MACEC,OAAO,EACL,yGAAyG;MAC3GC,UAAU,EAAE;IACd,CAAC,CACF;IACDC,OAAO,EAAE,CACP;MACEF,OAAO,EAAE,sBAAsB;MAC/BC,UAAU,EAAE;IACd,CAAC,EACD;MACED,OAAO,EACL,kdAAkd;MACpdC,UAAU,EAAE;IACd,CAAC,CACF;IACD;IACAE,QAAQ,EACN,mGAAmG;IACrGC,MAAM,EAAE;MACNJ,OAAO,EAAEK,MAAM,CACb,YAAY,CAACC,MAAM,GACjB,KAAK;MAAG;MACP,cAAc,CAACA,MAAM,GACpB,GAAG;MAAG;MACN,yBAAyB,CAACA,MAAM,GAChC,GAAG;MAAG;MACN,2BAA2B,CAACA,MAAM,GAClC,GAAG;MAAG;MACN,qCAAqC,CAACA,MAAM,GAC5C,GAAG;MAAG;MACN,eAAe,CAACA,MAAM,GACtB,GAAG;MAAG;MACN,mFAAmF,CAChFA,MAAM,CAAC,GACZ,GAAG,GACH,WAAW,CAACA,MAChB,CAAC;MACDL,UAAU,EAAE;IACd,CAAC;IACDM,QAAQ,EACN;EACJ,CAAC,CAAC;EACFX,KAAK,CAACC,SAAS,CAACJ,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAACO,OAAO,GACjD,sEAAsE;EACxEJ,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE;IACpDC,KAAK,EAAE;MACL;MACAT,OAAO,EACL,wLAAwL;MAC1LC,UAAU,EAAE,IAAI;MAChBS,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACN,cAAc,EAAE;UACdX,OAAO,EAAE,2BAA2B;UACpCC,UAAU,EAAE,IAAI;UAChBW,KAAK,EAAE,gBAAgB;UACvBD,MAAM,EAAEf,KAAK,CAACC,SAAS,CAACY;QAC1B,CAAC;QACD,iBAAiB,EAAE,SAAS;QAC5B,aAAa,EAAE;MACjB;IACF,CAAC;IACD;IACA,mBAAmB,EAAE;MACnBT,OAAO,EACL,+LAA+L;MACjMY,KAAK,EAAE;IACT,CAAC;IACDC,SAAS,EAAE,CACT;MACEb,OAAO,EACL,qIAAqI;MACvIC,UAAU,EAAE,IAAI;MAChBU,MAAM,EAAEf,KAAK,CAACC,SAAS,CAACJ;IAC1B,CAAC,EACD;MACEO,OAAO,EACL,oFAAoF;MACtFC,UAAU,EAAE,IAAI;MAChBU,MAAM,EAAEf,KAAK,CAACC,SAAS,CAACJ;IAC1B,CAAC,EACD;MACEO,OAAO,EACL,iEAAiE;MACnEC,UAAU,EAAE,IAAI;MAChBU,MAAM,EAAEf,KAAK,CAACC,SAAS,CAACJ;IAC1B,CAAC,EACD;MACEO,OAAO,EACL,6eAA6e;MAC/eC,UAAU,EAAE,IAAI;MAChBU,MAAM,EAAEf,KAAK,CAACC,SAAS,CAACJ;IAC1B,CAAC,CACF;IACDqB,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFlB,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,YAAY,EAAE,QAAQ,EAAE;IACnDO,QAAQ,EAAE;MACRf,OAAO,EAAE,OAAO;MAChBU,MAAM,EAAE,IAAI;MACZE,KAAK,EAAE;IACT,CAAC;IACD,iBAAiB,EAAE;MACjBZ,OAAO,EACL,0EAA0E;MAC5EU,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACN,sBAAsB,EAAE;UACtBX,OAAO,EAAE,OAAO;UAChBY,KAAK,EAAE;QACT,CAAC;QACDI,aAAa,EAAE;UACbhB,OAAO,EACL,kEAAkE;UACpEC,UAAU,EAAE,IAAI;UAChBU,MAAM,EAAE;YACN,2BAA2B,EAAE;cAC3BX,OAAO,EAAE,WAAW;cACpBY,KAAK,EAAE;YACT,CAAC;YACDK,IAAI,EAAErB,KAAK,CAACC,SAAS,CAACJ;UACxB;QACF,CAAC;QACDyB,MAAM,EAAE;MACV;IACF,CAAC;IACD,iBAAiB,EAAE;MACjBlB,OAAO,EACL,2EAA2E;MAC7EC,UAAU,EAAE,IAAI;MAChBS,MAAM,EAAE,IAAI;MACZE,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFhB,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;IACrD,kBAAkB,EAAE;MAClBR,OAAO,EACL,mFAAmF;MACrFC,UAAU,EAAE,IAAI;MAChBW,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,IAAIhB,KAAK,CAACC,SAAS,CAACsB,MAAM,EAAE;IAC1BvB,KAAK,CAACC,SAAS,CAACsB,MAAM,CAACC,GAAG,CAACC,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC,EAAC;IAC9D;IACAzB,KAAK,CAACC,SAAS,CAACsB,MAAM,CAACC,GAAG,CAACE,YAAY,CACrC,wNAAwN,CACrNhB,MAAM,EACT,YACF,CAAC;EACH;EACAV,KAAK,CAACC,SAAS,CAAC0B,EAAE,GAAG3B,KAAK,CAACC,SAAS,CAACJ,UAAU;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}