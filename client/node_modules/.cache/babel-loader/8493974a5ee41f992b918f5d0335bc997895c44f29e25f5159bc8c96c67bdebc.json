{"ast": null, "code": "'use strict';\n\nmodule.exports = stylus;\nstylus.displayName = 'stylus';\nstylus.aliases = [];\nfunction stylus(Prism) {\n  ;\n  (function (Prism) {\n    var unit = {\n      pattern: /(\\b\\d+)(?:%|[a-z]+)/,\n      lookbehind: true\n    }; // 123 -123 .123 -.123 12.3 -12.3\n    var number = {\n      pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n      lookbehind: true\n    };\n    var inside = {\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n        lookbehind: true\n      },\n      url: {\n        pattern: /\\burl\\(([\"']?).*?\\1\\)/i,\n        greedy: true\n      },\n      string: {\n        pattern: /(\"|')(?:(?!\\1)[^\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\\1/,\n        greedy: true\n      },\n      interpolation: null,\n      // See below\n      func: null,\n      // See below\n      important: /\\B!(?:important|optional)\\b/i,\n      keyword: {\n        pattern: /(^|\\s+)(?:(?:else|for|if|return|unless)(?=\\s|$)|@[\\w-]+)/,\n        lookbehind: true\n      },\n      hexcode: /#[\\da-f]{3,6}/i,\n      color: [/\\b(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)\\b/i, {\n        pattern: /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n        inside: {\n          unit: unit,\n          number: number,\n          function: /[\\w-]+(?=\\()/,\n          punctuation: /[(),]/\n        }\n      }],\n      entity: /\\\\[\\da-f]{1,8}/i,\n      unit: unit,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: [\n      // We want non-word chars around \"-\" because it is\n      // accepted in property names.\n      /~|[+!\\/%<>?=]=?|[-:]=|\\*[*=]?|\\.{2,3}|&&|\\|\\||\\B-\\B|\\b(?:and|in|is(?: a| defined| not|nt)?|not|or)\\b/],\n      number: number,\n      punctuation: /[{}()\\[\\];:,]/\n    };\n    inside['interpolation'] = {\n      pattern: /\\{[^\\r\\n}:]+\\}/,\n      alias: 'variable',\n      inside: {\n        delimiter: {\n          pattern: /^\\{|\\}$/,\n          alias: 'punctuation'\n        },\n        rest: inside\n      }\n    };\n    inside['func'] = {\n      pattern: /[\\w-]+\\([^)]*\\).*/,\n      inside: {\n        function: /^[^(]+/,\n        rest: inside\n      }\n    };\n    Prism.languages.stylus = {\n      'atrule-declaration': {\n        pattern: /(^[ \\t]*)@.+/m,\n        lookbehind: true,\n        inside: {\n          atrule: /^@[\\w-]+/,\n          rest: inside\n        }\n      },\n      'variable-declaration': {\n        pattern: /(^[ \\t]*)[\\w$-]+\\s*.?=[ \\t]*(?:\\{[^{}]*\\}|\\S.*|$)/m,\n        lookbehind: true,\n        inside: {\n          variable: /^\\S+/,\n          rest: inside\n        }\n      },\n      statement: {\n        pattern: /(^[ \\t]*)(?:else|for|if|return|unless)[ \\t].+/m,\n        lookbehind: true,\n        inside: {\n          keyword: /^\\S+/,\n          rest: inside\n        }\n      },\n      // A property/value pair cannot end with a comma or a brace\n      // It cannot have indented content unless it ended with a semicolon\n      'property-declaration': {\n        pattern: /((?:^|\\{)([ \\t]*))(?:[\\w-]|\\{[^}\\r\\n]+\\})+(?:\\s*:\\s*|[ \\t]+)(?!\\s)[^{\\r\\n]*(?:;|[^{\\r\\n,]$(?!(?:\\r?\\n|\\r)(?:\\{|\\2[ \\t])))/m,\n        lookbehind: true,\n        inside: {\n          property: {\n            pattern: /^[^\\s:]+/,\n            inside: {\n              interpolation: inside.interpolation\n            }\n          },\n          rest: inside\n        }\n      },\n      // A selector can contain parentheses only as part of a pseudo-element\n      // It can span multiple lines.\n      // It must end with a comma or an accolade or have indented content.\n      selector: {\n        pattern: /(^[ \\t]*)(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)(?:(?:\\r?\\n|\\r)(?:\\1(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)))*(?:,$|\\{|(?=(?:\\r?\\n|\\r)(?:\\{|\\1[ \\t])))/m,\n        lookbehind: true,\n        inside: {\n          interpolation: inside.interpolation,\n          comment: inside.comment,\n          punctuation: /[{},]/\n        }\n      },\n      func: inside.func,\n      string: inside.string,\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n        lookbehind: true,\n        greedy: true\n      },\n      interpolation: inside.interpolation,\n      punctuation: /[{}()\\[\\];:.]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "stylus", "displayName", "aliases", "Prism", "unit", "pattern", "lookbehind", "number", "inside", "comment", "url", "greedy", "string", "interpolation", "func", "important", "keyword", "hexcode", "color", "function", "punctuation", "entity", "boolean", "operator", "alias", "delimiter", "rest", "languages", "at<PERSON>le", "variable", "statement", "property", "selector"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/stylus.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = stylus\nstylus.displayName = 'stylus'\nstylus.aliases = []\nfunction stylus(Prism) {\n  ;(function (Prism) {\n    var unit = {\n      pattern: /(\\b\\d+)(?:%|[a-z]+)/,\n      lookbehind: true\n    } // 123 -123 .123 -.123 12.3 -12.3\n    var number = {\n      pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n      lookbehind: true\n    }\n    var inside = {\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n        lookbehind: true\n      },\n      url: {\n        pattern: /\\burl\\(([\"']?).*?\\1\\)/i,\n        greedy: true\n      },\n      string: {\n        pattern: /(\"|')(?:(?!\\1)[^\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\\1/,\n        greedy: true\n      },\n      interpolation: null,\n      // See below\n      func: null,\n      // See below\n      important: /\\B!(?:important|optional)\\b/i,\n      keyword: {\n        pattern: /(^|\\s+)(?:(?:else|for|if|return|unless)(?=\\s|$)|@[\\w-]+)/,\n        lookbehind: true\n      },\n      hexcode: /#[\\da-f]{3,6}/i,\n      color: [\n        /\\b(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)\\b/i,\n        {\n          pattern:\n            /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n          inside: {\n            unit: unit,\n            number: number,\n            function: /[\\w-]+(?=\\()/,\n            punctuation: /[(),]/\n          }\n        }\n      ],\n      entity: /\\\\[\\da-f]{1,8}/i,\n      unit: unit,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: [\n        // We want non-word chars around \"-\" because it is\n        // accepted in property names.\n        /~|[+!\\/%<>?=]=?|[-:]=|\\*[*=]?|\\.{2,3}|&&|\\|\\||\\B-\\B|\\b(?:and|in|is(?: a| defined| not|nt)?|not|or)\\b/\n      ],\n      number: number,\n      punctuation: /[{}()\\[\\];:,]/\n    }\n    inside['interpolation'] = {\n      pattern: /\\{[^\\r\\n}:]+\\}/,\n      alias: 'variable',\n      inside: {\n        delimiter: {\n          pattern: /^\\{|\\}$/,\n          alias: 'punctuation'\n        },\n        rest: inside\n      }\n    }\n    inside['func'] = {\n      pattern: /[\\w-]+\\([^)]*\\).*/,\n      inside: {\n        function: /^[^(]+/,\n        rest: inside\n      }\n    }\n    Prism.languages.stylus = {\n      'atrule-declaration': {\n        pattern: /(^[ \\t]*)@.+/m,\n        lookbehind: true,\n        inside: {\n          atrule: /^@[\\w-]+/,\n          rest: inside\n        }\n      },\n      'variable-declaration': {\n        pattern: /(^[ \\t]*)[\\w$-]+\\s*.?=[ \\t]*(?:\\{[^{}]*\\}|\\S.*|$)/m,\n        lookbehind: true,\n        inside: {\n          variable: /^\\S+/,\n          rest: inside\n        }\n      },\n      statement: {\n        pattern: /(^[ \\t]*)(?:else|for|if|return|unless)[ \\t].+/m,\n        lookbehind: true,\n        inside: {\n          keyword: /^\\S+/,\n          rest: inside\n        }\n      },\n      // A property/value pair cannot end with a comma or a brace\n      // It cannot have indented content unless it ended with a semicolon\n      'property-declaration': {\n        pattern:\n          /((?:^|\\{)([ \\t]*))(?:[\\w-]|\\{[^}\\r\\n]+\\})+(?:\\s*:\\s*|[ \\t]+)(?!\\s)[^{\\r\\n]*(?:;|[^{\\r\\n,]$(?!(?:\\r?\\n|\\r)(?:\\{|\\2[ \\t])))/m,\n        lookbehind: true,\n        inside: {\n          property: {\n            pattern: /^[^\\s:]+/,\n            inside: {\n              interpolation: inside.interpolation\n            }\n          },\n          rest: inside\n        }\n      },\n      // A selector can contain parentheses only as part of a pseudo-element\n      // It can span multiple lines.\n      // It must end with a comma or an accolade or have indented content.\n      selector: {\n        pattern:\n          /(^[ \\t]*)(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)(?:(?:\\r?\\n|\\r)(?:\\1(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)))*(?:,$|\\{|(?=(?:\\r?\\n|\\r)(?:\\{|\\1[ \\t])))/m,\n        lookbehind: true,\n        inside: {\n          interpolation: inside.interpolation,\n          comment: inside.comment,\n          punctuation: /[{},]/\n        }\n      },\n      func: inside.func,\n      string: inside.string,\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n        lookbehind: true,\n        greedy: true\n      },\n      interpolation: inside.interpolation,\n      punctuation: /[{}()\\[\\];:.]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,IAAI,GAAG;MACTC,OAAO,EAAE,qBAAqB;MAC9BC,UAAU,EAAE;IACd,CAAC,EAAC;IACF,IAAIC,MAAM,GAAG;MACXF,OAAO,EAAE,sCAAsC;MAC/CC,UAAU,EAAE;IACd,CAAC;IACD,IAAIE,MAAM,GAAG;MACXC,OAAO,EAAE;QACPJ,OAAO,EAAE,sCAAsC;QAC/CC,UAAU,EAAE;MACd,CAAC;MACDI,GAAG,EAAE;QACHL,OAAO,EAAE,wBAAwB;QACjCM,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNP,OAAO,EAAE,+CAA+C;QACxDM,MAAM,EAAE;MACV,CAAC;MACDE,aAAa,EAAE,IAAI;MACnB;MACAC,IAAI,EAAE,IAAI;MACV;MACAC,SAAS,EAAE,8BAA8B;MACzCC,OAAO,EAAE;QACPX,OAAO,EAAE,0DAA0D;QACnEC,UAAU,EAAE;MACd,CAAC;MACDW,OAAO,EAAE,gBAAgB;MACzBC,KAAK,EAAE,CACL,o5CAAo5C,EACp5C;QACEb,OAAO,EACL,2JAA2J;QAC7JG,MAAM,EAAE;UACNJ,IAAI,EAAEA,IAAI;UACVG,MAAM,EAAEA,MAAM;UACdY,QAAQ,EAAE,cAAc;UACxBC,WAAW,EAAE;QACf;MACF,CAAC,CACF;MACDC,MAAM,EAAE,iBAAiB;MACzBjB,IAAI,EAAEA,IAAI;MACVkB,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EAAE;MACR;MACA;MACA,sGAAsG,CACvG;MACDhB,MAAM,EAAEA,MAAM;MACda,WAAW,EAAE;IACf,CAAC;IACDZ,MAAM,CAAC,eAAe,CAAC,GAAG;MACxBH,OAAO,EAAE,gBAAgB;MACzBmB,KAAK,EAAE,UAAU;MACjBhB,MAAM,EAAE;QACNiB,SAAS,EAAE;UACTpB,OAAO,EAAE,SAAS;UAClBmB,KAAK,EAAE;QACT,CAAC;QACDE,IAAI,EAAElB;MACR;IACF,CAAC;IACDA,MAAM,CAAC,MAAM,CAAC,GAAG;MACfH,OAAO,EAAE,mBAAmB;MAC5BG,MAAM,EAAE;QACNW,QAAQ,EAAE,QAAQ;QAClBO,IAAI,EAAElB;MACR;IACF,CAAC;IACDL,KAAK,CAACwB,SAAS,CAAC3B,MAAM,GAAG;MACvB,oBAAoB,EAAE;QACpBK,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNoB,MAAM,EAAE,UAAU;UAClBF,IAAI,EAAElB;QACR;MACF,CAAC;MACD,sBAAsB,EAAE;QACtBH,OAAO,EAAE,oDAAoD;QAC7DC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNqB,QAAQ,EAAE,MAAM;UAChBH,IAAI,EAAElB;QACR;MACF,CAAC;MACDsB,SAAS,EAAE;QACTzB,OAAO,EAAE,gDAAgD;QACzDC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNQ,OAAO,EAAE,MAAM;UACfU,IAAI,EAAElB;QACR;MACF,CAAC;MACD;MACA;MACA,sBAAsB,EAAE;QACtBH,OAAO,EACL,4HAA4H;QAC9HC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNuB,QAAQ,EAAE;YACR1B,OAAO,EAAE,UAAU;YACnBG,MAAM,EAAE;cACNK,aAAa,EAAEL,MAAM,CAACK;YACxB;UACF,CAAC;UACDa,IAAI,EAAElB;QACR;MACF,CAAC;MACD;MACA;MACA;MACAwB,QAAQ,EAAE;QACR3B,OAAO,EACL,uOAAuO;QACzOC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNK,aAAa,EAAEL,MAAM,CAACK,aAAa;UACnCJ,OAAO,EAAED,MAAM,CAACC,OAAO;UACvBW,WAAW,EAAE;QACf;MACF,CAAC;MACDN,IAAI,EAAEN,MAAM,CAACM,IAAI;MACjBF,MAAM,EAAEJ,MAAM,CAACI,MAAM;MACrBH,OAAO,EAAE;QACPJ,OAAO,EAAE,sCAAsC;QAC/CC,UAAU,EAAE,IAAI;QAChBK,MAAM,EAAE;MACV,CAAC;MACDE,aAAa,EAAEL,MAAM,CAACK,aAAa;MACnCO,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEjB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}