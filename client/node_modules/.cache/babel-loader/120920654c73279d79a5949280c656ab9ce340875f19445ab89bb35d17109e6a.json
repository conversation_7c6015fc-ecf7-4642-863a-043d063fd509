{"ast": null, "code": "/*\nLanguage: XQuery\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON>\nDescription: Supports XQuery 3.1 including XQuery Update 3, so also XPath (as it is a superset)\nRefactored to process xml constructor syntax and function-bodies. Added missing data-types, xpath operands, inbuilt functions, and query prologs\nWebsite: https://www.w3.org/XML/Query/\nCategory: functional\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xquery(_hljs) {\n  // see https://www.w3.org/TR/xquery/#id-terminal-delimitation\n  const KEYWORDS = 'module schema namespace boundary-space preserve no-preserve strip default collation base-uri ordering context decimal-format decimal-separator copy-namespaces empty-sequence except exponent-separator external grouping-separator inherit no-inherit lax minus-sign per-mille percent schema-attribute schema-element strict unordered zero-digit ' + 'declare import option function validate variable ' + 'for at in let where order group by return if then else ' + 'tumbling sliding window start when only end previous next stable ' + 'ascending descending allowing empty greatest least some every satisfies switch case typeswitch try catch ' + 'and or to union intersect instance of treat as castable cast map array ' + 'delete insert into replace value rename copy modify update';\n\n  // Node Types (sorted by inheritance)\n  // atomic types (sorted by inheritance)\n  const TYPE = 'item document-node node attribute document element comment namespace namespace-node processing-instruction text construction ' + 'xs:anyAtomicType xs:untypedAtomic xs:duration xs:time xs:decimal xs:float xs:double xs:gYearMonth xs:gYear xs:gMonthDay xs:gMonth xs:gDay xs:boolean xs:base64Binary xs:hexBinary xs:anyURI xs:QName xs:NOTATION xs:dateTime xs:dateTimeStamp xs:date xs:string xs:normalizedString xs:token xs:language xs:NMTOKEN xs:Name xs:NCName xs:ID xs:IDREF xs:ENTITY xs:integer xs:nonPositiveInteger xs:negativeInteger xs:long xs:int xs:short xs:byte xs:nonNegativeInteger xs:unisignedLong xs:unsignedInt xs:unsignedShort xs:unsignedByte xs:positiveInteger xs:yearMonthDuration xs:dayTimeDuration';\n  const LITERAL = 'eq ne lt le gt ge is ' + 'self:: child:: descendant:: descendant-or-self:: attribute:: following:: following-sibling:: parent:: ancestor:: ancestor-or-self:: preceding:: preceding-sibling:: ' + 'NaN';\n\n  // functions (TODO: find regex for op: without breaking build)\n  const BUILT_IN = {\n    className: 'built_in',\n    variants: [{\n      begin: /\\barray:/,\n      end: /(?:append|filter|flatten|fold-(?:left|right)|for-each(?:-pair)?|get|head|insert-before|join|put|remove|reverse|size|sort|subarray|tail)\\b/\n    }, {\n      begin: /\\bmap:/,\n      end: /(?:contains|entry|find|for-each|get|keys|merge|put|remove|size)\\b/\n    }, {\n      begin: /\\bmath:/,\n      end: /(?:a(?:cos|sin|tan[2]?)|cos|exp(?:10)?|log(?:10)?|pi|pow|sin|sqrt|tan)\\b/\n    }, {\n      begin: /\\bop:/,\n      end: /\\(/,\n      excludeEnd: true\n    }, {\n      begin: /\\bfn:/,\n      end: /\\(/,\n      excludeEnd: true\n    },\n    // do not highlight inbuilt strings as variable or xml element names\n    {\n      begin: /[^</$:'\"-]\\b(?:abs|accumulator-(?:after|before)|adjust-(?:date(?:Time)?|time)-to-timezone|analyze-string|apply|available-(?:environment-variables|system-properties)|avg|base-uri|boolean|ceiling|codepoints?-(?:equal|to-string)|collation-key|collection|compare|concat|contains(?:-token)?|copy-of|count|current(?:-)?(?:date(?:Time)?|time|group(?:ing-key)?|output-uri|merge-(?:group|key))?data|dateTime|days?-from-(?:date(?:Time)?|duration)|deep-equal|default-(?:collation|language)|distinct-values|document(?:-uri)?|doc(?:-available)?|element-(?:available|with-id)|empty|encode-for-uri|ends-with|environment-variable|error|escape-html-uri|exactly-one|exists|false|filter|floor|fold-(?:left|right)|for-each(?:-pair)?|format-(?:date(?:Time)?|time|integer|number)|function-(?:arity|available|lookup|name)|generate-id|has-children|head|hours-from-(?:dateTime|duration|time)|id(?:ref)?|implicit-timezone|in-scope-prefixes|index-of|innermost|insert-before|iri-to-uri|json-(?:doc|to-xml)|key|lang|last|load-xquery-module|local-name(?:-from-QName)?|(?:lower|upper)-case|matches|max|minutes-from-(?:dateTime|duration|time)|min|months?-from-(?:date(?:Time)?|duration)|name(?:space-uri-?(?:for-prefix|from-QName)?)?|nilled|node-name|normalize-(?:space|unicode)|not|number|one-or-more|outermost|parse-(?:ietf-date|json)|path|position|(?:prefix-from-)?QName|random-number-generator|regex-group|remove|replace|resolve-(?:QName|uri)|reverse|root|round(?:-half-to-even)?|seconds-from-(?:dateTime|duration|time)|snapshot|sort|starts-with|static-base-uri|stream-available|string-?(?:join|length|to-codepoints)?|subsequence|substring-?(?:after|before)?|sum|system-property|tail|timezone-from-(?:date(?:Time)?|time)|tokenize|trace|trans(?:form|late)|true|type-available|unordered|unparsed-(?:entity|text)?-?(?:public-id|uri|available|lines)?|uri-collection|xml-to-json|years?-from-(?:date(?:Time)?|duration)|zero-or-one)\\b/\n    }, {\n      begin: /\\blocal:/,\n      end: /\\(/,\n      excludeEnd: true\n    }, {\n      begin: /\\bzip:/,\n      end: /(?:zip-file|(?:xml|html|text|binary)-entry| (?:update-)?entries)\\b/\n    }, {\n      begin: /\\b(?:util|db|functx|app|xdmp|xmldb):/,\n      end: /\\(/,\n      excludeEnd: true\n    }]\n  };\n  const TITLE = {\n    className: 'title',\n    begin: /\\bxquery version \"[13]\\.[01]\"\\s?(?:encoding \".+\")?/,\n    end: /;/\n  };\n  const VAR = {\n    className: 'variable',\n    begin: /[$][\\w\\-:]+/\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: /(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b/,\n    relevance: 0\n  };\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: /\"/,\n      end: /\"/,\n      contains: [{\n        begin: /\"\"/,\n        relevance: 0\n      }]\n    }, {\n      begin: /'/,\n      end: /'/,\n      contains: [{\n        begin: /''/,\n        relevance: 0\n      }]\n    }]\n  };\n  const ANNOTATION = {\n    className: 'meta',\n    begin: /%[\\w\\-:]+/\n  };\n  const COMMENT = {\n    className: 'comment',\n    begin: /\\(:/,\n    end: /:\\)/,\n    relevance: 10,\n    contains: [{\n      className: 'doctag',\n      begin: /@\\w+/\n    }]\n  };\n\n  // see https://www.w3.org/TR/xquery/#id-computedConstructors\n  // mocha: computed_inbuilt\n  // see https://www.regexpal.com/?fam=99749\n  const COMPUTED = {\n    beginKeywords: 'element attribute comment document processing-instruction',\n    end: /\\{/,\n    excludeEnd: true\n  };\n\n  // mocha: direct_method\n  const DIRECT = {\n    begin: /<([\\w._:-]+)(\\s+\\S*=('|\").*('|\"))?>/,\n    end: /(\\/[\\w._:-]+>)/,\n    subLanguage: 'xml',\n    contains: [{\n      begin: /\\{/,\n      end: /\\}/,\n      subLanguage: 'xquery'\n    }, 'self']\n  };\n  const CONTAINS = [VAR, BUILT_IN, STRING, NUMBER, COMMENT, ANNOTATION, TITLE, COMPUTED, DIRECT];\n  return {\n    name: 'XQuery',\n    aliases: ['xpath', 'xq'],\n    case_insensitive: false,\n    illegal: /(proc)|(abstract)|(extends)|(until)|(#)/,\n    keywords: {\n      $pattern: /[a-zA-Z$][a-zA-Z0-9_:-]*/,\n      keyword: KEYWORDS,\n      type: TYPE,\n      literal: LITERAL\n    },\n    contains: CONTAINS\n  };\n}\nmodule.exports = xquery;", "map": {"version": 3, "names": ["xquery", "_hljs", "KEYWORDS", "TYPE", "LITERAL", "BUILT_IN", "className", "variants", "begin", "end", "excludeEnd", "TITLE", "VAR", "NUMBER", "relevance", "STRING", "contains", "ANNOTATION", "COMMENT", "COMPUTED", "beginKeywords", "DIRECT", "subLanguage", "CONTAINS", "name", "aliases", "case_insensitive", "illegal", "keywords", "$pattern", "keyword", "type", "literal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/xquery.js"], "sourcesContent": ["/*\nLanguage: XQuery\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON>\nDescription: Supports XQuery 3.1 including XQuery Update 3, so also XPath (as it is a superset)\nRefactored to process xml constructor syntax and function-bodies. Added missing data-types, xpath operands, inbuilt functions, and query prologs\nWebsite: https://www.w3.org/XML/Query/\nCategory: functional\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xquery(_hljs) {\n  // see https://www.w3.org/TR/xquery/#id-terminal-delimitation\n  const KEYWORDS =\n    'module schema namespace boundary-space preserve no-preserve strip default collation base-uri ordering context decimal-format decimal-separator copy-namespaces empty-sequence except exponent-separator external grouping-separator inherit no-inherit lax minus-sign per-mille percent schema-attribute schema-element strict unordered zero-digit ' +\n    'declare import option function validate variable ' +\n    'for at in let where order group by return if then else ' +\n    'tumbling sliding window start when only end previous next stable ' +\n    'ascending descending allowing empty greatest least some every satisfies switch case typeswitch try catch ' +\n    'and or to union intersect instance of treat as castable cast map array ' +\n    'delete insert into replace value rename copy modify update';\n\n  // Node Types (sorted by inheritance)\n  // atomic types (sorted by inheritance)\n  const TYPE =\n    'item document-node node attribute document element comment namespace namespace-node processing-instruction text construction ' +\n    'xs:anyAtomicType xs:untypedAtomic xs:duration xs:time xs:decimal xs:float xs:double xs:gYearMonth xs:gYear xs:gMonthDay xs:gMonth xs:gDay xs:boolean xs:base64Binary xs:hexBinary xs:anyURI xs:QName xs:NOTATION xs:dateTime xs:dateTimeStamp xs:date xs:string xs:normalizedString xs:token xs:language xs:NMTOKEN xs:Name xs:NCName xs:ID xs:IDREF xs:ENTITY xs:integer xs:nonPositiveInteger xs:negativeInteger xs:long xs:int xs:short xs:byte xs:nonNegativeInteger xs:unisignedLong xs:unsignedInt xs:unsignedShort xs:unsignedByte xs:positiveInteger xs:yearMonthDuration xs:dayTimeDuration';\n\n  const LITERAL =\n    'eq ne lt le gt ge is ' +\n    'self:: child:: descendant:: descendant-or-self:: attribute:: following:: following-sibling:: parent:: ancestor:: ancestor-or-self:: preceding:: preceding-sibling:: ' +\n    'NaN';\n\n  // functions (TODO: find regex for op: without breaking build)\n  const BUILT_IN = {\n    className: 'built_in',\n    variants: [\n      {\n        begin: /\\barray:/,\n        end: /(?:append|filter|flatten|fold-(?:left|right)|for-each(?:-pair)?|get|head|insert-before|join|put|remove|reverse|size|sort|subarray|tail)\\b/\n      },\n      {\n        begin: /\\bmap:/,\n        end: /(?:contains|entry|find|for-each|get|keys|merge|put|remove|size)\\b/\n      },\n      {\n        begin: /\\bmath:/,\n        end: /(?:a(?:cos|sin|tan[2]?)|cos|exp(?:10)?|log(?:10)?|pi|pow|sin|sqrt|tan)\\b/\n      },\n      {\n        begin: /\\bop:/,\n        end: /\\(/,\n        excludeEnd: true\n      },\n      {\n        begin: /\\bfn:/,\n        end: /\\(/,\n        excludeEnd: true\n      },\n      // do not highlight inbuilt strings as variable or xml element names\n      {\n        begin: /[^</$:'\"-]\\b(?:abs|accumulator-(?:after|before)|adjust-(?:date(?:Time)?|time)-to-timezone|analyze-string|apply|available-(?:environment-variables|system-properties)|avg|base-uri|boolean|ceiling|codepoints?-(?:equal|to-string)|collation-key|collection|compare|concat|contains(?:-token)?|copy-of|count|current(?:-)?(?:date(?:Time)?|time|group(?:ing-key)?|output-uri|merge-(?:group|key))?data|dateTime|days?-from-(?:date(?:Time)?|duration)|deep-equal|default-(?:collation|language)|distinct-values|document(?:-uri)?|doc(?:-available)?|element-(?:available|with-id)|empty|encode-for-uri|ends-with|environment-variable|error|escape-html-uri|exactly-one|exists|false|filter|floor|fold-(?:left|right)|for-each(?:-pair)?|format-(?:date(?:Time)?|time|integer|number)|function-(?:arity|available|lookup|name)|generate-id|has-children|head|hours-from-(?:dateTime|duration|time)|id(?:ref)?|implicit-timezone|in-scope-prefixes|index-of|innermost|insert-before|iri-to-uri|json-(?:doc|to-xml)|key|lang|last|load-xquery-module|local-name(?:-from-QName)?|(?:lower|upper)-case|matches|max|minutes-from-(?:dateTime|duration|time)|min|months?-from-(?:date(?:Time)?|duration)|name(?:space-uri-?(?:for-prefix|from-QName)?)?|nilled|node-name|normalize-(?:space|unicode)|not|number|one-or-more|outermost|parse-(?:ietf-date|json)|path|position|(?:prefix-from-)?QName|random-number-generator|regex-group|remove|replace|resolve-(?:QName|uri)|reverse|root|round(?:-half-to-even)?|seconds-from-(?:dateTime|duration|time)|snapshot|sort|starts-with|static-base-uri|stream-available|string-?(?:join|length|to-codepoints)?|subsequence|substring-?(?:after|before)?|sum|system-property|tail|timezone-from-(?:date(?:Time)?|time)|tokenize|trace|trans(?:form|late)|true|type-available|unordered|unparsed-(?:entity|text)?-?(?:public-id|uri|available|lines)?|uri-collection|xml-to-json|years?-from-(?:date(?:Time)?|duration)|zero-or-one)\\b/\n      },\n      {\n        begin: /\\blocal:/,\n        end: /\\(/,\n        excludeEnd: true\n      },\n      {\n        begin: /\\bzip:/,\n        end: /(?:zip-file|(?:xml|html|text|binary)-entry| (?:update-)?entries)\\b/\n      },\n      {\n        begin: /\\b(?:util|db|functx|app|xdmp|xmldb):/,\n        end: /\\(/,\n        excludeEnd: true\n      }\n    ]\n  };\n\n  const TITLE = {\n    className: 'title',\n    begin: /\\bxquery version \"[13]\\.[01]\"\\s?(?:encoding \".+\")?/,\n    end: /;/\n  };\n\n  const VAR = {\n    className: 'variable',\n    begin: /[$][\\w\\-:]+/\n  };\n\n  const NUMBER = {\n    className: 'number',\n    begin: /(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b/,\n    relevance: 0\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /\"/,\n        end: /\"/,\n        contains: [\n          {\n            begin: /\"\"/,\n            relevance: 0\n          }\n        ]\n      },\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [\n          {\n            begin: /''/,\n            relevance: 0\n          }\n        ]\n      }\n    ]\n  };\n\n  const ANNOTATION = {\n    className: 'meta',\n    begin: /%[\\w\\-:]+/\n  };\n\n  const COMMENT = {\n    className: 'comment',\n    begin: /\\(:/,\n    end: /:\\)/,\n    relevance: 10,\n    contains: [\n      {\n        className: 'doctag',\n        begin: /@\\w+/\n      }\n    ]\n  };\n\n  // see https://www.w3.org/TR/xquery/#id-computedConstructors\n  // mocha: computed_inbuilt\n  // see https://www.regexpal.com/?fam=99749\n  const COMPUTED = {\n    beginKeywords: 'element attribute comment document processing-instruction',\n    end: /\\{/,\n    excludeEnd: true\n  };\n\n  // mocha: direct_method\n  const DIRECT = {\n    begin: /<([\\w._:-]+)(\\s+\\S*=('|\").*('|\"))?>/,\n    end: /(\\/[\\w._:-]+>)/,\n    subLanguage: 'xml',\n    contains: [\n      {\n        begin: /\\{/,\n        end: /\\}/,\n        subLanguage: 'xquery'\n      },\n      'self'\n    ]\n  };\n\n  const CONTAINS = [\n    VAR,\n    BUILT_IN,\n    STRING,\n    NUMBER,\n    COMMENT,\n    ANNOTATION,\n    TITLE,\n    COMPUTED,\n    DIRECT\n  ];\n\n  return {\n    name: 'XQuery',\n    aliases: [\n      'xpath',\n      'xq'\n    ],\n    case_insensitive: false,\n    illegal: /(proc)|(abstract)|(extends)|(until)|(#)/,\n    keywords: {\n      $pattern: /[a-zA-Z$][a-zA-Z0-9_:-]*/,\n      keyword: KEYWORDS,\n      type: TYPE,\n      literal: LITERAL\n    },\n    contains: CONTAINS\n  };\n}\n\nmodule.exports = xquery;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,KAAK,EAAE;EACrB;EACA,MAAMC,QAAQ,GACZ,sVAAsV,GACtV,mDAAmD,GACnD,yDAAyD,GACzD,mEAAmE,GACnE,2GAA2G,GAC3G,yEAAyE,GACzE,4DAA4D;;EAE9D;EACA;EACA,MAAMC,IAAI,GACR,+HAA+H,GAC/H,skBAAskB;EAExkB,MAAMC,OAAO,GACX,uBAAuB,GACvB,sKAAsK,GACtK,KAAK;;EAEP;EACA,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,QAAQ;MACfC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,IAAI;MACTC,UAAU,EAAE;IACd,CAAC,EACD;MACEF,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,IAAI;MACTC,UAAU,EAAE;IACd,CAAC;IACD;IACA;MACEF,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE,IAAI;MACTC,UAAU,EAAE;IACd,CAAC,EACD;MACEF,KAAK,EAAE,QAAQ;MACfC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,sCAAsC;MAC7CC,GAAG,EAAE,IAAI;MACTC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC;EAED,MAAMC,KAAK,GAAG;IACZL,SAAS,EAAE,OAAO;IAClBE,KAAK,EAAE,oDAAoD;IAC3DC,GAAG,EAAE;EACP,CAAC;EAED,MAAMG,GAAG,GAAG;IACVN,SAAS,EAAE,UAAU;IACrBE,KAAK,EAAE;EACT,CAAC;EAED,MAAMK,MAAM,GAAG;IACbP,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE,sEAAsE;IAC7EM,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,MAAM,GAAG;IACbT,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRO,QAAQ,EAAE,CACR;QACER,KAAK,EAAE,IAAI;QACXM,SAAS,EAAE;MACb,CAAC;IAEL,CAAC,EACD;MACEN,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRO,QAAQ,EAAE,CACR;QACER,KAAK,EAAE,IAAI;QACXM,SAAS,EAAE;MACb,CAAC;IAEL,CAAC;EAEL,CAAC;EAED,MAAMG,UAAU,GAAG;IACjBX,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE;EACT,CAAC;EAED,MAAMU,OAAO,GAAG;IACdZ,SAAS,EAAE,SAAS;IACpBE,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,KAAK;IACVK,SAAS,EAAE,EAAE;IACbE,QAAQ,EAAE,CACR;MACEV,SAAS,EAAE,QAAQ;MACnBE,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;;EAED;EACA;EACA;EACA,MAAMW,QAAQ,GAAG;IACfC,aAAa,EAAE,2DAA2D;IAC1EX,GAAG,EAAE,IAAI;IACTC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMW,MAAM,GAAG;IACbb,KAAK,EAAE,qCAAqC;IAC5CC,GAAG,EAAE,gBAAgB;IACrBa,WAAW,EAAE,KAAK;IAClBN,QAAQ,EAAE,CACR;MACER,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTa,WAAW,EAAE;IACf,CAAC,EACD,MAAM;EAEV,CAAC;EAED,MAAMC,QAAQ,GAAG,CACfX,GAAG,EACHP,QAAQ,EACRU,MAAM,EACNF,MAAM,EACNK,OAAO,EACPD,UAAU,EACVN,KAAK,EACLQ,QAAQ,EACRE,MAAM,CACP;EAED,OAAO;IACLG,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CACP,OAAO,EACP,IAAI,CACL;IACDC,gBAAgB,EAAE,KAAK;IACvBC,OAAO,EAAE,yCAAyC;IAClDC,QAAQ,EAAE;MACRC,QAAQ,EAAE,0BAA0B;MACpCC,OAAO,EAAE5B,QAAQ;MACjB6B,IAAI,EAAE5B,IAAI;MACV6B,OAAO,EAAE5B;IACX,CAAC;IACDY,QAAQ,EAAEO;EACZ,CAAC;AACH;AAEAU,MAAM,CAACC,OAAO,GAAGlC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}