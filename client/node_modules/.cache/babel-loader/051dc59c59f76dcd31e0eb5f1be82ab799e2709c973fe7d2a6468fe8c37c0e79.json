{"ast": null, "code": "/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  var LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  var URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  var KEY = {\n    className: 'attr',\n    variants: [{\n      begin: '\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)'\n    }, {\n      begin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)'\n    },\n    // double quoted keys\n    {\n      begin: '\\'\\\\w[\\\\w :\\\\/.-]*\\':(?=[ \\t]|$)'\n    } // single quoted keys\n    ]\n  };\n  var TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [{\n      begin: /\\{\\{/,\n      end: /\\}\\}/\n    },\n    // jinja templates Ansible\n    {\n      begin: /%\\{/,\n      end: /\\}/\n    } // Ruby i18n\n    ]\n  };\n  var STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [{\n      begin: /'/,\n      end: /'/\n    }, {\n      begin: /\"/,\n      end: /\"/\n    }, {\n      begin: /\\S+/\n    }],\n    contains: [hljs.BACKSLASH_ESCAPE, TEMPLATE_VARIABLES]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  var CONTAINER_STRING = hljs.inherit(STRING, {\n    variants: [{\n      begin: /'/,\n      end: /'/\n    }, {\n      begin: /\"/,\n      end: /\"/\n    }, {\n      begin: /[^\\s,{}[\\]]+/\n    }]\n  });\n  var DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  var TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  var FRACTION_RE = '(\\\\.[0-9]*)?';\n  var ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  var TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n  var VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  var OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  var ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  var MODES = [KEY, {\n    className: 'meta',\n    begin: '^---\\\\s*$',\n    relevance: 10\n  }, {\n    // multi line string\n    // Blocks start with a | or > followed by a newline\n    //\n    // Indentation of subsequent lines must be the same to\n    // be considered part of the block\n    className: 'string',\n    begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n  }, {\n    // Ruby/Rails erb\n    begin: '<%[%=-]?',\n    end: '[%-]?%>',\n    subLanguage: 'ruby',\n    excludeBegin: true,\n    excludeEnd: true,\n    relevance: 0\n  }, {\n    // named tags\n    className: 'type',\n    begin: '!\\\\w+!' + URI_CHARACTERS\n  },\n  // https://yaml.org/spec/1.2/spec.html#id2784064\n  {\n    // verbatim tags\n    className: 'type',\n    begin: '!<' + URI_CHARACTERS + \">\"\n  }, {\n    // primary tags\n    className: 'type',\n    begin: '!' + URI_CHARACTERS\n  }, {\n    // secondary tags\n    className: 'type',\n    begin: '!!' + URI_CHARACTERS\n  }, {\n    // fragment id &ref\n    className: 'meta',\n    begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n  }, {\n    // fragment reference *ref\n    className: 'meta',\n    begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n  }, {\n    // array listing\n    className: 'bullet',\n    // TODO: remove |$ hack when we have proper look-ahead support\n    begin: '-(?=[ ]|$)',\n    relevance: 0\n  }, hljs.HASH_COMMENT_MODE, {\n    beginKeywords: LITERALS,\n    keywords: {\n      literal: LITERALS\n    }\n  }, TIMESTAMP,\n  // numbers are any valid C-style number that\n  // sit isolated from other words\n  {\n    className: 'number',\n    begin: hljs.C_NUMBER_RE + '\\\\b',\n    relevance: 0\n  }, OBJECT, ARRAY, STRING];\n  var VALUE_MODES = [...MODES];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: ['yml'],\n    contains: MODES\n  };\n}\nmodule.exports = yaml;", "map": {"version": 3, "names": ["yaml", "hljs", "LITERALS", "URI_CHARACTERS", "KEY", "className", "variants", "begin", "TEMPLATE_VARIABLES", "end", "STRING", "relevance", "contains", "BACKSLASH_ESCAPE", "CONTAINER_STRING", "inherit", "DATE_RE", "TIME_RE", "FRACTION_RE", "ZONE_RE", "TIMESTAMP", "VALUE_CONTAINER", "endsWithParent", "excludeEnd", "keywords", "OBJECT", "illegal", "ARRAY", "MODES", "subLanguage", "excludeBegin", "UNDERSCORE_IDENT_RE", "HASH_COMMENT_MODE", "beginKeywords", "literal", "C_NUMBER_RE", "VALUE_MODES", "pop", "push", "name", "case_insensitive", "aliases", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/yaml.js"], "sourcesContent": ["/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  var LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  var URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  var KEY = {\n    className: 'attr',\n    variants: [\n      { begin: '\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)' },\n      { begin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)' }, // double quoted keys\n      { begin: '\\'\\\\w[\\\\w :\\\\/.-]*\\':(?=[ \\t]|$)' } // single quoted keys\n    ]\n  };\n\n  var TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [\n      { begin: /\\{\\{/, end: /\\}\\}/ }, // jinja templates Ansible\n      { begin: /%\\{/, end: /\\}/ } // Ruby i18n\n    ]\n  };\n  var STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      { begin: /'/, end: /'/ },\n      { begin: /\"/, end: /\"/ },\n      { begin: /\\S+/ }\n    ],\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      TEMPLATE_VARIABLES\n    ]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  var CONTAINER_STRING = hljs.inherit(STRING, {\n    variants: [\n      { begin: /'/, end: /'/ },\n      { begin: /\"/, end: /\"/ },\n      { begin: /[^\\s,{}[\\]]+/ }\n    ]\n  });\n\n  var DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  var TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  var FRACTION_RE = '(\\\\.[0-9]*)?';\n  var ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  var TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n\n  var VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  var OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  var ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [VALUE_CONTAINER],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n\n  var MODES = [\n    KEY,\n    {\n      className: 'meta',\n      begin: '^---\\\\s*$',\n      relevance: 10\n    },\n    { // multi line string\n      // Blocks start with a | or > followed by a newline\n      //\n      // Indentation of subsequent lines must be the same to\n      // be considered part of the block\n      className: 'string',\n      begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n    },\n    { // Ruby/Rails erb\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    },\n    { // named tags\n      className: 'type',\n      begin: '!\\\\w+!' + URI_CHARACTERS\n    },\n    // https://yaml.org/spec/1.2/spec.html#id2784064\n    { // verbatim tags\n      className: 'type',\n      begin: '!<' + URI_CHARACTERS + \">\"\n    },\n    { // primary tags\n      className: 'type',\n      begin: '!' + URI_CHARACTERS\n    },\n    { // secondary tags\n      className: 'type',\n      begin: '!!' + URI_CHARACTERS\n    },\n    { // fragment id &ref\n      className: 'meta',\n      begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // fragment reference *ref\n      className: 'meta',\n      begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // array listing\n      className: 'bullet',\n      // TODO: remove |$ hack when we have proper look-ahead support\n      begin: '-(?=[ ]|$)',\n      relevance: 0\n    },\n    hljs.HASH_COMMENT_MODE,\n    {\n      beginKeywords: LITERALS,\n      keywords: { literal: LITERALS }\n    },\n    TIMESTAMP,\n    // numbers are any valid C-style number that\n    // sit isolated from other words\n    {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE + '\\\\b',\n      relevance: 0\n    },\n    OBJECT,\n    ARRAY,\n    STRING\n  ];\n\n  var VALUE_MODES = [...MODES];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: [ 'yml' ],\n    contains: MODES\n  };\n}\n\nmodule.exports = yaml;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,QAAQ,GAAG,wBAAwB;;EAEvC;EACA,IAAIC,cAAc,GAAG,8BAA8B;;EAEnD;EACA;EACA;EACA;EACA,IAAIC,GAAG,GAAG;IACRC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,CACR;MAAEC,KAAK,EAAE;IAA+B,CAAC,EACzC;MAAEA,KAAK,EAAE;IAAiC,CAAC;IAAE;IAC7C;MAAEA,KAAK,EAAE;IAAmC,CAAC,CAAC;IAAA;EAElD,CAAC;EAED,IAAIC,kBAAkB,GAAG;IACvBH,SAAS,EAAE,mBAAmB;IAC9BC,QAAQ,EAAE,CACR;MAAEC,KAAK,EAAE,MAAM;MAAEE,GAAG,EAAE;IAAO,CAAC;IAAE;IAChC;MAAEF,KAAK,EAAE,KAAK;MAAEE,GAAG,EAAE;IAAK,CAAC,CAAC;IAAA;EAEhC,CAAC;EACD,IAAIC,MAAM,GAAG;IACXL,SAAS,EAAE,QAAQ;IACnBM,SAAS,EAAE,CAAC;IACZL,QAAQ,EAAE,CACR;MAAEC,KAAK,EAAE,GAAG;MAAEE,GAAG,EAAE;IAAI,CAAC,EACxB;MAAEF,KAAK,EAAE,GAAG;MAAEE,GAAG,EAAE;IAAI,CAAC,EACxB;MAAEF,KAAK,EAAE;IAAM,CAAC,CACjB;IACDK,QAAQ,EAAE,CACRX,IAAI,CAACY,gBAAgB,EACrBL,kBAAkB;EAEtB,CAAC;;EAED;EACA;EACA,IAAIM,gBAAgB,GAAGb,IAAI,CAACc,OAAO,CAACL,MAAM,EAAE;IAC1CJ,QAAQ,EAAE,CACR;MAAEC,KAAK,EAAE,GAAG;MAAEE,GAAG,EAAE;IAAI,CAAC,EACxB;MAAEF,KAAK,EAAE,GAAG;MAAEE,GAAG,EAAE;IAAI,CAAC,EACxB;MAAEF,KAAK,EAAE;IAAe,CAAC;EAE7B,CAAC,CAAC;EAEF,IAAIS,OAAO,GAAG,4BAA4B;EAC1C,IAAIC,OAAO,GAAG,wCAAwC;EACtD,IAAIC,WAAW,GAAG,cAAc;EAChC,IAAIC,OAAO,GAAG,6CAA6C;EAC3D,IAAIC,SAAS,GAAG;IACdf,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE,KAAK,GAAGS,OAAO,GAAGC,OAAO,GAAGC,WAAW,GAAGC,OAAO,GAAG;EAC7D,CAAC;EAED,IAAIE,eAAe,GAAG;IACpBZ,GAAG,EAAE,GAAG;IACRa,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAEtB,QAAQ;IAClBS,SAAS,EAAE;EACb,CAAC;EACD,IAAIc,MAAM,GAAG;IACXlB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,IAAI;IACTG,QAAQ,EAAE,CAACS,eAAe,CAAC;IAC3BK,OAAO,EAAE,KAAK;IACdf,SAAS,EAAE;EACb,CAAC;EACD,IAAIgB,KAAK,GAAG;IACVpB,KAAK,EAAE,KAAK;IACZE,GAAG,EAAE,KAAK;IACVG,QAAQ,EAAE,CAACS,eAAe,CAAC;IAC3BK,OAAO,EAAE,KAAK;IACdf,SAAS,EAAE;EACb,CAAC;EAED,IAAIiB,KAAK,GAAG,CACVxB,GAAG,EACH;IACEC,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,WAAW;IAClBI,SAAS,EAAE;EACb,CAAC,EACD;IAAE;IACA;IACA;IACA;IACA;IACAN,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE;EACT,CAAC,EACD;IAAE;IACAA,KAAK,EAAE,UAAU;IACjBE,GAAG,EAAE,SAAS;IACdoB,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,IAAI;IAClBP,UAAU,EAAE,IAAI;IAChBZ,SAAS,EAAE;EACb,CAAC,EACD;IAAE;IACAN,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,QAAQ,GAAGJ;EACpB,CAAC;EACD;EACA;IAAE;IACAE,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,IAAI,GAAGJ,cAAc,GAAG;EACjC,CAAC,EACD;IAAE;IACAE,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,GAAG,GAAGJ;EACf,CAAC,EACD;IAAE;IACAE,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,IAAI,GAAGJ;EAChB,CAAC,EACD;IAAE;IACAE,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,GAAG,GAAGN,IAAI,CAAC8B,mBAAmB,GAAG;EAC1C,CAAC,EACD;IAAE;IACA1B,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,KAAK,GAAGN,IAAI,CAAC8B,mBAAmB,GAAG;EAC5C,CAAC,EACD;IAAE;IACA1B,SAAS,EAAE,QAAQ;IACnB;IACAE,KAAK,EAAE,YAAY;IACnBI,SAAS,EAAE;EACb,CAAC,EACDV,IAAI,CAAC+B,iBAAiB,EACtB;IACEC,aAAa,EAAE/B,QAAQ;IACvBsB,QAAQ,EAAE;MAAEU,OAAO,EAAEhC;IAAS;EAChC,CAAC,EACDkB,SAAS;EACT;EACA;EACA;IACEf,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAEN,IAAI,CAACkC,WAAW,GAAG,KAAK;IAC/BxB,SAAS,EAAE;EACb,CAAC,EACDc,MAAM,EACNE,KAAK,EACLjB,MAAM,CACP;EAED,IAAI0B,WAAW,GAAG,CAAC,GAAGR,KAAK,CAAC;EAC5BQ,WAAW,CAACC,GAAG,CAAC,CAAC;EACjBD,WAAW,CAACE,IAAI,CAACxB,gBAAgB,CAAC;EAClCO,eAAe,CAACT,QAAQ,GAAGwB,WAAW;EAEtC,OAAO;IACLG,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,CAAE,KAAK,CAAE;IAClB7B,QAAQ,EAAEgB;EACZ,CAAC;AACH;AAEAc,MAAM,CAACC,OAAO,GAAG3C,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}