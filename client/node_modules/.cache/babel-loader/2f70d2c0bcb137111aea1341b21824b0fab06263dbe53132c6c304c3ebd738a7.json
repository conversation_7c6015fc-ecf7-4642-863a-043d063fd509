{"ast": null, "code": "/*\nLanguage: AutoHotkey\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: AutoHotkey language definition\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction autohotkey(hljs) {\n  const BACKTICK_ESCAPE = {\n    begin: '`[\\\\s\\\\S]'\n  };\n  return {\n    name: 'AutoHotkey',\n    case_insensitive: true,\n    aliases: ['ahk'],\n    keywords: {\n      keyword: 'Break Continue Critical Exit ExitApp Gosub Goto New OnExit Pause return SetBatchLines SetTimer Suspend Thread Throw Until ahk_id ahk_class ahk_pid ahk_exe ahk_group',\n      literal: 'true false NOT AND OR',\n      built_in: 'ComSpec Clipboard ClipboardAll ErrorLevel'\n    },\n    contains: [BACKTICK_ESCAPE, hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      contains: [BACKTICK_ESCAPE]\n    }), hljs.COMMENT(';', '$', {\n      relevance: 0\n    }), hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'number',\n      begin: hljs.NUMBER_RE,\n      relevance: 0\n    }, {\n      // subst would be the most accurate however fails the point of\n      // highlighting. variable is comparably the most accurate that actually\n      // has some effect\n      className: 'variable',\n      begin: '%[a-zA-Z0-9#_$@]+%'\n    }, {\n      className: 'built_in',\n      begin: '^\\\\s*\\\\w+\\\\s*(,|%)'\n      // I don't really know if this is totally relevant\n    }, {\n      // symbol would be most accurate however is highlighted just like\n      // built_in and that makes up a lot of AutoHotkey code meaning that it\n      // would fail to highlight anything\n      className: 'title',\n      variants: [{\n        begin: '^[^\\\\n\";]+::(?!=)'\n      }, {\n        begin: '^[^\\\\n\";]+:(?!=)',\n        // zero relevance as it catches a lot of things\n        // followed by a single ':' in many languages\n        relevance: 0\n      }]\n    }, {\n      className: 'meta',\n      begin: '^\\\\s*#\\\\w+',\n      end: '$',\n      relevance: 0\n    }, {\n      className: 'built_in',\n      begin: 'A_[a-zA-Z0-9]+'\n    }, {\n      // consecutive commas, not for highlighting but just for relevance\n      begin: ',\\\\s*,'\n    }]\n  };\n}\nmodule.exports = autohotkey;", "map": {"version": 3, "names": ["autohotkey", "hljs", "BACKTICK_ESCAPE", "begin", "name", "case_insensitive", "aliases", "keywords", "keyword", "literal", "built_in", "contains", "inherit", "QUOTE_STRING_MODE", "COMMENT", "relevance", "C_BLOCK_COMMENT_MODE", "className", "NUMBER_RE", "variants", "end", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/autohotkey.js"], "sourcesContent": ["/*\nLanguage: AutoHotkey\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: AutoHotkey language definition\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction autohotkey(hljs) {\n  const BACKTICK_ESCAPE = {\n    begin: '`[\\\\s\\\\S]'\n  };\n\n  return {\n    name: 'AutoHotkey',\n    case_insensitive: true,\n    aliases: ['ahk'],\n    keywords: {\n      keyword: 'Break Continue Critical Exit ExitApp Gosub Goto New OnExit Pause return SetBatchLines SetTimer Suspend Thread Throw Until ahk_id ahk_class ahk_pid ahk_exe ahk_group',\n      literal: 'true false NOT AND OR',\n      built_in: 'ComSpec Clipboard ClipboardAll ErrorLevel'\n    },\n    contains: [\n      BACKTICK_ESCAPE,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        contains: [BACKTICK_ESCAPE]\n      }),\n      hljs.COMMENT(';', '$', {\n        relevance: 0\n      }),\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'number',\n        begin: hljs.NUMBER_RE,\n        relevance: 0\n      },\n      {\n        // subst would be the most accurate however fails the point of\n        // highlighting. variable is comparably the most accurate that actually\n        // has some effect\n        className: 'variable',\n        begin: '%[a-zA-Z0-9#_$@]+%'\n      },\n      {\n        className: 'built_in',\n        begin: '^\\\\s*\\\\w+\\\\s*(,|%)'\n        // I don't really know if this is totally relevant\n      },\n      {\n        // symbol would be most accurate however is highlighted just like\n        // built_in and that makes up a lot of AutoHotkey code meaning that it\n        // would fail to highlight anything\n        className: 'title',\n        variants: [\n          {\n            begin: '^[^\\\\n\";]+::(?!=)'\n          },\n          {\n            begin: '^[^\\\\n\";]+:(?!=)',\n            // zero relevance as it catches a lot of things\n            // followed by a single ':' in many languages\n            relevance: 0\n          }\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '^\\\\s*#\\\\w+',\n        end: '$',\n        relevance: 0\n      },\n      {\n        className: 'built_in',\n        begin: 'A_[a-zA-Z0-9]+'\n      },\n      {\n        // consecutive commas, not for highlighting but just for relevance\n        begin: ',\\\\s*,'\n      }\n    ]\n  };\n}\n\nmodule.exports = autohotkey;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,MAAMC,eAAe,GAAG;IACtBC,KAAK,EAAE;EACT,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,YAAY;IAClBC,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,QAAQ,EAAE;MACRC,OAAO,EAAE,sKAAsK;MAC/KC,OAAO,EAAE,uBAAuB;MAChCC,QAAQ,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE,CACRT,eAAe,EACfD,IAAI,CAACW,OAAO,CAACX,IAAI,CAACY,iBAAiB,EAAE;MACnCF,QAAQ,EAAE,CAACT,eAAe;IAC5B,CAAC,CAAC,EACFD,IAAI,CAACa,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;MACrBC,SAAS,EAAE;IACb,CAAC,CAAC,EACFd,IAAI,CAACe,oBAAoB,EACzB;MACEC,SAAS,EAAE,QAAQ;MACnBd,KAAK,EAAEF,IAAI,CAACiB,SAAS;MACrBH,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACA;MACA;MACAE,SAAS,EAAE,UAAU;MACrBd,KAAK,EAAE;IACT,CAAC,EACD;MACEc,SAAS,EAAE,UAAU;MACrBd,KAAK,EAAE;MACP;IACF,CAAC,EACD;MACE;MACA;MACA;MACAc,SAAS,EAAE,OAAO;MAClBE,QAAQ,EAAE,CACR;QACEhB,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE,kBAAkB;QACzB;QACA;QACAY,SAAS,EAAE;MACb,CAAC;IAEL,CAAC,EACD;MACEE,SAAS,EAAE,MAAM;MACjBd,KAAK,EAAE,YAAY;MACnBiB,GAAG,EAAE,GAAG;MACRL,SAAS,EAAE;IACb,CAAC,EACD;MACEE,SAAS,EAAE,UAAU;MACrBd,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAkB,MAAM,CAACC,OAAO,GAAGtB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}