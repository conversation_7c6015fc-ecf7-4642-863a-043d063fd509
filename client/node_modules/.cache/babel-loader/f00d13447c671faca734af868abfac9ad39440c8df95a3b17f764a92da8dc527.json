{"ast": null, "code": "/*\nLanguage: Inform 7\nAuthor: <PERSON> <<EMAIL>>\nDescription: Language definition for Inform 7, a DSL for writing parser interactive fiction.\nWebsite: http://inform7.com\n*/\n\nfunction inform7(hljs) {\n  const START_BRACKET = '\\\\[';\n  const END_BRACKET = '\\\\]';\n  return {\n    name: 'Inform 7',\n    aliases: ['i7'],\n    case_insensitive: true,\n    keywords: {\n      // Some keywords more or less unique to I7, for relevance.\n      keyword:\n      // kind:\n      'thing room person man woman animal container ' + 'supporter backdrop door ' +\n      // characteristic:\n      'scenery open closed locked inside gender ' +\n      // verb:\n      'is are say understand ' +\n      // misc keyword:\n      'kind of rule'\n    },\n    contains: [{\n      className: 'string',\n      begin: '\"',\n      end: '\"',\n      relevance: 0,\n      contains: [{\n        className: 'subst',\n        begin: START_BRACKET,\n        end: END_BRACKET\n      }]\n    }, {\n      className: 'section',\n      begin: /^(Volume|Book|Part|Chapter|Section|Table)\\b/,\n      end: '$'\n    }, {\n      // Rule definition\n      // This is here for relevance.\n      begin: /^(Check|Carry out|Report|Instead of|To|Rule|When|Before|After)\\b/,\n      end: ':',\n      contains: [{\n        // Rule name\n        begin: '\\\\(This',\n        end: '\\\\)'\n      }]\n    }, {\n      className: 'comment',\n      begin: START_BRACKET,\n      end: END_BRACKET,\n      contains: ['self']\n    }]\n  };\n}\nmodule.exports = inform7;", "map": {"version": 3, "names": ["inform7", "hljs", "START_BRACKET", "END_BRACKET", "name", "aliases", "case_insensitive", "keywords", "keyword", "contains", "className", "begin", "end", "relevance", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/inform7.js"], "sourcesContent": ["/*\nLanguage: Inform 7\nAuthor: <PERSON> <<EMAIL>>\nDescription: Language definition for Inform 7, a DSL for writing parser interactive fiction.\nWebsite: http://inform7.com\n*/\n\nfunction inform7(hljs) {\n  const START_BRACKET = '\\\\[';\n  const END_BRACKET = '\\\\]';\n  return {\n    name: 'Inform 7',\n    aliases: ['i7'],\n    case_insensitive: true,\n    keywords: {\n      // Some keywords more or less unique to I7, for relevance.\n      keyword:\n        // kind:\n        'thing room person man woman animal container ' +\n        'supporter backdrop door ' +\n        // characteristic:\n        'scenery open closed locked inside gender ' +\n        // verb:\n        'is are say understand ' +\n        // misc keyword:\n        'kind of rule'\n    },\n    contains: [\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        relevance: 0,\n        contains: [\n          {\n            className: 'subst',\n            begin: START_BRACKET,\n            end: END_BRACKET\n          }\n        ]\n      },\n      {\n        className: 'section',\n        begin: /^(Volume|Book|Part|Chapter|Section|Table)\\b/,\n        end: '$'\n      },\n      {\n        // Rule definition\n        // This is here for relevance.\n        begin: /^(Check|Carry out|Report|Instead of|To|Rule|When|Before|After)\\b/,\n        end: ':',\n        contains: [\n          {\n            // Rule name\n            begin: '\\\\(This',\n            end: '\\\\)'\n          }\n        ]\n      },\n      {\n        className: 'comment',\n        begin: START_BRACKET,\n        end: END_BRACKET,\n        contains: ['self']\n      }\n    ]\n  };\n}\n\nmodule.exports = inform7;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,aAAa,GAAG,KAAK;EAC3B,MAAMC,WAAW,GAAG,KAAK;EACzB,OAAO;IACLC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CAAC,IAAI,CAAC;IACfC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACR;MACAC,OAAO;MACL;MACA,+CAA+C,GAC/C,0BAA0B;MAC1B;MACA,2CAA2C;MAC3C;MACA,wBAAwB;MACxB;MACA;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE,CAAC;MACZJ,QAAQ,EAAE,CACR;QACEC,SAAS,EAAE,OAAO;QAClBC,KAAK,EAAET,aAAa;QACpBU,GAAG,EAAET;MACP,CAAC;IAEL,CAAC,EACD;MACEO,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,6CAA6C;MACpDC,GAAG,EAAE;IACP,CAAC,EACD;MACE;MACA;MACAD,KAAK,EAAE,kEAAkE;MACzEC,GAAG,EAAE,GAAG;MACRH,QAAQ,EAAE,CACR;QACE;QACAE,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACD;MACEF,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAET,aAAa;MACpBU,GAAG,EAAET,WAAW;MAChBM,QAAQ,EAAE,CAAC,MAAM;IACnB,CAAC;EAEL,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGf,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}