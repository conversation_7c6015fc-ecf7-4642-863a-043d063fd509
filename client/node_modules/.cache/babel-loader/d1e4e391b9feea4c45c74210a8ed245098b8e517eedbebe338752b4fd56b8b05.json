{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = tt2;\ntt2.displayName = 'tt2';\ntt2.aliases = [];\nfunction tt2(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    Prism.languages.tt2 = Prism.languages.extend('clike', {\n      comment: /#.*|\\[%#[\\s\\S]*?%\\]/,\n      keyword: /\\b(?:BLOCK|CALL|CASE|CATCH|CLEAR|DEBUG|DEFAULT|ELSE|ELSIF|END|FILTER|FINAL|FOREACH|GET|IF|IN|INCLUDE|INSERT|LAST|MACRO|META|NEXT|PERL|PROCESS|RAWPERL|RETURN|SET|STOP|SWITCH|TAGS|THROW|TRY|UNLESS|USE|WHILE|WRAPPER)\\b/,\n      punctuation: /[[\\]{},()]/\n    });\n    Prism.languages.insertBefore('tt2', 'number', {\n      operator: /=[>=]?|!=?|<=?|>=?|&&|\\|\\|?|\\b(?:and|not|or)\\b/,\n      variable: {\n        pattern: /\\b[a-z]\\w*(?:\\s*\\.\\s*(?:\\d+|\\$?[a-z]\\w*))*\\b/i\n      }\n    });\n    Prism.languages.insertBefore('tt2', 'keyword', {\n      delimiter: {\n        pattern: /^(?:\\[%|%%)-?|-?%\\]$/,\n        alias: 'punctuation'\n      }\n    });\n    Prism.languages.insertBefore('tt2', 'string', {\n      'single-quoted-string': {\n        pattern: /'[^\\\\']*(?:\\\\[\\s\\S][^\\\\']*)*'/,\n        greedy: true,\n        alias: 'string'\n      },\n      'double-quoted-string': {\n        pattern: /\"[^\\\\\"]*(?:\\\\[\\s\\S][^\\\\\"]*)*\"/,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          variable: {\n            pattern: /\\$(?:[a-z]\\w*(?:\\.(?:\\d+|\\$?[a-z]\\w*))*)/i\n          }\n        }\n      }\n    }); // The different types of TT2 strings \"replace\" the C-like standard string\n    delete Prism.languages.tt2.string;\n    Prism.hooks.add('before-tokenize', function (env) {\n      var tt2Pattern = /\\[%[\\s\\S]+?%\\]/g;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'tt2', tt2Pattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'tt2');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "tt2", "displayName", "aliases", "Prism", "register", "languages", "extend", "comment", "keyword", "punctuation", "insertBefore", "operator", "variable", "pattern", "delimiter", "alias", "greedy", "inside", "string", "hooks", "add", "env", "tt2Pattern", "buildPlaceholders", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/tt2.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = tt2\ntt2.displayName = 'tt2'\ntt2.aliases = []\nfunction tt2(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.tt2 = Prism.languages.extend('clike', {\n      comment: /#.*|\\[%#[\\s\\S]*?%\\]/,\n      keyword:\n        /\\b(?:BLOCK|CALL|CASE|CATCH|CLEAR|DEBUG|DEFAULT|ELSE|ELSIF|END|FILTER|FINAL|FOREACH|GET|IF|IN|INCLUDE|INSERT|LAST|MACRO|META|NEXT|PERL|PROCESS|RAWPERL|RETURN|SET|STOP|SWITCH|TAGS|THROW|TRY|UNLESS|USE|WHILE|WRAPPER)\\b/,\n      punctuation: /[[\\]{},()]/\n    })\n    Prism.languages.insertBefore('tt2', 'number', {\n      operator: /=[>=]?|!=?|<=?|>=?|&&|\\|\\|?|\\b(?:and|not|or)\\b/,\n      variable: {\n        pattern: /\\b[a-z]\\w*(?:\\s*\\.\\s*(?:\\d+|\\$?[a-z]\\w*))*\\b/i\n      }\n    })\n    Prism.languages.insertBefore('tt2', 'keyword', {\n      delimiter: {\n        pattern: /^(?:\\[%|%%)-?|-?%\\]$/,\n        alias: 'punctuation'\n      }\n    })\n    Prism.languages.insertBefore('tt2', 'string', {\n      'single-quoted-string': {\n        pattern: /'[^\\\\']*(?:\\\\[\\s\\S][^\\\\']*)*'/,\n        greedy: true,\n        alias: 'string'\n      },\n      'double-quoted-string': {\n        pattern: /\"[^\\\\\"]*(?:\\\\[\\s\\S][^\\\\\"]*)*\"/,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          variable: {\n            pattern: /\\$(?:[a-z]\\w*(?:\\.(?:\\d+|\\$?[a-z]\\w*))*)/i\n          }\n        }\n      }\n    }) // The different types of TT2 strings \"replace\" the C-like standard string\n    delete Prism.languages.tt2.string\n    Prism.hooks.add('before-tokenize', function (env) {\n      var tt2Pattern = /\\[%[\\s\\S]+?%\\]/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'tt2',\n        tt2Pattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'tt2')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACxC,CAAC,UAAUO,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,GAAG,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MACpDC,OAAO,EAAE,qBAAqB;MAC9BC,OAAO,EACL,yNAAyN;MAC3NC,WAAW,EAAE;IACf,CAAC,CAAC;IACFN,KAAK,CAACE,SAAS,CAACK,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE;MAC5CC,QAAQ,EAAE,gDAAgD;MAC1DC,QAAQ,EAAE;QACRC,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACFV,KAAK,CAACE,SAAS,CAACK,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE;MAC7CI,SAAS,EAAE;QACTD,OAAO,EAAE,sBAAsB;QAC/BE,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFZ,KAAK,CAACE,SAAS,CAACK,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE;MAC5C,sBAAsB,EAAE;QACtBG,OAAO,EAAE,+BAA+B;QACxCG,MAAM,EAAE,IAAI;QACZD,KAAK,EAAE;MACT,CAAC;MACD,sBAAsB,EAAE;QACtBF,OAAO,EAAE,+BAA+B;QACxCG,MAAM,EAAE,IAAI;QACZD,KAAK,EAAE,QAAQ;QACfE,MAAM,EAAE;UACNL,QAAQ,EAAE;YACRC,OAAO,EAAE;UACX;QACF;MACF;IACF,CAAC,CAAC,EAAC;IACH,OAAOV,KAAK,CAACE,SAAS,CAACL,GAAG,CAACkB,MAAM;IACjCf,KAAK,CAACgB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIC,UAAU,GAAG,iBAAiB;MAClCnB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACkB,iBAAiB,CACpDF,GAAG,EACH,KAAK,EACLC,UACF,CAAC;IACH,CAAC,CAAC;IACFnB,KAAK,CAACgB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/ClB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACmB,oBAAoB,CAACH,GAAG,EAAE,KAAK,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,EAAElB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}