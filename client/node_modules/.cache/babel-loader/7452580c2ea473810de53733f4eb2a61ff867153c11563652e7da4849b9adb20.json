{"ast": null, "code": "'use strict';\n\nmodule.exports = moonscript;\nmoonscript.displayName = 'moonscript';\nmoonscript.aliases = ['moon'];\nfunction moonscript(Prism) {\n  Prism.languages.moonscript = {\n    comment: /--.*/,\n    string: [{\n      pattern: /'[^']*'|\\[(=*)\\[[\\s\\S]*?\\]\\1\\]/,\n      greedy: true\n    }, {\n      pattern: /\"[^\"]*\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /#\\{[^{}]*\\}/,\n          inside: {\n            moonscript: {\n              pattern: /(^#\\{)[\\s\\S]+(?=\\})/,\n              lookbehind: true,\n              inside: null // see beow\n            },\n            'interpolation-punctuation': {\n              pattern: /#\\{|\\}/,\n              alias: 'punctuation'\n            }\n          }\n        }\n      }\n    }],\n    'class-name': [{\n      pattern: /(\\b(?:class|extends)[ \\t]+)\\w+/,\n      lookbehind: true\n    },\n    // class-like names start with a capital letter\n    /\\b[A-Z]\\w*/],\n    keyword: /\\b(?:class|continue|do|else|elseif|export|extends|for|from|if|import|in|local|nil|return|self|super|switch|then|unless|using|when|while|with)\\b/,\n    variable: /@@?\\w*/,\n    property: {\n      pattern: /\\b(?!\\d)\\w+(?=:)|(:)(?!\\d)\\w+/,\n      lookbehind: true\n    },\n    function: {\n      pattern: /\\b(?:_G|_VERSION|assert|collectgarbage|coroutine\\.(?:create|resume|running|status|wrap|yield)|debug\\.(?:debug|getfenv|gethook|getinfo|getlocal|getmetatable|getregistry|getupvalue|setfenv|sethook|setlocal|setmetatable|setupvalue|traceback)|dofile|error|getfenv|getmetatable|io\\.(?:close|flush|input|lines|open|output|popen|read|stderr|stdin|stdout|tmpfile|type|write)|ipairs|load|loadfile|loadstring|math\\.(?:abs|acos|asin|atan|atan2|ceil|cos|cosh|deg|exp|floor|fmod|frexp|ldexp|log|log10|max|min|modf|pi|pow|rad|random|randomseed|sin|sinh|sqrt|tan|tanh)|module|next|os\\.(?:clock|date|difftime|execute|exit|getenv|remove|rename|setlocale|time|tmpname)|package\\.(?:cpath|loaded|loadlib|path|preload|seeall)|pairs|pcall|print|rawequal|rawget|rawset|require|select|setfenv|setmetatable|string\\.(?:byte|char|dump|find|format|gmatch|gsub|len|lower|match|rep|reverse|sub|upper)|table\\.(?:concat|insert|maxn|remove|sort)|tonumber|tostring|type|unpack|xpcall)\\b/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    number: /(?:\\B\\.\\d+|\\b\\d+\\.\\d+|\\b\\d+(?=[eE]))(?:[eE][-+]?\\d+)?\\b|\\b(?:0x[a-fA-F\\d]+|\\d+)(?:U?LL)?\\b/,\n    operator: /\\.{3}|[-=]>|~=|(?:[-+*/%<>!=]|\\.\\.)=?|[:#^]|\\b(?:and|or)\\b=?|\\b(?:not)\\b/,\n    punctuation: /[.,()[\\]{}\\\\]/\n  };\n  Prism.languages.moonscript.string[1].inside.interpolation.inside.moonscript.inside = Prism.languages.moonscript;\n  Prism.languages.moon = Prism.languages.moonscript;\n}", "map": {"version": 3, "names": ["module", "exports", "moonscript", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "inside", "interpolation", "lookbehind", "alias", "keyword", "variable", "property", "function", "punctuation", "boolean", "number", "operator", "moon"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/moonscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = moonscript\nmoonscript.displayName = 'moonscript'\nmoonscript.aliases = ['moon']\nfunction moonscript(Prism) {\n  Prism.languages.moonscript = {\n    comment: /--.*/,\n    string: [\n      {\n        pattern: /'[^']*'|\\[(=*)\\[[\\s\\S]*?\\]\\1\\]/,\n        greedy: true\n      },\n      {\n        pattern: /\"[^\"]*\"/,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /#\\{[^{}]*\\}/,\n            inside: {\n              moonscript: {\n                pattern: /(^#\\{)[\\s\\S]+(?=\\})/,\n                lookbehind: true,\n                inside: null // see beow\n              },\n              'interpolation-punctuation': {\n                pattern: /#\\{|\\}/,\n                alias: 'punctuation'\n              }\n            }\n          }\n        }\n      }\n    ],\n    'class-name': [\n      {\n        pattern: /(\\b(?:class|extends)[ \\t]+)\\w+/,\n        lookbehind: true\n      }, // class-like names start with a capital letter\n      /\\b[A-Z]\\w*/\n    ],\n    keyword:\n      /\\b(?:class|continue|do|else|elseif|export|extends|for|from|if|import|in|local|nil|return|self|super|switch|then|unless|using|when|while|with)\\b/,\n    variable: /@@?\\w*/,\n    property: {\n      pattern: /\\b(?!\\d)\\w+(?=:)|(:)(?!\\d)\\w+/,\n      lookbehind: true\n    },\n    function: {\n      pattern:\n        /\\b(?:_G|_VERSION|assert|collectgarbage|coroutine\\.(?:create|resume|running|status|wrap|yield)|debug\\.(?:debug|getfenv|gethook|getinfo|getlocal|getmetatable|getregistry|getupvalue|setfenv|sethook|setlocal|setmetatable|setupvalue|traceback)|dofile|error|getfenv|getmetatable|io\\.(?:close|flush|input|lines|open|output|popen|read|stderr|stdin|stdout|tmpfile|type|write)|ipairs|load|loadfile|loadstring|math\\.(?:abs|acos|asin|atan|atan2|ceil|cos|cosh|deg|exp|floor|fmod|frexp|ldexp|log|log10|max|min|modf|pi|pow|rad|random|randomseed|sin|sinh|sqrt|tan|tanh)|module|next|os\\.(?:clock|date|difftime|execute|exit|getenv|remove|rename|setlocale|time|tmpname)|package\\.(?:cpath|loaded|loadlib|path|preload|seeall)|pairs|pcall|print|rawequal|rawget|rawset|require|select|setfenv|setmetatable|string\\.(?:byte|char|dump|find|format|gmatch|gsub|len|lower|match|rep|reverse|sub|upper)|table\\.(?:concat|insert|maxn|remove|sort)|tonumber|tostring|type|unpack|xpcall)\\b/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    number:\n      /(?:\\B\\.\\d+|\\b\\d+\\.\\d+|\\b\\d+(?=[eE]))(?:[eE][-+]?\\d+)?\\b|\\b(?:0x[a-fA-F\\d]+|\\d+)(?:U?LL)?\\b/,\n    operator:\n      /\\.{3}|[-=]>|~=|(?:[-+*/%<>!=]|\\.\\.)=?|[:#^]|\\b(?:and|or)\\b=?|\\b(?:not)\\b/,\n    punctuation: /[.,()[\\]{}\\\\]/\n  }\n  Prism.languages.moonscript.string[1].inside.interpolation.inside.moonscript.inside =\n    Prism.languages.moonscript\n  Prism.languages.moon = Prism.languages.moonscript\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,CAAC,MAAM,CAAC;AAC7B,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAG;IAC3BK,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,CACN;MACEC,OAAO,EAAE,gCAAgC;MACzCC,MAAM,EAAE;IACV,CAAC,EACD;MACED,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNC,aAAa,EAAE;UACbH,OAAO,EAAE,aAAa;UACtBE,MAAM,EAAE;YACNT,UAAU,EAAE;cACVO,OAAO,EAAE,qBAAqB;cAC9BI,UAAU,EAAE,IAAI;cAChBF,MAAM,EAAE,IAAI,CAAC;YACf,CAAC;YACD,2BAA2B,EAAE;cAC3BF,OAAO,EAAE,QAAQ;cACjBK,KAAK,EAAE;YACT;UACF;QACF;MACF;IACF,CAAC,CACF;IACD,YAAY,EAAE,CACZ;MACEL,OAAO,EAAE,gCAAgC;MACzCI,UAAU,EAAE;IACd,CAAC;IAAE;IACH,YAAY,CACb;IACDE,OAAO,EACL,iJAAiJ;IACnJC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE;MACRR,OAAO,EAAE,+BAA+B;MACxCI,UAAU,EAAE;IACd,CAAC;IACDK,QAAQ,EAAE;MACRT,OAAO,EACL,07BAA07B;MAC57BE,MAAM,EAAE;QACNQ,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EACJ,4FAA4F;IAC9FC,QAAQ,EACN,0EAA0E;IAC5EH,WAAW,EAAE;EACf,CAAC;EACDd,KAAK,CAACC,SAAS,CAACJ,UAAU,CAACM,MAAM,CAAC,CAAC,CAAC,CAACG,MAAM,CAACC,aAAa,CAACD,MAAM,CAACT,UAAU,CAACS,MAAM,GAChFN,KAAK,CAACC,SAAS,CAACJ,UAAU;EAC5BG,KAAK,CAACC,SAAS,CAACiB,IAAI,GAAGlB,KAAK,CAACC,SAAS,CAACJ,UAAU;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}