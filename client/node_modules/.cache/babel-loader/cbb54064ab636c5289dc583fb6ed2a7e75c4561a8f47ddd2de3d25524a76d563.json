{"ast": null, "code": "export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"#e3eaf2\",\n    \"background\": \"none\",\n    \"fontFamily\": \"Consolas, Monaco, \\\"Andale Mono\\\", \\\"Ubuntu Mono\\\", monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"#e3eaf2\",\n    \"background\": \"#111b27\",\n    \"fontFamily\": \"Consolas, Monaco, \\\"Andale Mono\\\", \\\"Ubuntu Mono\\\", monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \"0.5em 0\",\n    \"overflow\": \"auto\"\n  },\n  \"pre[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"pre[class*=\\\"language-\\\"]::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"]::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"] ::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#111b27\",\n    \"padding\": \"0.1em 0.3em\",\n    \"borderRadius\": \"0.3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"prolog\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"doctype\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"cdata\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"punctuation\": {\n    \"color\": \"#e3eaf2\"\n  },\n  \"delimiter.important\": {\n    \"color\": \"#66cccc\",\n    \"fontWeight\": \"inherit\"\n  },\n  \"selector.parent\": {\n    \"color\": \"#66cccc\"\n  },\n  \"tag\": {\n    \"color\": \"#66cccc\"\n  },\n  \"tag.punctuation\": {\n    \"color\": \"#66cccc\"\n  },\n  \"attr-name\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"boolean\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"boolean.important\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"number\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"constant\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"selector.attribute\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"class-name\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"key\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"parameter\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"property\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"property-access\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"variable\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"attr-value\": {\n    \"color\": \"#91d076\"\n  },\n  \"inserted\": {\n    \"color\": \"#91d076\"\n  },\n  \"color\": {\n    \"color\": \"#91d076\"\n  },\n  \"selector.value\": {\n    \"color\": \"#91d076\"\n  },\n  \"string\": {\n    \"color\": \"#91d076\"\n  },\n  \"string.url-link\": {\n    \"color\": \"#91d076\"\n  },\n  \"builtin\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"keyword-array\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"package\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"regex\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"function\": {\n    \"color\": \"#c699e3\"\n  },\n  \"selector.class\": {\n    \"color\": \"#c699e3\"\n  },\n  \"selector.id\": {\n    \"color\": \"#c699e3\"\n  },\n  \"atrule.rule\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"combinator\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"keyword\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"operator\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"pseudo-class\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"pseudo-element\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"selector\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"unit\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"deleted\": {\n    \"color\": \"#cd6660\"\n  },\n  \"important\": {\n    \"color\": \"#cd6660\",\n    \"fontWeight\": \"bold\"\n  },\n  \"keyword-this\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \"this\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  },\n  \"entity\": {\n    \"cursor\": \"help\"\n  },\n  \".language-markdown .token.title\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \".language-markdown .token.title .token.punctuation\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \".language-markdown .token.blockquote.punctuation\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".language-markdown .token.code\": {\n    \"color\": \"#66cccc\"\n  },\n  \".language-markdown .token.hr.punctuation\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".language-markdown .token.url .token.content\": {\n    \"color\": \"#91d076\"\n  },\n  \".language-markdown .token.url-link\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".language-markdown .token.list.punctuation\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".language-markdown .token.table-header\": {\n    \"color\": \"#e3eaf2\"\n  },\n  \".language-json .token.operator\": {\n    \"color\": \"#e3eaf2\"\n  },\n  \".language-scss .token.variable\": {\n    \"color\": \"#66cccc\"\n  },\n  \"token.tab:not(:empty):before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"token.cr:before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"token.lf:before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"token.space:before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#8da1b9\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#8da1b9\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#8da1b9\"\n  },\n  \".line-highlight.line-highlight\": {\n    \"background\": \"linear-gradient(to right, #3c526d5f 70%, #3c526d55)\"\n  },\n  \".line-highlight.line-highlight:before\": {\n    \"backgroundColor\": \"#8da1b9\",\n    \"color\": \"#111b27\",\n    \"boxShadow\": \"0 1px #3c526d\"\n  },\n  \".line-highlight.line-highlight[data-end]:after\": {\n    \"backgroundColor\": \"#8da1b9\",\n    \"color\": \"#111b27\",\n    \"boxShadow\": \"0 1px #3c526d\"\n  },\n  \"pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before\": {\n    \"backgroundColor\": \"#8da1b918\"\n  },\n  \".line-numbers.line-numbers .line-numbers-rows\": {\n    \"borderRight\": \"1px solid #0b121b\",\n    \"background\": \"#0b121b7a\"\n  },\n  \".line-numbers .line-numbers-rows > span:before\": {\n    \"color\": \"#8da1b9da\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-1\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-5\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-9\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-2\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-6\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-10\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-3\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-7\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-11\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-4\": {\n    \"color\": \"#c699e3\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-8\": {\n    \"color\": \"#c699e3\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-12\": {\n    \"color\": \"#c699e3\"\n  },\n  \"pre.diff-highlight > code .token.token.deleted:not(.prefix)\": {\n    \"backgroundColor\": \"#cd66601f\"\n  },\n  \"pre > code.diff-highlight .token.token.deleted:not(.prefix)\": {\n    \"backgroundColor\": \"#cd66601f\"\n  },\n  \"pre.diff-highlight > code .token.token.inserted:not(.prefix)\": {\n    \"backgroundColor\": \"#91d0761f\"\n  },\n  \"pre > code.diff-highlight .token.token.inserted:not(.prefix)\": {\n    \"backgroundColor\": \"#91d0761f\"\n  },\n  \".command-line .command-line-prompt\": {\n    \"borderRight\": \"1px solid #0b121b\"\n  },\n  \".command-line .command-line-prompt > span:before\": {\n    \"color\": \"#8da1b9da\"\n  }\n};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/styles/prism/coldark-dark.js"], "sourcesContent": ["export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"#e3eaf2\",\n    \"background\": \"none\",\n    \"fontFamily\": \"Consolas, Monaco, \\\"Andale Mono\\\", \\\"Ubuntu Mono\\\", monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"#e3eaf2\",\n    \"background\": \"#111b27\",\n    \"fontFamily\": \"Consolas, Monaco, \\\"Andale Mono\\\", \\\"Ubuntu Mono\\\", monospace\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \"0.5em 0\",\n    \"overflow\": \"auto\"\n  },\n  \"pre[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"]::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"] ::-moz-selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"pre[class*=\\\"language-\\\"]::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"pre[class*=\\\"language-\\\"] ::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"]::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \"code[class*=\\\"language-\\\"] ::selection\": {\n    \"background\": \"#3c526d\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#111b27\",\n    \"padding\": \"0.1em 0.3em\",\n    \"borderRadius\": \"0.3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"prolog\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"doctype\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"cdata\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"punctuation\": {\n    \"color\": \"#e3eaf2\"\n  },\n  \"delimiter.important\": {\n    \"color\": \"#66cccc\",\n    \"fontWeight\": \"inherit\"\n  },\n  \"selector.parent\": {\n    \"color\": \"#66cccc\"\n  },\n  \"tag\": {\n    \"color\": \"#66cccc\"\n  },\n  \"tag.punctuation\": {\n    \"color\": \"#66cccc\"\n  },\n  \"attr-name\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"boolean\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"boolean.important\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"number\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"constant\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"selector.attribute\": {\n    \"color\": \"#e6d37a\"\n  },\n  \"class-name\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"key\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"parameter\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"property\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"property-access\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"variable\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \"attr-value\": {\n    \"color\": \"#91d076\"\n  },\n  \"inserted\": {\n    \"color\": \"#91d076\"\n  },\n  \"color\": {\n    \"color\": \"#91d076\"\n  },\n  \"selector.value\": {\n    \"color\": \"#91d076\"\n  },\n  \"string\": {\n    \"color\": \"#91d076\"\n  },\n  \"string.url-link\": {\n    \"color\": \"#91d076\"\n  },\n  \"builtin\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"keyword-array\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"package\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"regex\": {\n    \"color\": \"#f4adf4\"\n  },\n  \"function\": {\n    \"color\": \"#c699e3\"\n  },\n  \"selector.class\": {\n    \"color\": \"#c699e3\"\n  },\n  \"selector.id\": {\n    \"color\": \"#c699e3\"\n  },\n  \"atrule.rule\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"combinator\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"keyword\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"operator\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"pseudo-class\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"pseudo-element\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"selector\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"unit\": {\n    \"color\": \"#e9ae7e\"\n  },\n  \"deleted\": {\n    \"color\": \"#cd6660\"\n  },\n  \"important\": {\n    \"color\": \"#cd6660\",\n    \"fontWeight\": \"bold\"\n  },\n  \"keyword-this\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \"this\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  },\n  \"entity\": {\n    \"cursor\": \"help\"\n  },\n  \".language-markdown .token.title\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \".language-markdown .token.title .token.punctuation\": {\n    \"color\": \"#6cb8e6\",\n    \"fontWeight\": \"bold\"\n  },\n  \".language-markdown .token.blockquote.punctuation\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".language-markdown .token.code\": {\n    \"color\": \"#66cccc\"\n  },\n  \".language-markdown .token.hr.punctuation\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".language-markdown .token.url .token.content\": {\n    \"color\": \"#91d076\"\n  },\n  \".language-markdown .token.url-link\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".language-markdown .token.list.punctuation\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".language-markdown .token.table-header\": {\n    \"color\": \"#e3eaf2\"\n  },\n  \".language-json .token.operator\": {\n    \"color\": \"#e3eaf2\"\n  },\n  \".language-scss .token.variable\": {\n    \"color\": \"#66cccc\"\n  },\n  \"token.tab:not(:empty):before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"token.cr:before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"token.lf:before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"token.space:before\": {\n    \"color\": \"#8da1b9\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#6cb8e6da\",\n    \"textDecoration\": \"none\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#8da1b9\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#8da1b9\"\n  },\n  \"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus\": {\n    \"color\": \"#111b27\",\n    \"background\": \"#8da1b9\"\n  },\n  \".line-highlight.line-highlight\": {\n    \"background\": \"linear-gradient(to right, #3c526d5f 70%, #3c526d55)\"\n  },\n  \".line-highlight.line-highlight:before\": {\n    \"backgroundColor\": \"#8da1b9\",\n    \"color\": \"#111b27\",\n    \"boxShadow\": \"0 1px #3c526d\"\n  },\n  \".line-highlight.line-highlight[data-end]:after\": {\n    \"backgroundColor\": \"#8da1b9\",\n    \"color\": \"#111b27\",\n    \"boxShadow\": \"0 1px #3c526d\"\n  },\n  \"pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before\": {\n    \"backgroundColor\": \"#8da1b918\"\n  },\n  \".line-numbers.line-numbers .line-numbers-rows\": {\n    \"borderRight\": \"1px solid #0b121b\",\n    \"background\": \"#0b121b7a\"\n  },\n  \".line-numbers .line-numbers-rows > span:before\": {\n    \"color\": \"#8da1b9da\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-1\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-5\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-9\": {\n    \"color\": \"#e6d37a\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-2\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-6\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-10\": {\n    \"color\": \"#f4adf4\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-3\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-7\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-11\": {\n    \"color\": \"#6cb8e6\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-4\": {\n    \"color\": \"#c699e3\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-8\": {\n    \"color\": \"#c699e3\"\n  },\n  \".rainbow-braces .token.token.punctuation.brace-level-12\": {\n    \"color\": \"#c699e3\"\n  },\n  \"pre.diff-highlight > code .token.token.deleted:not(.prefix)\": {\n    \"backgroundColor\": \"#cd66601f\"\n  },\n  \"pre > code.diff-highlight .token.token.deleted:not(.prefix)\": {\n    \"backgroundColor\": \"#cd66601f\"\n  },\n  \"pre.diff-highlight > code .token.token.inserted:not(.prefix)\": {\n    \"backgroundColor\": \"#91d0761f\"\n  },\n  \"pre > code.diff-highlight .token.token.inserted:not(.prefix)\": {\n    \"backgroundColor\": \"#91d0761f\"\n  },\n  \".command-line .command-line-prompt\": {\n    \"borderRight\": \"1px solid #0b121b\"\n  },\n  \".command-line .command-line-prompt > span:before\": {\n    \"color\": \"#8da1b9da\"\n  }\n};"], "mappings": "AAAA,eAAe;EACb,4BAA4B,EAAE;IAC5B,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE,+DAA+D;IAC7E,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE;EACb,CAAC;EACD,2BAA2B,EAAE;IAC3B,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,SAAS;IACvB,YAAY,EAAE,+DAA+D;IAC7E,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,SAAS;IACnB,UAAU,EAAE;EACd,CAAC;EACD,2CAA2C,EAAE;IAC3C,YAAY,EAAE;EAChB,CAAC;EACD,4CAA4C,EAAE;IAC5C,YAAY,EAAE;EAChB,CAAC;EACD,4CAA4C,EAAE;IAC5C,YAAY,EAAE;EAChB,CAAC;EACD,6CAA6C,EAAE;IAC7C,YAAY,EAAE;EAChB,CAAC;EACD,sCAAsC,EAAE;IACtC,YAAY,EAAE;EAChB,CAAC;EACD,uCAAuC,EAAE;IACvC,YAAY,EAAE;EAChB,CAAC;EACD,uCAAuC,EAAE;IACvC,YAAY,EAAE;EAChB,CAAC;EACD,wCAAwC,EAAE;IACxC,YAAY,EAAE;EAChB,CAAC;EACD,wCAAwC,EAAE;IACxC,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,aAAa;IACxB,cAAc,EAAE,OAAO;IACvB,YAAY,EAAE;EAChB,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,OAAO,EAAE;EACX,CAAC;EACD,qBAAqB,EAAE;IACrB,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,iBAAiB,EAAE;IACjB,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,iBAAiB,EAAE;IACjB,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,mBAAmB,EAAE;IACnB,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,oBAAoB,EAAE;IACpB,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,iBAAiB,EAAE;IACjB,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,gBAAgB,EAAE;IAChB,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,iBAAiB,EAAE;IACjB,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,eAAe,EAAE;IACf,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,gBAAgB,EAAE;IAChB,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,cAAc,EAAE;IACd,OAAO,EAAE;EACX,CAAC;EACD,gBAAgB,EAAE;IAChB,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACN,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,cAAc,EAAE;IACd,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACN,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACN,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACR,QAAQ,EAAE;EACZ,CAAC;EACD,iCAAiC,EAAE;IACjC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,oDAAoD,EAAE;IACpD,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,kDAAkD,EAAE;IAClD,OAAO,EAAE;EACX,CAAC;EACD,gCAAgC,EAAE;IAChC,OAAO,EAAE;EACX,CAAC;EACD,0CAA0C,EAAE;IAC1C,OAAO,EAAE;EACX,CAAC;EACD,8CAA8C,EAAE;IAC9C,OAAO,EAAE;EACX,CAAC;EACD,oCAAoC,EAAE;IACpC,OAAO,EAAE;EACX,CAAC;EACD,4CAA4C,EAAE;IAC5C,OAAO,EAAE;EACX,CAAC;EACD,wCAAwC,EAAE;IACxC,OAAO,EAAE;EACX,CAAC;EACD,gCAAgC,EAAE;IAChC,OAAO,EAAE;EACX,CAAC;EACD,gCAAgC,EAAE;IAChC,OAAO,EAAE;EACX,CAAC;EACD,8BAA8B,EAAE;IAC9B,OAAO,EAAE;EACX,CAAC;EACD,iBAAiB,EAAE;IACjB,OAAO,EAAE;EACX,CAAC;EACD,iBAAiB,EAAE;IACjB,OAAO,EAAE;EACX,CAAC;EACD,oBAAoB,EAAE;IACpB,OAAO,EAAE;EACX,CAAC;EACD,yDAAyD,EAAE;IACzD,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,8DAA8D,EAAE;IAC9D,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,+DAA+D,EAAE;IAC/D,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,WAAW;IACzB,gBAAgB,EAAE;EACpB,CAAC;EACD,+DAA+D,EAAE;IAC/D,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,WAAW;IACzB,gBAAgB,EAAE;EACpB,CAAC;EACD,oEAAoE,EAAE;IACpE,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,WAAW;IACzB,gBAAgB,EAAE;EACpB,CAAC;EACD,oEAAoE,EAAE;IACpE,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,WAAW;IACzB,gBAAgB,EAAE;EACpB,CAAC;EACD,4DAA4D,EAAE;IAC5D,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,kEAAkE,EAAE;IAClE,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,kEAAkE,EAAE;IAClE,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,gCAAgC,EAAE;IAChC,YAAY,EAAE;EAChB,CAAC;EACD,uCAAuC,EAAE;IACvC,iBAAiB,EAAE,SAAS;IAC5B,OAAO,EAAE,SAAS;IAClB,WAAW,EAAE;EACf,CAAC;EACD,gDAAgD,EAAE;IAChD,iBAAiB,EAAE,SAAS;IAC5B,OAAO,EAAE,SAAS;IAClB,WAAW,EAAE;EACf,CAAC;EACD,gGAAgG,EAAE;IAChG,iBAAiB,EAAE;EACrB,CAAC;EACD,+CAA+C,EAAE;IAC/C,aAAa,EAAE,mBAAmB;IAClC,YAAY,EAAE;EAChB,CAAC;EACD,gDAAgD,EAAE;IAChD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,yDAAyD,EAAE;IACzD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,yDAAyD,EAAE;IACzD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,wDAAwD,EAAE;IACxD,OAAO,EAAE;EACX,CAAC;EACD,yDAAyD,EAAE;IACzD,OAAO,EAAE;EACX,CAAC;EACD,6DAA6D,EAAE;IAC7D,iBAAiB,EAAE;EACrB,CAAC;EACD,6DAA6D,EAAE;IAC7D,iBAAiB,EAAE;EACrB,CAAC;EACD,8DAA8D,EAAE;IAC9D,iBAAiB,EAAE;EACrB,CAAC;EACD,8DAA8D,EAAE;IAC9D,iBAAiB,EAAE;EACrB,CAAC;EACD,oCAAoC,EAAE;IACpC,aAAa,EAAE;EACjB,CAAC;EACD,kDAAkD,EAAE;IAClD,OAAO,EAAE;EACX;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}