{"ast": null, "code": "'use strict';\n\nmodule.exports = eiffel;\neiffel.displayName = 'eiffel';\neiffel.aliases = [];\nfunction eiffel(Prism) {\n  Prism.languages.eiffel = {\n    comment: /--.*/,\n    string: [\n    // Aligned-verbatim-strings\n    {\n      pattern: /\"([^[]*)\\[[\\s\\S]*?\\]\\1\"/,\n      greedy: true\n    },\n    // Non-aligned-verbatim-strings\n    {\n      pattern: /\"([^{]*)\\{[\\s\\S]*?\\}\\1\"/,\n      greedy: true\n    },\n    // Single-line string\n    {\n      pattern: /\"(?:%(?:(?!\\n)\\s)*\\n\\s*%|%\\S|[^%\"\\r\\n])*\"/,\n      greedy: true\n    }],\n    // normal char | special char | char code\n    char: /'(?:%.|[^%'\\r\\n])+'/,\n    keyword: /\\b(?:across|agent|alias|all|and|as|assign|attached|attribute|check|class|convert|create|Current|debug|deferred|detachable|do|else|elseif|end|ensure|expanded|export|external|feature|from|frozen|if|implies|inherit|inspect|invariant|like|local|loop|not|note|obsolete|old|once|or|Precursor|redefine|rename|require|rescue|Result|retry|select|separate|some|then|undefine|until|variant|Void|when|xor)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    // Convention: class-names are always all upper-case characters\n    'class-name': /\\b[A-Z][\\dA-Z_]*\\b/,\n    number: [\n    // hexa | octal | bin\n    /\\b0[xcb][\\da-f](?:_*[\\da-f])*\\b/i,\n    // Decimal\n    /(?:\\b\\d(?:_*\\d)*)?\\.(?:(?:\\d(?:_*\\d)*)?e[+-]?)?\\d(?:_*\\d)*\\b|\\b\\d(?:_*\\d)*\\b\\.?/i],\n    punctuation: /:=|<<|>>|\\(\\||\\|\\)|->|\\.(?=\\w)|[{}[\\];(),:?]/,\n    operator: /\\\\\\\\|\\|\\.\\.\\||\\.\\.|\\/[~\\/=]?|[><]=?|[-+*^=~]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "eiffel", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "char", "keyword", "boolean", "number", "punctuation", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/eiffel.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = eiffel\neiffel.displayName = 'eiffel'\neiffel.aliases = []\nfunction eiffel(Prism) {\n  Prism.languages.eiffel = {\n    comment: /--.*/,\n    string: [\n      // Aligned-verbatim-strings\n      {\n        pattern: /\"([^[]*)\\[[\\s\\S]*?\\]\\1\"/,\n        greedy: true\n      }, // Non-aligned-verbatim-strings\n      {\n        pattern: /\"([^{]*)\\{[\\s\\S]*?\\}\\1\"/,\n        greedy: true\n      }, // Single-line string\n      {\n        pattern: /\"(?:%(?:(?!\\n)\\s)*\\n\\s*%|%\\S|[^%\"\\r\\n])*\"/,\n        greedy: true\n      }\n    ],\n    // normal char | special char | char code\n    char: /'(?:%.|[^%'\\r\\n])+'/,\n    keyword:\n      /\\b(?:across|agent|alias|all|and|as|assign|attached|attribute|check|class|convert|create|Current|debug|deferred|detachable|do|else|elseif|end|ensure|expanded|export|external|feature|from|frozen|if|implies|inherit|inspect|invariant|like|local|loop|not|note|obsolete|old|once|or|Precursor|redefine|rename|require|rescue|Result|retry|select|separate|some|then|undefine|until|variant|Void|when|xor)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    // Convention: class-names are always all upper-case characters\n    'class-name': /\\b[A-Z][\\dA-Z_]*\\b/,\n    number: [\n      // hexa | octal | bin\n      /\\b0[xcb][\\da-f](?:_*[\\da-f])*\\b/i, // Decimal\n      /(?:\\b\\d(?:_*\\d)*)?\\.(?:(?:\\d(?:_*\\d)*)?e[+-]?)?\\d(?:_*\\d)*\\b|\\b\\d(?:_*\\d)*\\b\\.?/i\n    ],\n    punctuation: /:=|<<|>>|\\(\\||\\|\\)|->|\\.(?=\\w)|[{}[\\];(),:?]/,\n    operator: /\\\\\\\\|\\|\\.\\.\\||\\.\\.|\\/[~\\/=]?|[><]=?|[-+*^=~]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE;IACN;IACA;MACEC,OAAO,EAAE,yBAAyB;MAClCC,MAAM,EAAE;IACV,CAAC;IAAE;IACH;MACED,OAAO,EAAE,yBAAyB;MAClCC,MAAM,EAAE;IACV,CAAC;IAAE;IACH;MACED,OAAO,EAAE,2CAA2C;MACpDC,MAAM,EAAE;IACV,CAAC,CACF;IACD;IACAC,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EACL,8YAA8Y;IAChZC,OAAO,EAAE,qBAAqB;IAC9B;IACA,YAAY,EAAE,oBAAoB;IAClCC,MAAM,EAAE;IACN;IACA,kCAAkC;IAAE;IACpC,kFAAkF,CACnF;IACDC,WAAW,EAAE,8CAA8C;IAC3DC,QAAQ,EAAE;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}