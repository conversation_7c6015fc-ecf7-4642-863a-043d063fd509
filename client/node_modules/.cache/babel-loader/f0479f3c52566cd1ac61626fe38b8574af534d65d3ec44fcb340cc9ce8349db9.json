{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: LLVM IR\nAuthor: <PERSON> <<EMAIL>>\nDescription: language used as intermediate representation in the LLVM compiler framework\nWebsite: https://llvm.org/docs/LangRef.html\nCategory: assembler\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction llvm(hljs) {\n  const IDENT_RE = /([-a-zA-Z$._][\\w$.-]*)/;\n  const TYPE = {\n    className: 'type',\n    begin: /\\bi\\d+(?=\\s|\\b)/\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    begin: /=/\n  };\n  const PUNCTUATION = {\n    className: 'punctuation',\n    relevance: 0,\n    begin: /,/\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [{\n      begin: /0[xX][a-fA-F0-9]+/\n    }, {\n      begin: /-?\\d+(?:[.]\\d+)?(?:[eE][-+]?\\d+(?:[.]\\d+)?)?/\n    }],\n    relevance: 0\n  };\n  const LABEL = {\n    className: 'symbol',\n    variants: [{\n      begin: /^\\s*[a-z]+:/\n    } // labels\n    ],\n    relevance: 0\n  };\n  const VARIABLE = {\n    className: 'variable',\n    variants: [{\n      begin: concat(/%/, IDENT_RE)\n    }, {\n      begin: /%\\d+/\n    }, {\n      begin: /#\\d+/\n    }]\n  };\n  const FUNCTION = {\n    className: 'title',\n    variants: [{\n      begin: concat(/@/, IDENT_RE)\n    }, {\n      begin: /@\\d+/\n    }, {\n      begin: concat(/!/, IDENT_RE)\n    }, {\n      begin: concat(/!\\d+/, IDENT_RE)\n    },\n    // https://llvm.org/docs/LangRef.html#namedmetadatastructure\n    // obviously a single digit can also be used in this fashion\n    {\n      begin: /!\\d+/\n    }]\n  };\n  return {\n    name: 'LLVM IR',\n    // TODO: split into different categories of keywords\n    keywords: 'begin end true false declare define global ' + 'constant private linker_private internal ' + 'available_externally linkonce linkonce_odr weak ' + 'weak_odr appending dllimport dllexport common ' + 'default hidden protected extern_weak external ' + 'thread_local zeroinitializer undef null to tail ' + 'target triple datalayout volatile nuw nsw nnan ' + 'ninf nsz arcp fast exact inbounds align ' + 'addrspace section alias module asm sideeffect ' + 'gc dbg linker_private_weak attributes blockaddress ' + 'initialexec localdynamic localexec prefix unnamed_addr ' + 'ccc fastcc coldcc x86_stdcallcc x86_fastcallcc ' + 'arm_apcscc arm_aapcscc arm_aapcs_vfpcc ptx_device ' + 'ptx_kernel intel_ocl_bicc msp430_intrcc spir_func ' + 'spir_kernel x86_64_sysvcc x86_64_win64cc x86_thiscallcc ' + 'cc c signext zeroext inreg sret nounwind ' + 'noreturn noalias nocapture byval nest readnone ' + 'readonly inlinehint noinline alwaysinline optsize ssp ' + 'sspreq noredzone noimplicitfloat naked builtin cold ' + 'nobuiltin noduplicate nonlazybind optnone returns_twice ' + 'sanitize_address sanitize_memory sanitize_thread sspstrong ' + 'uwtable returned type opaque eq ne slt sgt ' + 'sle sge ult ugt ule uge oeq one olt ogt ' + 'ole oge ord uno ueq une x acq_rel acquire ' + 'alignstack atomic catch cleanup filter inteldialect ' + 'max min monotonic nand personality release seq_cst ' + 'singlethread umax umin unordered xchg add fadd ' + 'sub fsub mul fmul udiv sdiv fdiv urem srem ' + 'frem shl lshr ashr and or xor icmp fcmp ' + 'phi call trunc zext sext fptrunc fpext uitofp ' + 'sitofp fptoui fptosi inttoptr ptrtoint bitcast ' + 'addrspacecast select va_arg ret br switch invoke ' + 'unwind unreachable indirectbr landingpad resume ' + 'malloc alloca free load store getelementptr ' + 'extractelement insertelement shufflevector getresult ' + 'extractvalue insertvalue atomicrmw cmpxchg fence ' + 'argmemonly double',\n    contains: [TYPE,\n    // this matches \"empty comments\"...\n    // ...because it's far more likely this is a statement terminator in\n    // another language than an actual comment\n    hljs.COMMENT(/;\\s*$/, null, {\n      relevance: 0\n    }), hljs.COMMENT(/;/, /$/), hljs.QUOTE_STRING_MODE, {\n      className: 'string',\n      variants: [\n      // Double-quoted string\n      {\n        begin: /\"/,\n        end: /[^\\\\]\"/\n      }]\n    }, FUNCTION, PUNCTUATION, OPERATOR, VARIABLE, LABEL, NUMBER]\n  };\n}\nmodule.exports = llvm;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "llvm", "hljs", "IDENT_RE", "TYPE", "className", "begin", "OPERATOR", "relevance", "PUNCTUATION", "NUMBER", "variants", "LABEL", "VARIABLE", "FUNCTION", "name", "keywords", "contains", "COMMENT", "QUOTE_STRING_MODE", "end", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/llvm.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: LLVM IR\nAuthor: <PERSON> <<EMAIL>>\nDescription: language used as intermediate representation in the LLVM compiler framework\nWebsite: https://llvm.org/docs/LangRef.html\nCategory: assembler\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction llvm(hljs) {\n  const IDENT_RE = /([-a-zA-Z$._][\\w$.-]*)/;\n  const TYPE = {\n    className: 'type',\n    begin: /\\bi\\d+(?=\\s|\\b)/\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    begin: /=/\n  };\n  const PUNCTUATION = {\n    className: 'punctuation',\n    relevance: 0,\n    begin: /,/\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n        { begin: /0[xX][a-fA-F0-9]+/ },\n        { begin: /-?\\d+(?:[.]\\d+)?(?:[eE][-+]?\\d+(?:[.]\\d+)?)?/ }\n    ],\n    relevance: 0\n  };\n  const LABEL = {\n    className: 'symbol',\n    variants: [\n        { begin: /^\\s*[a-z]+:/ }, // labels\n    ],\n    relevance: 0\n  };\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      { begin: concat(/%/, IDENT_RE) },\n      { begin: /%\\d+/ },\n      { begin: /#\\d+/ },\n    ]\n  };\n  const FUNCTION = {\n    className: 'title',\n    variants: [\n      { begin: concat(/@/, IDENT_RE) },\n      { begin: /@\\d+/ },\n      { begin: concat(/!/, IDENT_RE) },\n      { begin: concat(/!\\d+/, IDENT_RE) },\n      // https://llvm.org/docs/LangRef.html#namedmetadatastructure\n      // obviously a single digit can also be used in this fashion\n      { begin: /!\\d+/ }\n    ]\n  };\n\n  return {\n    name: 'LLVM IR',\n    // TODO: split into different categories of keywords\n    keywords:\n      'begin end true false declare define global ' +\n      'constant private linker_private internal ' +\n      'available_externally linkonce linkonce_odr weak ' +\n      'weak_odr appending dllimport dllexport common ' +\n      'default hidden protected extern_weak external ' +\n      'thread_local zeroinitializer undef null to tail ' +\n      'target triple datalayout volatile nuw nsw nnan ' +\n      'ninf nsz arcp fast exact inbounds align ' +\n      'addrspace section alias module asm sideeffect ' +\n      'gc dbg linker_private_weak attributes blockaddress ' +\n      'initialexec localdynamic localexec prefix unnamed_addr ' +\n      'ccc fastcc coldcc x86_stdcallcc x86_fastcallcc ' +\n      'arm_apcscc arm_aapcscc arm_aapcs_vfpcc ptx_device ' +\n      'ptx_kernel intel_ocl_bicc msp430_intrcc spir_func ' +\n      'spir_kernel x86_64_sysvcc x86_64_win64cc x86_thiscallcc ' +\n      'cc c signext zeroext inreg sret nounwind ' +\n      'noreturn noalias nocapture byval nest readnone ' +\n      'readonly inlinehint noinline alwaysinline optsize ssp ' +\n      'sspreq noredzone noimplicitfloat naked builtin cold ' +\n      'nobuiltin noduplicate nonlazybind optnone returns_twice ' +\n      'sanitize_address sanitize_memory sanitize_thread sspstrong ' +\n      'uwtable returned type opaque eq ne slt sgt ' +\n      'sle sge ult ugt ule uge oeq one olt ogt ' +\n      'ole oge ord uno ueq une x acq_rel acquire ' +\n      'alignstack atomic catch cleanup filter inteldialect ' +\n      'max min monotonic nand personality release seq_cst ' +\n      'singlethread umax umin unordered xchg add fadd ' +\n      'sub fsub mul fmul udiv sdiv fdiv urem srem ' +\n      'frem shl lshr ashr and or xor icmp fcmp ' +\n      'phi call trunc zext sext fptrunc fpext uitofp ' +\n      'sitofp fptoui fptosi inttoptr ptrtoint bitcast ' +\n      'addrspacecast select va_arg ret br switch invoke ' +\n      'unwind unreachable indirectbr landingpad resume ' +\n      'malloc alloca free load store getelementptr ' +\n      'extractelement insertelement shufflevector getresult ' +\n      'extractvalue insertvalue atomicrmw cmpxchg fence ' +\n      'argmemonly double',\n    contains: [\n      TYPE,\n      // this matches \"empty comments\"...\n      // ...because it's far more likely this is a statement terminator in\n      // another language than an actual comment\n      hljs.COMMENT(/;\\s*$/, null, { relevance: 0 }),\n      hljs.COMMENT(/;/, /$/),\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        variants: [\n          // Double-quoted string\n          { begin: /\"/, end: /[^\\\\]\"/ },\n        ]\n      },\n      FUNCTION,\n      PUNCTUATION,\n      OPERATOR,\n      VARIABLE,\n      LABEL,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = llvm;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,QAAQ,GAAG,wBAAwB;EACzC,MAAMC,IAAI,GAAG;IACXC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,QAAQ,GAAG;IACfF,SAAS,EAAE,UAAU;IACrBG,SAAS,EAAE,CAAC;IACZF,KAAK,EAAE;EACT,CAAC;EACD,MAAMG,WAAW,GAAG;IAClBJ,SAAS,EAAE,aAAa;IACxBG,SAAS,EAAE,CAAC;IACZF,KAAK,EAAE;EACT,CAAC;EACD,MAAMI,MAAM,GAAG;IACbL,SAAS,EAAE,QAAQ;IACnBM,QAAQ,EAAE,CACN;MAAEL,KAAK,EAAE;IAAoB,CAAC,EAC9B;MAAEA,KAAK,EAAE;IAA+C,CAAC,CAC5D;IACDE,SAAS,EAAE;EACb,CAAC;EACD,MAAMI,KAAK,GAAG;IACZP,SAAS,EAAE,QAAQ;IACnBM,QAAQ,EAAE,CACN;MAAEL,KAAK,EAAE;IAAc,CAAC,CAAE;IAAA,CAC7B;IACDE,SAAS,EAAE;EACb,CAAC;EACD,MAAMK,QAAQ,GAAG;IACfR,SAAS,EAAE,UAAU;IACrBM,QAAQ,EAAE,CACR;MAAEL,KAAK,EAAEX,MAAM,CAAC,GAAG,EAAEQ,QAAQ;IAAE,CAAC,EAChC;MAAEG,KAAK,EAAE;IAAO,CAAC,EACjB;MAAEA,KAAK,EAAE;IAAO,CAAC;EAErB,CAAC;EACD,MAAMQ,QAAQ,GAAG;IACfT,SAAS,EAAE,OAAO;IAClBM,QAAQ,EAAE,CACR;MAAEL,KAAK,EAAEX,MAAM,CAAC,GAAG,EAAEQ,QAAQ;IAAE,CAAC,EAChC;MAAEG,KAAK,EAAE;IAAO,CAAC,EACjB;MAAEA,KAAK,EAAEX,MAAM,CAAC,GAAG,EAAEQ,QAAQ;IAAE,CAAC,EAChC;MAAEG,KAAK,EAAEX,MAAM,CAAC,MAAM,EAAEQ,QAAQ;IAAE,CAAC;IACnC;IACA;IACA;MAAEG,KAAK,EAAE;IAAO,CAAC;EAErB,CAAC;EAED,OAAO;IACLS,IAAI,EAAE,SAAS;IACf;IACAC,QAAQ,EACN,6CAA6C,GAC7C,2CAA2C,GAC3C,kDAAkD,GAClD,gDAAgD,GAChD,gDAAgD,GAChD,kDAAkD,GAClD,iDAAiD,GACjD,0CAA0C,GAC1C,gDAAgD,GAChD,qDAAqD,GACrD,yDAAyD,GACzD,iDAAiD,GACjD,oDAAoD,GACpD,oDAAoD,GACpD,0DAA0D,GAC1D,2CAA2C,GAC3C,iDAAiD,GACjD,wDAAwD,GACxD,sDAAsD,GACtD,0DAA0D,GAC1D,6DAA6D,GAC7D,6CAA6C,GAC7C,0CAA0C,GAC1C,4CAA4C,GAC5C,sDAAsD,GACtD,qDAAqD,GACrD,iDAAiD,GACjD,6CAA6C,GAC7C,0CAA0C,GAC1C,gDAAgD,GAChD,iDAAiD,GACjD,mDAAmD,GACnD,kDAAkD,GAClD,8CAA8C,GAC9C,uDAAuD,GACvD,mDAAmD,GACnD,mBAAmB;IACrBC,QAAQ,EAAE,CACRb,IAAI;IACJ;IACA;IACA;IACAF,IAAI,CAACgB,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE;MAAEV,SAAS,EAAE;IAAE,CAAC,CAAC,EAC7CN,IAAI,CAACgB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EACtBhB,IAAI,CAACiB,iBAAiB,EACtB;MACEd,SAAS,EAAE,QAAQ;MACnBM,QAAQ,EAAE;MACR;MACA;QAAEL,KAAK,EAAE,GAAG;QAAEc,GAAG,EAAE;MAAS,CAAC;IAEjC,CAAC,EACDN,QAAQ,EACRL,WAAW,EACXF,QAAQ,EACRM,QAAQ,EACRD,KAAK,EACLF,MAAM;EAEV,CAAC;AACH;AAEAW,MAAM,CAACC,OAAO,GAAGrB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}