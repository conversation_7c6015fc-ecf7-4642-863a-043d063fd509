{"ast": null, "code": "'use strict';\n\nexports.parse = parse;\nexports.stringify = stringify;\nvar empty = '';\nvar space = ' ';\nvar whiteSpace = /[ \\t\\n\\r\\f]+/g;\nfunction parse(value) {\n  var input = String(value || empty).trim();\n  return input === empty ? [] : input.split(whiteSpace);\n}\nfunction stringify(values) {\n  return values.join(space).trim();\n}", "map": {"version": 3, "names": ["exports", "parse", "stringify", "empty", "space", "whiteSpace", "value", "input", "String", "trim", "split", "values", "join"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/space-separated-tokens/index.js"], "sourcesContent": ["'use strict'\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar empty = ''\nvar space = ' '\nvar whiteSpace = /[ \\t\\n\\r\\f]+/g\n\nfunction parse(value) {\n  var input = String(value || empty).trim()\n  return input === empty ? [] : input.split(whiteSpace)\n}\n\nfunction stringify(values) {\n  return values.join(space).trim()\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,KAAK,GAAGA,KAAK;AACrBD,OAAO,CAACE,SAAS,GAAGA,SAAS;AAE7B,IAAIC,KAAK,GAAG,EAAE;AACd,IAAIC,KAAK,GAAG,GAAG;AACf,IAAIC,UAAU,GAAG,eAAe;AAEhC,SAASJ,KAAKA,CAACK,KAAK,EAAE;EACpB,IAAIC,KAAK,GAAGC,MAAM,CAACF,KAAK,IAAIH,KAAK,CAAC,CAACM,IAAI,CAAC,CAAC;EACzC,OAAOF,KAAK,KAAKJ,KAAK,GAAG,EAAE,GAAGI,KAAK,CAACG,KAAK,CAACL,UAAU,CAAC;AACvD;AAEA,SAASH,SAASA,CAACS,MAAM,EAAE;EACzB,OAAOA,MAAM,CAACC,IAAI,CAACR,KAAK,CAAC,CAACK,IAAI,CAAC,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}