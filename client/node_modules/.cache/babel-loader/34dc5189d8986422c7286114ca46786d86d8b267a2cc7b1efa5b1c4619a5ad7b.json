{"ast": null, "code": "/*\nLanguage: Microsoft X++\nDescription: X++ is a language used in Microsoft Dynamics 365, Dynamics AX, and Axapta.\nAuthor: <PERSON><PERSON><PERSON> <d<PERSON><PERSON>@roudakov.ru>\nWebsite: https://dynamics.microsoft.com/en-us/ax-overview/\nCategory: enterprise\n*/\n\n/** @type LanguageFn */\nfunction axapta(hljs) {\n  const BUILT_IN_KEYWORDS = ['anytype', 'boolean', 'byte', 'char', 'container', 'date', 'double', 'enum', 'guid', 'int', 'int64', 'long', 'real', 'short', 'str', 'utcdatetime', 'var'];\n  const LITERAL_KEYWORDS = ['default', 'false', 'null', 'true'];\n  const NORMAL_KEYWORDS = ['abstract', 'as', 'asc', 'avg', 'break', 'breakpoint', 'by', 'byref', 'case', 'catch', 'changecompany', 'class', 'client', 'client', 'common', 'const', 'continue', 'count', 'crosscompany', 'delegate', 'delete_from', 'desc', 'display', 'div', 'do', 'edit', 'else', 'eventhandler', 'exists', 'extends', 'final', 'finally', 'firstfast', 'firstonly', 'firstonly1', 'firstonly10', 'firstonly100', 'firstonly1000', 'flush', 'for', 'forceliterals', 'forcenestedloop', 'forceplaceholders', 'forceselectorder', 'forupdate', 'from', 'generateonly', 'group', 'hint', 'if', 'implements', 'in', 'index', 'insert_recordset', 'interface', 'internal', 'is', 'join', 'like', 'maxof', 'minof', 'mod', 'namespace', 'new', 'next', 'nofetch', 'notexists', 'optimisticlock', 'order', 'outer', 'pessimisticlock', 'print', 'private', 'protected', 'public', 'readonly', 'repeatableread', 'retry', 'return', 'reverse', 'select', 'server', 'setting', 'static', 'sum', 'super', 'switch', 'this', 'throw', 'try', 'ttsabort', 'ttsbegin', 'ttscommit', 'unchecked', 'update_recordset', 'using', 'validtimestate', 'void', 'where', 'while'];\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS,\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  return {\n    name: 'X++',\n    aliases: ['x++'],\n    keywords: KEYWORDS,\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE, {\n      className: 'meta',\n      begin: '#',\n      end: '$'\n    }, {\n      className: 'class',\n      beginKeywords: 'class interface',\n      end: /\\{/,\n      excludeEnd: true,\n      illegal: ':',\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }]\n  };\n}\nmodule.exports = axapta;", "map": {"version": 3, "names": ["axapta", "hljs", "BUILT_IN_KEYWORDS", "LITERAL_KEYWORDS", "NORMAL_KEYWORDS", "KEYWORDS", "keyword", "built_in", "literal", "name", "aliases", "keywords", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_NUMBER_MODE", "className", "begin", "end", "beginKeywords", "excludeEnd", "illegal", "UNDERSCORE_TITLE_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/axapta.js"], "sourcesContent": ["/*\nLanguage: Microsoft X++\nDescription: X++ is a language used in Microsoft Dynamics 365, Dynamics AX, and Axapta.\nAuthor: <PERSON><PERSON><PERSON> <d<PERSON><PERSON>@roudakov.ru>\nWebsite: https://dynamics.microsoft.com/en-us/ax-overview/\nCategory: enterprise\n*/\n\n/** @type LanguageFn */\nfunction axapta(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'anytype',\n    'boolean',\n    'byte',\n    'char',\n    'container',\n    'date',\n    'double',\n    'enum',\n    'guid',\n    'int',\n    'int64',\n    'long',\n    'real',\n    'short',\n    'str',\n    'utcdatetime',\n    'var'\n  ];\n\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'asc',\n    'avg',\n    'break',\n    'breakpoint',\n    'by',\n    'byref',\n    'case',\n    'catch',\n    'changecompany',\n    'class',\n    'client',\n    'client',\n    'common',\n    'const',\n    'continue',\n    'count',\n    'crosscompany',\n    'delegate',\n    'delete_from',\n    'desc',\n    'display',\n    'div',\n    'do',\n    'edit',\n    'else',\n    'eventhandler',\n    'exists',\n    'extends',\n    'final',\n    'finally',\n    'firstfast',\n    'firstonly',\n    'firstonly1',\n    'firstonly10',\n    'firstonly100',\n    'firstonly1000',\n    'flush',\n    'for',\n    'forceliterals',\n    'forcenestedloop',\n    'forceplaceholders',\n    'forceselectorder',\n    'forupdate',\n    'from',\n    'generateonly',\n    'group',\n    'hint',\n    'if',\n    'implements',\n    'in',\n    'index',\n    'insert_recordset',\n    'interface',\n    'internal',\n    'is',\n    'join',\n    'like',\n    'maxof',\n    'minof',\n    'mod',\n    'namespace',\n    'new',\n    'next',\n    'nofetch',\n    'notexists',\n    'optimisticlock',\n    'order',\n    'outer',\n    'pessimisticlock',\n    'print',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'repeatableread',\n    'retry',\n    'return',\n    'reverse',\n    'select',\n    'server',\n    'setting',\n    'static',\n    'sum',\n    'super',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'ttsabort',\n    'ttsbegin',\n    'ttscommit',\n    'unchecked',\n    'update_recordset',\n    'using',\n    'validtimestate',\n    'void',\n    'where',\n    'while'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS,\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n\n  return {\n    name: 'X++',\n    aliases: ['x++'],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$'\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        illegal: ':',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = axapta;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,iBAAiB,GAAG,CACxB,SAAS,EACT,SAAS,EACT,MAAM,EACN,MAAM,EACN,WAAW,EACX,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,KAAK,EACL,aAAa,EACb,KAAK,CACN;EAED,MAAMC,gBAAgB,GAAG,CACvB,SAAS,EACT,OAAO,EACP,MAAM,EACN,MAAM,CACP;EAED,MAAMC,eAAe,GAAG,CACtB,UAAU,EACV,IAAI,EACJ,KAAK,EACL,KAAK,EACL,OAAO,EACP,YAAY,EACZ,IAAI,EACJ,OAAO,EACP,MAAM,EACN,OAAO,EACP,eAAe,EACf,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,UAAU,EACV,OAAO,EACP,cAAc,EACd,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EACT,KAAK,EACL,IAAI,EACJ,MAAM,EACN,MAAM,EACN,cAAc,EACd,QAAQ,EACR,SAAS,EACT,OAAO,EACP,SAAS,EACT,WAAW,EACX,WAAW,EACX,YAAY,EACZ,aAAa,EACb,cAAc,EACd,eAAe,EACf,OAAO,EACP,KAAK,EACL,eAAe,EACf,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,WAAW,EACX,MAAM,EACN,cAAc,EACd,OAAO,EACP,MAAM,EACN,IAAI,EACJ,YAAY,EACZ,IAAI,EACJ,OAAO,EACP,kBAAkB,EAClB,WAAW,EACX,UAAU,EACV,IAAI,EACJ,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,WAAW,EACX,KAAK,EACL,MAAM,EACN,SAAS,EACT,WAAW,EACX,gBAAgB,EAChB,OAAO,EACP,OAAO,EACP,iBAAiB,EACjB,OAAO,EACP,SAAS,EACT,WAAW,EACX,QAAQ,EACR,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACX,kBAAkB,EAClB,OAAO,EACP,gBAAgB,EAChB,MAAM,EACN,OAAO,EACP,OAAO,CACR;EAED,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAEF,eAAe;IACxBG,QAAQ,EAAEL,iBAAiB;IAC3BM,OAAO,EAAEL;EACX,CAAC;EAED,OAAO;IACLM,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,QAAQ,EAAEN,QAAQ;IAClBO,QAAQ,EAAE,CACRX,IAAI,CAACY,mBAAmB,EACxBZ,IAAI,CAACa,oBAAoB,EACzBb,IAAI,CAACc,gBAAgB,EACrBd,IAAI,CAACe,iBAAiB,EACtBf,IAAI,CAACgB,aAAa,EAClB;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,EACD;MACEF,SAAS,EAAE,OAAO;MAClBG,aAAa,EAAE,iBAAiB;MAChCD,GAAG,EAAE,IAAI;MACTE,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,GAAG;MACZX,QAAQ,EAAE,CACR;QACES,aAAa,EAAE;MACjB,CAAC,EACDpB,IAAI,CAACuB,qBAAqB;IAE9B,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAG1B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}