{"ast": null, "code": "'use strict';\n\nvar refractorSql = require('./sql.js');\nmodule.exports = apex;\napex.displayName = 'apex';\napex.aliases = [];\nfunction apex(Prism) {\n  Prism.register(refractorSql);\n  (function (Prism) {\n    var keywords = /\\b(?:(?:after|before)(?=\\s+[a-z])|abstract|activate|and|any|array|as|asc|autonomous|begin|bigdecimal|blob|boolean|break|bulk|by|byte|case|cast|catch|char|class|collect|commit|const|continue|currency|date|datetime|decimal|default|delete|desc|do|double|else|end|enum|exception|exit|export|extends|final|finally|float|for|from|get(?=\\s*[{};])|global|goto|group|having|hint|if|implements|import|in|inner|insert|instanceof|int|integer|interface|into|join|like|limit|list|long|loop|map|merge|new|not|null|nulls|number|object|of|on|or|outer|override|package|parallel|pragma|private|protected|public|retrieve|return|rollback|select|set|short|sObject|sort|static|string|super|switch|synchronized|system|testmethod|then|this|throw|time|transaction|transient|trigger|try|undelete|update|upsert|using|virtual|void|webservice|when|where|while|(?:inherited|with|without)\\s+sharing)\\b/i;\n    var className = /\\b(?:(?=[a-z_]\\w*\\s*[<\\[])|(?!<keyword>))[A-Z_]\\w*(?:\\s*\\.\\s*[A-Z_]\\w*)*\\b(?:\\s*(?:\\[\\s*\\]|<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>))*/.source.replace(/<keyword>/g, function () {\n      return keywords.source;\n    });\n    /** @param {string} pattern */\n    function insertClassName(pattern) {\n      return RegExp(pattern.replace(/<CLASS-NAME>/g, function () {\n        return className;\n      }), 'i');\n    }\n    var classNameInside = {\n      keyword: keywords,\n      punctuation: /[()\\[\\]{};,:.<>]/\n    };\n    Prism.languages.apex = {\n      comment: Prism.languages.clike.comment,\n      string: Prism.languages.clike.string,\n      sql: {\n        pattern: /((?:[=,({:]|\\breturn)\\s*)\\[[^\\[\\]]*\\]/i,\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-sql',\n        inside: Prism.languages.sql\n      },\n      annotation: {\n        pattern: /@\\w+\\b/,\n        alias: 'punctuation'\n      },\n      'class-name': [{\n        pattern: insertClassName(/(\\b(?:class|enum|extends|implements|instanceof|interface|new|trigger\\s+\\w+\\s+on)\\s+)<CLASS-NAME>/.source),\n        lookbehind: true,\n        inside: classNameInside\n      }, {\n        // cast\n        pattern: insertClassName(/(\\(\\s*)<CLASS-NAME>(?=\\s*\\)\\s*[\\w(])/.source),\n        lookbehind: true,\n        inside: classNameInside\n      }, {\n        // variable/parameter declaration and return types\n        pattern: insertClassName(/<CLASS-NAME>(?=\\s*\\w+\\s*[;=,(){:])/.source),\n        inside: classNameInside\n      }],\n      trigger: {\n        pattern: /(\\btrigger\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      keyword: keywords,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      boolean: /\\b(?:false|true)\\b/i,\n      number: /(?:\\B\\.\\d+|\\b\\d+(?:\\.\\d+|L)?)\\b/i,\n      operator: /[!=](?:==?)?|\\?\\.?|&&|\\|\\||--|\\+\\+|[-+*/^&|]=?|:|<<?=?|>{1,3}=?/,\n      punctuation: /[()\\[\\]{};,.]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorSql", "require", "module", "exports", "apex", "displayName", "aliases", "Prism", "register", "keywords", "className", "source", "replace", "insertClassName", "pattern", "RegExp", "classNameInside", "keyword", "punctuation", "languages", "comment", "clike", "string", "sql", "lookbehind", "greedy", "alias", "inside", "annotation", "trigger", "function", "boolean", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/apex.js"], "sourcesContent": ["'use strict'\nvar refractorSql = require('./sql.js')\nmodule.exports = apex\napex.displayName = 'apex'\napex.aliases = []\nfunction apex(Prism) {\n  Prism.register(refractorSql)\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:(?:after|before)(?=\\s+[a-z])|abstract|activate|and|any|array|as|asc|autonomous|begin|bigdecimal|blob|boolean|break|bulk|by|byte|case|cast|catch|char|class|collect|commit|const|continue|currency|date|datetime|decimal|default|delete|desc|do|double|else|end|enum|exception|exit|export|extends|final|finally|float|for|from|get(?=\\s*[{};])|global|goto|group|having|hint|if|implements|import|in|inner|insert|instanceof|int|integer|interface|into|join|like|limit|list|long|loop|map|merge|new|not|null|nulls|number|object|of|on|or|outer|override|package|parallel|pragma|private|protected|public|retrieve|return|rollback|select|set|short|sObject|sort|static|string|super|switch|synchronized|system|testmethod|then|this|throw|time|transaction|transient|trigger|try|undelete|update|upsert|using|virtual|void|webservice|when|where|while|(?:inherited|with|without)\\s+sharing)\\b/i\n    var className =\n      /\\b(?:(?=[a-z_]\\w*\\s*[<\\[])|(?!<keyword>))[A-Z_]\\w*(?:\\s*\\.\\s*[A-Z_]\\w*)*\\b(?:\\s*(?:\\[\\s*\\]|<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>))*/.source.replace(\n        /<keyword>/g,\n        function () {\n          return keywords.source\n        }\n      )\n    /** @param {string} pattern */\n    function insertClassName(pattern) {\n      return RegExp(\n        pattern.replace(/<CLASS-NAME>/g, function () {\n          return className\n        }),\n        'i'\n      )\n    }\n    var classNameInside = {\n      keyword: keywords,\n      punctuation: /[()\\[\\]{};,:.<>]/\n    }\n    Prism.languages.apex = {\n      comment: Prism.languages.clike.comment,\n      string: Prism.languages.clike.string,\n      sql: {\n        pattern: /((?:[=,({:]|\\breturn)\\s*)\\[[^\\[\\]]*\\]/i,\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-sql',\n        inside: Prism.languages.sql\n      },\n      annotation: {\n        pattern: /@\\w+\\b/,\n        alias: 'punctuation'\n      },\n      'class-name': [\n        {\n          pattern: insertClassName(\n            /(\\b(?:class|enum|extends|implements|instanceof|interface|new|trigger\\s+\\w+\\s+on)\\s+)<CLASS-NAME>/\n              .source\n          ),\n          lookbehind: true,\n          inside: classNameInside\n        },\n        {\n          // cast\n          pattern: insertClassName(\n            /(\\(\\s*)<CLASS-NAME>(?=\\s*\\)\\s*[\\w(])/.source\n          ),\n          lookbehind: true,\n          inside: classNameInside\n        },\n        {\n          // variable/parameter declaration and return types\n          pattern: insertClassName(/<CLASS-NAME>(?=\\s*\\w+\\s*[;=,(){:])/.source),\n          inside: classNameInside\n        }\n      ],\n      trigger: {\n        pattern: /(\\btrigger\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      keyword: keywords,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      boolean: /\\b(?:false|true)\\b/i,\n      number: /(?:\\B\\.\\d+|\\b\\d+(?:\\.\\d+|L)?)\\b/i,\n      operator:\n        /[!=](?:==?)?|\\?\\.?|&&|\\|\\||--|\\+\\+|[-+*/^&|]=?|:|<<?=?|>{1,3}=?/,\n      punctuation: /[()\\[\\]{};,.]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,UAAU,CAAC;AACtCC,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,QAAQ,CAACR,YAAY,CAAC;EAC3B,CAAC,UAAUO,KAAK,EAAE;IACjB,IAAIE,QAAQ,GACV,w2BAAw2B;IAC12B,IAAIC,SAAS,GACX,kIAAkI,CAACC,MAAM,CAACC,OAAO,CAC/I,YAAY,EACZ,YAAY;MACV,OAAOH,QAAQ,CAACE,MAAM;IACxB,CACF,CAAC;IACH;IACA,SAASE,eAAeA,CAACC,OAAO,EAAE;MAChC,OAAOC,MAAM,CACXD,OAAO,CAACF,OAAO,CAAC,eAAe,EAAE,YAAY;QAC3C,OAAOF,SAAS;MAClB,CAAC,CAAC,EACF,GACF,CAAC;IACH;IACA,IAAIM,eAAe,GAAG;MACpBC,OAAO,EAAER,QAAQ;MACjBS,WAAW,EAAE;IACf,CAAC;IACDX,KAAK,CAACY,SAAS,CAACf,IAAI,GAAG;MACrBgB,OAAO,EAAEb,KAAK,CAACY,SAAS,CAACE,KAAK,CAACD,OAAO;MACtCE,MAAM,EAAEf,KAAK,CAACY,SAAS,CAACE,KAAK,CAACC,MAAM;MACpCC,GAAG,EAAE;QACHT,OAAO,EAAE,wCAAwC;QACjDU,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAEpB,KAAK,CAACY,SAAS,CAACI;MAC1B,CAAC;MACDK,UAAU,EAAE;QACVd,OAAO,EAAE,QAAQ;QACjBY,KAAK,EAAE;MACT,CAAC;MACD,YAAY,EAAE,CACZ;QACEZ,OAAO,EAAED,eAAe,CACtB,kGAAkG,CAC/FF,MACL,CAAC;QACDa,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAEX;MACV,CAAC,EACD;QACE;QACAF,OAAO,EAAED,eAAe,CACtB,sCAAsC,CAACF,MACzC,CAAC;QACDa,UAAU,EAAE,IAAI;QAChBG,MAAM,EAAEX;MACV,CAAC,EACD;QACE;QACAF,OAAO,EAAED,eAAe,CAAC,oCAAoC,CAACF,MAAM,CAAC;QACrEgB,MAAM,EAAEX;MACV,CAAC,CACF;MACDa,OAAO,EAAE;QACPf,OAAO,EAAE,sBAAsB;QAC/BU,UAAU,EAAE,IAAI;QAChBE,KAAK,EAAE;MACT,CAAC;MACDT,OAAO,EAAER,QAAQ;MACjBqB,QAAQ,EAAE,uBAAuB;MACjCC,OAAO,EAAE,qBAAqB;MAC9BC,MAAM,EAAE,kCAAkC;MAC1CC,QAAQ,EACN,iEAAiE;MACnEf,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEX,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}