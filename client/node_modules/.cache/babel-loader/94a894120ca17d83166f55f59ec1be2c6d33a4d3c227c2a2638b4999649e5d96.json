{"ast": null, "code": "'use strict';\n\nmodule.exports = latex;\nlatex.displayName = 'latex';\nlatex.aliases = ['tex', 'context'];\nfunction latex(Prism) {\n  ;\n  (function (Prism) {\n    var funcPattern = /\\\\(?:[^a-z()[\\]]|[a-z*]+)/i;\n    var insideEqu = {\n      'equation-command': {\n        pattern: funcPattern,\n        alias: 'regex'\n      }\n    };\n    Prism.languages.latex = {\n      comment: /%.*/,\n      // the verbatim environment prints whitespace to the document\n      cdata: {\n        pattern: /(\\\\begin\\{((?:lstlisting|verbatim)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n        lookbehind: true\n      },\n      /*\n       * equations can be between $$ $$ or $ $ or \\( \\) or \\[ \\]\n       * (all are multiline)\n       */\n      equation: [{\n        pattern: /\\$\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$\\$|\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$|\\\\\\([\\s\\S]*?\\\\\\)|\\\\\\[[\\s\\S]*?\\\\\\]/,\n        inside: insideEqu,\n        alias: 'string'\n      }, {\n        pattern: /(\\\\begin\\{((?:align|eqnarray|equation|gather|math|multline)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n        lookbehind: true,\n        inside: insideEqu,\n        alias: 'string'\n      }],\n      /*\n       * arguments which are keywords or references are highlighted\n       * as keywords\n       */\n      keyword: {\n        pattern: /(\\\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      url: {\n        pattern: /(\\\\url\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      /*\n       * section or chapter headlines are highlighted as bold so that\n       * they stand out more\n       */\n      headline: {\n        pattern: /(\\\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\\*?(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      function: {\n        pattern: funcPattern,\n        alias: 'selector'\n      },\n      punctuation: /[[\\]{}&]/\n    };\n    Prism.languages.tex = Prism.languages.latex;\n    Prism.languages.context = Prism.languages.latex;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "latex", "displayName", "aliases", "Prism", "funcPattern", "insideEqu", "pattern", "alias", "languages", "comment", "cdata", "lookbehind", "equation", "inside", "keyword", "url", "headline", "function", "punctuation", "tex", "context"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/latex.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = latex\nlatex.displayName = 'latex'\nlatex.aliases = ['tex', 'context']\nfunction latex(Prism) {\n  ;(function (Prism) {\n    var funcPattern = /\\\\(?:[^a-z()[\\]]|[a-z*]+)/i\n    var insideEqu = {\n      'equation-command': {\n        pattern: funcPattern,\n        alias: 'regex'\n      }\n    }\n    Prism.languages.latex = {\n      comment: /%.*/,\n      // the verbatim environment prints whitespace to the document\n      cdata: {\n        pattern:\n          /(\\\\begin\\{((?:lstlisting|verbatim)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n        lookbehind: true\n      },\n      /*\n       * equations can be between $$ $$ or $ $ or \\( \\) or \\[ \\]\n       * (all are multiline)\n       */\n      equation: [\n        {\n          pattern:\n            /\\$\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$\\$|\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$|\\\\\\([\\s\\S]*?\\\\\\)|\\\\\\[[\\s\\S]*?\\\\\\]/,\n          inside: insideEqu,\n          alias: 'string'\n        },\n        {\n          pattern:\n            /(\\\\begin\\{((?:align|eqnarray|equation|gather|math|multline)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n          lookbehind: true,\n          inside: insideEqu,\n          alias: 'string'\n        }\n      ],\n      /*\n       * arguments which are keywords or references are highlighted\n       * as keywords\n       */\n      keyword: {\n        pattern:\n          /(\\\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      url: {\n        pattern: /(\\\\url\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      /*\n       * section or chapter headlines are highlighted as bold so that\n       * they stand out more\n       */\n      headline: {\n        pattern:\n          /(\\\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\\*?(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      function: {\n        pattern: funcPattern,\n        alias: 'selector'\n      },\n      punctuation: /[[\\]{}&]/\n    }\n    Prism.languages.tex = Prism.languages.latex\n    Prism.languages.context = Prism.languages.latex\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;AAClC,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,WAAW,GAAG,4BAA4B;IAC9C,IAAIC,SAAS,GAAG;MACd,kBAAkB,EAAE;QAClBC,OAAO,EAAEF,WAAW;QACpBG,KAAK,EAAE;MACT;IACF,CAAC;IACDJ,KAAK,CAACK,SAAS,CAACR,KAAK,GAAG;MACtBS,OAAO,EAAE,KAAK;MACd;MACAC,KAAK,EAAE;QACLJ,OAAO,EACL,kEAAkE;QACpEK,UAAU,EAAE;MACd,CAAC;MACD;AACN;AACA;AACA;MACMC,QAAQ,EAAE,CACR;QACEN,OAAO,EACL,yFAAyF;QAC3FO,MAAM,EAAER,SAAS;QACjBE,KAAK,EAAE;MACT,CAAC,EACD;QACED,OAAO,EACL,2FAA2F;QAC7FK,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAER,SAAS;QACjBE,KAAK,EAAE;MACT,CAAC,CACF;MACD;AACN;AACA;AACA;MACMO,OAAO,EAAE;QACPR,OAAO,EACL,uFAAuF;QACzFK,UAAU,EAAE;MACd,CAAC;MACDI,GAAG,EAAE;QACHT,OAAO,EAAE,sBAAsB;QAC/BK,UAAU,EAAE;MACd,CAAC;MACD;AACN;AACA;AACA;MACMK,QAAQ,EAAE;QACRV,OAAO,EACL,2JAA2J;QAC7JK,UAAU,EAAE,IAAI;QAChBJ,KAAK,EAAE;MACT,CAAC;MACDU,QAAQ,EAAE;QACRX,OAAO,EAAEF,WAAW;QACpBG,KAAK,EAAE;MACT,CAAC;MACDW,WAAW,EAAE;IACf,CAAC;IACDf,KAAK,CAACK,SAAS,CAACW,GAAG,GAAGhB,KAAK,CAACK,SAAS,CAACR,KAAK;IAC3CG,KAAK,CAACK,SAAS,CAACY,OAAO,GAAGjB,KAAK,CAACK,SAAS,CAACR,KAAK;EACjD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}