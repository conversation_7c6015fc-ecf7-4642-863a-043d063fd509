{"ast": null, "code": "'use strict';\n\nmodule.exports = warpscript;\nwarpscript.displayName = 'warpscript';\nwarpscript.aliases = [];\nfunction warpscript(Prism) {\n  Prism.languages.warpscript = {\n    comment: /#.*|\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'|<'(?:[^\\\\']|'(?!>)|\\\\.)*'>/,\n      greedy: true\n    },\n    variable: /\\$\\S+/,\n    macro: {\n      pattern: /@\\S+/,\n      alias: 'property'\n    },\n    // WarpScript doesn't have any keywords, these are all functions under the control category\n    // https://www.warp10.io/tags/control\n    keyword: /\\b(?:BREAK|CHECKMACRO|CONTINUE|CUDF|DEFINED|DEFINEDMACRO|EVAL|FAIL|FOR|FOREACH|FORSTEP|IFT|IFTE|MSGFAIL|NRETURN|RETHROW|RETURN|SWITCH|TRY|UDF|UNTIL|WHILE)\\b/,\n    number: /[+-]?\\b(?:NaN|Infinity|\\d+(?:\\.\\d*)?(?:[Ee][+-]?\\d+)?|0x[\\da-fA-F]+|0b[01]+)\\b/,\n    boolean: /\\b(?:F|T|false|true)\\b/,\n    punctuation: /<%|%>|[{}[\\]()]/,\n    // Some operators from the \"operators\" category\n    // https://www.warp10.io/tags/operators\n    operator: /==|&&?|\\|\\|?|\\*\\*?|>>>?|<<|[<>!~]=?|[-/%^]|\\+!?|\\b(?:AND|NOT|OR)\\b/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "warpscript", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "variable", "macro", "alias", "keyword", "number", "boolean", "punctuation", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/warpscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = warpscript\nwarpscript.displayName = 'warpscript'\nwarpscript.aliases = []\nfunction warpscript(Prism) {\n  Prism.languages.warpscript = {\n    comment: /#.*|\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern:\n        /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'|<'(?:[^\\\\']|'(?!>)|\\\\.)*'>/,\n      greedy: true\n    },\n    variable: /\\$\\S+/,\n    macro: {\n      pattern: /@\\S+/,\n      alias: 'property'\n    },\n    // WarpScript doesn't have any keywords, these are all functions under the control category\n    // https://www.warp10.io/tags/control\n    keyword:\n      /\\b(?:BREAK|CHECKMACRO|CONTINUE|CUDF|DEFINED|DEFINEDMACRO|EVAL|FAIL|FOR|FOREACH|FORSTEP|IFT|IFTE|MSGFAIL|NRETURN|RETHROW|RETURN|SWITCH|TRY|UDF|UNTIL|WHILE)\\b/,\n    number:\n      /[+-]?\\b(?:NaN|Infinity|\\d+(?:\\.\\d*)?(?:[Ee][+-]?\\d+)?|0x[\\da-fA-F]+|0b[01]+)\\b/,\n    boolean: /\\b(?:F|T|false|true)\\b/,\n    punctuation: /<%|%>|[{}[\\]()]/,\n    // Some operators from the \"operators\" category\n    // https://www.warp10.io/tags/operators\n    operator:\n      /==|&&?|\\|\\|?|\\*\\*?|>>>?|<<|[<>!~]=?|[-/%^]|\\+!?|\\b(?:AND|NOT|OR)\\b/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,EAAE;AACvB,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAG;IAC3BK,OAAO,EAAE,6BAA6B;IACtCC,MAAM,EAAE;MACNC,OAAO,EACL,wEAAwE;MAC1EC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE;MACLH,OAAO,EAAE,MAAM;MACfI,KAAK,EAAE;IACT,CAAC;IACD;IACA;IACAC,OAAO,EACL,8JAA8J;IAChKC,MAAM,EACJ,gFAAgF;IAClFC,OAAO,EAAE,wBAAwB;IACjCC,WAAW,EAAE,iBAAiB;IAC9B;IACA;IACAC,QAAQ,EACN;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}