{"ast": null, "code": "'use strict';\n\nmodule.exports = dot;\ndot.displayName = 'dot';\ndot.aliases = ['gv'];\nfunction dot(Prism) {\n  // https://www.graphviz.org/doc/info/lang.html\n  ;\n  (function (Prism) {\n    var ID = '(?:' + [\n    // an identifier\n    /[a-zA-Z_\\x80-\\uFFFF][\\w\\x80-\\uFFFF]*/.source,\n    // a number\n    /-?(?:\\.\\d+|\\d+(?:\\.\\d*)?)/.source,\n    // a double-quoted string\n    /\"[^\"\\\\]*(?:\\\\[\\s\\S][^\"\\\\]*)*\"/.source,\n    // HTML-like string\n    /<(?:[^<>]|(?!<!--)<(?:[^<>\"']|\"[^\"]*\"|'[^']*')+>|<!--(?:[^-]|-(?!->))*-->)*>/.source].join('|') + ')';\n    var IDInside = {\n      markup: {\n        pattern: /(^<)[\\s\\S]+(?=>$)/,\n        lookbehind: true,\n        alias: ['language-markup', 'language-html', 'language-xml'],\n        inside: Prism.languages.markup\n      }\n    };\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function withID(source, flags) {\n      return RegExp(source.replace(/<ID>/g, function () {\n        return ID;\n      }), flags);\n    }\n    Prism.languages.dot = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\/|^#.*/m,\n        greedy: true\n      },\n      'graph-name': {\n        pattern: withID(/(\\b(?:digraph|graph|subgraph)[ \\t\\r\\n]+)<ID>/.source, 'i'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name',\n        inside: IDInside\n      },\n      'attr-value': {\n        pattern: withID(/(=[ \\t\\r\\n]*)<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      'attr-name': {\n        pattern: withID(/([\\[;, \\t\\r\\n])<ID>(?=[ \\t\\r\\n]*=)/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      keyword: /\\b(?:digraph|edge|graph|node|strict|subgraph)\\b/i,\n      'compass-point': {\n        pattern: /(:[ \\t\\r\\n]*)(?:[ewc_]|[ns][ew]?)(?![\\w\\x80-\\uFFFF])/,\n        lookbehind: true,\n        alias: 'builtin'\n      },\n      node: {\n        pattern: withID(/(^|[^-.\\w\\x80-\\uFFFF\\\\])<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      operator: /[=:]|-[->]/,\n      punctuation: /[\\[\\]{};,]/\n    };\n    Prism.languages.gv = Prism.languages.dot;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "dot", "displayName", "aliases", "Prism", "ID", "source", "join", "IDInside", "markup", "pattern", "lookbehind", "alias", "inside", "languages", "withID", "flags", "RegExp", "replace", "comment", "greedy", "keyword", "node", "operator", "punctuation", "gv"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/dot.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dot\ndot.displayName = 'dot'\ndot.aliases = ['gv']\nfunction dot(Prism) {\n  // https://www.graphviz.org/doc/info/lang.html\n  ;(function (Prism) {\n    var ID =\n      '(?:' +\n      [\n        // an identifier\n        /[a-zA-Z_\\x80-\\uFFFF][\\w\\x80-\\uFFFF]*/.source, // a number\n        /-?(?:\\.\\d+|\\d+(?:\\.\\d*)?)/.source, // a double-quoted string\n        /\"[^\"\\\\]*(?:\\\\[\\s\\S][^\"\\\\]*)*\"/.source, // HTML-like string\n        /<(?:[^<>]|(?!<!--)<(?:[^<>\"']|\"[^\"]*\"|'[^']*')+>|<!--(?:[^-]|-(?!->))*-->)*>/\n          .source\n      ].join('|') +\n      ')'\n    var IDInside = {\n      markup: {\n        pattern: /(^<)[\\s\\S]+(?=>$)/,\n        lookbehind: true,\n        alias: ['language-markup', 'language-html', 'language-xml'],\n        inside: Prism.languages.markup\n      }\n    }\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function withID(source, flags) {\n      return RegExp(\n        source.replace(/<ID>/g, function () {\n          return ID\n        }),\n        flags\n      )\n    }\n    Prism.languages.dot = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\/|^#.*/m,\n        greedy: true\n      },\n      'graph-name': {\n        pattern: withID(\n          /(\\b(?:digraph|graph|subgraph)[ \\t\\r\\n]+)<ID>/.source,\n          'i'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name',\n        inside: IDInside\n      },\n      'attr-value': {\n        pattern: withID(/(=[ \\t\\r\\n]*)<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      'attr-name': {\n        pattern: withID(/([\\[;, \\t\\r\\n])<ID>(?=[ \\t\\r\\n]*=)/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      keyword: /\\b(?:digraph|edge|graph|node|strict|subgraph)\\b/i,\n      'compass-point': {\n        pattern: /(:[ \\t\\r\\n]*)(?:[ewc_]|[ns][ew]?)(?![\\w\\x80-\\uFFFF])/,\n        lookbehind: true,\n        alias: 'builtin'\n      },\n      node: {\n        pattern: withID(/(^|[^-.\\w\\x80-\\uFFFF\\\\])<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      operator: /[=:]|-[->]/,\n      punctuation: /[\\[\\]{};,]/\n    }\n    Prism.languages.gv = Prism.languages.dot\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AACpB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EACA;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,EAAE,GACJ,KAAK,GACL;IACE;IACA,sCAAsC,CAACC,MAAM;IAAE;IAC/C,2BAA2B,CAACA,MAAM;IAAE;IACpC,+BAA+B,CAACA,MAAM;IAAE;IACxC,8EAA8E,CAC3EA,MAAM,CACV,CAACC,IAAI,CAAC,GAAG,CAAC,GACX,GAAG;IACL,IAAIC,QAAQ,GAAG;MACbC,MAAM,EAAE;QACNC,OAAO,EAAE,mBAAmB;QAC5BC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,cAAc,CAAC;QAC3DC,MAAM,EAAET,KAAK,CAACU,SAAS,CAACL;MAC1B;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;IACI,SAASM,MAAMA,CAACT,MAAM,EAAEU,KAAK,EAAE;MAC7B,OAAOC,MAAM,CACXX,MAAM,CAACY,OAAO,CAAC,OAAO,EAAE,YAAY;QAClC,OAAOb,EAAE;MACX,CAAC,CAAC,EACFW,KACF,CAAC;IACH;IACAZ,KAAK,CAACU,SAAS,CAACb,GAAG,GAAG;MACpBkB,OAAO,EAAE;QACPT,OAAO,EAAE,+BAA+B;QACxCU,MAAM,EAAE;MACV,CAAC;MACD,YAAY,EAAE;QACZV,OAAO,EAAEK,MAAM,CACb,8CAA8C,CAACT,MAAM,EACrD,GACF,CAAC;QACDK,UAAU,EAAE,IAAI;QAChBS,MAAM,EAAE,IAAI;QACZR,KAAK,EAAE,YAAY;QACnBC,MAAM,EAAEL;MACV,CAAC;MACD,YAAY,EAAE;QACZE,OAAO,EAAEK,MAAM,CAAC,mBAAmB,CAACT,MAAM,CAAC;QAC3CK,UAAU,EAAE,IAAI;QAChBS,MAAM,EAAE,IAAI;QACZP,MAAM,EAAEL;MACV,CAAC;MACD,WAAW,EAAE;QACXE,OAAO,EAAEK,MAAM,CAAC,oCAAoC,CAACT,MAAM,CAAC;QAC5DK,UAAU,EAAE,IAAI;QAChBS,MAAM,EAAE,IAAI;QACZP,MAAM,EAAEL;MACV,CAAC;MACDa,OAAO,EAAE,kDAAkD;MAC3D,eAAe,EAAE;QACfX,OAAO,EAAE,sDAAsD;QAC/DC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDU,IAAI,EAAE;QACJZ,OAAO,EAAEK,MAAM,CAAC,8BAA8B,CAACT,MAAM,CAAC;QACtDK,UAAU,EAAE,IAAI;QAChBS,MAAM,EAAE,IAAI;QACZP,MAAM,EAAEL;MACV,CAAC;MACDe,QAAQ,EAAE,YAAY;MACtBC,WAAW,EAAE;IACf,CAAC;IACDpB,KAAK,CAACU,SAAS,CAACW,EAAE,GAAGrB,KAAK,CAACU,SAAS,CAACb,GAAG;EAC1C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}