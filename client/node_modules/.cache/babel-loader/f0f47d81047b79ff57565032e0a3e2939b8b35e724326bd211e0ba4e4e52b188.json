{"ast": null, "code": "'use strict';\n\nmodule.exports = nevod;\nnevod.displayName = 'nevod';\nnevod.aliases = [];\nfunction nevod(Prism) {\n  Prism.languages.nevod = {\n    comment: /\\/\\/.*|(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$))/,\n    string: {\n      pattern: /(?:\"(?:\"\"|[^\"])*\"(?!\")|'(?:''|[^'])*'(?!'))!?\\*?/,\n      greedy: true,\n      inside: {\n        'string-attrs': /!$|!\\*$|\\*$/\n      }\n    },\n    namespace: {\n      pattern: /(@namespace\\s+)[a-zA-Z0-9\\-.]+(?=\\s*\\{)/,\n      lookbehind: true\n    },\n    pattern: {\n      pattern: /(@pattern\\s+)?#?[a-zA-Z0-9\\-.]+(?:\\s*\\(\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]+\\s*(?:,\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]*)*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      inside: {\n        'pattern-name': {\n          pattern: /^#?[a-zA-Z0-9\\-.]+/,\n          alias: 'class-name'\n        },\n        fields: {\n          pattern: /\\(.*\\)/,\n          inside: {\n            'field-name': {\n              pattern: /[a-zA-Z0-9\\-.]+/,\n              alias: 'variable'\n            },\n            punctuation: /[,()]/,\n            operator: {\n              pattern: /~/,\n              alias: 'field-hidden-mark'\n            }\n          }\n        }\n      }\n    },\n    search: {\n      pattern: /(@search\\s+|#)[a-zA-Z0-9\\-.]+(?:\\.\\*)?(?=\\s*;)/,\n      alias: 'function',\n      lookbehind: true\n    },\n    keyword: /@(?:having|inside|namespace|outside|pattern|require|search|where)\\b/,\n    'standard-pattern': {\n      pattern: /\\b(?:Alpha|AlphaNum|Any|Blank|End|LineBreak|Num|NumAlpha|Punct|Space|Start|Symbol|Word|WordBreak)\\b(?:\\([a-zA-Z0-9\\-.,\\s+]*\\))?/,\n      inside: {\n        'standard-pattern-name': {\n          pattern: /^[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        quantifier: {\n          pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n          alias: 'number'\n        },\n        'standard-pattern-attr': {\n          pattern: /[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        punctuation: /[,()]/\n      }\n    },\n    quantifier: {\n      pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n      alias: 'number'\n    },\n    operator: [{\n      pattern: /=/,\n      alias: 'pattern-def'\n    }, {\n      pattern: /&/,\n      alias: 'conjunction'\n    }, {\n      pattern: /~/,\n      alias: 'exception'\n    }, {\n      pattern: /\\?/,\n      alias: 'optionality'\n    }, {\n      pattern: /[[\\]]/,\n      alias: 'repetition'\n    }, {\n      pattern: /[{}]/,\n      alias: 'variation'\n    }, {\n      pattern: /[+_]/,\n      alias: 'sequence'\n    }, {\n      pattern: /\\.{2,3}/,\n      alias: 'span'\n    }],\n    'field-capture': [{\n      pattern: /([a-zA-Z0-9\\-.]+\\s*\\()\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+(?:\\s*,\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+)*(?=\\s*\\))/,\n      lookbehind: true,\n      inside: {\n        'field-name': {\n          pattern: /[a-zA-Z0-9\\-.]+/,\n          alias: 'variable'\n        },\n        colon: /:/\n      }\n    }, {\n      pattern: /[a-zA-Z0-9\\-.]+\\s*:/,\n      inside: {\n        'field-name': {\n          pattern: /[a-zA-Z0-9\\-.]+/,\n          alias: 'variable'\n        },\n        colon: /:/\n      }\n    }],\n    punctuation: /[:;,()]/,\n    name: /[a-zA-Z0-9\\-.]+/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "nevod", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "inside", "namespace", "lookbehind", "alias", "fields", "punctuation", "operator", "search", "keyword", "quantifier", "colon", "name"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/nevod.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nevod\nnevod.displayName = 'nevod'\nnevod.aliases = []\nfunction nevod(Prism) {\n  Prism.languages.nevod = {\n    comment: /\\/\\/.*|(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$))/,\n    string: {\n      pattern: /(?:\"(?:\"\"|[^\"])*\"(?!\")|'(?:''|[^'])*'(?!'))!?\\*?/,\n      greedy: true,\n      inside: {\n        'string-attrs': /!$|!\\*$|\\*$/\n      }\n    },\n    namespace: {\n      pattern: /(@namespace\\s+)[a-zA-Z0-9\\-.]+(?=\\s*\\{)/,\n      lookbehind: true\n    },\n    pattern: {\n      pattern:\n        /(@pattern\\s+)?#?[a-zA-Z0-9\\-.]+(?:\\s*\\(\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]+\\s*(?:,\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]*)*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      inside: {\n        'pattern-name': {\n          pattern: /^#?[a-zA-Z0-9\\-.]+/,\n          alias: 'class-name'\n        },\n        fields: {\n          pattern: /\\(.*\\)/,\n          inside: {\n            'field-name': {\n              pattern: /[a-zA-Z0-9\\-.]+/,\n              alias: 'variable'\n            },\n            punctuation: /[,()]/,\n            operator: {\n              pattern: /~/,\n              alias: 'field-hidden-mark'\n            }\n          }\n        }\n      }\n    },\n    search: {\n      pattern: /(@search\\s+|#)[a-zA-Z0-9\\-.]+(?:\\.\\*)?(?=\\s*;)/,\n      alias: 'function',\n      lookbehind: true\n    },\n    keyword:\n      /@(?:having|inside|namespace|outside|pattern|require|search|where)\\b/,\n    'standard-pattern': {\n      pattern:\n        /\\b(?:Alpha|AlphaNum|Any|Blank|End|LineBreak|Num|NumAlpha|Punct|Space|Start|Symbol|Word|WordBreak)\\b(?:\\([a-zA-Z0-9\\-.,\\s+]*\\))?/,\n      inside: {\n        'standard-pattern-name': {\n          pattern: /^[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        quantifier: {\n          pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n          alias: 'number'\n        },\n        'standard-pattern-attr': {\n          pattern: /[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        punctuation: /[,()]/\n      }\n    },\n    quantifier: {\n      pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n      alias: 'number'\n    },\n    operator: [\n      {\n        pattern: /=/,\n        alias: 'pattern-def'\n      },\n      {\n        pattern: /&/,\n        alias: 'conjunction'\n      },\n      {\n        pattern: /~/,\n        alias: 'exception'\n      },\n      {\n        pattern: /\\?/,\n        alias: 'optionality'\n      },\n      {\n        pattern: /[[\\]]/,\n        alias: 'repetition'\n      },\n      {\n        pattern: /[{}]/,\n        alias: 'variation'\n      },\n      {\n        pattern: /[+_]/,\n        alias: 'sequence'\n      },\n      {\n        pattern: /\\.{2,3}/,\n        alias: 'span'\n      }\n    ],\n    'field-capture': [\n      {\n        pattern:\n          /([a-zA-Z0-9\\-.]+\\s*\\()\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+(?:\\s*,\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+)*(?=\\s*\\))/,\n        lookbehind: true,\n        inside: {\n          'field-name': {\n            pattern: /[a-zA-Z0-9\\-.]+/,\n            alias: 'variable'\n          },\n          colon: /:/\n        }\n      },\n      {\n        pattern: /[a-zA-Z0-9\\-.]+\\s*:/,\n        inside: {\n          'field-name': {\n            pattern: /[a-zA-Z0-9\\-.]+/,\n            alias: 'variable'\n          },\n          colon: /:/\n        }\n      }\n    ],\n    punctuation: /[:;,()]/,\n    name: /[a-zA-Z0-9\\-.]+/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE,mCAAmC;IAC5CC,MAAM,EAAE;MACNC,OAAO,EAAE,kDAAkD;MAC3DC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACN,cAAc,EAAE;MAClB;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,OAAO,EAAE,yCAAyC;MAClDI,UAAU,EAAE;IACd,CAAC;IACDJ,OAAO,EAAE;MACPA,OAAO,EACL,oHAAoH;MACtHI,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;QACN,cAAc,EAAE;UACdF,OAAO,EAAE,oBAAoB;UAC7BK,KAAK,EAAE;QACT,CAAC;QACDC,MAAM,EAAE;UACNN,OAAO,EAAE,QAAQ;UACjBE,MAAM,EAAE;YACN,YAAY,EAAE;cACZF,OAAO,EAAE,iBAAiB;cAC1BK,KAAK,EAAE;YACT,CAAC;YACDE,WAAW,EAAE,OAAO;YACpBC,QAAQ,EAAE;cACRR,OAAO,EAAE,GAAG;cACZK,KAAK,EAAE;YACT;UACF;QACF;MACF;IACF,CAAC;IACDI,MAAM,EAAE;MACNT,OAAO,EAAE,gDAAgD;MACzDK,KAAK,EAAE,UAAU;MACjBD,UAAU,EAAE;IACd,CAAC;IACDM,OAAO,EACL,qEAAqE;IACvE,kBAAkB,EAAE;MAClBV,OAAO,EACL,iIAAiI;MACnIE,MAAM,EAAE;QACN,uBAAuB,EAAE;UACvBF,OAAO,EAAE,kBAAkB;UAC3BK,KAAK,EAAE;QACT,CAAC;QACDM,UAAU,EAAE;UACVX,OAAO,EAAE,kCAAkC;UAC3CK,KAAK,EAAE;QACT,CAAC;QACD,uBAAuB,EAAE;UACvBL,OAAO,EAAE,iBAAiB;UAC1BK,KAAK,EAAE;QACT,CAAC;QACDE,WAAW,EAAE;MACf;IACF,CAAC;IACDI,UAAU,EAAE;MACVX,OAAO,EAAE,kCAAkC;MAC3CK,KAAK,EAAE;IACT,CAAC;IACDG,QAAQ,EAAE,CACR;MACER,OAAO,EAAE,GAAG;MACZK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,GAAG;MACZK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,GAAG;MACZK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,IAAI;MACbK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,OAAO;MAChBK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,MAAM;MACfK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,MAAM;MACfK,KAAK,EAAE;IACT,CAAC,EACD;MACEL,OAAO,EAAE,SAAS;MAClBK,KAAK,EAAE;IACT,CAAC,CACF;IACD,eAAe,EAAE,CACf;MACEL,OAAO,EACL,0HAA0H;MAC5HI,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;QACN,YAAY,EAAE;UACZF,OAAO,EAAE,iBAAiB;UAC1BK,KAAK,EAAE;QACT,CAAC;QACDO,KAAK,EAAE;MACT;IACF,CAAC,EACD;MACEZ,OAAO,EAAE,qBAAqB;MAC9BE,MAAM,EAAE;QACN,YAAY,EAAE;UACZF,OAAO,EAAE,iBAAiB;UAC1BK,KAAK,EAAE;QACT,CAAC;QACDO,KAAK,EAAE;MACT;IACF,CAAC,CACF;IACDL,WAAW,EAAE,SAAS;IACtBM,IAAI,EAAE;EACR,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}