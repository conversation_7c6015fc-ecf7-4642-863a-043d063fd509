{"ast": null, "code": "'use strict';\n\nmodule.exports = kusto;\nkusto.displayName = 'kusto';\nkusto.aliases = [];\nfunction kusto(Prism) {\n  Prism.languages.kusto = {\n    comment: {\n      pattern: /\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /```[\\s\\S]*?```|[hH]?(?:\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|'(?:[^\\r\\n\\\\']|\\\\.)*'|@(?:\"[^\\r\\n\"]*\"|'[^\\r\\n']*'))/,\n      greedy: true\n    },\n    verb: {\n      pattern: /(\\|\\s*)[a-z][\\w-]*/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    command: {\n      pattern: /\\.[a-z][a-z\\d-]*\\b/,\n      alias: 'keyword'\n    },\n    'class-name': /\\b(?:bool|datetime|decimal|dynamic|guid|int|long|real|string|timespan)\\b/,\n    keyword: /\\b(?:access|alias|and|anti|as|asc|auto|between|by|(?:contains|(?:ends|starts)with|has(?:perfix|suffix)?)(?:_cs)?|database|declare|desc|external|from|fullouter|has_all|in|ingestion|inline|inner|innerunique|into|(?:left|right)(?:anti(?:semi)?|inner|outer|semi)?|let|like|local|not|of|on|or|pattern|print|query_parameters|range|restrict|schema|set|step|table|tables|to|view|where|with|matches\\s+regex|nulls\\s+(?:first|last))(?![\\w-])/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/,\n    datetime: [{\n      // RFC 822 + RFC 850\n      pattern: /\\b(?:(?:Fri|Friday|Mon|Monday|Sat|Saturday|Sun|Sunday|Thu|Thursday|Tue|Tuesday|Wed|Wednesday)\\s*,\\s*)?\\d{1,2}(?:\\s+|-)(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)(?:\\s+|-)\\d{2}\\s+\\d{2}:\\d{2}(?::\\d{2})?(?:\\s*(?:\\b(?:[A-Z]|(?:[ECMT][DS]|GM|U)T)|[+-]\\d{4}))?\\b/,\n      alias: 'number'\n    }, {\n      // ISO 8601\n      pattern: /[+-]?\\b(?:\\d{4}-\\d{2}-\\d{2}(?:[ T]\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)?|\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)Z?/,\n      alias: 'number'\n    }],\n    number: /\\b(?:0x[0-9A-Fa-f]+|\\d+(?:\\.\\d+)?(?:[Ee][+-]?\\d+)?)(?:(?:min|sec|[mnµ]s|[dhms]|microsecond|tick)\\b)?|[+-]?\\binf\\b/,\n    operator: /=>|[!=]~|[!=<>]=?|[-+*/%|]|\\.\\./,\n    punctuation: /[()\\[\\]{},;.:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "kusto", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "verb", "lookbehind", "alias", "command", "keyword", "boolean", "function", "datetime", "number", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/kusto.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = kusto\nkusto.displayName = 'kusto'\nkusto.aliases = []\nfunction kusto(Prism) {\n  Prism.languages.kusto = {\n    comment: {\n      pattern: /\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern:\n        /```[\\s\\S]*?```|[hH]?(?:\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|'(?:[^\\r\\n\\\\']|\\\\.)*'|@(?:\"[^\\r\\n\"]*\"|'[^\\r\\n']*'))/,\n      greedy: true\n    },\n    verb: {\n      pattern: /(\\|\\s*)[a-z][\\w-]*/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    command: {\n      pattern: /\\.[a-z][a-z\\d-]*\\b/,\n      alias: 'keyword'\n    },\n    'class-name':\n      /\\b(?:bool|datetime|decimal|dynamic|guid|int|long|real|string|timespan)\\b/,\n    keyword:\n      /\\b(?:access|alias|and|anti|as|asc|auto|between|by|(?:contains|(?:ends|starts)with|has(?:perfix|suffix)?)(?:_cs)?|database|declare|desc|external|from|fullouter|has_all|in|ingestion|inline|inner|innerunique|into|(?:left|right)(?:anti(?:semi)?|inner|outer|semi)?|let|like|local|not|of|on|or|pattern|print|query_parameters|range|restrict|schema|set|step|table|tables|to|view|where|with|matches\\s+regex|nulls\\s+(?:first|last))(?![\\w-])/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/,\n    datetime: [\n      {\n        // RFC 822 + RFC 850\n        pattern:\n          /\\b(?:(?:Fri|Friday|Mon|Monday|Sat|Saturday|Sun|Sunday|Thu|Thursday|Tue|Tuesday|Wed|Wednesday)\\s*,\\s*)?\\d{1,2}(?:\\s+|-)(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)(?:\\s+|-)\\d{2}\\s+\\d{2}:\\d{2}(?::\\d{2})?(?:\\s*(?:\\b(?:[A-Z]|(?:[ECMT][DS]|GM|U)T)|[+-]\\d{4}))?\\b/,\n        alias: 'number'\n      },\n      {\n        // ISO 8601\n        pattern:\n          /[+-]?\\b(?:\\d{4}-\\d{2}-\\d{2}(?:[ T]\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)?|\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)Z?/,\n        alias: 'number'\n      }\n    ],\n    number:\n      /\\b(?:0x[0-9A-Fa-f]+|\\d+(?:\\.\\d+)?(?:[Ee][+-]?\\d+)?)(?:(?:min|sec|[mnµ]s|[dhms]|microsecond|tick)\\b)?|[+-]?\\binf\\b/,\n    operator: /=>|[!=]~|[!=<>]=?|[-+*/%|]|\\.\\./,\n    punctuation: /[()\\[\\]{},;.:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE;MACPC,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EACL,kGAAkG;MACpGC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJH,OAAO,EAAE,qBAAqB;MAC9BI,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPN,OAAO,EAAE,oBAAoB;MAC7BK,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EACV,0EAA0E;IAC5EE,OAAO,EACL,gbAAgb;IAClbC,OAAO,EAAE,yBAAyB;IAClCC,QAAQ,EAAE,sBAAsB;IAChCC,QAAQ,EAAE,CACR;MACE;MACAV,OAAO,EACL,0QAA0Q;MAC5QK,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAL,OAAO,EACL,0GAA0G;MAC5GK,KAAK,EAAE;IACT,CAAC,CACF;IACDM,MAAM,EACJ,mHAAmH;IACrHC,QAAQ,EAAE,iCAAiC;IAC3CC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}