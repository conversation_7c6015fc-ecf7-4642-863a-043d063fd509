{"ast": null, "code": "'use strict';\n\nmodule.exports = nix;\nnix.displayName = 'nix';\nnix.aliases = [];\nfunction nix(Prism) {\n  Prism.languages.nix = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|#.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|''(?:(?!'')[\\s\\S]|''(?:'|\\\\|\\$\\{))*''/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          // The lookbehind ensures the ${} is not preceded by \\ or ''\n          pattern: /(^|(?:^|(?!'').)[^\\\\])\\$\\{(?:[^{}]|\\{[^}]*\\})*\\}/,\n          lookbehind: true,\n          inside: null // see below\n        }\n      }\n    },\n    url: [/\\b(?:[a-z]{3,7}:\\/\\/)[\\w\\-+%~\\/.:#=?&]+/, {\n      pattern: /([^\\/])(?:[\\w\\-+%~.:#=?&]*(?!\\/\\/)[\\w\\-+%~\\/.:#=?&])?(?!\\/\\/)\\/[\\w\\-+%~\\/.:#=?&]*/,\n      lookbehind: true\n    }],\n    antiquotation: {\n      pattern: /\\$(?=\\{)/,\n      alias: 'important'\n    },\n    number: /\\b\\d+\\b/,\n    keyword: /\\b(?:assert|builtins|else|if|in|inherit|let|null|or|then|with)\\b/,\n    function: /\\b(?:abort|add|all|any|attrNames|attrValues|baseNameOf|compareVersions|concatLists|currentSystem|deepSeq|derivation|dirOf|div|elem(?:At)?|fetch(?:Tarball|url)|filter(?:Source)?|fromJSON|genList|getAttr|getEnv|hasAttr|hashString|head|import|intersectAttrs|is(?:Attrs|Bool|Function|Int|List|Null|String)|length|lessThan|listToAttrs|map|mul|parseDrvName|pathExists|read(?:Dir|File)|removeAttrs|replaceStrings|seq|sort|stringLength|sub(?:string)?|tail|throw|to(?:File|JSON|Path|String|XML)|trace|typeOf)\\b|\\bfoldl'\\B/,\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /[=!<>]=?|\\+\\+?|\\|\\||&&|\\/\\/|->?|[?@]/,\n    punctuation: /[{}()[\\].,:;]/\n  };\n  Prism.languages.nix.string.inside.interpolation.inside = Prism.languages.nix;\n}", "map": {"version": 3, "names": ["module", "exports", "nix", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "inside", "interpolation", "lookbehind", "url", "antiquotation", "alias", "number", "keyword", "function", "boolean", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/nix.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nix\nnix.displayName = 'nix'\nnix.aliases = []\nfunction nix(Prism) {\n  Prism.languages.nix = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|#.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|''(?:(?!'')[\\s\\S]|''(?:'|\\\\|\\$\\{))*''/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          // The lookbehind ensures the ${} is not preceded by \\ or ''\n          pattern: /(^|(?:^|(?!'').)[^\\\\])\\$\\{(?:[^{}]|\\{[^}]*\\})*\\}/,\n          lookbehind: true,\n          inside: null // see below\n        }\n      }\n    },\n    url: [\n      /\\b(?:[a-z]{3,7}:\\/\\/)[\\w\\-+%~\\/.:#=?&]+/,\n      {\n        pattern:\n          /([^\\/])(?:[\\w\\-+%~.:#=?&]*(?!\\/\\/)[\\w\\-+%~\\/.:#=?&])?(?!\\/\\/)\\/[\\w\\-+%~\\/.:#=?&]*/,\n        lookbehind: true\n      }\n    ],\n    antiquotation: {\n      pattern: /\\$(?=\\{)/,\n      alias: 'important'\n    },\n    number: /\\b\\d+\\b/,\n    keyword: /\\b(?:assert|builtins|else|if|in|inherit|let|null|or|then|with)\\b/,\n    function:\n      /\\b(?:abort|add|all|any|attrNames|attrValues|baseNameOf|compareVersions|concatLists|currentSystem|deepSeq|derivation|dirOf|div|elem(?:At)?|fetch(?:Tarball|url)|filter(?:Source)?|fromJSON|genList|getAttr|getEnv|hasAttr|hashString|head|import|intersectAttrs|is(?:Attrs|Bool|Function|Int|List|Null|String)|length|lessThan|listToAttrs|map|mul|parseDrvName|pathExists|read(?:Dir|File)|removeAttrs|replaceStrings|seq|sort|stringLength|sub(?:string)?|tail|throw|to(?:File|JSON|Path|String|XML)|trace|typeOf)\\b|\\bfoldl'\\B/,\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /[=!<>]=?|\\+\\+?|\\|\\||&&|\\/\\/|->?|[?@]/,\n    punctuation: /[{}()[\\].,:;]/\n  }\n  Prism.languages.nix.string.inside.interpolation.inside = Prism.languages.nix\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE;MACPC,OAAO,EAAE,sBAAsB;MAC/BC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,8DAA8D;MACvEC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACNC,aAAa,EAAE;UACb;UACAJ,OAAO,EAAE,kDAAkD;UAC3DK,UAAU,EAAE,IAAI;UAChBF,MAAM,EAAE,IAAI,CAAC;QACf;MACF;IACF,CAAC;IACDG,GAAG,EAAE,CACH,yCAAyC,EACzC;MACEN,OAAO,EACL,mFAAmF;MACrFK,UAAU,EAAE;IACd,CAAC,CACF;IACDE,aAAa,EAAE;MACbP,OAAO,EAAE,UAAU;MACnBQ,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,kEAAkE;IAC3EC,QAAQ,EACN,kgBAAkgB;IACpgBC,OAAO,EAAE,oBAAoB;IAC7BC,QAAQ,EAAE,sCAAsC;IAChDC,WAAW,EAAE;EACf,CAAC;EACDjB,KAAK,CAACC,SAAS,CAACJ,GAAG,CAACQ,MAAM,CAACC,MAAM,CAACC,aAAa,CAACD,MAAM,GAAGN,KAAK,CAACC,SAAS,CAACJ,GAAG;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}