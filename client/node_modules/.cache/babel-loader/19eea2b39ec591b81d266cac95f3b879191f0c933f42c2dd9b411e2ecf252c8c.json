{"ast": null, "code": "'use strict';\n\nmodule.exports = perl;\nperl.displayName = 'perl';\nperl.aliases = [];\nfunction perl(Prism) {\n  ;\n  (function (Prism) {\n    var brackets = /(?:\\((?:[^()\\\\]|\\\\[\\s\\S])*\\)|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\}|\\[(?:[^[\\]\\\\]|\\\\[\\s\\S])*\\]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)/.source;\n    Prism.languages.perl = {\n      comment: [{\n        // POD\n        pattern: /(^\\s*)=\\w[\\s\\S]*?=cut.*/m,\n        lookbehind: true,\n        greedy: true\n      }, {\n        pattern: /(^|[^\\\\$])#.*/,\n        lookbehind: true,\n        greedy: true\n      }],\n      // TODO Could be nice to handle Heredoc too.\n      string: [{\n        pattern: RegExp(/\\b(?:q|qq|qw|qx)(?![a-zA-Z0-9])\\s*/.source + '(?:' + [\n        // q/.../\n        /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source,\n        // q a...a\n        // eslint-disable-next-line regexp/strict\n        /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source,\n        // q(...)\n        // q{...}\n        // q[...]\n        // q<...>\n        brackets].join('|') + ')'),\n        greedy: true\n      },\n      // \"...\", `...`\n      {\n        pattern: /(\"|`)(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/,\n        greedy: true\n      },\n      // '...'\n      // FIXME Multi-line single-quoted strings are not supported as they would break variables containing '\n      {\n        pattern: /'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n        greedy: true\n      }],\n      regex: [{\n        pattern: RegExp(/\\b(?:m|qr)(?![a-zA-Z0-9])\\s*/.source + '(?:' + [\n        // m/.../\n        /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source,\n        // m a...a\n        // eslint-disable-next-line regexp/strict\n        /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source,\n        // m(...)\n        // m{...}\n        // m[...]\n        // m<...>\n        brackets].join('|') + ')' + /[msixpodualngc]*/.source),\n        greedy: true\n      },\n      // The lookbehinds prevent -s from breaking\n      {\n        pattern: RegExp(/(^|[^-])\\b(?:s|tr|y)(?![a-zA-Z0-9])\\s*/.source + '(?:' + [\n        // s/.../.../\n        // eslint-disable-next-line regexp/strict\n        /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source,\n        // s a...a...a\n        // eslint-disable-next-line regexp/strict\n        /([a-zA-Z0-9])(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3/.source,\n        // s(...)(...)\n        // s{...}{...}\n        // s[...][...]\n        // s<...><...>\n        // s(...)[...]\n        brackets + /\\s*/.source + brackets].join('|') + ')' + /[msixpodualngcer]*/.source),\n        lookbehind: true,\n        greedy: true\n      },\n      // /.../\n      // The look-ahead tries to prevent two divisions on\n      // the same line from being highlighted as regex.\n      // This does not support multi-line regex.\n      {\n        pattern: /\\/(?:[^\\/\\\\\\r\\n]|\\\\.)*\\/[msixpodualngc]*(?=\\s*(?:$|[\\r\\n,.;})&|\\-+*~<>!?^]|(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|x|xor)\\b))/,\n        greedy: true\n      }],\n      // FIXME Not sure about the handling of ::, ', and #\n      variable: [\n      // ${^POSTMATCH}\n      /[&*$@%]\\{\\^[A-Z]+\\}/,\n      // $^V\n      /[&*$@%]\\^[A-Z_]/,\n      // ${...}\n      /[&*$@%]#?(?=\\{)/,\n      // $foo\n      /[&*$@%]#?(?:(?:::)*'?(?!\\d)[\\w$]+(?![\\w$]))+(?:::)*/,\n      // $1\n      /[&*$@%]\\d+/,\n      // $_, @_, %!\n      // The negative lookahead prevents from breaking the %= operator\n      /(?!%=)[$@%][!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~]/],\n      filehandle: {\n        // <>, <FOO>, _\n        pattern: /<(?![<=])\\S*?>|\\b_\\b/,\n        alias: 'symbol'\n      },\n      'v-string': {\n        // v1.2, 1.2.3\n        pattern: /v\\d+(?:\\.\\d+)*|\\d+(?:\\.\\d+){2,}/,\n        alias: 'string'\n      },\n      function: {\n        pattern: /(\\bsub[ \\t]+)\\w+/,\n        lookbehind: true\n      },\n      keyword: /\\b(?:any|break|continue|default|delete|die|do|else|elsif|eval|for|foreach|given|goto|if|last|local|my|next|our|package|print|redo|require|return|say|state|sub|switch|undef|unless|until|use|when|while)\\b/,\n      number: /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)\\b/,\n      operator: /-[rwxoRWXOezsfdlpSbctugkTBMAC]\\b|\\+[+=]?|-[-=>]?|\\*\\*?=?|\\/\\/?=?|=[=~>]?|~[~=]?|\\|\\|?=?|&&?=?|<(?:=>?|<=?)?|>>?=?|![~=]?|[%^]=?|\\.(?:=|\\.\\.?)?|[\\\\?]|\\bx(?:=|\\b)|\\b(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|xor)\\b/,\n      punctuation: /[{}[\\];(),:]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "perl", "displayName", "aliases", "Prism", "brackets", "source", "languages", "comment", "pattern", "lookbehind", "greedy", "string", "RegExp", "join", "regex", "variable", "filehandle", "alias", "function", "keyword", "number", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/perl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = perl\nperl.displayName = 'perl'\nperl.aliases = []\nfunction perl(Prism) {\n  ;(function (Prism) {\n    var brackets =\n      /(?:\\((?:[^()\\\\]|\\\\[\\s\\S])*\\)|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\}|\\[(?:[^[\\]\\\\]|\\\\[\\s\\S])*\\]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)/\n        .source\n    Prism.languages.perl = {\n      comment: [\n        {\n          // POD\n          pattern: /(^\\s*)=\\w[\\s\\S]*?=cut.*/m,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\$])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      // TODO Could be nice to handle Heredoc too.\n      string: [\n        {\n          pattern: RegExp(\n            /\\b(?:q|qq|qw|qx)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // q/.../\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source, // q a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source, // q(...)\n                // q{...}\n                // q[...]\n                // q<...>\n                brackets\n              ].join('|') +\n              ')'\n          ),\n          greedy: true\n        }, // \"...\", `...`\n        {\n          pattern: /(\"|`)(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/,\n          greedy: true\n        }, // '...'\n        // FIXME Multi-line single-quoted strings are not supported as they would break variables containing '\n        {\n          pattern: /'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n          greedy: true\n        }\n      ],\n      regex: [\n        {\n          pattern: RegExp(\n            /\\b(?:m|qr)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // m/.../\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source, // m a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source, // m(...)\n                // m{...}\n                // m[...]\n                // m<...>\n                brackets\n              ].join('|') +\n              ')' +\n              /[msixpodualngc]*/.source\n          ),\n          greedy: true\n        }, // The lookbehinds prevent -s from breaking\n        {\n          pattern: RegExp(\n            /(^|[^-])\\b(?:s|tr|y)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // s/.../.../\n                // eslint-disable-next-line regexp/strict\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/\n                  .source, // s a...a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3/\n                  .source, // s(...)(...)\n                // s{...}{...}\n                // s[...][...]\n                // s<...><...>\n                // s(...)[...]\n                brackets + /\\s*/.source + brackets\n              ].join('|') +\n              ')' +\n              /[msixpodualngcer]*/.source\n          ),\n          lookbehind: true,\n          greedy: true\n        }, // /.../\n        // The look-ahead tries to prevent two divisions on\n        // the same line from being highlighted as regex.\n        // This does not support multi-line regex.\n        {\n          pattern:\n            /\\/(?:[^\\/\\\\\\r\\n]|\\\\.)*\\/[msixpodualngc]*(?=\\s*(?:$|[\\r\\n,.;})&|\\-+*~<>!?^]|(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|x|xor)\\b))/,\n          greedy: true\n        }\n      ],\n      // FIXME Not sure about the handling of ::, ', and #\n      variable: [\n        // ${^POSTMATCH}\n        /[&*$@%]\\{\\^[A-Z]+\\}/, // $^V\n        /[&*$@%]\\^[A-Z_]/, // ${...}\n        /[&*$@%]#?(?=\\{)/, // $foo\n        /[&*$@%]#?(?:(?:::)*'?(?!\\d)[\\w$]+(?![\\w$]))+(?:::)*/, // $1\n        /[&*$@%]\\d+/, // $_, @_, %!\n        // The negative lookahead prevents from breaking the %= operator\n        /(?!%=)[$@%][!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~]/\n      ],\n      filehandle: {\n        // <>, <FOO>, _\n        pattern: /<(?![<=])\\S*?>|\\b_\\b/,\n        alias: 'symbol'\n      },\n      'v-string': {\n        // v1.2, 1.2.3\n        pattern: /v\\d+(?:\\.\\d+)*|\\d+(?:\\.\\d+){2,}/,\n        alias: 'string'\n      },\n      function: {\n        pattern: /(\\bsub[ \\t]+)\\w+/,\n        lookbehind: true\n      },\n      keyword:\n        /\\b(?:any|break|continue|default|delete|die|do|else|elsif|eval|for|foreach|given|goto|if|last|local|my|next|our|package|print|redo|require|return|say|state|sub|switch|undef|unless|until|use|when|while)\\b/,\n      number:\n        /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)\\b/,\n      operator:\n        /-[rwxoRWXOezsfdlpSbctugkTBMAC]\\b|\\+[+=]?|-[-=>]?|\\*\\*?=?|\\/\\/?=?|=[=~>]?|~[~=]?|\\|\\|?=?|&&?=?|<(?:=>?|<=?)?|>>?=?|![~=]?|[%^]=?|\\.(?:=|\\.\\.?)?|[\\\\?]|\\bx(?:=|\\b)|\\b(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|xor)\\b/,\n      punctuation: /[{}[\\];(),:]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GACV,4GAA4G,CACzGC,MAAM;IACXF,KAAK,CAACG,SAAS,CAACN,IAAI,GAAG;MACrBO,OAAO,EAAE,CACP;QACE;QACAC,OAAO,EAAE,0BAA0B;QACnCC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACEF,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACD;MACAC,MAAM,EAAE,CACN;QACEH,OAAO,EAAEI,MAAM,CACb,oCAAoC,CAACP,MAAM,GACzC,KAAK,GACL;QACE;QACA,kDAAkD,CAACA,MAAM;QAAE;QAC3D;QACA,0CAA0C,CAACA,MAAM;QAAE;QACnD;QACA;QACA;QACAD,QAAQ,CACT,CAACS,IAAI,CAAC,GAAG,CAAC,GACX,GACJ,CAAC;QACDH,MAAM,EAAE;MACV,CAAC;MAAE;MACH;QACEF,OAAO,EAAE,kCAAkC;QAC3CE,MAAM,EAAE;MACV,CAAC;MAAE;MACH;MACA;QACEF,OAAO,EAAE,uBAAuB;QAChCE,MAAM,EAAE;MACV,CAAC,CACF;MACDI,KAAK,EAAE,CACL;QACEN,OAAO,EAAEI,MAAM,CACb,8BAA8B,CAACP,MAAM,GACnC,KAAK,GACL;QACE;QACA,kDAAkD,CAACA,MAAM;QAAE;QAC3D;QACA,0CAA0C,CAACA,MAAM;QAAE;QACnD;QACA;QACA;QACAD,QAAQ,CACT,CAACS,IAAI,CAAC,GAAG,CAAC,GACX,GAAG,GACH,kBAAkB,CAACR,MACvB,CAAC;QACDK,MAAM,EAAE;MACV,CAAC;MAAE;MACH;QACEF,OAAO,EAAEI,MAAM,CACb,wCAAwC,CAACP,MAAM,GAC7C,KAAK,GACL;QACE;QACA;QACA,6EAA6E,CAC1EA,MAAM;QAAE;QACX;QACA,qEAAqE,CAClEA,MAAM;QAAE;QACX;QACA;QACA;QACA;QACAD,QAAQ,GAAG,KAAK,CAACC,MAAM,GAAGD,QAAQ,CACnC,CAACS,IAAI,CAAC,GAAG,CAAC,GACX,GAAG,GACH,oBAAoB,CAACR,MACzB,CAAC;QACDI,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC;MAAE;MACH;MACA;MACA;MACA;QACEF,OAAO,EACL,2HAA2H;QAC7HE,MAAM,EAAE;MACV,CAAC,CACF;MACD;MACAK,QAAQ,EAAE;MACR;MACA,qBAAqB;MAAE;MACvB,iBAAiB;MAAE;MACnB,iBAAiB;MAAE;MACnB,qDAAqD;MAAE;MACvD,YAAY;MAAE;MACd;MACA,mDAAmD,CACpD;MACDC,UAAU,EAAE;QACV;QACAR,OAAO,EAAE,sBAAsB;QAC/BS,KAAK,EAAE;MACT,CAAC;MACD,UAAU,EAAE;QACV;QACAT,OAAO,EAAE,iCAAiC;QAC1CS,KAAK,EAAE;MACT,CAAC;MACDC,QAAQ,EAAE;QACRV,OAAO,EAAE,kBAAkB;QAC3BC,UAAU,EAAE;MACd,CAAC;MACDU,OAAO,EACL,4MAA4M;MAC9MC,MAAM,EACJ,6GAA6G;MAC/GC,QAAQ,EACN,+MAA+M;MACjNC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEnB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}