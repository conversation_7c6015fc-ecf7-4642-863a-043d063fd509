{"ast": null, "code": "'use strict';\n\nmodule.exports = webIdl;\nwebIdl.displayName = 'webIdl';\nwebIdl.aliases = [];\nfunction webIdl(Prism) {\n  ;\n  (function (Prism) {\n    var id = /(?:\\B-|\\b_|\\b)[A-Za-z][\\w-]*(?![\\w-])/.source;\n    var type = '(?:' + /\\b(?:unsigned\\s+)?long\\s+long(?![\\w-])/.source + '|' + /\\b(?:unrestricted|unsigned)\\s+[a-z]+(?![\\w-])/.source + '|' + /(?!(?:unrestricted|unsigned)\\b)/.source + id + /(?:\\s*<(?:[^<>]|<[^<>]*>)*>)?/.source + ')' + /(?:\\s*\\?)?/.source;\n    var typeInside = {};\n    Prism.languages['web-idl'] = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"]*\"/,\n        greedy: true\n      },\n      namespace: {\n        pattern: RegExp(/(\\bnamespace\\s+)/.source + id),\n        lookbehind: true\n      },\n      'class-name': [{\n        pattern: /(^|[^\\w-])(?:iterable|maplike|setlike)\\s*<(?:[^<>]|<[^<>]*>)*>/,\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        pattern: RegExp(/(\\b(?:attribute|const|deleter|getter|optional|setter)\\s+)/.source + type),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // callback return type\n        pattern: RegExp('(' + /\\bcallback\\s+/.source + id + /\\s*=\\s*/.source + ')' + type),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        // typedef\n        pattern: RegExp(/(\\btypedef\\b\\s*)/.source + type),\n        lookbehind: true,\n        inside: typeInside\n      }, {\n        pattern: RegExp(/(\\b(?:callback|dictionary|enum|interface(?:\\s+mixin)?)\\s+)(?!(?:interface|mixin)\\b)/.source + id),\n        lookbehind: true\n      }, {\n        // inheritance\n        pattern: RegExp(/(:\\s*)/.source + id),\n        lookbehind: true\n      },\n      // includes and implements\n      RegExp(id + /(?=\\s+(?:implements|includes)\\b)/.source), {\n        pattern: RegExp(/(\\b(?:implements|includes)\\s+)/.source + id),\n        lookbehind: true\n      }, {\n        // function return type, parameter types, and dictionary members\n        pattern: RegExp(type + '(?=' + /\\s*(?:\\.{3}\\s*)?/.source + id + /\\s*[(),;=]/.source + ')'),\n        inside: typeInside\n      }],\n      builtin: /\\b(?:ArrayBuffer|BigInt64Array|BigUint64Array|ByteString|DOMString|DataView|Float32Array|Float64Array|FrozenArray|Int16Array|Int32Array|Int8Array|ObservableArray|Promise|USVString|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray)\\b/,\n      keyword: [/\\b(?:async|attribute|callback|const|constructor|deleter|dictionary|enum|getter|implements|includes|inherit|interface|mixin|namespace|null|optional|or|partial|readonly|required|setter|static|stringifier|typedef|unrestricted)\\b/,\n      // type keywords\n      /\\b(?:any|bigint|boolean|byte|double|float|iterable|long|maplike|object|octet|record|sequence|setlike|short|symbol|undefined|unsigned|void)\\b/],\n      boolean: /\\b(?:false|true)\\b/,\n      number: {\n        pattern: /(^|[^\\w-])-?(?:0x[0-9a-f]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|NaN|Infinity)(?![\\w-])/i,\n        lookbehind: true\n      },\n      operator: /\\.{3}|[=:?<>-]/,\n      punctuation: /[(){}[\\].,;]/\n    };\n    for (var key in Prism.languages['web-idl']) {\n      if (key !== 'class-name') {\n        typeInside[key] = Prism.languages['web-idl'][key];\n      }\n    }\n    Prism.languages['webidl'] = Prism.languages['web-idl'];\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "webIdl", "displayName", "aliases", "Prism", "id", "source", "type", "typeInside", "languages", "comment", "pattern", "greedy", "string", "namespace", "RegExp", "lookbehind", "inside", "builtin", "keyword", "boolean", "number", "operator", "punctuation", "key"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/web-idl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = webIdl\nwebIdl.displayName = 'webIdl'\nwebIdl.aliases = []\nfunction webIdl(Prism) {\n  ;(function (Prism) {\n    var id = /(?:\\B-|\\b_|\\b)[A-Za-z][\\w-]*(?![\\w-])/.source\n    var type =\n      '(?:' +\n      /\\b(?:unsigned\\s+)?long\\s+long(?![\\w-])/.source +\n      '|' +\n      /\\b(?:unrestricted|unsigned)\\s+[a-z]+(?![\\w-])/.source +\n      '|' +\n      /(?!(?:unrestricted|unsigned)\\b)/.source +\n      id +\n      /(?:\\s*<(?:[^<>]|<[^<>]*>)*>)?/.source +\n      ')' +\n      /(?:\\s*\\?)?/.source\n    var typeInside = {}\n    Prism.languages['web-idl'] = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"]*\"/,\n        greedy: true\n      },\n      namespace: {\n        pattern: RegExp(/(\\bnamespace\\s+)/.source + id),\n        lookbehind: true\n      },\n      'class-name': [\n        {\n          pattern:\n            /(^|[^\\w-])(?:iterable|maplike|setlike)\\s*<(?:[^<>]|<[^<>]*>)*>/,\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          pattern: RegExp(\n            /(\\b(?:attribute|const|deleter|getter|optional|setter)\\s+)/.source +\n              type\n          ),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // callback return type\n          pattern: RegExp(\n            '(' + /\\bcallback\\s+/.source + id + /\\s*=\\s*/.source + ')' + type\n          ),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // typedef\n          pattern: RegExp(/(\\btypedef\\b\\s*)/.source + type),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          pattern: RegExp(\n            /(\\b(?:callback|dictionary|enum|interface(?:\\s+mixin)?)\\s+)(?!(?:interface|mixin)\\b)/\n              .source + id\n          ),\n          lookbehind: true\n        },\n        {\n          // inheritance\n          pattern: RegExp(/(:\\s*)/.source + id),\n          lookbehind: true\n        }, // includes and implements\n        RegExp(id + /(?=\\s+(?:implements|includes)\\b)/.source),\n        {\n          pattern: RegExp(/(\\b(?:implements|includes)\\s+)/.source + id),\n          lookbehind: true\n        },\n        {\n          // function return type, parameter types, and dictionary members\n          pattern: RegExp(\n            type +\n              '(?=' +\n              /\\s*(?:\\.{3}\\s*)?/.source +\n              id +\n              /\\s*[(),;=]/.source +\n              ')'\n          ),\n          inside: typeInside\n        }\n      ],\n      builtin:\n        /\\b(?:ArrayBuffer|BigInt64Array|BigUint64Array|ByteString|DOMString|DataView|Float32Array|Float64Array|FrozenArray|Int16Array|Int32Array|Int8Array|ObservableArray|Promise|USVString|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray)\\b/,\n      keyword: [\n        /\\b(?:async|attribute|callback|const|constructor|deleter|dictionary|enum|getter|implements|includes|inherit|interface|mixin|namespace|null|optional|or|partial|readonly|required|setter|static|stringifier|typedef|unrestricted)\\b/, // type keywords\n        /\\b(?:any|bigint|boolean|byte|double|float|iterable|long|maplike|object|octet|record|sequence|setlike|short|symbol|undefined|unsigned|void)\\b/\n      ],\n      boolean: /\\b(?:false|true)\\b/,\n      number: {\n        pattern:\n          /(^|[^\\w-])-?(?:0x[0-9a-f]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|NaN|Infinity)(?![\\w-])/i,\n        lookbehind: true\n      },\n      operator: /\\.{3}|[=:?<>-]/,\n      punctuation: /[(){}[\\].,;]/\n    }\n    for (var key in Prism.languages['web-idl']) {\n      if (key !== 'class-name') {\n        typeInside[key] = Prism.languages['web-idl'][key]\n      }\n    }\n    Prism.languages['webidl'] = Prism.languages['web-idl']\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,EAAE,GAAG,uCAAuC,CAACC,MAAM;IACvD,IAAIC,IAAI,GACN,KAAK,GACL,wCAAwC,CAACD,MAAM,GAC/C,GAAG,GACH,+CAA+C,CAACA,MAAM,GACtD,GAAG,GACH,iCAAiC,CAACA,MAAM,GACxCD,EAAE,GACF,+BAA+B,CAACC,MAAM,GACtC,GAAG,GACH,YAAY,CAACA,MAAM;IACrB,IAAIE,UAAU,GAAG,CAAC,CAAC;IACnBJ,KAAK,CAACK,SAAS,CAAC,SAAS,CAAC,GAAG;MAC3BC,OAAO,EAAE;QACPC,OAAO,EAAE,yBAAyB;QAClCC,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNF,OAAO,EAAE,SAAS;QAClBC,MAAM,EAAE;MACV,CAAC;MACDE,SAAS,EAAE;QACTH,OAAO,EAAEI,MAAM,CAAC,kBAAkB,CAACT,MAAM,GAAGD,EAAE,CAAC;QAC/CW,UAAU,EAAE;MACd,CAAC;MACD,YAAY,EAAE,CACZ;QACEL,OAAO,EACL,gEAAgE;QAClEK,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAET;MACV,CAAC,EACD;QACEG,OAAO,EAAEI,MAAM,CACb,2DAA2D,CAACT,MAAM,GAChEC,IACJ,CAAC;QACDS,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAET;MACV,CAAC,EACD;QACE;QACAG,OAAO,EAAEI,MAAM,CACb,GAAG,GAAG,eAAe,CAACT,MAAM,GAAGD,EAAE,GAAG,SAAS,CAACC,MAAM,GAAG,GAAG,GAAGC,IAC/D,CAAC;QACDS,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAET;MACV,CAAC,EACD;QACE;QACAG,OAAO,EAAEI,MAAM,CAAC,kBAAkB,CAACT,MAAM,GAAGC,IAAI,CAAC;QACjDS,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAET;MACV,CAAC,EACD;QACEG,OAAO,EAAEI,MAAM,CACb,qFAAqF,CAClFT,MAAM,GAAGD,EACd,CAAC;QACDW,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACAL,OAAO,EAAEI,MAAM,CAAC,QAAQ,CAACT,MAAM,GAAGD,EAAE,CAAC;QACrCW,UAAU,EAAE;MACd,CAAC;MAAE;MACHD,MAAM,CAACV,EAAE,GAAG,kCAAkC,CAACC,MAAM,CAAC,EACtD;QACEK,OAAO,EAAEI,MAAM,CAAC,gCAAgC,CAACT,MAAM,GAAGD,EAAE,CAAC;QAC7DW,UAAU,EAAE;MACd,CAAC,EACD;QACE;QACAL,OAAO,EAAEI,MAAM,CACbR,IAAI,GACF,KAAK,GACL,kBAAkB,CAACD,MAAM,GACzBD,EAAE,GACF,YAAY,CAACC,MAAM,GACnB,GACJ,CAAC;QACDW,MAAM,EAAET;MACV,CAAC,CACF;MACDU,OAAO,EACL,6OAA6O;MAC/OC,OAAO,EAAE,CACP,mOAAmO;MAAE;MACrO,8IAA8I,CAC/I;MACDC,OAAO,EAAE,oBAAoB;MAC7BC,MAAM,EAAE;QACNV,OAAO,EACL,0FAA0F;QAC5FK,UAAU,EAAE;MACd,CAAC;MACDM,QAAQ,EAAE,gBAAgB;MAC1BC,WAAW,EAAE;IACf,CAAC;IACD,KAAK,IAAIC,GAAG,IAAIpB,KAAK,CAACK,SAAS,CAAC,SAAS,CAAC,EAAE;MAC1C,IAAIe,GAAG,KAAK,YAAY,EAAE;QACxBhB,UAAU,CAACgB,GAAG,CAAC,GAAGpB,KAAK,CAACK,SAAS,CAAC,SAAS,CAAC,CAACe,GAAG,CAAC;MACnD;IACF;IACApB,KAAK,CAACK,SAAS,CAAC,QAAQ,CAAC,GAAGL,KAAK,CAACK,SAAS,CAAC,SAAS,CAAC;EACxD,CAAC,EAAEL,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}