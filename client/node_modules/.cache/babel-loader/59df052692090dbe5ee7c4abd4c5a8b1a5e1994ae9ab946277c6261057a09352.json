{"ast": null, "code": "/*\nLanguage: Protocol Buffers\nAuthor: <PERSON> <<EMAIL>>\nDescription: Protocol buffer message definition format\nWebsite: https://developers.google.com/protocol-buffers/docs/proto3\nCategory: protocols\n*/\n\nfunction protobuf(hljs) {\n  return {\n    name: 'Protocol Buffers',\n    keywords: {\n      keyword: 'package import option optional required repeated group oneof',\n      built_in: 'double float int32 int64 uint32 uint64 sint32 sint64 ' + 'fixed32 fixed64 sfixed32 sfixed64 bool string bytes',\n      literal: 'true false'\n    },\n    contains: [hljs.QUOTE_STRING_MODE, hljs.NUMBER_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'class',\n      beginKeywords: 'message enum service',\n      end: /\\{/,\n      illegal: /\\n/,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        starts: {\n          endsWithParent: true,\n          excludeEnd: true\n        } // hack: eating everything after the first title\n      })]\n    }, {\n      className: 'function',\n      beginKeywords: 'rpc',\n      end: /[{;]/,\n      excludeEnd: true,\n      keywords: 'rpc returns'\n    }, {\n      // match enum items (relevance)\n      // BLAH = ...;\n      begin: /^\\s*[A-Z_]+(?=\\s*=[^\\n]+;$)/\n    }]\n  };\n}\nmodule.exports = protobuf;", "map": {"version": 3, "names": ["protobuf", "hljs", "name", "keywords", "keyword", "built_in", "literal", "contains", "QUOTE_STRING_MODE", "NUMBER_MODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "className", "beginKeywords", "end", "illegal", "inherit", "TITLE_MODE", "starts", "endsWithParent", "excludeEnd", "begin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/protobuf.js"], "sourcesContent": ["/*\nLanguage: Protocol Buffers\nAuthor: <PERSON> <<EMAIL>>\nDescription: Protocol buffer message definition format\nWebsite: https://developers.google.com/protocol-buffers/docs/proto3\nCategory: protocols\n*/\n\nfunction protobuf(hljs) {\n  return {\n    name: 'Protocol Buffers',\n    keywords: {\n      keyword: 'package import option optional required repeated group oneof',\n      built_in: 'double float int32 int64 uint32 uint64 sint32 sint64 ' +\n        'fixed32 fixed64 sfixed32 sfixed64 bool string bytes',\n      literal: 'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'message enum service', end: /\\{/,\n        illegal: /\\n/,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            starts: {endsWithParent: true, excludeEnd: true} // hack: eating everything after the first title\n          })\n        ]\n      },\n      {\n        className: 'function',\n        beginKeywords: 'rpc',\n        end: /[{;]/, excludeEnd: true,\n        keywords: 'rpc returns'\n      },\n      { // match enum items (relevance)\n        // BLAH = ...;\n        begin: /^\\s*[A-Z_]+(?=\\s*=[^\\n]+;$)/\n      }\n    ]\n  };\n}\n\nmodule.exports = protobuf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,OAAO;IACLC,IAAI,EAAE,kBAAkB;IACxBC,QAAQ,EAAE;MACRC,OAAO,EAAE,8DAA8D;MACvEC,QAAQ,EAAE,uDAAuD,GAC/D,qDAAqD;MACvDC,OAAO,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE,CACRN,IAAI,CAACO,iBAAiB,EACtBP,IAAI,CAACQ,WAAW,EAChBR,IAAI,CAACS,mBAAmB,EACxBT,IAAI,CAACU,oBAAoB,EACzB;MACEC,SAAS,EAAE,OAAO;MAClBC,aAAa,EAAE,sBAAsB;MAAEC,GAAG,EAAE,IAAI;MAChDC,OAAO,EAAE,IAAI;MACbR,QAAQ,EAAE,CACRN,IAAI,CAACe,OAAO,CAACf,IAAI,CAACgB,UAAU,EAAE;QAC5BC,MAAM,EAAE;UAACC,cAAc,EAAE,IAAI;UAAEC,UAAU,EAAE;QAAI,CAAC,CAAC;MACnD,CAAC,CAAC;IAEN,CAAC,EACD;MACER,SAAS,EAAE,UAAU;MACrBC,aAAa,EAAE,KAAK;MACpBC,GAAG,EAAE,MAAM;MAAEM,UAAU,EAAE,IAAI;MAC7BjB,QAAQ,EAAE;IACZ,CAAC,EACD;MAAE;MACA;MACAkB,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGvB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}