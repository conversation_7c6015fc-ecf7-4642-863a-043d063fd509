{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: QML\nRequires: javascript.js, xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax highlighting for the Qt Quick QML scripting language, based mostly off\n             the JavaScript parser.\nWebsite: https://doc.qt.io/qt-5/qmlapplications.html\nCategory: scripting\n*/\n\nfunction qml(hljs) {\n  const KEYWORDS = {\n    keyword: 'in of on if for while finally var new function do return void else break catch ' + 'instanceof with throw case default try this switch continue typeof delete ' + 'let yield const export super debugger as async await import',\n    literal: 'true false null undefined NaN Infinity',\n    built_in: 'eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent ' + 'encodeURI encodeURIComponent escape unescape Object Function Boolean Error ' + 'EvalError InternalError RangeError ReferenceError StopIteration SyntaxError ' + 'TypeError URIError Number Math Date String RegExp Array Float32Array ' + 'Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array ' + 'Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require ' + 'module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect ' + 'Behavior bool color coordinate date double enumeration font geocircle georectangle ' + 'geoshape int list matrix4x4 parent point quaternion real rect ' + 'size string url variant vector2d vector3d vector4d ' + 'Promise'\n  };\n  const QML_IDENT_RE = '[a-zA-Z_][a-zA-Z0-9\\\\._]*';\n\n  // Isolate property statements. Ends at a :, =, ;, ,, a comment or end of line.\n  // Use property class.\n  const PROPERTY = {\n    className: 'keyword',\n    begin: '\\\\bproperty\\\\b',\n    starts: {\n      className: 'string',\n      end: '(:|=|;|,|//|/\\\\*|$)',\n      returnEnd: true\n    }\n  };\n\n  // Isolate signal statements. Ends at a ) a comment or end of line.\n  // Use property class.\n  const SIGNAL = {\n    className: 'keyword',\n    begin: '\\\\bsignal\\\\b',\n    starts: {\n      className: 'string',\n      end: '(\\\\(|:|=|;|,|//|/\\\\*|$)',\n      returnEnd: true\n    }\n  };\n\n  // id: is special in QML. When we see id: we want to mark the id: as attribute and\n  // emphasize the token following.\n  const ID_ID = {\n    className: 'attribute',\n    begin: '\\\\bid\\\\s*:',\n    starts: {\n      className: 'string',\n      end: QML_IDENT_RE,\n      returnEnd: false\n    }\n  };\n\n  // Find QML object attribute. An attribute is a QML identifier followed by :.\n  // Unfortunately it's hard to know where it ends, as it may contain scalars,\n  // objects, object definitions, or javascript. The true end is either when the parent\n  // ends or the next attribute is detected.\n  const QML_ATTRIBUTE = {\n    begin: QML_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    contains: [{\n      className: 'attribute',\n      begin: QML_IDENT_RE,\n      end: '\\\\s*:',\n      excludeEnd: true,\n      relevance: 0\n    }],\n    relevance: 0\n  };\n\n  // Find QML object. A QML object is a QML identifier followed by { and ends at the matching }.\n  // All we really care about is finding IDENT followed by { and just mark up the IDENT and ignore the {.\n  const QML_OBJECT = {\n    begin: concat(QML_IDENT_RE, /\\s*\\{/),\n    end: /\\{/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [hljs.inherit(hljs.TITLE_MODE, {\n      begin: QML_IDENT_RE\n    })]\n  };\n  return {\n    name: 'QML',\n    aliases: ['qt'],\n    case_insensitive: false,\n    keywords: KEYWORDS,\n    contains: [{\n      className: 'meta',\n      begin: /^\\s*['\"]use (strict|asm)['\"]/\n    }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n      // template string\n      className: 'string',\n      begin: '`',\n      end: '`',\n      contains: [hljs.BACKSLASH_ESCAPE, {\n        className: 'subst',\n        begin: '\\\\$\\\\{',\n        end: '\\\\}'\n      }]\n    }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'number',\n      variants: [{\n        begin: '\\\\b(0[bB][01]+)'\n      }, {\n        begin: '\\\\b(0[oO][0-7]+)'\n      }, {\n        begin: hljs.C_NUMBER_RE\n      }],\n      relevance: 0\n    }, {\n      // \"value\" container\n      begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n      keywords: 'return throw case',\n      contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.REGEXP_MODE, {\n        // E4X / JSX\n        begin: /</,\n        end: />\\s*[);\\]]/,\n        relevance: 0,\n        subLanguage: 'xml'\n      }],\n      relevance: 0\n    }, SIGNAL, PROPERTY, {\n      className: 'function',\n      beginKeywords: 'function',\n      end: /\\{/,\n      excludeEnd: true,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        begin: /[A-Za-z$_][0-9A-Za-z$_]*/\n      }), {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n      }],\n      illegal: /\\[|%/\n    }, {\n      // hack: prevents detection of keywords after dots\n      begin: '\\\\.' + hljs.IDENT_RE,\n      relevance: 0\n    }, ID_ID, QML_ATTRIBUTE, QML_OBJECT],\n    illegal: /#/\n  };\n}\nmodule.exports = qml;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "qml", "hljs", "KEYWORDS", "keyword", "literal", "built_in", "QML_IDENT_RE", "PROPERTY", "className", "begin", "starts", "end", "returnEnd", "SIGNAL", "ID_ID", "QML_ATTRIBUTE", "returnBegin", "contains", "excludeEnd", "relevance", "QML_OBJECT", "inherit", "TITLE_MODE", "name", "aliases", "case_insensitive", "keywords", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "BACKSLASH_ESCAPE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "variants", "C_NUMBER_RE", "RE_STARTERS_RE", "REGEXP_MODE", "subLanguage", "beginKeywords", "excludeBegin", "illegal", "IDENT_RE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/qml.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: QML\nRequires: javascript.js, xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax highlighting for the Qt Quick QML scripting language, based mostly off\n             the JavaScript parser.\nWebsite: https://doc.qt.io/qt-5/qmlapplications.html\nCategory: scripting\n*/\n\nfunction qml(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'in of on if for while finally var new function do return void else break catch ' +\n      'instanceof with throw case default try this switch continue typeof delete ' +\n      'let yield const export super debugger as async await import',\n    literal:\n      'true false null undefined NaN Infinity',\n    built_in:\n      'eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent ' +\n      'encodeURI encodeURIComponent escape unescape Object Function Boolean Error ' +\n      'EvalError InternalError RangeError ReferenceError StopIteration SyntaxError ' +\n      'TypeError URIError Number Math Date String RegExp Array Float32Array ' +\n      'Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array ' +\n      'Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require ' +\n      'module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect ' +\n      'Behavior bool color coordinate date double enumeration font geocircle georectangle ' +\n      'geoshape int list matrix4x4 parent point quaternion real rect ' +\n      'size string url variant vector2d vector3d vector4d ' +\n      'Promise'\n  };\n\n  const QML_IDENT_RE = '[a-zA-Z_][a-zA-Z0-9\\\\._]*';\n\n  // Isolate property statements. Ends at a :, =, ;, ,, a comment or end of line.\n  // Use property class.\n  const PROPERTY = {\n    className: 'keyword',\n    begin: '\\\\bproperty\\\\b',\n    starts: {\n      className: 'string',\n      end: '(:|=|;|,|//|/\\\\*|$)',\n      returnEnd: true\n    }\n  };\n\n  // Isolate signal statements. Ends at a ) a comment or end of line.\n  // Use property class.\n  const SIGNAL = {\n    className: 'keyword',\n    begin: '\\\\bsignal\\\\b',\n    starts: {\n      className: 'string',\n      end: '(\\\\(|:|=|;|,|//|/\\\\*|$)',\n      returnEnd: true\n    }\n  };\n\n  // id: is special in QML. When we see id: we want to mark the id: as attribute and\n  // emphasize the token following.\n  const ID_ID = {\n    className: 'attribute',\n    begin: '\\\\bid\\\\s*:',\n    starts: {\n      className: 'string',\n      end: QML_IDENT_RE,\n      returnEnd: false\n    }\n  };\n\n  // Find QML object attribute. An attribute is a QML identifier followed by :.\n  // Unfortunately it's hard to know where it ends, as it may contain scalars,\n  // objects, object definitions, or javascript. The true end is either when the parent\n  // ends or the next attribute is detected.\n  const QML_ATTRIBUTE = {\n    begin: QML_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    contains: [\n      {\n        className: 'attribute',\n        begin: QML_IDENT_RE,\n        end: '\\\\s*:',\n        excludeEnd: true,\n        relevance: 0\n      }\n    ],\n    relevance: 0\n  };\n\n  // Find QML object. A QML object is a QML identifier followed by { and ends at the matching }.\n  // All we really care about is finding IDENT followed by { and just mark up the IDENT and ignore the {.\n  const QML_OBJECT = {\n    begin: concat(QML_IDENT_RE, /\\s*\\{/),\n    end: /\\{/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: QML_IDENT_RE\n      })\n    ]\n  };\n\n  return {\n    name: 'QML',\n    aliases: [ 'qt' ],\n    case_insensitive: false,\n    keywords: KEYWORDS,\n    contains: [\n      {\n        className: 'meta',\n        begin: /^\\s*['\"]use (strict|asm)['\"]/\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      { // template string\n        className: 'string',\n        begin: '`',\n        end: '`',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          {\n            className: 'subst',\n            begin: '\\\\$\\\\{',\n            end: '\\\\}'\n          }\n        ]\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: '\\\\b(0[bB][01]+)'\n          },\n          {\n            begin: '\\\\b(0[oO][0-7]+)'\n          },\n          {\n            begin: hljs.C_NUMBER_RE\n          }\n        ],\n        relevance: 0\n      },\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        contains: [\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.REGEXP_MODE,\n          { // E4X / JSX\n            begin: /</,\n            end: />\\s*[);\\]]/,\n            relevance: 0,\n            subLanguage: 'xml'\n          }\n        ],\n        relevance: 0\n      },\n      SIGNAL,\n      PROPERTY,\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: /[A-Za-z$_][0-9A-Za-z$_]*/\n          }),\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            contains: [\n              hljs.C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          }\n        ],\n        illegal: /\\[|%/\n      },\n      {\n        // hack: prevents detection of keywords after dots\n        begin: '\\\\.' + hljs.IDENT_RE,\n        relevance: 0\n      },\n      ID_ID,\n      QML_ATTRIBUTE,\n      QML_OBJECT\n    ],\n    illegal: /#/\n  };\n}\n\nmodule.exports = qml;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,QAAQ,GAAG;IACfC,OAAO,EACL,iFAAiF,GACjF,4EAA4E,GAC5E,6DAA6D;IAC/DC,OAAO,EACL,wCAAwC;IAC1CC,QAAQ,EACN,uEAAuE,GACvE,6EAA6E,GAC7E,8EAA8E,GAC9E,uEAAuE,GACvE,uEAAuE,GACvE,gFAAgF,GAChF,8EAA8E,GAC9E,qFAAqF,GACrF,gEAAgE,GAChE,qDAAqD,GACrD;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,2BAA2B;;EAEhD;EACA;EACA,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,gBAAgB;IACvBC,MAAM,EAAE;MACNF,SAAS,EAAE,QAAQ;MACnBG,GAAG,EAAE,qBAAqB;MAC1BC,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA;EACA,MAAMC,MAAM,GAAG;IACbL,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE;MACNF,SAAS,EAAE,QAAQ;MACnBG,GAAG,EAAE,yBAAyB;MAC9BC,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA;EACA,MAAME,KAAK,GAAG;IACZN,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE;MACNF,SAAS,EAAE,QAAQ;MACnBG,GAAG,EAAEL,YAAY;MACjBM,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA;EACA;EACA;EACA,MAAMG,aAAa,GAAG;IACpBN,KAAK,EAAEH,YAAY,GAAG,OAAO;IAC7BU,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,CACR;MACET,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAEH,YAAY;MACnBK,GAAG,EAAE,OAAO;MACZO,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;IACb,CAAC,CACF;IACDA,SAAS,EAAE;EACb,CAAC;;EAED;EACA;EACA,MAAMC,UAAU,GAAG;IACjBX,KAAK,EAAEf,MAAM,CAACY,YAAY,EAAE,OAAO,CAAC;IACpCK,GAAG,EAAE,IAAI;IACTK,WAAW,EAAE,IAAI;IACjBG,SAAS,EAAE,CAAC;IACZF,QAAQ,EAAE,CACRhB,IAAI,CAACoB,OAAO,CAACpB,IAAI,CAACqB,UAAU,EAAE;MAC5Bb,KAAK,EAAEH;IACT,CAAC,CAAC;EAEN,CAAC;EAED,OAAO;IACLiB,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,CAAE,IAAI,CAAE;IACjBC,gBAAgB,EAAE,KAAK;IACvBC,QAAQ,EAAExB,QAAQ;IAClBe,QAAQ,EAAE,CACR;MACET,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC,EACDR,IAAI,CAAC0B,gBAAgB,EACrB1B,IAAI,CAAC2B,iBAAiB,EACtB;MAAE;MACApB,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MACVE,GAAG,EAAE,GAAG;MACRM,QAAQ,EAAE,CACRhB,IAAI,CAAC4B,gBAAgB,EACrB;QACErB,SAAS,EAAE,OAAO;QAClBC,KAAK,EAAE,QAAQ;QACfE,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACDV,IAAI,CAAC6B,mBAAmB,EACxB7B,IAAI,CAAC8B,oBAAoB,EACzB;MACEvB,SAAS,EAAE,QAAQ;MACnBwB,QAAQ,EAAE,CACR;QACEvB,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAER,IAAI,CAACgC;MACd,CAAC,CACF;MACDd,SAAS,EAAE;IACb,CAAC,EACD;MAAE;MACAV,KAAK,EAAE,GAAG,GAAGR,IAAI,CAACiC,cAAc,GAAG,iCAAiC;MACpER,QAAQ,EAAE,mBAAmB;MAC7BT,QAAQ,EAAE,CACRhB,IAAI,CAAC6B,mBAAmB,EACxB7B,IAAI,CAAC8B,oBAAoB,EACzB9B,IAAI,CAACkC,WAAW,EAChB;QAAE;QACA1B,KAAK,EAAE,GAAG;QACVE,GAAG,EAAE,YAAY;QACjBQ,SAAS,EAAE,CAAC;QACZiB,WAAW,EAAE;MACf,CAAC,CACF;MACDjB,SAAS,EAAE;IACb,CAAC,EACDN,MAAM,EACNN,QAAQ,EACR;MACEC,SAAS,EAAE,UAAU;MACrB6B,aAAa,EAAE,UAAU;MACzB1B,GAAG,EAAE,IAAI;MACTO,UAAU,EAAE,IAAI;MAChBD,QAAQ,EAAE,CACRhB,IAAI,CAACoB,OAAO,CAACpB,IAAI,CAACqB,UAAU,EAAE;QAC5Bb,KAAK,EAAE;MACT,CAAC,CAAC,EACF;QACED,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,IAAI;QACXE,GAAG,EAAE,IAAI;QACT2B,YAAY,EAAE,IAAI;QAClBpB,UAAU,EAAE,IAAI;QAChBD,QAAQ,EAAE,CACRhB,IAAI,CAAC6B,mBAAmB,EACxB7B,IAAI,CAAC8B,oBAAoB;MAE7B,CAAC,CACF;MACDQ,OAAO,EAAE;IACX,CAAC,EACD;MACE;MACA9B,KAAK,EAAE,KAAK,GAAGR,IAAI,CAACuC,QAAQ;MAC5BrB,SAAS,EAAE;IACb,CAAC,EACDL,KAAK,EACLC,aAAa,EACbK,UAAU,CACX;IACDmB,OAAO,EAAE;EACX,CAAC;AACH;AAEAE,MAAM,CAACC,OAAO,GAAG1C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}