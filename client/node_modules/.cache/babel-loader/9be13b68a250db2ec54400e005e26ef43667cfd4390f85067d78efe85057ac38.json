{"ast": null, "code": "'use strict';\n\nmodule.exports = pure;\npure.displayName = 'pure';\npure.aliases = [];\nfunction pure(Prism) {\n  ;\n  (function (Prism) {\n    // https://agraef.github.io/pure-docs/pure.html#lexical-matters\n    Prism.languages.pure = {\n      comment: [{\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true\n      }, {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true\n      }, /#!.+/],\n      'inline-lang': {\n        pattern: /%<[\\s\\S]+?%>/,\n        greedy: true,\n        inside: {\n          lang: {\n            pattern: /(^%< *)-\\*-.+?-\\*-/,\n            lookbehind: true,\n            alias: 'comment'\n          },\n          delimiter: {\n            pattern: /^%<.*|%>$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        greedy: true\n      },\n      number: {\n        // The look-behind prevents wrong highlighting of the .. operator\n        pattern: /((?:\\.\\.)?)(?:\\b(?:inf|nan)\\b|\\b0x[\\da-f]+|(?:\\b(?:0b)?\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[+-]?\\d+)?L?)/i,\n        lookbehind: true\n      },\n      keyword: /\\b(?:NULL|ans|break|bt|case|catch|cd|clear|const|def|del|dump|else|end|exit|extern|false|force|help|if|infix[lr]?|interface|let|ls|mem|namespace|nonfix|of|otherwise|outfix|override|postfix|prefix|private|public|pwd|quit|run|save|show|stats|then|throw|trace|true|type|underride|using|when|with)\\b/,\n      function: /\\b(?:abs|add_(?:addr|constdef|(?:fundef|interface|macdef|typedef)(?:_at)?|vardef)|all|any|applp?|arity|bigintp?|blob(?:_crc|_size|p)?|boolp?|byte_c?string(?:_pointer)?|byte_(?:matrix|pointer)|calloc|cat|catmap|ceil|char[ps]?|check_ptrtag|chr|clear_sentry|clearsym|closurep?|cmatrixp?|cols?|colcat(?:map)?|colmap|colrev|colvector(?:p|seq)?|complex(?:_float_(?:matrix|pointer)|_matrix(?:_view)?|_pointer|p)?|conj|cookedp?|cst|cstring(?:_(?:dup|list|vector))?|curry3?|cyclen?|del_(?:constdef|fundef|interface|macdef|typedef|vardef)|delete|diag(?:mat)?|dim|dmatrixp?|do|double(?:_matrix(?:_view)?|_pointer|p)?|dowith3?|drop|dropwhile|eval(?:cmd)?|exactp|filter|fix|fixity|flip|float(?:_matrix|_pointer)|floor|fold[lr]1?|frac|free|funp?|functionp?|gcd|get(?:_(?:byte|constdef|double|float|fundef|int(?:64)?|interface(?:_typedef)?|long|macdef|pointer|ptrtag|sentry|short|string|typedef|vardef))?|globsym|hash|head|id|im|imatrixp?|index|inexactp|infp|init|insert|int(?:_matrix(?:_view)?|_pointer|p)?|int64_(?:matrix|pointer)|integerp?|iteraten?|iterwhile|join|keys?|lambdap?|last(?:err(?:pos)?)?|lcd|list[2p]?|listmap|make_ptrtag|malloc|map|matcat|matrixp?|max|member|min|nanp|nargs|nmatrixp?|null|numberp?|ord|pack(?:ed)?|pointer(?:_cast|_tag|_type|p)?|pow|pred|ptrtag|put(?:_(?:byte|double|float|int(?:64)?|long|pointer|short|string))?|rationalp?|re|realp?|realloc|recordp?|redim|reduce(?:_with)?|refp?|repeatn?|reverse|rlistp?|round|rows?|rowcat(?:map)?|rowmap|rowrev|rowvector(?:p|seq)?|same|scan[lr]1?|sentry|sgn|short_(?:matrix|pointer)|slice|smatrixp?|sort|split|str|strcat|stream|stride|string(?:_(?:dup|list|vector)|p)?|subdiag(?:mat)?|submat|subseq2?|substr|succ|supdiag(?:mat)?|symbolp?|tail|take|takewhile|thunkp?|transpose|trunc|tuplep?|typep|ubyte|uint(?:64)?|ulong|uncurry3?|unref|unzip3?|update|ushort|vals?|varp?|vector(?:p|seq)?|void|zip3?|zipwith3?)\\b/,\n      special: {\n        pattern: /\\b__[a-z]+__\\b/i,\n        alias: 'builtin'\n      },\n      // Any combination of operator chars can be an operator\n      // eslint-disable-next-line no-misleading-character-class\n      operator: /(?:[!\"#$%&'*+,\\-.\\/:<=>?@\\\\^`|~\\u00a1-\\u00bf\\u00d7-\\u00f7\\u20d0-\\u2bff]|\\b_+\\b)+|\\b(?:and|div|mod|not|or)\\b/,\n      // FIXME: How can we prevent | and , to be highlighted as operator when they are used alone?\n      punctuation: /[(){}\\[\\];,|]/\n    };\n    var inlineLanguages = ['c', {\n      lang: 'c++',\n      alias: 'cpp'\n    }, 'fortran'];\n    var inlineLanguageRe = /%< *-\\*- *<lang>\\d* *-\\*-[\\s\\S]+?%>/.source;\n    inlineLanguages.forEach(function (lang) {\n      var alias = lang;\n      if (typeof lang !== 'string') {\n        alias = lang.alias;\n        lang = lang.lang;\n      }\n      if (Prism.languages[alias]) {\n        var o = {};\n        o['inline-lang-' + alias] = {\n          pattern: RegExp(inlineLanguageRe.replace('<lang>', lang.replace(/([.+*?\\/\\\\(){}\\[\\]])/g, '\\\\$1')), 'i'),\n          inside: Prism.util.clone(Prism.languages.pure['inline-lang'].inside)\n        };\n        o['inline-lang-' + alias].inside.rest = Prism.util.clone(Prism.languages[alias]);\n        Prism.languages.insertBefore('pure', 'inline-lang', o);\n      }\n    }); // C is the default inline language\n    if (Prism.languages.c) {\n      Prism.languages.pure['inline-lang'].inside.rest = Prism.util.clone(Prism.languages.c);\n    }\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "pure", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "inside", "lang", "alias", "delimiter", "string", "number", "keyword", "function", "special", "operator", "punctuation", "inlineLanguages", "inlineLanguageRe", "source", "for<PERSON>ach", "o", "RegExp", "replace", "util", "clone", "rest", "insertBefore", "c"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/pure.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pure\npure.displayName = 'pure'\npure.aliases = []\nfunction pure(Prism) {\n  ;(function (Prism) {\n    // https://agraef.github.io/pure-docs/pure.html#lexical-matters\n    Prism.languages.pure = {\n      comment: [\n        {\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n          lookbehind: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true\n        },\n        /#!.+/\n      ],\n      'inline-lang': {\n        pattern: /%<[\\s\\S]+?%>/,\n        greedy: true,\n        inside: {\n          lang: {\n            pattern: /(^%< *)-\\*-.+?-\\*-/,\n            lookbehind: true,\n            alias: 'comment'\n          },\n          delimiter: {\n            pattern: /^%<.*|%>$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        greedy: true\n      },\n      number: {\n        // The look-behind prevents wrong highlighting of the .. operator\n        pattern:\n          /((?:\\.\\.)?)(?:\\b(?:inf|nan)\\b|\\b0x[\\da-f]+|(?:\\b(?:0b)?\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[+-]?\\d+)?L?)/i,\n        lookbehind: true\n      },\n      keyword:\n        /\\b(?:NULL|ans|break|bt|case|catch|cd|clear|const|def|del|dump|else|end|exit|extern|false|force|help|if|infix[lr]?|interface|let|ls|mem|namespace|nonfix|of|otherwise|outfix|override|postfix|prefix|private|public|pwd|quit|run|save|show|stats|then|throw|trace|true|type|underride|using|when|with)\\b/,\n      function:\n        /\\b(?:abs|add_(?:addr|constdef|(?:fundef|interface|macdef|typedef)(?:_at)?|vardef)|all|any|applp?|arity|bigintp?|blob(?:_crc|_size|p)?|boolp?|byte_c?string(?:_pointer)?|byte_(?:matrix|pointer)|calloc|cat|catmap|ceil|char[ps]?|check_ptrtag|chr|clear_sentry|clearsym|closurep?|cmatrixp?|cols?|colcat(?:map)?|colmap|colrev|colvector(?:p|seq)?|complex(?:_float_(?:matrix|pointer)|_matrix(?:_view)?|_pointer|p)?|conj|cookedp?|cst|cstring(?:_(?:dup|list|vector))?|curry3?|cyclen?|del_(?:constdef|fundef|interface|macdef|typedef|vardef)|delete|diag(?:mat)?|dim|dmatrixp?|do|double(?:_matrix(?:_view)?|_pointer|p)?|dowith3?|drop|dropwhile|eval(?:cmd)?|exactp|filter|fix|fixity|flip|float(?:_matrix|_pointer)|floor|fold[lr]1?|frac|free|funp?|functionp?|gcd|get(?:_(?:byte|constdef|double|float|fundef|int(?:64)?|interface(?:_typedef)?|long|macdef|pointer|ptrtag|sentry|short|string|typedef|vardef))?|globsym|hash|head|id|im|imatrixp?|index|inexactp|infp|init|insert|int(?:_matrix(?:_view)?|_pointer|p)?|int64_(?:matrix|pointer)|integerp?|iteraten?|iterwhile|join|keys?|lambdap?|last(?:err(?:pos)?)?|lcd|list[2p]?|listmap|make_ptrtag|malloc|map|matcat|matrixp?|max|member|min|nanp|nargs|nmatrixp?|null|numberp?|ord|pack(?:ed)?|pointer(?:_cast|_tag|_type|p)?|pow|pred|ptrtag|put(?:_(?:byte|double|float|int(?:64)?|long|pointer|short|string))?|rationalp?|re|realp?|realloc|recordp?|redim|reduce(?:_with)?|refp?|repeatn?|reverse|rlistp?|round|rows?|rowcat(?:map)?|rowmap|rowrev|rowvector(?:p|seq)?|same|scan[lr]1?|sentry|sgn|short_(?:matrix|pointer)|slice|smatrixp?|sort|split|str|strcat|stream|stride|string(?:_(?:dup|list|vector)|p)?|subdiag(?:mat)?|submat|subseq2?|substr|succ|supdiag(?:mat)?|symbolp?|tail|take|takewhile|thunkp?|transpose|trunc|tuplep?|typep|ubyte|uint(?:64)?|ulong|uncurry3?|unref|unzip3?|update|ushort|vals?|varp?|vector(?:p|seq)?|void|zip3?|zipwith3?)\\b/,\n      special: {\n        pattern: /\\b__[a-z]+__\\b/i,\n        alias: 'builtin'\n      },\n      // Any combination of operator chars can be an operator\n      // eslint-disable-next-line no-misleading-character-class\n      operator:\n        /(?:[!\"#$%&'*+,\\-.\\/:<=>?@\\\\^`|~\\u00a1-\\u00bf\\u00d7-\\u00f7\\u20d0-\\u2bff]|\\b_+\\b)+|\\b(?:and|div|mod|not|or)\\b/,\n      // FIXME: How can we prevent | and , to be highlighted as operator when they are used alone?\n      punctuation: /[(){}\\[\\];,|]/\n    }\n    var inlineLanguages = [\n      'c',\n      {\n        lang: 'c++',\n        alias: 'cpp'\n      },\n      'fortran'\n    ]\n    var inlineLanguageRe = /%< *-\\*- *<lang>\\d* *-\\*-[\\s\\S]+?%>/.source\n    inlineLanguages.forEach(function (lang) {\n      var alias = lang\n      if (typeof lang !== 'string') {\n        alias = lang.alias\n        lang = lang.lang\n      }\n      if (Prism.languages[alias]) {\n        var o = {}\n        o['inline-lang-' + alias] = {\n          pattern: RegExp(\n            inlineLanguageRe.replace(\n              '<lang>',\n              lang.replace(/([.+*?\\/\\\\(){}\\[\\]])/g, '\\\\$1')\n            ),\n            'i'\n          ),\n          inside: Prism.util.clone(Prism.languages.pure['inline-lang'].inside)\n        }\n        o['inline-lang-' + alias].inside.rest = Prism.util.clone(\n          Prism.languages[alias]\n        )\n        Prism.languages.insertBefore('pure', 'inline-lang', o)\n      }\n    }) // C is the default inline language\n    if (Prism.languages.c) {\n      Prism.languages.pure['inline-lang'].inside.rest = Prism.util.clone(\n        Prism.languages.c\n      )\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACAA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;MACrBK,OAAO,EAAE,CACP;QACEC,OAAO,EAAE,2BAA2B;QACpCC,UAAU,EAAE;MACd,CAAC,EACD;QACED,OAAO,EAAE,kBAAkB;QAC3BC,UAAU,EAAE;MACd,CAAC,EACD,MAAM,CACP;MACD,aAAa,EAAE;QACbD,OAAO,EAAE,cAAc;QACvBE,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,IAAI,EAAE;YACJJ,OAAO,EAAE,oBAAoB;YAC7BC,UAAU,EAAE,IAAI;YAChBI,KAAK,EAAE;UACT,CAAC;UACDC,SAAS,EAAE;YACTN,OAAO,EAAE,WAAW;YACpBK,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDE,MAAM,EAAE;QACNP,OAAO,EAAE,uBAAuB;QAChCE,MAAM,EAAE;MACV,CAAC;MACDM,MAAM,EAAE;QACN;QACAR,OAAO,EACL,iGAAiG;QACnGC,UAAU,EAAE;MACd,CAAC;MACDQ,OAAO,EACL,ySAAyS;MAC3SC,QAAQ,EACN,y0DAAy0D;MAC30DC,OAAO,EAAE;QACPX,OAAO,EAAE,iBAAiB;QAC1BK,KAAK,EAAE;MACT,CAAC;MACD;MACA;MACAO,QAAQ,EACN,6GAA6G;MAC/G;MACAC,WAAW,EAAE;IACf,CAAC;IACD,IAAIC,eAAe,GAAG,CACpB,GAAG,EACH;MACEV,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;IACT,CAAC,EACD,SAAS,CACV;IACD,IAAIU,gBAAgB,GAAG,qCAAqC,CAACC,MAAM;IACnEF,eAAe,CAACG,OAAO,CAAC,UAAUb,IAAI,EAAE;MACtC,IAAIC,KAAK,GAAGD,IAAI;MAChB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5BC,KAAK,GAAGD,IAAI,CAACC,KAAK;QAClBD,IAAI,GAAGA,IAAI,CAACA,IAAI;MAClB;MACA,IAAIP,KAAK,CAACC,SAAS,CAACO,KAAK,CAAC,EAAE;QAC1B,IAAIa,CAAC,GAAG,CAAC,CAAC;QACVA,CAAC,CAAC,cAAc,GAAGb,KAAK,CAAC,GAAG;UAC1BL,OAAO,EAAEmB,MAAM,CACbJ,gBAAgB,CAACK,OAAO,CACtB,QAAQ,EACRhB,IAAI,CAACgB,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAC9C,CAAC,EACD,GACF,CAAC;UACDjB,MAAM,EAAEN,KAAK,CAACwB,IAAI,CAACC,KAAK,CAACzB,KAAK,CAACC,SAAS,CAACJ,IAAI,CAAC,aAAa,CAAC,CAACS,MAAM;QACrE,CAAC;QACDe,CAAC,CAAC,cAAc,GAAGb,KAAK,CAAC,CAACF,MAAM,CAACoB,IAAI,GAAG1B,KAAK,CAACwB,IAAI,CAACC,KAAK,CACtDzB,KAAK,CAACC,SAAS,CAACO,KAAK,CACvB,CAAC;QACDR,KAAK,CAACC,SAAS,CAAC0B,YAAY,CAAC,MAAM,EAAE,aAAa,EAAEN,CAAC,CAAC;MACxD;IACF,CAAC,CAAC,EAAC;IACH,IAAIrB,KAAK,CAACC,SAAS,CAAC2B,CAAC,EAAE;MACrB5B,KAAK,CAACC,SAAS,CAACJ,IAAI,CAAC,aAAa,CAAC,CAACS,MAAM,CAACoB,IAAI,GAAG1B,KAAK,CAACwB,IAAI,CAACC,KAAK,CAChEzB,KAAK,CAACC,SAAS,CAAC2B,CAClB,CAAC;IACH;EACF,CAAC,EAAE5B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}