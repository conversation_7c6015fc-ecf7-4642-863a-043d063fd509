{"ast": null, "code": "'use strict';\n\nmodule.exports = fortran;\nfortran.displayName = 'fortran';\nfortran.aliases = [];\nfunction fortran(Prism) {\n  Prism.languages.fortran = {\n    'quoted-number': {\n      pattern: /[BOZ](['\"])[A-F0-9]+\\1/i,\n      alias: 'number'\n    },\n    string: {\n      pattern: /(?:\\b\\w+_)?(['\"])(?:\\1\\1|&(?:\\r\\n?|\\n)(?:[ \\t]*!.*(?:\\r\\n?|\\n)|(?![ \\t]*!))|(?!\\1).)*(?:\\1|&)/,\n      inside: {\n        comment: {\n          pattern: /(&(?:\\r\\n?|\\n)\\s*)!.*/,\n          lookbehind: true\n        }\n      }\n    },\n    comment: {\n      pattern: /!.*/,\n      greedy: true\n    },\n    boolean: /\\.(?:FALSE|TRUE)\\.(?:_\\w+)?/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[ED][+-]?\\d+)?(?:_\\w+)?/i,\n    keyword: [\n    // Types\n    /\\b(?:CHARACTER|COMPLEX|DOUBLE ?PRECISION|INTEGER|LOGICAL|REAL)\\b/i,\n    // END statements\n    /\\b(?:END ?)?(?:BLOCK ?DATA|DO|FILE|FORALL|FUNCTION|IF|INTERFACE|MODULE(?! PROCEDURE)|PROGRAM|SELECT|SUBROUTINE|TYPE|WHERE)\\b/i,\n    // Statements\n    /\\b(?:ALLOCATABLE|ALLOCATE|BACKSPACE|CALL|CASE|CLOSE|COMMON|CONTAINS|CONTINUE|CYCLE|DATA|DEALLOCATE|DIMENSION|DO|END|EQUIVALENCE|EXIT|EXTERNAL|FORMAT|GO ?TO|IMPLICIT(?: NONE)?|INQUIRE|INTENT|INTRINSIC|MODULE PROCEDURE|NAMELIST|NULLIFY|OPEN|OPTIONAL|PARAMETER|POINTER|PRINT|PRIVATE|PUBLIC|READ|RETURN|REWIND|SAVE|SELECT|STOP|TARGET|WHILE|WRITE)\\b/i,\n    // Others\n    /\\b(?:ASSIGNMENT|DEFAULT|ELEMENTAL|ELSE|ELSEIF|ELSEWHERE|ENTRY|IN|INCLUDE|INOUT|KIND|NULL|ONLY|OPERATOR|OUT|PURE|RECURSIVE|RESULT|SEQUENCE|STAT|THEN|USE)\\b/i],\n    operator: [/\\*\\*|\\/\\/|=>|[=\\/]=|[<>]=?|::|[+\\-*=%]|\\.[A-Z]+\\./i, {\n      // Use lookbehind to prevent confusion with (/ /)\n      pattern: /(^|(?!\\().)\\/(?!\\))/,\n      lookbehind: true\n    }],\n    punctuation: /\\(\\/|\\/\\)|[(),;:&]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "fortran", "displayName", "aliases", "Prism", "languages", "pattern", "alias", "string", "inside", "comment", "lookbehind", "greedy", "boolean", "number", "keyword", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/fortran.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = fortran\nfortran.displayName = 'fortran'\nfortran.aliases = []\nfunction fortran(Prism) {\n  Prism.languages.fortran = {\n    'quoted-number': {\n      pattern: /[BOZ](['\"])[A-F0-9]+\\1/i,\n      alias: 'number'\n    },\n    string: {\n      pattern:\n        /(?:\\b\\w+_)?(['\"])(?:\\1\\1|&(?:\\r\\n?|\\n)(?:[ \\t]*!.*(?:\\r\\n?|\\n)|(?![ \\t]*!))|(?!\\1).)*(?:\\1|&)/,\n      inside: {\n        comment: {\n          pattern: /(&(?:\\r\\n?|\\n)\\s*)!.*/,\n          lookbehind: true\n        }\n      }\n    },\n    comment: {\n      pattern: /!.*/,\n      greedy: true\n    },\n    boolean: /\\.(?:FALSE|TRUE)\\.(?:_\\w+)?/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[ED][+-]?\\d+)?(?:_\\w+)?/i,\n    keyword: [\n      // Types\n      /\\b(?:CHARACTER|COMPLEX|DOUBLE ?PRECISION|INTEGER|LOGICAL|REAL)\\b/i, // END statements\n      /\\b(?:END ?)?(?:BLOCK ?DATA|DO|FILE|FORALL|FUNCTION|IF|INTERFACE|MODULE(?! PROCEDURE)|PROGRAM|SELECT|SUBROUTINE|TYPE|WHERE)\\b/i, // Statements\n      /\\b(?:ALLOCATABLE|ALLOCATE|BACKSPACE|CALL|CASE|CLOSE|COMMON|CONTAINS|CONTINUE|CYCLE|DATA|DEALLOCATE|DIMENSION|DO|END|EQUIVALENCE|EXIT|EXTERNAL|FORMAT|GO ?TO|IMPLICIT(?: NONE)?|INQUIRE|INTENT|INTRINSIC|MODULE PROCEDURE|NAMELIST|NULLIFY|OPEN|OPTIONAL|PARAMETER|POINTER|PRINT|PRIVATE|PUBLIC|READ|RETURN|REWIND|SAVE|SELECT|STOP|TARGET|WHILE|WRITE)\\b/i, // Others\n      /\\b(?:ASSIGNMENT|DEFAULT|ELEMENTAL|ELSE|ELSEIF|ELSEWHERE|ENTRY|IN|INCLUDE|INOUT|KIND|NULL|ONLY|OPERATOR|OUT|PURE|RECURSIVE|RESULT|SEQUENCE|STAT|THEN|USE)\\b/i\n    ],\n    operator: [\n      /\\*\\*|\\/\\/|=>|[=\\/]=|[<>]=?|::|[+\\-*=%]|\\.[A-Z]+\\./i,\n      {\n        // Use lookbehind to prevent confusion with (/ /)\n        pattern: /(^|(?!\\().)\\/(?!\\))/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /\\(\\/|\\/\\)|[(),;:&]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxB,eAAe,EAAE;MACfK,OAAO,EAAE,yBAAyB;MAClCC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EACL,+FAA+F;MACjGG,MAAM,EAAE;QACNC,OAAO,EAAE;UACPJ,OAAO,EAAE,uBAAuB;UAChCK,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDD,OAAO,EAAE;MACPJ,OAAO,EAAE,KAAK;MACdM,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,8BAA8B;IACvCC,MAAM,EAAE,wDAAwD;IAChEC,OAAO,EAAE;IACP;IACA,mEAAmE;IAAE;IACrE,+HAA+H;IAAE;IACjI,2VAA2V;IAAE;IAC7V,6JAA6J,CAC9J;IACDC,QAAQ,EAAE,CACR,oDAAoD,EACpD;MACE;MACAV,OAAO,EAAE,qBAAqB;MAC9BK,UAAU,EAAE;IACd,CAAC,CACF;IACDM,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}