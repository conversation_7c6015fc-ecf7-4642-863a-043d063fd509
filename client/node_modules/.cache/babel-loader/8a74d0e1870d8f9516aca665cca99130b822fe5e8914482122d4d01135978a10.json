{"ast": null, "code": "export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"none\",\n    \"textShadow\": \"0 1px rgba(0, 0, 0, 0.3)\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"#272822\",\n    \"textShadow\": \"0 1px rgba(0, 0, 0, 0.3)\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\",\n    \"borderRadius\": \"0.3em\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#272822\",\n    \"padding\": \".1em\",\n    \"borderRadius\": \".3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"#8292a2\"\n  },\n  \"prolog\": {\n    \"color\": \"#8292a2\"\n  },\n  \"doctype\": {\n    \"color\": \"#8292a2\"\n  },\n  \"cdata\": {\n    \"color\": \"#8292a2\"\n  },\n  \"punctuation\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"#f92672\"\n  },\n  \"tag\": {\n    \"color\": \"#f92672\"\n  },\n  \"constant\": {\n    \"color\": \"#f92672\"\n  },\n  \"symbol\": {\n    \"color\": \"#f92672\"\n  },\n  \"deleted\": {\n    \"color\": \"#f92672\"\n  },\n  \"boolean\": {\n    \"color\": \"#ae81ff\"\n  },\n  \"number\": {\n    \"color\": \"#ae81ff\"\n  },\n  \"selector\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"attr-name\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"string\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"char\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"builtin\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"inserted\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"operator\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"entity\": {\n    \"color\": \"#f8f8f2\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \".style .token.string\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"variable\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"atrule\": {\n    \"color\": \"#e6db74\"\n  },\n  \"attr-value\": {\n    \"color\": \"#e6db74\"\n  },\n  \"function\": {\n    \"color\": \"#e6db74\"\n  },\n  \"class-name\": {\n    \"color\": \"#e6db74\"\n  },\n  \"keyword\": {\n    \"color\": \"#66d9ef\"\n  },\n  \"regex\": {\n    \"color\": \"#fd971f\"\n  },\n  \"important\": {\n    \"color\": \"#fd971f\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  }\n};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/styles/prism/okaidia.js"], "sourcesContent": ["export default {\n  \"code[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"none\",\n    \"textShadow\": \"0 1px rgba(0, 0, 0, 0.3)\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\"\n  },\n  \"pre[class*=\\\"language-\\\"]\": {\n    \"color\": \"#f8f8f2\",\n    \"background\": \"#272822\",\n    \"textShadow\": \"0 1px rgba(0, 0, 0, 0.3)\",\n    \"fontFamily\": \"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace\",\n    \"fontSize\": \"1em\",\n    \"textAlign\": \"left\",\n    \"whiteSpace\": \"pre\",\n    \"wordSpacing\": \"normal\",\n    \"wordBreak\": \"normal\",\n    \"wordWrap\": \"normal\",\n    \"lineHeight\": \"1.5\",\n    \"MozTabSize\": \"4\",\n    \"OTabSize\": \"4\",\n    \"tabSize\": \"4\",\n    \"WebkitHyphens\": \"none\",\n    \"MozHyphens\": \"none\",\n    \"msHyphens\": \"none\",\n    \"hyphens\": \"none\",\n    \"padding\": \"1em\",\n    \"margin\": \".5em 0\",\n    \"overflow\": \"auto\",\n    \"borderRadius\": \"0.3em\"\n  },\n  \":not(pre) > code[class*=\\\"language-\\\"]\": {\n    \"background\": \"#272822\",\n    \"padding\": \".1em\",\n    \"borderRadius\": \".3em\",\n    \"whiteSpace\": \"normal\"\n  },\n  \"comment\": {\n    \"color\": \"#8292a2\"\n  },\n  \"prolog\": {\n    \"color\": \"#8292a2\"\n  },\n  \"doctype\": {\n    \"color\": \"#8292a2\"\n  },\n  \"cdata\": {\n    \"color\": \"#8292a2\"\n  },\n  \"punctuation\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"namespace\": {\n    \"Opacity\": \".7\"\n  },\n  \"property\": {\n    \"color\": \"#f92672\"\n  },\n  \"tag\": {\n    \"color\": \"#f92672\"\n  },\n  \"constant\": {\n    \"color\": \"#f92672\"\n  },\n  \"symbol\": {\n    \"color\": \"#f92672\"\n  },\n  \"deleted\": {\n    \"color\": \"#f92672\"\n  },\n  \"boolean\": {\n    \"color\": \"#ae81ff\"\n  },\n  \"number\": {\n    \"color\": \"#ae81ff\"\n  },\n  \"selector\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"attr-name\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"string\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"char\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"builtin\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"inserted\": {\n    \"color\": \"#a6e22e\"\n  },\n  \"operator\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"entity\": {\n    \"color\": \"#f8f8f2\",\n    \"cursor\": \"help\"\n  },\n  \"url\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \".language-css .token.string\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \".style .token.string\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"variable\": {\n    \"color\": \"#f8f8f2\"\n  },\n  \"atrule\": {\n    \"color\": \"#e6db74\"\n  },\n  \"attr-value\": {\n    \"color\": \"#e6db74\"\n  },\n  \"function\": {\n    \"color\": \"#e6db74\"\n  },\n  \"class-name\": {\n    \"color\": \"#e6db74\"\n  },\n  \"keyword\": {\n    \"color\": \"#66d9ef\"\n  },\n  \"regex\": {\n    \"color\": \"#fd971f\"\n  },\n  \"important\": {\n    \"color\": \"#fd971f\",\n    \"fontWeight\": \"bold\"\n  },\n  \"bold\": {\n    \"fontWeight\": \"bold\"\n  },\n  \"italic\": {\n    \"fontStyle\": \"italic\"\n  }\n};"], "mappings": "AAAA,eAAe;EACb,4BAA4B,EAAE;IAC5B,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE,0BAA0B;IACxC,YAAY,EAAE,2DAA2D;IACzE,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE;EACb,CAAC;EACD,2BAA2B,EAAE;IAC3B,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,SAAS;IACvB,YAAY,EAAE,0BAA0B;IACxC,YAAY,EAAE,2DAA2D;IACzE,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,MAAM;IACnB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,QAAQ;IACvB,WAAW,EAAE,QAAQ;IACrB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,GAAG;IACjB,UAAU,EAAE,GAAG;IACf,SAAS,EAAE,GAAG;IACd,eAAe,EAAE,MAAM;IACvB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE,MAAM;IAClB,cAAc,EAAE;EAClB,CAAC;EACD,wCAAwC,EAAE;IACxC,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,MAAM;IACjB,cAAc,EAAE,MAAM;IACtB,YAAY,EAAE;EAChB,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,aAAa,EAAE;IACb,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,SAAS,EAAE;EACb,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACN,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE;EACZ,CAAC;EACD,KAAK,EAAE;IACL,OAAO,EAAE;EACX,CAAC;EACD,6BAA6B,EAAE;IAC7B,OAAO,EAAE;EACX,CAAC;EACD,sBAAsB,EAAE;IACtB,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,QAAQ,EAAE;IACR,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,UAAU,EAAE;IACV,OAAO,EAAE;EACX,CAAC;EACD,YAAY,EAAE;IACZ,OAAO,EAAE;EACX,CAAC;EACD,SAAS,EAAE;IACT,OAAO,EAAE;EACX,CAAC;EACD,OAAO,EAAE;IACP,OAAO,EAAE;EACX,CAAC;EACD,WAAW,EAAE;IACX,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE;EAChB,CAAC;EACD,MAAM,EAAE;IACN,YAAY,EAAE;EAChB,CAAC;EACD,QAAQ,EAAE;IACR,WAAW,EAAE;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}