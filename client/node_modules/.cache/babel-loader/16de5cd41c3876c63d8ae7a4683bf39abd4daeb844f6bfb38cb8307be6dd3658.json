{"ast": null, "code": "/*\nLanguage: Packet Filter config\nDescription: pf.conf — packet filter configuration file (OpenBSD)\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://man.openbsd.org/pf.conf\nCategory: config\n*/\n\nfunction pf(hljs) {\n  const MACRO = {\n    className: 'variable',\n    begin: /\\$[\\w\\d#@][\\w\\d_]*/\n  };\n  const TABLE = {\n    className: 'variable',\n    begin: /<(?!\\/)/,\n    end: />/\n  };\n  return {\n    name: 'Packet Filter config',\n    aliases: ['pf.conf'],\n    keywords: {\n      $pattern: /[a-z0-9_<>-]+/,\n      built_in:\n      /* block match pass are \"actions\" in pf.conf(5), the rest are\n       * lexically similar top-level commands.\n       */\n      'block match pass load anchor|5 antispoof|10 set table',\n      keyword: 'in out log quick on rdomain inet inet6 proto from port os to route ' + 'allow-opts divert-packet divert-reply divert-to flags group icmp-type ' + 'icmp6-type label once probability recieved-on rtable prio queue ' + 'tos tag tagged user keep fragment for os drop ' + 'af-to|10 binat-to|10 nat-to|10 rdr-to|10 bitmask least-stats random round-robin ' + 'source-hash static-port ' + 'dup-to reply-to route-to ' + 'parent bandwidth default min max qlimit ' + 'block-policy debug fingerprints hostid limit loginterface optimization ' + 'reassemble ruleset-optimization basic none profile skip state-defaults ' + 'state-policy timeout ' + 'const counters persist ' + 'no modulate synproxy state|5 floating if-bound no-sync pflow|10 sloppy ' + 'source-track global rule max-src-nodes max-src-states max-src-conn ' + 'max-src-conn-rate overload flush ' + 'scrub|5 max-mss min-ttl no-df|10 random-id',\n      literal: 'all any no-route self urpf-failed egress|5 unknown'\n    },\n    contains: [hljs.HASH_COMMENT_MODE, hljs.NUMBER_MODE, hljs.QUOTE_STRING_MODE, MACRO, TABLE]\n  };\n}\nmodule.exports = pf;", "map": {"version": 3, "names": ["pf", "hljs", "MACRO", "className", "begin", "TABLE", "end", "name", "aliases", "keywords", "$pattern", "built_in", "keyword", "literal", "contains", "HASH_COMMENT_MODE", "NUMBER_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/pf.js"], "sourcesContent": ["/*\nLanguage: Packet Filter config\nDescription: pf.conf — packet filter configuration file (OpenBSD)\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://man.openbsd.org/pf.conf\nCategory: config\n*/\n\nfunction pf(hljs) {\n  const MACRO = {\n    className: 'variable',\n    begin: /\\$[\\w\\d#@][\\w\\d_]*/\n  };\n  const TABLE = {\n    className: 'variable',\n    begin: /<(?!\\/)/,\n    end: />/\n  };\n\n  return {\n    name: 'Packet Filter config',\n    aliases: [ 'pf.conf' ],\n    keywords: {\n      $pattern: /[a-z0-9_<>-]+/,\n      built_in: /* block match pass are \"actions\" in pf.conf(5), the rest are\n                 * lexically similar top-level commands.\n                 */\n        'block match pass load anchor|5 antispoof|10 set table',\n      keyword:\n        'in out log quick on rdomain inet inet6 proto from port os to route ' +\n        'allow-opts divert-packet divert-reply divert-to flags group icmp-type ' +\n        'icmp6-type label once probability recieved-on rtable prio queue ' +\n        'tos tag tagged user keep fragment for os drop ' +\n        'af-to|10 binat-to|10 nat-to|10 rdr-to|10 bitmask least-stats random round-robin ' +\n        'source-hash static-port ' +\n        'dup-to reply-to route-to ' +\n        'parent bandwidth default min max qlimit ' +\n        'block-policy debug fingerprints hostid limit loginterface optimization ' +\n        'reassemble ruleset-optimization basic none profile skip state-defaults ' +\n        'state-policy timeout ' +\n        'const counters persist ' +\n        'no modulate synproxy state|5 floating if-bound no-sync pflow|10 sloppy ' +\n        'source-track global rule max-src-nodes max-src-states max-src-conn ' +\n        'max-src-conn-rate overload flush ' +\n        'scrub|5 max-mss min-ttl no-df|10 random-id',\n      literal:\n        'all any no-route self urpf-failed egress|5 unknown'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      MACRO,\n      TABLE\n    ]\n  };\n}\n\nmodule.exports = pf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,EAAEA,CAACC,IAAI,EAAE;EAChB,MAAMC,KAAK,GAAG;IACZC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,KAAK,GAAG;IACZF,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE;EACP,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,EAAE,CAAE,SAAS,CAAE;IACtBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,eAAe;MACzBC,QAAQ;MAAE;AAChB;AACA;MACQ,uDAAuD;MACzDC,OAAO,EACL,qEAAqE,GACrE,wEAAwE,GACxE,kEAAkE,GAClE,gDAAgD,GAChD,kFAAkF,GAClF,0BAA0B,GAC1B,2BAA2B,GAC3B,0CAA0C,GAC1C,yEAAyE,GACzE,yEAAyE,GACzE,uBAAuB,GACvB,yBAAyB,GACzB,yEAAyE,GACzE,qEAAqE,GACrE,mCAAmC,GACnC,4CAA4C;MAC9CC,OAAO,EACL;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRb,IAAI,CAACc,iBAAiB,EACtBd,IAAI,CAACe,WAAW,EAChBf,IAAI,CAACgB,iBAAiB,EACtBf,KAAK,EACLG,KAAK;EAET,CAAC;AACH;AAEAa,MAAM,CAACC,OAAO,GAAGnB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}