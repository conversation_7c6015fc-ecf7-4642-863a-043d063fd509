{"ast": null, "code": "'use strict';\n\nmodule.exports = dnsZoneFile;\ndnsZoneFile.displayName = 'dnsZoneFile';\ndnsZoneFile.aliases = [];\nfunction dnsZoneFile(Prism) {\n  Prism.languages['dns-zone-file'] = {\n    comment: /;.*/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    variable: [{\n      pattern: /(^\\$ORIGIN[ \\t]+)\\S+/m,\n      lookbehind: true\n    }, {\n      pattern: /(^|\\s)@(?=\\s|$)/,\n      lookbehind: true\n    }],\n    keyword: /^\\$(?:INCLUDE|ORIGIN|TTL)(?=\\s|$)/m,\n    class: {\n      // https://tools.ietf.org/html/rfc1035#page-13\n      pattern: /(^|\\s)(?:CH|CS|HS|IN)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    type: {\n      // https://en.wikipedia.org/wiki/List_of_DNS_record_types\n      pattern: /(^|\\s)(?:A|A6|AAAA|AFSDB|APL|ATMA|CAA|CDNSKEY|CDS|CERT|CNAME|DHCID|DLV|DNAME|DNSKEY|DS|EID|GID|GPOS|HINFO|HIP|IPSECKEY|ISDN|KEY|KX|LOC|MAILA|MAILB|MB|MD|MF|MG|MINFO|MR|MX|NAPTR|NB|NBSTAT|NIMLOC|NINFO|NS|NSAP|NSAP-PTR|NSEC|NSEC3|NSEC3PARAM|NULL|NXT|OPENPGPKEY|PTR|PX|RKEY|RP|RRSIG|RT|SIG|SINK|SMIMEA|SOA|SPF|SRV|SSHFP|TA|TKEY|TLSA|TSIG|TXT|UID|UINFO|UNSPEC|URI|WKS|X25)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    punctuation: /[()]/\n  };\n  Prism.languages['dns-zone'] = Prism.languages['dns-zone-file'];\n}", "map": {"version": 3, "names": ["module", "exports", "dnsZoneFile", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "variable", "lookbehind", "keyword", "class", "alias", "type", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/dns-zone-file.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dnsZoneFile\ndnsZoneFile.displayName = 'dnsZoneFile'\ndnsZoneFile.aliases = []\nfunction dnsZoneFile(Prism) {\n  Prism.languages['dns-zone-file'] = {\n    comment: /;.*/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    variable: [\n      {\n        pattern: /(^\\$ORIGIN[ \\t]+)\\S+/m,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|\\s)@(?=\\s|$)/,\n        lookbehind: true\n      }\n    ],\n    keyword: /^\\$(?:INCLUDE|ORIGIN|TTL)(?=\\s|$)/m,\n    class: {\n      // https://tools.ietf.org/html/rfc1035#page-13\n      pattern: /(^|\\s)(?:CH|CS|HS|IN)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    type: {\n      // https://en.wikipedia.org/wiki/List_of_DNS_record_types\n      pattern:\n        /(^|\\s)(?:A|A6|AAAA|AFSDB|APL|ATMA|CAA|CDNSKEY|CDS|CERT|CNAME|DHCID|DLV|DNAME|DNSKEY|DS|EID|GID|GPOS|HINFO|HIP|IPSECKEY|ISDN|KEY|KX|LOC|MAILA|MAILB|MB|MD|MF|MG|MINFO|MR|MX|NAPTR|NB|NBSTAT|NIMLOC|NINFO|NS|NSAP|NSAP-PTR|NSEC|NSEC3|NSEC3PARAM|NULL|NXT|OPENPGPKEY|PTR|PX|RKEY|RP|RRSIG|RT|SIG|SINK|SMIMEA|SOA|SPF|SRV|SSHFP|TA|TKEY|TLSA|TSIG|TXT|UID|UINFO|UNSPEC|URI|WKS|X25)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    punctuation: /[()]/\n  }\n  Prism.languages['dns-zone'] = Prism.languages['dns-zone-file']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,WAAW;AAC5BA,WAAW,CAACC,WAAW,GAAG,aAAa;AACvCD,WAAW,CAACE,OAAO,GAAG,EAAE;AACxB,SAASF,WAAWA,CAACG,KAAK,EAAE;EAC1BA,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,GAAG;IACjCC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE,CACR;MACEF,OAAO,EAAE,uBAAuB;MAChCG,UAAU,EAAE;IACd,CAAC,EACD;MACEH,OAAO,EAAE,iBAAiB;MAC1BG,UAAU,EAAE;IACd,CAAC,CACF;IACDC,OAAO,EAAE,oCAAoC;IAC7CC,KAAK,EAAE;MACL;MACAL,OAAO,EAAE,+BAA+B;MACxCG,UAAU,EAAE,IAAI;MAChBG,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJ;MACAP,OAAO,EACL,0XAA0X;MAC5XG,UAAU,EAAE,IAAI;MAChBG,KAAK,EAAE;IACT,CAAC;IACDE,WAAW,EAAE;EACf,CAAC;EACDZ,KAAK,CAACC,SAAS,CAAC,UAAU,CAAC,GAAGD,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}