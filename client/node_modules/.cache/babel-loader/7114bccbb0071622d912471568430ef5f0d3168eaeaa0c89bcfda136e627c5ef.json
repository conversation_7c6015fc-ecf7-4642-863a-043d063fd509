{"ast": null, "code": "'use strict';\n\nmodule.exports = regex;\nregex.displayName = 'regex';\nregex.aliases = [];\nfunction regex(Prism) {\n  ;\n  (function (Prism) {\n    var specialEscape = {\n      pattern: /\\\\[\\\\(){}[\\]^$+*?|.]/,\n      alias: 'escape'\n    };\n    var escape = /\\\\(?:x[\\da-fA-F]{2}|u[\\da-fA-F]{4}|u\\{[\\da-fA-F]+\\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/;\n    var charSet = {\n      pattern: /\\.|\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    };\n    var charSetWithoutDot = {\n      pattern: /\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    };\n    var rangeChar = '(?:[^\\\\\\\\-]|' + escape.source + ')';\n    var range = RegExp(rangeChar + '-' + rangeChar); // the name of a capturing group\n    var groupName = {\n      pattern: /(<|')[^<>']+(?=[>']$)/,\n      lookbehind: true,\n      alias: 'variable'\n    };\n    Prism.languages.regex = {\n      'char-class': {\n        pattern: /((?:^|[^\\\\])(?:\\\\\\\\)*)\\[(?:[^\\\\\\]]|\\\\[\\s\\S])*\\]/,\n        lookbehind: true,\n        inside: {\n          'char-class-negation': {\n            pattern: /(^\\[)\\^/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          'char-class-punctuation': {\n            pattern: /^\\[|\\]$/,\n            alias: 'punctuation'\n          },\n          range: {\n            pattern: range,\n            inside: {\n              escape: escape,\n              'range-punctuation': {\n                pattern: /-/,\n                alias: 'operator'\n              }\n            }\n          },\n          'special-escape': specialEscape,\n          'char-set': charSetWithoutDot,\n          escape: escape\n        }\n      },\n      'special-escape': specialEscape,\n      'char-set': charSet,\n      backreference: [{\n        // a backreference which is not an octal escape\n        pattern: /\\\\(?![123][0-7]{2})[1-9]/,\n        alias: 'keyword'\n      }, {\n        pattern: /\\\\k<[^<>']+>/,\n        alias: 'keyword',\n        inside: {\n          'group-name': groupName\n        }\n      }],\n      anchor: {\n        pattern: /[$^]|\\\\[ABbGZz]/,\n        alias: 'function'\n      },\n      escape: escape,\n      group: [{\n        // https://docs.oracle.com/javase/10/docs/api/java/util/regex/Pattern.html\n        // https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expression-language-quick-reference?view=netframework-4.7.2#grouping-constructs\n        // (), (?<name>), (?'name'), (?>), (?:), (?=), (?!), (?<=), (?<!), (?is-m), (?i-m:)\n        pattern: /\\((?:\\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,\n        alias: 'punctuation',\n        inside: {\n          'group-name': groupName\n        }\n      }, {\n        pattern: /\\)/,\n        alias: 'punctuation'\n      }],\n      quantifier: {\n        pattern: /(?:[+*?]|\\{\\d+(?:,\\d*)?\\})[?+]?/,\n        alias: 'number'\n      },\n      alternation: {\n        pattern: /\\|/,\n        alias: 'keyword'\n      }\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "regex", "displayName", "aliases", "Prism", "specialEscape", "pattern", "alias", "escape", "charSet", "charSetWithoutDot", "rangeChar", "source", "range", "RegExp", "groupName", "lookbehind", "languages", "inside", "backreference", "anchor", "group", "quantifier", "alternation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/regex.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = regex\nregex.displayName = 'regex'\nregex.aliases = []\nfunction regex(Prism) {\n  ;(function (Prism) {\n    var specialEscape = {\n      pattern: /\\\\[\\\\(){}[\\]^$+*?|.]/,\n      alias: 'escape'\n    }\n    var escape =\n      /\\\\(?:x[\\da-fA-F]{2}|u[\\da-fA-F]{4}|u\\{[\\da-fA-F]+\\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/\n    var charSet = {\n      pattern: /\\.|\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    }\n    var charSetWithoutDot = {\n      pattern: /\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    }\n    var rangeChar = '(?:[^\\\\\\\\-]|' + escape.source + ')'\n    var range = RegExp(rangeChar + '-' + rangeChar) // the name of a capturing group\n    var groupName = {\n      pattern: /(<|')[^<>']+(?=[>']$)/,\n      lookbehind: true,\n      alias: 'variable'\n    }\n    Prism.languages.regex = {\n      'char-class': {\n        pattern: /((?:^|[^\\\\])(?:\\\\\\\\)*)\\[(?:[^\\\\\\]]|\\\\[\\s\\S])*\\]/,\n        lookbehind: true,\n        inside: {\n          'char-class-negation': {\n            pattern: /(^\\[)\\^/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          'char-class-punctuation': {\n            pattern: /^\\[|\\]$/,\n            alias: 'punctuation'\n          },\n          range: {\n            pattern: range,\n            inside: {\n              escape: escape,\n              'range-punctuation': {\n                pattern: /-/,\n                alias: 'operator'\n              }\n            }\n          },\n          'special-escape': specialEscape,\n          'char-set': charSetWithoutDot,\n          escape: escape\n        }\n      },\n      'special-escape': specialEscape,\n      'char-set': charSet,\n      backreference: [\n        {\n          // a backreference which is not an octal escape\n          pattern: /\\\\(?![123][0-7]{2})[1-9]/,\n          alias: 'keyword'\n        },\n        {\n          pattern: /\\\\k<[^<>']+>/,\n          alias: 'keyword',\n          inside: {\n            'group-name': groupName\n          }\n        }\n      ],\n      anchor: {\n        pattern: /[$^]|\\\\[ABbGZz]/,\n        alias: 'function'\n      },\n      escape: escape,\n      group: [\n        {\n          // https://docs.oracle.com/javase/10/docs/api/java/util/regex/Pattern.html\n          // https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expression-language-quick-reference?view=netframework-4.7.2#grouping-constructs\n          // (), (?<name>), (?'name'), (?>), (?:), (?=), (?!), (?<=), (?<!), (?is-m), (?i-m:)\n          pattern:\n            /\\((?:\\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,\n          alias: 'punctuation',\n          inside: {\n            'group-name': groupName\n          }\n        },\n        {\n          pattern: /\\)/,\n          alias: 'punctuation'\n        }\n      ],\n      quantifier: {\n        pattern: /(?:[+*?]|\\{\\d+(?:,\\d*)?\\})[?+]?/,\n        alias: 'number'\n      },\n      alternation: {\n        pattern: /\\|/,\n        alias: 'keyword'\n      }\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,aAAa,GAAG;MAClBC,OAAO,EAAE,sBAAsB;MAC/BC,KAAK,EAAE;IACT,CAAC;IACD,IAAIC,MAAM,GACR,4FAA4F;IAC9F,IAAIC,OAAO,GAAG;MACZH,OAAO,EAAE,2BAA2B;MACpCC,KAAK,EAAE;IACT,CAAC;IACD,IAAIG,iBAAiB,GAAG;MACtBJ,OAAO,EAAE,wBAAwB;MACjCC,KAAK,EAAE;IACT,CAAC;IACD,IAAII,SAAS,GAAG,cAAc,GAAGH,MAAM,CAACI,MAAM,GAAG,GAAG;IACpD,IAAIC,KAAK,GAAGC,MAAM,CAACH,SAAS,GAAG,GAAG,GAAGA,SAAS,CAAC,EAAC;IAChD,IAAII,SAAS,GAAG;MACdT,OAAO,EAAE,uBAAuB;MAChCU,UAAU,EAAE,IAAI;MAChBT,KAAK,EAAE;IACT,CAAC;IACDH,KAAK,CAACa,SAAS,CAAChB,KAAK,GAAG;MACtB,YAAY,EAAE;QACZK,OAAO,EAAE,iDAAiD;QAC1DU,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACN,qBAAqB,EAAE;YACrBZ,OAAO,EAAE,SAAS;YAClBU,UAAU,EAAE,IAAI;YAChBT,KAAK,EAAE;UACT,CAAC;UACD,wBAAwB,EAAE;YACxBD,OAAO,EAAE,SAAS;YAClBC,KAAK,EAAE;UACT,CAAC;UACDM,KAAK,EAAE;YACLP,OAAO,EAAEO,KAAK;YACdK,MAAM,EAAE;cACNV,MAAM,EAAEA,MAAM;cACd,mBAAmB,EAAE;gBACnBF,OAAO,EAAE,GAAG;gBACZC,KAAK,EAAE;cACT;YACF;UACF,CAAC;UACD,gBAAgB,EAAEF,aAAa;UAC/B,UAAU,EAAEK,iBAAiB;UAC7BF,MAAM,EAAEA;QACV;MACF,CAAC;MACD,gBAAgB,EAAEH,aAAa;MAC/B,UAAU,EAAEI,OAAO;MACnBU,aAAa,EAAE,CACb;QACE;QACAb,OAAO,EAAE,0BAA0B;QACnCC,KAAK,EAAE;MACT,CAAC,EACD;QACED,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,SAAS;QAChBW,MAAM,EAAE;UACN,YAAY,EAAEH;QAChB;MACF,CAAC,CACF;MACDK,MAAM,EAAE;QACNd,OAAO,EAAE,iBAAiB;QAC1BC,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAEA,MAAM;MACda,KAAK,EAAE,CACL;QACE;QACA;QACA;QACAf,OAAO,EACL,6EAA6E;QAC/EC,KAAK,EAAE,aAAa;QACpBW,MAAM,EAAE;UACN,YAAY,EAAEH;QAChB;MACF,CAAC,EACD;QACET,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC,CACF;MACDe,UAAU,EAAE;QACVhB,OAAO,EAAE,iCAAiC;QAC1CC,KAAK,EAAE;MACT,CAAC;MACDgB,WAAW,EAAE;QACXjB,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT;IACF,CAAC;EACH,CAAC,EAAEH,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}