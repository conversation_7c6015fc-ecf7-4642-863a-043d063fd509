{"ast": null, "code": "'use strict';\n\nmodule.exports = icuMessageFormat;\nicuMessageFormat.displayName = 'icuMessageFormat';\nicuMessageFormat.aliases = [];\nfunction icuMessageFormat(Prism) {\n  // https://unicode-org.github.io/icu/userguide/format_parse/messages/\n  // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/MessageFormat.html\n  ;\n  (function (Prism) {\n    /**\n     * @param {string} source\n     * @param {number} level\n     * @returns {string}\n     */\n    function nested(source, level) {\n      if (level <= 0) {\n        return /[]/.source;\n      } else {\n        return source.replace(/<SELF>/g, function () {\n          return nested(source, level - 1);\n        });\n      }\n    }\n    var stringPattern = /'[{}:=,](?:[^']|'')*'(?!')/;\n    var escape = {\n      pattern: /''/,\n      greedy: true,\n      alias: 'operator'\n    };\n    var string = {\n      pattern: stringPattern,\n      greedy: true,\n      inside: {\n        escape: escape\n      }\n    };\n    var argumentSource = nested(/\\{(?:[^{}']|'(?![{},'])|''|<STR>|<SELF>)*\\}/.source.replace(/<STR>/g, function () {\n      return stringPattern.source;\n    }), 8);\n    var nestedMessage = {\n      pattern: RegExp(argumentSource),\n      inside: {\n        message: {\n          pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        'message-delimiter': {\n          pattern: /./,\n          alias: 'punctuation'\n        }\n      }\n    };\n    Prism.languages['icu-message-format'] = {\n      argument: {\n        pattern: RegExp(argumentSource),\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n            lookbehind: true,\n            inside: {\n              'argument-name': {\n                pattern: /^(\\s*)[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'choice-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4c/classicu_1_1ChoiceFormat.html#details\n                pattern: /^(\\s*,\\s*choice\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  punctuation: /\\|/,\n                  range: {\n                    pattern: /^(\\s*)[+-]?(?:\\d+(?:\\.\\d*)?|\\u221e)\\s*[<#\\u2264]/,\n                    lookbehind: true,\n                    inside: {\n                      operator: /[<#\\u2264]/,\n                      number: /\\S+/\n                    }\n                  },\n                  rest: null // see below\n                }\n              },\n              'plural-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/PluralFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern: /^(\\s*,\\s*(?:plural|selectordinal)\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  offset: /^offset:\\s*\\d+/,\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /=\\d+|[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^(?:few|many|one|other|two|zero)$/\n                    }\n                  }\n                }\n              },\n              'select-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/SelectFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern: /^(\\s*,\\s*select\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^other$/\n                    }\n                  }\n                }\n              },\n              keyword: /\\b(?:choice|plural|select|selectordinal)\\b/,\n              'arg-type': {\n                pattern: /\\b(?:date|duration|number|ordinal|spellout|time)\\b/,\n                alias: 'keyword'\n              },\n              'arg-skeleton': {\n                pattern: /(,\\s*)::[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'arg-style': {\n                pattern: /(,\\s*)(?:currency|full|integer|long|medium|percent|short)(?=\\s*$)/,\n                lookbehind: true\n              },\n              'arg-style-text': {\n                pattern: RegExp(/(^\\s*,\\s*(?=\\S))/.source + nested(/(?:[^{}']|'[^']*'|\\{(?:<SELF>)?\\})+/.source, 8) + '$'),\n                lookbehind: true,\n                alias: 'string'\n              },\n              punctuation: /,/\n            }\n          },\n          'argument-delimiter': {\n            pattern: /./,\n            alias: 'operator'\n          }\n        }\n      },\n      escape: escape,\n      string: string\n    };\n    nestedMessage.inside.message.inside = Prism.languages['icu-message-format'];\n    Prism.languages['icu-message-format'].argument.inside.content.inside['choice-style'].inside.rest = Prism.languages['icu-message-format'];\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "icuMessageFormat", "displayName", "aliases", "Prism", "nested", "source", "level", "replace", "stringPattern", "escape", "pattern", "greedy", "alias", "string", "inside", "argumentSource", "nestedMessage", "RegExp", "message", "lookbehind", "languages", "argument", "content", "punctuation", "range", "operator", "number", "rest", "offset", "selector", "keyword"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/icu-message-format.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = icuMessageFormat\nicuMessageFormat.displayName = 'icuMessageFormat'\nicuMessageFormat.aliases = []\nfunction icuMessageFormat(Prism) {\n  // https://unicode-org.github.io/icu/userguide/format_parse/messages/\n  // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/MessageFormat.html\n  ;(function (Prism) {\n    /**\n     * @param {string} source\n     * @param {number} level\n     * @returns {string}\n     */\n    function nested(source, level) {\n      if (level <= 0) {\n        return /[]/.source\n      } else {\n        return source.replace(/<SELF>/g, function () {\n          return nested(source, level - 1)\n        })\n      }\n    }\n    var stringPattern = /'[{}:=,](?:[^']|'')*'(?!')/\n    var escape = {\n      pattern: /''/,\n      greedy: true,\n      alias: 'operator'\n    }\n    var string = {\n      pattern: stringPattern,\n      greedy: true,\n      inside: {\n        escape: escape\n      }\n    }\n    var argumentSource = nested(\n      /\\{(?:[^{}']|'(?![{},'])|''|<STR>|<SELF>)*\\}/.source.replace(\n        /<STR>/g,\n        function () {\n          return stringPattern.source\n        }\n      ),\n      8\n    )\n    var nestedMessage = {\n      pattern: RegExp(argumentSource),\n      inside: {\n        message: {\n          pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        'message-delimiter': {\n          pattern: /./,\n          alias: 'punctuation'\n        }\n      }\n    }\n    Prism.languages['icu-message-format'] = {\n      argument: {\n        pattern: RegExp(argumentSource),\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n            lookbehind: true,\n            inside: {\n              'argument-name': {\n                pattern: /^(\\s*)[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'choice-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4c/classicu_1_1ChoiceFormat.html#details\n                pattern: /^(\\s*,\\s*choice\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  punctuation: /\\|/,\n                  range: {\n                    pattern: /^(\\s*)[+-]?(?:\\d+(?:\\.\\d*)?|\\u221e)\\s*[<#\\u2264]/,\n                    lookbehind: true,\n                    inside: {\n                      operator: /[<#\\u2264]/,\n                      number: /\\S+/\n                    }\n                  },\n                  rest: null // see below\n                }\n              },\n              'plural-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/PluralFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern:\n                  /^(\\s*,\\s*(?:plural|selectordinal)\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  offset: /^offset:\\s*\\d+/,\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /=\\d+|[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^(?:few|many|one|other|two|zero)$/\n                    }\n                  }\n                }\n              },\n              'select-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/SelectFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern: /^(\\s*,\\s*select\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^other$/\n                    }\n                  }\n                }\n              },\n              keyword: /\\b(?:choice|plural|select|selectordinal)\\b/,\n              'arg-type': {\n                pattern: /\\b(?:date|duration|number|ordinal|spellout|time)\\b/,\n                alias: 'keyword'\n              },\n              'arg-skeleton': {\n                pattern: /(,\\s*)::[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'arg-style': {\n                pattern:\n                  /(,\\s*)(?:currency|full|integer|long|medium|percent|short)(?=\\s*$)/,\n                lookbehind: true\n              },\n              'arg-style-text': {\n                pattern: RegExp(\n                  /(^\\s*,\\s*(?=\\S))/.source +\n                    nested(/(?:[^{}']|'[^']*'|\\{(?:<SELF>)?\\})+/.source, 8) +\n                    '$'\n                ),\n                lookbehind: true,\n                alias: 'string'\n              },\n              punctuation: /,/\n            }\n          },\n          'argument-delimiter': {\n            pattern: /./,\n            alias: 'operator'\n          }\n        }\n      },\n      escape: escape,\n      string: string\n    }\n    nestedMessage.inside.message.inside = Prism.languages['icu-message-format']\n    Prism.languages['icu-message-format'].argument.inside.content.inside[\n      'choice-style'\n    ].inside.rest = Prism.languages['icu-message-format']\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,gBAAgB;AACjCA,gBAAgB,CAACC,WAAW,GAAG,kBAAkB;AACjDD,gBAAgB,CAACE,OAAO,GAAG,EAAE;AAC7B,SAASF,gBAAgBA,CAACG,KAAK,EAAE;EAC/B;EACA;EACA;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACI,SAASC,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAE;MAC7B,IAAIA,KAAK,IAAI,CAAC,EAAE;QACd,OAAO,IAAI,CAACD,MAAM;MACpB,CAAC,MAAM;QACL,OAAOA,MAAM,CAACE,OAAO,CAAC,SAAS,EAAE,YAAY;UAC3C,OAAOH,MAAM,CAACC,MAAM,EAAEC,KAAK,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC;MACJ;IACF;IACA,IAAIE,aAAa,GAAG,4BAA4B;IAChD,IAAIC,MAAM,GAAG;MACXC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT,CAAC;IACD,IAAIC,MAAM,GAAG;MACXH,OAAO,EAAEF,aAAa;MACtBG,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACNL,MAAM,EAAEA;MACV;IACF,CAAC;IACD,IAAIM,cAAc,GAAGX,MAAM,CACzB,6CAA6C,CAACC,MAAM,CAACE,OAAO,CAC1D,QAAQ,EACR,YAAY;MACV,OAAOC,aAAa,CAACH,MAAM;IAC7B,CACF,CAAC,EACD,CACF,CAAC;IACD,IAAIW,aAAa,GAAG;MAClBN,OAAO,EAAEO,MAAM,CAACF,cAAc,CAAC;MAC/BD,MAAM,EAAE;QACNI,OAAO,EAAE;UACPR,OAAO,EAAE,qBAAqB;UAC9BS,UAAU,EAAE,IAAI;UAChBL,MAAM,EAAE,IAAI,CAAC;QACf,CAAC;QACD,mBAAmB,EAAE;UACnBJ,OAAO,EAAE,GAAG;UACZE,KAAK,EAAE;QACT;MACF;IACF,CAAC;IACDT,KAAK,CAACiB,SAAS,CAAC,oBAAoB,CAAC,GAAG;MACtCC,QAAQ,EAAE;QACRX,OAAO,EAAEO,MAAM,CAACF,cAAc,CAAC;QAC/BJ,MAAM,EAAE,IAAI;QACZG,MAAM,EAAE;UACNQ,OAAO,EAAE;YACPZ,OAAO,EAAE,qBAAqB;YAC9BS,UAAU,EAAE,IAAI;YAChBL,MAAM,EAAE;cACN,eAAe,EAAE;gBACfJ,OAAO,EAAE,mBAAmB;gBAC5BS,UAAU,EAAE;cACd,CAAC;cACD,cAAc,EAAE;gBACd;gBACAT,OAAO,EAAE,yCAAyC;gBAClDS,UAAU,EAAE,IAAI;gBAChBL,MAAM,EAAE;kBACNS,WAAW,EAAE,IAAI;kBACjBC,KAAK,EAAE;oBACLd,OAAO,EAAE,kDAAkD;oBAC3DS,UAAU,EAAE,IAAI;oBAChBL,MAAM,EAAE;sBACNW,QAAQ,EAAE,YAAY;sBACtBC,MAAM,EAAE;oBACV;kBACF,CAAC;kBACDC,IAAI,EAAE,IAAI,CAAC;gBACb;cACF,CAAC;cACD,cAAc,EAAE;gBACd;gBACAjB,OAAO,EACL,2DAA2D;gBAC7DS,UAAU,EAAE,IAAI;gBAChBL,MAAM,EAAE;kBACNc,MAAM,EAAE,gBAAgB;kBACxB,gBAAgB,EAAEZ,aAAa;kBAC/Ba,QAAQ,EAAE;oBACRnB,OAAO,EAAE,kBAAkB;oBAC3BI,MAAM,EAAE;sBACNgB,OAAO,EAAE;oBACX;kBACF;gBACF;cACF,CAAC;cACD,cAAc,EAAE;gBACd;gBACApB,OAAO,EAAE,yCAAyC;gBAClDS,UAAU,EAAE,IAAI;gBAChBL,MAAM,EAAE;kBACN,gBAAgB,EAAEE,aAAa;kBAC/Ba,QAAQ,EAAE;oBACRnB,OAAO,EAAE,aAAa;oBACtBI,MAAM,EAAE;sBACNgB,OAAO,EAAE;oBACX;kBACF;gBACF;cACF,CAAC;cACDA,OAAO,EAAE,4CAA4C;cACrD,UAAU,EAAE;gBACVpB,OAAO,EAAE,oDAAoD;gBAC7DE,KAAK,EAAE;cACT,CAAC;cACD,cAAc,EAAE;gBACdF,OAAO,EAAE,qBAAqB;gBAC9BS,UAAU,EAAE;cACd,CAAC;cACD,WAAW,EAAE;gBACXT,OAAO,EACL,mEAAmE;gBACrES,UAAU,EAAE;cACd,CAAC;cACD,gBAAgB,EAAE;gBAChBT,OAAO,EAAEO,MAAM,CACb,kBAAkB,CAACZ,MAAM,GACvBD,MAAM,CAAC,qCAAqC,CAACC,MAAM,EAAE,CAAC,CAAC,GACvD,GACJ,CAAC;gBACDc,UAAU,EAAE,IAAI;gBAChBP,KAAK,EAAE;cACT,CAAC;cACDW,WAAW,EAAE;YACf;UACF,CAAC;UACD,oBAAoB,EAAE;YACpBb,OAAO,EAAE,GAAG;YACZE,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDH,MAAM,EAAEA,MAAM;MACdI,MAAM,EAAEA;IACV,CAAC;IACDG,aAAa,CAACF,MAAM,CAACI,OAAO,CAACJ,MAAM,GAAGX,KAAK,CAACiB,SAAS,CAAC,oBAAoB,CAAC;IAC3EjB,KAAK,CAACiB,SAAS,CAAC,oBAAoB,CAAC,CAACC,QAAQ,CAACP,MAAM,CAACQ,OAAO,CAACR,MAAM,CAClE,cAAc,CACf,CAACA,MAAM,CAACa,IAAI,GAAGxB,KAAK,CAACiB,SAAS,CAAC,oBAAoB,CAAC;EACvD,CAAC,EAAEjB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}