{"ast": null, "code": "/*\nLanguage: Ceylon\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://ceylon-lang.org\n*/\n\n/** @type LanguageFn */\nfunction ceylon(hljs) {\n  // 2.3. Identifiers and keywords\n  const KEYWORDS = 'assembly module package import alias class interface object given value ' + 'assign void function new of extends satisfies abstracts in out return ' + 'break continue throw assert dynamic if else switch case for while try ' + 'catch finally then let this outer super is exists nonempty';\n  // 7.4.1 Declaration Modifiers\n  const DECLARATION_MODIFIERS = 'shared abstract formal default actual variable late native deprecated ' + 'final sealed annotation suppressWarnings small';\n  // 7.4.2 Documentation\n  const DOCUMENTATION = 'doc by license see throws tagged';\n  const SUBST = {\n    className: 'subst',\n    excludeBegin: true,\n    excludeEnd: true,\n    begin: /``/,\n    end: /``/,\n    keywords: KEYWORDS,\n    relevance: 10\n  };\n  const EXPRESSIONS = [{\n    // verbatim string\n    className: 'string',\n    begin: '\"\"\"',\n    end: '\"\"\"',\n    relevance: 10\n  }, {\n    // string literal or template\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    contains: [SUBST]\n  }, {\n    // character literal\n    className: 'string',\n    begin: \"'\",\n    end: \"'\"\n  }, {\n    // numeric literal\n    className: 'number',\n    begin: '#[0-9a-fA-F_]+|\\\\$[01_]+|[0-9_]+(?:\\\\.[0-9_](?:[eE][+-]?\\\\d+)?)?[kMGTPmunpf]?',\n    relevance: 0\n  }];\n  SUBST.contains = EXPRESSIONS;\n  return {\n    name: 'Ceylon',\n    keywords: {\n      keyword: KEYWORDS + ' ' + DECLARATION_MODIFIERS,\n      meta: DOCUMENTATION\n    },\n    illegal: '\\\\$[^01]|#[^0-9a-fA-F]',\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.COMMENT('/\\\\*', '\\\\*/', {\n      contains: ['self']\n    }), {\n      // compiler annotation\n      className: 'meta',\n      begin: '@[a-z]\\\\w*(?::\"[^\"]*\")?'\n    }].concat(EXPRESSIONS)\n  };\n}\nmodule.exports = ceylon;", "map": {"version": 3, "names": ["ceylon", "hljs", "KEYWORDS", "DECLARATION_MODIFIERS", "DOCUMENTATION", "SUBST", "className", "excludeBegin", "excludeEnd", "begin", "end", "keywords", "relevance", "EXPRESSIONS", "contains", "name", "keyword", "meta", "illegal", "C_LINE_COMMENT_MODE", "COMMENT", "concat", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/ceylon.js"], "sourcesContent": ["/*\nLanguage: Ceylon\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://ceylon-lang.org\n*/\n\n/** @type LanguageFn */\nfunction ceylon(hljs) {\n  // 2.3. Identifiers and keywords\n  const KEYWORDS =\n    'assembly module package import alias class interface object given value ' +\n    'assign void function new of extends satisfies abstracts in out return ' +\n    'break continue throw assert dynamic if else switch case for while try ' +\n    'catch finally then let this outer super is exists nonempty';\n  // 7.4.1 Declaration Modifiers\n  const DECLARATION_MODIFIERS =\n    'shared abstract formal default actual variable late native deprecated ' +\n    'final sealed annotation suppressWarnings small';\n  // 7.4.2 Documentation\n  const DOCUMENTATION =\n    'doc by license see throws tagged';\n  const SUBST = {\n    className: 'subst',\n    excludeBegin: true,\n    excludeEnd: true,\n    begin: /``/,\n    end: /``/,\n    keywords: KEYWORDS,\n    relevance: 10\n  };\n  const EXPRESSIONS = [\n    {\n      // verbatim string\n      className: 'string',\n      begin: '\"\"\"',\n      end: '\"\"\"',\n      relevance: 10\n    },\n    {\n      // string literal or template\n      className: 'string',\n      begin: '\"',\n      end: '\"',\n      contains: [SUBST]\n    },\n    {\n      // character literal\n      className: 'string',\n      begin: \"'\",\n      end: \"'\"\n    },\n    {\n      // numeric literal\n      className: 'number',\n      begin: '#[0-9a-fA-F_]+|\\\\$[01_]+|[0-9_]+(?:\\\\.[0-9_](?:[eE][+-]?\\\\d+)?)?[kMGTPmunpf]?',\n      relevance: 0\n    }\n  ];\n  SUBST.contains = EXPRESSIONS;\n\n  return {\n    name: 'Ceylon',\n    keywords: {\n      keyword: KEYWORDS + ' ' + DECLARATION_MODIFIERS,\n      meta: DOCUMENTATION\n    },\n    illegal: '\\\\$[^01]|#[^0-9a-fA-F]',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*', '\\\\*/', {\n        contains: ['self']\n      }),\n      {\n        // compiler annotation\n        className: 'meta',\n        begin: '@[a-z]\\\\w*(?::\"[^\"]*\")?'\n      }\n    ].concat(EXPRESSIONS)\n  };\n}\n\nmodule.exports = ceylon;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB;EACA,MAAMC,QAAQ,GACZ,0EAA0E,GAC1E,wEAAwE,GACxE,wEAAwE,GACxE,4DAA4D;EAC9D;EACA,MAAMC,qBAAqB,GACzB,wEAAwE,GACxE,gDAAgD;EAClD;EACA,MAAMC,aAAa,GACjB,kCAAkC;EACpC,MAAMC,KAAK,GAAG;IACZC,SAAS,EAAE,OAAO;IAClBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAET,QAAQ;IAClBU,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,WAAW,GAAG,CAClB;IACE;IACAP,SAAS,EAAE,QAAQ;IACnBG,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,KAAK;IACVE,SAAS,EAAE;EACb,CAAC,EACD;IACE;IACAN,SAAS,EAAE,QAAQ;IACnBG,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRI,QAAQ,EAAE,CAACT,KAAK;EAClB,CAAC,EACD;IACE;IACAC,SAAS,EAAE,QAAQ;IACnBG,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE;EACP,CAAC,EACD;IACE;IACAJ,SAAS,EAAE,QAAQ;IACnBG,KAAK,EAAE,+EAA+E;IACtFG,SAAS,EAAE;EACb,CAAC,CACF;EACDP,KAAK,CAACS,QAAQ,GAAGD,WAAW;EAE5B,OAAO;IACLE,IAAI,EAAE,QAAQ;IACdJ,QAAQ,EAAE;MACRK,OAAO,EAAEd,QAAQ,GAAG,GAAG,GAAGC,qBAAqB;MAC/Cc,IAAI,EAAEb;IACR,CAAC;IACDc,OAAO,EAAE,wBAAwB;IACjCJ,QAAQ,EAAE,CACRb,IAAI,CAACkB,mBAAmB,EACxBlB,IAAI,CAACmB,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE;MAC3BN,QAAQ,EAAE,CAAC,MAAM;IACnB,CAAC,CAAC,EACF;MACE;MACAR,SAAS,EAAE,MAAM;MACjBG,KAAK,EAAE;IACT,CAAC,CACF,CAACY,MAAM,CAACR,WAAW;EACtB,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGvB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}