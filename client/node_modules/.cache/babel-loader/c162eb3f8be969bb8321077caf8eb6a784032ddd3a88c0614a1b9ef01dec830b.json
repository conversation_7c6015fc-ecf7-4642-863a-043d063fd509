{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = soy;\nsoy.displayName = 'soy';\nsoy.aliases = [];\nfunction soy(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    var stringPattern = /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/;\n    var numberPattern = /\\b\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?\\b|\\b0x[\\dA-F]+\\b/;\n    Prism.languages.soy = {\n      comment: [/\\/\\*[\\s\\S]*?\\*\\//, {\n        pattern: /(\\s)\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }],\n      'command-arg': {\n        pattern: /(\\{+\\/?\\s*(?:alias|call|delcall|delpackage|deltemplate|namespace|template)\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      parameter: {\n        pattern: /(\\{+\\/?\\s*@?param\\??\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      keyword: [{\n        pattern: /(\\{+\\/?[^\\S\\r\\n]*)(?:\\\\[nrt]|alias|call|case|css|default|delcall|delpackage|deltemplate|else(?:if)?|fallbackmsg|for(?:each)?|if(?:empty)?|lb|let|literal|msg|namespace|nil|@?param\\??|rb|sp|switch|template|xid)/,\n        lookbehind: true\n      }, /\\b(?:any|as|attributes|bool|css|float|html|in|int|js|list|map|null|number|string|uri)\\b/],\n      delimiter: {\n        pattern: /^\\{+\\/?|\\/?\\}+$/,\n        alias: 'punctuation'\n      },\n      property: /\\w+(?==)/,\n      variable: {\n        pattern: /\\$[^\\W\\d]\\w*(?:\\??(?:\\.\\w+|\\[[^\\]]+\\]))*/,\n        inside: {\n          string: {\n            pattern: stringPattern,\n            greedy: true\n          },\n          number: numberPattern,\n          punctuation: /[\\[\\].?]/\n        }\n      },\n      string: {\n        pattern: stringPattern,\n        greedy: true\n      },\n      function: [/\\w+(?=\\()/, {\n        pattern: /(\\|[^\\S\\r\\n]*)\\w+/,\n        lookbehind: true\n      }],\n      boolean: /\\b(?:false|true)\\b/,\n      number: numberPattern,\n      operator: /\\?:?|<=?|>=?|==?|!=|[+*/%-]|\\b(?:and|not|or)\\b/,\n      punctuation: /[{}()\\[\\]|.,:]/\n    }; // Tokenize all inline Soy expressions\n    Prism.hooks.add('before-tokenize', function (env) {\n      var soyPattern = /\\{\\{.+?\\}\\}|\\{.+?\\}|\\s\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//g;\n      var soyLitteralStart = '{literal}';\n      var soyLitteralEnd = '{/literal}';\n      var soyLitteralMode = false;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'soy', soyPattern, function (match) {\n        // Soy tags inside {literal} block are ignored\n        if (match === soyLitteralEnd) {\n          soyLitteralMode = false;\n        }\n        if (!soyLitteralMode) {\n          if (match === soyLitteralStart) {\n            soyLitteralMode = true;\n          }\n          return true;\n        }\n        return false;\n      });\n    }); // Re-insert the tokens after tokenizing\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'soy');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "soy", "displayName", "aliases", "Prism", "register", "stringPattern", "numberPattern", "languages", "comment", "pattern", "lookbehind", "greedy", "alias", "inside", "punctuation", "parameter", "keyword", "delimiter", "property", "variable", "string", "number", "function", "boolean", "operator", "hooks", "add", "env", "soyPattern", "soyLitteralStart", "soyLitteralEnd", "soyLitteralMode", "buildPlaceholders", "match", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/soy.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = soy\nsoy.displayName = 'soy'\nsoy.aliases = []\nfunction soy(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    var stringPattern = /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/\n    var numberPattern = /\\b\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?\\b|\\b0x[\\dA-F]+\\b/\n    Prism.languages.soy = {\n      comment: [\n        /\\/\\*[\\s\\S]*?\\*\\//,\n        {\n          pattern: /(\\s)\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'command-arg': {\n        pattern:\n          /(\\{+\\/?\\s*(?:alias|call|delcall|delpackage|deltemplate|namespace|template)\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      parameter: {\n        pattern: /(\\{+\\/?\\s*@?param\\??\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      keyword: [\n        {\n          pattern:\n            /(\\{+\\/?[^\\S\\r\\n]*)(?:\\\\[nrt]|alias|call|case|css|default|delcall|delpackage|deltemplate|else(?:if)?|fallbackmsg|for(?:each)?|if(?:empty)?|lb|let|literal|msg|namespace|nil|@?param\\??|rb|sp|switch|template|xid)/,\n          lookbehind: true\n        },\n        /\\b(?:any|as|attributes|bool|css|float|html|in|int|js|list|map|null|number|string|uri)\\b/\n      ],\n      delimiter: {\n        pattern: /^\\{+\\/?|\\/?\\}+$/,\n        alias: 'punctuation'\n      },\n      property: /\\w+(?==)/,\n      variable: {\n        pattern: /\\$[^\\W\\d]\\w*(?:\\??(?:\\.\\w+|\\[[^\\]]+\\]))*/,\n        inside: {\n          string: {\n            pattern: stringPattern,\n            greedy: true\n          },\n          number: numberPattern,\n          punctuation: /[\\[\\].?]/\n        }\n      },\n      string: {\n        pattern: stringPattern,\n        greedy: true\n      },\n      function: [\n        /\\w+(?=\\()/,\n        {\n          pattern: /(\\|[^\\S\\r\\n]*)\\w+/,\n          lookbehind: true\n        }\n      ],\n      boolean: /\\b(?:false|true)\\b/,\n      number: numberPattern,\n      operator: /\\?:?|<=?|>=?|==?|!=|[+*/%-]|\\b(?:and|not|or)\\b/,\n      punctuation: /[{}()\\[\\]|.,:]/\n    } // Tokenize all inline Soy expressions\n    Prism.hooks.add('before-tokenize', function (env) {\n      var soyPattern = /\\{\\{.+?\\}\\}|\\{.+?\\}|\\s\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//g\n      var soyLitteralStart = '{literal}'\n      var soyLitteralEnd = '{/literal}'\n      var soyLitteralMode = false\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'soy',\n        soyPattern,\n        function (match) {\n          // Soy tags inside {literal} block are ignored\n          if (match === soyLitteralEnd) {\n            soyLitteralMode = false\n          }\n          if (!soyLitteralMode) {\n            if (match === soyLitteralStart) {\n              soyLitteralMode = true\n            }\n            return true\n          }\n          return false\n        }\n      )\n    }) // Re-insert the tokens after tokenizing\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'soy')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACxC,CAAC,UAAUO,KAAK,EAAE;IACjB,IAAIE,aAAa,GAAG,gDAAgD;IACpE,IAAIC,aAAa,GAAG,mDAAmD;IACvEH,KAAK,CAACI,SAAS,CAACP,GAAG,GAAG;MACpBQ,OAAO,EAAE,CACP,kBAAkB,EAClB;QACEC,OAAO,EAAE,YAAY;QACrBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACD,aAAa,EAAE;QACbF,OAAO,EACL,yFAAyF;QAC3FC,UAAU,EAAE,IAAI;QAChBE,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,SAAS,EAAE;QACTN,OAAO,EAAE,mCAAmC;QAC5CC,UAAU,EAAE,IAAI;QAChBE,KAAK,EAAE;MACT,CAAC;MACDI,OAAO,EAAE,CACP;QACEP,OAAO,EACL,kNAAkN;QACpNC,UAAU,EAAE;MACd,CAAC,EACD,yFAAyF,CAC1F;MACDO,SAAS,EAAE;QACTR,OAAO,EAAE,iBAAiB;QAC1BG,KAAK,EAAE;MACT,CAAC;MACDM,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;QACRV,OAAO,EAAE,0CAA0C;QACnDI,MAAM,EAAE;UACNO,MAAM,EAAE;YACNX,OAAO,EAAEJ,aAAa;YACtBM,MAAM,EAAE;UACV,CAAC;UACDU,MAAM,EAAEf,aAAa;UACrBQ,WAAW,EAAE;QACf;MACF,CAAC;MACDM,MAAM,EAAE;QACNX,OAAO,EAAEJ,aAAa;QACtBM,MAAM,EAAE;MACV,CAAC;MACDW,QAAQ,EAAE,CACR,WAAW,EACX;QACEb,OAAO,EAAE,mBAAmB;QAC5BC,UAAU,EAAE;MACd,CAAC,CACF;MACDa,OAAO,EAAE,oBAAoB;MAC7BF,MAAM,EAAEf,aAAa;MACrBkB,QAAQ,EAAE,gDAAgD;MAC1DV,WAAW,EAAE;IACf,CAAC,EAAC;IACFX,KAAK,CAACsB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIC,UAAU,GAAG,gDAAgD;MACjE,IAAIC,gBAAgB,GAAG,WAAW;MAClC,IAAIC,cAAc,GAAG,YAAY;MACjC,IAAIC,eAAe,GAAG,KAAK;MAC3B5B,KAAK,CAACI,SAAS,CAAC,mBAAmB,CAAC,CAACyB,iBAAiB,CACpDL,GAAG,EACH,KAAK,EACLC,UAAU,EACV,UAAUK,KAAK,EAAE;QACf;QACA,IAAIA,KAAK,KAAKH,cAAc,EAAE;UAC5BC,eAAe,GAAG,KAAK;QACzB;QACA,IAAI,CAACA,eAAe,EAAE;UACpB,IAAIE,KAAK,KAAKJ,gBAAgB,EAAE;YAC9BE,eAAe,GAAG,IAAI;UACxB;UACA,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CACF,CAAC;IACH,CAAC,CAAC,EAAC;IACH5B,KAAK,CAACsB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/CxB,KAAK,CAACI,SAAS,CAAC,mBAAmB,CAAC,CAAC2B,oBAAoB,CAACP,GAAG,EAAE,KAAK,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,EAAExB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}