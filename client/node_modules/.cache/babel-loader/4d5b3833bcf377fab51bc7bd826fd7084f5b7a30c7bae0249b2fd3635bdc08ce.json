{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"language\", \"children\", \"style\", \"customStyle\", \"codeTagProps\", \"useInlineStyles\", \"showLineNumbers\", \"showInlineLineNumbers\", \"startingLineNumber\", \"lineNumberContainerStyle\", \"lineNumberStyle\", \"wrapLines\", \"wrapLongLines\", \"lineProps\", \"renderer\", \"PreTag\", \"CodeTag\", \"code\", \"astGenerator\"];\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nimport React from 'react';\nimport createElement from './create-element';\nimport checkForListedLanguage from './checkForListedLanguage';\nvar newLineRegex = /\\n/g;\nfunction getNewLines(str) {\n  return str.match(newLineRegex);\n}\nfunction getAllLineNumbers(_ref) {\n  var lines = _ref.lines,\n    startingLineNumber = _ref.startingLineNumber,\n    style = _ref.style;\n  return lines.map(function (_, i) {\n    var number = i + startingLineNumber;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      key: \"line-\".concat(i),\n      className: \"react-syntax-highlighter-line-number\",\n      style: typeof style === 'function' ? style(number) : style\n    }, \"\".concat(number, \"\\n\"));\n  });\n}\nfunction AllLineNumbers(_ref2) {\n  var codeString = _ref2.codeString,\n    codeStyle = _ref2.codeStyle,\n    _ref2$containerStyle = _ref2.containerStyle,\n    containerStyle = _ref2$containerStyle === void 0 ? {\n      \"float\": 'left',\n      paddingRight: '10px'\n    } : _ref2$containerStyle,\n    _ref2$numberStyle = _ref2.numberStyle,\n    numberStyle = _ref2$numberStyle === void 0 ? {} : _ref2$numberStyle,\n    startingLineNumber = _ref2.startingLineNumber;\n  return /*#__PURE__*/React.createElement(\"code\", {\n    style: Object.assign({}, codeStyle, containerStyle)\n  }, getAllLineNumbers({\n    lines: codeString.replace(/\\n$/, '').split('\\n'),\n    style: numberStyle,\n    startingLineNumber: startingLineNumber\n  }));\n}\nfunction getEmWidthOfNumber(num) {\n  return \"\".concat(num.toString().length, \".25em\");\n}\nfunction getInlineLineNumber(lineNumber, inlineLineNumberStyle) {\n  return {\n    type: 'element',\n    tagName: 'span',\n    properties: {\n      key: \"line-number--\".concat(lineNumber),\n      className: ['comment', 'linenumber', 'react-syntax-highlighter-line-number'],\n      style: inlineLineNumberStyle\n    },\n    children: [{\n      type: 'text',\n      value: lineNumber\n    }]\n  };\n}\nfunction assembleLineNumberStyles(lineNumberStyle, lineNumber, largestLineNumber) {\n  // minimally necessary styling for line numbers\n  var defaultLineNumberStyle = {\n    display: 'inline-block',\n    minWidth: getEmWidthOfNumber(largestLineNumber),\n    paddingRight: '1em',\n    textAlign: 'right',\n    userSelect: 'none'\n  };\n  // prep custom styling\n  var customLineNumberStyle = typeof lineNumberStyle === 'function' ? lineNumberStyle(lineNumber) : lineNumberStyle;\n  // combine\n  var assembledStyle = _objectSpread(_objectSpread({}, defaultLineNumberStyle), customLineNumberStyle);\n  return assembledStyle;\n}\nfunction createLineElement(_ref3) {\n  var children = _ref3.children,\n    lineNumber = _ref3.lineNumber,\n    lineNumberStyle = _ref3.lineNumberStyle,\n    largestLineNumber = _ref3.largestLineNumber,\n    showInlineLineNumbers = _ref3.showInlineLineNumbers,\n    _ref3$lineProps = _ref3.lineProps,\n    lineProps = _ref3$lineProps === void 0 ? {} : _ref3$lineProps,\n    _ref3$className = _ref3.className,\n    className = _ref3$className === void 0 ? [] : _ref3$className,\n    showLineNumbers = _ref3.showLineNumbers,\n    wrapLongLines = _ref3.wrapLongLines,\n    _ref3$wrapLines = _ref3.wrapLines,\n    wrapLines = _ref3$wrapLines === void 0 ? false : _ref3$wrapLines;\n  var properties = wrapLines ? _objectSpread({}, typeof lineProps === 'function' ? lineProps(lineNumber) : lineProps) : {};\n  properties['className'] = properties['className'] ? [].concat(_toConsumableArray(properties['className'].trim().split(/\\s+/)), _toConsumableArray(className)) : className;\n  if (lineNumber && showInlineLineNumbers) {\n    var inlineLineNumberStyle = assembleLineNumberStyles(lineNumberStyle, lineNumber, largestLineNumber);\n    children.unshift(getInlineLineNumber(lineNumber, inlineLineNumberStyle));\n  }\n  if (wrapLongLines & showLineNumbers) {\n    properties.style = _objectSpread({\n      display: 'flex'\n    }, properties.style);\n  }\n  return {\n    type: 'element',\n    tagName: 'span',\n    properties: properties,\n    children: children\n  };\n}\nfunction flattenCodeTree(tree) {\n  var className = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var newTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    if (node.type === 'text') {\n      newTree.push(createLineElement({\n        children: [node],\n        className: _toConsumableArray(new Set(className))\n      }));\n    } else if (node.children) {\n      var classNames = className.concat(node.properties.className);\n      flattenCodeTree(node.children, classNames).forEach(function (i) {\n        return newTree.push(i);\n      });\n    }\n  }\n  return newTree;\n}\nfunction processLines(codeTree, wrapLines, lineProps, showLineNumbers, showInlineLineNumbers, startingLineNumber, largestLineNumber, lineNumberStyle, wrapLongLines) {\n  var _ref4;\n  var tree = flattenCodeTree(codeTree.value);\n  var newTree = [];\n  var lastLineBreakIndex = -1;\n  var index = 0;\n  function createWrappedLine(children, lineNumber) {\n    var className = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    return createLineElement({\n      children: children,\n      lineNumber: lineNumber,\n      lineNumberStyle: lineNumberStyle,\n      largestLineNumber: largestLineNumber,\n      showInlineLineNumbers: showInlineLineNumbers,\n      lineProps: lineProps,\n      className: className,\n      showLineNumbers: showLineNumbers,\n      wrapLongLines: wrapLongLines,\n      wrapLines: wrapLines\n    });\n  }\n  function createUnwrappedLine(children, lineNumber) {\n    if (showLineNumbers && lineNumber && showInlineLineNumbers) {\n      var inlineLineNumberStyle = assembleLineNumberStyles(lineNumberStyle, lineNumber, largestLineNumber);\n      children.unshift(getInlineLineNumber(lineNumber, inlineLineNumberStyle));\n    }\n    return children;\n  }\n  function createLine(children, lineNumber) {\n    var className = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    return wrapLines || className.length > 0 ? createWrappedLine(children, lineNumber, className) : createUnwrappedLine(children, lineNumber);\n  }\n  var _loop = function _loop() {\n    var node = tree[index];\n    var value = node.children[0].value;\n    var newLines = getNewLines(value);\n    if (newLines) {\n      var splitValue = value.split('\\n');\n      splitValue.forEach(function (text, i) {\n        var lineNumber = showLineNumbers && newTree.length + startingLineNumber;\n        var newChild = {\n          type: 'text',\n          value: \"\".concat(text, \"\\n\")\n        };\n\n        // if it's the first line\n        if (i === 0) {\n          var _children = tree.slice(lastLineBreakIndex + 1, index).concat(createLineElement({\n            children: [newChild],\n            className: node.properties.className\n          }));\n          var _line = createLine(_children, lineNumber);\n          newTree.push(_line);\n\n          // if it's the last line\n        } else if (i === splitValue.length - 1) {\n          var stringChild = tree[index + 1] && tree[index + 1].children && tree[index + 1].children[0];\n          var lastLineInPreviousSpan = {\n            type: 'text',\n            value: \"\".concat(text)\n          };\n          if (stringChild) {\n            var newElem = createLineElement({\n              children: [lastLineInPreviousSpan],\n              className: node.properties.className\n            });\n            tree.splice(index + 1, 0, newElem);\n          } else {\n            var _children2 = [lastLineInPreviousSpan];\n            var _line2 = createLine(_children2, lineNumber, node.properties.className);\n            newTree.push(_line2);\n          }\n\n          // if it's neither the first nor the last line\n        } else {\n          var _children3 = [newChild];\n          var _line3 = createLine(_children3, lineNumber, node.properties.className);\n          newTree.push(_line3);\n        }\n      });\n      lastLineBreakIndex = index;\n    }\n    index++;\n  };\n  while (index < tree.length) {\n    _loop();\n  }\n  if (lastLineBreakIndex !== tree.length - 1) {\n    var children = tree.slice(lastLineBreakIndex + 1, tree.length);\n    if (children && children.length) {\n      var lineNumber = showLineNumbers && newTree.length + startingLineNumber;\n      var line = createLine(children, lineNumber);\n      newTree.push(line);\n    }\n  }\n  return wrapLines ? newTree : (_ref4 = []).concat.apply(_ref4, newTree);\n}\nfunction defaultRenderer(_ref5) {\n  var rows = _ref5.rows,\n    stylesheet = _ref5.stylesheet,\n    useInlineStyles = _ref5.useInlineStyles;\n  return rows.map(function (node, i) {\n    return createElement({\n      node: node,\n      stylesheet: stylesheet,\n      useInlineStyles: useInlineStyles,\n      key: \"code-segement\".concat(i)\n    });\n  });\n}\n\n// only highlight.js has the highlightAuto method\nfunction isHighlightJs(astGenerator) {\n  return astGenerator && typeof astGenerator.highlightAuto !== 'undefined';\n}\nfunction getCodeTree(_ref6) {\n  var astGenerator = _ref6.astGenerator,\n    language = _ref6.language,\n    code = _ref6.code,\n    defaultCodeValue = _ref6.defaultCodeValue;\n  // figure out whether we're using lowlight/highlight or refractor/prism\n  // then attempt highlighting accordingly\n\n  // lowlight/highlight?\n  if (isHighlightJs(astGenerator)) {\n    var hasLanguage = checkForListedLanguage(astGenerator, language);\n    if (language === 'text') {\n      return {\n        value: defaultCodeValue,\n        language: 'text'\n      };\n    } else if (hasLanguage) {\n      return astGenerator.highlight(language, code);\n    } else {\n      return astGenerator.highlightAuto(code);\n    }\n  }\n\n  // must be refractor/prism, then\n  try {\n    return language && language !== 'text' ? {\n      value: astGenerator.highlight(code, language)\n    } : {\n      value: defaultCodeValue\n    };\n  } catch (e) {\n    return {\n      value: defaultCodeValue\n    };\n  }\n}\nexport default function (defaultAstGenerator, defaultStyle) {\n  return function SyntaxHighlighter(_ref7) {\n    var language = _ref7.language,\n      children = _ref7.children,\n      _ref7$style = _ref7.style,\n      style = _ref7$style === void 0 ? defaultStyle : _ref7$style,\n      _ref7$customStyle = _ref7.customStyle,\n      customStyle = _ref7$customStyle === void 0 ? {} : _ref7$customStyle,\n      _ref7$codeTagProps = _ref7.codeTagProps,\n      codeTagProps = _ref7$codeTagProps === void 0 ? {\n        className: language ? \"language-\".concat(language) : undefined,\n        style: _objectSpread(_objectSpread({}, style['code[class*=\"language-\"]']), style[\"code[class*=\\\"language-\".concat(language, \"\\\"]\")])\n      } : _ref7$codeTagProps,\n      _ref7$useInlineStyles = _ref7.useInlineStyles,\n      useInlineStyles = _ref7$useInlineStyles === void 0 ? true : _ref7$useInlineStyles,\n      _ref7$showLineNumbers = _ref7.showLineNumbers,\n      showLineNumbers = _ref7$showLineNumbers === void 0 ? false : _ref7$showLineNumbers,\n      _ref7$showInlineLineN = _ref7.showInlineLineNumbers,\n      showInlineLineNumbers = _ref7$showInlineLineN === void 0 ? true : _ref7$showInlineLineN,\n      _ref7$startingLineNum = _ref7.startingLineNumber,\n      startingLineNumber = _ref7$startingLineNum === void 0 ? 1 : _ref7$startingLineNum,\n      lineNumberContainerStyle = _ref7.lineNumberContainerStyle,\n      _ref7$lineNumberStyle = _ref7.lineNumberStyle,\n      lineNumberStyle = _ref7$lineNumberStyle === void 0 ? {} : _ref7$lineNumberStyle,\n      wrapLines = _ref7.wrapLines,\n      _ref7$wrapLongLines = _ref7.wrapLongLines,\n      wrapLongLines = _ref7$wrapLongLines === void 0 ? false : _ref7$wrapLongLines,\n      _ref7$lineProps = _ref7.lineProps,\n      lineProps = _ref7$lineProps === void 0 ? {} : _ref7$lineProps,\n      renderer = _ref7.renderer,\n      _ref7$PreTag = _ref7.PreTag,\n      PreTag = _ref7$PreTag === void 0 ? 'pre' : _ref7$PreTag,\n      _ref7$CodeTag = _ref7.CodeTag,\n      CodeTag = _ref7$CodeTag === void 0 ? 'code' : _ref7$CodeTag,\n      _ref7$code = _ref7.code,\n      code = _ref7$code === void 0 ? (Array.isArray(children) ? children[0] : children) || '' : _ref7$code,\n      astGenerator = _ref7.astGenerator,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n    astGenerator = astGenerator || defaultAstGenerator;\n    var allLineNumbers = showLineNumbers ? /*#__PURE__*/React.createElement(AllLineNumbers, {\n      containerStyle: lineNumberContainerStyle,\n      codeStyle: codeTagProps.style || {},\n      numberStyle: lineNumberStyle,\n      startingLineNumber: startingLineNumber,\n      codeString: code\n    }) : null;\n    var defaultPreStyle = style.hljs || style['pre[class*=\"language-\"]'] || {\n      backgroundColor: '#fff'\n    };\n    var generatorClassName = isHighlightJs(astGenerator) ? 'hljs' : 'prismjs';\n    var preProps = useInlineStyles ? Object.assign({}, rest, {\n      style: Object.assign({}, defaultPreStyle, customStyle)\n    }) : Object.assign({}, rest, {\n      className: rest.className ? \"\".concat(generatorClassName, \" \").concat(rest.className) : generatorClassName,\n      style: Object.assign({}, customStyle)\n    });\n    if (wrapLongLines) {\n      codeTagProps.style = _objectSpread({\n        whiteSpace: 'pre-wrap'\n      }, codeTagProps.style);\n    } else {\n      codeTagProps.style = _objectSpread({\n        whiteSpace: 'pre'\n      }, codeTagProps.style);\n    }\n    if (!astGenerator) {\n      return /*#__PURE__*/React.createElement(PreTag, preProps, allLineNumbers, /*#__PURE__*/React.createElement(CodeTag, codeTagProps, code));\n    }\n\n    /*\n     * Some custom renderers rely on individual row elements so we need to turn wrapLines on\n     * if renderer is provided and wrapLines is undefined.\n     */\n    if (wrapLines === undefined && renderer || wrapLongLines) wrapLines = true;\n    renderer = renderer || defaultRenderer;\n    var defaultCodeValue = [{\n      type: 'text',\n      value: code\n    }];\n    var codeTree = getCodeTree({\n      astGenerator: astGenerator,\n      language: language,\n      code: code,\n      defaultCodeValue: defaultCodeValue\n    });\n    if (codeTree.language === null) {\n      codeTree.value = defaultCodeValue;\n    }\n\n    // determine largest line number so that we can force minWidth on all linenumber elements\n    var lineCount = codeTree.value.length;\n    if (lineCount === 1 && codeTree.value[0].type === 'text') {\n      // Since codeTree for an unparsable text (e.g. 'a\\na\\na') is [{ type: 'text', value: 'a\\na\\na' }]\n      lineCount = codeTree.value[0].value.split('\\n').length;\n    }\n    var largestLineNumber = lineCount + startingLineNumber;\n    var rows = processLines(codeTree, wrapLines, lineProps, showLineNumbers, showInlineLineNumbers, startingLineNumber, largestLineNumber, lineNumberStyle, wrapLongLines);\n    return /*#__PURE__*/React.createElement(PreTag, preProps, /*#__PURE__*/React.createElement(CodeTag, codeTagProps, !showInlineLineNumbers && allLineNumbers, renderer({\n      rows: rows,\n      stylesheet: style,\n      useInlineStyles: useInlineStyles\n    })));\n  };\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_toConsumableArray", "_defineProperty", "_excluded", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "React", "createElement", "checkForListedLanguage", "newLineRegex", "getNewLines", "str", "match", "getAllLineNumbers", "_ref", "lines", "startingLineNumber", "style", "map", "_", "i", "number", "key", "concat", "className", "AllLineNumbers", "_ref2", "codeString", "codeStyle", "_ref2$containerStyle", "containerStyle", "paddingRight", "_ref2$numberStyle", "numberStyle", "assign", "replace", "split", "getEmWidthOfNumber", "num", "toString", "getInlineLineNumber", "lineNumber", "inlineLineNumberStyle", "type", "tagName", "properties", "children", "value", "assembleLineNumberStyles", "lineNumberStyle", "largestLineNumber", "defaultLineNumberStyle", "display", "min<PERSON><PERSON><PERSON>", "textAlign", "userSelect", "customLineNumberStyle", "assembledStyle", "createLineElement", "_ref3", "showInlineLineNumbers", "_ref3$lineProps", "lineProps", "_ref3$className", "showLineNumbers", "wrapLongLines", "_ref3$wrapLines", "wrapLines", "trim", "unshift", "flattenCodeTree", "tree", "undefined", "newTree", "node", "Set", "classNames", "processLines", "codeTree", "_ref4", "lastLineBreakIndex", "index", "createWrappedLine", "createUnwrappedLine", "createLine", "_loop", "newLines", "splitValue", "text", "<PERSON><PERSON><PERSON><PERSON>", "_children", "slice", "_line", "<PERSON><PERSON><PERSON><PERSON>", "lastLineInPreviousSpan", "newElem", "splice", "_children2", "_line2", "_children3", "_line3", "line", "defaultRenderer", "_ref5", "rows", "stylesheet", "useInlineStyles", "isHighlightJs", "astGenerator", "highlightAuto", "getCodeTree", "_ref6", "language", "code", "defaultCodeValue", "hasLanguage", "highlight", "defaultAstGenerator", "defaultStyle", "Syntax<PERSON><PERSON><PERSON><PERSON>", "_ref7", "_ref7$style", "_ref7$customStyle", "customStyle", "_ref7$codeTagProps", "codeTagProps", "_ref7$useInlineStyles", "_ref7$showLineNumbers", "_ref7$showInlineLineN", "_ref7$startingLineNum", "lineNumberContainerStyle", "_ref7$lineNumberStyle", "_ref7$wrapLongLines", "_ref7$lineProps", "renderer", "_ref7$PreTag", "PreTag", "_ref7$CodeTag", "CodeTag", "_ref7$code", "Array", "isArray", "rest", "allLineNumbers", "defaultPreStyle", "hljs", "backgroundColor", "generatorClassName", "preProps", "whiteSpace", "lineCount"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/highlight.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"language\", \"children\", \"style\", \"customStyle\", \"codeTagProps\", \"useInlineStyles\", \"showLineNumbers\", \"showInlineLineNumbers\", \"startingLineNumber\", \"lineNumberContainerStyle\", \"lineNumberStyle\", \"wrapLines\", \"wrapLongLines\", \"lineProps\", \"renderer\", \"PreTag\", \"CodeTag\", \"code\", \"astGenerator\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport createElement from './create-element';\nimport checkForListedLanguage from './checkForListedLanguage';\nvar newLineRegex = /\\n/g;\nfunction getNewLines(str) {\n  return str.match(newLineRegex);\n}\nfunction getAllLineNumbers(_ref) {\n  var lines = _ref.lines,\n    startingLineNumber = _ref.startingLineNumber,\n    style = _ref.style;\n  return lines.map(function (_, i) {\n    var number = i + startingLineNumber;\n    return /*#__PURE__*/React.createElement(\"span\", {\n      key: \"line-\".concat(i),\n      className: \"react-syntax-highlighter-line-number\",\n      style: typeof style === 'function' ? style(number) : style\n    }, \"\".concat(number, \"\\n\"));\n  });\n}\nfunction AllLineNumbers(_ref2) {\n  var codeString = _ref2.codeString,\n    codeStyle = _ref2.codeStyle,\n    _ref2$containerStyle = _ref2.containerStyle,\n    containerStyle = _ref2$containerStyle === void 0 ? {\n      \"float\": 'left',\n      paddingRight: '10px'\n    } : _ref2$containerStyle,\n    _ref2$numberStyle = _ref2.numberStyle,\n    numberStyle = _ref2$numberStyle === void 0 ? {} : _ref2$numberStyle,\n    startingLineNumber = _ref2.startingLineNumber;\n  return /*#__PURE__*/React.createElement(\"code\", {\n    style: Object.assign({}, codeStyle, containerStyle)\n  }, getAllLineNumbers({\n    lines: codeString.replace(/\\n$/, '').split('\\n'),\n    style: numberStyle,\n    startingLineNumber: startingLineNumber\n  }));\n}\nfunction getEmWidthOfNumber(num) {\n  return \"\".concat(num.toString().length, \".25em\");\n}\nfunction getInlineLineNumber(lineNumber, inlineLineNumberStyle) {\n  return {\n    type: 'element',\n    tagName: 'span',\n    properties: {\n      key: \"line-number--\".concat(lineNumber),\n      className: ['comment', 'linenumber', 'react-syntax-highlighter-line-number'],\n      style: inlineLineNumberStyle\n    },\n    children: [{\n      type: 'text',\n      value: lineNumber\n    }]\n  };\n}\nfunction assembleLineNumberStyles(lineNumberStyle, lineNumber, largestLineNumber) {\n  // minimally necessary styling for line numbers\n  var defaultLineNumberStyle = {\n    display: 'inline-block',\n    minWidth: getEmWidthOfNumber(largestLineNumber),\n    paddingRight: '1em',\n    textAlign: 'right',\n    userSelect: 'none'\n  };\n  // prep custom styling\n  var customLineNumberStyle = typeof lineNumberStyle === 'function' ? lineNumberStyle(lineNumber) : lineNumberStyle;\n  // combine\n  var assembledStyle = _objectSpread(_objectSpread({}, defaultLineNumberStyle), customLineNumberStyle);\n  return assembledStyle;\n}\nfunction createLineElement(_ref3) {\n  var children = _ref3.children,\n    lineNumber = _ref3.lineNumber,\n    lineNumberStyle = _ref3.lineNumberStyle,\n    largestLineNumber = _ref3.largestLineNumber,\n    showInlineLineNumbers = _ref3.showInlineLineNumbers,\n    _ref3$lineProps = _ref3.lineProps,\n    lineProps = _ref3$lineProps === void 0 ? {} : _ref3$lineProps,\n    _ref3$className = _ref3.className,\n    className = _ref3$className === void 0 ? [] : _ref3$className,\n    showLineNumbers = _ref3.showLineNumbers,\n    wrapLongLines = _ref3.wrapLongLines,\n    _ref3$wrapLines = _ref3.wrapLines,\n    wrapLines = _ref3$wrapLines === void 0 ? false : _ref3$wrapLines;\n  var properties = wrapLines ? _objectSpread({}, typeof lineProps === 'function' ? lineProps(lineNumber) : lineProps) : {};\n  properties['className'] = properties['className'] ? [].concat(_toConsumableArray(properties['className'].trim().split(/\\s+/)), _toConsumableArray(className)) : className;\n  if (lineNumber && showInlineLineNumbers) {\n    var inlineLineNumberStyle = assembleLineNumberStyles(lineNumberStyle, lineNumber, largestLineNumber);\n    children.unshift(getInlineLineNumber(lineNumber, inlineLineNumberStyle));\n  }\n  if (wrapLongLines & showLineNumbers) {\n    properties.style = _objectSpread({\n      display: 'flex'\n    }, properties.style);\n  }\n  return {\n    type: 'element',\n    tagName: 'span',\n    properties: properties,\n    children: children\n  };\n}\nfunction flattenCodeTree(tree) {\n  var className = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var newTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    if (node.type === 'text') {\n      newTree.push(createLineElement({\n        children: [node],\n        className: _toConsumableArray(new Set(className))\n      }));\n    } else if (node.children) {\n      var classNames = className.concat(node.properties.className);\n      flattenCodeTree(node.children, classNames).forEach(function (i) {\n        return newTree.push(i);\n      });\n    }\n  }\n  return newTree;\n}\nfunction processLines(codeTree, wrapLines, lineProps, showLineNumbers, showInlineLineNumbers, startingLineNumber, largestLineNumber, lineNumberStyle, wrapLongLines) {\n  var _ref4;\n  var tree = flattenCodeTree(codeTree.value);\n  var newTree = [];\n  var lastLineBreakIndex = -1;\n  var index = 0;\n  function createWrappedLine(children, lineNumber) {\n    var className = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    return createLineElement({\n      children: children,\n      lineNumber: lineNumber,\n      lineNumberStyle: lineNumberStyle,\n      largestLineNumber: largestLineNumber,\n      showInlineLineNumbers: showInlineLineNumbers,\n      lineProps: lineProps,\n      className: className,\n      showLineNumbers: showLineNumbers,\n      wrapLongLines: wrapLongLines,\n      wrapLines: wrapLines\n    });\n  }\n  function createUnwrappedLine(children, lineNumber) {\n    if (showLineNumbers && lineNumber && showInlineLineNumbers) {\n      var inlineLineNumberStyle = assembleLineNumberStyles(lineNumberStyle, lineNumber, largestLineNumber);\n      children.unshift(getInlineLineNumber(lineNumber, inlineLineNumberStyle));\n    }\n    return children;\n  }\n  function createLine(children, lineNumber) {\n    var className = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    return wrapLines || className.length > 0 ? createWrappedLine(children, lineNumber, className) : createUnwrappedLine(children, lineNumber);\n  }\n  var _loop = function _loop() {\n    var node = tree[index];\n    var value = node.children[0].value;\n    var newLines = getNewLines(value);\n    if (newLines) {\n      var splitValue = value.split('\\n');\n      splitValue.forEach(function (text, i) {\n        var lineNumber = showLineNumbers && newTree.length + startingLineNumber;\n        var newChild = {\n          type: 'text',\n          value: \"\".concat(text, \"\\n\")\n        };\n\n        // if it's the first line\n        if (i === 0) {\n          var _children = tree.slice(lastLineBreakIndex + 1, index).concat(createLineElement({\n            children: [newChild],\n            className: node.properties.className\n          }));\n          var _line = createLine(_children, lineNumber);\n          newTree.push(_line);\n\n          // if it's the last line\n        } else if (i === splitValue.length - 1) {\n          var stringChild = tree[index + 1] && tree[index + 1].children && tree[index + 1].children[0];\n          var lastLineInPreviousSpan = {\n            type: 'text',\n            value: \"\".concat(text)\n          };\n          if (stringChild) {\n            var newElem = createLineElement({\n              children: [lastLineInPreviousSpan],\n              className: node.properties.className\n            });\n            tree.splice(index + 1, 0, newElem);\n          } else {\n            var _children2 = [lastLineInPreviousSpan];\n            var _line2 = createLine(_children2, lineNumber, node.properties.className);\n            newTree.push(_line2);\n          }\n\n          // if it's neither the first nor the last line\n        } else {\n          var _children3 = [newChild];\n          var _line3 = createLine(_children3, lineNumber, node.properties.className);\n          newTree.push(_line3);\n        }\n      });\n      lastLineBreakIndex = index;\n    }\n    index++;\n  };\n  while (index < tree.length) {\n    _loop();\n  }\n  if (lastLineBreakIndex !== tree.length - 1) {\n    var children = tree.slice(lastLineBreakIndex + 1, tree.length);\n    if (children && children.length) {\n      var lineNumber = showLineNumbers && newTree.length + startingLineNumber;\n      var line = createLine(children, lineNumber);\n      newTree.push(line);\n    }\n  }\n  return wrapLines ? newTree : (_ref4 = []).concat.apply(_ref4, newTree);\n}\nfunction defaultRenderer(_ref5) {\n  var rows = _ref5.rows,\n    stylesheet = _ref5.stylesheet,\n    useInlineStyles = _ref5.useInlineStyles;\n  return rows.map(function (node, i) {\n    return createElement({\n      node: node,\n      stylesheet: stylesheet,\n      useInlineStyles: useInlineStyles,\n      key: \"code-segement\".concat(i)\n    });\n  });\n}\n\n// only highlight.js has the highlightAuto method\nfunction isHighlightJs(astGenerator) {\n  return astGenerator && typeof astGenerator.highlightAuto !== 'undefined';\n}\nfunction getCodeTree(_ref6) {\n  var astGenerator = _ref6.astGenerator,\n    language = _ref6.language,\n    code = _ref6.code,\n    defaultCodeValue = _ref6.defaultCodeValue;\n  // figure out whether we're using lowlight/highlight or refractor/prism\n  // then attempt highlighting accordingly\n\n  // lowlight/highlight?\n  if (isHighlightJs(astGenerator)) {\n    var hasLanguage = checkForListedLanguage(astGenerator, language);\n    if (language === 'text') {\n      return {\n        value: defaultCodeValue,\n        language: 'text'\n      };\n    } else if (hasLanguage) {\n      return astGenerator.highlight(language, code);\n    } else {\n      return astGenerator.highlightAuto(code);\n    }\n  }\n\n  // must be refractor/prism, then\n  try {\n    return language && language !== 'text' ? {\n      value: astGenerator.highlight(code, language)\n    } : {\n      value: defaultCodeValue\n    };\n  } catch (e) {\n    return {\n      value: defaultCodeValue\n    };\n  }\n}\nexport default function (defaultAstGenerator, defaultStyle) {\n  return function SyntaxHighlighter(_ref7) {\n    var language = _ref7.language,\n      children = _ref7.children,\n      _ref7$style = _ref7.style,\n      style = _ref7$style === void 0 ? defaultStyle : _ref7$style,\n      _ref7$customStyle = _ref7.customStyle,\n      customStyle = _ref7$customStyle === void 0 ? {} : _ref7$customStyle,\n      _ref7$codeTagProps = _ref7.codeTagProps,\n      codeTagProps = _ref7$codeTagProps === void 0 ? {\n        className: language ? \"language-\".concat(language) : undefined,\n        style: _objectSpread(_objectSpread({}, style['code[class*=\"language-\"]']), style[\"code[class*=\\\"language-\".concat(language, \"\\\"]\")])\n      } : _ref7$codeTagProps,\n      _ref7$useInlineStyles = _ref7.useInlineStyles,\n      useInlineStyles = _ref7$useInlineStyles === void 0 ? true : _ref7$useInlineStyles,\n      _ref7$showLineNumbers = _ref7.showLineNumbers,\n      showLineNumbers = _ref7$showLineNumbers === void 0 ? false : _ref7$showLineNumbers,\n      _ref7$showInlineLineN = _ref7.showInlineLineNumbers,\n      showInlineLineNumbers = _ref7$showInlineLineN === void 0 ? true : _ref7$showInlineLineN,\n      _ref7$startingLineNum = _ref7.startingLineNumber,\n      startingLineNumber = _ref7$startingLineNum === void 0 ? 1 : _ref7$startingLineNum,\n      lineNumberContainerStyle = _ref7.lineNumberContainerStyle,\n      _ref7$lineNumberStyle = _ref7.lineNumberStyle,\n      lineNumberStyle = _ref7$lineNumberStyle === void 0 ? {} : _ref7$lineNumberStyle,\n      wrapLines = _ref7.wrapLines,\n      _ref7$wrapLongLines = _ref7.wrapLongLines,\n      wrapLongLines = _ref7$wrapLongLines === void 0 ? false : _ref7$wrapLongLines,\n      _ref7$lineProps = _ref7.lineProps,\n      lineProps = _ref7$lineProps === void 0 ? {} : _ref7$lineProps,\n      renderer = _ref7.renderer,\n      _ref7$PreTag = _ref7.PreTag,\n      PreTag = _ref7$PreTag === void 0 ? 'pre' : _ref7$PreTag,\n      _ref7$CodeTag = _ref7.CodeTag,\n      CodeTag = _ref7$CodeTag === void 0 ? 'code' : _ref7$CodeTag,\n      _ref7$code = _ref7.code,\n      code = _ref7$code === void 0 ? (Array.isArray(children) ? children[0] : children) || '' : _ref7$code,\n      astGenerator = _ref7.astGenerator,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n    astGenerator = astGenerator || defaultAstGenerator;\n    var allLineNumbers = showLineNumbers ? /*#__PURE__*/React.createElement(AllLineNumbers, {\n      containerStyle: lineNumberContainerStyle,\n      codeStyle: codeTagProps.style || {},\n      numberStyle: lineNumberStyle,\n      startingLineNumber: startingLineNumber,\n      codeString: code\n    }) : null;\n    var defaultPreStyle = style.hljs || style['pre[class*=\"language-\"]'] || {\n      backgroundColor: '#fff'\n    };\n    var generatorClassName = isHighlightJs(astGenerator) ? 'hljs' : 'prismjs';\n    var preProps = useInlineStyles ? Object.assign({}, rest, {\n      style: Object.assign({}, defaultPreStyle, customStyle)\n    }) : Object.assign({}, rest, {\n      className: rest.className ? \"\".concat(generatorClassName, \" \").concat(rest.className) : generatorClassName,\n      style: Object.assign({}, customStyle)\n    });\n    if (wrapLongLines) {\n      codeTagProps.style = _objectSpread({\n        whiteSpace: 'pre-wrap'\n      }, codeTagProps.style);\n    } else {\n      codeTagProps.style = _objectSpread({\n        whiteSpace: 'pre'\n      }, codeTagProps.style);\n    }\n    if (!astGenerator) {\n      return /*#__PURE__*/React.createElement(PreTag, preProps, allLineNumbers, /*#__PURE__*/React.createElement(CodeTag, codeTagProps, code));\n    }\n\n    /*\n     * Some custom renderers rely on individual row elements so we need to turn wrapLines on\n     * if renderer is provided and wrapLines is undefined.\n     */\n    if (wrapLines === undefined && renderer || wrapLongLines) wrapLines = true;\n    renderer = renderer || defaultRenderer;\n    var defaultCodeValue = [{\n      type: 'text',\n      value: code\n    }];\n    var codeTree = getCodeTree({\n      astGenerator: astGenerator,\n      language: language,\n      code: code,\n      defaultCodeValue: defaultCodeValue\n    });\n    if (codeTree.language === null) {\n      codeTree.value = defaultCodeValue;\n    }\n\n    // determine largest line number so that we can force minWidth on all linenumber elements\n    var lineCount = codeTree.value.length;\n    if (lineCount === 1 && codeTree.value[0].type === 'text') {\n      // Since codeTree for an unparsable text (e.g. 'a\\na\\na') is [{ type: 'text', value: 'a\\na\\na' }]\n      lineCount = codeTree.value[0].value.split('\\n').length;\n    }\n    var largestLineNumber = lineCount + startingLineNumber;\n    var rows = processLines(codeTree, wrapLines, lineProps, showLineNumbers, showInlineLineNumbers, startingLineNumber, largestLineNumber, lineNumberStyle, wrapLongLines);\n    return /*#__PURE__*/React.createElement(PreTag, preProps, /*#__PURE__*/React.createElement(CodeTag, codeTagProps, !showInlineLineNumbers && allLineNumbers, renderer({\n      rows: rows,\n      stylesheet: style,\n      useInlineStyles: useInlineStyles\n    })));\n  };\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,gDAAgD;AACrF,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,oBAAoB,EAAE,0BAA0B,EAAE,iBAAiB,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC;AACxT,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEJ,eAAe,CAACG,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,OAAOmB,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,IAAIC,YAAY,GAAG,KAAK;AACxB,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACC,KAAK,CAACH,YAAY,CAAC;AAChC;AACA,SAASI,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,kBAAkB,GAAGF,IAAI,CAACE,kBAAkB;IAC5CC,KAAK,GAAGH,IAAI,CAACG,KAAK;EACpB,OAAOF,KAAK,CAACG,GAAG,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC/B,IAAIC,MAAM,GAAGD,CAAC,GAAGJ,kBAAkB;IACnC,OAAO,aAAaV,KAAK,CAACC,aAAa,CAAC,MAAM,EAAE;MAC9Ce,GAAG,EAAE,OAAO,CAACC,MAAM,CAACH,CAAC,CAAC;MACtBI,SAAS,EAAE,sCAAsC;MACjDP,KAAK,EAAE,OAAOA,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACI,MAAM,CAAC,GAAGJ;IACvD,CAAC,EAAE,EAAE,CAACM,MAAM,CAACF,MAAM,EAAE,IAAI,CAAC,CAAC;EAC7B,CAAC,CAAC;AACJ;AACA,SAASI,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,UAAU,GAAGD,KAAK,CAACC,UAAU;IAC/BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,oBAAoB,GAAGH,KAAK,CAACI,cAAc;IAC3CA,cAAc,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG;MACjD,OAAO,EAAE,MAAM;MACfE,YAAY,EAAE;IAChB,CAAC,GAAGF,oBAAoB;IACxBG,iBAAiB,GAAGN,KAAK,CAACO,WAAW;IACrCA,WAAW,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,iBAAiB;IACnEhB,kBAAkB,GAAGU,KAAK,CAACV,kBAAkB;EAC/C,OAAO,aAAaV,KAAK,CAACC,aAAa,CAAC,MAAM,EAAE;IAC9CU,KAAK,EAAE3B,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEN,SAAS,EAAEE,cAAc;EACpD,CAAC,EAAEjB,iBAAiB,CAAC;IACnBE,KAAK,EAAEY,UAAU,CAACQ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAChDnB,KAAK,EAAEgB,WAAW;IAClBjB,kBAAkB,EAAEA;EACtB,CAAC,CAAC,CAAC;AACL;AACA,SAASqB,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,OAAO,EAAE,CAACf,MAAM,CAACe,GAAG,CAACC,QAAQ,CAAC,CAAC,CAACtC,MAAM,EAAE,OAAO,CAAC;AAClD;AACA,SAASuC,mBAAmBA,CAACC,UAAU,EAAEC,qBAAqB,EAAE;EAC9D,OAAO;IACLC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE;MACVvB,GAAG,EAAE,eAAe,CAACC,MAAM,CAACkB,UAAU,CAAC;MACvCjB,SAAS,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,sCAAsC,CAAC;MAC5EP,KAAK,EAAEyB;IACT,CAAC;IACDI,QAAQ,EAAE,CAAC;MACTH,IAAI,EAAE,MAAM;MACZI,KAAK,EAAEN;IACT,CAAC;EACH,CAAC;AACH;AACA,SAASO,wBAAwBA,CAACC,eAAe,EAAER,UAAU,EAAES,iBAAiB,EAAE;EAChF;EACA,IAAIC,sBAAsB,GAAG;IAC3BC,OAAO,EAAE,cAAc;IACvBC,QAAQ,EAAEhB,kBAAkB,CAACa,iBAAiB,CAAC;IAC/CnB,YAAY,EAAE,KAAK;IACnBuB,SAAS,EAAE,OAAO;IAClBC,UAAU,EAAE;EACd,CAAC;EACD;EACA,IAAIC,qBAAqB,GAAG,OAAOP,eAAe,KAAK,UAAU,GAAGA,eAAe,CAACR,UAAU,CAAC,GAAGQ,eAAe;EACjH;EACA,IAAIQ,cAAc,GAAG1D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,sBAAsB,CAAC,EAAEK,qBAAqB,CAAC;EACpG,OAAOC,cAAc;AACvB;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAIb,QAAQ,GAAGa,KAAK,CAACb,QAAQ;IAC3BL,UAAU,GAAGkB,KAAK,CAAClB,UAAU;IAC7BQ,eAAe,GAAGU,KAAK,CAACV,eAAe;IACvCC,iBAAiB,GAAGS,KAAK,CAACT,iBAAiB;IAC3CU,qBAAqB,GAAGD,KAAK,CAACC,qBAAqB;IACnDC,eAAe,GAAGF,KAAK,CAACG,SAAS;IACjCA,SAAS,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,eAAe;IAC7DE,eAAe,GAAGJ,KAAK,CAACnC,SAAS;IACjCA,SAAS,GAAGuC,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;IAC7DC,eAAe,GAAGL,KAAK,CAACK,eAAe;IACvCC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,eAAe,GAAGP,KAAK,CAACQ,SAAS;IACjCA,SAAS,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;EAClE,IAAIrB,UAAU,GAAGsB,SAAS,GAAGpE,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO+D,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACrB,UAAU,CAAC,GAAGqB,SAAS,CAAC,GAAG,CAAC,CAAC;EACxHjB,UAAU,CAAC,WAAW,CAAC,GAAGA,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,CAACtB,MAAM,CAACxC,kBAAkB,CAAC8D,UAAU,CAAC,WAAW,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAChC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAErD,kBAAkB,CAACyC,SAAS,CAAC,CAAC,GAAGA,SAAS;EACzK,IAAIiB,UAAU,IAAImB,qBAAqB,EAAE;IACvC,IAAIlB,qBAAqB,GAAGM,wBAAwB,CAACC,eAAe,EAAER,UAAU,EAAES,iBAAiB,CAAC;IACpGJ,QAAQ,CAACuB,OAAO,CAAC7B,mBAAmB,CAACC,UAAU,EAAEC,qBAAqB,CAAC,CAAC;EAC1E;EACA,IAAIuB,aAAa,GAAGD,eAAe,EAAE;IACnCnB,UAAU,CAAC5B,KAAK,GAAGlB,aAAa,CAAC;MAC/BqD,OAAO,EAAE;IACX,CAAC,EAAEP,UAAU,CAAC5B,KAAK,CAAC;EACtB;EACA,OAAO;IACL0B,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA;EACZ,CAAC;AACH;AACA,SAASwB,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAI/C,SAAS,GAAGxB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACtF,IAAIyE,OAAO,GAAGzE,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACpF,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,IAAI,CAACtE,MAAM,EAAEmB,CAAC,EAAE,EAAE;IACpC,IAAIsD,IAAI,GAAGH,IAAI,CAACnD,CAAC,CAAC;IAClB,IAAIsD,IAAI,CAAC/B,IAAI,KAAK,MAAM,EAAE;MACxB8B,OAAO,CAAC5E,IAAI,CAAC6D,iBAAiB,CAAC;QAC7BZ,QAAQ,EAAE,CAAC4B,IAAI,CAAC;QAChBlD,SAAS,EAAEzC,kBAAkB,CAAC,IAAI4F,GAAG,CAACnD,SAAS,CAAC;MAClD,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIkD,IAAI,CAAC5B,QAAQ,EAAE;MACxB,IAAI8B,UAAU,GAAGpD,SAAS,CAACD,MAAM,CAACmD,IAAI,CAAC7B,UAAU,CAACrB,SAAS,CAAC;MAC5D8C,eAAe,CAACI,IAAI,CAAC5B,QAAQ,EAAE8B,UAAU,CAAC,CAAC1E,OAAO,CAAC,UAAUkB,CAAC,EAAE;QAC9D,OAAOqD,OAAO,CAAC5E,IAAI,CAACuB,CAAC,CAAC;MACxB,CAAC,CAAC;IACJ;EACF;EACA,OAAOqD,OAAO;AAChB;AACA,SAASI,YAAYA,CAACC,QAAQ,EAAEX,SAAS,EAAEL,SAAS,EAAEE,eAAe,EAAEJ,qBAAqB,EAAE5C,kBAAkB,EAAEkC,iBAAiB,EAAED,eAAe,EAAEgB,aAAa,EAAE;EACnK,IAAIc,KAAK;EACT,IAAIR,IAAI,GAAGD,eAAe,CAACQ,QAAQ,CAAC/B,KAAK,CAAC;EAC1C,IAAI0B,OAAO,GAAG,EAAE;EAChB,IAAIO,kBAAkB,GAAG,CAAC,CAAC;EAC3B,IAAIC,KAAK,GAAG,CAAC;EACb,SAASC,iBAAiBA,CAACpC,QAAQ,EAAEL,UAAU,EAAE;IAC/C,IAAIjB,SAAS,GAAGxB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACtF,OAAO0D,iBAAiB,CAAC;MACvBZ,QAAQ,EAAEA,QAAQ;MAClBL,UAAU,EAAEA,UAAU;MACtBQ,eAAe,EAAEA,eAAe;MAChCC,iBAAiB,EAAEA,iBAAiB;MACpCU,qBAAqB,EAAEA,qBAAqB;MAC5CE,SAAS,EAAEA,SAAS;MACpBtC,SAAS,EAAEA,SAAS;MACpBwC,eAAe,EAAEA,eAAe;MAChCC,aAAa,EAAEA,aAAa;MAC5BE,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EACA,SAASgB,mBAAmBA,CAACrC,QAAQ,EAAEL,UAAU,EAAE;IACjD,IAAIuB,eAAe,IAAIvB,UAAU,IAAImB,qBAAqB,EAAE;MAC1D,IAAIlB,qBAAqB,GAAGM,wBAAwB,CAACC,eAAe,EAAER,UAAU,EAAES,iBAAiB,CAAC;MACpGJ,QAAQ,CAACuB,OAAO,CAAC7B,mBAAmB,CAACC,UAAU,EAAEC,qBAAqB,CAAC,CAAC;IAC1E;IACA,OAAOI,QAAQ;EACjB;EACA,SAASsC,UAAUA,CAACtC,QAAQ,EAAEL,UAAU,EAAE;IACxC,IAAIjB,SAAS,GAAGxB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACtF,OAAOmE,SAAS,IAAI3C,SAAS,CAACvB,MAAM,GAAG,CAAC,GAAGiF,iBAAiB,CAACpC,QAAQ,EAAEL,UAAU,EAAEjB,SAAS,CAAC,GAAG2D,mBAAmB,CAACrC,QAAQ,EAAEL,UAAU,CAAC;EAC3I;EACA,IAAI4C,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,IAAIX,IAAI,GAAGH,IAAI,CAACU,KAAK,CAAC;IACtB,IAAIlC,KAAK,GAAG2B,IAAI,CAAC5B,QAAQ,CAAC,CAAC,CAAC,CAACC,KAAK;IAClC,IAAIuC,QAAQ,GAAG5E,WAAW,CAACqC,KAAK,CAAC;IACjC,IAAIuC,QAAQ,EAAE;MACZ,IAAIC,UAAU,GAAGxC,KAAK,CAACX,KAAK,CAAC,IAAI,CAAC;MAClCmD,UAAU,CAACrF,OAAO,CAAC,UAAUsF,IAAI,EAAEpE,CAAC,EAAE;QACpC,IAAIqB,UAAU,GAAGuB,eAAe,IAAIS,OAAO,CAACxE,MAAM,GAAGe,kBAAkB;QACvE,IAAIyE,QAAQ,GAAG;UACb9C,IAAI,EAAE,MAAM;UACZI,KAAK,EAAE,EAAE,CAACxB,MAAM,CAACiE,IAAI,EAAE,IAAI;QAC7B,CAAC;;QAED;QACA,IAAIpE,CAAC,KAAK,CAAC,EAAE;UACX,IAAIsE,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACX,kBAAkB,GAAG,CAAC,EAAEC,KAAK,CAAC,CAAC1D,MAAM,CAACmC,iBAAiB,CAAC;YACjFZ,QAAQ,EAAE,CAAC2C,QAAQ,CAAC;YACpBjE,SAAS,EAAEkD,IAAI,CAAC7B,UAAU,CAACrB;UAC7B,CAAC,CAAC,CAAC;UACH,IAAIoE,KAAK,GAAGR,UAAU,CAACM,SAAS,EAAEjD,UAAU,CAAC;UAC7CgC,OAAO,CAAC5E,IAAI,CAAC+F,KAAK,CAAC;;UAEnB;QACF,CAAC,MAAM,IAAIxE,CAAC,KAAKmE,UAAU,CAACtF,MAAM,GAAG,CAAC,EAAE;UACtC,IAAI4F,WAAW,GAAGtB,IAAI,CAACU,KAAK,GAAG,CAAC,CAAC,IAAIV,IAAI,CAACU,KAAK,GAAG,CAAC,CAAC,CAACnC,QAAQ,IAAIyB,IAAI,CAACU,KAAK,GAAG,CAAC,CAAC,CAACnC,QAAQ,CAAC,CAAC,CAAC;UAC5F,IAAIgD,sBAAsB,GAAG;YAC3BnD,IAAI,EAAE,MAAM;YACZI,KAAK,EAAE,EAAE,CAACxB,MAAM,CAACiE,IAAI;UACvB,CAAC;UACD,IAAIK,WAAW,EAAE;YACf,IAAIE,OAAO,GAAGrC,iBAAiB,CAAC;cAC9BZ,QAAQ,EAAE,CAACgD,sBAAsB,CAAC;cAClCtE,SAAS,EAAEkD,IAAI,CAAC7B,UAAU,CAACrB;YAC7B,CAAC,CAAC;YACF+C,IAAI,CAACyB,MAAM,CAACf,KAAK,GAAG,CAAC,EAAE,CAAC,EAAEc,OAAO,CAAC;UACpC,CAAC,MAAM;YACL,IAAIE,UAAU,GAAG,CAACH,sBAAsB,CAAC;YACzC,IAAII,MAAM,GAAGd,UAAU,CAACa,UAAU,EAAExD,UAAU,EAAEiC,IAAI,CAAC7B,UAAU,CAACrB,SAAS,CAAC;YAC1EiD,OAAO,CAAC5E,IAAI,CAACqG,MAAM,CAAC;UACtB;;UAEA;QACF,CAAC,MAAM;UACL,IAAIC,UAAU,GAAG,CAACV,QAAQ,CAAC;UAC3B,IAAIW,MAAM,GAAGhB,UAAU,CAACe,UAAU,EAAE1D,UAAU,EAAEiC,IAAI,CAAC7B,UAAU,CAACrB,SAAS,CAAC;UAC1EiD,OAAO,CAAC5E,IAAI,CAACuG,MAAM,CAAC;QACtB;MACF,CAAC,CAAC;MACFpB,kBAAkB,GAAGC,KAAK;IAC5B;IACAA,KAAK,EAAE;EACT,CAAC;EACD,OAAOA,KAAK,GAAGV,IAAI,CAACtE,MAAM,EAAE;IAC1BoF,KAAK,CAAC,CAAC;EACT;EACA,IAAIL,kBAAkB,KAAKT,IAAI,CAACtE,MAAM,GAAG,CAAC,EAAE;IAC1C,IAAI6C,QAAQ,GAAGyB,IAAI,CAACoB,KAAK,CAACX,kBAAkB,GAAG,CAAC,EAAET,IAAI,CAACtE,MAAM,CAAC;IAC9D,IAAI6C,QAAQ,IAAIA,QAAQ,CAAC7C,MAAM,EAAE;MAC/B,IAAIwC,UAAU,GAAGuB,eAAe,IAAIS,OAAO,CAACxE,MAAM,GAAGe,kBAAkB;MACvE,IAAIqF,IAAI,GAAGjB,UAAU,CAACtC,QAAQ,EAAEL,UAAU,CAAC;MAC3CgC,OAAO,CAAC5E,IAAI,CAACwG,IAAI,CAAC;IACpB;EACF;EACA,OAAOlC,SAAS,GAAGM,OAAO,GAAG,CAACM,KAAK,GAAG,EAAE,EAAExD,MAAM,CAACzB,KAAK,CAACiF,KAAK,EAAEN,OAAO,CAAC;AACxE;AACA,SAAS6B,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBC,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BC,eAAe,GAAGH,KAAK,CAACG,eAAe;EACzC,OAAOF,IAAI,CAACtF,GAAG,CAAC,UAAUwD,IAAI,EAAEtD,CAAC,EAAE;IACjC,OAAOb,aAAa,CAAC;MACnBmE,IAAI,EAAEA,IAAI;MACV+B,UAAU,EAAEA,UAAU;MACtBC,eAAe,EAAEA,eAAe;MAChCpF,GAAG,EAAE,eAAe,CAACC,MAAM,CAACH,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA,SAASuF,aAAaA,CAACC,YAAY,EAAE;EACnC,OAAOA,YAAY,IAAI,OAAOA,YAAY,CAACC,aAAa,KAAK,WAAW;AAC1E;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIH,YAAY,GAAGG,KAAK,CAACH,YAAY;IACnCI,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IACzBC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACjBC,gBAAgB,GAAGH,KAAK,CAACG,gBAAgB;EAC3C;EACA;;EAEA;EACA,IAAIP,aAAa,CAACC,YAAY,CAAC,EAAE;IAC/B,IAAIO,WAAW,GAAG3G,sBAAsB,CAACoG,YAAY,EAAEI,QAAQ,CAAC;IAChE,IAAIA,QAAQ,KAAK,MAAM,EAAE;MACvB,OAAO;QACLjE,KAAK,EAAEmE,gBAAgB;QACvBF,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,MAAM,IAAIG,WAAW,EAAE;MACtB,OAAOP,YAAY,CAACQ,SAAS,CAACJ,QAAQ,EAAEC,IAAI,CAAC;IAC/C,CAAC,MAAM;MACL,OAAOL,YAAY,CAACC,aAAa,CAACI,IAAI,CAAC;IACzC;EACF;;EAEA;EACA,IAAI;IACF,OAAOD,QAAQ,IAAIA,QAAQ,KAAK,MAAM,GAAG;MACvCjE,KAAK,EAAE6D,YAAY,CAACQ,SAAS,CAACH,IAAI,EAAED,QAAQ;IAC9C,CAAC,GAAG;MACFjE,KAAK,EAAEmE;IACT,CAAC;EACH,CAAC,CAAC,OAAO/H,CAAC,EAAE;IACV,OAAO;MACL4D,KAAK,EAAEmE;IACT,CAAC;EACH;AACF;AACA,eAAe,UAAUG,mBAAmB,EAAEC,YAAY,EAAE;EAC1D,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;IACvC,IAAIR,QAAQ,GAAGQ,KAAK,CAACR,QAAQ;MAC3BlE,QAAQ,GAAG0E,KAAK,CAAC1E,QAAQ;MACzB2E,WAAW,GAAGD,KAAK,CAACvG,KAAK;MACzBA,KAAK,GAAGwG,WAAW,KAAK,KAAK,CAAC,GAAGH,YAAY,GAAGG,WAAW;MAC3DC,iBAAiB,GAAGF,KAAK,CAACG,WAAW;MACrCA,WAAW,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,iBAAiB;MACnEE,kBAAkB,GAAGJ,KAAK,CAACK,YAAY;MACvCA,YAAY,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG;QAC7CpG,SAAS,EAAEwF,QAAQ,GAAG,WAAW,CAACzF,MAAM,CAACyF,QAAQ,CAAC,GAAGxC,SAAS;QAC9DvD,KAAK,EAAElB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkB,KAAK,CAAC,0BAA0B,CAAC,CAAC,EAAEA,KAAK,CAAC,yBAAyB,CAACM,MAAM,CAACyF,QAAQ,EAAE,KAAK,CAAC,CAAC;MACrI,CAAC,GAAGY,kBAAkB;MACtBE,qBAAqB,GAAGN,KAAK,CAACd,eAAe;MAC7CA,eAAe,GAAGoB,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;MACjFC,qBAAqB,GAAGP,KAAK,CAACxD,eAAe;MAC7CA,eAAe,GAAG+D,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;MAClFC,qBAAqB,GAAGR,KAAK,CAAC5D,qBAAqB;MACnDA,qBAAqB,GAAGoE,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;MACvFC,qBAAqB,GAAGT,KAAK,CAACxG,kBAAkB;MAChDA,kBAAkB,GAAGiH,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;MACjFC,wBAAwB,GAAGV,KAAK,CAACU,wBAAwB;MACzDC,qBAAqB,GAAGX,KAAK,CAACvE,eAAe;MAC7CA,eAAe,GAAGkF,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;MAC/EhE,SAAS,GAAGqD,KAAK,CAACrD,SAAS;MAC3BiE,mBAAmB,GAAGZ,KAAK,CAACvD,aAAa;MACzCA,aAAa,GAAGmE,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,mBAAmB;MAC5EC,eAAe,GAAGb,KAAK,CAAC1D,SAAS;MACjCA,SAAS,GAAGuE,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,eAAe;MAC7DC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;MACzBC,YAAY,GAAGf,KAAK,CAACgB,MAAM;MAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;MACvDE,aAAa,GAAGjB,KAAK,CAACkB,OAAO;MAC7BA,OAAO,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,aAAa;MAC3DE,UAAU,GAAGnB,KAAK,CAACP,IAAI;MACvBA,IAAI,GAAG0B,UAAU,KAAK,KAAK,CAAC,GAAG,CAACC,KAAK,CAACC,OAAO,CAAC/F,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,KAAK,EAAE,GAAG6F,UAAU;MACpG/B,YAAY,GAAGY,KAAK,CAACZ,YAAY;MACjCkC,IAAI,GAAGhK,wBAAwB,CAAC0I,KAAK,EAAEvI,SAAS,CAAC;IACnD2H,YAAY,GAAGA,YAAY,IAAIS,mBAAmB;IAClD,IAAI0B,cAAc,GAAG/E,eAAe,GAAG,aAAa1D,KAAK,CAACC,aAAa,CAACkB,cAAc,EAAE;MACtFK,cAAc,EAAEoG,wBAAwB;MACxCtG,SAAS,EAAEiG,YAAY,CAAC5G,KAAK,IAAI,CAAC,CAAC;MACnCgB,WAAW,EAAEgB,eAAe;MAC5BjC,kBAAkB,EAAEA,kBAAkB;MACtCW,UAAU,EAAEsF;IACd,CAAC,CAAC,GAAG,IAAI;IACT,IAAI+B,eAAe,GAAG/H,KAAK,CAACgI,IAAI,IAAIhI,KAAK,CAAC,yBAAyB,CAAC,IAAI;MACtEiI,eAAe,EAAE;IACnB,CAAC;IACD,IAAIC,kBAAkB,GAAGxC,aAAa,CAACC,YAAY,CAAC,GAAG,MAAM,GAAG,SAAS;IACzE,IAAIwC,QAAQ,GAAG1C,eAAe,GAAGpH,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAE4G,IAAI,EAAE;MACvD7H,KAAK,EAAE3B,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAE8G,eAAe,EAAErB,WAAW;IACvD,CAAC,CAAC,GAAGrI,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAE4G,IAAI,EAAE;MAC3BtH,SAAS,EAAEsH,IAAI,CAACtH,SAAS,GAAG,EAAE,CAACD,MAAM,CAAC4H,kBAAkB,EAAE,GAAG,CAAC,CAAC5H,MAAM,CAACuH,IAAI,CAACtH,SAAS,CAAC,GAAG2H,kBAAkB;MAC1GlI,KAAK,EAAE3B,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEyF,WAAW;IACtC,CAAC,CAAC;IACF,IAAI1D,aAAa,EAAE;MACjB4D,YAAY,CAAC5G,KAAK,GAAGlB,aAAa,CAAC;QACjCsJ,UAAU,EAAE;MACd,CAAC,EAAExB,YAAY,CAAC5G,KAAK,CAAC;IACxB,CAAC,MAAM;MACL4G,YAAY,CAAC5G,KAAK,GAAGlB,aAAa,CAAC;QACjCsJ,UAAU,EAAE;MACd,CAAC,EAAExB,YAAY,CAAC5G,KAAK,CAAC;IACxB;IACA,IAAI,CAAC2F,YAAY,EAAE;MACjB,OAAO,aAAatG,KAAK,CAACC,aAAa,CAACiI,MAAM,EAAEY,QAAQ,EAAEL,cAAc,EAAE,aAAazI,KAAK,CAACC,aAAa,CAACmI,OAAO,EAAEb,YAAY,EAAEZ,IAAI,CAAC,CAAC;IAC1I;;IAEA;AACJ;AACA;AACA;IACI,IAAI9C,SAAS,KAAKK,SAAS,IAAI8D,QAAQ,IAAIrE,aAAa,EAAEE,SAAS,GAAG,IAAI;IAC1EmE,QAAQ,GAAGA,QAAQ,IAAIhC,eAAe;IACtC,IAAIY,gBAAgB,GAAG,CAAC;MACtBvE,IAAI,EAAE,MAAM;MACZI,KAAK,EAAEkE;IACT,CAAC,CAAC;IACF,IAAInC,QAAQ,GAAGgC,WAAW,CAAC;MACzBF,YAAY,EAAEA,YAAY;MAC1BI,QAAQ,EAAEA,QAAQ;MAClBC,IAAI,EAAEA,IAAI;MACVC,gBAAgB,EAAEA;IACpB,CAAC,CAAC;IACF,IAAIpC,QAAQ,CAACkC,QAAQ,KAAK,IAAI,EAAE;MAC9BlC,QAAQ,CAAC/B,KAAK,GAAGmE,gBAAgB;IACnC;;IAEA;IACA,IAAIoC,SAAS,GAAGxE,QAAQ,CAAC/B,KAAK,CAAC9C,MAAM;IACrC,IAAIqJ,SAAS,KAAK,CAAC,IAAIxE,QAAQ,CAAC/B,KAAK,CAAC,CAAC,CAAC,CAACJ,IAAI,KAAK,MAAM,EAAE;MACxD;MACA2G,SAAS,GAAGxE,QAAQ,CAAC/B,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAACX,KAAK,CAAC,IAAI,CAAC,CAACnC,MAAM;IACxD;IACA,IAAIiD,iBAAiB,GAAGoG,SAAS,GAAGtI,kBAAkB;IACtD,IAAIwF,IAAI,GAAG3B,YAAY,CAACC,QAAQ,EAAEX,SAAS,EAAEL,SAAS,EAAEE,eAAe,EAAEJ,qBAAqB,EAAE5C,kBAAkB,EAAEkC,iBAAiB,EAAED,eAAe,EAAEgB,aAAa,CAAC;IACtK,OAAO,aAAa3D,KAAK,CAACC,aAAa,CAACiI,MAAM,EAAEY,QAAQ,EAAE,aAAa9I,KAAK,CAACC,aAAa,CAACmI,OAAO,EAAEb,YAAY,EAAE,CAACjE,qBAAqB,IAAImF,cAAc,EAAET,QAAQ,CAAC;MACnK9B,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAExF,KAAK;MACjByF,eAAe,EAAEA;IACnB,CAAC,CAAC,CAAC,CAAC;EACN,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}