{"ast": null, "code": "'use strict';\n\nvar refractorJava = require('./java.js');\nmodule.exports = scala;\nscala.displayName = 'scala';\nscala.aliases = [];\nfunction scala(Prism) {\n  Prism.register(refractorJava);\n  Prism.languages.scala = Prism.languages.extend('java', {\n    'triple-quoted-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword: /<-|=>|\\b(?:abstract|case|catch|class|def|do|else|extends|final|finally|for|forSome|if|implicit|import|lazy|match|new|null|object|override|package|private|protected|return|sealed|self|super|this|throw|trait|try|type|val|var|while|with|yield)\\b/,\n    number: /\\b0x(?:[\\da-f]*\\.)?[\\da-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e\\d+)?[dfl]?/i,\n    builtin: /\\b(?:Any|AnyRef|AnyVal|Boolean|Byte|Char|Double|Float|Int|Long|Nothing|Short|String|Unit)\\b/,\n    symbol: /'[^\\d\\s\\\\]\\w*/\n  });\n  Prism.languages.insertBefore('scala', 'triple-quoted-string', {\n    'string-interpolation': {\n      pattern: /\\b[a-z]\\w*(?:\"\"\"(?:[^$]|\\$(?:[^{]|\\{(?:[^{}]|\\{[^{}]*\\})*\\}))*?\"\"\"|\"(?:[^$\"\\r\\n]|\\$(?:[^{]|\\{(?:[^{}]|\\{[^{}]*\\})*\\}))*\")/i,\n      greedy: true,\n      inside: {\n        id: {\n          pattern: /^\\w+/,\n          greedy: true,\n          alias: 'function'\n        },\n        escape: {\n          pattern: /\\\\\\$\"|\\$[$\"]/,\n          greedy: true,\n          alias: 'symbol'\n        },\n        interpolation: {\n          pattern: /\\$(?:\\w+|\\{(?:[^{}]|\\{[^{}]*\\})*\\})/,\n          greedy: true,\n          inside: {\n            punctuation: /^\\$\\{?|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.scala\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  });\n  delete Prism.languages.scala['class-name'];\n  delete Prism.languages.scala['function'];\n}", "map": {"version": 3, "names": ["refractorJava", "require", "module", "exports", "scala", "displayName", "aliases", "Prism", "register", "languages", "extend", "pattern", "greedy", "alias", "string", "keyword", "number", "builtin", "symbol", "insertBefore", "inside", "id", "escape", "interpolation", "punctuation", "expression"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/scala.js"], "sourcesContent": ["'use strict'\nvar refractorJava = require('./java.js')\nmodule.exports = scala\nscala.displayName = 'scala'\nscala.aliases = []\nfunction scala(Prism) {\n  Prism.register(refractorJava)\n  Prism.languages.scala = Prism.languages.extend('java', {\n    'triple-quoted-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword:\n      /<-|=>|\\b(?:abstract|case|catch|class|def|do|else|extends|final|finally|for|forSome|if|implicit|import|lazy|match|new|null|object|override|package|private|protected|return|sealed|self|super|this|throw|trait|try|type|val|var|while|with|yield)\\b/,\n    number:\n      /\\b0x(?:[\\da-f]*\\.)?[\\da-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e\\d+)?[dfl]?/i,\n    builtin:\n      /\\b(?:Any|AnyRef|AnyVal|Boolean|Byte|Char|Double|Float|Int|Long|Nothing|Short|String|Unit)\\b/,\n    symbol: /'[^\\d\\s\\\\]\\w*/\n  })\n  Prism.languages.insertBefore('scala', 'triple-quoted-string', {\n    'string-interpolation': {\n      pattern:\n        /\\b[a-z]\\w*(?:\"\"\"(?:[^$]|\\$(?:[^{]|\\{(?:[^{}]|\\{[^{}]*\\})*\\}))*?\"\"\"|\"(?:[^$\"\\r\\n]|\\$(?:[^{]|\\{(?:[^{}]|\\{[^{}]*\\})*\\}))*\")/i,\n      greedy: true,\n      inside: {\n        id: {\n          pattern: /^\\w+/,\n          greedy: true,\n          alias: 'function'\n        },\n        escape: {\n          pattern: /\\\\\\$\"|\\$[$\"]/,\n          greedy: true,\n          alias: 'symbol'\n        },\n        interpolation: {\n          pattern: /\\$(?:\\w+|\\{(?:[^{}]|\\{[^{}]*\\})*\\})/,\n          greedy: true,\n          inside: {\n            punctuation: /^\\$\\{?|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.scala\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  delete Prism.languages.scala['class-name']\n  delete Prism.languages.scala['function']\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxCC,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACR,aAAa,CAAC;EAC7BO,KAAK,CAACE,SAAS,CAACL,KAAK,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE;IACrD,sBAAsB,EAAE;MACtBC,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,iCAAiC;MAC1CC,MAAM,EAAE;IACV,CAAC;IACDG,OAAO,EACL,oPAAoP;IACtPC,MAAM,EACJ,yEAAyE;IAC3EC,OAAO,EACL,6FAA6F;IAC/FC,MAAM,EAAE;EACV,CAAC,CAAC;EACFX,KAAK,CAACE,SAAS,CAACU,YAAY,CAAC,OAAO,EAAE,sBAAsB,EAAE;IAC5D,sBAAsB,EAAE;MACtBR,OAAO,EACL,4HAA4H;MAC9HC,MAAM,EAAE,IAAI;MACZQ,MAAM,EAAE;QACNC,EAAE,EAAE;UACFV,OAAO,EAAE,MAAM;UACfC,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE;QACT,CAAC;QACDS,MAAM,EAAE;UACNX,OAAO,EAAE,cAAc;UACvBC,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE;QACT,CAAC;QACDU,aAAa,EAAE;UACbZ,OAAO,EAAE,qCAAqC;UAC9CC,MAAM,EAAE,IAAI;UACZQ,MAAM,EAAE;YACNI,WAAW,EAAE,YAAY;YACzBC,UAAU,EAAE;cACVd,OAAO,EAAE,SAAS;cAClBS,MAAM,EAAEb,KAAK,CAACE,SAAS,CAACL;YAC1B;UACF;QACF,CAAC;QACDU,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;EACF,OAAOP,KAAK,CAACE,SAAS,CAACL,KAAK,CAAC,YAAY,CAAC;EAC1C,OAAOG,KAAK,CAACE,SAAS,CAACL,KAAK,CAAC,UAAU,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}