{"ast": null, "code": "/*\nLanguage: Diff\nDescription: Unified and context diff\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/diffutils/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction diff(hljs) {\n  return {\n    name: 'Diff',\n    aliases: ['patch'],\n    contains: [{\n      className: 'meta',\n      relevance: 10,\n      variants: [{\n        begin: /^@@ +-\\d+,\\d+ +\\+\\d+,\\d+ +@@/\n      }, {\n        begin: /^\\*\\*\\* +\\d+,\\d+ +\\*\\*\\*\\*$/\n      }, {\n        begin: /^--- +\\d+,\\d+ +----$/\n      }]\n    }, {\n      className: 'comment',\n      variants: [{\n        begin: /Index: /,\n        end: /$/\n      }, {\n        begin: /^index/,\n        end: /$/\n      }, {\n        begin: /={3,}/,\n        end: /$/\n      }, {\n        begin: /^-{3}/,\n        end: /$/\n      }, {\n        begin: /^\\*{3} /,\n        end: /$/\n      }, {\n        begin: /^\\+{3}/,\n        end: /$/\n      }, {\n        begin: /^\\*{15}$/\n      }, {\n        begin: /^diff --git/,\n        end: /$/\n      }]\n    }, {\n      className: 'addition',\n      begin: /^\\+/,\n      end: /$/\n    }, {\n      className: 'deletion',\n      begin: /^-/,\n      end: /$/\n    }, {\n      className: 'addition',\n      begin: /^!/,\n      end: /$/\n    }]\n  };\n}\nmodule.exports = diff;", "map": {"version": 3, "names": ["diff", "hljs", "name", "aliases", "contains", "className", "relevance", "variants", "begin", "end", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/diff.js"], "sourcesContent": ["/*\nLanguage: Diff\nDescription: Unified and context diff\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/diffutils/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction diff(hljs) {\n  return {\n    name: 'Diff',\n    aliases: ['patch'],\n    contains: [\n      {\n        className: 'meta',\n        relevance: 10,\n        variants: [\n          {\n            begin: /^@@ +-\\d+,\\d+ +\\+\\d+,\\d+ +@@/\n          },\n          {\n            begin: /^\\*\\*\\* +\\d+,\\d+ +\\*\\*\\*\\*$/\n          },\n          {\n            begin: /^--- +\\d+,\\d+ +----$/\n          }\n        ]\n      },\n      {\n        className: 'comment',\n        variants: [\n          {\n            begin: /Index: /,\n            end: /$/\n          },\n          {\n            begin: /^index/,\n            end: /$/\n          },\n          {\n            begin: /={3,}/,\n            end: /$/\n          },\n          {\n            begin: /^-{3}/,\n            end: /$/\n          },\n          {\n            begin: /^\\*{3} /,\n            end: /$/\n          },\n          {\n            begin: /^\\+{3}/,\n            end: /$/\n          },\n          {\n            begin: /^\\*{15}$/\n          },\n          {\n            begin: /^diff --git/,\n            end: /$/\n          }\n        ]\n      },\n      {\n        className: 'addition',\n        begin: /^\\+/,\n        end: /$/\n      },\n      {\n        className: 'deletion',\n        begin: /^-/,\n        end: /$/\n      },\n      {\n        className: 'addition',\n        begin: /^!/,\n        end: /$/\n      }\n    ]\n  };\n}\n\nmodule.exports = diff;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACD;MACEH,SAAS,EAAE,SAAS;MACpBE,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE,aAAa;QACpBC,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACD;MACEJ,SAAS,EAAE,UAAU;MACrBG,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IACP,CAAC,EACD;MACEJ,SAAS,EAAE,UAAU;MACrBG,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACEJ,SAAS,EAAE,UAAU;MACrBG,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGX,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}