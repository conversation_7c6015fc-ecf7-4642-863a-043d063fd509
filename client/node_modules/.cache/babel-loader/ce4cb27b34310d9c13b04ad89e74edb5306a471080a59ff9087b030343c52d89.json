{"ast": null, "code": "'use strict';\n\nmodule.exports = swift;\nswift.displayName = 'swift';\nswift.aliases = [];\nfunction swift(Prism) {\n  Prism.languages.swift = {\n    comment: {\n      // Nested comments are supported up to 2 levels\n      pattern: /(^|[^\\\\:])(?:\\/\\/.*|\\/\\*(?:[^/*]|\\/(?!\\*)|\\*(?!\\/)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\*\\/)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'string-literal': [\n    // https://docs.swift.org/swift-book/LanguageGuide/StringsAndCharacters.html\n    {\n      pattern: RegExp(/(^|[^\"#])/.source + '(?:' +\n      // single-line string\n      /\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^(])|[^\\\\\\r\\n\"])*\"/.source + '|' +\n      // multi-line string\n      /\"\"\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|[^(])|[^\\\\\"]|\"(?!\"\"))*\"\"\"/.source + ')' + /(?![\"#])/.source),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(\\\\\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        'interpolation-punctuation': {\n          pattern: /^\\)|\\\\\\($/,\n          alias: 'punctuation'\n        },\n        punctuation: /\\\\(?=[\\r\\n])/,\n        string: /[\\s\\S]+/\n      }\n    }, {\n      pattern: RegExp(/(^|[^\"#])(#+)/.source + '(?:' +\n      // single-line string\n      /\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^#])|[^\\\\\\r\\n])*?\"/.source + '|' +\n      // multi-line string\n      /\"\"\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|[^#])|[^\\\\])*?\"\"\"/.source + ')' + '\\\\2'),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(\\\\#+\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        'interpolation-punctuation': {\n          pattern: /^\\)|\\\\#+\\($/,\n          alias: 'punctuation'\n        },\n        string: /[\\s\\S]+/\n      }\n    }],\n    directive: {\n      // directives with conditions\n      pattern: RegExp(/#/.source + '(?:' + (/(?:elseif|if)\\b/.source + '(?:[ \\t]*' +\n      // This regex is a little complex. It's equivalent to this:\n      //   (?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*<round>)?|<round>)(?:[ \\t]*(?:&&|\\|\\|))?\n      // where <round> is a general parentheses expression.\n      /(?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*\\((?:[^()]|\\([^()]*\\))*\\))?|\\((?:[^()]|\\([^()]*\\))*\\))(?:[ \\t]*(?:&&|\\|\\|))?/.source + ')+') + '|' + /(?:else|endif)\\b/.source + ')'),\n      alias: 'property',\n      inside: {\n        'directive-name': /^#\\w+/,\n        boolean: /\\b(?:false|true)\\b/,\n        number: /\\b\\d+(?:\\.\\d+)*\\b/,\n        operator: /!|&&|\\|\\||[<>]=?/,\n        punctuation: /[(),]/\n      }\n    },\n    literal: {\n      pattern: /#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\\b/,\n      alias: 'constant'\n    },\n    'other-directive': {\n      pattern: /#\\w+\\b/,\n      alias: 'property'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'atrule'\n    },\n    'function-definition': {\n      pattern: /(\\bfunc\\s+)\\w+/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    label: {\n      // https://docs.swift.org/swift-book/LanguageGuide/ControlFlow.html#ID141\n      pattern: /\\b(break|continue)\\s+\\w+|\\b[a-zA-Z_]\\w*(?=\\s*:\\s*(?:for|repeat|while)\\b)/,\n      lookbehind: true,\n      alias: 'important'\n    },\n    keyword: /\\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    nil: {\n      pattern: /\\bnil\\b/,\n      alias: 'constant'\n    },\n    'short-argument': /\\$\\d+\\b/,\n    omit: {\n      pattern: /\\b_\\b/,\n      alias: 'keyword'\n    },\n    number: /\\b(?:[\\d_]+(?:\\.[\\de_]+)?|0x[a-f0-9_]+(?:\\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b/i,\n    // A class name must start with an upper-case letter and be either 1 letter long or contain a lower-case letter.\n    'class-name': /\\b[A-Z](?:[A-Z_\\d]*[a-z]\\w*)?\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant: /\\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\\b/,\n    // Operators are generic in Swift. Developers can even create new operators (e.g. +++).\n    // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html#ID481\n    // This regex only supports ASCII operators.\n    operator: /[-+*/%=!<>&|^~?]+|\\.[.\\-+*/%=!<>&|^~?]+/,\n    punctuation: /[{}[\\]();,.:\\\\]/\n  };\n  Prism.languages.swift['string-literal'].forEach(function (rule) {\n    rule.inside['interpolation'].inside = Prism.languages.swift;\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "swift", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "RegExp", "source", "inside", "interpolation", "alias", "punctuation", "string", "directive", "boolean", "number", "operator", "literal", "attribute", "label", "keyword", "nil", "omit", "function", "constant", "for<PERSON>ach", "rule"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/swift.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = swift\nswift.displayName = 'swift'\nswift.aliases = []\nfunction swift(Prism) {\n  Prism.languages.swift = {\n    comment: {\n      // Nested comments are supported up to 2 levels\n      pattern:\n        /(^|[^\\\\:])(?:\\/\\/.*|\\/\\*(?:[^/*]|\\/(?!\\*)|\\*(?!\\/)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\*\\/)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'string-literal': [\n      // https://docs.swift.org/swift-book/LanguageGuide/StringsAndCharacters.html\n      {\n        pattern: RegExp(\n          /(^|[^\"#])/.source +\n            '(?:' + // single-line string\n            /\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^(])|[^\\\\\\r\\n\"])*\"/\n              .source +\n            '|' + // multi-line string\n            /\"\"\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|[^(])|[^\\\\\"]|\"(?!\"\"))*\"\"\"/\n              .source +\n            ')' +\n            /(?![\"#])/.source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /(\\\\\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          'interpolation-punctuation': {\n            pattern: /^\\)|\\\\\\($/,\n            alias: 'punctuation'\n          },\n          punctuation: /\\\\(?=[\\r\\n])/,\n          string: /[\\s\\S]+/\n        }\n      },\n      {\n        pattern: RegExp(\n          /(^|[^\"#])(#+)/.source +\n            '(?:' + // single-line string\n            /\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^#])|[^\\\\\\r\\n])*?\"/\n              .source +\n            '|' + // multi-line string\n            /\"\"\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|[^#])|[^\\\\])*?\"\"\"/.source +\n            ')' +\n            '\\\\2'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /(\\\\#+\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          'interpolation-punctuation': {\n            pattern: /^\\)|\\\\#+\\($/,\n            alias: 'punctuation'\n          },\n          string: /[\\s\\S]+/\n        }\n      }\n    ],\n    directive: {\n      // directives with conditions\n      pattern: RegExp(\n        /#/.source +\n          '(?:' +\n          (/(?:elseif|if)\\b/.source +\n            '(?:[ \\t]*' + // This regex is a little complex. It's equivalent to this:\n            //   (?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*<round>)?|<round>)(?:[ \\t]*(?:&&|\\|\\|))?\n            // where <round> is a general parentheses expression.\n            /(?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*\\((?:[^()]|\\([^()]*\\))*\\))?|\\((?:[^()]|\\([^()]*\\))*\\))(?:[ \\t]*(?:&&|\\|\\|))?/\n              .source +\n            ')+') +\n          '|' +\n          /(?:else|endif)\\b/.source +\n          ')'\n      ),\n      alias: 'property',\n      inside: {\n        'directive-name': /^#\\w+/,\n        boolean: /\\b(?:false|true)\\b/,\n        number: /\\b\\d+(?:\\.\\d+)*\\b/,\n        operator: /!|&&|\\|\\||[<>]=?/,\n        punctuation: /[(),]/\n      }\n    },\n    literal: {\n      pattern:\n        /#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\\b/,\n      alias: 'constant'\n    },\n    'other-directive': {\n      pattern: /#\\w+\\b/,\n      alias: 'property'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'atrule'\n    },\n    'function-definition': {\n      pattern: /(\\bfunc\\s+)\\w+/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    label: {\n      // https://docs.swift.org/swift-book/LanguageGuide/ControlFlow.html#ID141\n      pattern:\n        /\\b(break|continue)\\s+\\w+|\\b[a-zA-Z_]\\w*(?=\\s*:\\s*(?:for|repeat|while)\\b)/,\n      lookbehind: true,\n      alias: 'important'\n    },\n    keyword:\n      /\\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    nil: {\n      pattern: /\\bnil\\b/,\n      alias: 'constant'\n    },\n    'short-argument': /\\$\\d+\\b/,\n    omit: {\n      pattern: /\\b_\\b/,\n      alias: 'keyword'\n    },\n    number:\n      /\\b(?:[\\d_]+(?:\\.[\\de_]+)?|0x[a-f0-9_]+(?:\\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b/i,\n    // A class name must start with an upper-case letter and be either 1 letter long or contain a lower-case letter.\n    'class-name': /\\b[A-Z](?:[A-Z_\\d]*[a-z]\\w*)?\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant: /\\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\\b/,\n    // Operators are generic in Swift. Developers can even create new operators (e.g. +++).\n    // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html#ID481\n    // This regex only supports ASCII operators.\n    operator: /[-+*/%=!<>&|^~?]+|\\.[.\\-+*/%=!<>&|^~?]+/,\n    punctuation: /[{}[\\]();,.:\\\\]/\n  }\n  Prism.languages.swift['string-literal'].forEach(function (rule) {\n    rule.inside['interpolation'].inside = Prism.languages.swift\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE;MACP;MACAC,OAAO,EACL,sFAAsF;MACxFC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,gBAAgB,EAAE;IAChB;IACA;MACEF,OAAO,EAAEG,MAAM,CACb,WAAW,CAACC,MAAM,GAChB,KAAK;MAAG;MACR,6DAA6D,CAC1DA,MAAM,GACT,GAAG;MAAG;MACN,gEAAgE,CAC7DA,MAAM,GACT,GAAG,GACH,UAAU,CAACA,MACf,CAAC;MACDH,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACNC,aAAa,EAAE;UACbN,OAAO,EAAE,mCAAmC;UAC5CC,UAAU,EAAE,IAAI;UAChBI,MAAM,EAAE,IAAI,CAAC;QACf,CAAC;QACD,2BAA2B,EAAE;UAC3BL,OAAO,EAAE,WAAW;UACpBO,KAAK,EAAE;QACT,CAAC;QACDC,WAAW,EAAE,cAAc;QAC3BC,MAAM,EAAE;MACV;IACF,CAAC,EACD;MACET,OAAO,EAAEG,MAAM,CACb,eAAe,CAACC,MAAM,GACpB,KAAK;MAAG;MACR,+DAA+D,CAC5DA,MAAM,GACT,GAAG;MAAG;MACN,0DAA0D,CAACA,MAAM,GACjE,GAAG,GACH,KACJ,CAAC;MACDH,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACNC,aAAa,EAAE;UACbN,OAAO,EAAE,qCAAqC;UAC9CC,UAAU,EAAE,IAAI;UAChBI,MAAM,EAAE,IAAI,CAAC;QACf,CAAC;QACD,2BAA2B,EAAE;UAC3BL,OAAO,EAAE,aAAa;UACtBO,KAAK,EAAE;QACT,CAAC;QACDE,MAAM,EAAE;MACV;IACF,CAAC,CACF;IACDC,SAAS,EAAE;MACT;MACAV,OAAO,EAAEG,MAAM,CACb,GAAG,CAACC,MAAM,GACR,KAAK,IACJ,iBAAiB,CAACA,MAAM,GACvB,WAAW;MAAG;MACd;MACA;MACA,6GAA6G,CAC1GA,MAAM,GACT,IAAI,CAAC,GACP,GAAG,GACH,kBAAkB,CAACA,MAAM,GACzB,GACJ,CAAC;MACDG,KAAK,EAAE,UAAU;MACjBF,MAAM,EAAE;QACN,gBAAgB,EAAE,OAAO;QACzBM,OAAO,EAAE,oBAAoB;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,QAAQ,EAAE,kBAAkB;QAC5BL,WAAW,EAAE;MACf;IACF,CAAC;IACDM,OAAO,EAAE;MACPd,OAAO,EACL,0FAA0F;MAC5FO,KAAK,EAAE;IACT,CAAC;IACD,iBAAiB,EAAE;MACjBP,OAAO,EAAE,QAAQ;MACjBO,KAAK,EAAE;IACT,CAAC;IACDQ,SAAS,EAAE;MACTf,OAAO,EAAE,MAAM;MACfO,KAAK,EAAE;IACT,CAAC;IACD,qBAAqB,EAAE;MACrBP,OAAO,EAAE,gBAAgB;MACzBC,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE;IACT,CAAC;IACDS,KAAK,EAAE;MACL;MACAhB,OAAO,EACL,0EAA0E;MAC5EC,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE;IACT,CAAC;IACDU,OAAO,EACL,snBAAsnB;IACxnBN,OAAO,EAAE,oBAAoB;IAC7BO,GAAG,EAAE;MACHlB,OAAO,EAAE,SAAS;MAClBO,KAAK,EAAE;IACT,CAAC;IACD,gBAAgB,EAAE,SAAS;IAC3BY,IAAI,EAAE;MACJnB,OAAO,EAAE,OAAO;MAChBO,KAAK,EAAE;IACT,CAAC;IACDK,MAAM,EACJ,iFAAiF;IACnF;IACA,YAAY,EAAE,iCAAiC;IAC/CQ,QAAQ,EAAE,uBAAuB;IACjCC,QAAQ,EAAE,qCAAqC;IAC/C;IACA;IACA;IACAR,QAAQ,EAAE,yCAAyC;IACnDL,WAAW,EAAE;EACf,CAAC;EACDX,KAAK,CAACC,SAAS,CAACJ,KAAK,CAAC,gBAAgB,CAAC,CAAC4B,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC9DA,IAAI,CAAClB,MAAM,CAAC,eAAe,CAAC,CAACA,MAAM,GAAGR,KAAK,CAACC,SAAS,CAACJ,KAAK;EAC7D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}