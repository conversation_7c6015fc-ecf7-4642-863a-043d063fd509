{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = handlebars;\nhandlebars.displayName = 'handlebars';\nhandlebars.aliases = ['hbs'];\nfunction handlebars(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    Prism.languages.handlebars = {\n      comment: /\\{\\{![\\s\\S]*?\\}\\}/,\n      delimiter: {\n        pattern: /^\\{\\{\\{?|\\}\\}\\}?$/,\n        alias: 'punctuation'\n      },\n      string: /([\"'])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      number: /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][+-]?\\d+)?/,\n      boolean: /\\b(?:false|true)\\b/,\n      block: {\n        pattern: /^(\\s*(?:~\\s*)?)[#\\/]\\S+?(?=\\s*(?:~\\s*)?$|\\s)/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      brackets: {\n        pattern: /\\[[^\\]]+\\]/,\n        inside: {\n          punctuation: /\\[|\\]/,\n          variable: /[\\s\\S]+/\n        }\n      },\n      punctuation: /[!\"#%&':()*+,.\\/;<=>@\\[\\\\\\]^`{|}~]/,\n      variable: /[^!\"#%&'()*+,\\/;<=>@\\[\\\\\\]^`{|}~\\s]+/\n    };\n    Prism.hooks.add('before-tokenize', function (env) {\n      var handlebarsPattern = /\\{\\{\\{[\\s\\S]+?\\}\\}\\}|\\{\\{[\\s\\S]+?\\}\\}/g;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'handlebars', handlebarsPattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'handlebars');\n    });\n    Prism.languages.hbs = Prism.languages.handlebars;\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "handlebars", "displayName", "aliases", "Prism", "register", "languages", "comment", "delimiter", "pattern", "alias", "string", "number", "boolean", "block", "lookbehind", "brackets", "inside", "punctuation", "variable", "hooks", "add", "env", "handlebarsPattern", "buildPlaceholders", "tokenizePlaceholders", "hbs"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/handlebars.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = handlebars\nhandlebars.displayName = 'handlebars'\nhandlebars.aliases = ['hbs']\nfunction handlebars(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.handlebars = {\n      comment: /\\{\\{![\\s\\S]*?\\}\\}/,\n      delimiter: {\n        pattern: /^\\{\\{\\{?|\\}\\}\\}?$/,\n        alias: 'punctuation'\n      },\n      string: /([\"'])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      number: /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][+-]?\\d+)?/,\n      boolean: /\\b(?:false|true)\\b/,\n      block: {\n        pattern: /^(\\s*(?:~\\s*)?)[#\\/]\\S+?(?=\\s*(?:~\\s*)?$|\\s)/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      brackets: {\n        pattern: /\\[[^\\]]+\\]/,\n        inside: {\n          punctuation: /\\[|\\]/,\n          variable: /[\\s\\S]+/\n        }\n      },\n      punctuation: /[!\"#%&':()*+,.\\/;<=>@\\[\\\\\\]^`{|}~]/,\n      variable: /[^!\"#%&'()*+,\\/;<=>@\\[\\\\\\]^`{|}~\\s]+/\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var handlebarsPattern = /\\{\\{\\{[\\s\\S]+?\\}\\}\\}|\\{\\{[\\s\\S]+?\\}\\}/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'handlebars',\n        handlebarsPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(\n        env,\n        'handlebars'\n      )\n    })\n    Prism.languages.hbs = Prism.languages.handlebars\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AAC5B,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACxC,CAAC,UAAUO,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,UAAU,GAAG;MAC3BM,OAAO,EAAE,mBAAmB;MAC5BC,SAAS,EAAE;QACTC,OAAO,EAAE,mBAAmB;QAC5BC,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAE,kCAAkC;MAC1CC,MAAM,EAAE,gEAAgE;MACxEC,OAAO,EAAE,oBAAoB;MAC7BC,KAAK,EAAE;QACLL,OAAO,EAAE,8CAA8C;QACvDM,UAAU,EAAE,IAAI;QAChBL,KAAK,EAAE;MACT,CAAC;MACDM,QAAQ,EAAE;QACRP,OAAO,EAAE,YAAY;QACrBQ,MAAM,EAAE;UACNC,WAAW,EAAE,OAAO;UACpBC,QAAQ,EAAE;QACZ;MACF,CAAC;MACDD,WAAW,EAAE,oCAAoC;MACjDC,QAAQ,EAAE;IACZ,CAAC;IACDf,KAAK,CAACgB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIC,iBAAiB,GAAG,wCAAwC;MAChEnB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACkB,iBAAiB,CACpDF,GAAG,EACH,YAAY,EACZC,iBACF,CAAC;IACH,CAAC,CAAC;IACFnB,KAAK,CAACgB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/ClB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACmB,oBAAoB,CACvDH,GAAG,EACH,YACF,CAAC;IACH,CAAC,CAAC;IACFlB,KAAK,CAACE,SAAS,CAACoB,GAAG,GAAGtB,KAAK,CAACE,SAAS,CAACL,UAAU;EAClD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}