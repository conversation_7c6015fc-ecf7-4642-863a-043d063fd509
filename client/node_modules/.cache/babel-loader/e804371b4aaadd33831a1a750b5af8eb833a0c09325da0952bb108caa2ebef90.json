{"ast": null, "code": "'use strict';\n\nmodule.exports = processing;\nprocessing.displayName = 'processing';\nprocessing.aliases = [];\nfunction processing(Prism) {\n  Prism.languages.processing = Prism.languages.extend('clike', {\n    keyword: /\\b(?:break|case|catch|class|continue|default|else|extends|final|for|if|implements|import|new|null|private|public|return|static|super|switch|this|try|void|while)\\b/,\n    // Spaces are allowed between function name and parenthesis\n    function: /\\b\\w+(?=\\s*\\()/,\n    operator: /<[<=]?|>[>=]?|&&?|\\|\\|?|[%?]|[!=+\\-*\\/]=?/\n  });\n  Prism.languages.insertBefore('processing', 'number', {\n    // Special case: XML is a type\n    constant: /\\b(?!XML\\b)[A-Z][A-Z\\d_]+\\b/,\n    type: {\n      pattern: /\\b(?:boolean|byte|char|color|double|float|int|[A-Z]\\w*)\\b/,\n      alias: 'class-name'\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "processing", "displayName", "aliases", "Prism", "languages", "extend", "keyword", "function", "operator", "insertBefore", "constant", "type", "pattern", "alias"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/processing.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = processing\nprocessing.displayName = 'processing'\nprocessing.aliases = []\nfunction processing(Prism) {\n  Prism.languages.processing = Prism.languages.extend('clike', {\n    keyword:\n      /\\b(?:break|case|catch|class|continue|default|else|extends|final|for|if|implements|import|new|null|private|public|return|static|super|switch|this|try|void|while)\\b/,\n    // Spaces are allowed between function name and parenthesis\n    function: /\\b\\w+(?=\\s*\\()/,\n    operator: /<[<=]?|>[>=]?|&&?|\\|\\|?|[%?]|[!=+\\-*\\/]=?/\n  })\n  Prism.languages.insertBefore('processing', 'number', {\n    // Special case: XML is a type\n    constant: /\\b(?!XML\\b)[A-Z][A-Z\\d_]+\\b/,\n    type: {\n      pattern: /\\b(?:boolean|byte|char|color|double|float|int|[A-Z]\\w*)\\b/,\n      alias: 'class-name'\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,EAAE;AACvB,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IAC3DC,OAAO,EACL,oKAAoK;IACtK;IACAC,QAAQ,EAAE,gBAAgB;IAC1BC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFL,KAAK,CAACC,SAAS,CAACK,YAAY,CAAC,YAAY,EAAE,QAAQ,EAAE;IACnD;IACAC,QAAQ,EAAE,6BAA6B;IACvCC,IAAI,EAAE;MACJC,OAAO,EAAE,2DAA2D;MACpEC,KAAK,EAAE;IACT;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}