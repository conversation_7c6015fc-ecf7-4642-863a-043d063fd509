{"ast": null, "code": "'use strict';\n\nmodule.exports = puppet;\npuppet.displayName = 'puppet';\npuppet.aliases = [];\nfunction puppet(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.puppet = {\n      heredoc: [\n      // Matches the content of a quoted heredoc string (subject to interpolation)\n      {\n        pattern: /(@\\(\"([^\"\\r\\n\\/):]+)\"(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          // Matches the end tag\n          punctuation: /(?=\\S).*\\S(?= *$)/ // See interpolation below\n        }\n      },\n      // Matches the content of an unquoted heredoc string (no interpolation)\n      {\n        pattern: /(@\\(([^\"\\r\\n\\/):]+)(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          // Matches the end tag\n          punctuation: /(?=\\S).*\\S(?= *$)/\n        }\n      },\n      // Matches the start tag of heredoc strings\n      {\n        pattern: /@\\(\"?(?:[^\"\\r\\n\\/):]+)\"?(?:\\/[nrts$uL]*)?\\)/,\n        alias: 'string',\n        inside: {\n          punctuation: {\n            pattern: /(\\().+?(?=\\))/,\n            lookbehind: true\n          }\n        }\n      }],\n      'multiline-comment': {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true,\n        greedy: true,\n        alias: 'comment'\n      },\n      regex: {\n        // Must be prefixed with the keyword \"node\" or a non-word char\n        pattern: /((?:\\bnode\\s+|[~=\\(\\[\\{,]\\s*|[=+]>\\s*|^\\s*))\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/(?:[imx]+\\b|\\B)/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          // Extended regexes must have the x flag. They can contain single-line comments.\n          'extended-regex': {\n            pattern: /^\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/[im]*x[im]*$/,\n            inside: {\n              comment: /#.*/\n            }\n          }\n        }\n      },\n      comment: {\n        pattern: /(^|[^\\\\])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      string: {\n        // Allow for one nested level of double quotes inside interpolation\n        pattern: /([\"'])(?:\\$\\{(?:[^'\"}]|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}|\\$(?!\\{)|(?!\\1)[^\\\\$]|\\\\[\\s\\S])*\\1/,\n        greedy: true,\n        inside: {\n          'double-quoted': {\n            pattern: /^\"[\\s\\S]*\"$/,\n            inside: {\n              // See interpolation below\n            }\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$(?:::)?\\w+(?:::\\w+)*/,\n        inside: {\n          punctuation: /::/\n        }\n      },\n      'attr-name': /(?:\\b\\w+|\\*)(?=\\s*=>)/,\n      function: [{\n        pattern: /(\\.)(?!\\d)\\w+/,\n        lookbehind: true\n      }, /\\b(?:contain|debug|err|fail|include|info|notice|realize|require|tag|warning)\\b|\\b(?!\\d)\\w+(?=\\()/],\n      number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d+)?(?:e-?\\d+)?)\\b/i,\n      boolean: /\\b(?:false|true)\\b/,\n      // Includes words reserved for future use\n      keyword: /\\b(?:application|attr|case|class|consumes|default|define|else|elsif|function|if|import|inherits|node|private|produces|type|undef|unless)\\b/,\n      datatype: {\n        pattern: /\\b(?:Any|Array|Boolean|Callable|Catalogentry|Class|Collection|Data|Default|Enum|Float|Hash|Integer|NotUndef|Numeric|Optional|Pattern|Regexp|Resource|Runtime|Scalar|String|Struct|Tuple|Type|Undef|Variant)\\b/,\n        alias: 'symbol'\n      },\n      operator: /=[=~>]?|![=~]?|<(?:<\\|?|[=~|-])?|>[>=]?|->?|~>|\\|>?>?|[*\\/%+?]|\\b(?:and|in|or)\\b/,\n      punctuation: /[\\[\\]{}().,;]|:+/\n    };\n    var interpolation = [{\n      // Allow for one nested level of braces inside interpolation\n      pattern: /(^|[^\\\\])\\$\\{(?:[^'\"{}]|\\{[^}]*\\}|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}/,\n      lookbehind: true,\n      inside: {\n        'short-variable': {\n          // Negative look-ahead prevent wrong highlighting of functions\n          pattern: /(^\\$\\{)(?!\\w+\\()(?:::)?\\w+(?:::\\w+)*/,\n          lookbehind: true,\n          alias: 'variable',\n          inside: {\n            punctuation: /::/\n          }\n        },\n        delimiter: {\n          pattern: /^\\$/,\n          alias: 'variable'\n        },\n        rest: Prism.languages.puppet\n      }\n    }, {\n      pattern: /(^|[^\\\\])\\$(?:::)?\\w+(?:::\\w+)*/,\n      lookbehind: true,\n      alias: 'variable',\n      inside: {\n        punctuation: /::/\n      }\n    }];\n    Prism.languages.puppet['heredoc'][0].inside.interpolation = interpolation;\n    Prism.languages.puppet['string'].inside['double-quoted'].inside.interpolation = interpolation;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "puppet", "displayName", "aliases", "Prism", "languages", "heredoc", "pattern", "lookbehind", "alias", "inside", "punctuation", "greedy", "regex", "comment", "string", "variable", "function", "number", "boolean", "keyword", "datatype", "operator", "interpolation", "delimiter", "rest"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/puppet.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = puppet\npuppet.displayName = 'puppet'\npuppet.aliases = []\nfunction puppet(Prism) {\n  ;(function (Prism) {\n    Prism.languages.puppet = {\n      heredoc: [\n        // Matches the content of a quoted heredoc string (subject to interpolation)\n        {\n          pattern:\n            /(@\\(\"([^\"\\r\\n\\/):]+)\"(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n          lookbehind: true,\n          alias: 'string',\n          inside: {\n            // Matches the end tag\n            punctuation: /(?=\\S).*\\S(?= *$)/ // See interpolation below\n          }\n        }, // Matches the content of an unquoted heredoc string (no interpolation)\n        {\n          pattern:\n            /(@\\(([^\"\\r\\n\\/):]+)(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'string',\n          inside: {\n            // Matches the end tag\n            punctuation: /(?=\\S).*\\S(?= *$)/\n          }\n        }, // Matches the start tag of heredoc strings\n        {\n          pattern: /@\\(\"?(?:[^\"\\r\\n\\/):]+)\"?(?:\\/[nrts$uL]*)?\\)/,\n          alias: 'string',\n          inside: {\n            punctuation: {\n              pattern: /(\\().+?(?=\\))/,\n              lookbehind: true\n            }\n          }\n        }\n      ],\n      'multiline-comment': {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true,\n        greedy: true,\n        alias: 'comment'\n      },\n      regex: {\n        // Must be prefixed with the keyword \"node\" or a non-word char\n        pattern:\n          /((?:\\bnode\\s+|[~=\\(\\[\\{,]\\s*|[=+]>\\s*|^\\s*))\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/(?:[imx]+\\b|\\B)/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          // Extended regexes must have the x flag. They can contain single-line comments.\n          'extended-regex': {\n            pattern: /^\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/[im]*x[im]*$/,\n            inside: {\n              comment: /#.*/\n            }\n          }\n        }\n      },\n      comment: {\n        pattern: /(^|[^\\\\])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      string: {\n        // Allow for one nested level of double quotes inside interpolation\n        pattern:\n          /([\"'])(?:\\$\\{(?:[^'\"}]|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}|\\$(?!\\{)|(?!\\1)[^\\\\$]|\\\\[\\s\\S])*\\1/,\n        greedy: true,\n        inside: {\n          'double-quoted': {\n            pattern: /^\"[\\s\\S]*\"$/,\n            inside: {\n              // See interpolation below\n            }\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$(?:::)?\\w+(?:::\\w+)*/,\n        inside: {\n          punctuation: /::/\n        }\n      },\n      'attr-name': /(?:\\b\\w+|\\*)(?=\\s*=>)/,\n      function: [\n        {\n          pattern: /(\\.)(?!\\d)\\w+/,\n          lookbehind: true\n        },\n        /\\b(?:contain|debug|err|fail|include|info|notice|realize|require|tag|warning)\\b|\\b(?!\\d)\\w+(?=\\()/\n      ],\n      number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d+)?(?:e-?\\d+)?)\\b/i,\n      boolean: /\\b(?:false|true)\\b/,\n      // Includes words reserved for future use\n      keyword:\n        /\\b(?:application|attr|case|class|consumes|default|define|else|elsif|function|if|import|inherits|node|private|produces|type|undef|unless)\\b/,\n      datatype: {\n        pattern:\n          /\\b(?:Any|Array|Boolean|Callable|Catalogentry|Class|Collection|Data|Default|Enum|Float|Hash|Integer|NotUndef|Numeric|Optional|Pattern|Regexp|Resource|Runtime|Scalar|String|Struct|Tuple|Type|Undef|Variant)\\b/,\n        alias: 'symbol'\n      },\n      operator:\n        /=[=~>]?|![=~]?|<(?:<\\|?|[=~|-])?|>[>=]?|->?|~>|\\|>?>?|[*\\/%+?]|\\b(?:and|in|or)\\b/,\n      punctuation: /[\\[\\]{}().,;]|:+/\n    }\n    var interpolation = [\n      {\n        // Allow for one nested level of braces inside interpolation\n        pattern:\n          /(^|[^\\\\])\\$\\{(?:[^'\"{}]|\\{[^}]*\\}|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}/,\n        lookbehind: true,\n        inside: {\n          'short-variable': {\n            // Negative look-ahead prevent wrong highlighting of functions\n            pattern: /(^\\$\\{)(?!\\w+\\()(?:::)?\\w+(?:::\\w+)*/,\n            lookbehind: true,\n            alias: 'variable',\n            inside: {\n              punctuation: /::/\n            }\n          },\n          delimiter: {\n            pattern: /^\\$/,\n            alias: 'variable'\n          },\n          rest: Prism.languages.puppet\n        }\n      },\n      {\n        pattern: /(^|[^\\\\])\\$(?:::)?\\w+(?:::\\w+)*/,\n        lookbehind: true,\n        alias: 'variable',\n        inside: {\n          punctuation: /::/\n        }\n      }\n    ]\n    Prism.languages.puppet['heredoc'][0].inside.interpolation = interpolation\n    Prism.languages.puppet['string'].inside[\n      'double-quoted'\n    ].inside.interpolation = interpolation\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;MACvBK,OAAO,EAAE;MACP;MACA;QACEC,OAAO,EACL,oHAAoH;QACtHC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE;UACN;UACAC,WAAW,EAAE,mBAAmB,CAAC;QACnC;MACF,CAAC;MAAE;MACH;QACEJ,OAAO,EACL,kHAAkH;QACpHC,UAAU,EAAE,IAAI;QAChBI,MAAM,EAAE,IAAI;QACZH,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE;UACN;UACAC,WAAW,EAAE;QACf;MACF,CAAC;MAAE;MACH;QACEJ,OAAO,EAAE,6CAA6C;QACtDE,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE;UACNC,WAAW,EAAE;YACXJ,OAAO,EAAE,eAAe;YACxBC,UAAU,EAAE;UACd;QACF;MACF,CAAC,CACF;MACD,mBAAmB,EAAE;QACnBD,OAAO,EAAE,2BAA2B;QACpCC,UAAU,EAAE,IAAI;QAChBI,MAAM,EAAE,IAAI;QACZH,KAAK,EAAE;MACT,CAAC;MACDI,KAAK,EAAE;QACL;QACAN,OAAO,EACL,sFAAsF;QACxFC,UAAU,EAAE,IAAI;QAChBI,MAAM,EAAE,IAAI;QACZF,MAAM,EAAE;UACN;UACA,gBAAgB,EAAE;YAChBH,OAAO,EAAE,wCAAwC;YACjDG,MAAM,EAAE;cACNI,OAAO,EAAE;YACX;UACF;QACF;MACF,CAAC;MACDA,OAAO,EAAE;QACPP,OAAO,EAAE,cAAc;QACvBC,UAAU,EAAE,IAAI;QAChBI,MAAM,EAAE;MACV,CAAC;MACDG,MAAM,EAAE;QACN;QACAR,OAAO,EACL,iGAAiG;QACnGK,MAAM,EAAE,IAAI;QACZF,MAAM,EAAE;UACN,eAAe,EAAE;YACfH,OAAO,EAAE,aAAa;YACtBG,MAAM,EAAE;cACN;YAAA;UAEJ;QACF;MACF,CAAC;MACDM,QAAQ,EAAE;QACRT,OAAO,EAAE,wBAAwB;QACjCG,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACD,WAAW,EAAE,uBAAuB;MACpCM,QAAQ,EAAE,CACR;QACEV,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE;MACd,CAAC,EACD,kGAAkG,CACnG;MACDU,MAAM,EAAE,8CAA8C;MACtDC,OAAO,EAAE,oBAAoB;MAC7B;MACAC,OAAO,EACL,4IAA4I;MAC9IC,QAAQ,EAAE;QACRd,OAAO,EACL,+MAA+M;QACjNE,KAAK,EAAE;MACT,CAAC;MACDa,QAAQ,EACN,kFAAkF;MACpFX,WAAW,EAAE;IACf,CAAC;IACD,IAAIY,aAAa,GAAG,CAClB;MACE;MACAhB,OAAO,EACL,yEAAyE;MAC3EC,UAAU,EAAE,IAAI;MAChBE,MAAM,EAAE;QACN,gBAAgB,EAAE;UAChB;UACAH,OAAO,EAAE,sCAAsC;UAC/CC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,UAAU;UACjBC,MAAM,EAAE;YACNC,WAAW,EAAE;UACf;QACF,CAAC;QACDa,SAAS,EAAE;UACTjB,OAAO,EAAE,KAAK;UACdE,KAAK,EAAE;QACT,CAAC;QACDgB,IAAI,EAAErB,KAAK,CAACC,SAAS,CAACJ;MACxB;IACF,CAAC,EACD;MACEM,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC,CACF;IACDP,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAACS,MAAM,CAACa,aAAa,GAAGA,aAAa;IACzEnB,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,QAAQ,CAAC,CAACS,MAAM,CACrC,eAAe,CAChB,CAACA,MAAM,CAACa,aAAa,GAAGA,aAAa;EACxC,CAAC,EAAEnB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}