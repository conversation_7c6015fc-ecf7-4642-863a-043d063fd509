{"ast": null, "code": "import highlight from './highlight';\nimport defaultStyle from './styles/hljs/default-style';\nimport lowlight from 'lowlight';\nimport supportedLanguages from './languages/hljs/supported-languages';\nvar highlighter = highlight(lowlight, defaultStyle);\nhighlighter.supportedLanguages = supportedLanguages;\nexport default highlighter;", "map": {"version": 3, "names": ["highlight", "defaultStyle", "lowlight", "supportedLanguages", "highlighter"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/default-highlight.js"], "sourcesContent": ["import highlight from './highlight';\nimport defaultStyle from './styles/hljs/default-style';\nimport lowlight from 'lowlight';\nimport supportedLanguages from './languages/hljs/supported-languages';\nvar highlighter = highlight(lowlight, defaultStyle);\nhighlighter.supportedLanguages = supportedLanguages;\nexport default highlighter;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,kBAAkB,MAAM,sCAAsC;AACrE,IAAIC,WAAW,GAAGJ,SAAS,CAACE,QAAQ,EAAED,YAAY,CAAC;AACnDG,WAAW,CAACD,kBAAkB,GAAGA,kBAAkB;AACnD,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}