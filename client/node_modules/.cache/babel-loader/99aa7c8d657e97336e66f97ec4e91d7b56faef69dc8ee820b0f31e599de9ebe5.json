{"ast": null, "code": "/*\nLanguage: Lua\nDescription: Lua is a powerful, efficient, lightweight, embeddable scripting language.\nAuthor: <PERSON> <<EMAIL>>\nCategory: common, scripting\nWebsite: https://www.lua.org\n*/\n\nfunction lua(hljs) {\n  const OPENING_LONG_BRACKET = '\\\\[=*\\\\[';\n  const CLOSING_LONG_BRACKET = '\\\\]=*\\\\]';\n  const LONG_BRACKETS = {\n    begin: OPENING_LONG_BRACKET,\n    end: CLOSING_LONG_BRACKET,\n    contains: ['self']\n  };\n  const COMMENTS = [hljs.COMMENT('--(?!' + OPENING_LONG_BRACKET + ')', '$'), hljs.COMMENT('--' + OPENING_LONG_BRACKET, CLOSING_LONG_BRACKET, {\n    contains: [LONG_BRACKETS],\n    relevance: 10\n  })];\n  return {\n    name: '<PERSON><PERSON>',\n    keywords: {\n      $pattern: hljs.UNDERSCORE_IDENT_RE,\n      literal: \"true false nil\",\n      keyword: \"and break do else elseif end for goto if in local not or repeat return then until while\",\n      built_in:\n      // Metatags and globals:\n      '_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len ' + '__gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert ' +\n      // Standard methods and properties:\n      'collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring ' + 'module next pairs pcall print rawequal rawget rawset require select setfenv ' + 'setmetatable tonumber tostring type unpack xpcall arg self ' +\n      // Library methods and properties (one line per library):\n      'coroutine resume yield status wrap create running debug getupvalue ' + 'debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv ' + 'io lines write close flush open output type read stderr stdin input stdout popen tmpfile ' + 'math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan ' + 'os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall ' + 'string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower ' + 'table setn insert getn foreachi maxn foreach concat sort remove'\n    },\n    contains: COMMENTS.concat([{\n      className: 'function',\n      beginKeywords: 'function',\n      end: '\\\\)',\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*'\n      }), {\n        className: 'params',\n        begin: '\\\\(',\n        endsWithParent: true,\n        contains: COMMENTS\n      }].concat(COMMENTS)\n    }, hljs.C_NUMBER_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n      className: 'string',\n      begin: OPENING_LONG_BRACKET,\n      end: CLOSING_LONG_BRACKET,\n      contains: [LONG_BRACKETS],\n      relevance: 5\n    }])\n  };\n}\nmodule.exports = lua;", "map": {"version": 3, "names": ["lua", "hljs", "OPENING_LONG_BRACKET", "CLOSING_LONG_BRACKET", "LONG_BRACKETS", "begin", "end", "contains", "COMMENTS", "COMMENT", "relevance", "name", "keywords", "$pattern", "UNDERSCORE_IDENT_RE", "literal", "keyword", "built_in", "concat", "className", "beginKeywords", "inherit", "TITLE_MODE", "endsWithParent", "C_NUMBER_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/lua.js"], "sourcesContent": ["/*\nLanguage: Lua\nDescription: Lua is a powerful, efficient, lightweight, embeddable scripting language.\nAuthor: <PERSON> <<EMAIL>>\nCategory: common, scripting\nWebsite: https://www.lua.org\n*/\n\nfunction lua(hljs) {\n  const OPENING_LONG_BRACKET = '\\\\[=*\\\\[';\n  const CLOSING_LONG_BRACKET = '\\\\]=*\\\\]';\n  const LONG_BRACKETS = {\n    begin: OPENING_LONG_BRACKET,\n    end: CLOSING_LONG_BRACKET,\n    contains: ['self']\n  };\n  const COMMENTS = [\n    hljs.COMMENT('--(?!' + OPENING_LONG_BRACKET + ')', '$'),\n    hljs.COMMENT(\n      '--' + OPENING_LONG_BRACKET,\n      CLOSING_LONG_BRACKET,\n      {\n        contains: [LONG_BRACKETS],\n        relevance: 10\n      }\n    )\n  ];\n  return {\n    name: '<PERSON><PERSON>',\n    keywords: {\n      $pattern: hljs.UNDERSCORE_IDENT_RE,\n      literal: \"true false nil\",\n      keyword: \"and break do else elseif end for goto if in local not or repeat return then until while\",\n      built_in:\n        // Metatags and globals:\n        '_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len ' +\n        '__gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert ' +\n        // Standard methods and properties:\n        'collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring ' +\n        'module next pairs pcall print rawequal rawget rawset require select setfenv ' +\n        'setmetatable tonumber tostring type unpack xpcall arg self ' +\n        // Library methods and properties (one line per library):\n        'coroutine resume yield status wrap create running debug getupvalue ' +\n        'debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv ' +\n        'io lines write close flush open output type read stderr stdin input stdout popen tmpfile ' +\n        'math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan ' +\n        'os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall ' +\n        'string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower ' +\n        'table setn insert getn foreachi maxn foreach concat sort remove'\n    },\n    contains: COMMENTS.concat([\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '\\\\)',\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*'\n          }),\n          {\n            className: 'params',\n            begin: '\\\\(',\n            endsWithParent: true,\n            contains: COMMENTS\n          }\n        ].concat(COMMENTS)\n      },\n      hljs.C_NUMBER_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: OPENING_LONG_BRACKET,\n        end: CLOSING_LONG_BRACKET,\n        contains: [LONG_BRACKETS],\n        relevance: 5\n      }\n    ])\n  };\n}\n\nmodule.exports = lua;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,oBAAoB,GAAG,UAAU;EACvC,MAAMC,oBAAoB,GAAG,UAAU;EACvC,MAAMC,aAAa,GAAG;IACpBC,KAAK,EAAEH,oBAAoB;IAC3BI,GAAG,EAAEH,oBAAoB;IACzBI,QAAQ,EAAE,CAAC,MAAM;EACnB,CAAC;EACD,MAAMC,QAAQ,GAAG,CACfP,IAAI,CAACQ,OAAO,CAAC,OAAO,GAAGP,oBAAoB,GAAG,GAAG,EAAE,GAAG,CAAC,EACvDD,IAAI,CAACQ,OAAO,CACV,IAAI,GAAGP,oBAAoB,EAC3BC,oBAAoB,EACpB;IACEI,QAAQ,EAAE,CAACH,aAAa,CAAC;IACzBM,SAAS,EAAE;EACb,CACF,CAAC,CACF;EACD,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MACRC,QAAQ,EAAEZ,IAAI,CAACa,mBAAmB;MAClCC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,yFAAyF;MAClGC,QAAQ;MACN;MACA,iFAAiF,GACjF,gFAAgF;MAChF;MACA,mFAAmF,GACnF,8EAA8E,GAC9E,6DAA6D;MAC7D;MACA,qEAAqE,GACrE,6HAA6H,GAC7H,2FAA2F,GAC3F,iKAAiK,GACjK,kJAAkJ,GAClJ,4FAA4F,GAC5F;IACJ,CAAC;IACDV,QAAQ,EAAEC,QAAQ,CAACU,MAAM,CAAC,CACxB;MACEC,SAAS,EAAE,UAAU;MACrBC,aAAa,EAAE,UAAU;MACzBd,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,CACRN,IAAI,CAACoB,OAAO,CAACpB,IAAI,CAACqB,UAAU,EAAE;QAC5BjB,KAAK,EAAE;MACT,CAAC,CAAC,EACF;QACEc,SAAS,EAAE,QAAQ;QACnBd,KAAK,EAAE,KAAK;QACZkB,cAAc,EAAE,IAAI;QACpBhB,QAAQ,EAAEC;MACZ,CAAC,CACF,CAACU,MAAM,CAACV,QAAQ;IACnB,CAAC,EACDP,IAAI,CAACuB,aAAa,EAClBvB,IAAI,CAACwB,gBAAgB,EACrBxB,IAAI,CAACyB,iBAAiB,EACtB;MACEP,SAAS,EAAE,QAAQ;MACnBd,KAAK,EAAEH,oBAAoB;MAC3BI,GAAG,EAAEH,oBAAoB;MACzBI,QAAQ,EAAE,CAACH,aAAa,CAAC;MACzBM,SAAS,EAAE;IACb,CAAC,CACF;EACH,CAAC;AACH;AAEAiB,MAAM,CAACC,OAAO,GAAG5B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}