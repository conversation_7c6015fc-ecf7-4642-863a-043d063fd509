{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: LaTeX\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.latex-project.org\nCategory: markup\n*/\n\n/** @type LanguageFn */\nfunction latex(hljs) {\n  const KNOWN_CONTROL_WORDS = either(...['(?:NeedsTeXFormat|RequirePackage|GetIdInfo)', 'Provides(?:Expl)?(?:Package|Class|File)', '(?:DeclareOption|ProcessOptions)', '(?:documentclass|usepackage|input|include)', 'makeat(?:letter|other)', 'ExplSyntax(?:On|Off)', '(?:new|renew|provide)?command', '(?:re)newenvironment', '(?:New|Renew|Provide|Declare)(?:Expandable)?DocumentCommand', '(?:New|Renew|Provide|Declare)DocumentEnvironment', '(?:(?:e|g|x)?def|let)', '(?:begin|end)', '(?:part|chapter|(?:sub){0,2}section|(?:sub)?paragraph)', 'caption', '(?:label|(?:eq|page|name)?ref|(?:paren|foot|super)?cite)', '(?:alpha|beta|[Gg]amma|[Dd]elta|(?:var)?epsilon|zeta|eta|[Tt]heta|vartheta)', '(?:iota|(?:var)?kappa|[Ll]ambda|mu|nu|[Xx]i|[Pp]i|varpi|(?:var)rho)', '(?:[Ss]igma|varsigma|tau|[Uu]psilon|[Pp]hi|varphi|chi|[Pp]si|[Oo]mega)', '(?:frac|sum|prod|lim|infty|times|sqrt|leq|geq|left|right|middle|[bB]igg?)', '(?:[lr]angle|q?quad|[lcvdi]?dots|d?dot|hat|tilde|bar)'].map(word => word + '(?![a-zA-Z@:_])'));\n  const L3_REGEX = new RegExp([\n  // A function \\module_function_name:signature or \\__module_function_name:signature,\n  // where both module and function_name need at least two characters and\n  // function_name may contain single underscores.\n  '(?:__)?[a-zA-Z]{2,}_[a-zA-Z](?:_?[a-zA-Z])+:[a-zA-Z]*',\n  // A variable \\scope_module_and_name_type or \\scope__module_ane_name_type,\n  // where scope is one of l, g or c, type needs at least two characters\n  // and module_and_name may contain single underscores.\n  '[lgc]__?[a-zA-Z](?:_?[a-zA-Z])*_[a-zA-Z]{2,}',\n  // A quark \\q_the_name or \\q__the_name or\n  // scan mark \\s_the_name or \\s__vthe_name,\n  // where variable_name needs at least two characters and\n  // may contain single underscores.\n  '[qs]__?[a-zA-Z](?:_?[a-zA-Z])+',\n  // Other LaTeX3 macro names that are not covered by the three rules above.\n  'use(?:_i)?:[a-zA-Z]*', '(?:else|fi|or):', '(?:if|cs|exp):w', '(?:hbox|vbox):n', '::[a-zA-Z]_unbraced', '::[a-zA-Z:]'].map(pattern => pattern + '(?![a-zA-Z:_])').join('|'));\n  const L2_VARIANTS = [{\n    begin: /[a-zA-Z@]+/\n  },\n  // control word\n  {\n    begin: /[^a-zA-Z@]?/\n  } // control symbol\n  ];\n  const DOUBLE_CARET_VARIANTS = [{\n    begin: /\\^{6}[0-9a-f]{6}/\n  }, {\n    begin: /\\^{5}[0-9a-f]{5}/\n  }, {\n    begin: /\\^{4}[0-9a-f]{4}/\n  }, {\n    begin: /\\^{3}[0-9a-f]{3}/\n  }, {\n    begin: /\\^{2}[0-9a-f]{2}/\n  }, {\n    begin: /\\^{2}[\\u0000-\\u007f]/\n  }];\n  const CONTROL_SEQUENCE = {\n    className: 'keyword',\n    begin: /\\\\/,\n    relevance: 0,\n    contains: [{\n      endsParent: true,\n      begin: KNOWN_CONTROL_WORDS\n    }, {\n      endsParent: true,\n      begin: L3_REGEX\n    }, {\n      endsParent: true,\n      variants: DOUBLE_CARET_VARIANTS\n    }, {\n      endsParent: true,\n      relevance: 0,\n      variants: L2_VARIANTS\n    }]\n  };\n  const MACRO_PARAM = {\n    className: 'params',\n    relevance: 0,\n    begin: /#+\\d?/\n  };\n  const DOUBLE_CARET_CHAR = {\n    // relevance: 1\n    variants: DOUBLE_CARET_VARIANTS\n  };\n  const SPECIAL_CATCODE = {\n    className: 'built_in',\n    relevance: 0,\n    begin: /[$&^_]/\n  };\n  const MAGIC_COMMENT = {\n    className: 'meta',\n    begin: '% !TeX',\n    end: '$',\n    relevance: 10\n  };\n  const COMMENT = hljs.COMMENT('%', '$', {\n    relevance: 0\n  });\n  const EVERYTHING_BUT_VERBATIM = [CONTROL_SEQUENCE, MACRO_PARAM, DOUBLE_CARET_CHAR, SPECIAL_CATCODE, MAGIC_COMMENT, COMMENT];\n  const BRACE_GROUP_NO_VERBATIM = {\n    begin: /\\{/,\n    end: /\\}/,\n    relevance: 0,\n    contains: ['self', ...EVERYTHING_BUT_VERBATIM]\n  };\n  const ARGUMENT_BRACES = hljs.inherit(BRACE_GROUP_NO_VERBATIM, {\n    relevance: 0,\n    endsParent: true,\n    contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n  });\n  const ARGUMENT_BRACKETS = {\n    begin: /\\[/,\n    end: /\\]/,\n    endsParent: true,\n    relevance: 0,\n    contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n  };\n  const SPACE_GOBBLER = {\n    begin: /\\s+/,\n    relevance: 0\n  };\n  const ARGUMENT_M = [ARGUMENT_BRACES];\n  const ARGUMENT_O = [ARGUMENT_BRACKETS];\n  const ARGUMENT_AND_THEN = function (arg, starts_mode) {\n    return {\n      contains: [SPACE_GOBBLER],\n      starts: {\n        relevance: 0,\n        contains: arg,\n        starts: starts_mode\n      }\n    };\n  };\n  const CSNAME = function (csname, starts_mode) {\n    return {\n      begin: '\\\\\\\\' + csname + '(?![a-zA-Z@:_])',\n      keywords: {\n        $pattern: /\\\\[a-zA-Z]+/,\n        keyword: '\\\\' + csname\n      },\n      relevance: 0,\n      contains: [SPACE_GOBBLER],\n      starts: starts_mode\n    };\n  };\n  const BEGIN_ENV = function (envname, starts_mode) {\n    return hljs.inherit({\n      begin: '\\\\\\\\begin(?=[ \\t]*(\\\\r?\\\\n[ \\t]*)?\\\\{' + envname + '\\\\})',\n      keywords: {\n        $pattern: /\\\\[a-zA-Z]+/,\n        keyword: '\\\\begin'\n      },\n      relevance: 0\n    }, ARGUMENT_AND_THEN(ARGUMENT_M, starts_mode));\n  };\n  const VERBATIM_DELIMITED_EQUAL = (innerName = \"string\") => {\n    return hljs.END_SAME_AS_BEGIN({\n      className: innerName,\n      begin: /(.|\\r?\\n)/,\n      end: /(.|\\r?\\n)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      endsParent: true\n    });\n  };\n  const VERBATIM_DELIMITED_ENV = function (envname) {\n    return {\n      className: 'string',\n      end: '(?=\\\\\\\\end\\\\{' + envname + '\\\\})'\n    };\n  };\n  const VERBATIM_DELIMITED_BRACES = (innerName = \"string\") => {\n    return {\n      relevance: 0,\n      begin: /\\{/,\n      starts: {\n        endsParent: true,\n        contains: [{\n          className: innerName,\n          end: /(?=\\})/,\n          endsParent: true,\n          contains: [{\n            begin: /\\{/,\n            end: /\\}/,\n            relevance: 0,\n            contains: [\"self\"]\n          }]\n        }]\n      }\n    };\n  };\n  const VERBATIM = [...['verb', 'lstinline'].map(csname => CSNAME(csname, {\n    contains: [VERBATIM_DELIMITED_EQUAL()]\n  })), CSNAME('mint', ARGUMENT_AND_THEN(ARGUMENT_M, {\n    contains: [VERBATIM_DELIMITED_EQUAL()]\n  })), CSNAME('mintinline', ARGUMENT_AND_THEN(ARGUMENT_M, {\n    contains: [VERBATIM_DELIMITED_BRACES(), VERBATIM_DELIMITED_EQUAL()]\n  })), CSNAME('url', {\n    contains: [VERBATIM_DELIMITED_BRACES(\"link\"), VERBATIM_DELIMITED_BRACES(\"link\")]\n  }), CSNAME('hyperref', {\n    contains: [VERBATIM_DELIMITED_BRACES(\"link\")]\n  }), CSNAME('href', ARGUMENT_AND_THEN(ARGUMENT_O, {\n    contains: [VERBATIM_DELIMITED_BRACES(\"link\")]\n  })), ...[].concat(...['', '\\\\*'].map(suffix => [BEGIN_ENV('verbatim' + suffix, VERBATIM_DELIMITED_ENV('verbatim' + suffix)), BEGIN_ENV('filecontents' + suffix, ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('filecontents' + suffix))), ...['', 'B', 'L'].map(prefix => BEGIN_ENV(prefix + 'Verbatim' + suffix, ARGUMENT_AND_THEN(ARGUMENT_O, VERBATIM_DELIMITED_ENV(prefix + 'Verbatim' + suffix))))])), BEGIN_ENV('minted', ARGUMENT_AND_THEN(ARGUMENT_O, ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('minted'))))];\n  return {\n    name: 'LaTeX',\n    aliases: ['tex'],\n    contains: [...VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n  };\n}\nmodule.exports = latex;", "map": {"version": 3, "names": ["source", "re", "either", "args", "joined", "map", "x", "join", "latex", "hljs", "KNOWN_CONTROL_WORDS", "word", "L3_REGEX", "RegExp", "pattern", "L2_VARIANTS", "begin", "DOUBLE_CARET_VARIANTS", "CONTROL_SEQUENCE", "className", "relevance", "contains", "endsParent", "variants", "MACRO_PARAM", "DOUBLE_CARET_CHAR", "SPECIAL_CATCODE", "MAGIC_COMMENT", "end", "COMMENT", "EVERYTHING_BUT_VERBATIM", "BRACE_GROUP_NO_VERBATIM", "ARGUMENT_BRACES", "inherit", "ARGUMENT_BRACKETS", "SPACE_GOBBLER", "ARGUMENT_M", "ARGUMENT_O", "ARGUMENT_AND_THEN", "arg", "starts_mode", "starts", "CSNAME", "csname", "keywords", "$pattern", "keyword", "BEGIN_ENV", "envname", "VERBATIM_DELIMITED_EQUAL", "innerName", "END_SAME_AS_BEGIN", "excludeBegin", "excludeEnd", "VERBATIM_DELIMITED_ENV", "VERBATIM_DELIMITED_BRACES", "VERBATIM", "concat", "suffix", "prefix", "name", "aliases", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/latex.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: LaTeX\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.latex-project.org\nCategory: markup\n*/\n\n/** @type LanguageFn */\nfunction latex(hljs) {\n  const KNOWN_CONTROL_WORDS = either(...[\n      '(?:NeedsTeXFormat|RequirePackage|GetIdInfo)',\n      'Provides(?:Expl)?(?:Package|Class|File)',\n      '(?:DeclareOption|ProcessOptions)',\n      '(?:documentclass|usepackage|input|include)',\n      'makeat(?:letter|other)',\n      'ExplSyntax(?:On|Off)',\n      '(?:new|renew|provide)?command',\n      '(?:re)newenvironment',\n      '(?:New|Renew|Provide|Declare)(?:Expandable)?DocumentCommand',\n      '(?:New|Renew|Provide|Declare)DocumentEnvironment',\n      '(?:(?:e|g|x)?def|let)',\n      '(?:begin|end)',\n      '(?:part|chapter|(?:sub){0,2}section|(?:sub)?paragraph)',\n      'caption',\n      '(?:label|(?:eq|page|name)?ref|(?:paren|foot|super)?cite)',\n      '(?:alpha|beta|[Gg]amma|[Dd]elta|(?:var)?epsilon|zeta|eta|[Tt]heta|vartheta)',\n      '(?:iota|(?:var)?kappa|[Ll]ambda|mu|nu|[Xx]i|[Pp]i|varpi|(?:var)rho)',\n      '(?:[Ss]igma|varsigma|tau|[Uu]psilon|[Pp]hi|varphi|chi|[Pp]si|[Oo]mega)',\n      '(?:frac|sum|prod|lim|infty|times|sqrt|leq|geq|left|right|middle|[bB]igg?)',\n      '(?:[lr]angle|q?quad|[lcvdi]?dots|d?dot|hat|tilde|bar)'\n    ].map(word => word + '(?![a-zA-Z@:_])'));\n  const L3_REGEX = new RegExp([\n      // A function \\module_function_name:signature or \\__module_function_name:signature,\n      // where both module and function_name need at least two characters and\n      // function_name may contain single underscores.\n      '(?:__)?[a-zA-Z]{2,}_[a-zA-Z](?:_?[a-zA-Z])+:[a-zA-Z]*',\n      // A variable \\scope_module_and_name_type or \\scope__module_ane_name_type,\n      // where scope is one of l, g or c, type needs at least two characters\n      // and module_and_name may contain single underscores.\n      '[lgc]__?[a-zA-Z](?:_?[a-zA-Z])*_[a-zA-Z]{2,}',\n      // A quark \\q_the_name or \\q__the_name or\n      // scan mark \\s_the_name or \\s__vthe_name,\n      // where variable_name needs at least two characters and\n      // may contain single underscores.\n      '[qs]__?[a-zA-Z](?:_?[a-zA-Z])+',\n      // Other LaTeX3 macro names that are not covered by the three rules above.\n      'use(?:_i)?:[a-zA-Z]*',\n      '(?:else|fi|or):',\n      '(?:if|cs|exp):w',\n      '(?:hbox|vbox):n',\n      '::[a-zA-Z]_unbraced',\n      '::[a-zA-Z:]'\n    ].map(pattern => pattern + '(?![a-zA-Z:_])').join('|'));\n  const L2_VARIANTS = [\n    {begin: /[a-zA-Z@]+/}, // control word\n    {begin: /[^a-zA-Z@]?/} // control symbol\n  ];\n  const DOUBLE_CARET_VARIANTS = [\n    {begin: /\\^{6}[0-9a-f]{6}/},\n    {begin: /\\^{5}[0-9a-f]{5}/},\n    {begin: /\\^{4}[0-9a-f]{4}/},\n    {begin: /\\^{3}[0-9a-f]{3}/},\n    {begin: /\\^{2}[0-9a-f]{2}/},\n    {begin: /\\^{2}[\\u0000-\\u007f]/}\n  ];\n  const CONTROL_SEQUENCE = {\n    className: 'keyword',\n    begin: /\\\\/,\n    relevance: 0,\n    contains: [\n      {\n        endsParent: true,\n        begin: KNOWN_CONTROL_WORDS\n      },\n      {\n        endsParent: true,\n        begin: L3_REGEX\n      },\n      {\n        endsParent: true,\n        variants: DOUBLE_CARET_VARIANTS\n      },\n      {\n        endsParent: true,\n        relevance: 0,\n        variants: L2_VARIANTS\n      }\n    ]\n  };\n  const MACRO_PARAM = {\n    className: 'params',\n    relevance: 0,\n    begin: /#+\\d?/\n  };\n  const DOUBLE_CARET_CHAR = {\n    // relevance: 1\n    variants: DOUBLE_CARET_VARIANTS\n  };\n  const SPECIAL_CATCODE = {\n    className: 'built_in',\n    relevance: 0,\n    begin: /[$&^_]/\n  };\n  const MAGIC_COMMENT = {\n    className: 'meta',\n    begin: '% !TeX',\n    end: '$',\n    relevance: 10\n  };\n  const COMMENT = hljs.COMMENT(\n    '%',\n    '$',\n    {\n      relevance: 0\n    }\n  );\n  const EVERYTHING_BUT_VERBATIM = [\n    CONTROL_SEQUENCE,\n    MACRO_PARAM,\n    DOUBLE_CARET_CHAR,\n    SPECIAL_CATCODE,\n    MAGIC_COMMENT,\n    COMMENT\n  ];\n  const BRACE_GROUP_NO_VERBATIM = {\n    begin: /\\{/, end: /\\}/,\n    relevance: 0,\n    contains: ['self', ...EVERYTHING_BUT_VERBATIM]\n  };\n  const ARGUMENT_BRACES = hljs.inherit(\n    BRACE_GROUP_NO_VERBATIM,\n    {\n      relevance: 0,\n      endsParent: true,\n      contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n    }\n  );\n  const ARGUMENT_BRACKETS = {\n    begin: /\\[/,\n      end: /\\]/,\n    endsParent: true,\n    relevance: 0,\n    contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n  };\n  const SPACE_GOBBLER = {\n    begin: /\\s+/,\n    relevance: 0\n  };\n  const ARGUMENT_M = [ARGUMENT_BRACES];\n  const ARGUMENT_O = [ARGUMENT_BRACKETS];\n  const ARGUMENT_AND_THEN = function(arg, starts_mode) {\n    return {\n      contains: [SPACE_GOBBLER],\n      starts: {\n        relevance: 0,\n        contains: arg,\n        starts: starts_mode\n      }\n    };\n  };\n  const CSNAME = function(csname, starts_mode) {\n    return {\n        begin: '\\\\\\\\' + csname + '(?![a-zA-Z@:_])',\n        keywords: {$pattern: /\\\\[a-zA-Z]+/, keyword: '\\\\' + csname},\n        relevance: 0,\n        contains: [SPACE_GOBBLER],\n        starts: starts_mode\n      };\n  };\n  const BEGIN_ENV = function(envname, starts_mode) {\n    return hljs.inherit(\n      {\n        begin: '\\\\\\\\begin(?=[ \\t]*(\\\\r?\\\\n[ \\t]*)?\\\\{' + envname + '\\\\})',\n        keywords: {$pattern: /\\\\[a-zA-Z]+/, keyword: '\\\\begin'},\n        relevance: 0,\n      },\n      ARGUMENT_AND_THEN(ARGUMENT_M, starts_mode)\n    );\n  };\n  const VERBATIM_DELIMITED_EQUAL = (innerName = \"string\") => {\n    return hljs.END_SAME_AS_BEGIN({\n      className: innerName,\n      begin: /(.|\\r?\\n)/,\n      end: /(.|\\r?\\n)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      endsParent: true\n    });\n  };\n  const VERBATIM_DELIMITED_ENV = function(envname) {\n    return {\n      className: 'string',\n      end: '(?=\\\\\\\\end\\\\{' + envname + '\\\\})'\n    };\n  };\n\n  const VERBATIM_DELIMITED_BRACES = (innerName = \"string\") => {\n    return {\n      relevance: 0,\n      begin: /\\{/,\n      starts: {\n        endsParent: true,\n        contains: [\n          {\n            className: innerName,\n            end: /(?=\\})/,\n            endsParent:true,\n            contains: [\n              {\n                begin: /\\{/,\n                end: /\\}/,\n                relevance: 0,\n                contains: [\"self\"]\n              }\n            ],\n          }\n        ]\n      }\n    };\n  };\n  const VERBATIM = [\n    ...['verb', 'lstinline'].map(csname => CSNAME(csname, {contains: [VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('mint', ARGUMENT_AND_THEN(ARGUMENT_M, {contains: [VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('mintinline', ARGUMENT_AND_THEN(ARGUMENT_M, {contains: [VERBATIM_DELIMITED_BRACES(), VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('url', {contains: [VERBATIM_DELIMITED_BRACES(\"link\"), VERBATIM_DELIMITED_BRACES(\"link\")]}),\n    CSNAME('hyperref', {contains: [VERBATIM_DELIMITED_BRACES(\"link\")]}),\n    CSNAME('href', ARGUMENT_AND_THEN(ARGUMENT_O, {contains: [VERBATIM_DELIMITED_BRACES(\"link\")]})),\n    ...[].concat(...['', '\\\\*'].map(suffix => [\n      BEGIN_ENV('verbatim' + suffix, VERBATIM_DELIMITED_ENV('verbatim' + suffix)),\n      BEGIN_ENV('filecontents' + suffix,  ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('filecontents' + suffix))),\n      ...['', 'B', 'L'].map(prefix =>\n        BEGIN_ENV(prefix + 'Verbatim' + suffix, ARGUMENT_AND_THEN(ARGUMENT_O, VERBATIM_DELIMITED_ENV(prefix + 'Verbatim' + suffix)))\n      )\n    ])),\n    BEGIN_ENV('minted', ARGUMENT_AND_THEN(ARGUMENT_O, ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('minted')))),\n  ];\n\n  return {\n    name: 'LaTeX',\n    aliases: ['tex'],\n    contains: [\n      ...VERBATIM,\n      ...EVERYTHING_BUT_VERBATIM\n    ]\n  };\n}\n\nmodule.exports = latex;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,mBAAmB,GAAGR,MAAM,CAAC,GAAG,CAClC,6CAA6C,EAC7C,yCAAyC,EACzC,kCAAkC,EAClC,4CAA4C,EAC5C,wBAAwB,EACxB,sBAAsB,EACtB,+BAA+B,EAC/B,sBAAsB,EACtB,6DAA6D,EAC7D,kDAAkD,EAClD,uBAAuB,EACvB,eAAe,EACf,wDAAwD,EACxD,SAAS,EACT,0DAA0D,EAC1D,6EAA6E,EAC7E,qEAAqE,EACrE,wEAAwE,EACxE,2EAA2E,EAC3E,uDAAuD,CACxD,CAACG,GAAG,CAACM,IAAI,IAAIA,IAAI,GAAG,iBAAiB,CAAC,CAAC;EAC1C,MAAMC,QAAQ,GAAG,IAAIC,MAAM,CAAC;EACxB;EACA;EACA;EACA,uDAAuD;EACvD;EACA;EACA;EACA,8CAA8C;EAC9C;EACA;EACA;EACA;EACA,gCAAgC;EAChC;EACA,sBAAsB,EACtB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,qBAAqB,EACrB,aAAa,CACd,CAACR,GAAG,CAACS,OAAO,IAAIA,OAAO,GAAG,gBAAgB,CAAC,CAACP,IAAI,CAAC,GAAG,CAAC,CAAC;EACzD,MAAMQ,WAAW,GAAG,CAClB;IAACC,KAAK,EAAE;EAAY,CAAC;EAAE;EACvB;IAACA,KAAK,EAAE;EAAa,CAAC,CAAC;EAAA,CACxB;EACD,MAAMC,qBAAqB,GAAG,CAC5B;IAACD,KAAK,EAAE;EAAkB,CAAC,EAC3B;IAACA,KAAK,EAAE;EAAkB,CAAC,EAC3B;IAACA,KAAK,EAAE;EAAkB,CAAC,EAC3B;IAACA,KAAK,EAAE;EAAkB,CAAC,EAC3B;IAACA,KAAK,EAAE;EAAkB,CAAC,EAC3B;IAACA,KAAK,EAAE;EAAsB,CAAC,CAChC;EACD,MAAME,gBAAgB,GAAG;IACvBC,SAAS,EAAE,SAAS;IACpBH,KAAK,EAAE,IAAI;IACXI,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACR;MACEC,UAAU,EAAE,IAAI;MAChBN,KAAK,EAAEN;IACT,CAAC,EACD;MACEY,UAAU,EAAE,IAAI;MAChBN,KAAK,EAAEJ;IACT,CAAC,EACD;MACEU,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAEN;IACZ,CAAC,EACD;MACEK,UAAU,EAAE,IAAI;MAChBF,SAAS,EAAE,CAAC;MACZG,QAAQ,EAAER;IACZ,CAAC;EAEL,CAAC;EACD,MAAMS,WAAW,GAAG;IAClBL,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,CAAC;IACZJ,KAAK,EAAE;EACT,CAAC;EACD,MAAMS,iBAAiB,GAAG;IACxB;IACAF,QAAQ,EAAEN;EACZ,CAAC;EACD,MAAMS,eAAe,GAAG;IACtBP,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,CAAC;IACZJ,KAAK,EAAE;EACT,CAAC;EACD,MAAMW,aAAa,GAAG;IACpBR,SAAS,EAAE,MAAM;IACjBH,KAAK,EAAE,QAAQ;IACfY,GAAG,EAAE,GAAG;IACRR,SAAS,EAAE;EACb,CAAC;EACD,MAAMS,OAAO,GAAGpB,IAAI,CAACoB,OAAO,CAC1B,GAAG,EACH,GAAG,EACH;IACET,SAAS,EAAE;EACb,CACF,CAAC;EACD,MAAMU,uBAAuB,GAAG,CAC9BZ,gBAAgB,EAChBM,WAAW,EACXC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbE,OAAO,CACR;EACD,MAAME,uBAAuB,GAAG;IAC9Bf,KAAK,EAAE,IAAI;IAAEY,GAAG,EAAE,IAAI;IACtBR,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC,MAAM,EAAE,GAAGS,uBAAuB;EAC/C,CAAC;EACD,MAAME,eAAe,GAAGvB,IAAI,CAACwB,OAAO,CAClCF,uBAAuB,EACvB;IACEX,SAAS,EAAE,CAAC;IACZE,UAAU,EAAE,IAAI;IAChBD,QAAQ,EAAE,CAACU,uBAAuB,EAAE,GAAGD,uBAAuB;EAChE,CACF,CAAC;EACD,MAAMI,iBAAiB,GAAG;IACxBlB,KAAK,EAAE,IAAI;IACTY,GAAG,EAAE,IAAI;IACXN,UAAU,EAAE,IAAI;IAChBF,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAACU,uBAAuB,EAAE,GAAGD,uBAAuB;EAChE,CAAC;EACD,MAAMK,aAAa,GAAG;IACpBnB,KAAK,EAAE,KAAK;IACZI,SAAS,EAAE;EACb,CAAC;EACD,MAAMgB,UAAU,GAAG,CAACJ,eAAe,CAAC;EACpC,MAAMK,UAAU,GAAG,CAACH,iBAAiB,CAAC;EACtC,MAAMI,iBAAiB,GAAG,SAAAA,CAASC,GAAG,EAAEC,WAAW,EAAE;IACnD,OAAO;MACLnB,QAAQ,EAAE,CAACc,aAAa,CAAC;MACzBM,MAAM,EAAE;QACNrB,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAEkB,GAAG;QACbE,MAAM,EAAED;MACV;IACF,CAAC;EACH,CAAC;EACD,MAAME,MAAM,GAAG,SAAAA,CAASC,MAAM,EAAEH,WAAW,EAAE;IAC3C,OAAO;MACHxB,KAAK,EAAE,MAAM,GAAG2B,MAAM,GAAG,iBAAiB;MAC1CC,QAAQ,EAAE;QAACC,QAAQ,EAAE,aAAa;QAAEC,OAAO,EAAE,IAAI,GAAGH;MAAM,CAAC;MAC3DvB,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAACc,aAAa,CAAC;MACzBM,MAAM,EAAED;IACV,CAAC;EACL,CAAC;EACD,MAAMO,SAAS,GAAG,SAAAA,CAASC,OAAO,EAAER,WAAW,EAAE;IAC/C,OAAO/B,IAAI,CAACwB,OAAO,CACjB;MACEjB,KAAK,EAAE,uCAAuC,GAAGgC,OAAO,GAAG,MAAM;MACjEJ,QAAQ,EAAE;QAACC,QAAQ,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAS,CAAC;MACvD1B,SAAS,EAAE;IACb,CAAC,EACDkB,iBAAiB,CAACF,UAAU,EAAEI,WAAW,CAC3C,CAAC;EACH,CAAC;EACD,MAAMS,wBAAwB,GAAGA,CAACC,SAAS,GAAG,QAAQ,KAAK;IACzD,OAAOzC,IAAI,CAAC0C,iBAAiB,CAAC;MAC5BhC,SAAS,EAAE+B,SAAS;MACpBlC,KAAK,EAAE,WAAW;MAClBY,GAAG,EAAE,WAAW;MAChBwB,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChB/B,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EACD,MAAMgC,sBAAsB,GAAG,SAAAA,CAASN,OAAO,EAAE;IAC/C,OAAO;MACL7B,SAAS,EAAE,QAAQ;MACnBS,GAAG,EAAE,eAAe,GAAGoB,OAAO,GAAG;IACnC,CAAC;EACH,CAAC;EAED,MAAMO,yBAAyB,GAAGA,CAACL,SAAS,GAAG,QAAQ,KAAK;IAC1D,OAAO;MACL9B,SAAS,EAAE,CAAC;MACZJ,KAAK,EAAE,IAAI;MACXyB,MAAM,EAAE;QACNnB,UAAU,EAAE,IAAI;QAChBD,QAAQ,EAAE,CACR;UACEF,SAAS,EAAE+B,SAAS;UACpBtB,GAAG,EAAE,QAAQ;UACbN,UAAU,EAAC,IAAI;UACfD,QAAQ,EAAE,CACR;YACEL,KAAK,EAAE,IAAI;YACXY,GAAG,EAAE,IAAI;YACTR,SAAS,EAAE,CAAC;YACZC,QAAQ,EAAE,CAAC,MAAM;UACnB,CAAC;QAEL,CAAC;MAEL;IACF,CAAC;EACH,CAAC;EACD,MAAMmC,QAAQ,GAAG,CACf,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAACnD,GAAG,CAACsC,MAAM,IAAID,MAAM,CAACC,MAAM,EAAE;IAACtB,QAAQ,EAAE,CAAC4B,wBAAwB,CAAC,CAAC;EAAC,CAAC,CAAC,CAAC,EAChGP,MAAM,CAAC,MAAM,EAAEJ,iBAAiB,CAACF,UAAU,EAAE;IAACf,QAAQ,EAAE,CAAC4B,wBAAwB,CAAC,CAAC;EAAC,CAAC,CAAC,CAAC,EACvFP,MAAM,CAAC,YAAY,EAAEJ,iBAAiB,CAACF,UAAU,EAAE;IAACf,QAAQ,EAAE,CAACkC,yBAAyB,CAAC,CAAC,EAAEN,wBAAwB,CAAC,CAAC;EAAC,CAAC,CAAC,CAAC,EAC1HP,MAAM,CAAC,KAAK,EAAE;IAACrB,QAAQ,EAAE,CAACkC,yBAAyB,CAAC,MAAM,CAAC,EAAEA,yBAAyB,CAAC,MAAM,CAAC;EAAC,CAAC,CAAC,EACjGb,MAAM,CAAC,UAAU,EAAE;IAACrB,QAAQ,EAAE,CAACkC,yBAAyB,CAAC,MAAM,CAAC;EAAC,CAAC,CAAC,EACnEb,MAAM,CAAC,MAAM,EAAEJ,iBAAiB,CAACD,UAAU,EAAE;IAAChB,QAAQ,EAAE,CAACkC,yBAAyB,CAAC,MAAM,CAAC;EAAC,CAAC,CAAC,CAAC,EAC9F,GAAG,EAAE,CAACE,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAACpD,GAAG,CAACqD,MAAM,IAAI,CACxCX,SAAS,CAAC,UAAU,GAAGW,MAAM,EAAEJ,sBAAsB,CAAC,UAAU,GAAGI,MAAM,CAAC,CAAC,EAC3EX,SAAS,CAAC,cAAc,GAAGW,MAAM,EAAGpB,iBAAiB,CAACF,UAAU,EAAEkB,sBAAsB,CAAC,cAAc,GAAGI,MAAM,CAAC,CAAC,CAAC,EACnH,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAACrD,GAAG,CAACsD,MAAM,IAC1BZ,SAAS,CAACY,MAAM,GAAG,UAAU,GAAGD,MAAM,EAAEpB,iBAAiB,CAACD,UAAU,EAAEiB,sBAAsB,CAACK,MAAM,GAAG,UAAU,GAAGD,MAAM,CAAC,CAAC,CAC7H,CAAC,CACF,CAAC,CAAC,EACHX,SAAS,CAAC,QAAQ,EAAET,iBAAiB,CAACD,UAAU,EAAEC,iBAAiB,CAACF,UAAU,EAAEkB,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACpH;EAED,OAAO;IACLM,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBxC,QAAQ,EAAE,CACR,GAAGmC,QAAQ,EACX,GAAG1B,uBAAuB;EAE9B,CAAC;AACH;AAEAgC,MAAM,CAACC,OAAO,GAAGvD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}