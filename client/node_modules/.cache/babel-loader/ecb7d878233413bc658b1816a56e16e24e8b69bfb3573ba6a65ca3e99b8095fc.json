{"ast": null, "code": "'use strict';\n\nmodule.exports = cypher;\ncypher.displayName = 'cypher';\ncypher.aliases = [];\nfunction cypher(Prism) {\n  Prism.languages.cypher = {\n    // https://neo4j.com/docs/cypher-manual/current/syntax/comments/\n    comment: /\\/\\/.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)(?=\\s*[{):])/,\n      lookbehind: true,\n      greedy: true\n    },\n    relationship: {\n      pattern: /(-\\[\\s*(?:\\w+\\s*|`(?:[^`\\\\\\r\\n])*`\\s*)?:\\s*|\\|\\s*:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    identifier: {\n      pattern: /`(?:[^`\\\\\\r\\n])*`/,\n      greedy: true\n    },\n    variable: /\\$\\w+/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/reserved/\n    keyword: /\\b(?:ADD|ALL|AND|AS|ASC|ASCENDING|ASSERT|BY|CALL|CASE|COMMIT|CONSTRAINT|CONTAINS|CREATE|CSV|DELETE|DESC|DESCENDING|DETACH|DISTINCT|DO|DROP|ELSE|END|ENDS|EXISTS|FOR|FOREACH|IN|INDEX|IS|JOIN|KEY|LIMIT|LOAD|MANDATORY|MATCH|MERGE|NODE|NOT|OF|ON|OPTIONAL|OR|ORDER(?=\\s+BY)|PERIODIC|REMOVE|REQUIRE|RETURN|SCALAR|SCAN|SET|SKIP|START|STARTS|THEN|UNION|UNIQUE|UNWIND|USING|WHEN|WHERE|WITH|XOR|YIELD)\\b/i,\n    function: /\\b\\w+\\b(?=\\s*\\()/,\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: /\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)\\b/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/operators/\n    operator: /:|<--?|--?>?|<>|=~?|[<>]=?|[+*/%^|]|\\.\\.\\.?/,\n    punctuation: /[()[\\]{},;.]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "cypher", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "lookbehind", "relationship", "alias", "identifier", "variable", "keyword", "function", "boolean", "number", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/cypher.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = cypher\ncypher.displayName = 'cypher'\ncypher.aliases = []\nfunction cypher(Prism) {\n  Prism.languages.cypher = {\n    // https://neo4j.com/docs/cypher-manual/current/syntax/comments/\n    comment: /\\/\\/.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)(?=\\s*[{):])/,\n      lookbehind: true,\n      greedy: true\n    },\n    relationship: {\n      pattern:\n        /(-\\[\\s*(?:\\w+\\s*|`(?:[^`\\\\\\r\\n])*`\\s*)?:\\s*|\\|\\s*:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    identifier: {\n      pattern: /`(?:[^`\\\\\\r\\n])*`/,\n      greedy: true\n    },\n    variable: /\\$\\w+/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/reserved/\n    keyword:\n      /\\b(?:ADD|ALL|AND|AS|ASC|ASCENDING|ASSERT|BY|CALL|CASE|COMMIT|CONSTRAINT|CONTAINS|CREATE|CSV|DELETE|DESC|DESCENDING|DETACH|DISTINCT|DO|DROP|ELSE|END|ENDS|EXISTS|FOR|FOREACH|IN|INDEX|IS|JOIN|KEY|LIMIT|LOAD|MANDATORY|MATCH|MERGE|NODE|NOT|OF|ON|OPTIONAL|OR|ORDER(?=\\s+BY)|PERIODIC|REMOVE|REQUIRE|RETURN|SCALAR|SCAN|SET|SKIP|START|STARTS|THEN|UNION|UNIQUE|UNWIND|USING|WHEN|WHERE|WITH|XOR|YIELD)\\b/i,\n    function: /\\b\\w+\\b(?=\\s*\\()/,\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: /\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)\\b/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/operators/\n    operator: /:|<--?|--?>?|<>|=~?|[<>]=?|[+*/%^|]|\\.\\.\\.?/,\n    punctuation: /[()[\\]{},;.]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvB;IACAK,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE;MACNC,OAAO,EAAE,6CAA6C;MACtDC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZD,OAAO,EAAE,6CAA6C;MACtDE,UAAU,EAAE,IAAI;MAChBD,MAAM,EAAE;IACV,CAAC;IACDE,YAAY,EAAE;MACZH,OAAO,EACL,iFAAiF;MACnFE,UAAU,EAAE,IAAI;MAChBD,MAAM,EAAE,IAAI;MACZG,KAAK,EAAE;IACT,CAAC;IACDC,UAAU,EAAE;MACVL,OAAO,EAAE,mBAAmB;MAC5BC,MAAM,EAAE;IACV,CAAC;IACDK,QAAQ,EAAE,OAAO;IACjB;IACAC,OAAO,EACL,2YAA2Y;IAC7YC,QAAQ,EAAE,kBAAkB;IAC5BC,OAAO,EAAE,0BAA0B;IACnCC,MAAM,EAAE,sDAAsD;IAC9D;IACAC,QAAQ,EAAE,6CAA6C;IACvDC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}