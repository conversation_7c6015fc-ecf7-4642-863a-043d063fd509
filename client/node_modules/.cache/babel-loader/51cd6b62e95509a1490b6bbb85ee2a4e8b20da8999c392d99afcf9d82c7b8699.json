{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Augmented Backus-Naur Form\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://tools.ietf.org/html/rfc5234\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction abnf(hljs) {\n  const regexes = {\n    ruleDeclaration: /^[a-zA-Z][a-zA-Z0-9-]*/,\n    unexpectedChars: /[!@#$^&',?+~`|:]/\n  };\n  const keywords = [\"ALPHA\", \"BIT\", \"CHAR\", \"CR\", \"CRLF\", \"CTL\", \"DIGIT\", \"DQUOTE\", \"HEXDIG\", \"HTAB\", \"LF\", \"LWSP\", \"OCTET\", \"SP\", \"VCHAR\", \"WSP\"];\n  const commentMode = hljs.COMMENT(/;/, /$/);\n  const terminalBinaryMode = {\n    className: \"symbol\",\n    begin: /%b[0-1]+(-[0-1]+|(\\.[0-1]+)+){0,1}/\n  };\n  const terminalDecimalMode = {\n    className: \"symbol\",\n    begin: /%d[0-9]+(-[0-9]+|(\\.[0-9]+)+){0,1}/\n  };\n  const terminalHexadecimalMode = {\n    className: \"symbol\",\n    begin: /%x[0-9A-F]+(-[0-9A-F]+|(\\.[0-9A-F]+)+){0,1}/\n  };\n  const caseSensitivityIndicatorMode = {\n    className: \"symbol\",\n    begin: /%[si]/\n  };\n  const ruleDeclarationMode = {\n    className: \"attribute\",\n    begin: concat(regexes.ruleDeclaration, /(?=\\s*=)/)\n  };\n  return {\n    name: 'Augmented Backus-Naur Form',\n    illegal: regexes.unexpectedChars,\n    keywords: keywords,\n    contains: [ruleDeclarationMode, commentMode, terminalBinaryMode, terminalDecimalMode, terminalHexadecimalMode, caseSensitivityIndicatorMode, hljs.QUOTE_STRING_MODE, hljs.NUMBER_MODE]\n  };\n}\nmodule.exports = abnf;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "abnf", "hljs", "regexes", "ruleDeclaration", "<PERSON><PERSON><PERSON><PERSON>", "keywords", "commentMode", "COMMENT", "terminalBinaryMode", "className", "begin", "terminalDecimalMode", "terminalHexadecimalMode", "caseSensitivityIndicatorMode", "ruleDeclarationMode", "name", "illegal", "contains", "QUOTE_STRING_MODE", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/abnf.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Augmented Backus-Naur Form\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://tools.ietf.org/html/rfc5234\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction abnf(hljs) {\n  const regexes = {\n    ruleDeclaration: /^[a-zA-Z][a-zA-Z0-9-]*/,\n    unexpectedChars: /[!@#$^&',?+~`|:]/\n  };\n\n  const keywords = [\n    \"ALPHA\",\n    \"BIT\",\n    \"CHAR\",\n    \"CR\",\n    \"CRLF\",\n    \"CTL\",\n    \"DIGIT\",\n    \"DQUOTE\",\n    \"HEXDIG\",\n    \"HTAB\",\n    \"LF\",\n    \"LWSP\",\n    \"OCTET\",\n    \"SP\",\n    \"VCHAR\",\n    \"WSP\"\n  ];\n\n  const commentMode = hljs.COMMENT(/;/, /$/);\n\n  const terminalBinaryMode = {\n    className: \"symbol\",\n    begin: /%b[0-1]+(-[0-1]+|(\\.[0-1]+)+){0,1}/\n  };\n\n  const terminalDecimalMode = {\n    className: \"symbol\",\n    begin: /%d[0-9]+(-[0-9]+|(\\.[0-9]+)+){0,1}/\n  };\n\n  const terminalHexadecimalMode = {\n    className: \"symbol\",\n    begin: /%x[0-9A-F]+(-[0-9A-F]+|(\\.[0-9A-F]+)+){0,1}/\n  };\n\n  const caseSensitivityIndicatorMode = {\n    className: \"symbol\",\n    begin: /%[si]/\n  };\n\n  const ruleDeclarationMode = {\n    className: \"attribute\",\n    begin: concat(regexes.ruleDeclaration, /(?=\\s*=)/)\n  };\n\n  return {\n    name: 'Augmented Backus-Naur Form',\n    illegal: regexes.unexpectedChars,\n    keywords: keywords,\n    contains: [\n      ruleDeclarationMode,\n      commentMode,\n      terminalBinaryMode,\n      terminalDecimalMode,\n      terminalHexadecimalMode,\n      caseSensitivityIndicatorMode,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = abnf;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,OAAO,GAAG;IACdC,eAAe,EAAE,wBAAwB;IACzCC,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,QAAQ,GAAG,CACf,OAAO,EACP,KAAK,EACL,MAAM,EACN,IAAI,EACJ,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,MAAM,EACN,OAAO,EACP,IAAI,EACJ,OAAO,EACP,KAAK,CACN;EAED,MAAMC,WAAW,GAAGL,IAAI,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAE1C,MAAMC,kBAAkB,GAAG;IACzBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,mBAAmB,GAAG;IAC1BF,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAME,uBAAuB,GAAG;IAC9BH,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMG,4BAA4B,GAAG;IACnCJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMI,mBAAmB,GAAG;IAC1BL,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAEhB,MAAM,CAACQ,OAAO,CAACC,eAAe,EAAE,UAAU;EACnD,CAAC;EAED,OAAO;IACLY,IAAI,EAAE,4BAA4B;IAClCC,OAAO,EAAEd,OAAO,CAACE,eAAe;IAChCC,QAAQ,EAAEA,QAAQ;IAClBY,QAAQ,EAAE,CACRH,mBAAmB,EACnBR,WAAW,EACXE,kBAAkB,EAClBG,mBAAmB,EACnBC,uBAAuB,EACvBC,4BAA4B,EAC5BZ,IAAI,CAACiB,iBAAiB,EACtBjB,IAAI,CAACkB,WAAW;EAEpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGrB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}