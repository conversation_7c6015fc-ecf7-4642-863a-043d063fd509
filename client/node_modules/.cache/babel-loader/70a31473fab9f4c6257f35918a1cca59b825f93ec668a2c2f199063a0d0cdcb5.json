{"ast": null, "code": "'use strict';\n\nmodule.exports = jolie;\njolie.displayName = 'jolie';\njolie.aliases = [];\nfunction jolie(Prism) {\n  Prism.languages.jolie = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /((?:\\b(?:as|courier|embed|in|inputPort|outputPort|service)\\b|@)[ \\t]*)\\w+/,\n      lookbehind: true\n    },\n    keyword: /\\b(?:as|cH|comp|concurrent|constants|courier|cset|csets|default|define|else|embed|embedded|execution|exit|extender|for|foreach|forward|from|global|if|import|in|include|init|inputPort|install|instanceof|interface|is_defined|linkIn|linkOut|main|new|nullProcess|outputPort|over|private|provide|public|scope|sequential|service|single|spawn|synchronized|this|throw|throws|type|undef|until|while|with)\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*[@(])/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?l?/i,\n    operator: /-[-=>]?|\\+[+=]?|<[<=]?|[>=*!]=?|&&|\\|\\||[?\\/%^@|]/,\n    punctuation: /[()[\\]{},;.:]/,\n    builtin: /\\b(?:Byte|any|bool|char|double|enum|float|int|length|long|ranges|regex|string|undefined|void)\\b/\n  });\n  Prism.languages.insertBefore('jolie', 'keyword', {\n    aggregates: {\n      pattern: /(\\bAggregates\\s*:\\s*)(?:\\w+(?:\\s+with\\s+\\w+)?\\s*,\\s*)*\\w+(?:\\s+with\\s+\\w+)?/,\n      lookbehind: true,\n      inside: {\n        keyword: /\\bwith\\b/,\n        'class-name': /\\w+/,\n        punctuation: /,/\n      }\n    },\n    redirects: {\n      pattern: /(\\bRedirects\\s*:\\s*)(?:\\w+\\s*=>\\s*\\w+\\s*,\\s*)*(?:\\w+\\s*=>\\s*\\w+)/,\n      lookbehind: true,\n      inside: {\n        punctuation: /,/,\n        'class-name': /\\w+/,\n        operator: /=>/\n      }\n    },\n    property: {\n      pattern: /\\b(?:Aggregates|[Ii]nterfaces|Java|Javascript|Jolie|[Ll]ocation|OneWay|[Pp]rotocol|Redirects|RequestResponse)\\b(?=[ \\t]*:)/\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "jolie", "displayName", "aliases", "Prism", "languages", "extend", "string", "pattern", "lookbehind", "greedy", "keyword", "function", "number", "operator", "punctuation", "builtin", "insertBefore", "aggregates", "inside", "redirects", "property"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/jolie.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jolie\njolie.displayName = 'jolie'\njolie.aliases = []\nfunction jolie(Prism) {\n  Prism.languages.jolie = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /((?:\\b(?:as|courier|embed|in|inputPort|outputPort|service)\\b|@)[ \\t]*)\\w+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:as|cH|comp|concurrent|constants|courier|cset|csets|default|define|else|embed|embedded|execution|exit|extender|for|foreach|forward|from|global|if|import|in|include|init|inputPort|install|instanceof|interface|is_defined|linkIn|linkOut|main|new|nullProcess|outputPort|over|private|provide|public|scope|sequential|service|single|spawn|synchronized|this|throw|throws|type|undef|until|while|with)\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*[@(])/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?l?/i,\n    operator: /-[-=>]?|\\+[+=]?|<[<=]?|[>=*!]=?|&&|\\|\\||[?\\/%^@|]/,\n    punctuation: /[()[\\]{},;.:]/,\n    builtin:\n      /\\b(?:Byte|any|bool|char|double|enum|float|int|length|long|ranges|regex|string|undefined|void)\\b/\n  })\n  Prism.languages.insertBefore('jolie', 'keyword', {\n    aggregates: {\n      pattern:\n        /(\\bAggregates\\s*:\\s*)(?:\\w+(?:\\s+with\\s+\\w+)?\\s*,\\s*)*\\w+(?:\\s+with\\s+\\w+)?/,\n      lookbehind: true,\n      inside: {\n        keyword: /\\bwith\\b/,\n        'class-name': /\\w+/,\n        punctuation: /,/\n      }\n    },\n    redirects: {\n      pattern:\n        /(\\bRedirects\\s*:\\s*)(?:\\w+\\s*=>\\s*\\w+\\s*,\\s*)*(?:\\w+\\s*=>\\s*\\w+)/,\n      lookbehind: true,\n      inside: {\n        punctuation: /,/,\n        'class-name': /\\w+/,\n        operator: /=>/\n      }\n    },\n    property: {\n      pattern:\n        /\\b(?:Aggregates|[Ii]nterfaces|Java|Javascript|Jolie|[Ll]ocation|OneWay|[Pp]rotocol|Redirects|RequestResponse)\\b(?=[ \\t]*:)/\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACtDC,MAAM,EAAE;MACNC,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZF,OAAO,EACL,2EAA2E;MAC7EC,UAAU,EAAE;IACd,CAAC;IACDE,OAAO,EACL,+YAA+Y;IACjZC,QAAQ,EAAE,4BAA4B;IACtCC,MAAM,EAAE,8CAA8C;IACtDC,QAAQ,EAAE,mDAAmD;IAC7DC,WAAW,EAAE,eAAe;IAC5BC,OAAO,EACL;EACJ,CAAC,CAAC;EACFZ,KAAK,CAACC,SAAS,CAACY,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE;IAC/CC,UAAU,EAAE;MACVV,OAAO,EACL,6EAA6E;MAC/EC,UAAU,EAAE,IAAI;MAChBU,MAAM,EAAE;QACNR,OAAO,EAAE,UAAU;QACnB,YAAY,EAAE,KAAK;QACnBI,WAAW,EAAE;MACf;IACF,CAAC;IACDK,SAAS,EAAE;MACTZ,OAAO,EACL,kEAAkE;MACpEC,UAAU,EAAE,IAAI;MAChBU,MAAM,EAAE;QACNJ,WAAW,EAAE,GAAG;QAChB,YAAY,EAAE,KAAK;QACnBD,QAAQ,EAAE;MACZ;IACF,CAAC;IACDO,QAAQ,EAAE;MACRb,OAAO,EACL;IACJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}