{"ast": null, "code": "'use strict';\n\nmodule.exports = log;\nlog.displayName = 'log';\nlog.aliases = [];\nfunction log(Prism) {\n  // This is a language definition for generic log files.\n  // Since there is no one log format, this language definition has to support all formats to some degree.\n  //\n  // Based on https://github.com/MTDL9/vim-log-highlighting\n  Prism.languages.log = {\n    string: {\n      // Single-quoted strings must not be confused with plain text. E.g. Can't isn't <PERSON>'s <PERSON>' toy\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?![st] | \\w)(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    exception: {\n      pattern: /(^|[^\\w.])[a-z][\\w.]*(?:Error|Exception):.*(?:(?:\\r\\n?|\\n)[ \\t]*(?:at[ \\t].+|\\.{3}.*|Caused by:.*))+(?:(?:\\r\\n?|\\n)[ \\t]*\\.\\.\\. .*)?/,\n      lookbehind: true,\n      greedy: true,\n      alias: ['javastacktrace', 'language-javastacktrace'],\n      inside: Prism.languages['javastacktrace'] || {\n        keyword: /\\bat\\b/,\n        function: /[a-z_][\\w$]*(?=\\()/,\n        punctuation: /[.:()]/\n      }\n    },\n    level: [{\n      pattern: /\\b(?:ALERT|CRIT|CRITICAL|EMERG|EMERGENCY|ERR|ERROR|FAILURE|FATAL|SEVERE)\\b/,\n      alias: ['error', 'important']\n    }, {\n      pattern: /\\b(?:WARN|WARNING|WRN)\\b/,\n      alias: ['warning', 'important']\n    }, {\n      pattern: /\\b(?:DISPLAY|INF|INFO|NOTICE|STATUS)\\b/,\n      alias: ['info', 'keyword']\n    }, {\n      pattern: /\\b(?:DBG|DEBUG|FINE)\\b/,\n      alias: ['debug', 'keyword']\n    }, {\n      pattern: /\\b(?:FINER|FINEST|TRACE|TRC|VERBOSE|VRB)\\b/,\n      alias: ['trace', 'comment']\n    }],\n    property: {\n      pattern: /((?:^|[\\]|])[ \\t]*)[a-z_](?:[\\w-]|\\b\\/\\b)*(?:[. ]\\(?\\w(?:[\\w-]|\\b\\/\\b)*\\)?)*:(?=\\s)/im,\n      lookbehind: true\n    },\n    separator: {\n      pattern: /(^|[^-+])-{3,}|={3,}|\\*{3,}|- - /m,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    url: /\\b(?:file|ftp|https?):\\/\\/[^\\s|,;'\"]*[^\\s|,;'\">.]/,\n    email: {\n      pattern: /(^|\\s)[-\\w+.]+@[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)+(?=\\s)/,\n      lookbehind: true,\n      alias: 'url'\n    },\n    'ip-address': {\n      pattern: /\\b(?:\\d{1,3}(?:\\.\\d{1,3}){3})\\b/,\n      alias: 'constant'\n    },\n    'mac-address': {\n      pattern: /\\b[a-f0-9]{2}(?::[a-f0-9]{2}){5}\\b/i,\n      alias: 'constant'\n    },\n    domain: {\n      pattern: /(^|\\s)[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)*\\.[a-z][a-z0-9-]+(?=\\s)/,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    uuid: {\n      pattern: /\\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\b/i,\n      alias: 'constant'\n    },\n    hash: {\n      pattern: /\\b(?:[a-f0-9]{32}){1,2}\\b/i,\n      alias: 'constant'\n    },\n    'file-path': {\n      pattern: /\\b[a-z]:[\\\\/][^\\s|,;:(){}\\[\\]\"']+|(^|[\\s:\\[\\](>|])\\.{0,2}\\/\\w[^\\s|,;:(){}\\[\\]\"']*/i,\n      lookbehind: true,\n      greedy: true,\n      alias: 'string'\n    },\n    date: {\n      pattern: RegExp(/\\b\\d{4}[-/]\\d{2}[-/]\\d{2}(?:T(?=\\d{1,2}:)|(?=\\s\\d{1,2}:))/.source + '|' + /\\b\\d{1,4}[-/ ](?:\\d{1,2}|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)[-/ ]\\d{2,4}T?\\b/.source + '|' + /\\b(?:(?:Fri|Mon|Sat|Sun|Thu|Tue|Wed)(?:\\s{1,2}(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep))?|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)\\s{1,2}\\d{1,2}\\b/.source, 'i'),\n      alias: 'number'\n    },\n    time: {\n      pattern: /\\b\\d{1,2}:\\d{1,2}:\\d{1,2}(?:[.,:]\\d+)?(?:\\s?[+-]\\d{2}:?\\d{2}|Z)?\\b/,\n      alias: 'number'\n    },\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: {\n      pattern: /(^|[^.\\w])(?:0x[a-f0-9]+|0o[0-7]+|0b[01]+|v?\\d[\\da-f]*(?:\\.\\d+)*(?:e[+-]?\\d+)?[a-z]{0,3}\\b)\\b(?!\\.\\w)/i,\n      lookbehind: true\n    },\n    operator: /[;:?<=>~/@!$%&+\\-|^(){}*#]/,\n    punctuation: /[\\[\\].,]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "log", "displayName", "aliases", "Prism", "languages", "string", "pattern", "greedy", "exception", "lookbehind", "alias", "inside", "keyword", "function", "punctuation", "level", "property", "separator", "url", "email", "domain", "uuid", "hash", "date", "RegExp", "source", "time", "boolean", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/log.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = log\nlog.displayName = 'log'\nlog.aliases = []\nfunction log(Prism) {\n  // This is a language definition for generic log files.\n  // Since there is no one log format, this language definition has to support all formats to some degree.\n  //\n  // Based on https://github.com/MTDL9/vim-log-highlighting\n  Prism.languages.log = {\n    string: {\n      // Single-quoted strings must not be confused with plain text. E.g. Can't isn't Susan's Chris' toy\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?![st] | \\w)(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    exception: {\n      pattern:\n        /(^|[^\\w.])[a-z][\\w.]*(?:Error|Exception):.*(?:(?:\\r\\n?|\\n)[ \\t]*(?:at[ \\t].+|\\.{3}.*|Caused by:.*))+(?:(?:\\r\\n?|\\n)[ \\t]*\\.\\.\\. .*)?/,\n      lookbehind: true,\n      greedy: true,\n      alias: ['javastacktrace', 'language-javastacktrace'],\n      inside: Prism.languages['javastacktrace'] || {\n        keyword: /\\bat\\b/,\n        function: /[a-z_][\\w$]*(?=\\()/,\n        punctuation: /[.:()]/\n      }\n    },\n    level: [\n      {\n        pattern:\n          /\\b(?:ALERT|CRIT|CRITICAL|EMERG|EMERGENCY|ERR|ERROR|FAILURE|FATAL|SEVERE)\\b/,\n        alias: ['error', 'important']\n      },\n      {\n        pattern: /\\b(?:WARN|WARNING|WRN)\\b/,\n        alias: ['warning', 'important']\n      },\n      {\n        pattern: /\\b(?:DISPLAY|INF|INFO|NOTICE|STATUS)\\b/,\n        alias: ['info', 'keyword']\n      },\n      {\n        pattern: /\\b(?:DBG|DEBUG|FINE)\\b/,\n        alias: ['debug', 'keyword']\n      },\n      {\n        pattern: /\\b(?:FINER|FINEST|TRACE|TRC|VERBOSE|VRB)\\b/,\n        alias: ['trace', 'comment']\n      }\n    ],\n    property: {\n      pattern:\n        /((?:^|[\\]|])[ \\t]*)[a-z_](?:[\\w-]|\\b\\/\\b)*(?:[. ]\\(?\\w(?:[\\w-]|\\b\\/\\b)*\\)?)*:(?=\\s)/im,\n      lookbehind: true\n    },\n    separator: {\n      pattern: /(^|[^-+])-{3,}|={3,}|\\*{3,}|- - /m,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    url: /\\b(?:file|ftp|https?):\\/\\/[^\\s|,;'\"]*[^\\s|,;'\">.]/,\n    email: {\n      pattern: /(^|\\s)[-\\w+.]+@[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)+(?=\\s)/,\n      lookbehind: true,\n      alias: 'url'\n    },\n    'ip-address': {\n      pattern: /\\b(?:\\d{1,3}(?:\\.\\d{1,3}){3})\\b/,\n      alias: 'constant'\n    },\n    'mac-address': {\n      pattern: /\\b[a-f0-9]{2}(?::[a-f0-9]{2}){5}\\b/i,\n      alias: 'constant'\n    },\n    domain: {\n      pattern:\n        /(^|\\s)[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)*\\.[a-z][a-z0-9-]+(?=\\s)/,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    uuid: {\n      pattern:\n        /\\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\b/i,\n      alias: 'constant'\n    },\n    hash: {\n      pattern: /\\b(?:[a-f0-9]{32}){1,2}\\b/i,\n      alias: 'constant'\n    },\n    'file-path': {\n      pattern:\n        /\\b[a-z]:[\\\\/][^\\s|,;:(){}\\[\\]\"']+|(^|[\\s:\\[\\](>|])\\.{0,2}\\/\\w[^\\s|,;:(){}\\[\\]\"']*/i,\n      lookbehind: true,\n      greedy: true,\n      alias: 'string'\n    },\n    date: {\n      pattern: RegExp(\n        /\\b\\d{4}[-/]\\d{2}[-/]\\d{2}(?:T(?=\\d{1,2}:)|(?=\\s\\d{1,2}:))/.source +\n          '|' +\n          /\\b\\d{1,4}[-/ ](?:\\d{1,2}|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)[-/ ]\\d{2,4}T?\\b/\n            .source +\n          '|' +\n          /\\b(?:(?:Fri|Mon|Sat|Sun|Thu|Tue|Wed)(?:\\s{1,2}(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep))?|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)\\s{1,2}\\d{1,2}\\b/\n            .source,\n        'i'\n      ),\n      alias: 'number'\n    },\n    time: {\n      pattern:\n        /\\b\\d{1,2}:\\d{1,2}:\\d{1,2}(?:[.,:]\\d+)?(?:\\s?[+-]\\d{2}:?\\d{2}|Z)?\\b/,\n      alias: 'number'\n    },\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: {\n      pattern:\n        /(^|[^.\\w])(?:0x[a-f0-9]+|0o[0-7]+|0b[01]+|v?\\d[\\da-f]*(?:\\.\\d+)*(?:e[+-]?\\d+)?[a-z]{0,3}\\b)\\b(?!\\.\\w)/i,\n      lookbehind: true\n    },\n    operator: /[;:?<=>~/@!$%&+\\-|^(){}*#]/,\n    punctuation: /[\\[\\].,]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EACA;EACA;EACA;EACAA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,MAAM,EAAE;MACN;MACAC,OAAO,EAAE,0DAA0D;MACnEC,MAAM,EAAE;IACV,CAAC;IACDC,SAAS,EAAE;MACTF,OAAO,EACL,sIAAsI;MACxIG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZG,KAAK,EAAE,CAAC,gBAAgB,EAAE,yBAAyB,CAAC;MACpDC,MAAM,EAAER,KAAK,CAACC,SAAS,CAAC,gBAAgB,CAAC,IAAI;QAC3CQ,OAAO,EAAE,QAAQ;QACjBC,QAAQ,EAAE,oBAAoB;QAC9BC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,KAAK,EAAE,CACL;MACET,OAAO,EACL,4EAA4E;MAC9EI,KAAK,EAAE,CAAC,OAAO,EAAE,WAAW;IAC9B,CAAC,EACD;MACEJ,OAAO,EAAE,0BAA0B;MACnCI,KAAK,EAAE,CAAC,SAAS,EAAE,WAAW;IAChC,CAAC,EACD;MACEJ,OAAO,EAAE,wCAAwC;MACjDI,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS;IAC3B,CAAC,EACD;MACEJ,OAAO,EAAE,wBAAwB;MACjCI,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS;IAC5B,CAAC,EACD;MACEJ,OAAO,EAAE,4CAA4C;MACrDI,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS;IAC5B,CAAC,CACF;IACDM,QAAQ,EAAE;MACRV,OAAO,EACL,uFAAuF;MACzFG,UAAU,EAAE;IACd,CAAC;IACDQ,SAAS,EAAE;MACTX,OAAO,EAAE,mCAAmC;MAC5CG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDQ,GAAG,EAAE,mDAAmD;IACxDC,KAAK,EAAE;MACLb,OAAO,EAAE,4DAA4D;MACrEG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE;MACZJ,OAAO,EAAE,iCAAiC;MAC1CI,KAAK,EAAE;IACT,CAAC;IACD,aAAa,EAAE;MACbJ,OAAO,EAAE,qCAAqC;MAC9CI,KAAK,EAAE;IACT,CAAC;IACDU,MAAM,EAAE;MACNd,OAAO,EACL,oEAAoE;MACtEG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDW,IAAI,EAAE;MACJf,OAAO,EACL,mEAAmE;MACrEI,KAAK,EAAE;IACT,CAAC;IACDY,IAAI,EAAE;MACJhB,OAAO,EAAE,4BAA4B;MACrCI,KAAK,EAAE;IACT,CAAC;IACD,WAAW,EAAE;MACXJ,OAAO,EACL,oFAAoF;MACtFG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZG,KAAK,EAAE;IACT,CAAC;IACDa,IAAI,EAAE;MACJjB,OAAO,EAAEkB,MAAM,CACb,2DAA2D,CAACC,MAAM,GAChE,GAAG,GACH,2FAA2F,CACxFA,MAAM,GACT,GAAG,GACH,sKAAsK,CACnKA,MAAM,EACX,GACF,CAAC;MACDf,KAAK,EAAE;IACT,CAAC;IACDgB,IAAI,EAAE;MACJpB,OAAO,EACL,oEAAoE;MACtEI,KAAK,EAAE;IACT,CAAC;IACDiB,OAAO,EAAE,0BAA0B;IACnCC,MAAM,EAAE;MACNtB,OAAO,EACL,wGAAwG;MAC1GG,UAAU,EAAE;IACd,CAAC;IACDoB,QAAQ,EAAE,4BAA4B;IACtCf,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}