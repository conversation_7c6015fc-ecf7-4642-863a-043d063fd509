{"ast": null, "code": "'use strict';\n\nmodule.exports = bash;\nbash.displayName = 'bash';\nbash.aliases = ['shell'];\nfunction bash(Prism) {\n  ;\n  (function (Prism) {\n    // $ set | grep '^[A-Z][^[:space:]]*=' | cut -d= -f1 | tr '\\n' '|'\n    // + LC_ALL, RANDOM, REPLY, SECONDS.\n    // + make sure PS1..4 are here as they are not always set,\n    // - some useless things.\n    var envVars = '\\\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\\\b';\n    var commandAfterHeredoc = {\n      pattern: /(^([\"']?)\\w+\\2)[ \\t]+\\S.*/,\n      lookbehind: true,\n      alias: 'punctuation',\n      // this looks reasonably well in all themes\n      inside: null // see below\n    };\n    var insideString = {\n      bash: commandAfterHeredoc,\n      environment: {\n        pattern: RegExp('\\\\$' + envVars),\n        alias: 'constant'\n      },\n      variable: [\n      // [0]: Arithmetic Environment\n      {\n        pattern: /\\$?\\(\\([\\s\\S]+?\\)\\)/,\n        greedy: true,\n        inside: {\n          // If there is a $ sign at the beginning highlight $(( and )) as variable\n          variable: [{\n            pattern: /(^\\$\\(\\([\\s\\S]+)\\)\\)/,\n            lookbehind: true\n          }, /^\\$\\(\\(/],\n          number: /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n          // Operators according to https://www.gnu.org/software/bash/manual/bashref.html#Shell-Arithmetic\n          operator: /--|\\+\\+|\\*\\*=?|<<=?|>>=?|&&|\\|\\||[=!+\\-*/%<>^&|]=?|[?~:]/,\n          // If there is no $ sign at the beginning highlight (( and )) as punctuation\n          punctuation: /\\(\\(?|\\)\\)?|,|;/\n        }\n      },\n      // [1]: Command Substitution\n      {\n        pattern: /\\$\\((?:\\([^)]+\\)|[^()])+\\)|`[^`]+`/,\n        greedy: true,\n        inside: {\n          variable: /^\\$\\(|^`|\\)$|`$/\n        }\n      },\n      // [2]: Brace expansion\n      {\n        pattern: /\\$\\{[^}]+\\}/,\n        greedy: true,\n        inside: {\n          operator: /:[-=?+]?|[!\\/]|##?|%%?|\\^\\^?|,,?/,\n          punctuation: /[\\[\\]]/,\n          environment: {\n            pattern: RegExp('(\\\\{)' + envVars),\n            lookbehind: true,\n            alias: 'constant'\n          }\n        }\n      }, /\\$(?:\\w+|[#?*!@$])/],\n      // Escape sequences from echo and printf's manuals, and escaped quotes.\n      entity: /\\\\(?:[abceEfnrtv\\\\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/\n    };\n    Prism.languages.bash = {\n      shebang: {\n        pattern: /^#!\\s*\\/.*/,\n        alias: 'important'\n      },\n      comment: {\n        pattern: /(^|[^\"{\\\\$])#.*/,\n        lookbehind: true\n      },\n      'function-name': [\n      // a) function foo {\n      // b) foo() {\n      // c) function foo() {\n      // but not “foo {”\n      {\n        // a) and c)\n        pattern: /(\\bfunction\\s+)[\\w-]+(?=(?:\\s*\\(?:\\s*\\))?\\s*\\{)/,\n        lookbehind: true,\n        alias: 'function'\n      }, {\n        // b)\n        pattern: /\\b[\\w-]+(?=\\s*\\(\\s*\\)\\s*\\{)/,\n        alias: 'function'\n      }],\n      // Highlight variable names as variables in for and select beginnings.\n      'for-or-select': {\n        pattern: /(\\b(?:for|select)\\s+)\\w+(?=\\s+in\\s)/,\n        alias: 'variable',\n        lookbehind: true\n      },\n      // Highlight variable names as variables in the left-hand part\n      // of assignments (“=” and “+=”).\n      'assign-left': {\n        pattern: /(^|[\\s;|&]|[<>]\\()\\w+(?=\\+?=)/,\n        inside: {\n          environment: {\n            pattern: RegExp('(^|[\\\\s;|&]|[<>]\\\\()' + envVars),\n            lookbehind: true,\n            alias: 'constant'\n          }\n        },\n        alias: 'variable',\n        lookbehind: true\n      },\n      string: [\n      // Support for Here-documents https://en.wikipedia.org/wiki/Here_document\n      {\n        pattern: /((?:^|[^<])<<-?\\s*)(\\w+)\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\2/,\n        lookbehind: true,\n        greedy: true,\n        inside: insideString\n      },\n      // Here-document with quotes around the tag\n      // → No expansion (so no “inside”).\n      {\n        pattern: /((?:^|[^<])<<-?\\s*)([\"'])(\\w+)\\2\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\3/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          bash: commandAfterHeredoc\n        }\n      },\n      // “Normal” string\n      {\n        // https://www.gnu.org/software/bash/manual/html_node/Double-Quotes.html\n        pattern: /(^|[^\\\\](?:\\\\\\\\)*)\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/,\n        lookbehind: true,\n        greedy: true,\n        inside: insideString\n      }, {\n        // https://www.gnu.org/software/bash/manual/html_node/Single-Quotes.html\n        pattern: /(^|[^$\\\\])'[^']*'/,\n        lookbehind: true,\n        greedy: true\n      }, {\n        // https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html\n        pattern: /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n        greedy: true,\n        inside: {\n          entity: insideString.entity\n        }\n      }],\n      environment: {\n        pattern: RegExp('\\\\$?' + envVars),\n        alias: 'constant'\n      },\n      variable: insideString.variable,\n      function: {\n        pattern: /(^|[\\s;|&]|[<>]\\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\\s;|&])/,\n        lookbehind: true\n      },\n      keyword: {\n        pattern: /(^|[\\s;|&]|[<>]\\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\\s;|&])/,\n        lookbehind: true\n      },\n      // https://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n      builtin: {\n        pattern: /(^|[\\s;|&]|[<>]\\()(?:\\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\\s;|&])/,\n        lookbehind: true,\n        // Alias added to make those easier to distinguish from strings.\n        alias: 'class-name'\n      },\n      boolean: {\n        pattern: /(^|[\\s;|&]|[<>]\\()(?:false|true)(?=$|[)\\s;|&])/,\n        lookbehind: true\n      },\n      'file-descriptor': {\n        pattern: /\\B&\\d\\b/,\n        alias: 'important'\n      },\n      operator: {\n        // Lots of redirections here, but not just that.\n        pattern: /\\d?<>|>\\||\\+=|=[=~]?|!=?|<<[<-]?|[&\\d]?>>|\\d[<>]&?|[<>][&=]?|&[>&]?|\\|[&|]?/,\n        inside: {\n          'file-descriptor': {\n            pattern: /^\\d/,\n            alias: 'important'\n          }\n        }\n      },\n      punctuation: /\\$?\\(\\(?|\\)\\)?|\\.\\.|[{}[\\];\\\\]/,\n      number: {\n        pattern: /(^|\\s)(?:[1-9]\\d*|0)(?:[.,]\\d+)?\\b/,\n        lookbehind: true\n      }\n    };\n    commandAfterHeredoc.inside = Prism.languages.bash;\n    /* Patterns in command substitution. */\n    var toBeCopied = ['comment', 'function-name', 'for-or-select', 'assign-left', 'string', 'environment', 'function', 'keyword', 'builtin', 'boolean', 'file-descriptor', 'operator', 'punctuation', 'number'];\n    var inside = insideString.variable[1].inside;\n    for (var i = 0; i < toBeCopied.length; i++) {\n      inside[toBeCopied[i]] = Prism.languages.bash[toBeCopied[i]];\n    }\n    Prism.languages.shell = Prism.languages.bash;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "bash", "displayName", "aliases", "Prism", "envVars", "commandAfterHeredoc", "pattern", "lookbehind", "alias", "inside", "insideString", "environment", "RegExp", "variable", "greedy", "number", "operator", "punctuation", "entity", "languages", "shebang", "comment", "string", "function", "keyword", "builtin", "boolean", "toBeCopied", "i", "length", "shell"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/bash.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bash\nbash.displayName = 'bash'\nbash.aliases = ['shell']\nfunction bash(Prism) {\n  ;(function (Prism) {\n    // $ set | grep '^[A-Z][^[:space:]]*=' | cut -d= -f1 | tr '\\n' '|'\n    // + LC_ALL, RANDOM, REPLY, SECONDS.\n    // + make sure PS1..4 are here as they are not always set,\n    // - some useless things.\n    var envVars =\n      '\\\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\\\b'\n    var commandAfterHeredoc = {\n      pattern: /(^([\"']?)\\w+\\2)[ \\t]+\\S.*/,\n      lookbehind: true,\n      alias: 'punctuation',\n      // this looks reasonably well in all themes\n      inside: null // see below\n    }\n    var insideString = {\n      bash: commandAfterHeredoc,\n      environment: {\n        pattern: RegExp('\\\\$' + envVars),\n        alias: 'constant'\n      },\n      variable: [\n        // [0]: Arithmetic Environment\n        {\n          pattern: /\\$?\\(\\([\\s\\S]+?\\)\\)/,\n          greedy: true,\n          inside: {\n            // If there is a $ sign at the beginning highlight $(( and )) as variable\n            variable: [\n              {\n                pattern: /(^\\$\\(\\([\\s\\S]+)\\)\\)/,\n                lookbehind: true\n              },\n              /^\\$\\(\\(/\n            ],\n            number:\n              /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n            // Operators according to https://www.gnu.org/software/bash/manual/bashref.html#Shell-Arithmetic\n            operator:\n              /--|\\+\\+|\\*\\*=?|<<=?|>>=?|&&|\\|\\||[=!+\\-*/%<>^&|]=?|[?~:]/,\n            // If there is no $ sign at the beginning highlight (( and )) as punctuation\n            punctuation: /\\(\\(?|\\)\\)?|,|;/\n          }\n        }, // [1]: Command Substitution\n        {\n          pattern: /\\$\\((?:\\([^)]+\\)|[^()])+\\)|`[^`]+`/,\n          greedy: true,\n          inside: {\n            variable: /^\\$\\(|^`|\\)$|`$/\n          }\n        }, // [2]: Brace expansion\n        {\n          pattern: /\\$\\{[^}]+\\}/,\n          greedy: true,\n          inside: {\n            operator: /:[-=?+]?|[!\\/]|##?|%%?|\\^\\^?|,,?/,\n            punctuation: /[\\[\\]]/,\n            environment: {\n              pattern: RegExp('(\\\\{)' + envVars),\n              lookbehind: true,\n              alias: 'constant'\n            }\n          }\n        },\n        /\\$(?:\\w+|[#?*!@$])/\n      ],\n      // Escape sequences from echo and printf's manuals, and escaped quotes.\n      entity:\n        /\\\\(?:[abceEfnrtv\\\\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/\n    }\n    Prism.languages.bash = {\n      shebang: {\n        pattern: /^#!\\s*\\/.*/,\n        alias: 'important'\n      },\n      comment: {\n        pattern: /(^|[^\"{\\\\$])#.*/,\n        lookbehind: true\n      },\n      'function-name': [\n        // a) function foo {\n        // b) foo() {\n        // c) function foo() {\n        // but not “foo {”\n        {\n          // a) and c)\n          pattern: /(\\bfunction\\s+)[\\w-]+(?=(?:\\s*\\(?:\\s*\\))?\\s*\\{)/,\n          lookbehind: true,\n          alias: 'function'\n        },\n        {\n          // b)\n          pattern: /\\b[\\w-]+(?=\\s*\\(\\s*\\)\\s*\\{)/,\n          alias: 'function'\n        }\n      ],\n      // Highlight variable names as variables in for and select beginnings.\n      'for-or-select': {\n        pattern: /(\\b(?:for|select)\\s+)\\w+(?=\\s+in\\s)/,\n        alias: 'variable',\n        lookbehind: true\n      },\n      // Highlight variable names as variables in the left-hand part\n      // of assignments (“=” and “+=”).\n      'assign-left': {\n        pattern: /(^|[\\s;|&]|[<>]\\()\\w+(?=\\+?=)/,\n        inside: {\n          environment: {\n            pattern: RegExp('(^|[\\\\s;|&]|[<>]\\\\()' + envVars),\n            lookbehind: true,\n            alias: 'constant'\n          }\n        },\n        alias: 'variable',\n        lookbehind: true\n      },\n      string: [\n        // Support for Here-documents https://en.wikipedia.org/wiki/Here_document\n        {\n          pattern: /((?:^|[^<])<<-?\\s*)(\\w+)\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\2/,\n          lookbehind: true,\n          greedy: true,\n          inside: insideString\n        }, // Here-document with quotes around the tag\n        // → No expansion (so no “inside”).\n        {\n          pattern: /((?:^|[^<])<<-?\\s*)([\"'])(\\w+)\\2\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\3/,\n          lookbehind: true,\n          greedy: true,\n          inside: {\n            bash: commandAfterHeredoc\n          }\n        }, // “Normal” string\n        {\n          // https://www.gnu.org/software/bash/manual/html_node/Double-Quotes.html\n          pattern:\n            /(^|[^\\\\](?:\\\\\\\\)*)\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/,\n          lookbehind: true,\n          greedy: true,\n          inside: insideString\n        },\n        {\n          // https://www.gnu.org/software/bash/manual/html_node/Single-Quotes.html\n          pattern: /(^|[^$\\\\])'[^']*'/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html\n          pattern: /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n          greedy: true,\n          inside: {\n            entity: insideString.entity\n          }\n        }\n      ],\n      environment: {\n        pattern: RegExp('\\\\$?' + envVars),\n        alias: 'constant'\n      },\n      variable: insideString.variable,\n      function: {\n        pattern:\n          /(^|[\\s;|&]|[<>]\\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\\s;|&])/,\n        lookbehind: true\n      },\n      keyword: {\n        pattern:\n          /(^|[\\s;|&]|[<>]\\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\\s;|&])/,\n        lookbehind: true\n      },\n      // https://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n      builtin: {\n        pattern:\n          /(^|[\\s;|&]|[<>]\\()(?:\\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\\s;|&])/,\n        lookbehind: true,\n        // Alias added to make those easier to distinguish from strings.\n        alias: 'class-name'\n      },\n      boolean: {\n        pattern: /(^|[\\s;|&]|[<>]\\()(?:false|true)(?=$|[)\\s;|&])/,\n        lookbehind: true\n      },\n      'file-descriptor': {\n        pattern: /\\B&\\d\\b/,\n        alias: 'important'\n      },\n      operator: {\n        // Lots of redirections here, but not just that.\n        pattern:\n          /\\d?<>|>\\||\\+=|=[=~]?|!=?|<<[<-]?|[&\\d]?>>|\\d[<>]&?|[<>][&=]?|&[>&]?|\\|[&|]?/,\n        inside: {\n          'file-descriptor': {\n            pattern: /^\\d/,\n            alias: 'important'\n          }\n        }\n      },\n      punctuation: /\\$?\\(\\(?|\\)\\)?|\\.\\.|[{}[\\];\\\\]/,\n      number: {\n        pattern: /(^|\\s)(?:[1-9]\\d*|0)(?:[.,]\\d+)?\\b/,\n        lookbehind: true\n      }\n    }\n    commandAfterHeredoc.inside = Prism.languages.bash\n    /* Patterns in command substitution. */\n    var toBeCopied = [\n      'comment',\n      'function-name',\n      'for-or-select',\n      'assign-left',\n      'string',\n      'environment',\n      'function',\n      'keyword',\n      'builtin',\n      'boolean',\n      'file-descriptor',\n      'operator',\n      'punctuation',\n      'number'\n    ]\n    var inside = insideString.variable[1].inside\n    for (var i = 0; i < toBeCopied.length; i++) {\n      inside[toBeCopied[i]] = Prism.languages.bash[toBeCopied[i]]\n    }\n    Prism.languages.shell = Prism.languages.bash\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,CAAC,OAAO,CAAC;AACxB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA;IACA;IACA;IACA,IAAIC,OAAO,GACT,yoCAAyoC;IAC3oC,IAAIC,mBAAmB,GAAG;MACxBC,OAAO,EAAE,2BAA2B;MACpCC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,aAAa;MACpB;MACAC,MAAM,EAAE,IAAI,CAAC;IACf,CAAC;IACD,IAAIC,YAAY,GAAG;MACjBV,IAAI,EAAEK,mBAAmB;MACzBM,WAAW,EAAE;QACXL,OAAO,EAAEM,MAAM,CAAC,KAAK,GAAGR,OAAO,CAAC;QAChCI,KAAK,EAAE;MACT,CAAC;MACDK,QAAQ,EAAE;MACR;MACA;QACEP,OAAO,EAAE,qBAAqB;QAC9BQ,MAAM,EAAE,IAAI;QACZL,MAAM,EAAE;UACN;UACAI,QAAQ,EAAE,CACR;YACEP,OAAO,EAAE,sBAAsB;YAC/BC,UAAU,EAAE;UACd,CAAC,EACD,SAAS,CACV;UACDQ,MAAM,EACJ,6DAA6D;UAC/D;UACAC,QAAQ,EACN,0DAA0D;UAC5D;UACAC,WAAW,EAAE;QACf;MACF,CAAC;MAAE;MACH;QACEX,OAAO,EAAE,oCAAoC;QAC7CQ,MAAM,EAAE,IAAI;QACZL,MAAM,EAAE;UACNI,QAAQ,EAAE;QACZ;MACF,CAAC;MAAE;MACH;QACEP,OAAO,EAAE,aAAa;QACtBQ,MAAM,EAAE,IAAI;QACZL,MAAM,EAAE;UACNO,QAAQ,EAAE,kCAAkC;UAC5CC,WAAW,EAAE,QAAQ;UACrBN,WAAW,EAAE;YACXL,OAAO,EAAEM,MAAM,CAAC,OAAO,GAAGR,OAAO,CAAC;YAClCG,UAAU,EAAE,IAAI;YAChBC,KAAK,EAAE;UACT;QACF;MACF,CAAC,EACD,oBAAoB,CACrB;MACD;MACAU,MAAM,EACJ;IACJ,CAAC;IACDf,KAAK,CAACgB,SAAS,CAACnB,IAAI,GAAG;MACrBoB,OAAO,EAAE;QACPd,OAAO,EAAE,YAAY;QACrBE,KAAK,EAAE;MACT,CAAC;MACDa,OAAO,EAAE;QACPf,OAAO,EAAE,iBAAiB;QAC1BC,UAAU,EAAE;MACd,CAAC;MACD,eAAe,EAAE;MACf;MACA;MACA;MACA;MACA;QACE;QACAD,OAAO,EAAE,iDAAiD;QAC1DC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACAF,OAAO,EAAE,6BAA6B;QACtCE,KAAK,EAAE;MACT,CAAC,CACF;MACD;MACA,eAAe,EAAE;QACfF,OAAO,EAAE,qCAAqC;QAC9CE,KAAK,EAAE,UAAU;QACjBD,UAAU,EAAE;MACd,CAAC;MACD;MACA;MACA,aAAa,EAAE;QACbD,OAAO,EAAE,+BAA+B;QACxCG,MAAM,EAAE;UACNE,WAAW,EAAE;YACXL,OAAO,EAAEM,MAAM,CAAC,sBAAsB,GAAGR,OAAO,CAAC;YACjDG,UAAU,EAAE,IAAI;YAChBC,KAAK,EAAE;UACT;QACF,CAAC;QACDA,KAAK,EAAE,UAAU;QACjBD,UAAU,EAAE;MACd,CAAC;MACDe,MAAM,EAAE;MACN;MACA;QACEhB,OAAO,EAAE,kDAAkD;QAC3DC,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAE,IAAI;QACZL,MAAM,EAAEC;MACV,CAAC;MAAE;MACH;MACA;QACEJ,OAAO,EAAE,0DAA0D;QACnEC,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAE,IAAI;QACZL,MAAM,EAAE;UACNT,IAAI,EAAEK;QACR;MACF,CAAC;MAAE;MACH;QACE;QACAC,OAAO,EACL,yEAAyE;QAC3EC,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAE,IAAI;QACZL,MAAM,EAAEC;MACV,CAAC,EACD;QACE;QACAJ,OAAO,EAAE,mBAAmB;QAC5BC,UAAU,EAAE,IAAI;QAChBO,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAR,OAAO,EAAE,0BAA0B;QACnCQ,MAAM,EAAE,IAAI;QACZL,MAAM,EAAE;UACNS,MAAM,EAAER,YAAY,CAACQ;QACvB;MACF,CAAC,CACF;MACDP,WAAW,EAAE;QACXL,OAAO,EAAEM,MAAM,CAAC,MAAM,GAAGR,OAAO,CAAC;QACjCI,KAAK,EAAE;MACT,CAAC;MACDK,QAAQ,EAAEH,YAAY,CAACG,QAAQ;MAC/BU,QAAQ,EAAE;QACRjB,OAAO,EACL,0jDAA0jD;QAC5jDC,UAAU,EAAE;MACd,CAAC;MACDiB,OAAO,EAAE;QACPlB,OAAO,EACL,+GAA+G;QACjHC,UAAU,EAAE;MACd,CAAC;MACD;MACAkB,OAAO,EAAE;QACPnB,OAAO,EACL,4SAA4S;QAC9SC,UAAU,EAAE,IAAI;QAChB;QACAC,KAAK,EAAE;MACT,CAAC;MACDkB,OAAO,EAAE;QACPpB,OAAO,EAAE,gDAAgD;QACzDC,UAAU,EAAE;MACd,CAAC;MACD,iBAAiB,EAAE;QACjBD,OAAO,EAAE,SAAS;QAClBE,KAAK,EAAE;MACT,CAAC;MACDQ,QAAQ,EAAE;QACR;QACAV,OAAO,EACL,6EAA6E;QAC/EG,MAAM,EAAE;UACN,iBAAiB,EAAE;YACjBH,OAAO,EAAE,KAAK;YACdE,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDS,WAAW,EAAE,gCAAgC;MAC7CF,MAAM,EAAE;QACNT,OAAO,EAAE,oCAAoC;QAC7CC,UAAU,EAAE;MACd;IACF,CAAC;IACDF,mBAAmB,CAACI,MAAM,GAAGN,KAAK,CAACgB,SAAS,CAACnB,IAAI;IACjD;IACA,IAAI2B,UAAU,GAAG,CACf,SAAS,EACT,eAAe,EACf,eAAe,EACf,aAAa,EACb,QAAQ,EACR,aAAa,EACb,UAAU,EACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,iBAAiB,EACjB,UAAU,EACV,aAAa,EACb,QAAQ,CACT;IACD,IAAIlB,MAAM,GAAGC,YAAY,CAACG,QAAQ,CAAC,CAAC,CAAC,CAACJ,MAAM;IAC5C,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1CnB,MAAM,CAACkB,UAAU,CAACC,CAAC,CAAC,CAAC,GAAGzB,KAAK,CAACgB,SAAS,CAACnB,IAAI,CAAC2B,UAAU,CAACC,CAAC,CAAC,CAAC;IAC7D;IACAzB,KAAK,CAACgB,SAAS,CAACW,KAAK,GAAG3B,KAAK,CAACgB,SAAS,CAACnB,IAAI;EAC9C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}