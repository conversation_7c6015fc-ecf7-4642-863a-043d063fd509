{"ast": null, "code": "/*\nLanguage: Clojure REPL\nDescription: Clojure REPL sessions\nAuthor: <PERSON> <<EMAIL>>\nRequires: clojure.js\nWebsite: https://clojure.org\nCategory: lisp\n*/\n\n/** @type LanguageFn */\nfunction clojureRepl(hljs) {\n  return {\n    name: 'Clojure REPL',\n    contains: [{\n      className: 'meta',\n      begin: /^([\\w.-]+|\\s*#_)?=>/,\n      starts: {\n        end: /$/,\n        subLanguage: 'clojure'\n      }\n    }]\n  };\n}\nmodule.exports = clojureRepl;", "map": {"version": 3, "names": ["clojureRepl", "hljs", "name", "contains", "className", "begin", "starts", "end", "subLanguage", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/clojure-repl.js"], "sourcesContent": ["/*\nLanguage: Clojure REPL\nDescription: Clojure REPL sessions\nAuthor: <PERSON> <<EMAIL>>\nRequires: clojure.js\nWebsite: https://clojure.org\nCategory: lisp\n*/\n\n/** @type LanguageFn */\nfunction clojureRepl(hljs) {\n  return {\n    name: 'Clojure REPL',\n    contains: [\n      {\n        className: 'meta',\n        begin: /^([\\w.-]+|\\s*#_)?=>/,\n        starts: {\n          end: /$/,\n          subLanguage: 'clojure'\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = clojureRepl;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,WAAWA,CAACC,IAAI,EAAE;EACzB,OAAO;IACLC,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,qBAAqB;MAC5BC,MAAM,EAAE;QACNC,GAAG,EAAE,GAAG;QACRC,WAAW,EAAE;MACf;IACF,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGV,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}