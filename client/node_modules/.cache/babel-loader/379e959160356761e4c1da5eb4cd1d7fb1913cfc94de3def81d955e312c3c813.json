{"ast": null, "code": "'use strict';\n\nmodule.exports = less;\nless.displayName = 'less';\nless.aliases = [];\nfunction less(Prism) {\n  /* FIXME :\n  :extend() is not handled specifically : its highlighting is buggy.\n  Mixin usage must be inside a ruleset to be highlighted.\n  At-rules (e.g. import) containing interpolations are buggy.\n  Detached rulesets are highlighted as at-rules.\n  A comment before a mixin usage prevents the latter to be properly highlighted.\n  */\n  Prism.languages.less = Prism.languages.extend('css', {\n    comment: [/\\/\\*[\\s\\S]*?\\*\\//, {\n      pattern: /(^|[^\\\\])\\/\\/.*/,\n      lookbehind: true\n    }],\n    atrule: {\n      pattern: /@[\\w-](?:\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        punctuation: /[:()]/\n      }\n    },\n    // selectors and mixins are considered the same\n    selector: {\n      pattern: /(?:@\\{[\\w-]+\\}|[^{};\\s@])(?:@\\{[\\w-]+\\}|\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};@\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        // mixin parameters\n        variable: /@+[\\w-]+/\n      }\n    },\n    property: /(?:@\\{[\\w-]+\\}|[\\w-])+(?:\\+_?)?(?=\\s*:)/,\n    operator: /[+\\-*\\/]/\n  });\n  Prism.languages.insertBefore('less', 'property', {\n    variable: [\n    // Variable declaration (the colon must be consumed!)\n    {\n      pattern: /@[\\w-]+\\s*:/,\n      inside: {\n        punctuation: /:/\n      }\n    },\n    // Variable usage\n    /@@?[\\w-]+/],\n    'mixin-usage': {\n      pattern: /([{;]\\s*)[.#](?!\\d)[\\w-].*?(?=[(;])/,\n      lookbehind: true,\n      alias: 'function'\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "less", "displayName", "aliases", "Prism", "languages", "extend", "comment", "pattern", "lookbehind", "at<PERSON>le", "inside", "punctuation", "selector", "variable", "property", "operator", "insertBefore", "alias"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/less.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = less\nless.displayName = 'less'\nless.aliases = []\nfunction less(Prism) {\n  /* FIXME :\n:extend() is not handled specifically : its highlighting is buggy.\nMixin usage must be inside a ruleset to be highlighted.\nAt-rules (e.g. import) containing interpolations are buggy.\nDetached rulesets are highlighted as at-rules.\nA comment before a mixin usage prevents the latter to be properly highlighted.\n*/\n  Prism.languages.less = Prism.languages.extend('css', {\n    comment: [\n      /\\/\\*[\\s\\S]*?\\*\\//,\n      {\n        pattern: /(^|[^\\\\])\\/\\/.*/,\n        lookbehind: true\n      }\n    ],\n    atrule: {\n      pattern:\n        /@[\\w-](?:\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        punctuation: /[:()]/\n      }\n    },\n    // selectors and mixins are considered the same\n    selector: {\n      pattern:\n        /(?:@\\{[\\w-]+\\}|[^{};\\s@])(?:@\\{[\\w-]+\\}|\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};@\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        // mixin parameters\n        variable: /@+[\\w-]+/\n      }\n    },\n    property: /(?:@\\{[\\w-]+\\}|[\\w-])+(?:\\+_?)?(?=\\s*:)/,\n    operator: /[+\\-*\\/]/\n  })\n  Prism.languages.insertBefore('less', 'property', {\n    variable: [\n      // Variable declaration (the colon must be consumed!)\n      {\n        pattern: /@[\\w-]+\\s*:/,\n        inside: {\n          punctuation: /:/\n        }\n      }, // Variable usage\n      /@@?[\\w-]+/\n    ],\n    'mixin-usage': {\n      pattern: /([{;]\\s*)[.#](?!\\d)[\\w-].*?(?=[(;])/,\n      lookbehind: true,\n      alias: 'function'\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;AACF;AACA;AACA;AACA;AACA;AACA;EACEA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,KAAK,EAAE;IACnDC,OAAO,EAAE,CACP,kBAAkB,EAClB;MACEC,OAAO,EAAE,iBAAiB;MAC1BC,UAAU,EAAE;IACd,CAAC,CACF;IACDC,MAAM,EAAE;MACNF,OAAO,EACL,yEAAyE;MAC3EG,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACD;IACAC,QAAQ,EAAE;MACRL,OAAO,EACL,yGAAyG;MAC3GG,MAAM,EAAE;QACN;QACAG,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,QAAQ,EAAE,yCAAyC;IACnDC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFZ,KAAK,CAACC,SAAS,CAACY,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE;IAC/CH,QAAQ,EAAE;IACR;IACA;MACEN,OAAO,EAAE,aAAa;MACtBG,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IAAE;IACH,WAAW,CACZ;IACD,aAAa,EAAE;MACbJ,OAAO,EAAE,qCAAqC;MAC9CC,UAAU,EAAE,IAAI;MAChBS,KAAK,EAAE;IACT;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}