{"ast": null, "code": "/*\nLanguage: BASIC\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Based on the BASIC reference from the Tandy 1000 guide\nWebsite: https://en.wikipedia.org/wiki/Tandy_1000\n*/\n\n/** @type LanguageFn */\nfunction basic(hljs) {\n  return {\n    name: 'BASIC',\n    case_insensitive: true,\n    illegal: '^\\.',\n    // Support explicitly typed variables that end with $%! or #.\n    keywords: {\n      $pattern: '[a-zA-Z][a-zA-Z0-9_$%!#]*',\n      keyword: 'ABS ASC AND ATN AUTO|0 BEEP BLOAD|10 BSAVE|10 CALL CALLS CDBL CHAIN CHDIR CHR$|10 CINT CIRCLE ' + 'CLEAR CLOSE CLS COLOR COM COMMON CONT COS CSNG CSRLIN CVD CVI CVS DATA DATE$ ' + 'DEFDBL DEFINT DEFSNG DEFSTR DEF|0 SEG USR DELETE DIM DRAW EDIT END ENVIRON ENVIRON$ ' + 'EOF EQV ERASE ERDEV ERDEV$ ERL ERR ERROR EXP FIELD FILES FIX FOR|0 FRE GET GOSUB|10 GOTO ' + 'HEX$ IF THEN ELSE|0 INKEY$ INP INPUT INPUT# INPUT$ INSTR IMP INT IOCTL IOCTL$ KEY ON ' + 'OFF LIST KILL LEFT$ LEN LET LINE LLIST LOAD LOC LOCATE LOF LOG LPRINT USING LSET ' + 'MERGE MID$ MKDIR MKD$ MKI$ MKS$ MOD NAME NEW NEXT NOISE NOT OCT$ ON OR PEN PLAY STRIG OPEN OPTION ' + 'BASE OUT PAINT PALETTE PCOPY PEEK PMAP POINT POKE POS PRINT PRINT] PSET PRESET ' + 'PUT RANDOMIZE READ REM RENUM RESET|0 RESTORE RESUME RETURN|0 RIGHT$ RMDIR RND RSET ' + 'RUN SAVE SCREEN SGN SHELL SIN SOUND SPACE$ SPC SQR STEP STICK STOP STR$ STRING$ SWAP ' + 'SYSTEM TAB TAN TIME$ TIMER TROFF TRON TO USR VAL VARPTR VARPTR$ VIEW WAIT WHILE ' + 'WEND WIDTH WINDOW WRITE XOR'\n    },\n    contains: [hljs.QUOTE_STRING_MODE, hljs.COMMENT('REM', '$', {\n      relevance: 10\n    }), hljs.COMMENT('\\'', '$', {\n      relevance: 0\n    }), {\n      // Match line numbers\n      className: 'symbol',\n      begin: '^[0-9]+ ',\n      relevance: 10\n    }, {\n      // Match typed numeric constants (1000, 12.34!, 1.2e5, 1.5#, 1.2D2)\n      className: 'number',\n      begin: '\\\\b\\\\d+(\\\\.\\\\d+)?([edED]\\\\d+)?[#\\!]?',\n      relevance: 0\n    }, {\n      // Match hexadecimal numbers (&Hxxxx)\n      className: 'number',\n      begin: '(&[hH][0-9a-fA-F]{1,4})'\n    }, {\n      // Match octal numbers (&Oxxxxxx)\n      className: 'number',\n      begin: '(&[oO][0-7]{1,6})'\n    }]\n  };\n}\nmodule.exports = basic;", "map": {"version": 3, "names": ["basic", "hljs", "name", "case_insensitive", "illegal", "keywords", "$pattern", "keyword", "contains", "QUOTE_STRING_MODE", "COMMENT", "relevance", "className", "begin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/basic.js"], "sourcesContent": ["/*\nLanguage: BASIC\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Based on the BASIC reference from the Tandy 1000 guide\nWebsite: https://en.wikipedia.org/wiki/Tandy_1000\n*/\n\n/** @type LanguageFn */\nfunction basic(hljs) {\n  return {\n    name: 'BASIC',\n    case_insensitive: true,\n    illegal: '^\\.',\n    // Support explicitly typed variables that end with $%! or #.\n    keywords: {\n      $pattern: '[a-zA-Z][a-zA-Z0-9_$%!#]*',\n      keyword:\n        'ABS ASC AND ATN AUTO|0 BEEP BLOAD|10 BSAVE|10 CALL CALLS CDBL CHAIN CHDIR CHR$|10 CINT CIRCLE ' +\n        'CLEAR CLOSE CLS COLOR COM COMMON CONT COS CSNG CSRLIN CVD CVI CVS DATA DATE$ ' +\n        'DEFDBL DEFINT DEFSNG DEFSTR DEF|0 SEG USR DELETE DIM DRAW EDIT END ENVIRON ENVIRON$ ' +\n        'EOF EQV ERASE ERDEV ERDEV$ ERL ERR ERROR EXP FIELD FILES FIX FOR|0 FRE GET GOSUB|10 GOTO ' +\n        'HEX$ IF THEN ELSE|0 INKEY$ INP INPUT INPUT# INPUT$ INSTR IMP INT IOCTL IOCTL$ KEY ON ' +\n        'OFF LIST KILL LEFT$ LEN LET LINE LLIST LOAD LOC LOCATE LOF LOG LPRINT USING LSET ' +\n        'MERGE MID$ MKDIR MKD$ MKI$ MKS$ MOD NAME NEW NEXT NOISE NOT OCT$ ON OR PEN PLAY STRIG OPEN OPTION ' +\n        'BASE OUT PAINT PALETTE PCOPY PEEK PMAP POINT POKE POS PRINT PRINT] PSET PRESET ' +\n        'PUT RANDOMIZE READ REM RENUM RESET|0 RESTORE RESUME RETURN|0 RIGHT$ RMDIR RND RSET ' +\n        'RUN SAVE SCREEN SGN SHELL SIN SOUND SPACE$ SPC SQR STEP STICK STOP STR$ STRING$ SWAP ' +\n        'SYSTEM TAB TAN TIME$ TIMER TROFF TRON TO USR VAL VARPTR VARPTR$ VIEW WAIT WHILE ' +\n        'WEND WIDTH WINDOW WRITE XOR'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.COMMENT('REM', '$', {\n        relevance: 10\n      }),\n      hljs.COMMENT('\\'', '$', {\n        relevance: 0\n      }),\n      {\n        // Match line numbers\n        className: 'symbol',\n        begin: '^[0-9]+ ',\n        relevance: 10\n      },\n      {\n        // Match typed numeric constants (1000, 12.34!, 1.2e5, 1.5#, 1.2D2)\n        className: 'number',\n        begin: '\\\\b\\\\d+(\\\\.\\\\d+)?([edED]\\\\d+)?[#\\!]?',\n        relevance: 0\n      },\n      {\n        // Match hexadecimal numbers (&Hxxxx)\n        className: 'number',\n        begin: '(&[hH][0-9a-fA-F]{1,4})'\n      },\n      {\n        // Match octal numbers (&Oxxxxxx)\n        className: 'number',\n        begin: '(&[oO][0-7]{1,6})'\n      }\n    ]\n  };\n}\n\nmodule.exports = basic;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,KAAK;IACd;IACAC,QAAQ,EAAE;MACRC,QAAQ,EAAE,2BAA2B;MACrCC,OAAO,EACL,gGAAgG,GAChG,+EAA+E,GAC/E,sFAAsF,GACtF,2FAA2F,GAC3F,uFAAuF,GACvF,mFAAmF,GACnF,oGAAoG,GACpG,iFAAiF,GACjF,qFAAqF,GACrF,uFAAuF,GACvF,kFAAkF,GAClF;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRP,IAAI,CAACQ,iBAAiB,EACtBR,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE;MACvBC,SAAS,EAAE;IACb,CAAC,CAAC,EACFV,IAAI,CAACS,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;MACtBC,SAAS,EAAE;IACb,CAAC,CAAC,EACF;MACE;MACAC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,UAAU;MACjBF,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACAC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,sCAAsC;MAC7CF,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACAC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAD,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGf,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}