{"ast": null, "code": "const MODES = hljs => {\n  return {\n    IMPORTANT: {\n      className: 'meta',\n      begin: '!important'\n    },\n    HEXCOLOR: {\n      className: 'number',\n      begin: '#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})'\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      className: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n    }\n  };\n};\nconst TAGS = ['a', 'abbr', 'address', 'article', 'aside', 'audio', 'b', 'blockquote', 'body', 'button', 'canvas', 'caption', 'cite', 'code', 'dd', 'del', 'details', 'dfn', 'div', 'dl', 'dt', 'em', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hgroup', 'html', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'mark', 'menu', 'nav', 'object', 'ol', 'p', 'q', 'quote', 'samp', 'section', 'span', 'strong', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'ul', 'var', 'video'];\nconst MEDIA_FEATURES = ['any-hover', 'any-pointer', 'aspect-ratio', 'color', 'color-gamut', 'color-index', 'device-aspect-ratio', 'device-height', 'device-width', 'display-mode', 'forced-colors', 'grid', 'height', 'hover', 'inverted-colors', 'monochrome', 'orientation', 'overflow-block', 'overflow-inline', 'pointer', 'prefers-color-scheme', 'prefers-contrast', 'prefers-reduced-motion', 'prefers-reduced-transparency', 'resolution', 'scan', 'scripting', 'update', 'width',\n// TODO: find a better solution?\n'min-width', 'max-width', 'min-height', 'max-height'];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = ['active', 'any-link', 'blank', 'checked', 'current', 'default', 'defined', 'dir',\n// dir()\n'disabled', 'drop', 'empty', 'enabled', 'first', 'first-child', 'first-of-type', 'fullscreen', 'future', 'focus', 'focus-visible', 'focus-within', 'has',\n// has()\n'host',\n// host or host()\n'host-context',\n// host-context()\n'hover', 'indeterminate', 'in-range', 'invalid', 'is',\n// is()\n'lang',\n// lang()\n'last-child', 'last-of-type', 'left', 'link', 'local-link', 'not',\n// not()\n'nth-child',\n// nth-child()\n'nth-col',\n// nth-col()\n'nth-last-child',\n// nth-last-child()\n'nth-last-col',\n// nth-last-col()\n'nth-last-of-type',\n//nth-last-of-type()\n'nth-of-type',\n//nth-of-type()\n'only-child', 'only-of-type', 'optional', 'out-of-range', 'past', 'placeholder-shown', 'read-only', 'read-write', 'required', 'right', 'root', 'scope', 'target', 'target-within', 'user-invalid', 'valid', 'visited', 'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = ['after', 'backdrop', 'before', 'cue', 'cue-region', 'first-letter', 'first-line', 'grammar-error', 'marker', 'part', 'placeholder', 'selection', 'slotted', 'spelling-error'];\nconst ATTRIBUTES = ['align-content', 'align-items', 'align-self', 'animation', 'animation-delay', 'animation-direction', 'animation-duration', 'animation-fill-mode', 'animation-iteration-count', 'animation-name', 'animation-play-state', 'animation-timing-function', 'auto', 'backface-visibility', 'background', 'background-attachment', 'background-clip', 'background-color', 'background-image', 'background-origin', 'background-position', 'background-repeat', 'background-size', 'border', 'border-bottom', 'border-bottom-color', 'border-bottom-left-radius', 'border-bottom-right-radius', 'border-bottom-style', 'border-bottom-width', 'border-collapse', 'border-color', 'border-image', 'border-image-outset', 'border-image-repeat', 'border-image-slice', 'border-image-source', 'border-image-width', 'border-left', 'border-left-color', 'border-left-style', 'border-left-width', 'border-radius', 'border-right', 'border-right-color', 'border-right-style', 'border-right-width', 'border-spacing', 'border-style', 'border-top', 'border-top-color', 'border-top-left-radius', 'border-top-right-radius', 'border-top-style', 'border-top-width', 'border-width', 'bottom', 'box-decoration-break', 'box-shadow', 'box-sizing', 'break-after', 'break-before', 'break-inside', 'caption-side', 'clear', 'clip', 'clip-path', 'color', 'column-count', 'column-fill', 'column-gap', 'column-rule', 'column-rule-color', 'column-rule-style', 'column-rule-width', 'column-span', 'column-width', 'columns', 'content', 'counter-increment', 'counter-reset', 'cursor', 'direction', 'display', 'empty-cells', 'filter', 'flex', 'flex-basis', 'flex-direction', 'flex-flow', 'flex-grow', 'flex-shrink', 'flex-wrap', 'float', 'font', 'font-display', 'font-family', 'font-feature-settings', 'font-kerning', 'font-language-override', 'font-size', 'font-size-adjust', 'font-smoothing', 'font-stretch', 'font-style', 'font-variant', 'font-variant-ligatures', 'font-variation-settings', 'font-weight', 'height', 'hyphens', 'icon', 'image-orientation', 'image-rendering', 'image-resolution', 'ime-mode', 'inherit', 'initial', 'justify-content', 'left', 'letter-spacing', 'line-height', 'list-style', 'list-style-image', 'list-style-position', 'list-style-type', 'margin', 'margin-bottom', 'margin-left', 'margin-right', 'margin-top', 'marks', 'mask', 'max-height', 'max-width', 'min-height', 'min-width', 'nav-down', 'nav-index', 'nav-left', 'nav-right', 'nav-up', 'none', 'normal', 'object-fit', 'object-position', 'opacity', 'order', 'orphans', 'outline', 'outline-color', 'outline-offset', 'outline-style', 'outline-width', 'overflow', 'overflow-wrap', 'overflow-x', 'overflow-y', 'padding', 'padding-bottom', 'padding-left', 'padding-right', 'padding-top', 'page-break-after', 'page-break-before', 'page-break-inside', 'perspective', 'perspective-origin', 'pointer-events', 'position', 'quotes', 'resize', 'right', 'src',\n// @font-face\n'tab-size', 'table-layout', 'text-align', 'text-align-last', 'text-decoration', 'text-decoration-color', 'text-decoration-line', 'text-decoration-style', 'text-indent', 'text-overflow', 'text-rendering', 'text-shadow', 'text-transform', 'text-underline-position', 'top', 'transform', 'transform-origin', 'transform-style', 'transition', 'transition-delay', 'transition-duration', 'transition-property', 'transition-timing-function', 'unicode-bidi', 'vertical-align', 'visibility', 'white-space', 'widows', 'width', 'word-break', 'word-spacing', 'word-wrap', 'z-index'\n// reverse makes sure longer attributes `font-weight` are matched fully\n// instead of getting false positives on say `font`\n].reverse();\n\n/*\nLanguage: SCSS\nDescription: Scss is an extension of the syntax of CSS.\nAuthor: Kurt Emch <<EMAIL>>\nWebsite: https://sass-lang.com\nCategory: common, css\n*/\n\n/** @type LanguageFn */\nfunction scss(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_ELEMENTS$1 = PSEUDO_ELEMENTS;\n  const PSEUDO_CLASSES$1 = PSEUDO_CLASSES;\n  const AT_IDENTIFIER = '@[a-z-]+'; // @font-face\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const VARIABLE = {\n    className: 'variable',\n    begin: '(\\\\$' + IDENT_RE + ')\\\\b'\n  };\n  return {\n    name: 'SCSS',\n    case_insensitive: true,\n    illegal: '[=/|\\']',\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'selector-id',\n      begin: '#[A-Za-z0-9_-]+',\n      relevance: 0\n    }, {\n      className: 'selector-class',\n      begin: '\\\\.[A-Za-z0-9_-]+',\n      relevance: 0\n    }, modes.ATTRIBUTE_SELECTOR_MODE, {\n      className: 'selector-tag',\n      begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n      // was there, before, but why?\n      relevance: 0\n    }, {\n      className: 'selector-pseudo',\n      begin: ':(' + PSEUDO_CLASSES$1.join('|') + ')'\n    }, {\n      className: 'selector-pseudo',\n      begin: '::(' + PSEUDO_ELEMENTS$1.join('|') + ')'\n    }, VARIABLE, {\n      // pseudo-selector params\n      begin: /\\(/,\n      end: /\\)/,\n      contains: [hljs.CSS_NUMBER_MODE]\n    }, {\n      className: 'attribute',\n      begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n    }, {\n      begin: '\\\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\\\b'\n    }, {\n      begin: ':',\n      end: ';',\n      contains: [VARIABLE, modes.HEXCOLOR, hljs.CSS_NUMBER_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, modes.IMPORTANT]\n    },\n    // matching these here allows us to treat them more like regular CSS\n    // rules so everything between the {} gets regular rule highlighting,\n    // which is what we want for page and font-face\n    {\n      begin: '@(page|font-face)',\n      lexemes: AT_IDENTIFIER,\n      keywords: '@page @font-face'\n    }, {\n      begin: '@',\n      end: '[{;]',\n      returnBegin: true,\n      keywords: {\n        $pattern: /[a-z-]+/,\n        keyword: AT_MODIFIERS,\n        attribute: MEDIA_FEATURES.join(\" \")\n      },\n      contains: [{\n        begin: AT_IDENTIFIER,\n        className: \"keyword\"\n      }, {\n        begin: /[a-z-]+(?=:)/,\n        className: \"attribute\"\n      }, VARIABLE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, modes.HEXCOLOR, hljs.CSS_NUMBER_MODE]\n    }]\n  };\n}\nmodule.exports = scss;", "map": {"version": 3, "names": ["MODES", "hljs", "IMPORTANT", "className", "begin", "HEXCOLOR", "ATTRIBUTE_SELECTOR_MODE", "end", "illegal", "contains", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "TAGS", "MEDIA_FEATURES", "PSEUDO_CLASSES", "PSEUDO_ELEMENTS", "ATTRIBUTES", "reverse", "scss", "modes", "PSEUDO_ELEMENTS$1", "PSEUDO_CLASSES$1", "AT_IDENTIFIER", "AT_MODIFIERS", "IDENT_RE", "VARIABLE", "name", "case_insensitive", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "relevance", "join", "CSS_NUMBER_MODE", "lexemes", "keywords", "returnBegin", "$pattern", "keyword", "attribute", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/scss.js"], "sourcesContent": ["const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      className: 'meta',\n      begin: '!important'\n    },\n    HEXCOLOR: {\n      className: 'number',\n      begin: '#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})'\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      className: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'auto',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'border',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'clear',\n  'clip',\n  'clip-path',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'content',\n  'counter-increment',\n  'counter-reset',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-variant-ligatures',\n  'font-variation-settings',\n  'font-weight',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inherit',\n  'initial',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-bottom',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'max-height',\n  'max-width',\n  'min-height',\n  'min-width',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-bottom',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'right',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-last',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-indent',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'white-space',\n  'widows',\n  'width',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n/*\nLanguage: SCSS\nDescription: Scss is an extension of the syntax of CSS.\nAuthor: Kurt Emch <<EMAIL>>\nWebsite: https://sass-lang.com\nCategory: common, css\n*/\n\n/** @type LanguageFn */\nfunction scss(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_ELEMENTS$1 = PSEUDO_ELEMENTS;\n  const PSEUDO_CLASSES$1 = PSEUDO_CLASSES;\n\n  const AT_IDENTIFIER = '@[a-z-]+'; // @font-face\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const VARIABLE = {\n    className: 'variable',\n    begin: '(\\\\$' + IDENT_RE + ')\\\\b'\n  };\n\n  return {\n    name: 'SCSS',\n    case_insensitive: true,\n    illegal: '[=/|\\']',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'selector-id',\n        begin: '#[A-Za-z0-9_-]+',\n        relevance: 0\n      },\n      {\n        className: 'selector-class',\n        begin: '\\\\.[A-Za-z0-9_-]+',\n        relevance: 0\n      },\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-tag',\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n        // was there, before, but why?\n        relevance: 0\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(' + PSEUDO_CLASSES$1.join('|') + ')'\n      },\n      {\n        className: 'selector-pseudo',\n        begin: '::(' + PSEUDO_ELEMENTS$1.join('|') + ')'\n      },\n      VARIABLE,\n      { // pseudo-selector params\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [ hljs.CSS_NUMBER_MODE ]\n      },\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n      },\n      {\n        begin: '\\\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\\\b'\n      },\n      {\n        begin: ':',\n        end: ';',\n        contains: [\n          VARIABLE,\n          modes.HEXCOLOR,\n          hljs.CSS_NUMBER_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          modes.IMPORTANT\n        ]\n      },\n      // matching these here allows us to treat them more like regular CSS\n      // rules so everything between the {} gets regular rule highlighting,\n      // which is what we want for page and font-face\n      {\n        begin: '@(page|font-face)',\n        lexemes: AT_IDENTIFIER,\n        keywords: '@page @font-face'\n      },\n      {\n        begin: '@',\n        end: '[{;]',\n        returnBegin: true,\n        keywords: {\n          $pattern: /[a-z-]+/,\n          keyword: AT_MODIFIERS,\n          attribute: MEDIA_FEATURES.join(\" \")\n        },\n        contains: [\n          {\n            begin: AT_IDENTIFIER,\n            className: \"keyword\"\n          },\n          {\n            begin: /[a-z-]+(?=:)/,\n            className: \"attribute\"\n          },\n          VARIABLE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          modes.HEXCOLOR,\n          hljs.CSS_NUMBER_MODE\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = scss;\n"], "mappings": "AAAA,MAAMA,KAAK,GAAIC,IAAI,IAAK;EACtB,OAAO;IACLC,SAAS,EAAE;MACTC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC;IACDE,uBAAuB,EAAE;MACvBH,SAAS,EAAE,eAAe;MAC1BC,KAAK,EAAE,IAAI;MACXG,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,CACRR,IAAI,CAACS,gBAAgB,EACrBT,IAAI,CAACU,iBAAiB;IAE1B;EACF,CAAC;AACH,CAAC;AAED,MAAMC,IAAI,GAAG,CACX,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,GAAG,EACH,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,GAAG,EACH,GAAG,EACH,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,CACR;AAED,MAAMC,cAAc,GAAG,CACrB,WAAW,EACX,aAAa,EACb,cAAc,EACd,OAAO,EACP,aAAa,EACb,aAAa,EACb,qBAAqB,EACrB,eAAe,EACf,cAAc,EACd,cAAc,EACd,eAAe,EACf,MAAM,EACN,QAAQ,EACR,OAAO,EACP,iBAAiB,EACjB,YAAY,EACZ,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,SAAS,EACT,sBAAsB,EACtB,kBAAkB,EAClB,wBAAwB,EACxB,8BAA8B,EAC9B,YAAY,EACZ,MAAM,EACN,WAAW,EACX,QAAQ,EACR,OAAO;AACP;AACA,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,CACb;;AAED;AACA,MAAMC,cAAc,GAAG,CACrB,QAAQ,EACR,UAAU,EACV,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK;AAAE;AACP,UAAU,EACV,MAAM,EACN,OAAO,EACP,SAAS,EACT,OAAO,EACP,aAAa,EACb,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,eAAe,EACf,cAAc,EACd,KAAK;AAAE;AACP,MAAM;AAAE;AACR,cAAc;AAAE;AAChB,OAAO,EACP,eAAe,EACf,UAAU,EACV,SAAS,EACT,IAAI;AAAE;AACN,MAAM;AAAE;AACR,YAAY,EACZ,cAAc,EACd,MAAM,EACN,MAAM,EACN,YAAY,EACZ,KAAK;AAAE;AACP,WAAW;AAAE;AACb,SAAS;AAAE;AACX,gBAAgB;AAAE;AAClB,cAAc;AAAE;AAChB,kBAAkB;AAAE;AACpB,aAAa;AAAE;AACf,YAAY,EACZ,cAAc,EACd,UAAU,EACV,cAAc,EACd,MAAM,EACN,mBAAmB,EACnB,WAAW,EACX,YAAY,EACZ,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,eAAe,EACf,cAAc,EACd,OAAO,EACP,SAAS,EACT,OAAO,CAAC;AAAA,CACT;;AAED;AACA,MAAMC,eAAe,GAAG,CACtB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,MAAM,EACN,aAAa,EACb,WAAW,EACX,SAAS,EACT,gBAAgB,CACjB;AAED,MAAMC,UAAU,GAAG,CACjB,eAAe,EACf,aAAa,EACb,YAAY,EACZ,WAAW,EACX,iBAAiB,EACjB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,2BAA2B,EAC3B,gBAAgB,EAChB,sBAAsB,EACtB,2BAA2B,EAC3B,MAAM,EACN,qBAAqB,EACrB,YAAY,EACZ,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,qBAAqB,EACrB,mBAAmB,EACnB,iBAAiB,EACjB,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,2BAA2B,EAC3B,4BAA4B,EAC5B,qBAAqB,EACrB,qBAAqB,EACrB,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,oBAAoB,EACpB,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,wBAAwB,EACxB,yBAAyB,EACzB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,sBAAsB,EACtB,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,OAAO,EACP,MAAM,EACN,WAAW,EACX,OAAO,EACP,cAAc,EACd,aAAa,EACb,YAAY,EACZ,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,EACb,cAAc,EACd,SAAS,EACT,SAAS,EACT,mBAAmB,EACnB,eAAe,EACf,QAAQ,EACR,WAAW,EACX,SAAS,EACT,aAAa,EACb,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,WAAW,EACX,WAAW,EACX,aAAa,EACb,WAAW,EACX,OAAO,EACP,MAAM,EACN,cAAc,EACd,aAAa,EACb,uBAAuB,EACvB,cAAc,EACd,wBAAwB,EACxB,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,cAAc,EACd,wBAAwB,EACxB,yBAAyB,EACzB,aAAa,EACb,QAAQ,EACR,SAAS,EACT,MAAM,EACN,mBAAmB,EACnB,iBAAiB,EACjB,kBAAkB,EAClB,UAAU,EACV,SAAS,EACT,SAAS,EACT,iBAAiB,EACjB,MAAM,EACN,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,iBAAiB,EACjB,QAAQ,EACR,eAAe,EACf,aAAa,EACb,cAAc,EACd,YAAY,EACZ,OAAO,EACP,MAAM,EACN,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,WAAW,EACX,UAAU,EACV,WAAW,EACX,UAAU,EACV,WAAW,EACX,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,iBAAiB,EACjB,SAAS,EACT,OAAO,EACP,SAAS,EACT,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,UAAU,EACV,eAAe,EACf,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,EACb,oBAAoB,EACpB,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK;AAAE;AACP,UAAU,EACV,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,uBAAuB,EACvB,sBAAsB,EACtB,uBAAuB,EACvB,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,gBAAgB,EAChB,yBAAyB,EACzB,KAAK,EACL,WAAW,EACX,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,4BAA4B,EAC5B,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,cAAc,EACd,WAAW,EACX;AACA;AACA;AAAA,CACD,CAACC,OAAO,CAAC,CAAC;;AAEX;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASC,IAAIA,CAACjB,IAAI,EAAE;EAClB,MAAMkB,KAAK,GAAGnB,KAAK,CAACC,IAAI,CAAC;EACzB,MAAMmB,iBAAiB,GAAGL,eAAe;EACzC,MAAMM,gBAAgB,GAAGP,cAAc;EAEvC,MAAMQ,aAAa,GAAG,UAAU,CAAC,CAAC;EAClC,MAAMC,YAAY,GAAG,iBAAiB;EACtC,MAAMC,QAAQ,GAAG,yBAAyB;EAC1C,MAAMC,QAAQ,GAAG;IACftB,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,MAAM,GAAGoB,QAAQ,GAAG;EAC7B,CAAC;EAED,OAAO;IACLE,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBnB,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,CACRR,IAAI,CAAC2B,mBAAmB,EACxB3B,IAAI,CAAC4B,oBAAoB,EACzB;MACE1B,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,iBAAiB;MACxB0B,SAAS,EAAE;IACb,CAAC,EACD;MACE3B,SAAS,EAAE,gBAAgB;MAC3BC,KAAK,EAAE,mBAAmB;MAC1B0B,SAAS,EAAE;IACb,CAAC,EACDX,KAAK,CAACb,uBAAuB,EAC7B;MACEH,SAAS,EAAE,cAAc;MACzBC,KAAK,EAAE,MAAM,GAAGQ,IAAI,CAACmB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;MACvC;MACAD,SAAS,EAAE;IACb,CAAC,EACD;MACE3B,SAAS,EAAE,iBAAiB;MAC5BC,KAAK,EAAE,IAAI,GAAGiB,gBAAgB,CAACU,IAAI,CAAC,GAAG,CAAC,GAAG;IAC7C,CAAC,EACD;MACE5B,SAAS,EAAE,iBAAiB;MAC5BC,KAAK,EAAE,KAAK,GAAGgB,iBAAiB,CAACW,IAAI,CAAC,GAAG,CAAC,GAAG;IAC/C,CAAC,EACDN,QAAQ,EACR;MAAE;MACArB,KAAK,EAAE,IAAI;MACXG,GAAG,EAAE,IAAI;MACTE,QAAQ,EAAE,CAAER,IAAI,CAAC+B,eAAe;IAClC,CAAC,EACD;MACE7B,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,MAAM,GAAGY,UAAU,CAACe,IAAI,CAAC,GAAG,CAAC,GAAG;IACzC,CAAC,EACD;MACE3B,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,GAAG;MACVG,GAAG,EAAE,GAAG;MACRE,QAAQ,EAAE,CACRgB,QAAQ,EACRN,KAAK,CAACd,QAAQ,EACdJ,IAAI,CAAC+B,eAAe,EACpB/B,IAAI,CAACU,iBAAiB,EACtBV,IAAI,CAACS,gBAAgB,EACrBS,KAAK,CAACjB,SAAS;IAEnB,CAAC;IACD;IACA;IACA;IACA;MACEE,KAAK,EAAE,mBAAmB;MAC1B6B,OAAO,EAAEX,aAAa;MACtBY,QAAQ,EAAE;IACZ,CAAC,EACD;MACE9B,KAAK,EAAE,GAAG;MACVG,GAAG,EAAE,MAAM;MACX4B,WAAW,EAAE,IAAI;MACjBD,QAAQ,EAAE;QACRE,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAEd,YAAY;QACrBe,SAAS,EAAEzB,cAAc,CAACkB,IAAI,CAAC,GAAG;MACpC,CAAC;MACDtB,QAAQ,EAAE,CACR;QACEL,KAAK,EAAEkB,aAAa;QACpBnB,SAAS,EAAE;MACb,CAAC,EACD;QACEC,KAAK,EAAE,cAAc;QACrBD,SAAS,EAAE;MACb,CAAC,EACDsB,QAAQ,EACRxB,IAAI,CAACU,iBAAiB,EACtBV,IAAI,CAACS,gBAAgB,EACrBS,KAAK,CAACd,QAAQ,EACdJ,IAAI,CAAC+B,eAAe;IAExB,CAAC;EAEL,CAAC;AACH;AAEAO,MAAM,CAACC,OAAO,GAAGtB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}