{"ast": null, "code": "'use strict';\n\nmodule.exports = ebnf;\nebnf.displayName = 'ebnf';\nebnf.aliases = [];\nfunction ebnf(Prism) {\n  Prism.languages.ebnf = {\n    comment: /\\(\\*[\\s\\S]*?\\*\\)/,\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n      greedy: true\n    },\n    special: {\n      pattern: /\\?[^?\\r\\n]*\\?/,\n      greedy: true,\n      alias: 'class-name'\n    },\n    definition: {\n      pattern: /^([\\t ]*)[a-z]\\w*(?:[ \\t]+[a-z]\\w*)*(?=\\s*=)/im,\n      lookbehind: true,\n      alias: ['rule', 'keyword']\n    },\n    rule: /\\b[a-z]\\w*(?:[ \\t]+[a-z]\\w*)*\\b/i,\n    punctuation: /\\([:/]|[:/]\\)|[.,;()[\\]{}]/,\n    operator: /[-=|*/!]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "ebnf", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "special", "alias", "definition", "lookbehind", "rule", "punctuation", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/ebnf.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ebnf\nebnf.displayName = 'ebnf'\nebnf.aliases = []\nfunction ebnf(Prism) {\n  Prism.languages.ebnf = {\n    comment: /\\(\\*[\\s\\S]*?\\*\\)/,\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n      greedy: true\n    },\n    special: {\n      pattern: /\\?[^?\\r\\n]*\\?/,\n      greedy: true,\n      alias: 'class-name'\n    },\n    definition: {\n      pattern: /^([\\t ]*)[a-z]\\w*(?:[ \\t]+[a-z]\\w*)*(?=\\s*=)/im,\n      lookbehind: true,\n      alias: ['rule', 'keyword']\n    },\n    rule: /\\b[a-z]\\w*(?:[ \\t]+[a-z]\\w*)*\\b/i,\n    punctuation: /\\([:/]|[:/]\\)|[.,;()[\\]{}]/,\n    operator: /[-=|*/!]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,OAAO,EAAE,kBAAkB;IAC3BC,MAAM,EAAE;MACNC,OAAO,EAAE,yBAAyB;MAClCC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPF,OAAO,EAAE,eAAe;MACxBC,MAAM,EAAE,IAAI;MACZE,KAAK,EAAE;IACT,CAAC;IACDC,UAAU,EAAE;MACVJ,OAAO,EAAE,gDAAgD;MACzDK,UAAU,EAAE,IAAI;MAChBF,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS;IAC3B,CAAC;IACDG,IAAI,EAAE,kCAAkC;IACxCC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}