{"ast": null, "code": "'use strict';\n\nmodule.exports = nasm;\nnasm.displayName = 'nasm';\nnasm.aliases = [];\nfunction nasm(Prism) {\n  Prism.languages.nasm = {\n    comment: /;.*$/m,\n    string: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    label: {\n      pattern: /(^\\s*)[A-Za-z._?$][\\w.?$@~#]*:/m,\n      lookbehind: true,\n      alias: 'function'\n    },\n    keyword: [/\\[?BITS (?:16|32|64)\\]?/, {\n      pattern: /(^\\s*)section\\s*[a-z.]+:?/im,\n      lookbehind: true\n    }, /(?:extern|global)[^;\\r\\n]*/i, /(?:CPU|DEFAULT|FLOAT).*$/m],\n    register: {\n      pattern: /\\b(?:st\\d|[xyz]mm\\d\\d?|[cdt]r\\d|r\\d\\d?[bwd]?|[er]?[abcd]x|[abcd][hl]|[er]?(?:bp|di|si|sp)|[cdefgs]s)\\b/i,\n      alias: 'variable'\n    },\n    number: /(?:\\b|(?=\\$))(?:0[hx](?:\\.[\\da-f]+|[\\da-f]+(?:\\.[\\da-f]+)?)(?:p[+-]?\\d+)?|\\d[\\da-f]+[hx]|\\$\\d[\\da-f]*|0[oq][0-7]+|[0-7]+[oq]|0[by][01]+|[01]+[by]|0[dt]\\d+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:\\.?e[+-]?\\d+)?[dt]?)\\b/i,\n    operator: /[\\[\\]*+\\-\\/%<>=&|$!]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "nasm", "displayName", "aliases", "Prism", "languages", "comment", "string", "label", "pattern", "lookbehind", "alias", "keyword", "register", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/nasm.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nasm\nnasm.displayName = 'nasm'\nnasm.aliases = []\nfunction nasm(Prism) {\n  Prism.languages.nasm = {\n    comment: /;.*$/m,\n    string: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    label: {\n      pattern: /(^\\s*)[A-Za-z._?$][\\w.?$@~#]*:/m,\n      lookbehind: true,\n      alias: 'function'\n    },\n    keyword: [\n      /\\[?BITS (?:16|32|64)\\]?/,\n      {\n        pattern: /(^\\s*)section\\s*[a-z.]+:?/im,\n        lookbehind: true\n      },\n      /(?:extern|global)[^;\\r\\n]*/i,\n      /(?:CPU|DEFAULT|FLOAT).*$/m\n    ],\n    register: {\n      pattern:\n        /\\b(?:st\\d|[xyz]mm\\d\\d?|[cdt]r\\d|r\\d\\d?[bwd]?|[er]?[abcd]x|[abcd][hl]|[er]?(?:bp|di|si|sp)|[cdefgs]s)\\b/i,\n      alias: 'variable'\n    },\n    number:\n      /(?:\\b|(?=\\$))(?:0[hx](?:\\.[\\da-f]+|[\\da-f]+(?:\\.[\\da-f]+)?)(?:p[+-]?\\d+)?|\\d[\\da-f]+[hx]|\\$\\d[\\da-f]*|0[oq][0-7]+|[0-7]+[oq]|0[by][01]+|[01]+[by]|0[dt]\\d+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:\\.?e[+-]?\\d+)?[dt]?)\\b/i,\n    operator: /[\\[\\]*+\\-\\/%<>=&|$!]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,OAAO,EAAE,OAAO;IAChBC,MAAM,EAAE,mCAAmC;IAC3CC,KAAK,EAAE;MACLC,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE,CACP,yBAAyB,EACzB;MACEH,OAAO,EAAE,6BAA6B;MACtCC,UAAU,EAAE;IACd,CAAC,EACD,6BAA6B,EAC7B,2BAA2B,CAC5B;IACDG,QAAQ,EAAE;MACRJ,OAAO,EACL,yGAAyG;MAC3GE,KAAK,EAAE;IACT,CAAC;IACDG,MAAM,EACJ,8MAA8M;IAChNC,QAAQ,EAAE;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}