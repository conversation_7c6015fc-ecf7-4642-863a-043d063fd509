{"ast": null, "code": "import highlight from './highlight';\nimport refractor from 'refractor/core';\nvar SyntaxHighlighter = highlight(refractor, {});\nSyntaxHighlighter.registerLanguage = function (_, language) {\n  return refractor.register(language);\n};\nSyntaxHighlighter.alias = function (name, aliases) {\n  return refractor.alias(name, aliases);\n};\nexport default SyntaxHighlighter;", "map": {"version": 3, "names": ["highlight", "refractor", "Syntax<PERSON><PERSON><PERSON><PERSON>", "registerLanguage", "_", "language", "register", "alias", "name", "aliases"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/prism-light.js"], "sourcesContent": ["import highlight from './highlight';\nimport refractor from 'refractor/core';\nvar SyntaxHighlighter = highlight(refractor, {});\nSyntaxHighlighter.registerLanguage = function (_, language) {\n  return refractor.register(language);\n};\nSyntaxHighlighter.alias = function (name, aliases) {\n  return refractor.alias(name, aliases);\n};\nexport default SyntaxHighlighter;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,IAAIC,iBAAiB,GAAGF,SAAS,CAACC,SAAS,EAAE,CAAC,CAAC,CAAC;AAChDC,iBAAiB,CAACC,gBAAgB,GAAG,UAAUC,CAAC,EAAEC,QAAQ,EAAE;EAC1D,OAAOJ,SAAS,CAACK,QAAQ,CAACD,QAAQ,CAAC;AACrC,CAAC;AACDH,iBAAiB,CAACK,KAAK,GAAG,UAAUC,IAAI,EAAEC,OAAO,EAAE;EACjD,OAAOR,SAAS,CAACM,KAAK,CAACC,IAAI,EAAEC,OAAO,CAAC;AACvC,CAAC;AACD,eAAeP,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}