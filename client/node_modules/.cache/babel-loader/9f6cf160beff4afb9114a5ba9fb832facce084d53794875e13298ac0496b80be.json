{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: HTTP\nDescription: HTTP request and response headers with automatic body highlighting\nAuthor: <PERSON> <<EMAIL>>\nCategory: common, protocols\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/Overview\n*/\n\nfunction http(hljs) {\n  const VERSION = 'HTTP/(2|1\\\\.[01])';\n  const HEADER_NAME = /[A-Za-z][A-Za-z0-9-]*/;\n  const HEADER = {\n    className: 'attribute',\n    begin: concat('^', HEADER_NAME, '(?=\\\\:\\\\s)'),\n    starts: {\n      contains: [{\n        className: \"punctuation\",\n        begin: /: /,\n        relevance: 0,\n        starts: {\n          end: '$',\n          relevance: 0\n        }\n      }]\n    }\n  };\n  const HEADERS_AND_BODY = [HEADER, {\n    begin: '\\\\n\\\\n',\n    starts: {\n      subLanguage: [],\n      endsWithParent: true\n    }\n  }];\n  return {\n    name: 'HTTP',\n    aliases: ['https'],\n    illegal: /\\S/,\n    contains: [\n    // response\n    {\n      begin: '^(?=' + VERSION + \" \\\\d{3})\",\n      end: /$/,\n      contains: [{\n        className: \"meta\",\n        begin: VERSION\n      }, {\n        className: 'number',\n        begin: '\\\\b\\\\d{3}\\\\b'\n      }],\n      starts: {\n        end: /\\b\\B/,\n        illegal: /\\S/,\n        contains: HEADERS_AND_BODY\n      }\n    },\n    // request\n    {\n      begin: '(?=^[A-Z]+ (.*?) ' + VERSION + '$)',\n      end: /$/,\n      contains: [{\n        className: 'string',\n        begin: ' ',\n        end: ' ',\n        excludeBegin: true,\n        excludeEnd: true\n      }, {\n        className: \"meta\",\n        begin: VERSION\n      }, {\n        className: 'keyword',\n        begin: '[A-Z]+'\n      }],\n      starts: {\n        end: /\\b\\B/,\n        illegal: /\\S/,\n        contains: HEADERS_AND_BODY\n      }\n    },\n    // to allow headers to work even without a preamble\n    hljs.inherit(HEADER, {\n      relevance: 0\n    })]\n  };\n}\nmodule.exports = http;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "http", "hljs", "VERSION", "HEADER_NAME", "HEADER", "className", "begin", "starts", "contains", "relevance", "end", "HEADERS_AND_BODY", "subLanguage", "endsWithParent", "name", "aliases", "illegal", "excludeBegin", "excludeEnd", "inherit", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/http.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: HTTP\nDescription: HTTP request and response headers with automatic body highlighting\nAuthor: <PERSON> <<EMAIL>>\nCategory: common, protocols\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/Overview\n*/\n\nfunction http(hljs) {\n  const VERSION = 'HTTP/(2|1\\\\.[01])';\n  const HEADER_NAME = /[A-Za-z][A-Za-z0-9-]*/;\n  const HEADER = {\n    className: 'attribute',\n    begin: concat('^', HEADER_NAME, '(?=\\\\:\\\\s)'),\n    starts: {\n      contains: [\n        {\n          className: \"punctuation\",\n          begin: /: /,\n          relevance: 0,\n          starts: {\n            end: '$',\n            relevance: 0\n          }\n        }\n      ]\n    }\n  };\n  const HEADERS_AND_BODY = [\n    HEADER,\n    {\n      begin: '\\\\n\\\\n',\n      starts: { subLanguage: [], endsWithParent: true }\n    }\n  ];\n\n  return {\n    name: 'HTTP',\n    aliases: ['https'],\n    illegal: /\\S/,\n    contains: [\n      // response\n      {\n        begin: '^(?=' + VERSION + \" \\\\d{3})\",\n        end: /$/,\n        contains: [\n          {\n            className: \"meta\",\n            begin: VERSION\n          },\n          {\n            className: 'number', begin: '\\\\b\\\\d{3}\\\\b'\n          }\n        ],\n        starts: {\n          end: /\\b\\B/,\n          illegal: /\\S/,\n          contains: HEADERS_AND_BODY\n        }\n      },\n      // request\n      {\n        begin: '(?=^[A-Z]+ (.*?) ' + VERSION + '$)',\n        end: /$/,\n        contains: [\n          {\n            className: 'string',\n            begin: ' ',\n            end: ' ',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: \"meta\",\n            begin: VERSION\n          },\n          {\n            className: 'keyword',\n            begin: '[A-Z]+'\n          }\n        ],\n        starts: {\n          end: /\\b\\B/,\n          illegal: /\\S/,\n          contains: HEADERS_AND_BODY\n        }\n      },\n      // to allow headers to work even without a preamble\n      hljs.inherit(HEADER, {\n        relevance: 0\n      })\n    ]\n  };\n}\n\nmodule.exports = http;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,OAAO,GAAG,mBAAmB;EACnC,MAAMC,WAAW,GAAG,uBAAuB;EAC3C,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAEZ,MAAM,CAAC,GAAG,EAAES,WAAW,EAAE,YAAY,CAAC;IAC7CI,MAAM,EAAE;MACNC,QAAQ,EAAE,CACR;QACEH,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,IAAI;QACXG,SAAS,EAAE,CAAC;QACZF,MAAM,EAAE;UACNG,GAAG,EAAE,GAAG;UACRD,SAAS,EAAE;QACb;MACF,CAAC;IAEL;EACF,CAAC;EACD,MAAME,gBAAgB,GAAG,CACvBP,MAAM,EACN;IACEE,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE;MAAEK,WAAW,EAAE,EAAE;MAAEC,cAAc,EAAE;IAAK;EAClD,CAAC,CACF;EAED,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBC,OAAO,EAAE,IAAI;IACbR,QAAQ,EAAE;IACR;IACA;MACEF,KAAK,EAAE,MAAM,GAAGJ,OAAO,GAAG,UAAU;MACpCQ,GAAG,EAAE,GAAG;MACRF,QAAQ,EAAE,CACR;QACEH,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAEJ;MACT,CAAC,EACD;QACEG,SAAS,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAC9B,CAAC,CACF;MACDC,MAAM,EAAE;QACNG,GAAG,EAAE,MAAM;QACXM,OAAO,EAAE,IAAI;QACbR,QAAQ,EAAEG;MACZ;IACF,CAAC;IACD;IACA;MACEL,KAAK,EAAE,mBAAmB,GAAGJ,OAAO,GAAG,IAAI;MAC3CQ,GAAG,EAAE,GAAG;MACRF,QAAQ,EAAE,CACR;QACEH,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,GAAG;QACVI,GAAG,EAAE,GAAG;QACRO,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEb,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAEJ;MACT,CAAC,EACD;QACEG,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE;MACT,CAAC,CACF;MACDC,MAAM,EAAE;QACNG,GAAG,EAAE,MAAM;QACXM,OAAO,EAAE,IAAI;QACbR,QAAQ,EAAEG;MACZ;IACF,CAAC;IACD;IACAV,IAAI,CAACkB,OAAO,CAACf,MAAM,EAAE;MACnBK,SAAS,EAAE;IACb,CAAC,CAAC;EAEN,CAAC;AACH;AAEAW,MAAM,CAACC,OAAO,GAAGrB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}