{"ast": null, "code": "'use strict';\n\nmodule.exports = uri;\nuri.displayName = 'uri';\nuri.aliases = ['url'];\nfunction uri(Prism) {\n  // https://tools.ietf.org/html/rfc3986#appendix-A\n  Prism.languages.uri = {\n    scheme: {\n      pattern: /^[a-z][a-z0-9+.-]*:/im,\n      greedy: true,\n      inside: {\n        'scheme-delimiter': /:$/\n      }\n    },\n    fragment: {\n      pattern: /#[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'fragment-delimiter': /^#/\n      }\n    },\n    query: {\n      pattern: /\\?[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'query-delimiter': {\n          pattern: /^\\?/,\n          greedy: true\n        },\n        'pair-delimiter': /[&;]/,\n        pair: {\n          pattern: /^[^=][\\s\\S]*/,\n          inside: {\n            key: /^[^=]+/,\n            value: {\n              pattern: /(^=)[\\s\\S]+/,\n              lookbehind: true\n            }\n          }\n        }\n      }\n    },\n    authority: {\n      pattern: RegExp(/^\\/\\//.source +\n      // [ userinfo \"@\" ]\n      /(?:[\\w\\-.~!$&'()*+,;=%:]*@)?/.source + (\n      // host\n      '(?:' +\n      // IP-literal\n      /\\[(?:[0-9a-fA-F:.]{2,48}|v[0-9a-fA-F]+\\.[\\w\\-.~!$&'()*+,;=]+)\\]/.source + '|' +\n      // IPv4address or registered name\n      /[\\w\\-.~!$&'()*+,;=%]*/.source + ')') +\n      // [ \":\" port ]\n      /(?::\\d*)?/.source, 'm'),\n      inside: {\n        'authority-delimiter': /^\\/\\//,\n        'user-info-segment': {\n          pattern: /^[\\w\\-.~!$&'()*+,;=%:]*@/,\n          inside: {\n            'user-info-delimiter': /@$/,\n            'user-info': /^[\\w\\-.~!$&'()*+,;=%:]+/\n          }\n        },\n        'port-segment': {\n          pattern: /:\\d*$/,\n          inside: {\n            'port-delimiter': /^:/,\n            port: /^\\d+/\n          }\n        },\n        host: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            'ip-literal': {\n              pattern: /^\\[[\\s\\S]+\\]$/,\n              inside: {\n                'ip-literal-delimiter': /^\\[|\\]$/,\n                'ipv-future': /^v[\\s\\S]+/,\n                'ipv6-address': /^[\\s\\S]+/\n              }\n            },\n            'ipv4-address': /^(?:(?:[03-9]\\d?|[12]\\d{0,2})\\.){3}(?:[03-9]\\d?|[12]\\d{0,2})$/\n          }\n        }\n      }\n    },\n    path: {\n      pattern: /^[\\w\\-.~!$&'()*+,;=%:@/]+/m,\n      inside: {\n        'path-separator': /\\//\n      }\n    }\n  };\n  Prism.languages.url = Prism.languages.uri;\n}", "map": {"version": 3, "names": ["module", "exports", "uri", "displayName", "aliases", "Prism", "languages", "scheme", "pattern", "greedy", "inside", "fragment", "query", "pair", "key", "value", "lookbehind", "authority", "RegExp", "source", "port", "host", "path", "url"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/uri.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = uri\nuri.displayName = 'uri'\nuri.aliases = ['url']\nfunction uri(Prism) {\n  // https://tools.ietf.org/html/rfc3986#appendix-A\n  Prism.languages.uri = {\n    scheme: {\n      pattern: /^[a-z][a-z0-9+.-]*:/im,\n      greedy: true,\n      inside: {\n        'scheme-delimiter': /:$/\n      }\n    },\n    fragment: {\n      pattern: /#[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'fragment-delimiter': /^#/\n      }\n    },\n    query: {\n      pattern: /\\?[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'query-delimiter': {\n          pattern: /^\\?/,\n          greedy: true\n        },\n        'pair-delimiter': /[&;]/,\n        pair: {\n          pattern: /^[^=][\\s\\S]*/,\n          inside: {\n            key: /^[^=]+/,\n            value: {\n              pattern: /(^=)[\\s\\S]+/,\n              lookbehind: true\n            }\n          }\n        }\n      }\n    },\n    authority: {\n      pattern: RegExp(\n        /^\\/\\//.source + // [ userinfo \"@\" ]\n          /(?:[\\w\\-.~!$&'()*+,;=%:]*@)?/.source + // host\n          ('(?:' + // IP-literal\n            /\\[(?:[0-9a-fA-F:.]{2,48}|v[0-9a-fA-F]+\\.[\\w\\-.~!$&'()*+,;=]+)\\]/\n              .source +\n            '|' + // IPv4address or registered name\n            /[\\w\\-.~!$&'()*+,;=%]*/.source +\n            ')') + // [ \":\" port ]\n          /(?::\\d*)?/.source,\n        'm'\n      ),\n      inside: {\n        'authority-delimiter': /^\\/\\//,\n        'user-info-segment': {\n          pattern: /^[\\w\\-.~!$&'()*+,;=%:]*@/,\n          inside: {\n            'user-info-delimiter': /@$/,\n            'user-info': /^[\\w\\-.~!$&'()*+,;=%:]+/\n          }\n        },\n        'port-segment': {\n          pattern: /:\\d*$/,\n          inside: {\n            'port-delimiter': /^:/,\n            port: /^\\d+/\n          }\n        },\n        host: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            'ip-literal': {\n              pattern: /^\\[[\\s\\S]+\\]$/,\n              inside: {\n                'ip-literal-delimiter': /^\\[|\\]$/,\n                'ipv-future': /^v[\\s\\S]+/,\n                'ipv6-address': /^[\\s\\S]+/\n              }\n            },\n            'ipv4-address':\n              /^(?:(?:[03-9]\\d?|[12]\\d{0,2})\\.){3}(?:[03-9]\\d?|[12]\\d{0,2})$/\n          }\n        }\n      }\n    },\n    path: {\n      pattern: /^[\\w\\-.~!$&'()*+,;=%:@/]+/m,\n      inside: {\n        'path-separator': /\\//\n      }\n    }\n  }\n  Prism.languages.url = Prism.languages.uri\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AACrB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EACAA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,MAAM,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACN,kBAAkB,EAAE;MACtB;IACF,CAAC;IACDC,QAAQ,EAAE;MACRH,OAAO,EAAE,4BAA4B;MACrCE,MAAM,EAAE;QACN,oBAAoB,EAAE;MACxB;IACF,CAAC;IACDE,KAAK,EAAE;MACLJ,OAAO,EAAE,6BAA6B;MACtCE,MAAM,EAAE;QACN,iBAAiB,EAAE;UACjBF,OAAO,EAAE,KAAK;UACdC,MAAM,EAAE;QACV,CAAC;QACD,gBAAgB,EAAE,MAAM;QACxBI,IAAI,EAAE;UACJL,OAAO,EAAE,cAAc;UACvBE,MAAM,EAAE;YACNI,GAAG,EAAE,QAAQ;YACbC,KAAK,EAAE;cACLP,OAAO,EAAE,aAAa;cACtBQ,UAAU,EAAE;YACd;UACF;QACF;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTT,OAAO,EAAEU,MAAM,CACb,OAAO,CAACC,MAAM;MAAG;MACf,8BAA8B,CAACA,MAAM;MAAG;MACvC,KAAK;MAAG;MACP,iEAAiE,CAC9DA,MAAM,GACT,GAAG;MAAG;MACN,uBAAuB,CAACA,MAAM,GAC9B,GAAG,CAAC;MAAG;MACT,WAAW,CAACA,MAAM,EACpB,GACF,CAAC;MACDT,MAAM,EAAE;QACN,qBAAqB,EAAE,OAAO;QAC9B,mBAAmB,EAAE;UACnBF,OAAO,EAAE,0BAA0B;UACnCE,MAAM,EAAE;YACN,qBAAqB,EAAE,IAAI;YAC3B,WAAW,EAAE;UACf;QACF,CAAC;QACD,cAAc,EAAE;UACdF,OAAO,EAAE,OAAO;UAChBE,MAAM,EAAE;YACN,gBAAgB,EAAE,IAAI;YACtBU,IAAI,EAAE;UACR;QACF,CAAC;QACDC,IAAI,EAAE;UACJb,OAAO,EAAE,SAAS;UAClBE,MAAM,EAAE;YACN,YAAY,EAAE;cACZF,OAAO,EAAE,eAAe;cACxBE,MAAM,EAAE;gBACN,sBAAsB,EAAE,SAAS;gBACjC,YAAY,EAAE,WAAW;gBACzB,cAAc,EAAE;cAClB;YACF,CAAC;YACD,cAAc,EACZ;UACJ;QACF;MACF;IACF,CAAC;IACDY,IAAI,EAAE;MACJd,OAAO,EAAE,4BAA4B;MACrCE,MAAM,EAAE;QACN,gBAAgB,EAAE;MACpB;IACF;EACF,CAAC;EACDL,KAAK,CAACC,SAAS,CAACiB,GAAG,GAAGlB,KAAK,CAACC,SAAS,CAACJ,GAAG;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}