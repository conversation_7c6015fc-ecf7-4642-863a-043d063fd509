{"ast": null, "code": "/*\nLanguage: Python profiler\nDescription: Python profiler results\nAuthor: <PERSON> <<EMAIL>>\n*/\n\nfunction profile(hljs) {\n  return {\n    name: 'Python profiler',\n    contains: [hljs.C_NUMBER_MODE, {\n      begin: '[a-zA-Z_][\\\\da-zA-Z_]+\\\\.[\\\\da-zA-Z_]{1,3}',\n      end: ':',\n      excludeEnd: true\n    }, {\n      begin: '(ncalls|tottime|cumtime)',\n      end: '$',\n      keywords: 'ncalls tottime|10 cumtime|10 filename',\n      relevance: 10\n    }, {\n      begin: 'function calls',\n      end: '$',\n      contains: [hljs.C_NUMBER_MODE],\n      relevance: 10\n    }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n      className: 'string',\n      begin: '\\\\(',\n      end: '\\\\)$',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    }]\n  };\n}\nmodule.exports = profile;", "map": {"version": 3, "names": ["profile", "hljs", "name", "contains", "C_NUMBER_MODE", "begin", "end", "excludeEnd", "keywords", "relevance", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "className", "excludeBegin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/profile.js"], "sourcesContent": ["/*\nLanguage: Python profiler\nDescription: Python profiler results\nAuthor: <PERSON> <<EMAIL>>\n*/\n\nfunction profile(hljs) {\n  return {\n    name: 'Python profiler',\n    contains: [\n      hljs.C_NUMBER_MODE,\n      {\n        begin: '[a-zA-Z_][\\\\da-zA-Z_]+\\\\.[\\\\da-zA-Z_]{1,3}',\n        end: ':',\n        excludeEnd: true\n      },\n      {\n        begin: '(ncalls|tottime|cumtime)',\n        end: '$',\n        keywords: 'ncalls tottime|10 cumtime|10 filename',\n        relevance: 10\n      },\n      {\n        begin: 'function calls',\n        end: '$',\n        contains: [ hljs.C_NUMBER_MODE ],\n        relevance: 10\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: '\\\\(',\n        end: '\\\\)$',\n        excludeBegin: true,\n        excludeEnd: true,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = profile;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,OAAO;IACLC,IAAI,EAAE,iBAAiB;IACvBC,QAAQ,EAAE,CACRF,IAAI,CAACG,aAAa,EAClB;MACEC,KAAK,EAAE,4CAA4C;MACnDC,GAAG,EAAE,GAAG;MACRC,UAAU,EAAE;IACd,CAAC,EACD;MACEF,KAAK,EAAE,0BAA0B;MACjCC,GAAG,EAAE,GAAG;MACRE,QAAQ,EAAE,uCAAuC;MACjDC,SAAS,EAAE;IACb,CAAC,EACD;MACEJ,KAAK,EAAE,gBAAgB;MACvBC,GAAG,EAAE,GAAG;MACRH,QAAQ,EAAE,CAAEF,IAAI,CAACG,aAAa,CAAE;MAChCK,SAAS,EAAE;IACb,CAAC,EACDR,IAAI,CAACS,gBAAgB,EACrBT,IAAI,CAACU,iBAAiB,EACtB;MACEC,SAAS,EAAE,QAAQ;MACnBP,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,MAAM;MACXO,YAAY,EAAE,IAAI;MAClBN,UAAU,EAAE,IAAI;MAChBE,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGf,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}