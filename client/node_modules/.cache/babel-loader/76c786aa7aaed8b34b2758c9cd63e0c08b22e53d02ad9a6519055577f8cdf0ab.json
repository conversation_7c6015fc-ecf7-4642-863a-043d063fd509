{"ast": null, "code": "'use strict';\n\nmodule.exports = brainfuck;\nbrainfuck.displayName = 'brainfuck';\nbrainfuck.aliases = [];\nfunction brainfuck(Prism) {\n  Prism.languages.brainfuck = {\n    pointer: {\n      pattern: /<|>/,\n      alias: 'keyword'\n    },\n    increment: {\n      pattern: /\\+/,\n      alias: 'inserted'\n    },\n    decrement: {\n      pattern: /-/,\n      alias: 'deleted'\n    },\n    branching: {\n      pattern: /\\[|\\]/,\n      alias: 'important'\n    },\n    operator: /[.,]/,\n    comment: /\\S+/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "brainfuck", "displayName", "aliases", "Prism", "languages", "pointer", "pattern", "alias", "increment", "decrement", "branching", "operator", "comment"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/brainfuck.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = brainfuck\nbrainfuck.displayName = 'brainfuck'\nbrainfuck.aliases = []\nfunction brainfuck(Prism) {\n  Prism.languages.brainfuck = {\n    pointer: {\n      pattern: /<|>/,\n      alias: 'keyword'\n    },\n    increment: {\n      pattern: /\\+/,\n      alias: 'inserted'\n    },\n    decrement: {\n      pattern: /-/,\n      alias: 'deleted'\n    },\n    branching: {\n      pattern: /\\[|\\]/,\n      alias: 'important'\n    },\n    operator: /[.,]/,\n    comment: /\\S+/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxBA,KAAK,CAACC,SAAS,CAACJ,SAAS,GAAG;IAC1BK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,SAAS,EAAE;MACTF,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACT,CAAC;IACDE,SAAS,EAAE;MACTH,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE;IACT,CAAC;IACDG,SAAS,EAAE;MACTJ,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE;IACT,CAAC;IACDI,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}