{"ast": null, "code": "import highlight from './highlight';\nimport defaultStyle from './styles/prism/prism';\nimport refractor from 'refractor';\nimport supportedLanguages from './languages/prism/supported-languages';\nvar highlighter = highlight(refractor, defaultStyle);\nhighlighter.supportedLanguages = supportedLanguages;\nexport default highlighter;", "map": {"version": 3, "names": ["highlight", "defaultStyle", "refractor", "supportedLanguages", "highlighter"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/prism.js"], "sourcesContent": ["import highlight from './highlight';\nimport defaultStyle from './styles/prism/prism';\nimport refractor from 'refractor';\nimport supportedLanguages from './languages/prism/supported-languages';\nvar highlighter = highlight(refractor, defaultStyle);\nhighlighter.supportedLanguages = supportedLanguages;\nexport default highlighter;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,IAAIC,WAAW,GAAGJ,SAAS,CAACE,SAAS,EAAED,YAAY,CAAC;AACpDG,WAAW,CAACD,kBAAkB,GAAGA,kBAAkB;AACnD,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}