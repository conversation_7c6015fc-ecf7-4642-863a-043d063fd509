{"ast": null, "code": "/*\nLanguage: Prolog\nDescription: Prolog is a general purpose logic programming language associated with artificial intelligence and computational linguistics.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Prolog\n*/\n\nfunction prolog(hljs) {\n  const ATOM = {\n    begin: /[a-z][A-Za-z0-9_]*/,\n    relevance: 0\n  };\n  const VAR = {\n    className: 'symbol',\n    variants: [{\n      begin: /[A-Z][a-zA-Z0-9_]*/\n    }, {\n      begin: /_[A-Za-z0-9_]*/\n    }],\n    relevance: 0\n  };\n  const PARENTED = {\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0\n  };\n  const LIST = {\n    begin: /\\[/,\n    end: /\\]/\n  };\n  const LINE_COMMENT = {\n    className: 'comment',\n    begin: /%/,\n    end: /$/,\n    contains: [hljs.PHRASAL_WORDS_MODE]\n  };\n  const BACKTICK_STRING = {\n    className: 'string',\n    begin: /`/,\n    end: /`/,\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const CHAR_CODE = {\n    className: 'string',\n    // 0'a etc.\n    begin: /0'(\\\\'|.)/\n  };\n  const SPACE_CODE = {\n    className: 'string',\n    begin: /0'\\\\s/ // 0'\\s\n  };\n  const PRED_OP = {\n    // relevance booster\n    begin: /:-/\n  };\n  const inner = [ATOM, VAR, PARENTED, PRED_OP, LIST, LINE_COMMENT, hljs.C_BLOCK_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, BACKTICK_STRING, CHAR_CODE, SPACE_CODE, hljs.C_NUMBER_MODE];\n  PARENTED.contains = inner;\n  LIST.contains = inner;\n  return {\n    name: 'Prolog',\n    contains: inner.concat([{\n      // relevance booster\n      begin: /\\.$/\n    }])\n  };\n}\nmodule.exports = prolog;", "map": {"version": 3, "names": ["prolog", "hljs", "ATOM", "begin", "relevance", "VAR", "className", "variants", "PARENTED", "end", "LIST", "LINE_COMMENT", "contains", "PHRASAL_WORDS_MODE", "BACKTICK_STRING", "BACKSLASH_ESCAPE", "CHAR_CODE", "SPACE_CODE", "PRED_OP", "inner", "C_BLOCK_COMMENT_MODE", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "C_NUMBER_MODE", "name", "concat", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/prolog.js"], "sourcesContent": ["/*\nLanguage: Prolog\nDescription: Prolog is a general purpose logic programming language associated with artificial intelligence and computational linguistics.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Prolog\n*/\n\nfunction prolog(hljs) {\n  const ATOM = {\n\n    begin: /[a-z][A-Za-z0-9_]*/,\n    relevance: 0\n  };\n\n  const VAR = {\n\n    className: 'symbol',\n    variants: [\n      {\n        begin: /[A-Z][a-zA-Z0-9_]*/\n      },\n      {\n        begin: /_[A-Za-z0-9_]*/\n      }\n    ],\n    relevance: 0\n  };\n\n  const PARENTED = {\n\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0\n  };\n\n  const LIST = {\n\n    begin: /\\[/,\n    end: /\\]/\n  };\n\n  const LINE_COMMENT = {\n\n    className: 'comment',\n    begin: /%/,\n    end: /$/,\n    contains: [ hljs.PHRASAL_WORDS_MODE ]\n  };\n\n  const BACKTICK_STRING = {\n\n    className: 'string',\n    begin: /`/,\n    end: /`/,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n\n  const CHAR_CODE = {\n    className: 'string', // 0'a etc.\n    begin: /0'(\\\\'|.)/\n  };\n\n  const SPACE_CODE = {\n    className: 'string',\n    begin: /0'\\\\s/ // 0'\\s\n  };\n\n  const PRED_OP = { // relevance booster\n    begin: /:-/\n  };\n\n  const inner = [\n\n    ATOM,\n    VAR,\n    PARENTED,\n    PRED_OP,\n    LIST,\n    LINE_COMMENT,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.QUOTE_STRING_MODE,\n    hljs.APOS_STRING_MODE,\n    BACKTICK_STRING,\n    CHAR_CODE,\n    SPACE_CODE,\n    hljs.C_NUMBER_MODE\n  ];\n\n  PARENTED.contains = inner;\n  LIST.contains = inner;\n\n  return {\n    name: 'Prolog',\n    contains: inner.concat([\n      { // relevance booster\n        begin: /\\.$/\n      }\n    ])\n  };\n}\n\nmodule.exports = prolog;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,IAAI,GAAG;IAEXC,KAAK,EAAE,oBAAoB;IAC3BC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,GAAG,GAAG;IAEVC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MACEJ,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,CACF;IACDC,SAAS,EAAE;EACb,CAAC;EAED,MAAMI,QAAQ,GAAG;IAEfL,KAAK,EAAE,IAAI;IACXM,GAAG,EAAE,IAAI;IACTL,SAAS,EAAE;EACb,CAAC;EAED,MAAMM,IAAI,GAAG;IAEXP,KAAK,EAAE,IAAI;IACXM,GAAG,EAAE;EACP,CAAC;EAED,MAAME,YAAY,GAAG;IAEnBL,SAAS,EAAE,SAAS;IACpBH,KAAK,EAAE,GAAG;IACVM,GAAG,EAAE,GAAG;IACRG,QAAQ,EAAE,CAAEX,IAAI,CAACY,kBAAkB;EACrC,CAAC;EAED,MAAMC,eAAe,GAAG;IAEtBR,SAAS,EAAE,QAAQ;IACnBH,KAAK,EAAE,GAAG;IACVM,GAAG,EAAE,GAAG;IACRG,QAAQ,EAAE,CAAEX,IAAI,CAACc,gBAAgB;EACnC,CAAC;EAED,MAAMC,SAAS,GAAG;IAChBV,SAAS,EAAE,QAAQ;IAAE;IACrBH,KAAK,EAAE;EACT,CAAC;EAED,MAAMc,UAAU,GAAG;IACjBX,SAAS,EAAE,QAAQ;IACnBH,KAAK,EAAE,OAAO,CAAC;EACjB,CAAC;EAED,MAAMe,OAAO,GAAG;IAAE;IAChBf,KAAK,EAAE;EACT,CAAC;EAED,MAAMgB,KAAK,GAAG,CAEZjB,IAAI,EACJG,GAAG,EACHG,QAAQ,EACRU,OAAO,EACPR,IAAI,EACJC,YAAY,EACZV,IAAI,CAACmB,oBAAoB,EACzBnB,IAAI,CAACoB,iBAAiB,EACtBpB,IAAI,CAACqB,gBAAgB,EACrBR,eAAe,EACfE,SAAS,EACTC,UAAU,EACVhB,IAAI,CAACsB,aAAa,CACnB;EAEDf,QAAQ,CAACI,QAAQ,GAAGO,KAAK;EACzBT,IAAI,CAACE,QAAQ,GAAGO,KAAK;EAErB,OAAO;IACLK,IAAI,EAAE,QAAQ;IACdZ,QAAQ,EAAEO,KAAK,CAACM,MAAM,CAAC,CACrB;MAAE;MACAtB,KAAK,EAAE;IACT,CAAC,CACF;EACH,CAAC;AACH;AAEAuB,MAAM,CAACC,OAAO,GAAG3B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}