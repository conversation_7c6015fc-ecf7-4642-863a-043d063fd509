{"ast": null, "code": "import createAsyncLoading<PERSON>ighlighter from './async-syntax-highlighter';\nimport supportedLanguages from './languages/prism/supported-languages';\nexport default createAsyncLoadingHighlighter({\n  loader: function loader() {\n    return import(/* webpackChunkName:\"react-syntax-highlighter/refractor-import\" */\n    'refractor').then(function (module) {\n      // Webpack 3 returns module.exports as default as module, but webpack 4 returns module.exports as module.default\n      return module[\"default\"] || module;\n    });\n  },\n  noAsyncLoadingLanguages: true,\n  supportedLanguages: supportedLanguages\n});", "map": {"version": 3, "names": ["createAsyncLoadingHighlighter", "supportedLanguages", "loader", "then", "module", "noAsyncLoadingLanguages"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/prism-async.js"], "sourcesContent": ["import createAsyncLoading<PERSON>ighlighter from './async-syntax-highlighter';\nimport supportedLanguages from './languages/prism/supported-languages';\nexport default createAsyncLoadingHighlighter({\n  loader: function loader() {\n    return import( /* webpackChunkName:\"react-syntax-highlighter/refractor-import\" */\n    'refractor').then(function (module) {\n      // Webpack 3 returns module.exports as default as module, but webpack 4 returns module.exports as module.default\n      return module[\"default\"] || module;\n    });\n  },\n  noAsyncLoadingLanguages: true,\n  supportedLanguages: supportedLanguages\n});"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,4BAA4B;AACtE,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,eAAeD,6BAA6B,CAAC;EAC3CE,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO,MAAM,CAAE;IACf,WAAW,CAAC,CAACC,IAAI,CAAC,UAAUC,MAAM,EAAE;MAClC;MACA,OAAOA,MAAM,CAAC,SAAS,CAAC,IAAIA,MAAM;IACpC,CAAC,CAAC;EACJ,CAAC;EACDC,uBAAuB,EAAE,IAAI;EAC7BJ,kBAAkB,EAAEA;AACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}