{"ast": null, "code": "'use strict';\n\nvar refractorScheme = require('./scheme.js');\nmodule.exports = lilypond;\nlilypond.displayName = 'lilypond';\nlilypond.aliases = [];\nfunction lilypond(Prism) {\n  Prism.register(refractorScheme);\n  (function (Prism) {\n    var schemeExpression = /\\((?:[^();\"#\\\\]|\\\\[\\s\\S]|;.*(?!.)|\"(?:[^\"\\\\]|\\\\.)*\"|#(?:\\{(?:(?!#\\})[\\s\\S])*#\\}|[^{])|<expr>)*\\)/.source; // allow for up to pow(2, recursivenessLog2) many levels of recursive brace expressions\n    // For some reason, this can't be 4\n    var recursivenessLog2 = 5;\n    for (var i = 0; i < recursivenessLog2; i++) {\n      schemeExpression = schemeExpression.replace(/<expr>/g, function () {\n        return schemeExpression;\n      });\n    }\n    schemeExpression = schemeExpression.replace(/<expr>/g, /[^\\s\\S]/.source);\n    var lilypond = Prism.languages.lilypond = {\n      comment: /%(?:(?!\\{).*|\\{[\\s\\S]*?%\\})/,\n      'embedded-scheme': {\n        pattern: RegExp(/(^|[=\\s])#(?:\"(?:[^\"\\\\]|\\\\.)*\"|[^\\s()\"]*(?:[^\\s()]|<expr>))/.source.replace(/<expr>/g, function () {\n          return schemeExpression;\n        }), 'm'),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          scheme: {\n            pattern: /^(#)[\\s\\S]+$/,\n            lookbehind: true,\n            alias: 'language-scheme',\n            inside: {\n              'embedded-lilypond': {\n                pattern: /#\\{[\\s\\S]*?#\\}/,\n                greedy: true,\n                inside: {\n                  punctuation: /^#\\{|#\\}$/,\n                  lilypond: {\n                    pattern: /[\\s\\S]+/,\n                    alias: 'language-lilypond',\n                    inside: null // see below\n                  }\n                }\n              },\n              rest: Prism.languages.scheme\n            }\n          },\n          punctuation: /#/\n        }\n      },\n      string: {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      'class-name': {\n        pattern: /(\\\\new\\s+)[\\w-]+/,\n        lookbehind: true\n      },\n      keyword: {\n        pattern: /\\\\[a-z][-\\w]*/i,\n        inside: {\n          punctuation: /^\\\\/\n        }\n      },\n      operator: /[=|]|<<|>>/,\n      punctuation: {\n        pattern: /(^|[a-z\\d])(?:'+|,+|[_^]?-[_^]?(?:[-+^!>._]|(?=\\d))|[_^]\\.?|[.!])|[{}()[\\]<>^~]|\\\\[()[\\]<>\\\\!]|--|__/,\n        lookbehind: true\n      },\n      number: /\\b\\d+(?:\\/\\d+)?\\b/\n    };\n    lilypond['embedded-scheme'].inside['scheme'].inside['embedded-lilypond'].inside['lilypond'].inside = lilypond;\n    Prism.languages.ly = lilypond;\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorScheme", "require", "module", "exports", "lilypond", "displayName", "aliases", "Prism", "register", "schemeExpression", "source", "recursivenessLog2", "i", "replace", "languages", "comment", "pattern", "RegExp", "lookbehind", "greedy", "inside", "scheme", "alias", "punctuation", "rest", "string", "keyword", "operator", "number", "ly"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/lilypond.js"], "sourcesContent": ["'use strict'\nvar refractorScheme = require('./scheme.js')\nmodule.exports = lilypond\nlilypond.displayName = 'lilypond'\nlilypond.aliases = []\nfunction lilypond(Prism) {\n  Prism.register(refractorScheme)\n  ;(function (Prism) {\n    var schemeExpression =\n      /\\((?:[^();\"#\\\\]|\\\\[\\s\\S]|;.*(?!.)|\"(?:[^\"\\\\]|\\\\.)*\"|#(?:\\{(?:(?!#\\})[\\s\\S])*#\\}|[^{])|<expr>)*\\)/\n        .source // allow for up to pow(2, recursivenessLog2) many levels of recursive brace expressions\n    // For some reason, this can't be 4\n    var recursivenessLog2 = 5\n    for (var i = 0; i < recursivenessLog2; i++) {\n      schemeExpression = schemeExpression.replace(/<expr>/g, function () {\n        return schemeExpression\n      })\n    }\n    schemeExpression = schemeExpression.replace(/<expr>/g, /[^\\s\\S]/.source)\n    var lilypond = (Prism.languages.lilypond = {\n      comment: /%(?:(?!\\{).*|\\{[\\s\\S]*?%\\})/,\n      'embedded-scheme': {\n        pattern: RegExp(\n          /(^|[=\\s])#(?:\"(?:[^\"\\\\]|\\\\.)*\"|[^\\s()\"]*(?:[^\\s()]|<expr>))/.source.replace(\n            /<expr>/g,\n            function () {\n              return schemeExpression\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          scheme: {\n            pattern: /^(#)[\\s\\S]+$/,\n            lookbehind: true,\n            alias: 'language-scheme',\n            inside: {\n              'embedded-lilypond': {\n                pattern: /#\\{[\\s\\S]*?#\\}/,\n                greedy: true,\n                inside: {\n                  punctuation: /^#\\{|#\\}$/,\n                  lilypond: {\n                    pattern: /[\\s\\S]+/,\n                    alias: 'language-lilypond',\n                    inside: null // see below\n                  }\n                }\n              },\n              rest: Prism.languages.scheme\n            }\n          },\n          punctuation: /#/\n        }\n      },\n      string: {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      'class-name': {\n        pattern: /(\\\\new\\s+)[\\w-]+/,\n        lookbehind: true\n      },\n      keyword: {\n        pattern: /\\\\[a-z][-\\w]*/i,\n        inside: {\n          punctuation: /^\\\\/\n        }\n      },\n      operator: /[=|]|<<|>>/,\n      punctuation: {\n        pattern:\n          /(^|[a-z\\d])(?:'+|,+|[_^]?-[_^]?(?:[-+^!>._]|(?=\\d))|[_^]\\.?|[.!])|[{}()[\\]<>^~]|\\\\[()[\\]<>\\\\!]|--|__/,\n        lookbehind: true\n      },\n      number: /\\b\\d+(?:\\/\\d+)?\\b/\n    })\n    lilypond['embedded-scheme'].inside['scheme'].inside[\n      'embedded-lilypond'\n    ].inside['lilypond'].inside = lilypond\n    Prism.languages.ly = lilypond\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,aAAa,CAAC;AAC5CC,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvBA,KAAK,CAACC,QAAQ,CAACR,eAAe,CAAC;EAC9B,CAAC,UAAUO,KAAK,EAAE;IACjB,IAAIE,gBAAgB,GAClB,kGAAkG,CAC/FC,MAAM,EAAC;IACZ;IACA,IAAIC,iBAAiB,GAAG,CAAC;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,iBAAiB,EAAEC,CAAC,EAAE,EAAE;MAC1CH,gBAAgB,GAAGA,gBAAgB,CAACI,OAAO,CAAC,SAAS,EAAE,YAAY;QACjE,OAAOJ,gBAAgB;MACzB,CAAC,CAAC;IACJ;IACAA,gBAAgB,GAAGA,gBAAgB,CAACI,OAAO,CAAC,SAAS,EAAE,SAAS,CAACH,MAAM,CAAC;IACxE,IAAIN,QAAQ,GAAIG,KAAK,CAACO,SAAS,CAACV,QAAQ,GAAG;MACzCW,OAAO,EAAE,6BAA6B;MACtC,iBAAiB,EAAE;QACjBC,OAAO,EAAEC,MAAM,CACb,6DAA6D,CAACP,MAAM,CAACG,OAAO,CAC1E,SAAS,EACT,YAAY;UACV,OAAOJ,gBAAgB;QACzB,CACF,CAAC,EACD,GACF,CAAC;QACDS,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,MAAM,EAAE;YACNL,OAAO,EAAE,cAAc;YACvBE,UAAU,EAAE,IAAI;YAChBI,KAAK,EAAE,iBAAiB;YACxBF,MAAM,EAAE;cACN,mBAAmB,EAAE;gBACnBJ,OAAO,EAAE,gBAAgB;gBACzBG,MAAM,EAAE,IAAI;gBACZC,MAAM,EAAE;kBACNG,WAAW,EAAE,WAAW;kBACxBnB,QAAQ,EAAE;oBACRY,OAAO,EAAE,SAAS;oBAClBM,KAAK,EAAE,mBAAmB;oBAC1BF,MAAM,EAAE,IAAI,CAAC;kBACf;gBACF;cACF,CAAC;cACDI,IAAI,EAAEjB,KAAK,CAACO,SAAS,CAACO;YACxB;UACF,CAAC;UACDE,WAAW,EAAE;QACf;MACF,CAAC;MACDE,MAAM,EAAE;QACNT,OAAO,EAAE,mBAAmB;QAC5BG,MAAM,EAAE;MACV,CAAC;MACD,YAAY,EAAE;QACZH,OAAO,EAAE,kBAAkB;QAC3BE,UAAU,EAAE;MACd,CAAC;MACDQ,OAAO,EAAE;QACPV,OAAO,EAAE,gBAAgB;QACzBI,MAAM,EAAE;UACNG,WAAW,EAAE;QACf;MACF,CAAC;MACDI,QAAQ,EAAE,YAAY;MACtBJ,WAAW,EAAE;QACXP,OAAO,EACL,sGAAsG;QACxGE,UAAU,EAAE;MACd,CAAC;MACDU,MAAM,EAAE;IACV,CAAE;IACFxB,QAAQ,CAAC,iBAAiB,CAAC,CAACgB,MAAM,CAAC,QAAQ,CAAC,CAACA,MAAM,CACjD,mBAAmB,CACpB,CAACA,MAAM,CAAC,UAAU,CAAC,CAACA,MAAM,GAAGhB,QAAQ;IACtCG,KAAK,CAACO,SAAS,CAACe,EAAE,GAAGzB,QAAQ;EAC/B,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}