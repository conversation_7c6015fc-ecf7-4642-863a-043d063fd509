{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nvar refractorPhp = require('./php.js');\nmodule.exports = latte;\nlatte.displayName = 'latte';\nlatte.aliases = [];\nfunction latte(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  Prism.register(refractorPhp);\n  (function (Prism) {\n    Prism.languages.latte = {\n      comment: /^\\{\\*[\\s\\S]*/,\n      'latte-tag': {\n        // https://latte.nette.org/en/tags\n        pattern: /(^\\{(?:\\/(?=[a-z]))?)(?:[=_]|[a-z]\\w*\\b(?!\\())/i,\n        lookbehind: true,\n        alias: 'important'\n      },\n      delimiter: {\n        pattern: /^\\{\\/?|\\}$/,\n        alias: 'punctuation'\n      },\n      php: {\n        pattern: /\\S(?:[\\s\\S]*\\S)?/,\n        alias: 'language-php',\n        inside: Prism.languages.php\n      }\n    };\n    var markupLatte = Prism.languages.extend('markup', {});\n    Prism.languages.insertBefore('inside', 'attr-value', {\n      'n-attr': {\n        pattern: /n:[\\w-]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+))?/,\n        inside: {\n          'attr-name': {\n            pattern: /^[^\\s=]+/,\n            alias: 'important'\n          },\n          'attr-value': {\n            pattern: /=[\\s\\S]+/,\n            inside: {\n              punctuation: [/^=/, {\n                pattern: /^(\\s*)[\"']|[\"']$/,\n                lookbehind: true\n              }],\n              php: {\n                pattern: /\\S(?:[\\s\\S]*\\S)?/,\n                inside: Prism.languages.php\n              }\n            }\n          }\n        }\n      }\n    }, markupLatte.tag);\n    Prism.hooks.add('before-tokenize', function (env) {\n      if (env.language !== 'latte') {\n        return;\n      }\n      var lattePattern = /\\{\\*[\\s\\S]*?\\*\\}|\\{[^'\"\\s{}*](?:[^\"'/{}]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\}/g;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'latte', lattePattern);\n      env.grammar = markupLatte;\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'latte');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "refractorPhp", "module", "exports", "latte", "displayName", "aliases", "Prism", "register", "languages", "comment", "pattern", "lookbehind", "alias", "delimiter", "php", "inside", "markupLatte", "extend", "insertBefore", "punctuation", "tag", "hooks", "add", "env", "language", "lattePattern", "buildPlaceholders", "grammar", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/latte.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nvar refractorPhp = require('./php.js')\nmodule.exports = latte\nlatte.displayName = 'latte'\nlatte.aliases = []\nfunction latte(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  Prism.register(refractorPhp)\n  ;(function (Prism) {\n    Prism.languages.latte = {\n      comment: /^\\{\\*[\\s\\S]*/,\n      'latte-tag': {\n        // https://latte.nette.org/en/tags\n        pattern: /(^\\{(?:\\/(?=[a-z]))?)(?:[=_]|[a-z]\\w*\\b(?!\\())/i,\n        lookbehind: true,\n        alias: 'important'\n      },\n      delimiter: {\n        pattern: /^\\{\\/?|\\}$/,\n        alias: 'punctuation'\n      },\n      php: {\n        pattern: /\\S(?:[\\s\\S]*\\S)?/,\n        alias: 'language-php',\n        inside: Prism.languages.php\n      }\n    }\n    var markupLatte = Prism.languages.extend('markup', {})\n    Prism.languages.insertBefore(\n      'inside',\n      'attr-value',\n      {\n        'n-attr': {\n          pattern: /n:[\\w-]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+))?/,\n          inside: {\n            'attr-name': {\n              pattern: /^[^\\s=]+/,\n              alias: 'important'\n            },\n            'attr-value': {\n              pattern: /=[\\s\\S]+/,\n              inside: {\n                punctuation: [\n                  /^=/,\n                  {\n                    pattern: /^(\\s*)[\"']|[\"']$/,\n                    lookbehind: true\n                  }\n                ],\n                php: {\n                  pattern: /\\S(?:[\\s\\S]*\\S)?/,\n                  inside: Prism.languages.php\n                }\n              }\n            }\n          }\n        }\n      },\n      markupLatte.tag\n    )\n    Prism.hooks.add('before-tokenize', function (env) {\n      if (env.language !== 'latte') {\n        return\n      }\n      var lattePattern =\n        /\\{\\*[\\s\\S]*?\\*\\}|\\{[^'\"\\s{}*](?:[^\"'/{}]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\}/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'latte',\n        lattePattern\n      )\n      env.grammar = markupLatte\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'latte')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjE,IAAIC,YAAY,GAAGD,OAAO,CAAC,UAAU,CAAC;AACtCE,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACT,yBAAyB,CAAC;EACzCQ,KAAK,CAACC,QAAQ,CAACP,YAAY,CAAC;EAC3B,CAAC,UAAUM,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,KAAK,GAAG;MACtBM,OAAO,EAAE,cAAc;MACvB,WAAW,EAAE;QACX;QACAC,OAAO,EAAE,iDAAiD;QAC1DC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,SAAS,EAAE;QACTH,OAAO,EAAE,YAAY;QACrBE,KAAK,EAAE;MACT,CAAC;MACDE,GAAG,EAAE;QACHJ,OAAO,EAAE,kBAAkB;QAC3BE,KAAK,EAAE,cAAc;QACrBG,MAAM,EAAET,KAAK,CAACE,SAAS,CAACM;MAC1B;IACF,CAAC;IACD,IAAIE,WAAW,GAAGV,KAAK,CAACE,SAAS,CAACS,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtDX,KAAK,CAACE,SAAS,CAACU,YAAY,CAC1B,QAAQ,EACR,YAAY,EACZ;MACE,QAAQ,EAAE;QACRR,OAAO,EAAE,oDAAoD;QAC7DK,MAAM,EAAE;UACN,WAAW,EAAE;YACXL,OAAO,EAAE,UAAU;YACnBE,KAAK,EAAE;UACT,CAAC;UACD,YAAY,EAAE;YACZF,OAAO,EAAE,UAAU;YACnBK,MAAM,EAAE;cACNI,WAAW,EAAE,CACX,IAAI,EACJ;gBACET,OAAO,EAAE,kBAAkB;gBAC3BC,UAAU,EAAE;cACd,CAAC,CACF;cACDG,GAAG,EAAE;gBACHJ,OAAO,EAAE,kBAAkB;gBAC3BK,MAAM,EAAET,KAAK,CAACE,SAAS,CAACM;cAC1B;YACF;UACF;QACF;MACF;IACF,CAAC,EACDE,WAAW,CAACI,GACd,CAAC;IACDd,KAAK,CAACe,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIA,GAAG,CAACC,QAAQ,KAAK,OAAO,EAAE;QAC5B;MACF;MACA,IAAIC,YAAY,GACd,sHAAsH;MACxHnB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACkB,iBAAiB,CACpDH,GAAG,EACH,OAAO,EACPE,YACF,CAAC;MACDF,GAAG,CAACI,OAAO,GAAGX,WAAW;IAC3B,CAAC,CAAC;IACFV,KAAK,CAACe,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/CjB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACoB,oBAAoB,CAACL,GAAG,EAAE,OAAO,CAAC;IACzE,CAAC,CAAC;EACJ,CAAC,EAAEjB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}