{"ast": null, "code": "'use strict';\n\nmodule.exports = scss;\nscss.displayName = 'scss';\nscss.aliases = [];\nfunction scss(Prism) {\n  Prism.languages.scss = Prism.languages.extend('css', {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true\n    },\n    atrule: {\n      pattern: /@[\\w-](?:\\([^()]+\\)|[^()\\s]|\\s+(?!\\s))*?(?=\\s+[{;])/,\n      inside: {\n        rule: /@[\\w-]+/ // See rest below\n      }\n    },\n    // url, compassified\n    url: /(?:[-a-z]+-)?url(?=\\()/i,\n    // CSS selector regex is not appropriate for Sass\n    // since there can be lot more things (var, @ directive, nesting..)\n    // a selector must start at the end of a property or after a brace (end of other rules or nesting)\n    // it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable\n    // the end of a selector is found when there is no rules in it ( {} or {\\s}) or if there is a property (because an interpolated var\n    // can \"pass\" as a selector- e.g: proper#{$erty})\n    // this one was hard to do, so please be careful if you edit this one :)\n    selector: {\n      // Initial look-ahead is used to prevent matching of blank selectors\n      pattern: /(?=\\S)[^@;{}()]?(?:[^@;{}()\\s]|\\s+(?!\\s)|#\\{\\$[-\\w]+\\})+(?=\\s*\\{(?:\\}|\\s|[^}][^:{}]*[:{][^}]))/,\n      inside: {\n        parent: {\n          pattern: /&/,\n          alias: 'important'\n        },\n        placeholder: /%[-\\w]+/,\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    },\n    property: {\n      pattern: /(?:[-\\w]|\\$[-\\w]|#\\{\\$[-\\w]+\\})+(?=\\s*:)/,\n      inside: {\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    }\n  });\n  Prism.languages.insertBefore('scss', 'atrule', {\n    keyword: [/@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\\b/i, {\n      pattern: /( )(?:from|through)(?= )/,\n      lookbehind: true\n    }]\n  });\n  Prism.languages.insertBefore('scss', 'important', {\n    // var and interpolated vars\n    variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n  });\n  Prism.languages.insertBefore('scss', 'function', {\n    'module-modifier': {\n      pattern: /\\b(?:as|hide|show|with)\\b/i,\n      alias: 'keyword'\n    },\n    placeholder: {\n      pattern: /%[-\\w]+/,\n      alias: 'selector'\n    },\n    statement: {\n      pattern: /\\B!(?:default|optional)\\b/i,\n      alias: 'keyword'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    operator: {\n      pattern: /(\\s)(?:[-+*\\/%]|[=!]=|<=?|>=?|and|not|or)(?=\\s)/,\n      lookbehind: true\n    }\n  });\n  Prism.languages.scss['atrule'].inside.rest = Prism.languages.scss;\n}", "map": {"version": 3, "names": ["module", "exports", "scss", "displayName", "aliases", "Prism", "languages", "extend", "comment", "pattern", "lookbehind", "at<PERSON>le", "inside", "rule", "url", "selector", "parent", "alias", "placeholder", "variable", "property", "insertBefore", "keyword", "statement", "boolean", "null", "operator", "rest"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/scss.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = scss\nscss.displayName = 'scss'\nscss.aliases = []\nfunction scss(Prism) {\n  Prism.languages.scss = Prism.languages.extend('css', {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true\n    },\n    atrule: {\n      pattern: /@[\\w-](?:\\([^()]+\\)|[^()\\s]|\\s+(?!\\s))*?(?=\\s+[{;])/,\n      inside: {\n        rule: /@[\\w-]+/ // See rest below\n      }\n    },\n    // url, compassified\n    url: /(?:[-a-z]+-)?url(?=\\()/i,\n    // CSS selector regex is not appropriate for Sass\n    // since there can be lot more things (var, @ directive, nesting..)\n    // a selector must start at the end of a property or after a brace (end of other rules or nesting)\n    // it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable\n    // the end of a selector is found when there is no rules in it ( {} or {\\s}) or if there is a property (because an interpolated var\n    // can \"pass\" as a selector- e.g: proper#{$erty})\n    // this one was hard to do, so please be careful if you edit this one :)\n    selector: {\n      // Initial look-ahead is used to prevent matching of blank selectors\n      pattern:\n        /(?=\\S)[^@;{}()]?(?:[^@;{}()\\s]|\\s+(?!\\s)|#\\{\\$[-\\w]+\\})+(?=\\s*\\{(?:\\}|\\s|[^}][^:{}]*[:{][^}]))/,\n      inside: {\n        parent: {\n          pattern: /&/,\n          alias: 'important'\n        },\n        placeholder: /%[-\\w]+/,\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    },\n    property: {\n      pattern: /(?:[-\\w]|\\$[-\\w]|#\\{\\$[-\\w]+\\})+(?=\\s*:)/,\n      inside: {\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    }\n  })\n  Prism.languages.insertBefore('scss', 'atrule', {\n    keyword: [\n      /@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\\b/i,\n      {\n        pattern: /( )(?:from|through)(?= )/,\n        lookbehind: true\n      }\n    ]\n  })\n  Prism.languages.insertBefore('scss', 'important', {\n    // var and interpolated vars\n    variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n  })\n  Prism.languages.insertBefore('scss', 'function', {\n    'module-modifier': {\n      pattern: /\\b(?:as|hide|show|with)\\b/i,\n      alias: 'keyword'\n    },\n    placeholder: {\n      pattern: /%[-\\w]+/,\n      alias: 'selector'\n    },\n    statement: {\n      pattern: /\\B!(?:default|optional)\\b/i,\n      alias: 'keyword'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    operator: {\n      pattern: /(\\s)(?:[-+*\\/%]|[=!]=|<=?|>=?|and|not|or)(?=\\s)/,\n      lookbehind: true\n    }\n  })\n  Prism.languages.scss['atrule'].inside.rest = Prism.languages.scss\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,KAAK,EAAE;IACnDC,OAAO,EAAE;MACPC,OAAO,EAAE,sCAAsC;MAC/CC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,qDAAqD;MAC9DG,MAAM,EAAE;QACNC,IAAI,EAAE,SAAS,CAAC;MAClB;IACF,CAAC;IACD;IACAC,GAAG,EAAE,yBAAyB;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,QAAQ,EAAE;MACR;MACAN,OAAO,EACL,gGAAgG;MAClGG,MAAM,EAAE;QACNI,MAAM,EAAE;UACNP,OAAO,EAAE,GAAG;UACZQ,KAAK,EAAE;QACT,CAAC;QACDC,WAAW,EAAE,SAAS;QACtBC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,QAAQ,EAAE;MACRX,OAAO,EAAE,0CAA0C;MACnDG,MAAM,EAAE;QACNO,QAAQ,EAAE;MACZ;IACF;EACF,CAAC,CAAC;EACFd,KAAK,CAACC,SAAS,CAACe,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE;IAC7CC,OAAO,EAAE,CACP,mHAAmH,EACnH;MACEb,OAAO,EAAE,0BAA0B;MACnCC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC,CAAC;EACFL,KAAK,CAACC,SAAS,CAACe,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE;IAChD;IACAF,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFd,KAAK,CAACC,SAAS,CAACe,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE;IAC/C,iBAAiB,EAAE;MACjBZ,OAAO,EAAE,4BAA4B;MACrCQ,KAAK,EAAE;IACT,CAAC;IACDC,WAAW,EAAE;MACXT,OAAO,EAAE,SAAS;MAClBQ,KAAK,EAAE;IACT,CAAC;IACDM,SAAS,EAAE;MACTd,OAAO,EAAE,4BAA4B;MACrCQ,KAAK,EAAE;IACT,CAAC;IACDO,OAAO,EAAE,oBAAoB;IAC7BC,IAAI,EAAE;MACJhB,OAAO,EAAE,UAAU;MACnBQ,KAAK,EAAE;IACT,CAAC;IACDS,QAAQ,EAAE;MACRjB,OAAO,EAAE,iDAAiD;MAC1DC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EACFL,KAAK,CAACC,SAAS,CAACJ,IAAI,CAAC,QAAQ,CAAC,CAACU,MAAM,CAACe,IAAI,GAAGtB,KAAK,CAACC,SAAS,CAACJ,IAAI;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}