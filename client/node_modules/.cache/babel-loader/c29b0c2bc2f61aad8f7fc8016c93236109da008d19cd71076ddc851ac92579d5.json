{"ast": null, "code": "/*\nLanguage: RenderMan RIB\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://renderman.pixar.com/resources/RenderMan_20/ribBinding.html\nCategory: graphics\n*/\n\nfunction rib(hljs) {\n  return {\n    name: 'RenderMan RIB',\n    keywords: 'ArchiveRecord AreaLightSource Atmosphere Attribute AttributeBegin AttributeEnd Basis ' + 'Begin Blobby Bound Clipping ClippingPlane Color ColorSamples ConcatTransform Cone ' + 'CoordinateSystem CoordSysTransform CropWindow Curves Cylinder DepthOfField Detail ' + 'DetailRange Disk Displacement Display End ErrorHandler Exposure Exterior Format ' + 'FrameAspectRatio FrameBegin FrameEnd GeneralPolygon GeometricApproximation Geometry ' + 'Hider Hyperboloid Identity Illuminate Imager Interior LightSource ' + 'MakeCubeFaceEnvironment MakeLatLongEnvironment MakeShadow MakeTexture <PERSON> ' + 'MotionBegin MotionEnd NuPatch ObjectBegin ObjectEnd ObjectInstance Opacity Option ' + 'Orientation Paraboloid Patch PatchMesh Perspective PixelFilter PixelSamples ' + 'PixelVariance Points PointsGeneralPolygons PointsPolygons Polygon Procedural Projection ' + 'Quantize ReadArchive RelativeDetail ReverseOrientation Rotate Scale ScreenWindow ' + 'ShadingInterpolation ShadingRate Shutter Sides Skew SolidBegin SolidEnd Sphere ' + 'SubdivisionMesh Surface TextureCoordinates Torus Transform TransformBegin TransformEnd ' + 'TransformPoints Translate TrimCurve WorldBegin WorldEnd',\n    illegal: '</',\n    contains: [hljs.HASH_COMMENT_MODE, hljs.C_NUMBER_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n  };\n}\nmodule.exports = rib;", "map": {"version": 3, "names": ["rib", "hljs", "name", "keywords", "illegal", "contains", "HASH_COMMENT_MODE", "C_NUMBER_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/rib.js"], "sourcesContent": ["/*\nLanguage: RenderMan RIB\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://renderman.pixar.com/resources/RenderMan_20/ribBinding.html\nCategory: graphics\n*/\n\nfunction rib(hljs) {\n  return {\n    name: 'RenderMan RIB',\n    keywords:\n      'ArchiveRecord AreaLightSource Atmosphere Attribute AttributeBegin AttributeEnd Basis ' +\n      'Begin Blobby Bound Clipping ClippingPlane Color ColorSamples ConcatTransform Cone ' +\n      'CoordinateSystem CoordSysTransform CropWindow Curves Cylinder DepthOfField Detail ' +\n      'DetailRange Disk Displacement Display End ErrorHandler Exposure Exterior Format ' +\n      'FrameAspectRatio FrameBegin FrameEnd GeneralPolygon GeometricApproximation Geometry ' +\n      'Hider Hyperboloid Identity Illuminate Imager Interior LightSource ' +\n      'MakeCubeFaceEnvironment MakeLatLongEnvironment MakeShadow MakeTexture <PERSON> ' +\n      'MotionBegin MotionEnd NuPatch ObjectBegin ObjectEnd ObjectInstance Opacity Option ' +\n      'Orientation Paraboloid Patch PatchMesh Perspective PixelFilter PixelSamples ' +\n      'PixelVariance Points PointsGeneralPolygons PointsPolygons Polygon Procedural Projection ' +\n      'Quantize ReadArchive RelativeDetail ReverseOrientation Rotate Scale ScreenWindow ' +\n      'ShadingInterpolation ShadingRate Shutter Sides Skew SolidBegin SolidEnd Sphere ' +\n      'SubdivisionMesh Surface TextureCoordinates Torus Transform TransformBegin TransformEnd ' +\n      'TransformPoints Translate TrimCurve WorldBegin WorldEnd',\n    illegal: '</',\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n}\n\nmodule.exports = rib;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,eAAe;IACrBC,QAAQ,EACN,uFAAuF,GACvF,oFAAoF,GACpF,oFAAoF,GACpF,kFAAkF,GAClF,sFAAsF,GACtF,oEAAoE,GACpE,8EAA8E,GAC9E,oFAAoF,GACpF,8EAA8E,GAC9E,0FAA0F,GAC1F,mFAAmF,GACnF,iFAAiF,GACjF,yFAAyF,GACzF,yDAAyD;IAC3DC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRJ,IAAI,CAACK,iBAAiB,EACtBL,IAAI,CAACM,aAAa,EAClBN,IAAI,CAACO,gBAAgB,EACrBP,IAAI,CAACQ,iBAAiB;EAE1B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGX,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}