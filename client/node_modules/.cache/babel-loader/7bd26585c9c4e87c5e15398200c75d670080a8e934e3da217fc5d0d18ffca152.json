{"ast": null, "code": "'use strict';\n\nmodule.exports = keyman;\nkeyman.displayName = 'keyman';\nkeyman.aliases = [];\nfunction keyman(Prism) {\n  Prism.languages.keyman = {\n    comment: {\n      pattern: /\\bc .*/i,\n      greedy: true\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n      greedy: true\n    },\n    'virtual-key': {\n      pattern: /\\[\\s*(?:(?:ALT|CAPS|CTRL|LALT|LCTRL|NCAPS|RALT|RCTRL|SHIFT)\\s+)*(?:[TKU]_[\\w?]+|[A-E]\\d\\d?|\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*\\]/i,\n      greedy: true,\n      alias: 'function' // alias for styles\n    },\n    // https://help.keyman.com/developer/language/guide/headers\n    'header-keyword': {\n      pattern: /&\\w+/,\n      alias: 'bold' // alias for styles\n    },\n    'header-statement': {\n      pattern: /\\b(?:bitmap|bitmaps|caps always off|caps on only|copyright|hotkey|language|layout|message|name|shift frees caps|version)\\b/i,\n      alias: 'bold' // alias for styles\n    },\n    'rule-keyword': {\n      pattern: /\\b(?:any|baselayout|beep|call|context|deadkey|dk|if|index|layer|notany|nul|outs|platform|reset|return|save|set|store|use)\\b/i,\n      alias: 'keyword'\n    },\n    'structural-keyword': {\n      pattern: /\\b(?:ansi|begin|group|match|nomatch|unicode|using keys)\\b/i,\n      alias: 'keyword'\n    },\n    'compile-target': {\n      pattern: /\\$(?:keyman|keymanonly|keymanweb|kmfl|weaver):/i,\n      alias: 'property'\n    },\n    // U+####, x###, d### characters and numbers\n    number: /\\b(?:U\\+[\\dA-F]+|d\\d+|x[\\da-f]+|\\d+)\\b/i,\n    operator: /[+>\\\\$]|\\.\\./,\n    punctuation: /[()=,]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "keyman", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "alias", "number", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/keyman.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = keyman\nkeyman.displayName = 'keyman'\nkeyman.aliases = []\nfunction keyman(Prism) {\n  Prism.languages.keyman = {\n    comment: {\n      pattern: /\\bc .*/i,\n      greedy: true\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n      greedy: true\n    },\n    'virtual-key': {\n      pattern:\n        /\\[\\s*(?:(?:ALT|CAPS|CTRL|LALT|LCTRL|NCAPS|RALT|RCTRL|SHIFT)\\s+)*(?:[TKU]_[\\w?]+|[A-E]\\d\\d?|\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*\\]/i,\n      greedy: true,\n      alias: 'function' // alias for styles\n    },\n    // https://help.keyman.com/developer/language/guide/headers\n    'header-keyword': {\n      pattern: /&\\w+/,\n      alias: 'bold' // alias for styles\n    },\n    'header-statement': {\n      pattern:\n        /\\b(?:bitmap|bitmaps|caps always off|caps on only|copyright|hotkey|language|layout|message|name|shift frees caps|version)\\b/i,\n      alias: 'bold' // alias for styles\n    },\n    'rule-keyword': {\n      pattern:\n        /\\b(?:any|baselayout|beep|call|context|deadkey|dk|if|index|layer|notany|nul|outs|platform|reset|return|save|set|store|use)\\b/i,\n      alias: 'keyword'\n    },\n    'structural-keyword': {\n      pattern: /\\b(?:ansi|begin|group|match|nomatch|unicode|using keys)\\b/i,\n      alias: 'keyword'\n    },\n    'compile-target': {\n      pattern: /\\$(?:keyman|keymanonly|keymanweb|kmfl|weaver):/i,\n      alias: 'property'\n    },\n    // U+####, x###, d### characters and numbers\n    number: /\\b(?:U\\+[\\dA-F]+|d\\d+|x[\\da-f]+|\\d+)\\b/i,\n    operator: /[+>\\\\$]|\\.\\./,\n    punctuation: /[()=,]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE;MACPC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,yBAAyB;MAClCC,MAAM,EAAE;IACV,CAAC;IACD,aAAa,EAAE;MACbD,OAAO,EACL,2HAA2H;MAC7HC,MAAM,EAAE,IAAI;MACZE,KAAK,EAAE,UAAU,CAAC;IACpB,CAAC;IACD;IACA,gBAAgB,EAAE;MAChBH,OAAO,EAAE,MAAM;MACfG,KAAK,EAAE,MAAM,CAAC;IAChB,CAAC;IACD,kBAAkB,EAAE;MAClBH,OAAO,EACL,6HAA6H;MAC/HG,KAAK,EAAE,MAAM,CAAC;IAChB,CAAC;IACD,cAAc,EAAE;MACdH,OAAO,EACL,8HAA8H;MAChIG,KAAK,EAAE;IACT,CAAC;IACD,oBAAoB,EAAE;MACpBH,OAAO,EAAE,4DAA4D;MACrEG,KAAK,EAAE;IACT,CAAC;IACD,gBAAgB,EAAE;MAChBH,OAAO,EAAE,iDAAiD;MAC1DG,KAAK,EAAE;IACT,CAAC;IACD;IACAC,MAAM,EAAE,yCAAyC;IACjDC,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}