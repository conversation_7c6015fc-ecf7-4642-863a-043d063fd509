{"ast": null, "code": "'use strict';\n\nmodule.exports = hpkp;\nhpkp.displayName = 'hpkp';\nhpkp.aliases = [];\nfunction hpkp(Prism) {\n  /**\n   * Original by <PERSON>.\n   *\n   * Reference: https://scotthelme.co.uk/hpkp-cheat-sheet/\n   */\n  Prism.languages.hpkp = {\n    directive: {\n      pattern: /\\b(?:includeSubDomains|max-age|pin-sha256|preload|report-to|report-uri|strict)(?=[\\s;=]|$)/i,\n      alias: 'property'\n    },\n    operator: /=/,\n    punctuation: /;/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "hpkp", "displayName", "aliases", "Prism", "languages", "directive", "pattern", "alias", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/hpkp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = hpkp\nhpkp.displayName = 'hpkp'\nhpkp.aliases = []\nfunction hpkp(Prism) {\n  /**\n   * Original by <PERSON>.\n   *\n   * Reference: https://scotthelme.co.uk/hpkp-cheat-sheet/\n   */\n  Prism.languages.hpkp = {\n    directive: {\n      pattern:\n        /\\b(?:includeSubDomains|max-age|pin-sha256|preload|report-to|report-uri|strict)(?=[\\s;=]|$)/i,\n      alias: 'property'\n    },\n    operator: /=/,\n    punctuation: /;/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;AACF;AACA;AACA;AACA;EACEA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,SAAS,EAAE;MACTC,OAAO,EACL,6FAA6F;MAC/FC,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}