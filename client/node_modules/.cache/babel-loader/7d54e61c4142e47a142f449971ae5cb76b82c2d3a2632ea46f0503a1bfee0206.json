{"ast": null, "code": "/*\nLanguage: Gradle\nDescription: Gradle is an open-source build automation tool focused on flexibility and performance.\nWebsite: https://gradle.org\nAuthor: <PERSON> <<EMAIL>>\n*/\n\nfunction gradle(hljs) {\n  return {\n    name: 'Gradle',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'task project allprojects subprojects artifacts buildscript configurations ' + 'dependencies repositories sourceSets description delete from into include ' + 'exclude source classpath destinationDir includes options sourceCompatibility ' + 'targetCompatibility group flatDir doLast doFirst flatten todir fromdir ant ' + 'def abstract break case catch continue default do else extends final finally ' + 'for if implements instanceof native new private protected public return static ' + 'switch synchronized throw throws transient try volatile while strictfp package ' + 'import false null super this true antlrtask checkstyle codenarc copy boolean ' + 'byte char class double float int interface long short void compile runTime ' + 'file fileTree abs any append asList asWritable call collect compareTo count ' + 'div dump each eachByte eachFile eachLine every find findAll flatten getAt ' + 'getErr getIn getOut getText grep immutable inject inspect intersect invokeMethods ' + 'isCase join leftShift minus multiply newInputStream newOutputStream newPrintWriter ' + 'newReader newWriter next plus pop power previous print println push putAt read ' + 'readBytes readLines reverse reverseEach round size sort splitEachLine step subMap ' + 'times toInteger toList tokenize upto waitForOrKill withPrintWriter withReader ' + 'withStream withWriter withWriterAppend write writeLine'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.NUMBER_MODE, hljs.REGEXP_MODE]\n  };\n}\nmodule.exports = gradle;", "map": {"version": 3, "names": ["gradle", "hljs", "name", "case_insensitive", "keywords", "keyword", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "NUMBER_MODE", "REGEXP_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/gradle.js"], "sourcesContent": ["/*\nLanguage: Gradle\nDescription: Gradle is an open-source build automation tool focused on flexibility and performance.\nWebsite: https://gradle.org\nAuthor: <PERSON> <<EMAIL>>\n*/\n\nfunction gradle(hljs) {\n  return {\n    name: 'Gradle',\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        'task project allprojects subprojects artifacts buildscript configurations ' +\n        'dependencies repositories sourceSets description delete from into include ' +\n        'exclude source classpath destinationDir includes options sourceCompatibility ' +\n        'targetCompatibility group flatDir doLast doFirst flatten todir fromdir ant ' +\n        'def abstract break case catch continue default do else extends final finally ' +\n        'for if implements instanceof native new private protected public return static ' +\n        'switch synchronized throw throws transient try volatile while strictfp package ' +\n        'import false null super this true antlrtask checkstyle codenarc copy boolean ' +\n        'byte char class double float int interface long short void compile runTime ' +\n        'file fileTree abs any append asList asWritable call collect compareTo count ' +\n        'div dump each eachByte eachFile eachLine every find findAll flatten getAt ' +\n        'getErr getIn getOut getText grep immutable inject inspect intersect invokeMethods ' +\n        'isCase join leftShift minus multiply newInputStream newOutputStream newPrintWriter ' +\n        'newReader newWriter next plus pop power previous print println push putAt read ' +\n        'readBytes readLines reverse reverseEach round size sort splitEachLine step subMap ' +\n        'times toInteger toList tokenize upto waitForOrKill withPrintWriter withReader ' +\n        'withStream withWriter withWriterAppend write writeLine'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.REGEXP_MODE\n\n    ]\n  };\n}\n\nmodule.exports = gradle;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,OAAO;IACLC,IAAI,EAAE,QAAQ;IACdC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO,EACL,4EAA4E,GAC5E,4EAA4E,GAC5E,+EAA+E,GAC/E,6EAA6E,GAC7E,+EAA+E,GAC/E,iFAAiF,GACjF,iFAAiF,GACjF,+EAA+E,GAC/E,6EAA6E,GAC7E,8EAA8E,GAC9E,4EAA4E,GAC5E,oFAAoF,GACpF,qFAAqF,GACrF,iFAAiF,GACjF,oFAAoF,GACpF,gFAAgF,GAChF;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRL,IAAI,CAACM,mBAAmB,EACxBN,IAAI,CAACO,oBAAoB,EACzBP,IAAI,CAACQ,gBAAgB,EACrBR,IAAI,CAACS,iBAAiB,EACtBT,IAAI,CAACU,WAAW,EAChBV,IAAI,CAACW,WAAW;EAGpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}