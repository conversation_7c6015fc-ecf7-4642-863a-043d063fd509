{"ast": null, "code": "'use strict';\n\nmodule.exports = roboconf;\nroboconf.displayName = 'roboconf';\nroboconf.aliases = [];\nfunction roboconf(Prism) {\n  Prism.languages.roboconf = {\n    comment: /#.*/,\n    keyword: {\n      pattern: /(^|\\s)(?:(?:external|import)\\b|(?:facet|instance of)(?=[ \\t]+[\\w-]+[ \\t]*\\{))/,\n      lookbehind: true\n    },\n    component: {\n      pattern: /[\\w-]+(?=[ \\t]*\\{)/,\n      alias: 'variable'\n    },\n    property: /[\\w.-]+(?=[ \\t]*:)/,\n    value: {\n      pattern: /(=[ \\t]*(?![ \\t]))[^,;]+/,\n      lookbehind: true,\n      alias: 'attr-value'\n    },\n    optional: {\n      pattern: /\\(optional\\)/,\n      alias: 'builtin'\n    },\n    wildcard: {\n      pattern: /(\\.)\\*/,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    punctuation: /[{},.;:=]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "roboconf", "displayName", "aliases", "Prism", "languages", "comment", "keyword", "pattern", "lookbehind", "component", "alias", "property", "value", "optional", "wildcard", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/roboconf.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = roboconf\nroboconf.displayName = 'roboconf'\nroboconf.aliases = []\nfunction roboconf(Prism) {\n  Prism.languages.roboconf = {\n    comment: /#.*/,\n    keyword: {\n      pattern:\n        /(^|\\s)(?:(?:external|import)\\b|(?:facet|instance of)(?=[ \\t]+[\\w-]+[ \\t]*\\{))/,\n      lookbehind: true\n    },\n    component: {\n      pattern: /[\\w-]+(?=[ \\t]*\\{)/,\n      alias: 'variable'\n    },\n    property: /[\\w.-]+(?=[ \\t]*:)/,\n    value: {\n      pattern: /(=[ \\t]*(?![ \\t]))[^,;]+/,\n      lookbehind: true,\n      alias: 'attr-value'\n    },\n    optional: {\n      pattern: /\\(optional\\)/,\n      alias: 'builtin'\n    },\n    wildcard: {\n      pattern: /(\\.)\\*/,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    punctuation: /[{},.;:=]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvBA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAG;IACzBK,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE;MACPC,OAAO,EACL,+EAA+E;MACjFC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE;MACTF,OAAO,EAAE,oBAAoB;MAC7BG,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE,oBAAoB;IAC9BC,KAAK,EAAE;MACLL,OAAO,EAAE,0BAA0B;MACnCC,UAAU,EAAE,IAAI;MAChBE,KAAK,EAAE;IACT,CAAC;IACDG,QAAQ,EAAE;MACRN,OAAO,EAAE,cAAc;MACvBG,KAAK,EAAE;IACT,CAAC;IACDI,QAAQ,EAAE;MACRP,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,IAAI;MAChBE,KAAK,EAAE;IACT,CAAC;IACDK,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}