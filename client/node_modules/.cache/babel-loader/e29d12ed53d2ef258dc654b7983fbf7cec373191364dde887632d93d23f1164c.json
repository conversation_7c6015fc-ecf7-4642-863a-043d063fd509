{"ast": null, "code": "export default (function (astGenerator, language) {\n  var langs = astGenerator.listLanguages();\n  return langs.indexOf(language) !== -1;\n});", "map": {"version": 3, "names": ["astGenerator", "language", "langs", "listLanguages", "indexOf"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/checkForListedLanguage.js"], "sourcesContent": ["export default (function (astGenerator, language) {\n  var langs = astGenerator.listLanguages();\n  return langs.indexOf(language) !== -1;\n});"], "mappings": "AAAA,gBAAgB,UAAUA,YAAY,EAAEC,QAAQ,EAAE;EAChD,IAAIC,KAAK,GAAGF,YAAY,CAACG,aAAa,CAAC,CAAC;EACxC,OAAOD,KAAK,CAACE,OAAO,CAACH,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}