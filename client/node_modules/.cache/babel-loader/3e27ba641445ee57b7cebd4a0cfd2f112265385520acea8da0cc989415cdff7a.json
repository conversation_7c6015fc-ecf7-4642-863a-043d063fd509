{"ast": null, "code": "'use strict';\n\nmodule.exports = gn;\ngn.displayName = 'gn';\ngn.aliases = ['gni'];\nfunction gn(Prism) {\n  // https://gn.googlesource.com/gn/+/refs/heads/main/docs/reference.md#grammar\n  Prism.languages.gn = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'string-literal': {\n      pattern: /(^|[^\\\\\"])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[\\s\\S]*?\\}|[a-zA-Z_]\\w*|0x[a-fA-F0-9]{2})/,\n          lookbehind: true,\n          inside: {\n            number: /^\\$0x[\\s\\S]{2}$/,\n            variable: /^\\$\\w+$/,\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: null // see below\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    keyword: /\\b(?:else|if)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    'builtin-function': {\n      // a few functions get special highlighting to improve readability\n      pattern: /\\b(?:assert|defined|foreach|import|pool|print|template|tool|toolchain)(?=\\s*\\()/i,\n      alias: 'keyword'\n    },\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant: /\\b(?:current_cpu|current_os|current_toolchain|default_toolchain|host_cpu|host_os|root_build_dir|root_gen_dir|root_out_dir|target_cpu|target_gen_dir|target_os|target_out_dir)\\b/,\n    number: /-?\\b\\d+\\b/,\n    operator: /[-+!=<>]=?|&&|\\|\\|/,\n    punctuation: /[(){}[\\],.]/\n  };\n  Prism.languages.gn['string-literal'].inside['interpolation'].inside['expression'].inside = Prism.languages.gn;\n  Prism.languages.gni = Prism.languages.gn;\n}", "map": {"version": 3, "names": ["module", "exports", "gn", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "lookbehind", "inside", "interpolation", "number", "variable", "alias", "expression", "string", "keyword", "boolean", "function", "constant", "operator", "punctuation", "gni"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/gn.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = gn\ngn.displayName = 'gn'\ngn.aliases = ['gni']\nfunction gn(Prism) {\n  // https://gn.googlesource.com/gn/+/refs/heads/main/docs/reference.md#grammar\n  Prism.languages.gn = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'string-literal': {\n      pattern: /(^|[^\\\\\"])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[\\s\\S]*?\\}|[a-zA-Z_]\\w*|0x[a-fA-F0-9]{2})/,\n          lookbehind: true,\n          inside: {\n            number: /^\\$0x[\\s\\S]{2}$/,\n            variable: /^\\$\\w+$/,\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: null // see below\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    keyword: /\\b(?:else|if)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    'builtin-function': {\n      // a few functions get special highlighting to improve readability\n      pattern:\n        /\\b(?:assert|defined|foreach|import|pool|print|template|tool|toolchain)(?=\\s*\\()/i,\n      alias: 'keyword'\n    },\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant:\n      /\\b(?:current_cpu|current_os|current_toolchain|default_toolchain|host_cpu|host_os|root_build_dir|root_gen_dir|root_out_dir|target_cpu|target_gen_dir|target_os|target_out_dir)\\b/,\n    number: /-?\\b\\d+\\b/,\n    operator: /[-+!=<>]=?|&&|\\|\\|/,\n    punctuation: /[(){}[\\],.]/\n  }\n  Prism.languages.gn['string-literal'].inside['interpolation'].inside[\n    'expression'\n  ].inside = Prism.languages.gn\n  Prism.languages.gni = Prism.languages.gn\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,EAAE;AACnBA,EAAE,CAACC,WAAW,GAAG,IAAI;AACrBD,EAAE,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AACpB,SAASF,EAAEA,CAACG,KAAK,EAAE;EACjB;EACAA,KAAK,CAACC,SAAS,CAACJ,EAAE,GAAG;IACnBK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACD,gBAAgB,EAAE;MAChBD,OAAO,EAAE,iCAAiC;MAC1CE,UAAU,EAAE,IAAI;MAChBD,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACNC,aAAa,EAAE;UACbJ,OAAO,EACL,yEAAyE;UAC3EE,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNE,MAAM,EAAE,iBAAiB;YACzBC,QAAQ,EAAE,SAAS;YACnB,2BAA2B,EAAE;cAC3BN,OAAO,EAAE,WAAW;cACpBO,KAAK,EAAE;YACT,CAAC;YACDC,UAAU,EAAE;cACVR,OAAO,EAAE,SAAS;cAClBG,MAAM,EAAE,IAAI,CAAC;YACf;UACF;QACF,CAAC;QACDM,MAAM,EAAE;MACV;IACF,CAAC;IACDC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE,oBAAoB;IAC7B,kBAAkB,EAAE;MAClB;MACAX,OAAO,EACL,kFAAkF;MACpFO,KAAK,EAAE;IACT,CAAC;IACDK,QAAQ,EAAE,uBAAuB;IACjCC,QAAQ,EACN,iLAAiL;IACnLR,MAAM,EAAE,WAAW;IACnBS,QAAQ,EAAE,oBAAoB;IAC9BC,WAAW,EAAE;EACf,CAAC;EACDlB,KAAK,CAACC,SAAS,CAACJ,EAAE,CAAC,gBAAgB,CAAC,CAACS,MAAM,CAAC,eAAe,CAAC,CAACA,MAAM,CACjE,YAAY,CACb,CAACA,MAAM,GAAGN,KAAK,CAACC,SAAS,CAACJ,EAAE;EAC7BG,KAAK,CAACC,SAAS,CAACkB,GAAG,GAAGnB,KAAK,CAACC,SAAS,CAACJ,EAAE;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}