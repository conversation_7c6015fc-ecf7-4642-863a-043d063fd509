{"ast": null, "code": "/*\nLanguage: Thrift\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Thrift message definition format\nWebsite: https://thrift.apache.org\nCategory: protocols\n*/\n\nfunction thrift(hljs) {\n  const BUILT_IN_TYPES = 'bool byte i16 i32 i64 double string binary';\n  return {\n    name: 'Thrift',\n    keywords: {\n      keyword: 'namespace const typedef struct enum service exception void oneway set list map required optional',\n      built_in: BUILT_IN_TYPES,\n      literal: 'true false'\n    },\n    contains: [hljs.QUOTE_STRING_MODE, hljs.NUMBER_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'class',\n      beginKeywords: 'struct enum service exception',\n      end: /\\{/,\n      illegal: /\\n/,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        // hack: eating everything after the first title\n        starts: {\n          endsWithParent: true,\n          excludeEnd: true\n        }\n      })]\n    }, {\n      begin: '\\\\b(set|list|map)\\\\s*<',\n      end: '>',\n      keywords: BUILT_IN_TYPES,\n      contains: ['self']\n    }]\n  };\n}\nmodule.exports = thrift;", "map": {"version": 3, "names": ["thrift", "hljs", "BUILT_IN_TYPES", "name", "keywords", "keyword", "built_in", "literal", "contains", "QUOTE_STRING_MODE", "NUMBER_MODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "className", "beginKeywords", "end", "illegal", "inherit", "TITLE_MODE", "starts", "endsWithParent", "excludeEnd", "begin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/thrift.js"], "sourcesContent": ["/*\nLanguage: Thrift\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Thrift message definition format\nWebsite: https://thrift.apache.org\nCategory: protocols\n*/\n\nfunction thrift(hljs) {\n  const BUILT_IN_TYPES = 'bool byte i16 i32 i64 double string binary';\n  return {\n    name: 'Thrift',\n    keywords: {\n      keyword:\n        'namespace const typedef struct enum service exception void oneway set list map required optional',\n      built_in:\n        BUILT_IN_TYPES,\n      literal:\n        'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'struct enum service exception',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            // hack: eating everything after the first title\n            starts: {\n              endsWithParent: true,\n              excludeEnd: true\n            }\n          })\n        ]\n      },\n      {\n        begin: '\\\\b(set|list|map)\\\\s*<',\n        end: '>',\n        keywords: BUILT_IN_TYPES,\n        contains: [ 'self' ]\n      }\n    ]\n  };\n}\n\nmodule.exports = thrift;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,cAAc,GAAG,4CAA4C;EACnE,OAAO;IACLC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE;MACRC,OAAO,EACL,kGAAkG;MACpGC,QAAQ,EACNJ,cAAc;MAChBK,OAAO,EACL;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRP,IAAI,CAACQ,iBAAiB,EACtBR,IAAI,CAACS,WAAW,EAChBT,IAAI,CAACU,mBAAmB,EACxBV,IAAI,CAACW,oBAAoB,EACzB;MACEC,SAAS,EAAE,OAAO;MAClBC,aAAa,EAAE,+BAA+B;MAC9CC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE,IAAI;MACbR,QAAQ,EAAE,CACRP,IAAI,CAACgB,OAAO,CAAChB,IAAI,CAACiB,UAAU,EAAE;QAC5B;QACAC,MAAM,EAAE;UACNC,cAAc,EAAE,IAAI;UACpBC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;IAEN,CAAC,EACD;MACEC,KAAK,EAAE,wBAAwB;MAC/BP,GAAG,EAAE,GAAG;MACRX,QAAQ,EAAEF,cAAc;MACxBM,QAAQ,EAAE,CAAE,MAAM;IACpB,CAAC;EAEL,CAAC;AACH;AAEAe,MAAM,CAACC,OAAO,GAAGxB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}