{"ast": null, "code": "'use strict';\n\nmodule.exports = systemd;\nsystemd.displayName = 'systemd';\nsystemd.aliases = [];\nfunction systemd(Prism) {\n  // https://www.freedesktop.org/software/systemd/man/systemd.syntax.html\n  ;\n  (function (Prism) {\n    var comment = {\n      pattern: /^[;#].*/m,\n      greedy: true\n    };\n    var quotesSource = /\"(?:[^\\r\\n\"\\\\]|\\\\(?:[^\\r]|\\r\\n?))*\"(?!\\S)/.source;\n    Prism.languages.systemd = {\n      comment: comment,\n      section: {\n        pattern: /^\\[[^\\n\\r\\[\\]]*\\](?=[ \\t]*$)/m,\n        greedy: true,\n        inside: {\n          punctuation: /^\\[|\\]$/,\n          'section-name': {\n            pattern: /[\\s\\S]+/,\n            alias: 'selector'\n          }\n        }\n      },\n      key: {\n        pattern: /^[^\\s=]+(?=[ \\t]*=)/m,\n        greedy: true,\n        alias: 'attr-name'\n      },\n      value: {\n        // This pattern is quite complex because of two properties:\n        //  1) Quotes (strings) must be preceded by a space. Since we can't use lookbehinds, we have to \"resolve\"\n        //     the lookbehind. You will see this in the main loop where spaces are handled separately.\n        //  2) Line continuations.\n        //     After line continuations, empty lines and comments are ignored so we have to consume them.\n        pattern: RegExp(/(=[ \\t]*(?!\\s))/.source +\n        // the value either starts with quotes or not\n        '(?:' + quotesSource + '|(?=[^\"\\r\\n]))' +\n        // main loop\n        '(?:' + (/[^\\s\\\\]/.source +\n        // handle spaces separately because of quotes\n        '|' + '[ \\t]+(?:(?![ \\t\"])|' + quotesSource + ')' +\n        // line continuation\n        '|' + /\\\\[\\r\\n]+(?:[#;].*[\\r\\n]+)*(?![#;])/.source) + ')*'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'attr-value',\n        inside: {\n          comment: comment,\n          quoted: {\n            pattern: RegExp(/(^|\\s)/.source + quotesSource),\n            lookbehind: true,\n            greedy: true\n          },\n          punctuation: /\\\\$/m,\n          boolean: {\n            pattern: /^(?:false|no|off|on|true|yes)$/,\n            greedy: true\n          }\n        }\n      },\n      punctuation: /=/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "systemd", "displayName", "aliases", "Prism", "comment", "pattern", "greedy", "quotesSource", "source", "languages", "section", "inside", "punctuation", "alias", "key", "value", "RegExp", "lookbehind", "quoted", "boolean"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/systemd.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = systemd\nsystemd.displayName = 'systemd'\nsystemd.aliases = []\nfunction systemd(Prism) {\n  // https://www.freedesktop.org/software/systemd/man/systemd.syntax.html\n  ;(function (Prism) {\n    var comment = {\n      pattern: /^[;#].*/m,\n      greedy: true\n    }\n    var quotesSource = /\"(?:[^\\r\\n\"\\\\]|\\\\(?:[^\\r]|\\r\\n?))*\"(?!\\S)/.source\n    Prism.languages.systemd = {\n      comment: comment,\n      section: {\n        pattern: /^\\[[^\\n\\r\\[\\]]*\\](?=[ \\t]*$)/m,\n        greedy: true,\n        inside: {\n          punctuation: /^\\[|\\]$/,\n          'section-name': {\n            pattern: /[\\s\\S]+/,\n            alias: 'selector'\n          }\n        }\n      },\n      key: {\n        pattern: /^[^\\s=]+(?=[ \\t]*=)/m,\n        greedy: true,\n        alias: 'attr-name'\n      },\n      value: {\n        // This pattern is quite complex because of two properties:\n        //  1) Quotes (strings) must be preceded by a space. Since we can't use lookbehinds, we have to \"resolve\"\n        //     the lookbehind. You will see this in the main loop where spaces are handled separately.\n        //  2) Line continuations.\n        //     After line continuations, empty lines and comments are ignored so we have to consume them.\n        pattern: RegExp(\n          /(=[ \\t]*(?!\\s))/.source + // the value either starts with quotes or not\n            '(?:' +\n            quotesSource +\n            '|(?=[^\"\\r\\n]))' + // main loop\n            '(?:' +\n            (/[^\\s\\\\]/.source + // handle spaces separately because of quotes\n              '|' +\n              '[ \\t]+(?:(?![ \\t\"])|' +\n              quotesSource +\n              ')' + // line continuation\n              '|' +\n              /\\\\[\\r\\n]+(?:[#;].*[\\r\\n]+)*(?![#;])/.source) +\n            ')*'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'attr-value',\n        inside: {\n          comment: comment,\n          quoted: {\n            pattern: RegExp(/(^|\\s)/.source + quotesSource),\n            lookbehind: true,\n            greedy: true\n          },\n          punctuation: /\\\\$/m,\n          boolean: {\n            pattern: /^(?:false|no|off|on|true|yes)$/,\n            greedy: true\n          }\n        }\n      },\n      punctuation: /=/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtB;EACA;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,OAAO,GAAG;MACZC,OAAO,EAAE,UAAU;MACnBC,MAAM,EAAE;IACV,CAAC;IACD,IAAIC,YAAY,GAAG,2CAA2C,CAACC,MAAM;IACrEL,KAAK,CAACM,SAAS,CAACT,OAAO,GAAG;MACxBI,OAAO,EAAEA,OAAO;MAChBM,OAAO,EAAE;QACPL,OAAO,EAAE,+BAA+B;QACxCC,MAAM,EAAE,IAAI;QACZK,MAAM,EAAE;UACNC,WAAW,EAAE,SAAS;UACtB,cAAc,EAAE;YACdP,OAAO,EAAE,SAAS;YAClBQ,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDC,GAAG,EAAE;QACHT,OAAO,EAAE,sBAAsB;QAC/BC,MAAM,EAAE,IAAI;QACZO,KAAK,EAAE;MACT,CAAC;MACDE,KAAK,EAAE;QACL;QACA;QACA;QACA;QACA;QACAV,OAAO,EAAEW,MAAM,CACb,iBAAiB,CAACR,MAAM;QAAG;QACzB,KAAK,GACLD,YAAY,GACZ,gBAAgB;QAAG;QACnB,KAAK,IACJ,SAAS,CAACC,MAAM;QAAG;QAClB,GAAG,GACH,sBAAsB,GACtBD,YAAY,GACZ,GAAG;QAAG;QACN,GAAG,GACH,qCAAqC,CAACC,MAAM,CAAC,GAC/C,IACJ,CAAC;QACDS,UAAU,EAAE,IAAI;QAChBX,MAAM,EAAE,IAAI;QACZO,KAAK,EAAE,YAAY;QACnBF,MAAM,EAAE;UACNP,OAAO,EAAEA,OAAO;UAChBc,MAAM,EAAE;YACNb,OAAO,EAAEW,MAAM,CAAC,QAAQ,CAACR,MAAM,GAAGD,YAAY,CAAC;YAC/CU,UAAU,EAAE,IAAI;YAChBX,MAAM,EAAE;UACV,CAAC;UACDM,WAAW,EAAE,MAAM;UACnBO,OAAO,EAAE;YACPd,OAAO,EAAE,gCAAgC;YACzCC,MAAM,EAAE;UACV;QACF;MACF,CAAC;MACDM,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAET,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}