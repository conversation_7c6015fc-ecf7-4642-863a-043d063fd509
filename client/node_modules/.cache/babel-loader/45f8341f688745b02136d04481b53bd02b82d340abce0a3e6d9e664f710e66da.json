{"ast": null, "code": "'use strict';\n\nmodule.exports = mongodb;\nmongodb.displayName = 'mongodb';\nmongodb.aliases = [];\nfunction mongodb(Prism) {\n  ;\n  (function (Prism) {\n    var operators = [\n    // query and projection\n    '$eq', '$gt', '$gte', '$in', '$lt', '$lte', '$ne', '$nin', '$and', '$not', '$nor', '$or', '$exists', '$type', '$expr', '$jsonSchema', '$mod', '$regex', '$text', '$where', '$geoIntersects', '$geoWithin', '$near', '$nearSphere', '$all', '$elemMatch', '$size', '$bitsAllClear', '$bitsAllSet', '$bitsAnyClear', '$bitsAnySet', '$comment', '$elemMatch', '$meta', '$slice',\n    // update\n    '$currentDate', '$inc', '$min', '$max', '$mul', '$rename', '$set', '$setOnInsert', '$unset', '$addToSet', '$pop', '$pull', '$push', '$pullAll', '$each', '$position', '$slice', '$sort', '$bit',\n    // aggregation pipeline stages\n    '$addFields', '$bucket', '$bucketAuto', '$collStats', '$count', '$currentOp', '$facet', '$geoNear', '$graphLookup', '$group', '$indexStats', '$limit', '$listLocalSessions', '$listSessions', '$lookup', '$match', '$merge', '$out', '$planCacheStats', '$project', '$redact', '$replaceRoot', '$replaceWith', '$sample', '$set', '$skip', '$sort', '$sortByCount', '$unionWith', '$unset', '$unwind', '$setWindowFields',\n    // aggregation pipeline operators\n    '$abs', '$accumulator', '$acos', '$acosh', '$add', '$addToSet', '$allElementsTrue', '$and', '$anyElementTrue', '$arrayElemAt', '$arrayToObject', '$asin', '$asinh', '$atan', '$atan2', '$atanh', '$avg', '$binarySize', '$bsonSize', '$ceil', '$cmp', '$concat', '$concatArrays', '$cond', '$convert', '$cos', '$dateFromParts', '$dateToParts', '$dateFromString', '$dateToString', '$dayOfMonth', '$dayOfWeek', '$dayOfYear', '$degreesToRadians', '$divide', '$eq', '$exp', '$filter', '$first', '$floor', '$function', '$gt', '$gte', '$hour', '$ifNull', '$in', '$indexOfArray', '$indexOfBytes', '$indexOfCP', '$isArray', '$isNumber', '$isoDayOfWeek', '$isoWeek', '$isoWeekYear', '$last', '$last', '$let', '$literal', '$ln', '$log', '$log10', '$lt', '$lte', '$ltrim', '$map', '$max', '$mergeObjects', '$meta', '$min', '$millisecond', '$minute', '$mod', '$month', '$multiply', '$ne', '$not', '$objectToArray', '$or', '$pow', '$push', '$radiansToDegrees', '$range', '$reduce', '$regexFind', '$regexFindAll', '$regexMatch', '$replaceOne', '$replaceAll', '$reverseArray', '$round', '$rtrim', '$second', '$setDifference', '$setEquals', '$setIntersection', '$setIsSubset', '$setUnion', '$size', '$sin', '$slice', '$split', '$sqrt', '$stdDevPop', '$stdDevSamp', '$strcasecmp', '$strLenBytes', '$strLenCP', '$substr', '$substrBytes', '$substrCP', '$subtract', '$sum', '$switch', '$tan', '$toBool', '$toDate', '$toDecimal', '$toDouble', '$toInt', '$toLong', '$toObjectId', '$toString', '$toLower', '$toUpper', '$trim', '$trunc', '$type', '$week', '$year', '$zip', '$count', '$dateAdd', '$dateDiff', '$dateSubtract', '$dateTrunc', '$getField', '$rand', '$sampleRate', '$setField', '$unsetField',\n    // aggregation pipeline query modifiers\n    '$comment', '$explain', '$hint', '$max', '$maxTimeMS', '$min', '$orderby', '$query', '$returnKey', '$showDiskLoc', '$natural'];\n    var builtinFunctions = ['ObjectId', 'Code', 'BinData', 'DBRef', 'Timestamp', 'NumberLong', 'NumberDecimal', 'MaxKey', 'MinKey', 'RegExp', 'ISODate', 'UUID'];\n    operators = operators.map(function (operator) {\n      return operator.replace('$', '\\\\$');\n    });\n    var operatorsSource = '(?:' + operators.join('|') + ')\\\\b';\n    Prism.languages.mongodb = Prism.languages.extend('javascript', {});\n    Prism.languages.insertBefore('mongodb', 'string', {\n      property: {\n        pattern: /(?:([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)(?=\\s*:)/,\n        greedy: true,\n        inside: {\n          keyword: RegExp('^([\\'\"])?' + operatorsSource + '(?:\\\\1)?$')\n        }\n      }\n    });\n    Prism.languages.mongodb.string.inside = {\n      url: {\n        // url pattern\n        pattern: /https?:\\/\\/[-\\w@:%.+~#=]{1,256}\\.[a-z0-9()]{1,6}\\b[-\\w()@:%+.~#?&/=]*/i,\n        greedy: true\n      },\n      entity: {\n        // ipv4\n        pattern: /\\b(?:(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\b/,\n        greedy: true\n      }\n    };\n    Prism.languages.insertBefore('mongodb', 'constant', {\n      builtin: {\n        pattern: RegExp('\\\\b(?:' + builtinFunctions.join('|') + ')\\\\b'),\n        alias: 'keyword'\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "mongodb", "displayName", "aliases", "Prism", "operators", "builtinFunctions", "map", "operator", "replace", "operatorsSource", "join", "languages", "extend", "insertBefore", "property", "pattern", "greedy", "inside", "keyword", "RegExp", "string", "url", "entity", "builtin", "alias"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/mongodb.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = mongodb\nmongodb.displayName = 'mongodb'\nmongodb.aliases = []\nfunction mongodb(Prism) {\n  ;(function (Prism) {\n    var operators = [\n      // query and projection\n      '$eq',\n      '$gt',\n      '$gte',\n      '$in',\n      '$lt',\n      '$lte',\n      '$ne',\n      '$nin',\n      '$and',\n      '$not',\n      '$nor',\n      '$or',\n      '$exists',\n      '$type',\n      '$expr',\n      '$jsonSchema',\n      '$mod',\n      '$regex',\n      '$text',\n      '$where',\n      '$geoIntersects',\n      '$geoWithin',\n      '$near',\n      '$nearSphere',\n      '$all',\n      '$elemMatch',\n      '$size',\n      '$bitsAllClear',\n      '$bitsAllSet',\n      '$bitsAnyClear',\n      '$bitsAnySet',\n      '$comment',\n      '$elemMatch',\n      '$meta',\n      '$slice', // update\n      '$currentDate',\n      '$inc',\n      '$min',\n      '$max',\n      '$mul',\n      '$rename',\n      '$set',\n      '$setOnInsert',\n      '$unset',\n      '$addToSet',\n      '$pop',\n      '$pull',\n      '$push',\n      '$pullAll',\n      '$each',\n      '$position',\n      '$slice',\n      '$sort',\n      '$bit', // aggregation pipeline stages\n      '$addFields',\n      '$bucket',\n      '$bucketAuto',\n      '$collStats',\n      '$count',\n      '$currentOp',\n      '$facet',\n      '$geoNear',\n      '$graphLookup',\n      '$group',\n      '$indexStats',\n      '$limit',\n      '$listLocalSessions',\n      '$listSessions',\n      '$lookup',\n      '$match',\n      '$merge',\n      '$out',\n      '$planCacheStats',\n      '$project',\n      '$redact',\n      '$replaceRoot',\n      '$replaceWith',\n      '$sample',\n      '$set',\n      '$skip',\n      '$sort',\n      '$sortByCount',\n      '$unionWith',\n      '$unset',\n      '$unwind',\n      '$setWindowFields', // aggregation pipeline operators\n      '$abs',\n      '$accumulator',\n      '$acos',\n      '$acosh',\n      '$add',\n      '$addToSet',\n      '$allElementsTrue',\n      '$and',\n      '$anyElementTrue',\n      '$arrayElemAt',\n      '$arrayToObject',\n      '$asin',\n      '$asinh',\n      '$atan',\n      '$atan2',\n      '$atanh',\n      '$avg',\n      '$binarySize',\n      '$bsonSize',\n      '$ceil',\n      '$cmp',\n      '$concat',\n      '$concatArrays',\n      '$cond',\n      '$convert',\n      '$cos',\n      '$dateFromParts',\n      '$dateToParts',\n      '$dateFromString',\n      '$dateToString',\n      '$dayOfMonth',\n      '$dayOfWeek',\n      '$dayOfYear',\n      '$degreesToRadians',\n      '$divide',\n      '$eq',\n      '$exp',\n      '$filter',\n      '$first',\n      '$floor',\n      '$function',\n      '$gt',\n      '$gte',\n      '$hour',\n      '$ifNull',\n      '$in',\n      '$indexOfArray',\n      '$indexOfBytes',\n      '$indexOfCP',\n      '$isArray',\n      '$isNumber',\n      '$isoDayOfWeek',\n      '$isoWeek',\n      '$isoWeekYear',\n      '$last',\n      '$last',\n      '$let',\n      '$literal',\n      '$ln',\n      '$log',\n      '$log10',\n      '$lt',\n      '$lte',\n      '$ltrim',\n      '$map',\n      '$max',\n      '$mergeObjects',\n      '$meta',\n      '$min',\n      '$millisecond',\n      '$minute',\n      '$mod',\n      '$month',\n      '$multiply',\n      '$ne',\n      '$not',\n      '$objectToArray',\n      '$or',\n      '$pow',\n      '$push',\n      '$radiansToDegrees',\n      '$range',\n      '$reduce',\n      '$regexFind',\n      '$regexFindAll',\n      '$regexMatch',\n      '$replaceOne',\n      '$replaceAll',\n      '$reverseArray',\n      '$round',\n      '$rtrim',\n      '$second',\n      '$setDifference',\n      '$setEquals',\n      '$setIntersection',\n      '$setIsSubset',\n      '$setUnion',\n      '$size',\n      '$sin',\n      '$slice',\n      '$split',\n      '$sqrt',\n      '$stdDevPop',\n      '$stdDevSamp',\n      '$strcasecmp',\n      '$strLenBytes',\n      '$strLenCP',\n      '$substr',\n      '$substrBytes',\n      '$substrCP',\n      '$subtract',\n      '$sum',\n      '$switch',\n      '$tan',\n      '$toBool',\n      '$toDate',\n      '$toDecimal',\n      '$toDouble',\n      '$toInt',\n      '$toLong',\n      '$toObjectId',\n      '$toString',\n      '$toLower',\n      '$toUpper',\n      '$trim',\n      '$trunc',\n      '$type',\n      '$week',\n      '$year',\n      '$zip',\n      '$count',\n      '$dateAdd',\n      '$dateDiff',\n      '$dateSubtract',\n      '$dateTrunc',\n      '$getField',\n      '$rand',\n      '$sampleRate',\n      '$setField',\n      '$unsetField', // aggregation pipeline query modifiers\n      '$comment',\n      '$explain',\n      '$hint',\n      '$max',\n      '$maxTimeMS',\n      '$min',\n      '$orderby',\n      '$query',\n      '$returnKey',\n      '$showDiskLoc',\n      '$natural'\n    ]\n    var builtinFunctions = [\n      'ObjectId',\n      'Code',\n      'BinData',\n      'DBRef',\n      'Timestamp',\n      'NumberLong',\n      'NumberDecimal',\n      'MaxKey',\n      'MinKey',\n      'RegExp',\n      'ISODate',\n      'UUID'\n    ]\n    operators = operators.map(function (operator) {\n      return operator.replace('$', '\\\\$')\n    })\n    var operatorsSource = '(?:' + operators.join('|') + ')\\\\b'\n    Prism.languages.mongodb = Prism.languages.extend('javascript', {})\n    Prism.languages.insertBefore('mongodb', 'string', {\n      property: {\n        pattern:\n          /(?:([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)(?=\\s*:)/,\n        greedy: true,\n        inside: {\n          keyword: RegExp('^([\\'\"])?' + operatorsSource + '(?:\\\\1)?$')\n        }\n      }\n    })\n    Prism.languages.mongodb.string.inside = {\n      url: {\n        // url pattern\n        pattern:\n          /https?:\\/\\/[-\\w@:%.+~#=]{1,256}\\.[a-z0-9()]{1,6}\\b[-\\w()@:%+.~#?&/=]*/i,\n        greedy: true\n      },\n      entity: {\n        // ipv4\n        pattern:\n          /\\b(?:(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\b/,\n        greedy: true\n      }\n    }\n    Prism.languages.insertBefore('mongodb', 'constant', {\n      builtin: {\n        pattern: RegExp('\\\\b(?:' + builtinFunctions.join('|') + ')\\\\b'),\n        alias: 'keyword'\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,SAAS,GAAG;IACd;IACA,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,SAAS,EACT,OAAO,EACP,OAAO,EACP,aAAa,EACb,MAAM,EACN,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,aAAa,EACb,MAAM,EACN,YAAY,EACZ,OAAO,EACP,eAAe,EACf,aAAa,EACb,eAAe,EACf,aAAa,EACb,UAAU,EACV,YAAY,EACZ,OAAO,EACP,QAAQ;IAAE;IACV,cAAc,EACd,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,SAAS,EACT,MAAM,EACN,cAAc,EACd,QAAQ,EACR,WAAW,EACX,MAAM,EACN,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,WAAW,EACX,QAAQ,EACR,OAAO,EACP,MAAM;IAAE;IACR,YAAY,EACZ,SAAS,EACT,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,cAAc,EACd,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,oBAAoB,EACpB,eAAe,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,iBAAiB,EACjB,UAAU,EACV,SAAS,EACT,cAAc,EACd,cAAc,EACd,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,kBAAkB;IAAE;IACpB,MAAM,EACN,cAAc,EACd,OAAO,EACP,QAAQ,EACR,MAAM,EACN,WAAW,EACX,kBAAkB,EAClB,MAAM,EACN,iBAAiB,EACjB,cAAc,EACd,gBAAgB,EAChB,OAAO,EACP,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,aAAa,EACb,WAAW,EACX,OAAO,EACP,MAAM,EACN,SAAS,EACT,eAAe,EACf,OAAO,EACP,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,SAAS,EACT,KAAK,EACL,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,KAAK,EACL,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,eAAe,EACf,eAAe,EACf,YAAY,EACZ,UAAU,EACV,WAAW,EACX,eAAe,EACf,UAAU,EACV,cAAc,EACd,OAAO,EACP,OAAO,EACP,MAAM,EACN,UAAU,EACV,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,eAAe,EACf,OAAO,EACP,MAAM,EACN,cAAc,EACd,SAAS,EACT,MAAM,EACN,QAAQ,EACR,WAAW,EACX,KAAK,EACL,MAAM,EACN,gBAAgB,EAChB,KAAK,EACL,MAAM,EACN,OAAO,EACP,mBAAmB,EACnB,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,eAAe,EACf,aAAa,EACb,aAAa,EACb,aAAa,EACb,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,WAAW,EACX,OAAO,EACP,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,aAAa,EACb,aAAa,EACb,cAAc,EACd,WAAW,EACX,SAAS,EACT,cAAc,EACd,WAAW,EACX,WAAW,EACX,MAAM,EACN,SAAS,EACT,MAAM,EACN,SAAS,EACT,SAAS,EACT,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,SAAS,EACT,aAAa,EACb,WAAW,EACX,UAAU,EACV,UAAU,EACV,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,QAAQ,EACR,UAAU,EACV,WAAW,EACX,eAAe,EACf,YAAY,EACZ,WAAW,EACX,OAAO,EACP,aAAa,EACb,WAAW,EACX,aAAa;IAAE;IACf,UAAU,EACV,UAAU,EACV,OAAO,EACP,MAAM,EACN,YAAY,EACZ,MAAM,EACN,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,UAAU,CACX;IACD,IAAIC,gBAAgB,GAAG,CACrB,UAAU,EACV,MAAM,EACN,SAAS,EACT,OAAO,EACP,WAAW,EACX,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,MAAM,CACP;IACDD,SAAS,GAAGA,SAAS,CAACE,GAAG,CAAC,UAAUC,QAAQ,EAAE;MAC5C,OAAOA,QAAQ,CAACC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,IAAIC,eAAe,GAAG,KAAK,GAAGL,SAAS,CAACM,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;IAC1DP,KAAK,CAACQ,SAAS,CAACX,OAAO,GAAGG,KAAK,CAACQ,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClET,KAAK,CAACQ,SAAS,CAACE,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE;MAChDC,QAAQ,EAAE;QACRC,OAAO,EACL,mHAAmH;QACrHC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,OAAO,EAAEC,MAAM,CAAC,WAAW,GAAGV,eAAe,GAAG,WAAW;QAC7D;MACF;IACF,CAAC,CAAC;IACFN,KAAK,CAACQ,SAAS,CAACX,OAAO,CAACoB,MAAM,CAACH,MAAM,GAAG;MACtCI,GAAG,EAAE;QACH;QACAN,OAAO,EACL,wEAAwE;QAC1EC,MAAM,EAAE;MACV,CAAC;MACDM,MAAM,EAAE;QACN;QACAP,OAAO,EACL,6EAA6E;QAC/EC,MAAM,EAAE;MACV;IACF,CAAC;IACDb,KAAK,CAACQ,SAAS,CAACE,YAAY,CAAC,SAAS,EAAE,UAAU,EAAE;MAClDU,OAAO,EAAE;QACPR,OAAO,EAAEI,MAAM,CAAC,QAAQ,GAAGd,gBAAgB,CAACK,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;QAC/Dc,KAAK,EAAE;MACT;IACF,CAAC,CAAC;EACJ,CAAC,EAAErB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}