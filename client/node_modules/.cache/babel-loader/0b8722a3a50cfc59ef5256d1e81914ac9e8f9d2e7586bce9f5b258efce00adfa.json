{"ast": null, "code": "'use strict';\n\nmodule.exports = markup;\nmarkup.displayName = 'markup';\nmarkup.aliases = ['html', 'mathml', 'svg', 'xml', 'ssml', 'atom', 'rss'];\nfunction markup(Prism) {\n  Prism.languages.markup = {\n    comment: {\n      pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n      greedy: true\n    },\n    prolog: {\n      pattern: /<\\?[\\s\\S]+?\\?>/,\n      greedy: true\n    },\n    doctype: {\n      // https://www.w3.org/TR/xml/#NT-doctypedecl\n      pattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n      greedy: true,\n      inside: {\n        'internal-subset': {\n          pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        string: {\n          pattern: /\"[^\"]*\"|'[^']*'/,\n          greedy: true\n        },\n        punctuation: /^<!|>$|[[\\]]/,\n        'doctype-tag': /^DOCTYPE/i,\n        name: /[^\\s<>'\"]+/\n      }\n    },\n    cdata: {\n      pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n      greedy: true\n    },\n    tag: {\n      pattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n      greedy: true,\n      inside: {\n        tag: {\n          pattern: /^<\\/?[^\\s>\\/]+/,\n          inside: {\n            punctuation: /^<\\/?/,\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        },\n        'special-attr': [],\n        'attr-value': {\n          pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n          inside: {\n            punctuation: [{\n              pattern: /^=/,\n              alias: 'attr-equals'\n            }, /\"|'/]\n          }\n        },\n        punctuation: /\\/?>/,\n        'attr-name': {\n          pattern: /[^\\s>\\/]+/,\n          inside: {\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        }\n      }\n    },\n    entity: [{\n      pattern: /&[\\da-z]{1,8};/i,\n      alias: 'named-entity'\n    }, /&#x?[\\da-f]{1,8};/i]\n  };\n  Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] = Prism.languages.markup['entity'];\n  Prism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup; // Plugin to make entity title show the real entity, idea by Roman Komarov\n  Prism.hooks.add('wrap', function (env) {\n    if (env.type === 'entity') {\n      env.attributes['title'] = env.content.value.replace(/&amp;/, '&');\n    }\n  });\n  Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n    /**\n     * Adds an inlined language to markup.\n     *\n     * An example of an inlined language is CSS with `<style>` tags.\n     *\n     * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addInlined('style', 'css');\n     */\n    value: function addInlined(tagName, lang) {\n      var includedCdataInside = {};\n      includedCdataInside['language-' + lang] = {\n        pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n        lookbehind: true,\n        inside: Prism.languages[lang]\n      };\n      includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n      var inside = {\n        'included-cdata': {\n          pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n          inside: includedCdataInside\n        }\n      };\n      inside['language-' + lang] = {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages[lang]\n      };\n      var def = {};\n      def[tagName] = {\n        pattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () {\n          return tagName;\n        }), 'i'),\n        lookbehind: true,\n        greedy: true,\n        inside: inside\n      };\n      Prism.languages.insertBefore('markup', 'cdata', def);\n    }\n  });\n  Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n    /**\n     * Adds an pattern to highlight languages embedded in HTML attributes.\n     *\n     * An example of an inlined language is CSS with `style` attributes.\n     *\n     * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addAttribute('style', 'css');\n     */\n    value: function (attrName, lang) {\n      Prism.languages.markup.tag.inside['special-attr'].push({\n        pattern: RegExp(/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source, 'i'),\n        lookbehind: true,\n        inside: {\n          'attr-name': /^[^\\s=]+/,\n          'attr-value': {\n            pattern: /=[\\s\\S]+/,\n            inside: {\n              value: {\n                pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n                lookbehind: true,\n                alias: [lang, 'language-' + lang],\n                inside: Prism.languages[lang]\n              },\n              punctuation: [{\n                pattern: /^=/,\n                alias: 'attr-equals'\n              }, /\"|'/]\n            }\n          }\n        }\n      });\n    }\n  });\n  Prism.languages.html = Prism.languages.markup;\n  Prism.languages.mathml = Prism.languages.markup;\n  Prism.languages.svg = Prism.languages.markup;\n  Prism.languages.xml = Prism.languages.extend('markup', {});\n  Prism.languages.ssml = Prism.languages.xml;\n  Prism.languages.atom = Prism.languages.xml;\n  Prism.languages.rss = Prism.languages.xml;\n}", "map": {"version": 3, "names": ["module", "exports", "markup", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "prolog", "doctype", "inside", "lookbehind", "string", "punctuation", "name", "cdata", "tag", "namespace", "alias", "entity", "hooks", "add", "env", "type", "attributes", "content", "value", "replace", "Object", "defineProperty", "addInlined", "tagName", "lang", "includedCdataInside", "def", "RegExp", "source", "insertBefore", "attrName", "push", "html", "mathml", "svg", "xml", "extend", "ssml", "atom", "rss"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/markup.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = markup\nmarkup.displayName = 'markup'\nmarkup.aliases = ['html', 'mathml', 'svg', 'xml', 'ssml', 'atom', 'rss']\nfunction markup(Prism) {\n  Prism.languages.markup = {\n    comment: {\n      pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n      greedy: true\n    },\n    prolog: {\n      pattern: /<\\?[\\s\\S]+?\\?>/,\n      greedy: true\n    },\n    doctype: {\n      // https://www.w3.org/TR/xml/#NT-doctypedecl\n      pattern:\n        /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n      greedy: true,\n      inside: {\n        'internal-subset': {\n          pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        string: {\n          pattern: /\"[^\"]*\"|'[^']*'/,\n          greedy: true\n        },\n        punctuation: /^<!|>$|[[\\]]/,\n        'doctype-tag': /^DOCTYPE/i,\n        name: /[^\\s<>'\"]+/\n      }\n    },\n    cdata: {\n      pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n      greedy: true\n    },\n    tag: {\n      pattern:\n        /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n      greedy: true,\n      inside: {\n        tag: {\n          pattern: /^<\\/?[^\\s>\\/]+/,\n          inside: {\n            punctuation: /^<\\/?/,\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        },\n        'special-attr': [],\n        'attr-value': {\n          pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n          inside: {\n            punctuation: [\n              {\n                pattern: /^=/,\n                alias: 'attr-equals'\n              },\n              /\"|'/\n            ]\n          }\n        },\n        punctuation: /\\/?>/,\n        'attr-name': {\n          pattern: /[^\\s>\\/]+/,\n          inside: {\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        }\n      }\n    },\n    entity: [\n      {\n        pattern: /&[\\da-z]{1,8};/i,\n        alias: 'named-entity'\n      },\n      /&#x?[\\da-f]{1,8};/i\n    ]\n  }\n  Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n    Prism.languages.markup['entity']\n  Prism.languages.markup['doctype'].inside['internal-subset'].inside =\n    Prism.languages.markup // Plugin to make entity title show the real entity, idea by Roman Komarov\n  Prism.hooks.add('wrap', function (env) {\n    if (env.type === 'entity') {\n      env.attributes['title'] = env.content.value.replace(/&amp;/, '&')\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n    /**\n     * Adds an inlined language to markup.\n     *\n     * An example of an inlined language is CSS with `<style>` tags.\n     *\n     * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addInlined('style', 'css');\n     */\n    value: function addInlined(tagName, lang) {\n      var includedCdataInside = {}\n      includedCdataInside['language-' + lang] = {\n        pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n        lookbehind: true,\n        inside: Prism.languages[lang]\n      }\n      includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i\n      var inside = {\n        'included-cdata': {\n          pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n          inside: includedCdataInside\n        }\n      }\n      inside['language-' + lang] = {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages[lang]\n      }\n      var def = {}\n      def[tagName] = {\n        pattern: RegExp(\n          /(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(\n            /__/g,\n            function () {\n              return tagName\n            }\n          ),\n          'i'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: inside\n      }\n      Prism.languages.insertBefore('markup', 'cdata', def)\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n    /**\n     * Adds an pattern to highlight languages embedded in HTML attributes.\n     *\n     * An example of an inlined language is CSS with `style` attributes.\n     *\n     * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addAttribute('style', 'css');\n     */\n    value: function (attrName, lang) {\n      Prism.languages.markup.tag.inside['special-attr'].push({\n        pattern: RegExp(\n          /(^|[\"'\\s])/.source +\n            '(?:' +\n            attrName +\n            ')' +\n            /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n          'i'\n        ),\n        lookbehind: true,\n        inside: {\n          'attr-name': /^[^\\s=]+/,\n          'attr-value': {\n            pattern: /=[\\s\\S]+/,\n            inside: {\n              value: {\n                pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n                lookbehind: true,\n                alias: [lang, 'language-' + lang],\n                inside: Prism.languages[lang]\n              },\n              punctuation: [\n                {\n                  pattern: /^=/,\n                  alias: 'attr-equals'\n                },\n                /\"|'/\n              ]\n            }\n          }\n        }\n      })\n    }\n  })\n  Prism.languages.html = Prism.languages.markup\n  Prism.languages.mathml = Prism.languages.markup\n  Prism.languages.svg = Prism.languages.markup\n  Prism.languages.xml = Prism.languages.extend('markup', {})\n  Prism.languages.ssml = Prism.languages.xml\n  Prism.languages.atom = Prism.languages.xml\n  Prism.languages.rss = Prism.languages.xml\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;AACxE,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE;MACPC,OAAO,EAAE,6BAA6B;MACtCC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE;IACV,CAAC;IACDE,OAAO,EAAE;MACP;MACAH,OAAO,EACL,sHAAsH;MACxHC,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACN,iBAAiB,EAAE;UACjBJ,OAAO,EAAE,4BAA4B;UACrCK,UAAU,EAAE,IAAI;UAChBJ,MAAM,EAAE,IAAI;UACZG,MAAM,EAAE,IAAI,CAAC;QACf,CAAC;QACDE,MAAM,EAAE;UACNN,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,EAAE;QACV,CAAC;QACDM,WAAW,EAAE,cAAc;QAC3B,aAAa,EAAE,WAAW;QAC1BC,IAAI,EAAE;MACR;IACF,CAAC;IACDC,KAAK,EAAE;MACLT,OAAO,EAAE,2BAA2B;MACpCC,MAAM,EAAE;IACV,CAAC;IACDS,GAAG,EAAE;MACHV,OAAO,EACL,sHAAsH;MACxHC,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACNM,GAAG,EAAE;UACHV,OAAO,EAAE,gBAAgB;UACzBI,MAAM,EAAE;YACNG,WAAW,EAAE,OAAO;YACpBI,SAAS,EAAE;UACb;QACF,CAAC;QACD,cAAc,EAAE,EAAE;QAClB,YAAY,EAAE;UACZX,OAAO,EAAE,oCAAoC;UAC7CI,MAAM,EAAE;YACNG,WAAW,EAAE,CACX;cACEP,OAAO,EAAE,IAAI;cACbY,KAAK,EAAE;YACT,CAAC,EACD,KAAK;UAET;QACF,CAAC;QACDL,WAAW,EAAE,MAAM;QACnB,WAAW,EAAE;UACXP,OAAO,EAAE,WAAW;UACpBI,MAAM,EAAE;YACNO,SAAS,EAAE;UACb;QACF;MACF;IACF,CAAC;IACDE,MAAM,EAAE,CACN;MACEb,OAAO,EAAE,iBAAiB;MAC1BY,KAAK,EAAE;IACT,CAAC,EACD,oBAAoB;EAExB,CAAC;EACDf,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,KAAK,CAAC,CAACU,MAAM,CAAC,YAAY,CAAC,CAACA,MAAM,CAAC,QAAQ,CAAC,GACjEP,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,QAAQ,CAAC;EAClCG,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,SAAS,CAAC,CAACU,MAAM,CAAC,iBAAiB,CAAC,CAACA,MAAM,GAChEP,KAAK,CAACC,SAAS,CAACJ,MAAM,EAAC;EACzBG,KAAK,CAACiB,KAAK,CAACC,GAAG,CAAC,MAAM,EAAE,UAAUC,GAAG,EAAE;IACrC,IAAIA,GAAG,CAACC,IAAI,KAAK,QAAQ,EAAE;MACzBD,GAAG,CAACE,UAAU,CAAC,OAAO,CAAC,GAAGF,GAAG,CAACG,OAAO,CAACC,KAAK,CAACC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;IACnE;EACF,CAAC,CAAC;EACFC,MAAM,CAACC,cAAc,CAAC1B,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACgB,GAAG,EAAE,YAAY,EAAE;IAC9D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIU,KAAK,EAAE,SAASI,UAAUA,CAACC,OAAO,EAAEC,IAAI,EAAE;MACxC,IAAIC,mBAAmB,GAAG,CAAC,CAAC;MAC5BA,mBAAmB,CAAC,WAAW,GAAGD,IAAI,CAAC,GAAG;QACxC1B,OAAO,EAAE,mCAAmC;QAC5CK,UAAU,EAAE,IAAI;QAChBD,MAAM,EAAEP,KAAK,CAACC,SAAS,CAAC4B,IAAI;MAC9B,CAAC;MACDC,mBAAmB,CAAC,OAAO,CAAC,GAAG,sBAAsB;MACrD,IAAIvB,MAAM,GAAG;QACX,gBAAgB,EAAE;UAChBJ,OAAO,EAAE,2BAA2B;UACpCI,MAAM,EAAEuB;QACV;MACF,CAAC;MACDvB,MAAM,CAAC,WAAW,GAAGsB,IAAI,CAAC,GAAG;QAC3B1B,OAAO,EAAE,SAAS;QAClBI,MAAM,EAAEP,KAAK,CAACC,SAAS,CAAC4B,IAAI;MAC9B,CAAC;MACD,IAAIE,GAAG,GAAG,CAAC,CAAC;MACZA,GAAG,CAACH,OAAO,CAAC,GAAG;QACbzB,OAAO,EAAE6B,MAAM,CACb,uFAAuF,CAACC,MAAM,CAACT,OAAO,CACpG,KAAK,EACL,YAAY;UACV,OAAOI,OAAO;QAChB,CACF,CAAC,EACD,GACF,CAAC;QACDpB,UAAU,EAAE,IAAI;QAChBJ,MAAM,EAAE,IAAI;QACZG,MAAM,EAAEA;MACV,CAAC;MACDP,KAAK,CAACC,SAAS,CAACiC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAEH,GAAG,CAAC;IACtD;EACF,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAAC1B,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACgB,GAAG,EAAE,cAAc,EAAE;IAChE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIU,KAAK,EAAE,SAAAA,CAAUY,QAAQ,EAAEN,IAAI,EAAE;MAC/B7B,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACgB,GAAG,CAACN,MAAM,CAAC,cAAc,CAAC,CAAC6B,IAAI,CAAC;QACrDjC,OAAO,EAAE6B,MAAM,CACb,YAAY,CAACC,MAAM,GACjB,KAAK,GACLE,QAAQ,GACR,GAAG,GACH,gDAAgD,CAACF,MAAM,EACzD,GACF,CAAC;QACDzB,UAAU,EAAE,IAAI;QAChBD,MAAM,EAAE;UACN,WAAW,EAAE,UAAU;UACvB,YAAY,EAAE;YACZJ,OAAO,EAAE,UAAU;YACnBI,MAAM,EAAE;cACNgB,KAAK,EAAE;gBACLpB,OAAO,EAAE,wCAAwC;gBACjDK,UAAU,EAAE,IAAI;gBAChBO,KAAK,EAAE,CAACc,IAAI,EAAE,WAAW,GAAGA,IAAI,CAAC;gBACjCtB,MAAM,EAAEP,KAAK,CAACC,SAAS,CAAC4B,IAAI;cAC9B,CAAC;cACDnB,WAAW,EAAE,CACX;gBACEP,OAAO,EAAE,IAAI;gBACbY,KAAK,EAAE;cACT,CAAC,EACD,KAAK;YAET;UACF;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFf,KAAK,CAACC,SAAS,CAACoC,IAAI,GAAGrC,KAAK,CAACC,SAAS,CAACJ,MAAM;EAC7CG,KAAK,CAACC,SAAS,CAACqC,MAAM,GAAGtC,KAAK,CAACC,SAAS,CAACJ,MAAM;EAC/CG,KAAK,CAACC,SAAS,CAACsC,GAAG,GAAGvC,KAAK,CAACC,SAAS,CAACJ,MAAM;EAC5CG,KAAK,CAACC,SAAS,CAACuC,GAAG,GAAGxC,KAAK,CAACC,SAAS,CAACwC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;EAC1DzC,KAAK,CAACC,SAAS,CAACyC,IAAI,GAAG1C,KAAK,CAACC,SAAS,CAACuC,GAAG;EAC1CxC,KAAK,CAACC,SAAS,CAAC0C,IAAI,GAAG3C,KAAK,CAACC,SAAS,CAACuC,GAAG;EAC1CxC,KAAK,CAACC,SAAS,CAAC2C,GAAG,GAAG5C,KAAK,CAACC,SAAS,CAACuC,GAAG;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}