{"ast": null, "code": "/*\nLanguage: DNS Zone\nAuthor: <PERSON> <<EMAIL>>\nCategory: config\nWebsite: https://en.wikipedia.org/wiki/Zone_file\n*/\n\n/** @type LanguageFn */\nfunction dns(hljs) {\n  return {\n    name: 'DNS Zone',\n    aliases: ['bind', 'zone'],\n    keywords: {\n      keyword: 'IN A AAAA AFSDB APL CAA CDNSKEY CDS CERT CNAME DHCID DLV DNAME DNSKEY DS HIP IPSECKEY KEY KX ' + 'LOC MX NAPTR NS NSEC NSEC3 NSEC3PARAM PTR RRSIG RP SIG SOA SRV SSHFP TA TKEY TLSA TSIG TXT'\n    },\n    contains: [hljs.COMMENT(';', '$', {\n      relevance: 0\n    }), {\n      className: 'meta',\n      begin: /^\\$(TTL|GENERATE|INCLUDE|ORIGIN)\\b/\n    },\n    // IPv6\n    {\n      className: 'number',\n      begin: '((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:)))\\\\b'\n    },\n    // IPv4\n    {\n      className: 'number',\n      begin: '((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\\\b'\n    }, hljs.inherit(hljs.NUMBER_MODE, {\n      begin: /\\b\\d+[dhwm]?/\n    })]\n  };\n}\nmodule.exports = dns;", "map": {"version": 3, "names": ["dns", "hljs", "name", "aliases", "keywords", "keyword", "contains", "COMMENT", "relevance", "className", "begin", "inherit", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/dns.js"], "sourcesContent": ["/*\nLanguage: DNS Zone\nAuthor: <PERSON> <<EMAIL>>\nCategory: config\nWebsite: https://en.wikipedia.org/wiki/Zone_file\n*/\n\n/** @type LanguageFn */\nfunction dns(hljs) {\n  return {\n    name: 'DNS Zone',\n    aliases: [\n      'bind',\n      'zone'\n    ],\n    keywords: {\n      keyword:\n        'IN A AAAA AFSDB APL CAA CDNSKEY CDS CERT CNAME DHCID DLV DNAME DNSKEY DS HIP IPSECKEY KEY KX ' +\n        'LOC MX NAPTR NS NSEC NSEC3 NSEC3PARAM PTR RRSIG RP SIG SOA SRV SSHFP TA TKEY TLSA TSIG TXT'\n    },\n    contains: [\n      hljs.COMMENT(';', '$', {\n        relevance: 0\n      }),\n      {\n        className: 'meta',\n        begin: /^\\$(TTL|GENERATE|INCLUDE|ORIGIN)\\b/\n      },\n      // IPv6\n      {\n        className: 'number',\n        begin: '((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:)))\\\\b'\n      },\n      // IPv4\n      {\n        className: 'number',\n        begin: '((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\\\b'\n      },\n      hljs.inherit(hljs.NUMBER_MODE, {\n        begin: /\\b\\d+[dhwm]?/\n      })\n    ]\n  };\n}\n\nmodule.exports = dns;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CACP,MAAM,EACN,MAAM,CACP;IACDC,QAAQ,EAAE;MACRC,OAAO,EACL,+FAA+F,GAC/F;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRL,IAAI,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;MACrBC,SAAS,EAAE;IACb,CAAC,CAAC,EACF;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACED,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACED,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC,EACDT,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,WAAW,EAAE;MAC7BF,KAAK,EAAE;IACT,CAAC,CAAC;EAEN,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAGd,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}