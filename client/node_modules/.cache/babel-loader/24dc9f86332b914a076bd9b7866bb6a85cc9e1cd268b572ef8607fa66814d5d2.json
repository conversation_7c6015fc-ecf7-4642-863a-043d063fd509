{"ast": null, "code": "/*\nLanguage: <PERSON><PERSON>k RouterOS script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Scripting host provides a way to automate some router maintenance tasks by means of executing user-defined scripts bounded to some event occurrence\nWebsite: https://wiki.mikrotik.com/wiki/Manual:Scripting\n*/\n\n// Colors from RouterOS terminal:\n//   green        - #0E9A00\n//   teal         - #0C9A9A\n//   purple       - #99069A\n//   light-brown  - #9A9900\n\nfunction routeros(hljs) {\n  const STATEMENTS = 'foreach do while for if from to step else on-error and or not in';\n\n  // Global commands: Every global command should start with \":\" token, otherwise it will be treated as variable.\n  const GLOBAL_COMMANDS = 'global local beep delay put len typeof pick log time set find environment terminal error execute parse resolve toarray tobool toid toip toip6 tonum tostr totime';\n\n  // Common commands: Following commands available from most sub-menus:\n  const COMMON_COMMANDS = 'add remove enable disable set get print export edit find run debug error info warning';\n  const LITERALS = 'true false yes no nothing nil null';\n  const OBJECTS = 'traffic-flow traffic-generator firewall scheduler aaa accounting address-list address align area bandwidth-server bfd bgp bridge client clock community config connection console customer default dhcp-client dhcp-server discovery dns e-mail ethernet filter firmware gps graphing group hardware health hotspot identity igmp-proxy incoming instance interface ip ipsec ipv6 irq l2tp-server lcd ldp logging mac-server mac-winbox mangle manual mirror mme mpls nat nd neighbor network note ntp ospf ospf-v3 ovpn-server page peer pim ping policy pool port ppp pppoe-client pptp-server prefix profile proposal proxy queue radius resource rip ripng route routing screen script security-profiles server service service-port settings shares smb sms sniffer snmp snooper socks sstp-server system tool tracking type upgrade upnp user-manager users user vlan secret vrrp watchdog web-access wireless pptp pppoe lan wan layer7-protocol lease simple raw';\n  const VAR = {\n    className: 'variable',\n    variants: [{\n      begin: /\\$[\\w\\d#@][\\w\\d_]*/\n    }, {\n      begin: /\\$\\{(.*?)\\}/\n    }]\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [hljs.BACKSLASH_ESCAPE, VAR, {\n      className: 'variable',\n      begin: /\\$\\(/,\n      end: /\\)/,\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }]\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  return {\n    name: 'Microtik RouterOS script',\n    aliases: ['mikrotik'],\n    case_insensitive: true,\n    keywords: {\n      $pattern: /:?[\\w-]+/,\n      literal: LITERALS,\n      keyword: STATEMENTS + ' :' + STATEMENTS.split(' ').join(' :') + ' :' + GLOBAL_COMMANDS.split(' ').join(' :')\n    },\n    contains: [{\n      // illegal syntax\n      variants: [{\n        // -- comment\n        begin: /\\/\\*/,\n        end: /\\*\\//\n      }, {\n        // Stan comment\n        begin: /\\/\\//,\n        end: /$/\n      }, {\n        // HTML tags\n        begin: /<\\//,\n        end: />/\n      }],\n      illegal: /./\n    }, hljs.COMMENT('^#', '$'), QUOTE_STRING, APOS_STRING, VAR,\n    // attribute=value\n    {\n      // > is to avoid matches with => in other grammars\n      begin: /[\\w-]+=([^\\s{}[\\]()>]+)/,\n      relevance: 0,\n      returnBegin: true,\n      contains: [{\n        className: 'attribute',\n        begin: /[^=]+/\n      }, {\n        begin: /=/,\n        endsWithParent: true,\n        relevance: 0,\n        contains: [QUOTE_STRING, APOS_STRING, VAR, {\n          className: 'literal',\n          begin: '\\\\b(' + LITERALS.split(' ').join('|') + ')\\\\b'\n        }, {\n          // Do not format unclassified values. Needed to exclude highlighting of values as built_in.\n          begin: /(\"[^\"]*\"|[^\\s{}[\\]]+)/\n        }\n        /*\n        {\n          // IPv4 addresses and subnets\n          className: 'number',\n          variants: [\n            {begin: IPADDR_wBITMASK+'(,'+IPADDR_wBITMASK+')*'}, //***********/24,*******/24\n            {begin: IPADDR+'-'+IPADDR},       // ***********-***********\n            {begin: IPADDR+'(,'+IPADDR+')*'}, // ***********,***********4,************,***********\n          ]\n        },\n        {\n          // MAC addresses and DHCP Client IDs\n          className: 'number',\n          begin: /\\b(1:)?([0-9A-Fa-f]{1,2}[:-]){5}([0-9A-Fa-f]){1,2}\\b/,\n        },\n        */]\n      }]\n    }, {\n      // HEX values\n      className: 'number',\n      begin: /\\*[0-9a-fA-F]+/\n    }, {\n      begin: '\\\\b(' + COMMON_COMMANDS.split(' ').join('|') + ')([\\\\s[(\\\\]|])',\n      returnBegin: true,\n      contains: [{\n        className: 'builtin-name',\n        // 'function',\n        begin: /\\w+/\n      }]\n    }, {\n      className: 'built_in',\n      variants: [{\n        begin: '(\\\\.\\\\./|/|\\\\s)((' + OBJECTS.split(' ').join('|') + ');?\\\\s)+'\n      }, {\n        begin: /\\.\\./,\n        relevance: 0\n      }]\n    }]\n  };\n}\nmodule.exports = routeros;", "map": {"version": 3, "names": ["routeros", "hljs", "STATEMENTS", "GLOBAL_COMMANDS", "COMMON_COMMANDS", "LITERALS", "OBJECTS", "VAR", "className", "variants", "begin", "QUOTE_STRING", "end", "contains", "BACKSLASH_ESCAPE", "APOS_STRING", "name", "aliases", "case_insensitive", "keywords", "$pattern", "literal", "keyword", "split", "join", "illegal", "COMMENT", "relevance", "returnBegin", "endsWithParent", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/routeros.js"], "sourcesContent": ["/*\nLanguage: <PERSON><PERSON>k RouterOS script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Scripting host provides a way to automate some router maintenance tasks by means of executing user-defined scripts bounded to some event occurrence\nWebsite: https://wiki.mikrotik.com/wiki/Manual:Scripting\n*/\n\n// Colors from RouterOS terminal:\n//   green        - #0E9A00\n//   teal         - #0C9A9A\n//   purple       - #99069A\n//   light-brown  - #9A9900\n\nfunction routeros(hljs) {\n  const STATEMENTS = 'foreach do while for if from to step else on-error and or not in';\n\n  // Global commands: Every global command should start with \":\" token, otherwise it will be treated as variable.\n  const GLOBAL_COMMANDS = 'global local beep delay put len typeof pick log time set find environment terminal error execute parse resolve toarray tobool toid toip toip6 tonum tostr totime';\n\n  // Common commands: Following commands available from most sub-menus:\n  const COMMON_COMMANDS = 'add remove enable disable set get print export edit find run debug error info warning';\n\n  const LITERALS = 'true false yes no nothing nil null';\n\n  const OBJECTS = 'traffic-flow traffic-generator firewall scheduler aaa accounting address-list address align area bandwidth-server bfd bgp bridge client clock community config connection console customer default dhcp-client dhcp-server discovery dns e-mail ethernet filter firmware gps graphing group hardware health hotspot identity igmp-proxy incoming instance interface ip ipsec ipv6 irq l2tp-server lcd ldp logging mac-server mac-winbox mangle manual mirror mme mpls nat nd neighbor network note ntp ospf ospf-v3 ovpn-server page peer pim ping policy pool port ppp pppoe-client pptp-server prefix profile proposal proxy queue radius resource rip ripng route routing screen script security-profiles server service service-port settings shares smb sms sniffer snmp snooper socks sstp-server system tool tracking type upgrade upnp user-manager users user vlan secret vrrp watchdog web-access wireless pptp pppoe lan wan layer7-protocol lease simple raw';\n\n  const VAR = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$[\\w\\d#@][\\w\\d_]*/\n      },\n      {\n        begin: /\\$\\{(.*?)\\}/\n      }\n    ]\n  };\n\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      {\n        className: 'variable',\n        begin: /\\$\\(/,\n        end: /\\)/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      }\n    ]\n  };\n\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n\n  return {\n    name: 'Microtik RouterOS script',\n    aliases: [\n      'mikrotik'\n    ],\n    case_insensitive: true,\n    keywords: {\n      $pattern: /:?[\\w-]+/,\n      literal: LITERALS,\n      keyword: STATEMENTS + ' :' + STATEMENTS.split(' ').join(' :') + ' :' + GLOBAL_COMMANDS.split(' ').join(' :')\n    },\n    contains: [\n      { // illegal syntax\n        variants: [\n          { // -- comment\n            begin: /\\/\\*/,\n            end: /\\*\\//\n          },\n          { // Stan comment\n            begin: /\\/\\//,\n            end: /$/\n          },\n          { // HTML tags\n            begin: /<\\//,\n            end: />/\n          }\n        ],\n        illegal: /./\n      },\n      hljs.COMMENT('^#', '$'),\n      QUOTE_STRING,\n      APOS_STRING,\n      VAR,\n      // attribute=value\n      {\n        // > is to avoid matches with => in other grammars\n        begin: /[\\w-]+=([^\\s{}[\\]()>]+)/,\n        relevance: 0,\n        returnBegin: true,\n        contains: [\n          {\n            className: 'attribute',\n            begin: /[^=]+/\n          },\n          {\n            begin: /=/,\n            endsWithParent: true,\n            relevance: 0,\n            contains: [\n              QUOTE_STRING,\n              APOS_STRING,\n              VAR,\n              {\n                className: 'literal',\n                begin: '\\\\b(' + LITERALS.split(' ').join('|') + ')\\\\b'\n              },\n              {\n                // Do not format unclassified values. Needed to exclude highlighting of values as built_in.\n                begin: /(\"[^\"]*\"|[^\\s{}[\\]]+)/\n              }\n              /*\n              {\n                // IPv4 addresses and subnets\n                className: 'number',\n                variants: [\n                  {begin: IPADDR_wBITMASK+'(,'+IPADDR_wBITMASK+')*'}, //***********/24,*******/24\n                  {begin: IPADDR+'-'+IPADDR},       // ***********-***********\n                  {begin: IPADDR+'(,'+IPADDR+')*'}, // ***********,***********4,************,***********\n                ]\n              },\n              {\n                // MAC addresses and DHCP Client IDs\n                className: 'number',\n                begin: /\\b(1:)?([0-9A-Fa-f]{1,2}[:-]){5}([0-9A-Fa-f]){1,2}\\b/,\n              },\n              */\n            ]\n          }\n        ]\n      },\n      {\n        // HEX values\n        className: 'number',\n        begin: /\\*[0-9a-fA-F]+/\n      },\n      {\n        begin: '\\\\b(' + COMMON_COMMANDS.split(' ').join('|') + ')([\\\\s[(\\\\]|])',\n        returnBegin: true,\n        contains: [\n          {\n            className: 'builtin-name', // 'function',\n            begin: /\\w+/\n          }\n        ]\n      },\n      {\n        className: 'built_in',\n        variants: [\n          {\n            begin: '(\\\\.\\\\./|/|\\\\s)((' + OBJECTS.split(' ').join('|') + ');?\\\\s)+'\n          },\n          {\n            begin: /\\.\\./,\n            relevance: 0\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = routeros;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,UAAU,GAAG,kEAAkE;;EAErF;EACA,MAAMC,eAAe,GAAG,kKAAkK;;EAE1L;EACA,MAAMC,eAAe,GAAG,uFAAuF;EAE/G,MAAMC,QAAQ,GAAG,oCAAoC;EAErD,MAAMC,OAAO,GAAG,06BAA06B;EAE17B,MAAMC,GAAG,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBH,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE,GAAG;IACVE,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACRZ,IAAI,CAACa,gBAAgB,EACrBP,GAAG,EACH;MACEC,SAAS,EAAE,UAAU;MACrBE,KAAK,EAAE,MAAM;MACbE,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,CAAEZ,IAAI,CAACa,gBAAgB;IACnC,CAAC;EAEL,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBP,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE,GAAG;IACVE,GAAG,EAAE;EACP,CAAC;EAED,OAAO;IACLI,IAAI,EAAE,0BAA0B;IAChCC,OAAO,EAAE,CACP,UAAU,CACX;IACDC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAEhB,QAAQ;MACjBiB,OAAO,EAAEpB,UAAU,GAAG,IAAI,GAAGA,UAAU,CAACqB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAGrB,eAAe,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI;IAC7G,CAAC;IACDX,QAAQ,EAAE,CACR;MAAE;MACAJ,QAAQ,EAAE,CACR;QAAE;QACAC,KAAK,EAAE,MAAM;QACbE,GAAG,EAAE;MACP,CAAC,EACD;QAAE;QACAF,KAAK,EAAE,MAAM;QACbE,GAAG,EAAE;MACP,CAAC,EACD;QAAE;QACAF,KAAK,EAAE,KAAK;QACZE,GAAG,EAAE;MACP,CAAC,CACF;MACDa,OAAO,EAAE;IACX,CAAC,EACDxB,IAAI,CAACyB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EACvBf,YAAY,EACZI,WAAW,EACXR,GAAG;IACH;IACA;MACE;MACAG,KAAK,EAAE,yBAAyB;MAChCiB,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,IAAI;MACjBf,QAAQ,EAAE,CACR;QACEL,SAAS,EAAE,WAAW;QACtBE,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE,GAAG;QACVmB,cAAc,EAAE,IAAI;QACpBF,SAAS,EAAE,CAAC;QACZd,QAAQ,EAAE,CACRF,YAAY,EACZI,WAAW,EACXR,GAAG,EACH;UACEC,SAAS,EAAE,SAAS;UACpBE,KAAK,EAAE,MAAM,GAAGL,QAAQ,CAACkB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG;QAClD,CAAC,EACD;UACE;UACAd,KAAK,EAAE;QACT;QACA;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAfc;MAiBJ,CAAC;IAEL,CAAC,EACD;MACE;MACAF,SAAS,EAAE,QAAQ;MACnBE,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,MAAM,GAAGN,eAAe,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB;MACvEI,WAAW,EAAE,IAAI;MACjBf,QAAQ,EAAE,CACR;QACEL,SAAS,EAAE,cAAc;QAAE;QAC3BE,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACD;MACEF,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,mBAAmB,GAAGJ,OAAO,CAACiB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG;MAC9D,CAAC,EACD;QACEd,KAAK,EAAE,MAAM;QACbiB,SAAS,EAAE;MACb,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAG/B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}