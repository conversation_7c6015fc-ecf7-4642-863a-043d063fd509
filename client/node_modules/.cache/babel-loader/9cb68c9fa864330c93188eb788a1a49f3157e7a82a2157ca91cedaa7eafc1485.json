{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport TextInput from './components/TextInput';\nimport PastedFilesList from './components/PastedFilesList';\nimport FileModal from './components/FileModal';\nimport Stats from './components/Stats';\nimport { pasteAPI } from './services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [pastedFiles, setPastedFiles] = useState([]);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // 加载文件列表\n  const loadFiles = async () => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.getList();\n      if (response.success) {\n        setPastedFiles(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to load files:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载统计信息\n  const loadStats = async () => {\n    try {\n      const response = await pasteAPI.getStats();\n      if (response.success) {\n        setStats(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to load stats:', error);\n    }\n  };\n\n  // 处理长文本粘贴\n  const handleLongTextPaste = async (content, filename) => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.upload(content, filename);\n      if (response.success) {\n        // 重新加载文件列表\n        await loadFiles();\n        await loadStats();\n        return response.data;\n      }\n      throw new Error(response.error || 'Upload failed');\n    } catch (error) {\n      console.error('Failed to upload:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 查看文件详情\n  const handleViewFile = async fileId => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.getFile(fileId);\n      if (response.success) {\n        setSelectedFile(response.data);\n        setIsModalOpen(true);\n      }\n    } catch (error) {\n      console.error('Failed to load file:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 删除文件\n  const handleDeleteFile = async fileId => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.deleteFile(fileId);\n      if (response.success) {\n        await loadFiles();\n        await loadStats();\n      }\n    } catch (error) {\n      console.error('Failed to delete file:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 关闭模态框\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedFile(null);\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadFiles();\n    loadStats();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCC4 Long Text Paste Demo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Paste long text content and see how it's handled like Claude.ai\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"App-main\",\n      children: [stats && /*#__PURE__*/_jsxDEV(Stats, {\n        stats: stats\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(TextInput, {\n        onLongTextPaste: handleLongTextPaste,\n        loading: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PastedFilesList, {\n        files: pastedFiles,\n        onViewFile: handleViewFile,\n        onDeleteFile: handleDeleteFile,\n        loading: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), isModalOpen && selectedFile && /*#__PURE__*/_jsxDEV(FileModal, {\n        file: selectedFile,\n        onClose: handleCloseModal\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"App-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Built with React + Node.js \\u2022 Inspired by Claude.ai's long text handling\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"lSKLJfl5QLz2kpxCoqbkjuWGKJo=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "TextInput", "PastedFilesList", "FileModal", "Stats", "pasteAPI", "jsxDEV", "_jsxDEV", "App", "_s", "pastedFiles", "setPastedFiles", "selectedFile", "setSelectedFile", "isModalOpen", "setIsModalOpen", "stats", "setStats", "loading", "setLoading", "loadFiles", "response", "getList", "success", "data", "error", "console", "loadStats", "getStats", "handleLongTextPaste", "content", "filename", "upload", "Error", "handleViewFile", "fileId", "getFile", "handleDeleteFile", "deleteFile", "handleCloseModal", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLongTextPaste", "files", "onViewFile", "onDeleteFile", "file", "onClose", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './App.css';\nimport TextInput from './components/TextInput';\nimport PastedFilesList from './components/PastedFilesList';\nimport FileModal from './components/FileModal';\nimport Stats from './components/Stats';\nimport { pasteAPI } from './services/api';\n\nfunction App() {\n  const [pastedFiles, setPastedFiles] = useState([]);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // 加载文件列表\n  const loadFiles = async () => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.getList();\n      if (response.success) {\n        setPastedFiles(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to load files:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载统计信息\n  const loadStats = async () => {\n    try {\n      const response = await pasteAPI.getStats();\n      if (response.success) {\n        setStats(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to load stats:', error);\n    }\n  };\n\n  // 处理长文本粘贴\n  const handleLongTextPaste = async (content, filename) => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.upload(content, filename);\n      if (response.success) {\n        // 重新加载文件列表\n        await loadFiles();\n        await loadStats();\n        return response.data;\n      }\n      throw new Error(response.error || 'Upload failed');\n    } catch (error) {\n      console.error('Failed to upload:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 查看文件详情\n  const handleViewFile = async (fileId) => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.getFile(fileId);\n      if (response.success) {\n        setSelectedFile(response.data);\n        setIsModalOpen(true);\n      }\n    } catch (error) {\n      console.error('Failed to load file:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 删除文件\n  const handleDeleteFile = async (fileId) => {\n    try {\n      setLoading(true);\n      const response = await pasteAPI.deleteFile(fileId);\n      if (response.success) {\n        await loadFiles();\n        await loadStats();\n      }\n    } catch (error) {\n      console.error('Failed to delete file:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 关闭模态框\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedFile(null);\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadFiles();\n    loadStats();\n  }, []);\n\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>📄 Long Text Paste Demo</h1>\n        <p>Paste long text content and see how it's handled like Claude.ai</p>\n      </header>\n\n      <main className=\"App-main\">\n        {/* 统计信息 */}\n        {stats && <Stats stats={stats} />}\n\n        {/* 文本输入区域 */}\n        <TextInput \n          onLongTextPaste={handleLongTextPaste}\n          loading={loading}\n        />\n\n        {/* 已粘贴文件列表 */}\n        <PastedFilesList\n          files={pastedFiles}\n          onViewFile={handleViewFile}\n          onDeleteFile={handleDeleteFile}\n          loading={loading}\n        />\n\n        {/* 文件详情模态框 */}\n        {isModalOpen && selectedFile && (\n          <FileModal\n            file={selectedFile}\n            onClose={handleCloseModal}\n          />\n        )}\n      </main>\n\n      <footer className=\"App-footer\">\n        <p>Built with React + Node.js • Inspired by Claude.ai's long text handling</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAClB,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,KAAK,MAAM,oBAAoB;AACtC,SAASC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAMqB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMhB,QAAQ,CAACiB,OAAO,CAAC,CAAC;MACzC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBZ,cAAc,CAACU,QAAQ,CAACG,IAAI,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMhB,QAAQ,CAACuB,QAAQ,CAAC,CAAC;MAC1C,IAAIP,QAAQ,CAACE,OAAO,EAAE;QACpBN,QAAQ,CAACI,QAAQ,CAACG,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAG,MAAAA,CAAOC,OAAO,EAAEC,QAAQ,KAAK;IACvD,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMhB,QAAQ,CAAC2B,MAAM,CAACF,OAAO,EAAEC,QAAQ,CAAC;MACzD,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMH,SAAS,CAAC,CAAC;QACjB,MAAMO,SAAS,CAAC,CAAC;QACjB,OAAON,QAAQ,CAACG,IAAI;MACtB;MACA,MAAM,IAAIS,KAAK,CAACZ,QAAQ,CAACI,KAAK,IAAI,eAAe,CAAC;IACpD,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK;IACb,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,cAAc,GAAG,MAAOC,MAAM,IAAK;IACvC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMhB,QAAQ,CAAC+B,OAAO,CAACD,MAAM,CAAC;MAC/C,IAAId,QAAQ,CAACE,OAAO,EAAE;QACpBV,eAAe,CAACQ,QAAQ,CAACG,IAAI,CAAC;QAC9BT,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkB,gBAAgB,GAAG,MAAOF,MAAM,IAAK;IACzC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMhB,QAAQ,CAACiC,UAAU,CAACH,MAAM,CAAC;MAClD,IAAId,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMH,SAAS,CAAC,CAAC;QACjB,MAAMO,SAAS,CAAC,CAAC;MACnB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxB,cAAc,CAAC,KAAK,CAAC;IACrBF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACAb,SAAS,CAAC,MAAM;IACdoB,SAAS,CAAC,CAAC;IACXO,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEpB,OAAA;IAAKiC,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBlC,OAAA;MAAQiC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5BlC,OAAA;QAAAkC,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCtC,OAAA;QAAAkC,QAAA,EAAG;MAA+D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eAETtC,OAAA;MAAMiC,SAAS,EAAC,UAAU;MAAAC,QAAA,GAEvBzB,KAAK,iBAAIT,OAAA,CAACH,KAAK;QAACY,KAAK,EAAEA;MAAM;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGjCtC,OAAA,CAACN,SAAS;QACR6C,eAAe,EAAEjB,mBAAoB;QACrCX,OAAO,EAAEA;MAAQ;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAGFtC,OAAA,CAACL,eAAe;QACd6C,KAAK,EAAErC,WAAY;QACnBsC,UAAU,EAAEd,cAAe;QAC3Be,YAAY,EAAEZ,gBAAiB;QAC/BnB,OAAO,EAAEA;MAAQ;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,EAGD/B,WAAW,IAAIF,YAAY,iBAC1BL,OAAA,CAACJ,SAAS;QACR+C,IAAI,EAAEtC,YAAa;QACnBuC,OAAO,EAAEZ;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEPtC,OAAA;MAAQiC,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BlC,OAAA;QAAAkC,QAAA,EAAG;MAAuE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACpC,EAAA,CAzIQD,GAAG;AAAA4C,EAAA,GAAH5C,GAAG;AA2IZ,eAAeA,GAAG;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}