{"ast": null, "code": "'use strict';\n\nvar refractorJson = require('./json.js');\nmodule.exports = json5;\njson5.displayName = 'json5';\njson5.aliases = [];\nfunction json5(Prism) {\n  Prism.register(refractorJson);\n  (function (Prism) {\n    var string = /(\"|')(?:\\\\(?:\\r\\n?|\\n|.)|(?!\\1)[^\\\\\\r\\n])*\\1/;\n    Prism.languages.json5 = Prism.languages.extend('json', {\n      property: [{\n        pattern: RegExp(string.source + '(?=\\\\s*:)'),\n        greedy: true\n      }, {\n        pattern: /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/,\n        alias: 'unquoted'\n      }],\n      string: {\n        pattern: string,\n        greedy: true\n      },\n      number: /[+-]?\\b(?:NaN|Infinity|0x[a-fA-F\\d]+)\\b|[+-]?(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[eE][+-]?\\d+\\b)?/\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractor<PERSON><PERSON>", "require", "module", "exports", "json5", "displayName", "aliases", "Prism", "register", "string", "languages", "extend", "property", "pattern", "RegExp", "source", "greedy", "alias", "number"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/json5.js"], "sourcesContent": ["'use strict'\nvar refractorJson = require('./json.js')\nmodule.exports = json5\njson5.displayName = 'json5'\njson5.aliases = []\nfunction json5(Prism) {\n  Prism.register(refractorJson)\n  ;(function (Prism) {\n    var string = /(\"|')(?:\\\\(?:\\r\\n?|\\n|.)|(?!\\1)[^\\\\\\r\\n])*\\1/\n    Prism.languages.json5 = Prism.languages.extend('json', {\n      property: [\n        {\n          pattern: RegExp(string.source + '(?=\\\\s*:)'),\n          greedy: true\n        },\n        {\n          pattern:\n            /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/,\n          alias: 'unquoted'\n        }\n      ],\n      string: {\n        pattern: string,\n        greedy: true\n      },\n      number:\n        /[+-]?\\b(?:NaN|Infinity|0x[a-fA-F\\d]+)\\b|[+-]?(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[eE][+-]?\\d+\\b)?/\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxCC,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACR,aAAa,CAAC;EAC5B,CAAC,UAAUO,KAAK,EAAE;IACjB,IAAIE,MAAM,GAAG,8CAA8C;IAC3DF,KAAK,CAACG,SAAS,CAACN,KAAK,GAAGG,KAAK,CAACG,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE;MACrDC,QAAQ,EAAE,CACR;QACEC,OAAO,EAAEC,MAAM,CAACL,MAAM,CAACM,MAAM,GAAG,WAAW,CAAC;QAC5CC,MAAM,EAAE;MACV,CAAC,EACD;QACEH,OAAO,EACL,gEAAgE;QAClEI,KAAK,EAAE;MACT,CAAC,CACF;MACDR,MAAM,EAAE;QACNI,OAAO,EAAEJ,MAAM;QACfO,MAAM,EAAE;MACV,CAAC;MACDE,MAAM,EACJ;IACJ,CAAC,CAAC;EACJ,CAAC,EAAEX,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}