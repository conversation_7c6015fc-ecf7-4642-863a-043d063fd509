{"ast": null, "code": "export { default } from './default-highlight';\nexport { default as LightAsync } from './light-async';\nexport { default as Light } from './light';\nexport { default as PrismAsyncLight } from './prism-async-light';\nexport { default as PrismAsync } from './prism-async';\nexport { default as PrismLight } from './prism-light';\nexport { default as Prism } from './prism';\nexport { default as createElement } from './create-element';", "map": {"version": 3, "names": ["default", "LightAsync", "Light", "PrismAsyncLight", "PrismAsync", "PrismLight", "Prism", "createElement"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/index.js"], "sourcesContent": ["export { default } from './default-highlight';\nexport { default as LightAsync } from './light-async';\nexport { default as Light } from './light';\nexport { default as PrismAsyncLight } from './prism-async-light';\nexport { default as PrismAsync } from './prism-async';\nexport { default as PrismLight } from './prism-light';\nexport { default as Prism } from './prism';\nexport { default as createElement } from './create-element';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,UAAU,QAAQ,eAAe;AACrD,SAASD,OAAO,IAAIE,KAAK,QAAQ,SAAS;AAC1C,SAASF,OAAO,IAAIG,eAAe,QAAQ,qBAAqB;AAChE,SAASH,OAAO,IAAII,UAAU,QAAQ,eAAe;AACrD,SAASJ,OAAO,IAAIK,UAAU,QAAQ,eAAe;AACrD,SAASL,OAAO,IAAIM,KAAK,QAAQ,SAAS;AAC1C,SAASN,OAAO,IAAIO,aAAa,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}