{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Fortran\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Fortran\nCategory: scientific\n*/\n\n/** @type LanguageFn */\nfunction fortran(hljs) {\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n  const COMMENT = {\n    variants: [hljs.COMMENT('!', '$', {\n      relevance: 0\n    }),\n    // allow FORTRAN 77 style comments\n    hljs.COMMENT('^C[ ]', '$', {\n      relevance: 0\n    }), hljs.COMMENT('^C$', '$', {\n      relevance: 0\n    })]\n  };\n\n  // regex in both fortran and irpf90 should match\n  const OPTIONAL_NUMBER_SUFFIX = /(_[a-z_\\d]+)?/;\n  const OPTIONAL_NUMBER_EXP = /([de][+-]?\\d+)?/;\n  const NUMBER = {\n    className: 'number',\n    variants: [{\n      begin: concat(/\\b\\d+/, /\\.(\\d*)/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n    }, {\n      begin: concat(/\\b\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n    }, {\n      begin: concat(/\\.\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n    }],\n    relevance: 0\n  };\n  const FUNCTION_DEF = {\n    className: 'function',\n    beginKeywords: 'subroutine function program',\n    illegal: '[${=\\\\n]',\n    contains: [hljs.UNDERSCORE_TITLE_MODE, PARAMS]\n  };\n  const STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n  };\n  const KEYWORDS = {\n    literal: '.False. .True.',\n    keyword: 'kind do concurrent local shared while private call intrinsic where elsewhere ' + 'type endtype endmodule endselect endinterface end enddo endif if forall endforall only contains default return stop then block endblock endassociate ' + 'public subroutine|10 function program .and. .or. .not. .le. .eq. .ge. .gt. .lt. ' + 'goto save else use module select case ' + 'access blank direct exist file fmt form formatted iostat name named nextrec number opened rec recl sequential status unformatted unit ' + 'continue format pause cycle exit ' + 'c_null_char c_alert c_backspace c_form_feed flush wait decimal round iomsg ' + 'synchronous nopass non_overridable pass protected volatile abstract extends import ' + 'non_intrinsic value deferred generic final enumerator class associate bind enum ' + 'c_int c_short c_long c_long_long c_signed_char c_size_t c_int8_t c_int16_t c_int32_t c_int64_t c_int_least8_t c_int_least16_t ' + 'c_int_least32_t c_int_least64_t c_int_fast8_t c_int_fast16_t c_int_fast32_t c_int_fast64_t c_intmax_t C_intptr_t c_float c_double ' + 'c_long_double c_float_complex c_double_complex c_long_double_complex c_bool c_char c_null_ptr c_null_funptr ' + 'c_new_line c_carriage_return c_horizontal_tab c_vertical_tab iso_c_binding c_loc c_funloc c_associated  c_f_pointer ' + 'c_ptr c_funptr iso_fortran_env character_storage_size error_unit file_storage_size input_unit iostat_end iostat_eor ' + 'numeric_storage_size output_unit c_f_procpointer ieee_arithmetic ieee_support_underflow_control ' + 'ieee_get_underflow_mode ieee_set_underflow_mode newunit contiguous recursive ' + 'pad position action delim readwrite eor advance nml interface procedure namelist include sequence elemental pure impure ' + 'integer real character complex logical codimension dimension allocatable|10 parameter ' + 'external implicit|10 none double precision assign intent optional pointer ' + 'target in out common equivalence data',\n    built_in: 'alog alog10 amax0 amax1 amin0 amin1 amod cabs ccos cexp clog csin csqrt dabs dacos dasin datan datan2 dcos dcosh ddim dexp dint ' + 'dlog dlog10 dmax1 dmin1 dmod dnint dsign dsin dsinh dsqrt dtan dtanh float iabs idim idint idnint ifix isign max0 max1 min0 min1 sngl ' + 'algama cdabs cdcos cdexp cdlog cdsin cdsqrt cqabs cqcos cqexp cqlog cqsin cqsqrt dcmplx dconjg derf derfc dfloat dgamma dimag dlgama ' + 'iqint qabs qacos qasin qatan qatan2 qcmplx qconjg qcos qcosh qdim qerf qerfc qexp qgamma qimag qlgama qlog qlog10 qmax1 qmin1 qmod ' + 'qnint qsign qsin qsinh qsqrt qtan qtanh abs acos aimag aint anint asin atan atan2 char cmplx conjg cos cosh exp ichar index int log ' + 'log10 max min nint sign sin sinh sqrt tan tanh print write dim lge lgt lle llt mod nullify allocate deallocate ' + 'adjustl adjustr all allocated any associated bit_size btest ceiling count cshift date_and_time digits dot_product ' + 'eoshift epsilon exponent floor fraction huge iand ibclr ibits ibset ieor ior ishft ishftc lbound len_trim matmul ' + 'maxexponent maxloc maxval merge minexponent minloc minval modulo mvbits nearest pack present product ' + 'radix random_number random_seed range repeat reshape rrspacing scale scan selected_int_kind selected_real_kind ' + 'set_exponent shape size spacing spread sum system_clock tiny transpose trim ubound unpack verify achar iachar transfer ' + 'dble entry dprod cpu_time command_argument_count get_command get_command_argument get_environment_variable is_iostat_end ' + 'ieee_arithmetic ieee_support_underflow_control ieee_get_underflow_mode ieee_set_underflow_mode ' + 'is_iostat_eor move_alloc new_line selected_char_kind same_type_as extends_type_of ' + 'acosh asinh atanh bessel_j0 bessel_j1 bessel_jn bessel_y0 bessel_y1 bessel_yn erf erfc erfc_scaled gamma log_gamma hypot norm2 ' + 'atomic_define atomic_ref execute_command_line leadz trailz storage_size merge_bits ' + 'bge bgt ble blt dshiftl dshiftr findloc iall iany iparity image_index lcobound ucobound maskl maskr ' + 'num_images parity popcnt poppar shifta shiftl shiftr this_image sync change team co_broadcast co_max co_min co_sum co_reduce'\n  };\n  return {\n    name: 'Fortran',\n    case_insensitive: true,\n    aliases: ['f90', 'f95'],\n    keywords: KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [STRING, FUNCTION_DEF,\n    // allow `C = value` for assignments so they aren't misdetected\n    // as Fortran 77 style comments\n    {\n      begin: /^C\\s*=(?!=)/,\n      relevance: 0\n    }, COMMENT, NUMBER]\n  };\n}\nmodule.exports = fortran;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "fortran", "hljs", "PARAMS", "className", "begin", "end", "COMMENT", "variants", "relevance", "OPTIONAL_NUMBER_SUFFIX", "OPTIONAL_NUMBER_EXP", "NUMBER", "FUNCTION_DEF", "beginKeywords", "illegal", "contains", "UNDERSCORE_TITLE_MODE", "STRING", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "KEYWORDS", "literal", "keyword", "built_in", "name", "case_insensitive", "aliases", "keywords", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/fortran.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Fortran\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Fortran\nCategory: scientific\n*/\n\n/** @type LanguageFn */\nfunction fortran(hljs) {\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT('!', '$', {\n        relevance: 0\n      }),\n      // allow FORTRAN 77 style comments\n      hljs.COMMENT('^C[ ]', '$', {\n        relevance: 0\n      }),\n      hljs.COMMENT('^C$', '$', {\n        relevance: 0\n      })\n    ]\n  };\n\n  // regex in both fortran and irpf90 should match\n  const OPTIONAL_NUMBER_SUFFIX = /(_[a-z_\\d]+)?/;\n  const OPTIONAL_NUMBER_EXP = /([de][+-]?\\d+)?/;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      {\n        begin: concat(/\\b\\d+/, /\\.(\\d*)/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      },\n      {\n        begin: concat(/\\b\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      },\n      {\n        begin: concat(/\\.\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      }\n    ],\n    relevance: 0\n  };\n\n  const FUNCTION_DEF = {\n    className: 'function',\n    beginKeywords: 'subroutine function program',\n    illegal: '[${=\\\\n]',\n    contains: [\n      hljs.UNDERSCORE_TITLE_MODE,\n      PARAMS\n    ]\n  };\n\n  const STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  const KEYWORDS = {\n    literal: '.False. .True.',\n    keyword: 'kind do concurrent local shared while private call intrinsic where elsewhere ' +\n      'type endtype endmodule endselect endinterface end enddo endif if forall endforall only contains default return stop then block endblock endassociate ' +\n      'public subroutine|10 function program .and. .or. .not. .le. .eq. .ge. .gt. .lt. ' +\n      'goto save else use module select case ' +\n      'access blank direct exist file fmt form formatted iostat name named nextrec number opened rec recl sequential status unformatted unit ' +\n      'continue format pause cycle exit ' +\n      'c_null_char c_alert c_backspace c_form_feed flush wait decimal round iomsg ' +\n      'synchronous nopass non_overridable pass protected volatile abstract extends import ' +\n      'non_intrinsic value deferred generic final enumerator class associate bind enum ' +\n      'c_int c_short c_long c_long_long c_signed_char c_size_t c_int8_t c_int16_t c_int32_t c_int64_t c_int_least8_t c_int_least16_t ' +\n      'c_int_least32_t c_int_least64_t c_int_fast8_t c_int_fast16_t c_int_fast32_t c_int_fast64_t c_intmax_t C_intptr_t c_float c_double ' +\n      'c_long_double c_float_complex c_double_complex c_long_double_complex c_bool c_char c_null_ptr c_null_funptr ' +\n      'c_new_line c_carriage_return c_horizontal_tab c_vertical_tab iso_c_binding c_loc c_funloc c_associated  c_f_pointer ' +\n      'c_ptr c_funptr iso_fortran_env character_storage_size error_unit file_storage_size input_unit iostat_end iostat_eor ' +\n      'numeric_storage_size output_unit c_f_procpointer ieee_arithmetic ieee_support_underflow_control ' +\n      'ieee_get_underflow_mode ieee_set_underflow_mode newunit contiguous recursive ' +\n      'pad position action delim readwrite eor advance nml interface procedure namelist include sequence elemental pure impure ' +\n      'integer real character complex logical codimension dimension allocatable|10 parameter ' +\n      'external implicit|10 none double precision assign intent optional pointer ' +\n      'target in out common equivalence data',\n    built_in: 'alog alog10 amax0 amax1 amin0 amin1 amod cabs ccos cexp clog csin csqrt dabs dacos dasin datan datan2 dcos dcosh ddim dexp dint ' +\n      'dlog dlog10 dmax1 dmin1 dmod dnint dsign dsin dsinh dsqrt dtan dtanh float iabs idim idint idnint ifix isign max0 max1 min0 min1 sngl ' +\n      'algama cdabs cdcos cdexp cdlog cdsin cdsqrt cqabs cqcos cqexp cqlog cqsin cqsqrt dcmplx dconjg derf derfc dfloat dgamma dimag dlgama ' +\n      'iqint qabs qacos qasin qatan qatan2 qcmplx qconjg qcos qcosh qdim qerf qerfc qexp qgamma qimag qlgama qlog qlog10 qmax1 qmin1 qmod ' +\n      'qnint qsign qsin qsinh qsqrt qtan qtanh abs acos aimag aint anint asin atan atan2 char cmplx conjg cos cosh exp ichar index int log ' +\n      'log10 max min nint sign sin sinh sqrt tan tanh print write dim lge lgt lle llt mod nullify allocate deallocate ' +\n      'adjustl adjustr all allocated any associated bit_size btest ceiling count cshift date_and_time digits dot_product ' +\n      'eoshift epsilon exponent floor fraction huge iand ibclr ibits ibset ieor ior ishft ishftc lbound len_trim matmul ' +\n      'maxexponent maxloc maxval merge minexponent minloc minval modulo mvbits nearest pack present product ' +\n      'radix random_number random_seed range repeat reshape rrspacing scale scan selected_int_kind selected_real_kind ' +\n      'set_exponent shape size spacing spread sum system_clock tiny transpose trim ubound unpack verify achar iachar transfer ' +\n      'dble entry dprod cpu_time command_argument_count get_command get_command_argument get_environment_variable is_iostat_end ' +\n      'ieee_arithmetic ieee_support_underflow_control ieee_get_underflow_mode ieee_set_underflow_mode ' +\n      'is_iostat_eor move_alloc new_line selected_char_kind same_type_as extends_type_of ' +\n      'acosh asinh atanh bessel_j0 bessel_j1 bessel_jn bessel_y0 bessel_y1 bessel_yn erf erfc erfc_scaled gamma log_gamma hypot norm2 ' +\n      'atomic_define atomic_ref execute_command_line leadz trailz storage_size merge_bits ' +\n      'bge bgt ble blt dshiftl dshiftr findloc iall iany iparity image_index lcobound ucobound maskl maskr ' +\n      'num_images parity popcnt poppar shifta shiftl shiftr this_image sync change team co_broadcast co_max co_min co_sum co_reduce'\n  };\n  return {\n    name: 'Fortran',\n    case_insensitive: true,\n    aliases: [\n      'f90',\n      'f95'\n    ],\n    keywords: KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [\n      STRING,\n      FUNCTION_DEF,\n      // allow `C = value` for assignments so they aren't misdetected\n      // as Fortran 77 style comments\n      {\n        begin: /^C\\s*=(?!=)/,\n        relevance: 0\n      },\n      COMMENT,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = fortran;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,OAAO,GAAG;IACdC,QAAQ,EAAE,CACRN,IAAI,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;MACrBE,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACAP,IAAI,CAACK,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE;MACzBE,SAAS,EAAE;IACb,CAAC,CAAC,EACFP,IAAI,CAACK,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE;MACvBE,SAAS,EAAE;IACb,CAAC,CAAC;EAEN,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAG,eAAe;EAC9C,MAAMC,mBAAmB,GAAG,iBAAiB;EAC7C,MAAMC,MAAM,GAAG;IACbR,SAAS,EAAE,QAAQ;IACnBI,QAAQ,EAAE,CACR;MACEH,KAAK,EAAEV,MAAM,CAAC,OAAO,EAAE,SAAS,EAAEgB,mBAAmB,EAAED,sBAAsB;IAC/E,CAAC,EACD;MACEL,KAAK,EAAEV,MAAM,CAAC,OAAO,EAAEgB,mBAAmB,EAAED,sBAAsB;IACpE,CAAC,EACD;MACEL,KAAK,EAAEV,MAAM,CAAC,OAAO,EAAEgB,mBAAmB,EAAED,sBAAsB;IACpE,CAAC,CACF;IACDD,SAAS,EAAE;EACb,CAAC;EAED,MAAMI,YAAY,GAAG;IACnBT,SAAS,EAAE,UAAU;IACrBU,aAAa,EAAE,6BAA6B;IAC5CC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,CACRd,IAAI,CAACe,qBAAqB,EAC1Bd,MAAM;EAEV,CAAC;EAED,MAAMe,MAAM,GAAG;IACbd,SAAS,EAAE,QAAQ;IACnBK,SAAS,EAAE,CAAC;IACZD,QAAQ,EAAE,CACRN,IAAI,CAACiB,gBAAgB,EACrBjB,IAAI,CAACkB,iBAAiB;EAE1B,CAAC;EAED,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,gBAAgB;IACzBC,OAAO,EAAE,+EAA+E,GACtF,uJAAuJ,GACvJ,kFAAkF,GAClF,wCAAwC,GACxC,wIAAwI,GACxI,mCAAmC,GACnC,6EAA6E,GAC7E,qFAAqF,GACrF,kFAAkF,GAClF,gIAAgI,GAChI,oIAAoI,GACpI,8GAA8G,GAC9G,sHAAsH,GACtH,sHAAsH,GACtH,kGAAkG,GAClG,+EAA+E,GAC/E,0HAA0H,GAC1H,wFAAwF,GACxF,4EAA4E,GAC5E,uCAAuC;IACzCC,QAAQ,EAAE,kIAAkI,GAC1I,wIAAwI,GACxI,uIAAuI,GACvI,qIAAqI,GACrI,sIAAsI,GACtI,iHAAiH,GACjH,oHAAoH,GACpH,mHAAmH,GACnH,uGAAuG,GACvG,iHAAiH,GACjH,yHAAyH,GACzH,2HAA2H,GAC3H,iGAAiG,GACjG,oFAAoF,GACpF,iIAAiI,GACjI,qFAAqF,GACrF,sGAAsG,GACtG;EACJ,CAAC;EACD,OAAO;IACLC,IAAI,EAAE,SAAS;IACfC,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,CACP,KAAK,EACL,KAAK,CACN;IACDC,QAAQ,EAAEP,QAAQ;IAClBN,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,CACRE,MAAM,EACNL,YAAY;IACZ;IACA;IACA;MACER,KAAK,EAAE,aAAa;MACpBI,SAAS,EAAE;IACb,CAAC,EACDF,OAAO,EACPK,MAAM;EAEV,CAAC;AACH;AAEAiB,MAAM,CAACC,OAAO,GAAG7B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}