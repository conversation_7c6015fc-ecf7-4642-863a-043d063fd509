{"ast": null, "code": "'use strict';\n\nvar normalize = require('./normalize');\nvar DefinedInfo = require('./lib/util/defined-info');\nvar Info = require('./lib/util/info');\nvar data = 'data';\nmodule.exports = find;\nvar valid = /^data[-\\w.:]+$/i;\nvar dash = /-[a-z]/g;\nvar cap = /[A-Z]/g;\nfunction find(schema, value) {\n  var normal = normalize(value);\n  var prop = value;\n  var Type = Info;\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]];\n  }\n  if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      prop = datasetToProperty(value);\n    } else {\n      value = datasetToAttribute(value);\n    }\n    Type = DefinedInfo;\n  }\n  return new Type(prop, value);\n}\nfunction datasetToProperty(attribute) {\n  var value = attribute.slice(5).replace(dash, camelcase);\n  return data + value.charAt(0).toUpperCase() + value.slice(1);\n}\nfunction datasetToAttribute(property) {\n  var value = property.slice(4);\n  if (dash.test(value)) {\n    return property;\n  }\n  value = value.replace(cap, kebab);\n  if (value.charAt(0) !== '-') {\n    value = '-' + value;\n  }\n  return data + value;\n}\nfunction kebab($0) {\n  return '-' + $0.toLowerCase();\n}\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase();\n}", "map": {"version": 3, "names": ["normalize", "require", "DefinedInfo", "Info", "data", "module", "exports", "find", "valid", "dash", "cap", "schema", "value", "normal", "prop", "Type", "property", "length", "slice", "test", "char<PERSON>t", "datasetToProperty", "datasetToAttribute", "attribute", "replace", "camelcase", "toUpperCase", "kebab", "$0", "toLowerCase"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/find.js"], "sourcesContent": ["'use strict'\n\nvar normalize = require('./normalize')\nvar DefinedInfo = require('./lib/util/defined-info')\nvar Info = require('./lib/util/info')\n\nvar data = 'data'\n\nmodule.exports = find\n\nvar valid = /^data[-\\w.:]+$/i\nvar dash = /-[a-z]/g\nvar cap = /[A-Z]/g\n\nfunction find(schema, value) {\n  var normal = normalize(value)\n  var prop = value\n  var Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      prop = datasetToProperty(value)\n    } else {\n      value = datasetToAttribute(value)\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\nfunction datasetToProperty(attribute) {\n  var value = attribute.slice(5).replace(dash, camelcase)\n  return data + value.charAt(0).toUpperCase() + value.slice(1)\n}\n\nfunction datasetToAttribute(property) {\n  var value = property.slice(4)\n\n  if (dash.test(value)) {\n    return property\n  }\n\n  value = value.replace(cap, kebab)\n\n  if (value.charAt(0) !== '-') {\n    value = '-' + value\n  }\n\n  return data + value\n}\n\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AACtC,IAAIC,WAAW,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACpD,IAAIE,IAAI,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AAErC,IAAIG,IAAI,GAAG,MAAM;AAEjBC,MAAM,CAACC,OAAO,GAAGC,IAAI;AAErB,IAAIC,KAAK,GAAG,iBAAiB;AAC7B,IAAIC,IAAI,GAAG,SAAS;AACpB,IAAIC,GAAG,GAAG,QAAQ;AAElB,SAASH,IAAIA,CAACI,MAAM,EAAEC,KAAK,EAAE;EAC3B,IAAIC,MAAM,GAAGb,SAAS,CAACY,KAAK,CAAC;EAC7B,IAAIE,IAAI,GAAGF,KAAK;EAChB,IAAIG,IAAI,GAAGZ,IAAI;EAEf,IAAIU,MAAM,IAAIF,MAAM,CAACE,MAAM,EAAE;IAC3B,OAAOF,MAAM,CAACK,QAAQ,CAACL,MAAM,CAACE,MAAM,CAACA,MAAM,CAAC,CAAC;EAC/C;EAEA,IAAIA,MAAM,CAACI,MAAM,GAAG,CAAC,IAAIJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKd,IAAI,IAAII,KAAK,CAACW,IAAI,CAACP,KAAK,CAAC,EAAE;IACzE;IACA,IAAIA,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC3BN,IAAI,GAAGO,iBAAiB,CAACT,KAAK,CAAC;IACjC,CAAC,MAAM;MACLA,KAAK,GAAGU,kBAAkB,CAACV,KAAK,CAAC;IACnC;IAEAG,IAAI,GAAGb,WAAW;EACpB;EAEA,OAAO,IAAIa,IAAI,CAACD,IAAI,EAAEF,KAAK,CAAC;AAC9B;AAEA,SAASS,iBAAiBA,CAACE,SAAS,EAAE;EACpC,IAAIX,KAAK,GAAGW,SAAS,CAACL,KAAK,CAAC,CAAC,CAAC,CAACM,OAAO,CAACf,IAAI,EAAEgB,SAAS,CAAC;EACvD,OAAOrB,IAAI,GAAGQ,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC,GAAGd,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;AAC9D;AAEA,SAASI,kBAAkBA,CAACN,QAAQ,EAAE;EACpC,IAAIJ,KAAK,GAAGI,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC;EAE7B,IAAIT,IAAI,CAACU,IAAI,CAACP,KAAK,CAAC,EAAE;IACpB,OAAOI,QAAQ;EACjB;EAEAJ,KAAK,GAAGA,KAAK,CAACY,OAAO,CAACd,GAAG,EAAEiB,KAAK,CAAC;EAEjC,IAAIf,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3BR,KAAK,GAAG,GAAG,GAAGA,KAAK;EACrB;EAEA,OAAOR,IAAI,GAAGQ,KAAK;AACrB;AAEA,SAASe,KAAKA,CAACC,EAAE,EAAE;EACjB,OAAO,GAAG,GAAGA,EAAE,CAACC,WAAW,CAAC,CAAC;AAC/B;AAEA,SAASJ,SAASA,CAACG,EAAE,EAAE;EACrB,OAAOA,EAAE,CAACR,MAAM,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}