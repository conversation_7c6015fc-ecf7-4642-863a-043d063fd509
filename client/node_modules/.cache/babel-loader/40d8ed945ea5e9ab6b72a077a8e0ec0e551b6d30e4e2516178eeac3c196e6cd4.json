{"ast": null, "code": "/*\n Language: dsconfig\n Description: dsconfig batch configuration language for LDAP directory servers\n Contributors: <PERSON> <<EMAIL>>\n Category: enterprise, config\n */\n\n/** @type LanguageFn */\nfunction dsconfig(hljs) {\n  const QUOTED_PROPERTY = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/\n  };\n  const APOS_PROPERTY = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const UNQUOTED_PROPERTY = {\n    className: 'string',\n    begin: /[\\w\\-?]+:\\w+/,\n    end: /\\W/,\n    relevance: 0\n  };\n  const VALUELESS_PROPERTY = {\n    className: 'string',\n    begin: /\\w+(\\-\\w+)*/,\n    end: /(?=\\W)/,\n    relevance: 0\n  };\n  return {\n    keywords: 'dsconfig',\n    contains: [{\n      className: 'keyword',\n      begin: '^dsconfig',\n      end: /\\s/,\n      excludeEnd: true,\n      relevance: 10\n    }, {\n      className: 'built_in',\n      begin: /(list|create|get|set|delete)-(\\w+)/,\n      end: /\\s/,\n      excludeEnd: true,\n      illegal: '!@#$%^&*()',\n      relevance: 10\n    }, {\n      className: 'built_in',\n      begin: /--(\\w+)/,\n      end: /\\s/,\n      excludeEnd: true\n    }, QUOTED_PROPERTY, APOS_PROPERTY, UNQUOTED_PROPERTY, VALUELESS_PROPERTY, hljs.HASH_COMMENT_MODE]\n  };\n}\nmodule.exports = dsconfig;", "map": {"version": 3, "names": ["dsconfig", "hljs", "QUOTED_PROPERTY", "className", "begin", "end", "APOS_PROPERTY", "UNQUOTED_PROPERTY", "relevance", "VALUELESS_PROPERTY", "keywords", "contains", "excludeEnd", "illegal", "HASH_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/dsconfig.js"], "sourcesContent": ["/*\n Language: dsconfig\n Description: dsconfig batch configuration language for LDAP directory servers\n Contributors: <PERSON> <<EMAIL>>\n Category: enterprise, config\n */\n\n /** @type LanguageFn */\nfunction dsconfig(hljs) {\n  const QUOTED_PROPERTY = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/\n  };\n  const APOS_PROPERTY = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const UNQUOTED_PROPERTY = {\n    className: 'string',\n    begin: /[\\w\\-?]+:\\w+/,\n    end: /\\W/,\n    relevance: 0\n  };\n  const VALUELESS_PROPERTY = {\n    className: 'string',\n    begin: /\\w+(\\-\\w+)*/,\n    end: /(?=\\W)/,\n    relevance: 0\n  };\n\n  return {\n    keywords: 'dsconfig',\n    contains: [\n      {\n        className: 'keyword',\n        begin: '^dsconfig',\n        end: /\\s/,\n        excludeEnd: true,\n        relevance: 10\n      },\n      {\n        className: 'built_in',\n        begin: /(list|create|get|set|delete)-(\\w+)/,\n        end: /\\s/,\n        excludeEnd: true,\n        illegal: '!@#$%^&*()',\n        relevance: 10\n      },\n      {\n        className: 'built_in',\n        begin: /--(\\w+)/,\n        end: /\\s/,\n        excludeEnd: true\n      },\n      QUOTED_PROPERTY,\n      APOS_PROPERTY,\n      UNQUOTED_PROPERTY,\n      VALUELESS_PROPERTY,\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = dsconfig;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEC;AACD,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,eAAe,GAAG;IACtBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE;EACP,CAAC;EACD,MAAMC,aAAa,GAAG;IACpBH,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE;EACP,CAAC;EACD,MAAME,iBAAiB,GAAG;IACxBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,cAAc;IACrBC,GAAG,EAAE,IAAI;IACTG,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,kBAAkB,GAAG;IACzBN,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE,QAAQ;IACbG,SAAS,EAAE;EACb,CAAC;EAED,OAAO;IACLE,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,CACR;MACER,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,WAAW;MAClBC,GAAG,EAAE,IAAI;MACTO,UAAU,EAAE,IAAI;MAChBJ,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,oCAAoC;MAC3CC,GAAG,EAAE,IAAI;MACTO,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,YAAY;MACrBL,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,IAAI;MACTO,UAAU,EAAE;IACd,CAAC,EACDV,eAAe,EACfI,aAAa,EACbC,iBAAiB,EACjBE,kBAAkB,EAClBR,IAAI,CAACa,iBAAiB;EAE1B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGhB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}