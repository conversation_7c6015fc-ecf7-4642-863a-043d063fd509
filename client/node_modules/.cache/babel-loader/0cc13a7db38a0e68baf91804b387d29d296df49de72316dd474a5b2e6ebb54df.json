{"ast": null, "code": "'use strict';\n\nmodule.exports = xmlDoc;\nxmlDoc.displayName = 'xmlDoc';\nxmlDoc.aliases = [];\nfunction xmlDoc(Prism) {\n  ;\n  (function (Prism) {\n    /**\n     * If the given language is present, it will insert the given doc comment grammar token into it.\n     *\n     * @param {string} lang\n     * @param {any} docComment\n     */\n    function insertDocComment(lang, docComment) {\n      if (Prism.languages[lang]) {\n        Prism.languages.insertBefore(lang, 'comment', {\n          'doc-comment': docComment\n        });\n      }\n    }\n    var tag = Prism.languages.markup.tag;\n    var slashDocComment = {\n      pattern: /\\/\\/\\/.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    };\n    var tickDocComment = {\n      pattern: /'''.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    };\n    insertDocComment('csharp', slashDocComment);\n    insertDocComment('fsharp', slashDocComment);\n    insertDocComment('vbnet', tickDocComment);\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "xmlDoc", "displayName", "aliases", "Prism", "insertDocComment", "lang", "docComment", "languages", "insertBefore", "tag", "markup", "slashDocComment", "pattern", "greedy", "alias", "inside", "tickDocComment"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/xml-doc.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = xmlDoc\nxmlDoc.displayName = 'xmlDoc'\nxmlDoc.aliases = []\nfunction xmlDoc(Prism) {\n  ;(function (Prism) {\n    /**\n     * If the given language is present, it will insert the given doc comment grammar token into it.\n     *\n     * @param {string} lang\n     * @param {any} docComment\n     */\n    function insertDocComment(lang, docComment) {\n      if (Prism.languages[lang]) {\n        Prism.languages.insertBefore(lang, 'comment', {\n          'doc-comment': docComment\n        })\n      }\n    }\n    var tag = Prism.languages.markup.tag\n    var slashDocComment = {\n      pattern: /\\/\\/\\/.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    }\n    var tickDocComment = {\n      pattern: /'''.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    }\n    insertDocComment('csharp', slashDocComment)\n    insertDocComment('fsharp', slashDocComment)\n    insertDocComment('vbnet', tickDocComment)\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,UAAU,EAAE;MAC1C,IAAIH,KAAK,CAACI,SAAS,CAACF,IAAI,CAAC,EAAE;QACzBF,KAAK,CAACI,SAAS,CAACC,YAAY,CAACH,IAAI,EAAE,SAAS,EAAE;UAC5C,aAAa,EAAEC;QACjB,CAAC,CAAC;MACJ;IACF;IACA,IAAIG,GAAG,GAAGN,KAAK,CAACI,SAAS,CAACG,MAAM,CAACD,GAAG;IACpC,IAAIE,eAAe,GAAG;MACpBC,OAAO,EAAE,UAAU;MACnBC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE;QACNN,GAAG,EAAEA;MACP;IACF,CAAC;IACD,IAAIO,cAAc,GAAG;MACnBJ,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE;QACNN,GAAG,EAAEA;MACP;IACF,CAAC;IACDL,gBAAgB,CAAC,QAAQ,EAAEO,eAAe,CAAC;IAC3CP,gBAAgB,CAAC,QAAQ,EAAEO,eAAe,CAAC;IAC3CP,gBAAgB,CAAC,OAAO,EAAEY,cAAc,CAAC;EAC3C,CAAC,EAAEb,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}