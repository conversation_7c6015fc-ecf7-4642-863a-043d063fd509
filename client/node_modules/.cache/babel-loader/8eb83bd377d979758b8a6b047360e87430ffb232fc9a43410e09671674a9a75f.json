{"ast": null, "code": "/*\nLanguage: Smalltalk\nDescription: Smalltalk is an object-oriented, dynamically typed reflective programming language.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Smalltalk\n*/\n\nfunction smalltalk(hljs) {\n  const VAR_IDENT_RE = '[a-z][a-zA-Z0-9_]*';\n  const CHAR = {\n    className: 'string',\n    begin: '\\\\$.{1}'\n  };\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '#' + hljs.UNDERSCORE_IDENT_RE\n  };\n  return {\n    name: 'Smalltalk',\n    aliases: ['st'],\n    keywords: 'self super nil true false thisContext',\n    // only 6\n    contains: [hljs.COMMENT('\"', '\"'), hljs.APOS_STRING_MODE, {\n      className: 'type',\n      begin: '\\\\b[A-Z][A-Za-z0-9_]*',\n      relevance: 0\n    }, {\n      begin: VAR_IDENT_RE + ':',\n      relevance: 0\n    }, hljs.C_NUMBER_MODE, SYMBOL, CHAR, {\n      // This looks more complicated than needed to avoid combinatorial\n      // explosion under V8. It effectively means `| var1 var2 ... |` with\n      // whitespace adjacent to `|` being optional.\n      begin: '\\\\|[ ]*' + VAR_IDENT_RE + '([ ]+' + VAR_IDENT_RE + ')*[ ]*\\\\|',\n      returnBegin: true,\n      end: /\\|/,\n      illegal: /\\S/,\n      contains: [{\n        begin: '(\\\\|[ ]*)?' + VAR_IDENT_RE\n      }]\n    }, {\n      begin: '#\\\\(',\n      end: '\\\\)',\n      contains: [hljs.APOS_STRING_MODE, CHAR, hljs.C_NUMBER_MODE, SYMBOL]\n    }]\n  };\n}\nmodule.exports = smalltalk;", "map": {"version": 3, "names": ["smalltalk", "hljs", "VAR_IDENT_RE", "CHAR", "className", "begin", "SYMBOL", "UNDERSCORE_IDENT_RE", "name", "aliases", "keywords", "contains", "COMMENT", "APOS_STRING_MODE", "relevance", "C_NUMBER_MODE", "returnBegin", "end", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/smalltalk.js"], "sourcesContent": ["/*\nLanguage: Smalltalk\nDescription: Smalltalk is an object-oriented, dynamically typed reflective programming language.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Smalltalk\n*/\n\nfunction smalltalk(hljs) {\n  const VAR_IDENT_RE = '[a-z][a-zA-Z0-9_]*';\n  const CHAR = {\n    className: 'string',\n    begin: '\\\\$.{1}'\n  };\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '#' + hljs.UNDERSCORE_IDENT_RE\n  };\n  return {\n    name: 'Smalltalk',\n    aliases: [ 'st' ],\n    keywords: 'self super nil true false thisContext', // only 6\n    contains: [\n      hljs.COMMENT('\"', '\"'),\n      hljs.APOS_STRING_MODE,\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][A-Za-z0-9_]*',\n        relevance: 0\n      },\n      {\n        begin: VAR_IDENT_RE + ':',\n        relevance: 0\n      },\n      hljs.C_NUMBER_MODE,\n      SYMBOL,\n      CHAR,\n      {\n        // This looks more complicated than needed to avoid combinatorial\n        // explosion under V8. It effectively means `| var1 var2 ... |` with\n        // whitespace adjacent to `|` being optional.\n        begin: '\\\\|[ ]*' + VAR_IDENT_RE + '([ ]+' + VAR_IDENT_RE + ')*[ ]*\\\\|',\n        returnBegin: true,\n        end: /\\|/,\n        illegal: /\\S/,\n        contains: [ {\n          begin: '(\\\\|[ ]*)?' + VAR_IDENT_RE\n        } ]\n      },\n      {\n        begin: '#\\\\(',\n        end: '\\\\)',\n        contains: [\n          hljs.APOS_STRING_MODE,\n          CHAR,\n          hljs.C_NUMBER_MODE,\n          SYMBOL\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = smalltalk;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAASA,CAACC,IAAI,EAAE;EACvB,MAAMC,YAAY,GAAG,oBAAoB;EACzC,MAAMC,IAAI,GAAG;IACXC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,MAAM,GAAG;IACbF,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG,GAAGJ,IAAI,CAACM;EACpB,CAAC;EACD,OAAO;IACLC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,CAAE,IAAI,CAAE;IACjBC,QAAQ,EAAE,uCAAuC;IAAE;IACnDC,QAAQ,EAAE,CACRV,IAAI,CAACW,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EACtBX,IAAI,CAACY,gBAAgB,EACrB;MACET,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,uBAAuB;MAC9BS,SAAS,EAAE;IACb,CAAC,EACD;MACET,KAAK,EAAEH,YAAY,GAAG,GAAG;MACzBY,SAAS,EAAE;IACb,CAAC,EACDb,IAAI,CAACc,aAAa,EAClBT,MAAM,EACNH,IAAI,EACJ;MACE;MACA;MACA;MACAE,KAAK,EAAE,SAAS,GAAGH,YAAY,GAAG,OAAO,GAAGA,YAAY,GAAG,WAAW;MACtEc,WAAW,EAAE,IAAI;MACjBC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE,IAAI;MACbP,QAAQ,EAAE,CAAE;QACVN,KAAK,EAAE,YAAY,GAAGH;MACxB,CAAC;IACH,CAAC,EACD;MACEG,KAAK,EAAE,MAAM;MACbY,GAAG,EAAE,KAAK;MACVN,QAAQ,EAAE,CACRV,IAAI,CAACY,gBAAgB,EACrBV,IAAI,EACJF,IAAI,CAACc,aAAa,EAClBT,MAAM;IAEV,CAAC;EAEL,CAAC;AACH;AAEAa,MAAM,CAACC,OAAO,GAAGpB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}