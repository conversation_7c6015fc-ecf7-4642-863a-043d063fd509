{"ast": null, "code": "/*\nLanguage: NSIS\nDescription: Nullsoft Scriptable Install System\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://nsis.sourceforge.io/Main_Page\n*/\n\nfunction nsis(hljs) {\n  const CONSTANTS = {\n    className: 'variable',\n    begin: /\\$(ADMINTOOLS|APPDATA|CDBURN_AREA|CMDLINE|COMMONFILES32|COMMONFILES64|COMMONFILES|COOKIES|DESKTOP|DOCUMENTS|EXEDIR|EXEFILE|EXEPATH|FAVORITES|FONTS|HISTORY|HWNDPARENT|INSTDIR|INTERNET_CACHE|LANGUAGE|LOCALAPPDATA|MUSIC|NETHOOD|OUTDIR|PICTURES|PLUGINSDIR|PRINTHOOD|PROFILE|PROGRAMFILES32|PROGRAMFILES64|PROGRAMFILES|QUICKLAUNCH|RECENT|RESOURCES_LOCALIZED|RESOURCES|SENDTO|SMPROGRAMS|SMSTARTUP|STARTMENU|SYSDIR|TEMP|TEMPLATES|VIDEOS|WINDIR)/\n  };\n  const DEFINES = {\n    // ${defines}\n    className: 'variable',\n    begin: /\\$+\\{[\\w.:-]+\\}/\n  };\n  const VARIABLES = {\n    // $variables\n    className: 'variable',\n    begin: /\\$+\\w+/,\n    illegal: /\\(\\)\\{\\}/\n  };\n  const LANGUAGES = {\n    // $(language_strings)\n    className: 'variable',\n    begin: /\\$+\\([\\w^.:-]+\\)/\n  };\n  const PARAMETERS = {\n    // command parameters\n    className: 'params',\n    begin: '(ARCHIVE|FILE_ATTRIBUTE_ARCHIVE|FILE_ATTRIBUTE_NORMAL|FILE_ATTRIBUTE_OFFLINE|FILE_ATTRIBUTE_READONLY|FILE_ATTRIBUTE_SYSTEM|FILE_ATTRIBUTE_TEMPORARY|HKCR|HKCU|HKDD|HKEY_CLASSES_ROOT|HKEY_CURRENT_CONFIG|HKEY_CURRENT_USER|HKEY_DYN_DATA|HKEY_LOCAL_MACHINE|HKEY_PERFORMANCE_DATA|HKEY_USERS|HKLM|HKPD|HKU|IDABORT|IDCANCEL|IDIGNORE|IDNO|IDOK|IDRETRY|IDYES|MB_ABORTRETRYIGNORE|MB_DEFBUTTON1|MB_DEFBUTTON2|MB_DEFBUTTON3|MB_DEFBUTTON4|MB_ICONEXCLAMATION|MB_ICONINFORMATION|MB_ICONQUESTION|MB_ICONSTOP|MB_OK|MB_OKCANCEL|MB_RETRYCANCEL|MB_RIGHT|MB_RTLREADING|MB_SETFOREGROUND|MB_TOPMOST|MB_USERICON|MB_YESNO|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SYSTEM|TEMPORARY)'\n  };\n  const COMPILER = {\n    // !compiler_flags\n    className: 'keyword',\n    begin: /!(addincludedir|addplugindir|appendfile|cd|define|delfile|echo|else|endif|error|execute|finalize|getdllversion|gettlbversion|if|ifdef|ifmacrodef|ifmacrondef|ifndef|include|insertmacro|macro|macroend|makensis|packhdr|searchparse|searchreplace|system|tempfile|undef|verbose|warning)/\n  };\n  const METACHARS = {\n    // $\\n, $\\r, $\\t, $$\n    className: 'meta',\n    begin: /\\$(\\\\[nrt]|\\$)/\n  };\n  const PLUGINS = {\n    // plug::ins\n    className: 'class',\n    begin: /\\w+::\\w+/\n  };\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: '\"',\n      end: '\"'\n    }, {\n      begin: '\\'',\n      end: '\\''\n    }, {\n      begin: '`',\n      end: '`'\n    }],\n    illegal: /\\n/,\n    contains: [METACHARS, CONSTANTS, DEFINES, VARIABLES, LANGUAGES]\n  };\n  return {\n    name: 'NSIS',\n    case_insensitive: false,\n    keywords: {\n      keyword: 'Abort AddBrandingImage AddSize AllowRootDirInstall AllowSkipFiles AutoCloseWindow BGFont BGGradient BrandingText BringToFront Call CallInstDLL Caption ChangeUI CheckBitmap ClearErrors CompletedText ComponentText CopyFiles CRCCheck CreateDirectory CreateFont CreateShortCut Delete DeleteINISec DeleteINIStr DeleteRegKey DeleteRegValue DetailPrint DetailsButtonText DirText DirVar DirVerify EnableWindow EnumRegKey EnumRegValue Exch Exec ExecShell ExecShellWait ExecWait ExpandEnvStrings File FileBufSize FileClose FileErrorText FileOpen FileRead FileReadByte FileReadUTF16LE FileReadWord FileWriteUTF16LE FileSeek FileWrite FileWriteByte FileWriteWord FindClose FindFirst FindNext FindWindow FlushINI GetCurInstType GetCurrentAddress GetDlgItem GetDLLVersion GetDLLVersionLocal GetErrorLevel GetFileTime GetFileTimeLocal GetFullPathName GetFunctionAddress GetInstDirError GetKnownFolderPath GetLabelAddress GetTempFileName Goto HideWindow Icon IfAbort IfErrors IfFileExists IfRebootFlag IfRtlLanguage IfShellVarContextAll IfSilent InitPluginsDir InstallButtonText InstallColors InstallDir InstallDirRegKey InstProgressFlags InstType InstTypeGetText InstTypeSetText Int64Cmp Int64CmpU Int64Fmt IntCmp IntCmpU IntFmt IntOp IntPtrCmp IntPtrCmpU IntPtrOp IsWindow LangString LicenseBkColor LicenseData LicenseForceSelection LicenseLangString LicenseText LoadAndSetImage LoadLanguageFile LockWindow LogSet LogText ManifestDPIAware ManifestLongPathAware ManifestMaxVersionTested ManifestSupportedOS MessageBox MiscButtonText Name Nop OutFile Page PageCallbacks PEAddResource PEDllCharacteristics PERemoveResource PESubsysVer Pop Push Quit ReadEnvStr ReadINIStr ReadRegDWORD ReadRegStr Reboot RegDLL Rename RequestExecutionLevel ReserveFile Return RMDir SearchPath SectionGetFlags SectionGetInstTypes SectionGetSize SectionGetText SectionIn SectionSetFlags SectionSetInstTypes SectionSetSize SectionSetText SendMessage SetAutoClose SetBrandingImage SetCompress SetCompressor SetCompressorDictSize SetCtlColors SetCurInstType SetDatablockOptimize SetDateSave SetDetailsPrint SetDetailsView SetErrorLevel SetErrors SetFileAttributes SetFont SetOutPath SetOverwrite SetRebootFlag SetRegView SetShellVarContext SetSilent ShowInstDetails ShowUninstDetails ShowWindow SilentInstall SilentUnInstall Sleep SpaceTexts StrCmp StrCmpS StrCpy StrLen SubCaption Unicode UninstallButtonText UninstallCaption UninstallIcon UninstallSubCaption UninstallText UninstPage UnRegDLL Var VIAddVersionKey VIFileVersion VIProductVersion WindowIcon WriteINIStr WriteRegBin WriteRegDWORD WriteRegExpandStr WriteRegMultiStr WriteRegNone WriteRegStr WriteUninstaller XPStyle',\n      literal: 'admin all auto both bottom bzip2 colored components current custom directory false force hide highest ifdiff ifnewer instfiles lastused leave left license listonly lzma nevershow none normal notset off on open print right show silent silentlog smooth textonly top true try un.components un.custom un.directory un.instfiles un.license uninstConfirm user Win10 Win7 Win8 WinVista zlib'\n    },\n    contains: [hljs.HASH_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.COMMENT(';', '$', {\n      relevance: 0\n    }), {\n      className: 'function',\n      beginKeywords: 'Function PageEx Section SectionGroup',\n      end: '$'\n    }, STRING, COMPILER, DEFINES, VARIABLES, LANGUAGES, PARAMETERS, PLUGINS, hljs.NUMBER_MODE]\n  };\n}\nmodule.exports = nsis;", "map": {"version": 3, "names": ["nsis", "hljs", "CONSTANTS", "className", "begin", "DEFINES", "VARIABLES", "illegal", "LANGUAGES", "PARAMETERS", "COMPILER", "METACHARS", "PLUGINS", "STRING", "variants", "end", "contains", "name", "case_insensitive", "keywords", "keyword", "literal", "HASH_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "COMMENT", "relevance", "beginKeywords", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/nsis.js"], "sourcesContent": ["/*\nLanguage: NSIS\nDescription: Nullsoft Scriptable Install System\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://nsis.sourceforge.io/Main_Page\n*/\n\nfunction nsis(hljs) {\n  const CONSTANTS = {\n    className: 'variable',\n    begin: /\\$(ADMINTOOLS|APPDATA|CDBURN_AREA|CMDLINE|COMMONFILES32|COMMONFILES64|COMMONFILES|COOKIES|DESKTOP|DOCUMENTS|EXEDIR|EXEFILE|EXEPATH|FAVORITES|FONTS|HISTORY|HWNDPARENT|INSTDIR|INTERNET_CACHE|LANGUAGE|LOCALAPPDATA|MUSIC|NETHOOD|OUTDIR|PICTURES|PLUGINSDIR|PRINTHOOD|PROFILE|PROGRAMFILES32|PROGRAMFILES64|PROGRAMFILES|QUICKLAUNCH|RECENT|RESOURCES_LOCALIZED|RESOURCES|SENDTO|SMPROGRAMS|SMSTARTUP|STARTMENU|SYSDIR|TEMP|TEMPLATES|VIDEOS|WINDIR)/\n  };\n\n  const DEFINES = {\n    // ${defines}\n    className: 'variable',\n    begin: /\\$+\\{[\\w.:-]+\\}/\n  };\n\n  const VARIABLES = {\n    // $variables\n    className: 'variable',\n    begin: /\\$+\\w+/,\n    illegal: /\\(\\)\\{\\}/\n  };\n\n  const LANGUAGES = {\n    // $(language_strings)\n    className: 'variable',\n    begin: /\\$+\\([\\w^.:-]+\\)/\n  };\n\n  const PARAMETERS = {\n    // command parameters\n    className: 'params',\n    begin: '(ARCHIVE|FILE_ATTRIBUTE_ARCHIVE|FILE_ATTRIBUTE_NORMAL|FILE_ATTRIBUTE_OFFLINE|FILE_ATTRIBUTE_READONLY|FILE_ATTRIBUTE_SYSTEM|FILE_ATTRIBUTE_TEMPORARY|HKCR|HKCU|HKDD|HKEY_CLASSES_ROOT|HKEY_CURRENT_CONFIG|HKEY_CURRENT_USER|HKEY_DYN_DATA|HKEY_LOCAL_MACHINE|HKEY_PERFORMANCE_DATA|HKEY_USERS|HKLM|HKPD|HKU|IDABORT|IDCANCEL|IDIGNORE|IDNO|IDOK|IDRETRY|IDYES|MB_ABORTRETRYIGNORE|MB_DEFBUTTON1|MB_DEFBUTTON2|MB_DEFBUTTON3|MB_DEFBUTTON4|MB_ICONEXCLAMATION|MB_ICONINFORMATION|MB_ICONQUESTION|MB_ICONSTOP|MB_OK|MB_OKCANCEL|MB_RETRYCANCEL|MB_RIGHT|MB_RTLREADING|MB_SETFOREGROUND|MB_TOPMOST|MB_USERICON|MB_YESNO|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SYSTEM|TEMPORARY)'\n  };\n\n  const COMPILER = {\n    // !compiler_flags\n    className: 'keyword',\n    begin: /!(addincludedir|addplugindir|appendfile|cd|define|delfile|echo|else|endif|error|execute|finalize|getdllversion|gettlbversion|if|ifdef|ifmacrodef|ifmacrondef|ifndef|include|insertmacro|macro|macroend|makensis|packhdr|searchparse|searchreplace|system|tempfile|undef|verbose|warning)/\n  };\n\n  const METACHARS = {\n    // $\\n, $\\r, $\\t, $$\n    className: 'meta',\n    begin: /\\$(\\\\[nrt]|\\$)/\n  };\n\n  const PLUGINS = {\n    // plug::ins\n    className: 'class',\n    begin: /\\w+::\\w+/\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: '\\'',\n        end: '\\''\n      },\n      {\n        begin: '`',\n        end: '`'\n      }\n    ],\n    illegal: /\\n/,\n    contains: [\n      METACHARS,\n      CONSTANTS,\n      DEFINES,\n      VARIABLES,\n      LANGUAGES\n    ]\n  };\n\n  return {\n    name: 'NSIS',\n    case_insensitive: false,\n    keywords: {\n      keyword:\n      'Abort AddBrandingImage AddSize AllowRootDirInstall AllowSkipFiles AutoCloseWindow BGFont BGGradient BrandingText BringToFront Call CallInstDLL Caption ChangeUI CheckBitmap ClearErrors CompletedText ComponentText CopyFiles CRCCheck CreateDirectory CreateFont CreateShortCut Delete DeleteINISec DeleteINIStr DeleteRegKey DeleteRegValue DetailPrint DetailsButtonText DirText DirVar DirVerify EnableWindow EnumRegKey EnumRegValue Exch Exec ExecShell ExecShellWait ExecWait ExpandEnvStrings File FileBufSize FileClose FileErrorText FileOpen FileRead FileReadByte FileReadUTF16LE FileReadWord FileWriteUTF16LE FileSeek FileWrite FileWriteByte FileWriteWord FindClose FindFirst FindNext FindWindow FlushINI GetCurInstType GetCurrentAddress GetDlgItem GetDLLVersion GetDLLVersionLocal GetErrorLevel GetFileTime GetFileTimeLocal GetFullPathName GetFunctionAddress GetInstDirError GetKnownFolderPath GetLabelAddress GetTempFileName Goto HideWindow Icon IfAbort IfErrors IfFileExists IfRebootFlag IfRtlLanguage IfShellVarContextAll IfSilent InitPluginsDir InstallButtonText InstallColors InstallDir InstallDirRegKey InstProgressFlags InstType InstTypeGetText InstTypeSetText Int64Cmp Int64CmpU Int64Fmt IntCmp IntCmpU IntFmt IntOp IntPtrCmp IntPtrCmpU IntPtrOp IsWindow LangString LicenseBkColor LicenseData LicenseForceSelection LicenseLangString LicenseText LoadAndSetImage LoadLanguageFile LockWindow LogSet LogText ManifestDPIAware ManifestLongPathAware ManifestMaxVersionTested ManifestSupportedOS MessageBox MiscButtonText Name Nop OutFile Page PageCallbacks PEAddResource PEDllCharacteristics PERemoveResource PESubsysVer Pop Push Quit ReadEnvStr ReadINIStr ReadRegDWORD ReadRegStr Reboot RegDLL Rename RequestExecutionLevel ReserveFile Return RMDir SearchPath SectionGetFlags SectionGetInstTypes SectionGetSize SectionGetText SectionIn SectionSetFlags SectionSetInstTypes SectionSetSize SectionSetText SendMessage SetAutoClose SetBrandingImage SetCompress SetCompressor SetCompressorDictSize SetCtlColors SetCurInstType SetDatablockOptimize SetDateSave SetDetailsPrint SetDetailsView SetErrorLevel SetErrors SetFileAttributes SetFont SetOutPath SetOverwrite SetRebootFlag SetRegView SetShellVarContext SetSilent ShowInstDetails ShowUninstDetails ShowWindow SilentInstall SilentUnInstall Sleep SpaceTexts StrCmp StrCmpS StrCpy StrLen SubCaption Unicode UninstallButtonText UninstallCaption UninstallIcon UninstallSubCaption UninstallText UninstPage UnRegDLL Var VIAddVersionKey VIFileVersion VIProductVersion WindowIcon WriteINIStr WriteRegBin WriteRegDWORD WriteRegExpandStr WriteRegMultiStr WriteRegNone WriteRegStr WriteUninstaller XPStyle',\n      literal:\n      'admin all auto both bottom bzip2 colored components current custom directory false force hide highest ifdiff ifnewer instfiles lastused leave left license listonly lzma nevershow none normal notset off on open print right show silent silentlog smooth textonly top true try un.components un.custom un.directory un.instfiles un.license uninstConfirm user Win10 Win7 Win8 WinVista zlib'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.COMMENT(\n        ';',\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      {\n        className: 'function',\n        beginKeywords: 'Function PageEx Section SectionGroup',\n        end: '$'\n      },\n      STRING,\n      COMPILER,\n      DEFINES,\n      VARIABLES,\n      LANGUAGES,\n      PARAMETERS,\n      PLUGINS,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = nsis;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,SAAS,GAAG;IAChBC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,OAAO,GAAG;IACd;IACAF,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;EAED,MAAME,SAAS,GAAG;IAChB;IACAH,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,QAAQ;IACfG,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,SAAS,GAAG;IAChB;IACAL,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMK,UAAU,GAAG;IACjB;IACAN,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMM,QAAQ,GAAG;IACf;IACAP,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMO,SAAS,GAAG;IAChB;IACAR,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMQ,OAAO,GAAG;IACd;IACAT,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMS,MAAM,GAAG;IACbV,SAAS,EAAE,QAAQ;IACnBW,QAAQ,EAAE,CACR;MACEV,KAAK,EAAE,GAAG;MACVW,GAAG,EAAE;IACP,CAAC,EACD;MACEX,KAAK,EAAE,IAAI;MACXW,GAAG,EAAE;IACP,CAAC,EACD;MACEX,KAAK,EAAE,GAAG;MACVW,GAAG,EAAE;IACP,CAAC,CACF;IACDR,OAAO,EAAE,IAAI;IACbS,QAAQ,EAAE,CACRL,SAAS,EACTT,SAAS,EACTG,OAAO,EACPC,SAAS,EACTE,SAAS;EAEb,CAAC;EAED,OAAO;IACLS,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,KAAK;IACvBC,QAAQ,EAAE;MACRC,OAAO,EACP,glFAAglF;MAChlFC,OAAO,EACP;IACF,CAAC;IACDL,QAAQ,EAAE,CACRf,IAAI,CAACqB,iBAAiB,EACtBrB,IAAI,CAACsB,oBAAoB,EACzBtB,IAAI,CAACuB,OAAO,CACV,GAAG,EACH,GAAG,EACH;MACEC,SAAS,EAAE;IACb,CACF,CAAC,EACD;MACEtB,SAAS,EAAE,UAAU;MACrBuB,aAAa,EAAE,sCAAsC;MACrDX,GAAG,EAAE;IACP,CAAC,EACDF,MAAM,EACNH,QAAQ,EACRL,OAAO,EACPC,SAAS,EACTE,SAAS,EACTC,UAAU,EACVG,OAAO,EACPX,IAAI,CAAC0B,WAAW;EAEpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAG7B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}