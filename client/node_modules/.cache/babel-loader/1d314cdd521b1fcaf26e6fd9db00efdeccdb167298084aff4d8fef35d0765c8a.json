{"ast": null, "code": "/*\nLanguage: VHDL\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nDescription: VHDL is a hardware description language used in electronic design automation to describe digital and mixed-signal systems.\nWebsite: https://en.wikipedia.org/wiki/VHDL\n*/\n\nfunction vhdl(hljs) {\n  // Regular expression for VHDL numeric literals.\n\n  // Decimal literal:\n  const INTEGER_RE = '\\\\d(_|\\\\d)*';\n  const EXPONENT_RE = '[eE][-+]?' + INTEGER_RE;\n  const DECIMAL_LITERAL_RE = INTEGER_RE + '(\\\\.' + INTEGER_RE + ')?' + '(' + EXPONENT_RE + ')?';\n  // Based literal:\n  const BASED_INTEGER_RE = '\\\\w+';\n  const BASED_LITERAL_RE = INTEGER_RE + '#' + BASED_INTEGER_RE + '(\\\\.' + BASED_INTEGER_RE + ')?' + '#' + '(' + EXPONENT_RE + ')?';\n  const NUMBER_RE = '\\\\b(' + BASED_LITERAL_RE + '|' + DECIMAL_LITERAL_RE + ')';\n  return {\n    name: 'VHDL',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'abs access after alias all and architecture array assert assume assume_guarantee attribute ' + 'begin block body buffer bus case component configuration constant context cover disconnect ' + 'downto default else elsif end entity exit fairness file for force function generate ' + 'generic group guarded if impure in inertial inout is label library linkage literal ' + 'loop map mod nand new next nor not null of on open or others out package parameter port ' + 'postponed procedure process property protected pure range record register reject ' + 'release rem report restrict restrict_guarantee return rol ror select sequence ' + 'severity shared signal sla sll sra srl strong subtype then to transport type ' + 'unaffected units until use variable view vmode vprop vunit wait when while with xnor xor',\n      built_in: 'boolean bit character ' + 'integer time delay_length natural positive ' + 'string bit_vector file_open_kind file_open_status ' + 'std_logic std_logic_vector unsigned signed boolean_vector integer_vector ' + 'std_ulogic std_ulogic_vector unresolved_unsigned u_unsigned unresolved_signed u_signed ' + 'real_vector time_vector',\n      literal: 'false true note warning error failure ' +\n      // severity_level\n      'line text side width' // textio\n    },\n    illegal: /\\{/,\n    contains: [hljs.C_BLOCK_COMMENT_MODE,\n    // VHDL-2008 block commenting.\n    hljs.COMMENT('--', '$'), hljs.QUOTE_STRING_MODE, {\n      className: 'number',\n      begin: NUMBER_RE,\n      relevance: 0\n    }, {\n      className: 'string',\n      begin: '\\'(U|X|0|1|Z|W|L|H|-)\\'',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      className: 'symbol',\n      begin: '\\'[A-Za-z](_?[A-Za-z0-9])*',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }]\n  };\n}\nmodule.exports = vhdl;", "map": {"version": 3, "names": ["vhdl", "hljs", "INTEGER_RE", "EXPONENT_RE", "DECIMAL_LITERAL_RE", "BASED_INTEGER_RE", "BASED_LITERAL_RE", "NUMBER_RE", "name", "case_insensitive", "keywords", "keyword", "built_in", "literal", "illegal", "contains", "C_BLOCK_COMMENT_MODE", "COMMENT", "QUOTE_STRING_MODE", "className", "begin", "relevance", "BACKSLASH_ESCAPE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/vhdl.js"], "sourcesContent": ["/*\nLanguage: VHDL\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nDescription: VHDL is a hardware description language used in electronic design automation to describe digital and mixed-signal systems.\nWebsite: https://en.wikipedia.org/wiki/VHDL\n*/\n\nfunction vhdl(hljs) {\n  // Regular expression for VHDL numeric literals.\n\n  // Decimal literal:\n  const INTEGER_RE = '\\\\d(_|\\\\d)*';\n  const EXPONENT_RE = '[eE][-+]?' + INTEGER_RE;\n  const DECIMAL_LITERAL_RE = INTEGER_RE + '(\\\\.' + INTEGER_RE + ')?' + '(' + EXPONENT_RE + ')?';\n  // Based literal:\n  const BASED_INTEGER_RE = '\\\\w+';\n  const BASED_LITERAL_RE = INTEGER_RE + '#' + BASED_INTEGER_RE + '(\\\\.' + BASED_INTEGER_RE + ')?' + '#' + '(' + EXPONENT_RE + ')?';\n\n  const NUMBER_RE = '\\\\b(' + BASED_LITERAL_RE + '|' + DECIMAL_LITERAL_RE + ')';\n\n  return {\n    name: 'VHDL',\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        'abs access after alias all and architecture array assert assume assume_guarantee attribute ' +\n        'begin block body buffer bus case component configuration constant context cover disconnect ' +\n        'downto default else elsif end entity exit fairness file for force function generate ' +\n        'generic group guarded if impure in inertial inout is label library linkage literal ' +\n        'loop map mod nand new next nor not null of on open or others out package parameter port ' +\n        'postponed procedure process property protected pure range record register reject ' +\n        'release rem report restrict restrict_guarantee return rol ror select sequence ' +\n        'severity shared signal sla sll sra srl strong subtype then to transport type ' +\n        'unaffected units until use variable view vmode vprop vunit wait when while with xnor xor',\n      built_in:\n        'boolean bit character ' +\n        'integer time delay_length natural positive ' +\n        'string bit_vector file_open_kind file_open_status ' +\n        'std_logic std_logic_vector unsigned signed boolean_vector integer_vector ' +\n        'std_ulogic std_ulogic_vector unresolved_unsigned u_unsigned unresolved_signed u_signed ' +\n        'real_vector time_vector',\n      literal:\n        'false true note warning error failure ' + // severity_level\n        'line text side width' // textio\n    },\n    illegal: /\\{/,\n    contains: [\n      hljs.C_BLOCK_COMMENT_MODE, // VHDL-2008 block commenting.\n      hljs.COMMENT('--', '$'),\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'number',\n        begin: NUMBER_RE,\n        relevance: 0\n      },\n      {\n        className: 'string',\n        begin: '\\'(U|X|0|1|Z|W|L|H|-)\\'',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        className: 'symbol',\n        begin: '\\'[A-Za-z](_?[A-Za-z0-9])*',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      }\n    ]\n  };\n}\n\nmodule.exports = vhdl;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB;;EAEA;EACA,MAAMC,UAAU,GAAG,aAAa;EAChC,MAAMC,WAAW,GAAG,WAAW,GAAGD,UAAU;EAC5C,MAAME,kBAAkB,GAAGF,UAAU,GAAG,MAAM,GAAGA,UAAU,GAAG,IAAI,GAAG,GAAG,GAAGC,WAAW,GAAG,IAAI;EAC7F;EACA,MAAME,gBAAgB,GAAG,MAAM;EAC/B,MAAMC,gBAAgB,GAAGJ,UAAU,GAAG,GAAG,GAAGG,gBAAgB,GAAG,MAAM,GAAGA,gBAAgB,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAGF,WAAW,GAAG,IAAI;EAEhI,MAAMI,SAAS,GAAG,MAAM,GAAGD,gBAAgB,GAAG,GAAG,GAAGF,kBAAkB,GAAG,GAAG;EAE5E,OAAO;IACLI,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO,EACL,6FAA6F,GAC7F,6FAA6F,GAC7F,sFAAsF,GACtF,qFAAqF,GACrF,0FAA0F,GAC1F,mFAAmF,GACnF,gFAAgF,GAChF,+EAA+E,GAC/E,0FAA0F;MAC5FC,QAAQ,EACN,wBAAwB,GACxB,6CAA6C,GAC7C,oDAAoD,GACpD,2EAA2E,GAC3E,yFAAyF,GACzF,yBAAyB;MAC3BC,OAAO,EACL,wCAAwC;MAAG;MAC3C,sBAAsB,CAAC;IAC3B,CAAC;IACDC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRd,IAAI,CAACe,oBAAoB;IAAE;IAC3Bf,IAAI,CAACgB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EACvBhB,IAAI,CAACiB,iBAAiB,EACtB;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAEb,SAAS;MAChBc,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,yBAAyB;MAChCL,QAAQ,EAAE,CAAEd,IAAI,CAACqB,gBAAgB;IACnC,CAAC,EACD;MACEH,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,4BAA4B;MACnCL,QAAQ,EAAE,CAAEd,IAAI,CAACqB,gBAAgB;IACnC,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGxB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}