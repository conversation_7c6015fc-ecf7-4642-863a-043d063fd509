{"ast": null, "code": "/*\nLanguage: Lisp\nDescription: Generic lisp syntax\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: lisp\n*/\n\nfunction lisp(hljs) {\n  var LISP_IDENT_RE = '[a-zA-Z_\\\\-+\\\\*\\\\/<=>&#][a-zA-Z0-9_\\\\-+*\\\\/<=>&#!]*';\n  var MEC_RE = '\\\\|[^]*?\\\\|';\n  var LISP_SIMPLE_NUMBER_RE = '(-|\\\\+)?\\\\d+(\\\\.\\\\d+|\\\\/\\\\d+)?((d|e|f|l|s|D|E|F|L|S)(\\\\+|-)?\\\\d+)?';\n  var LITERAL = {\n    className: 'literal',\n    begin: '\\\\b(t{1}|nil)\\\\b'\n  };\n  var NUMBER = {\n    className: 'number',\n    variants: [{\n      begin: LISP_SIMPLE_NUMBER_RE,\n      relevance: 0\n    }, {\n      begin: '#(b|B)[0-1]+(/[0-1]+)?'\n    }, {\n      begin: '#(o|O)[0-7]+(/[0-7]+)?'\n    }, {\n      begin: '#(x|X)[0-9a-fA-F]+(/[0-9a-fA-F]+)?'\n    }, {\n      begin: '#(c|C)\\\\(' + LISP_SIMPLE_NUMBER_RE + ' +' + LISP_SIMPLE_NUMBER_RE,\n      end: '\\\\)'\n    }]\n  };\n  var STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  var COMMENT = hljs.COMMENT(';', '$', {\n    relevance: 0\n  });\n  var VARIABLE = {\n    begin: '\\\\*',\n    end: '\\\\*'\n  };\n  var KEYWORD = {\n    className: 'symbol',\n    begin: '[:&]' + LISP_IDENT_RE\n  };\n  var IDENT = {\n    begin: LISP_IDENT_RE,\n    relevance: 0\n  };\n  var MEC = {\n    begin: MEC_RE\n  };\n  var QUOTED_LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: ['self', LITERAL, STRING, NUMBER, IDENT]\n  };\n  var QUOTED = {\n    contains: [NUMBER, STRING, VARIABLE, KEYWORD, QUOTED_LIST, IDENT],\n    variants: [{\n      begin: '[\\'`]\\\\(',\n      end: '\\\\)'\n    }, {\n      begin: '\\\\(quote ',\n      end: '\\\\)',\n      keywords: {\n        name: 'quote'\n      }\n    }, {\n      begin: '\\'' + MEC_RE\n    }]\n  };\n  var QUOTED_ATOM = {\n    variants: [{\n      begin: '\\'' + LISP_IDENT_RE\n    }, {\n      begin: '#\\'' + LISP_IDENT_RE + '(::' + LISP_IDENT_RE + ')*'\n    }]\n  };\n  var LIST = {\n    begin: '\\\\(\\\\s*',\n    end: '\\\\)'\n  };\n  var BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  LIST.contains = [{\n    className: 'name',\n    variants: [{\n      begin: LISP_IDENT_RE,\n      relevance: 0\n    }, {\n      begin: MEC_RE\n    }]\n  }, BODY];\n  BODY.contains = [QUOTED, QUOTED_ATOM, LIST, LITERAL, NUMBER, STRING, COMMENT, VARIABLE, KEYWORD, MEC, IDENT];\n  return {\n    name: 'Lisp',\n    illegal: /\\S/,\n    contains: [NUMBER, hljs.SHEBANG(), LITERAL, STRING, COMMENT, QUOTED, QUOTED_ATOM, LIST, IDENT]\n  };\n}\nmodule.exports = lisp;", "map": {"version": 3, "names": ["lisp", "hljs", "LISP_IDENT_RE", "MEC_RE", "LISP_SIMPLE_NUMBER_RE", "LITERAL", "className", "begin", "NUMBER", "variants", "relevance", "end", "STRING", "inherit", "QUOTE_STRING_MODE", "illegal", "COMMENT", "VARIABLE", "KEYWORD", "IDENT", "MEC", "QUOTED_LIST", "contains", "QUOTED", "keywords", "name", "QUOTED_ATOM", "LIST", "BODY", "endsWithParent", "SHEBANG", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/lisp.js"], "sourcesContent": ["/*\nLanguage: Lisp\nDescription: Generic lisp syntax\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: lisp\n*/\n\nfunction lisp(hljs) {\n  var LISP_IDENT_RE = '[a-zA-Z_\\\\-+\\\\*\\\\/<=>&#][a-zA-Z0-9_\\\\-+*\\\\/<=>&#!]*';\n  var MEC_RE = '\\\\|[^]*?\\\\|';\n  var LISP_SIMPLE_NUMBER_RE = '(-|\\\\+)?\\\\d+(\\\\.\\\\d+|\\\\/\\\\d+)?((d|e|f|l|s|D|E|F|L|S)(\\\\+|-)?\\\\d+)?';\n  var LITERAL = {\n    className: 'literal',\n    begin: '\\\\b(t{1}|nil)\\\\b'\n  };\n  var NUMBER = {\n    className: 'number',\n    variants: [\n      {begin: LISP_SIMPLE_NUMBER_RE, relevance: 0},\n      {begin: '#(b|B)[0-1]+(/[0-1]+)?'},\n      {begin: '#(o|O)[0-7]+(/[0-7]+)?'},\n      {begin: '#(x|X)[0-9a-fA-F]+(/[0-9a-fA-F]+)?'},\n      {begin: '#(c|C)\\\\(' + LISP_SIMPLE_NUMBER_RE + ' +' + LISP_SIMPLE_NUMBER_RE, end: '\\\\)'}\n    ]\n  };\n  var STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {illegal: null});\n  var COMMENT = hljs.COMMENT(\n    ';', '$',\n    {\n      relevance: 0\n    }\n  );\n  var VARIABLE = {\n    begin: '\\\\*', end: '\\\\*'\n  };\n  var KEYWORD = {\n    className: 'symbol',\n    begin: '[:&]' + LISP_IDENT_RE\n  };\n  var IDENT = {\n    begin: LISP_IDENT_RE,\n    relevance: 0\n  };\n  var MEC = {\n    begin: MEC_RE\n  };\n  var QUOTED_LIST = {\n    begin: '\\\\(', end: '\\\\)',\n    contains: ['self', LITERAL, STRING, NUMBER, IDENT]\n  };\n  var QUOTED = {\n    contains: [NUMBER, STRING, VARIABLE, KEYWORD, QUOTED_LIST, IDENT],\n    variants: [\n      {\n        begin: '[\\'`]\\\\(', end: '\\\\)'\n      },\n      {\n        begin: '\\\\(quote ', end: '\\\\)',\n        keywords: {name: 'quote'}\n      },\n      {\n        begin: '\\'' + MEC_RE\n      }\n    ]\n  };\n  var QUOTED_ATOM = {\n    variants: [\n      {begin: '\\'' + LISP_IDENT_RE},\n      {begin: '#\\'' + LISP_IDENT_RE + '(::' + LISP_IDENT_RE + ')*'}\n    ]\n  };\n  var LIST = {\n    begin: '\\\\(\\\\s*', end: '\\\\)'\n  };\n  var BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  LIST.contains = [\n    {\n      className: 'name',\n      variants: [\n        {\n          begin: LISP_IDENT_RE,\n          relevance: 0,\n        },\n        {begin: MEC_RE}\n      ]\n    },\n    BODY\n  ];\n  BODY.contains = [QUOTED, QUOTED_ATOM, LIST, LITERAL, NUMBER, STRING, COMMENT, VARIABLE, KEYWORD, MEC, IDENT];\n\n  return {\n    name: 'Lisp',\n    illegal: /\\S/,\n    contains: [\n      NUMBER,\n      hljs.SHEBANG(),\n      LITERAL,\n      STRING,\n      COMMENT,\n      QUOTED,\n      QUOTED_ATOM,\n      LIST,\n      IDENT\n    ]\n  };\n}\n\nmodule.exports = lisp;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,aAAa,GAAG,qDAAqD;EACzE,IAAIC,MAAM,GAAG,aAAa;EAC1B,IAAIC,qBAAqB,GAAG,oEAAoE;EAChG,IAAIC,OAAO,GAAG;IACZC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE;EACT,CAAC;EACD,IAAIC,MAAM,GAAG;IACXF,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MAACF,KAAK,EAAEH,qBAAqB;MAAEM,SAAS,EAAE;IAAC,CAAC,EAC5C;MAACH,KAAK,EAAE;IAAwB,CAAC,EACjC;MAACA,KAAK,EAAE;IAAwB,CAAC,EACjC;MAACA,KAAK,EAAE;IAAoC,CAAC,EAC7C;MAACA,KAAK,EAAE,WAAW,GAAGH,qBAAqB,GAAG,IAAI,GAAGA,qBAAqB;MAAEO,GAAG,EAAE;IAAK,CAAC;EAE3F,CAAC;EACD,IAAIC,MAAM,GAAGX,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACa,iBAAiB,EAAE;IAACC,OAAO,EAAE;EAAI,CAAC,CAAC;EAClE,IAAIC,OAAO,GAAGf,IAAI,CAACe,OAAO,CACxB,GAAG,EAAE,GAAG,EACR;IACEN,SAAS,EAAE;EACb,CACF,CAAC;EACD,IAAIO,QAAQ,GAAG;IACbV,KAAK,EAAE,KAAK;IAAEI,GAAG,EAAE;EACrB,CAAC;EACD,IAAIO,OAAO,GAAG;IACZZ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,MAAM,GAAGL;EAClB,CAAC;EACD,IAAIiB,KAAK,GAAG;IACVZ,KAAK,EAAEL,aAAa;IACpBQ,SAAS,EAAE;EACb,CAAC;EACD,IAAIU,GAAG,GAAG;IACRb,KAAK,EAAEJ;EACT,CAAC;EACD,IAAIkB,WAAW,GAAG;IAChBd,KAAK,EAAE,KAAK;IAAEI,GAAG,EAAE,KAAK;IACxBW,QAAQ,EAAE,CAAC,MAAM,EAAEjB,OAAO,EAAEO,MAAM,EAAEJ,MAAM,EAAEW,KAAK;EACnD,CAAC;EACD,IAAII,MAAM,GAAG;IACXD,QAAQ,EAAE,CAACd,MAAM,EAAEI,MAAM,EAAEK,QAAQ,EAAEC,OAAO,EAAEG,WAAW,EAAEF,KAAK,CAAC;IACjEV,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE,UAAU;MAAEI,GAAG,EAAE;IAC1B,CAAC,EACD;MACEJ,KAAK,EAAE,WAAW;MAAEI,GAAG,EAAE,KAAK;MAC9Ba,QAAQ,EAAE;QAACC,IAAI,EAAE;MAAO;IAC1B,CAAC,EACD;MACElB,KAAK,EAAE,IAAI,GAAGJ;IAChB,CAAC;EAEL,CAAC;EACD,IAAIuB,WAAW,GAAG;IAChBjB,QAAQ,EAAE,CACR;MAACF,KAAK,EAAE,IAAI,GAAGL;IAAa,CAAC,EAC7B;MAACK,KAAK,EAAE,KAAK,GAAGL,aAAa,GAAG,KAAK,GAAGA,aAAa,GAAG;IAAI,CAAC;EAEjE,CAAC;EACD,IAAIyB,IAAI,GAAG;IACTpB,KAAK,EAAE,SAAS;IAAEI,GAAG,EAAE;EACzB,CAAC;EACD,IAAIiB,IAAI,GAAG;IACTC,cAAc,EAAE,IAAI;IACpBnB,SAAS,EAAE;EACb,CAAC;EACDiB,IAAI,CAACL,QAAQ,GAAG,CACd;IACEhB,SAAS,EAAE,MAAM;IACjBG,QAAQ,EAAE,CACR;MACEF,KAAK,EAAEL,aAAa;MACpBQ,SAAS,EAAE;IACb,CAAC,EACD;MAACH,KAAK,EAAEJ;IAAM,CAAC;EAEnB,CAAC,EACDyB,IAAI,CACL;EACDA,IAAI,CAACN,QAAQ,GAAG,CAACC,MAAM,EAAEG,WAAW,EAAEC,IAAI,EAAEtB,OAAO,EAAEG,MAAM,EAAEI,MAAM,EAAEI,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEE,GAAG,EAAED,KAAK,CAAC;EAE5G,OAAO;IACLM,IAAI,EAAE,MAAM;IACZV,OAAO,EAAE,IAAI;IACbO,QAAQ,EAAE,CACRd,MAAM,EACNP,IAAI,CAAC6B,OAAO,CAAC,CAAC,EACdzB,OAAO,EACPO,MAAM,EACNI,OAAO,EACPO,MAAM,EACNG,WAAW,EACXC,IAAI,EACJR,KAAK;EAET,CAAC;AACH;AAEAY,MAAM,CAACC,OAAO,GAAGhC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}