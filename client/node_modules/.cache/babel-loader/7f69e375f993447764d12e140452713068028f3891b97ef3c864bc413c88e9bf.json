{"ast": null, "code": "'use strict';\n\nmodule.exports = pug;\npug.displayName = 'pug';\npug.aliases = [];\nfunction pug(Prism) {\n  ;\n  (function (Prism) {\n    // TODO:\n    // - Add CSS highlighting inside <style> tags\n    // - Add support for multi-line code blocks\n    // - Add support for interpolation #{} and !{}\n    // - Add support for tag interpolation #[]\n    // - Add explicit support for plain text using |\n    // - Add support for markup embedded in plain text\n    Prism.languages.pug = {\n      // Multiline stuff should appear before the rest\n      // This handles both single-line and multi-line comments\n      comment: {\n        pattern: /(^([\\t ]*))\\/\\/.*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)*/m,\n        lookbehind: true\n      },\n      // All the tag-related part is in lookbehind\n      // so that it can be highlighted by the \"tag\" pattern\n      'multiline-script': {\n        pattern: /(^([\\t ]*)script\\b.*\\.[\\t ]*)(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      // See at the end of the file for known filters\n      filter: {\n        pattern: /(^([\\t ]*)):.+(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true,\n        inside: {\n          'filter-name': {\n            pattern: /^:[\\w-]+/,\n            alias: 'variable'\n          },\n          text: /\\S[\\s\\S]*/\n        }\n      },\n      'multiline-plain-text': {\n        pattern: /(^([\\t ]*)[\\w\\-#.]+\\.[\\t ]*)(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true\n      },\n      markup: {\n        pattern: /(^[\\t ]*)<.+/m,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      },\n      doctype: {\n        pattern: /((?:^|\\n)[\\t ]*)doctype(?: .+)?/,\n        lookbehind: true\n      },\n      // This handle all conditional and loop keywords\n      'flow-control': {\n        pattern: /(^[\\t ]*)(?:case|default|each|else|if|unless|when|while)\\b(?: .+)?/m,\n        lookbehind: true,\n        inside: {\n          each: {\n            pattern: /^each .+? in\\b/,\n            inside: {\n              keyword: /\\b(?:each|in)\\b/,\n              punctuation: /,/\n            }\n          },\n          branch: {\n            pattern: /^(?:case|default|else|if|unless|when|while)\\b/,\n            alias: 'keyword'\n          },\n          rest: Prism.languages.javascript\n        }\n      },\n      keyword: {\n        pattern: /(^[\\t ]*)(?:append|block|extends|include|prepend)\\b.+/m,\n        lookbehind: true\n      },\n      mixin: [\n      // Declaration\n      {\n        pattern: /(^[\\t ]*)mixin .+/m,\n        lookbehind: true,\n        inside: {\n          keyword: /^mixin/,\n          function: /\\w+(?=\\s*\\(|\\s*$)/,\n          punctuation: /[(),.]/\n        }\n      },\n      // Usage\n      {\n        pattern: /(^[\\t ]*)\\+.+/m,\n        lookbehind: true,\n        inside: {\n          name: {\n            pattern: /^\\+\\w+/,\n            alias: 'function'\n          },\n          rest: Prism.languages.javascript\n        }\n      }],\n      script: {\n        pattern: /(^[\\t ]*script(?:(?:&[^(]+)?\\([^)]+\\))*[\\t ]).+/m,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      'plain-text': {\n        pattern: /(^[\\t ]*(?!-)[\\w\\-#.]*[\\w\\-](?:(?:&[^(]+)?\\([^)]+\\))*\\/?[\\t ]).+/m,\n        lookbehind: true\n      },\n      tag: {\n        pattern: /(^[\\t ]*)(?!-)[\\w\\-#.]*[\\w\\-](?:(?:&[^(]+)?\\([^)]+\\))*\\/?:?/m,\n        lookbehind: true,\n        inside: {\n          attributes: [{\n            pattern: /&[^(]+\\([^)]+\\)/,\n            inside: Prism.languages.javascript\n          }, {\n            pattern: /\\([^)]+\\)/,\n            inside: {\n              'attr-value': {\n                pattern: /(=\\s*(?!\\s))(?:\\{[^}]*\\}|[^,)\\r\\n]+)/,\n                lookbehind: true,\n                inside: Prism.languages.javascript\n              },\n              'attr-name': /[\\w-]+(?=\\s*!?=|\\s*[,)])/,\n              punctuation: /[!=(),]+/\n            }\n          }],\n          punctuation: /:/,\n          'attr-id': /#[\\w\\-]+/,\n          'attr-class': /\\.[\\w\\-]+/\n        }\n      },\n      code: [{\n        pattern: /(^[\\t ]*(?:-|!?=)).+/m,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }],\n      punctuation: /[.\\-!=|]+/\n    };\n    var filter_pattern = /(^([\\t ]*)):<filter_name>(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/.source; // Non exhaustive list of available filters and associated languages\n    var filters = [{\n      filter: 'atpl',\n      language: 'twig'\n    }, {\n      filter: 'coffee',\n      language: 'coffeescript'\n    }, 'ejs', 'handlebars', 'less', 'livescript', 'markdown', {\n      filter: 'sass',\n      language: 'scss'\n    }, 'stylus'];\n    var all_filters = {};\n    for (var i = 0, l = filters.length; i < l; i++) {\n      var filter = filters[i];\n      filter = typeof filter === 'string' ? {\n        filter: filter,\n        language: filter\n      } : filter;\n      if (Prism.languages[filter.language]) {\n        all_filters['filter-' + filter.filter] = {\n          pattern: RegExp(filter_pattern.replace('<filter_name>', function () {\n            return filter.filter;\n          }), 'm'),\n          lookbehind: true,\n          inside: {\n            'filter-name': {\n              pattern: /^:[\\w-]+/,\n              alias: 'variable'\n            },\n            text: {\n              pattern: /\\S[\\s\\S]*/,\n              alias: [filter.language, 'language-' + filter.language],\n              inside: Prism.languages[filter.language]\n            }\n          }\n        };\n      }\n    }\n    Prism.languages.insertBefore('pug', 'filter', all_filters);\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "pug", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "inside", "javascript", "filter", "alias", "text", "markup", "doctype", "each", "keyword", "punctuation", "branch", "rest", "mixin", "function", "name", "script", "tag", "attributes", "code", "filter_pattern", "source", "filters", "language", "all_filters", "i", "l", "length", "RegExp", "replace", "insertBefore"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/pug.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pug\npug.displayName = 'pug'\npug.aliases = []\nfunction pug(Prism) {\n  ;(function (Prism) {\n    // TODO:\n    // - Add CSS highlighting inside <style> tags\n    // - Add support for multi-line code blocks\n    // - Add support for interpolation #{} and !{}\n    // - Add support for tag interpolation #[]\n    // - Add explicit support for plain text using |\n    // - Add support for markup embedded in plain text\n    Prism.languages.pug = {\n      // Multiline stuff should appear before the rest\n      // This handles both single-line and multi-line comments\n      comment: {\n        pattern: /(^([\\t ]*))\\/\\/.*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)*/m,\n        lookbehind: true\n      },\n      // All the tag-related part is in lookbehind\n      // so that it can be highlighted by the \"tag\" pattern\n      'multiline-script': {\n        pattern:\n          /(^([\\t ]*)script\\b.*\\.[\\t ]*)(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      // See at the end of the file for known filters\n      filter: {\n        pattern:\n          /(^([\\t ]*)):.+(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true,\n        inside: {\n          'filter-name': {\n            pattern: /^:[\\w-]+/,\n            alias: 'variable'\n          },\n          text: /\\S[\\s\\S]*/\n        }\n      },\n      'multiline-plain-text': {\n        pattern:\n          /(^([\\t ]*)[\\w\\-#.]+\\.[\\t ]*)(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true\n      },\n      markup: {\n        pattern: /(^[\\t ]*)<.+/m,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      },\n      doctype: {\n        pattern: /((?:^|\\n)[\\t ]*)doctype(?: .+)?/,\n        lookbehind: true\n      },\n      // This handle all conditional and loop keywords\n      'flow-control': {\n        pattern:\n          /(^[\\t ]*)(?:case|default|each|else|if|unless|when|while)\\b(?: .+)?/m,\n        lookbehind: true,\n        inside: {\n          each: {\n            pattern: /^each .+? in\\b/,\n            inside: {\n              keyword: /\\b(?:each|in)\\b/,\n              punctuation: /,/\n            }\n          },\n          branch: {\n            pattern: /^(?:case|default|else|if|unless|when|while)\\b/,\n            alias: 'keyword'\n          },\n          rest: Prism.languages.javascript\n        }\n      },\n      keyword: {\n        pattern: /(^[\\t ]*)(?:append|block|extends|include|prepend)\\b.+/m,\n        lookbehind: true\n      },\n      mixin: [\n        // Declaration\n        {\n          pattern: /(^[\\t ]*)mixin .+/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^mixin/,\n            function: /\\w+(?=\\s*\\(|\\s*$)/,\n            punctuation: /[(),.]/\n          }\n        }, // Usage\n        {\n          pattern: /(^[\\t ]*)\\+.+/m,\n          lookbehind: true,\n          inside: {\n            name: {\n              pattern: /^\\+\\w+/,\n              alias: 'function'\n            },\n            rest: Prism.languages.javascript\n          }\n        }\n      ],\n      script: {\n        pattern: /(^[\\t ]*script(?:(?:&[^(]+)?\\([^)]+\\))*[\\t ]).+/m,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      'plain-text': {\n        pattern:\n          /(^[\\t ]*(?!-)[\\w\\-#.]*[\\w\\-](?:(?:&[^(]+)?\\([^)]+\\))*\\/?[\\t ]).+/m,\n        lookbehind: true\n      },\n      tag: {\n        pattern: /(^[\\t ]*)(?!-)[\\w\\-#.]*[\\w\\-](?:(?:&[^(]+)?\\([^)]+\\))*\\/?:?/m,\n        lookbehind: true,\n        inside: {\n          attributes: [\n            {\n              pattern: /&[^(]+\\([^)]+\\)/,\n              inside: Prism.languages.javascript\n            },\n            {\n              pattern: /\\([^)]+\\)/,\n              inside: {\n                'attr-value': {\n                  pattern: /(=\\s*(?!\\s))(?:\\{[^}]*\\}|[^,)\\r\\n]+)/,\n                  lookbehind: true,\n                  inside: Prism.languages.javascript\n                },\n                'attr-name': /[\\w-]+(?=\\s*!?=|\\s*[,)])/,\n                punctuation: /[!=(),]+/\n              }\n            }\n          ],\n          punctuation: /:/,\n          'attr-id': /#[\\w\\-]+/,\n          'attr-class': /\\.[\\w\\-]+/\n        }\n      },\n      code: [\n        {\n          pattern: /(^[\\t ]*(?:-|!?=)).+/m,\n          lookbehind: true,\n          inside: Prism.languages.javascript\n        }\n      ],\n      punctuation: /[.\\-!=|]+/\n    }\n    var filter_pattern =\n      /(^([\\t ]*)):<filter_name>(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/\n        .source // Non exhaustive list of available filters and associated languages\n    var filters = [\n      {\n        filter: 'atpl',\n        language: 'twig'\n      },\n      {\n        filter: 'coffee',\n        language: 'coffeescript'\n      },\n      'ejs',\n      'handlebars',\n      'less',\n      'livescript',\n      'markdown',\n      {\n        filter: 'sass',\n        language: 'scss'\n      },\n      'stylus'\n    ]\n    var all_filters = {}\n    for (var i = 0, l = filters.length; i < l; i++) {\n      var filter = filters[i]\n      filter =\n        typeof filter === 'string'\n          ? {\n              filter: filter,\n              language: filter\n            }\n          : filter\n      if (Prism.languages[filter.language]) {\n        all_filters['filter-' + filter.filter] = {\n          pattern: RegExp(\n            filter_pattern.replace('<filter_name>', function () {\n              return filter.filter\n            }),\n            'm'\n          ),\n          lookbehind: true,\n          inside: {\n            'filter-name': {\n              pattern: /^:[\\w-]+/,\n              alias: 'variable'\n            },\n            text: {\n              pattern: /\\S[\\s\\S]*/,\n              alias: [filter.language, 'language-' + filter.language],\n              inside: Prism.languages[filter.language]\n            }\n          }\n        }\n      }\n    }\n    Prism.languages.insertBefore('pug', 'filter', all_filters)\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;MACpB;MACA;MACAK,OAAO,EAAE;QACPC,OAAO,EAAE,8CAA8C;QACvDC,UAAU,EAAE;MACd,CAAC;MACD;MACA;MACA,kBAAkB,EAAE;QAClBD,OAAO,EACL,qFAAqF;QACvFC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEL,KAAK,CAACC,SAAS,CAACK;MAC1B,CAAC;MACD;MACAC,MAAM,EAAE;QACNJ,OAAO,EACL,sEAAsE;QACxEC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACN,aAAa,EAAE;YACbF,OAAO,EAAE,UAAU;YACnBK,KAAK,EAAE;UACT,CAAC;UACDC,IAAI,EAAE;QACR;MACF,CAAC;MACD,sBAAsB,EAAE;QACtBN,OAAO,EACL,oFAAoF;QACtFC,UAAU,EAAE;MACd,CAAC;MACDM,MAAM,EAAE;QACNP,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEL,KAAK,CAACC,SAAS,CAACS;MAC1B,CAAC;MACDC,OAAO,EAAE;QACPR,OAAO,EAAE,iCAAiC;QAC1CC,UAAU,EAAE;MACd,CAAC;MACD;MACA,cAAc,EAAE;QACdD,OAAO,EACL,qEAAqE;QACvEC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNO,IAAI,EAAE;YACJT,OAAO,EAAE,gBAAgB;YACzBE,MAAM,EAAE;cACNQ,OAAO,EAAE,iBAAiB;cAC1BC,WAAW,EAAE;YACf;UACF,CAAC;UACDC,MAAM,EAAE;YACNZ,OAAO,EAAE,+CAA+C;YACxDK,KAAK,EAAE;UACT,CAAC;UACDQ,IAAI,EAAEhB,KAAK,CAACC,SAAS,CAACK;QACxB;MACF,CAAC;MACDO,OAAO,EAAE;QACPV,OAAO,EAAE,wDAAwD;QACjEC,UAAU,EAAE;MACd,CAAC;MACDa,KAAK,EAAE;MACL;MACA;QACEd,OAAO,EAAE,oBAAoB;QAC7BC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNQ,OAAO,EAAE,QAAQ;UACjBK,QAAQ,EAAE,mBAAmB;UAC7BJ,WAAW,EAAE;QACf;MACF,CAAC;MAAE;MACH;QACEX,OAAO,EAAE,gBAAgB;QACzBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNc,IAAI,EAAE;YACJhB,OAAO,EAAE,QAAQ;YACjBK,KAAK,EAAE;UACT,CAAC;UACDQ,IAAI,EAAEhB,KAAK,CAACC,SAAS,CAACK;QACxB;MACF,CAAC,CACF;MACDc,MAAM,EAAE;QACNjB,OAAO,EAAE,kDAAkD;QAC3DC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEL,KAAK,CAACC,SAAS,CAACK;MAC1B,CAAC;MACD,YAAY,EAAE;QACZH,OAAO,EACL,mEAAmE;QACrEC,UAAU,EAAE;MACd,CAAC;MACDiB,GAAG,EAAE;QACHlB,OAAO,EAAE,8DAA8D;QACvEC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNiB,UAAU,EAAE,CACV;YACEnB,OAAO,EAAE,iBAAiB;YAC1BE,MAAM,EAAEL,KAAK,CAACC,SAAS,CAACK;UAC1B,CAAC,EACD;YACEH,OAAO,EAAE,WAAW;YACpBE,MAAM,EAAE;cACN,YAAY,EAAE;gBACZF,OAAO,EAAE,sCAAsC;gBAC/CC,UAAU,EAAE,IAAI;gBAChBC,MAAM,EAAEL,KAAK,CAACC,SAAS,CAACK;cAC1B,CAAC;cACD,WAAW,EAAE,0BAA0B;cACvCQ,WAAW,EAAE;YACf;UACF,CAAC,CACF;UACDA,WAAW,EAAE,GAAG;UAChB,SAAS,EAAE,UAAU;UACrB,YAAY,EAAE;QAChB;MACF,CAAC;MACDS,IAAI,EAAE,CACJ;QACEpB,OAAO,EAAE,uBAAuB;QAChCC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEL,KAAK,CAACC,SAAS,CAACK;MAC1B,CAAC,CACF;MACDQ,WAAW,EAAE;IACf,CAAC;IACD,IAAIU,cAAc,GAChB,gFAAgF,CAC7EC,MAAM,EAAC;IACZ,IAAIC,OAAO,GAAG,CACZ;MACEnB,MAAM,EAAE,MAAM;MACdoB,QAAQ,EAAE;IACZ,CAAC,EACD;MACEpB,MAAM,EAAE,QAAQ;MAChBoB,QAAQ,EAAE;IACZ,CAAC,EACD,KAAK,EACL,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,UAAU,EACV;MACEpB,MAAM,EAAE,MAAM;MACdoB,QAAQ,EAAE;IACZ,CAAC,EACD,QAAQ,CACT;IACD,IAAIC,WAAW,GAAG,CAAC,CAAC;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,OAAO,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAItB,MAAM,GAAGmB,OAAO,CAACG,CAAC,CAAC;MACvBtB,MAAM,GACJ,OAAOA,MAAM,KAAK,QAAQ,GACtB;QACEA,MAAM,EAAEA,MAAM;QACdoB,QAAQ,EAAEpB;MACZ,CAAC,GACDA,MAAM;MACZ,IAAIP,KAAK,CAACC,SAAS,CAACM,MAAM,CAACoB,QAAQ,CAAC,EAAE;QACpCC,WAAW,CAAC,SAAS,GAAGrB,MAAM,CAACA,MAAM,CAAC,GAAG;UACvCJ,OAAO,EAAE6B,MAAM,CACbR,cAAc,CAACS,OAAO,CAAC,eAAe,EAAE,YAAY;YAClD,OAAO1B,MAAM,CAACA,MAAM;UACtB,CAAC,CAAC,EACF,GACF,CAAC;UACDH,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACN,aAAa,EAAE;cACbF,OAAO,EAAE,UAAU;cACnBK,KAAK,EAAE;YACT,CAAC;YACDC,IAAI,EAAE;cACJN,OAAO,EAAE,WAAW;cACpBK,KAAK,EAAE,CAACD,MAAM,CAACoB,QAAQ,EAAE,WAAW,GAAGpB,MAAM,CAACoB,QAAQ,CAAC;cACvDtB,MAAM,EAAEL,KAAK,CAACC,SAAS,CAACM,MAAM,CAACoB,QAAQ;YACzC;UACF;QACF,CAAC;MACH;IACF;IACA3B,KAAK,CAACC,SAAS,CAACiC,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAEN,WAAW,CAAC;EAC5D,CAAC,EAAE5B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}