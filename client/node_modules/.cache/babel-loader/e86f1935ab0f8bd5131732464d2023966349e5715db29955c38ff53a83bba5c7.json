{"ast": null, "code": "'use strict';\n\nmodule.exports = solutionFile;\nsolutionFile.displayName = 'solutionFile';\nsolutionFile.aliases = [];\nfunction solutionFile(Prism) {\n  ;\n  (function (Prism) {\n    var guid = {\n      // https://en.wikipedia.org/wiki/Universally_unique_identifier#Format\n      pattern: /\\{[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}\\}/i,\n      alias: 'constant',\n      inside: {\n        punctuation: /[{}]/\n      }\n    };\n    Prism.languages['solution-file'] = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n        greedy: true,\n        inside: {\n          guid: guid\n        }\n      },\n      object: {\n        // Foo\n        //   Bar(\"abs\") = 9\n        //   EndBar\n        //   Prop = TRUE\n        // EndFoo\n        pattern: /^([ \\t]*)(?:([A-Z]\\w*)\\b(?=.*(?:\\r\\n?|\\n)(?:\\1[ \\t].*(?:\\r\\n?|\\n))*\\1End\\2(?=[ \\t]*$))|End[A-Z]\\w*(?=[ \\t]*$))/m,\n        lookbehind: true,\n        greedy: true,\n        alias: 'keyword'\n      },\n      property: {\n        pattern: /^([ \\t]*)(?!\\s)[^\\r\\n\"#=()]*[^\\s\"#=()](?=\\s*=)/m,\n        lookbehind: true,\n        inside: {\n          guid: guid\n        }\n      },\n      guid: guid,\n      number: /\\b\\d+(?:\\.\\d+)*\\b/,\n      boolean: /\\b(?:FALSE|TRUE)\\b/,\n      operator: /=/,\n      punctuation: /[(),]/\n    };\n    Prism.languages['sln'] = Prism.languages['solution-file'];\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "solutionFile", "displayName", "aliases", "Prism", "guid", "pattern", "alias", "inside", "punctuation", "languages", "comment", "greedy", "string", "object", "lookbehind", "property", "number", "boolean", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/solution-file.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = solutionFile\nsolutionFile.displayName = 'solutionFile'\nsolutionFile.aliases = []\nfunction solutionFile(Prism) {\n  ;(function (Prism) {\n    var guid = {\n      // https://en.wikipedia.org/wiki/Universally_unique_identifier#Format\n      pattern: /\\{[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}\\}/i,\n      alias: 'constant',\n      inside: {\n        punctuation: /[{}]/\n      }\n    }\n    Prism.languages['solution-file'] = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n        greedy: true,\n        inside: {\n          guid: guid\n        }\n      },\n      object: {\n        // Foo\n        //   Bar(\"abs\") = 9\n        //   EndBar\n        //   Prop = TRUE\n        // EndFoo\n        pattern:\n          /^([ \\t]*)(?:([A-Z]\\w*)\\b(?=.*(?:\\r\\n?|\\n)(?:\\1[ \\t].*(?:\\r\\n?|\\n))*\\1End\\2(?=[ \\t]*$))|End[A-Z]\\w*(?=[ \\t]*$))/m,\n        lookbehind: true,\n        greedy: true,\n        alias: 'keyword'\n      },\n      property: {\n        pattern: /^([ \\t]*)(?!\\s)[^\\r\\n\"#=()]*[^\\s\"#=()](?=\\s*=)/m,\n        lookbehind: true,\n        inside: {\n          guid: guid\n        }\n      },\n      guid: guid,\n      number: /\\b\\d+(?:\\.\\d+)*\\b/,\n      boolean: /\\b(?:FALSE|TRUE)\\b/,\n      operator: /=/,\n      punctuation: /[(),]/\n    }\n    Prism.languages['sln'] = Prism.languages['solution-file']\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3B;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,IAAI,GAAG;MACT;MACAC,OAAO,EAAE,8DAA8D;MACvEC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACDL,KAAK,CAACM,SAAS,CAAC,eAAe,CAAC,GAAG;MACjCC,OAAO,EAAE;QACPL,OAAO,EAAE,KAAK;QACdM,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNP,OAAO,EAAE,yBAAyB;QAClCM,MAAM,EAAE,IAAI;QACZJ,MAAM,EAAE;UACNH,IAAI,EAAEA;QACR;MACF,CAAC;MACDS,MAAM,EAAE;QACN;QACA;QACA;QACA;QACA;QACAR,OAAO,EACL,iHAAiH;QACnHS,UAAU,EAAE,IAAI;QAChBH,MAAM,EAAE,IAAI;QACZL,KAAK,EAAE;MACT,CAAC;MACDS,QAAQ,EAAE;QACRV,OAAO,EAAE,iDAAiD;QAC1DS,UAAU,EAAE,IAAI;QAChBP,MAAM,EAAE;UACNH,IAAI,EAAEA;QACR;MACF,CAAC;MACDA,IAAI,EAAEA,IAAI;MACVY,MAAM,EAAE,mBAAmB;MAC3BC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EAAE,GAAG;MACbV,WAAW,EAAE;IACf,CAAC;IACDL,KAAK,CAACM,SAAS,CAAC,KAAK,CAAC,GAAGN,KAAK,CAACM,SAAS,CAAC,eAAe,CAAC;EAC3D,CAAC,EAAEN,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}