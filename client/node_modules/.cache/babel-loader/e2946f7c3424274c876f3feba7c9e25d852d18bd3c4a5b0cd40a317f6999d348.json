{"ast": null, "code": "'use strict';\n\nvar refractorJavadoclike = require('./javadoclike.js');\nvar refractorTypescript = require('./typescript.js');\nmodule.exports = jsdoc;\njsdoc.displayName = 'jsdoc';\njsdoc.aliases = [];\nfunction jsdoc(Prism) {\n  Prism.register(refractorJavadoclike);\n  Prism.register(refractorTypescript);\n  (function (Prism) {\n    var javascript = Prism.languages.javascript;\n    var type = /\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})+\\}/.source;\n    var parameterPrefix = '(@(?:arg|argument|param|property)\\\\s+(?:' + type + '\\\\s+)?)';\n    Prism.languages.jsdoc = Prism.languages.extend('javadoclike', {\n      parameter: {\n        // @param {string} foo - foo bar\n        pattern: RegExp(parameterPrefix + /(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?=\\s|$)/.source),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    });\n    Prism.languages.insertBefore('jsdoc', 'keyword', {\n      'optional-parameter': {\n        // @param {string} [baz.foo=\"bar\"] foo bar\n        pattern: RegExp(parameterPrefix + /\\[(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?:=[^[\\]]+)?\\](?=\\s|$)/.source),\n        lookbehind: true,\n        inside: {\n          parameter: {\n            pattern: /(^\\[)[$\\w\\xA0-\\uFFFF\\.]+/,\n            lookbehind: true,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          code: {\n            pattern: /(=)[\\s\\S]*(?=\\]$)/,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          },\n          punctuation: /[=[\\]]/\n        }\n      },\n      'class-name': [{\n        pattern: RegExp(/(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\\s+(?:<TYPE>\\s+)?)[A-Z]\\w*(?:\\.[A-Z]\\w*)*/.source.replace(/<TYPE>/g, function () {\n          return type;\n        })),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }, {\n        pattern: RegExp('(@[a-z]+\\\\s+)' + type),\n        lookbehind: true,\n        inside: {\n          string: javascript.string,\n          number: javascript.number,\n          boolean: javascript.boolean,\n          keyword: Prism.languages.typescript.keyword,\n          operator: /=>|\\.\\.\\.|[&|?:*]/,\n          punctuation: /[.,;=<>{}()[\\]]/\n        }\n      }],\n      example: {\n        pattern: /(@example\\s+(?!\\s))(?:[^@\\s]|\\s+(?!\\s))+?(?=\\s*(?:\\*\\s*)?(?:@\\w|\\*\\/))/,\n        lookbehind: true,\n        inside: {\n          code: {\n            pattern: /^([\\t ]*(?:\\*\\s*)?)\\S.*$/m,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          }\n        }\n      }\n    });\n    Prism.languages.javadoclike.addSupport('javascript', Prism.languages.jsdoc);\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorJavadoclike", "require", "refractorTypescript", "module", "exports", "jsdoc", "displayName", "aliases", "Prism", "register", "javascript", "languages", "type", "source", "parameterPrefix", "extend", "parameter", "pattern", "RegExp", "lookbehind", "inside", "punctuation", "insertBefore", "code", "alias", "replace", "string", "number", "boolean", "keyword", "typescript", "operator", "example", "javadoclike", "addSupport"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/jsdoc.js"], "sourcesContent": ["'use strict'\nvar refractorJavadoclike = require('./javadoclike.js')\nvar refractorTypescript = require('./typescript.js')\nmodule.exports = jsdoc\njsdoc.displayName = 'jsdoc'\njsdoc.aliases = []\nfunction jsdoc(Prism) {\n  Prism.register(refractorJavadoclike)\n  Prism.register(refractorTypescript)\n  ;(function (Prism) {\n    var javascript = Prism.languages.javascript\n    var type = /\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})+\\}/.source\n    var parameterPrefix =\n      '(@(?:arg|argument|param|property)\\\\s+(?:' + type + '\\\\s+)?)'\n    Prism.languages.jsdoc = Prism.languages.extend('javadoclike', {\n      parameter: {\n        // @param {string} foo - foo bar\n        pattern: RegExp(\n          parameterPrefix + /(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?=\\s|$)/.source\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    })\n    Prism.languages.insertBefore('jsdoc', 'keyword', {\n      'optional-parameter': {\n        // @param {string} [baz.foo=\"bar\"] foo bar\n        pattern: RegExp(\n          parameterPrefix +\n            /\\[(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?:=[^[\\]]+)?\\](?=\\s|$)/.source\n        ),\n        lookbehind: true,\n        inside: {\n          parameter: {\n            pattern: /(^\\[)[$\\w\\xA0-\\uFFFF\\.]+/,\n            lookbehind: true,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          code: {\n            pattern: /(=)[\\s\\S]*(?=\\]$)/,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          },\n          punctuation: /[=[\\]]/\n        }\n      },\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\\s+(?:<TYPE>\\s+)?)[A-Z]\\w*(?:\\.[A-Z]\\w*)*/.source.replace(\n              /<TYPE>/g,\n              function () {\n                return type\n              }\n            )\n          ),\n          lookbehind: true,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        {\n          pattern: RegExp('(@[a-z]+\\\\s+)' + type),\n          lookbehind: true,\n          inside: {\n            string: javascript.string,\n            number: javascript.number,\n            boolean: javascript.boolean,\n            keyword: Prism.languages.typescript.keyword,\n            operator: /=>|\\.\\.\\.|[&|?:*]/,\n            punctuation: /[.,;=<>{}()[\\]]/\n          }\n        }\n      ],\n      example: {\n        pattern:\n          /(@example\\s+(?!\\s))(?:[^@\\s]|\\s+(?!\\s))+?(?=\\s*(?:\\*\\s*)?(?:@\\w|\\*\\/))/,\n        lookbehind: true,\n        inside: {\n          code: {\n            pattern: /^([\\t ]*(?:\\*\\s*)?)\\S.*$/m,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          }\n        }\n      }\n    })\n    Prism.languages.javadoclike.addSupport('javascript', Prism.languages.jsdoc)\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,oBAAoB,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACtD,IAAIC,mBAAmB,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AACpDE,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACT,oBAAoB,CAAC;EACpCQ,KAAK,CAACC,QAAQ,CAACP,mBAAmB,CAAC;EAClC,CAAC,UAAUM,KAAK,EAAE;IACjB,IAAIE,UAAU,GAAGF,KAAK,CAACG,SAAS,CAACD,UAAU;IAC3C,IAAIE,IAAI,GAAG,0CAA0C,CAACC,MAAM;IAC5D,IAAIC,eAAe,GACjB,0CAA0C,GAAGF,IAAI,GAAG,SAAS;IAC/DJ,KAAK,CAACG,SAAS,CAACN,KAAK,GAAGG,KAAK,CAACG,SAAS,CAACI,MAAM,CAAC,aAAa,EAAE;MAC5DC,SAAS,EAAE;QACT;QACAC,OAAO,EAAEC,MAAM,CACbJ,eAAe,GAAG,sCAAsC,CAACD,MAC3D,CAAC;QACDM,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF;IACF,CAAC,CAAC;IACFb,KAAK,CAACG,SAAS,CAACW,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE;MAC/C,oBAAoB,EAAE;QACpB;QACAL,OAAO,EAAEC,MAAM,CACbJ,eAAe,GACb,uDAAuD,CAACD,MAC5D,CAAC;QACDM,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNJ,SAAS,EAAE;YACTC,OAAO,EAAE,0BAA0B;YACnCE,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;cACNC,WAAW,EAAE;YACf;UACF,CAAC;UACDE,IAAI,EAAE;YACJN,OAAO,EAAE,mBAAmB;YAC5BE,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAEV,UAAU;YAClBc,KAAK,EAAE;UACT,CAAC;UACDH,WAAW,EAAE;QACf;MACF,CAAC;MACD,YAAY,EAAE,CACZ;QACEJ,OAAO,EAAEC,MAAM,CACb,kHAAkH,CAACL,MAAM,CAACY,OAAO,CAC/H,SAAS,EACT,YAAY;UACV,OAAOb,IAAI;QACb,CACF,CACF,CAAC;QACDO,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEJ,OAAO,EAAEC,MAAM,CAAC,eAAe,GAAGN,IAAI,CAAC;QACvCO,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNM,MAAM,EAAEhB,UAAU,CAACgB,MAAM;UACzBC,MAAM,EAAEjB,UAAU,CAACiB,MAAM;UACzBC,OAAO,EAAElB,UAAU,CAACkB,OAAO;UAC3BC,OAAO,EAAErB,KAAK,CAACG,SAAS,CAACmB,UAAU,CAACD,OAAO;UAC3CE,QAAQ,EAAE,mBAAmB;UAC7BV,WAAW,EAAE;QACf;MACF,CAAC,CACF;MACDW,OAAO,EAAE;QACPf,OAAO,EACL,wEAAwE;QAC1EE,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNG,IAAI,EAAE;YACJN,OAAO,EAAE,2BAA2B;YACpCE,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAEV,UAAU;YAClBc,KAAK,EAAE;UACT;QACF;MACF;IACF,CAAC,CAAC;IACFhB,KAAK,CAACG,SAAS,CAACsB,WAAW,CAACC,UAAU,CAAC,YAAY,EAAE1B,KAAK,CAACG,SAAS,CAACN,KAAK,CAAC;EAC7E,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}