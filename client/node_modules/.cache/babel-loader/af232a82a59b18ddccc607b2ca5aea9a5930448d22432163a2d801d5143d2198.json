{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cPlusPlus(hljs) {\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', {\n    contains: [{\n      begin: /\\\\\\n/\n    }]\n  });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '(' + DECLTYPE_AUTO_RE + '|' + optional(NAMESPACE_RE) + '[a-zA-Z_]\\\\w*' + optional(TEMPLATE_ARGUMENT_RE) + ')';\n  const CPP_PRIMITIVE_TYPES = {\n    className: 'keyword',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [{\n      begin: '(u8?|U|L)?\"',\n      end: '\"',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + \"|.)\",\n      end: '\\'',\n      illegal: '.'\n    }, hljs.END_SAME_AS_BEGIN({\n      begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n      end: /\\)([^()\\\\ ]{0,16})\"/\n    })]\n  };\n  const NUMBERS = {\n    className: 'number',\n    variants: [{\n      begin: '\\\\b(0b[01\\']+)'\n    }, {\n      begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)'\n    }, {\n      begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n    }],\n    relevance: 0\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword': 'if else elif endif define undef warning error line ' + 'pragma _Pragma ifdef ifndef include'\n    },\n    contains: [{\n      begin: /\\\\\\n/,\n      relevance: 0\n    }, hljs.inherit(STRINGS, {\n      className: 'meta-string'\n    }), {\n      className: 'meta-string',\n      begin: /<.*?>/\n    }, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n  };\n  const TITLE_MODE = {\n    className: 'title',\n    begin: optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n  const FUNCTION_TITLE = optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n  const COMMON_CPP_HINTS = ['asin', 'atan2', 'atan', 'calloc', 'ceil', 'cosh', 'cos', 'exit', 'exp', 'fabs', 'floor', 'fmod', 'fprintf', 'fputs', 'free', 'frexp', 'auto_ptr', 'deque', 'list', 'queue', 'stack', 'vector', 'map', 'set', 'pair', 'bitset', 'multiset', 'multimap', 'unordered_set', 'fscanf', 'future', 'isalnum', 'isalpha', 'iscntrl', 'isdigit', 'isgraph', 'islower', 'isprint', 'ispunct', 'isspace', 'isupper', 'isxdigit', 'tolower', 'toupper', 'labs', 'ldexp', 'log10', 'log', 'malloc', 'realloc', 'memchr', 'memcmp', 'memcpy', 'memset', 'modf', 'pow', 'printf', 'putchar', 'puts', 'scanf', 'sinh', 'sin', 'snprintf', 'sprintf', 'sqrt', 'sscanf', 'strcat', 'strchr', 'strcmp', 'strcpy', 'strcspn', 'strlen', 'strncat', 'strncmp', 'strncpy', 'strpbrk', 'strrchr', 'strspn', 'strstr', 'tanh', 'tan', 'unordered_map', 'unordered_multiset', 'unordered_multimap', 'priority_queue', 'make_pair', 'array', 'shared_ptr', 'abort', 'terminate', 'abs', 'acos', 'vfprintf', 'vprintf', 'vsprintf', 'endl', 'initializer_list', 'unique_ptr', 'complex', 'imaginary', 'std', 'string', 'wstring', 'cin', 'cout', 'cerr', 'clog', 'stdin', 'stdout', 'stderr', 'stringstream', 'istringstream', 'ostringstream'];\n  const CPP_KEYWORDS = {\n    keyword: 'int float while private char char8_t char16_t char32_t catch import module export virtual operator sizeof ' + 'dynamic_cast|10 typedef const_cast|10 const for static_cast|10 union namespace ' + 'unsigned long volatile static protected bool template mutable if public friend ' + 'do goto auto void enum else break extern using asm case typeid wchar_t ' + 'short reinterpret_cast|10 default double register explicit signed typename try this ' + 'switch continue inline delete alignas alignof constexpr consteval constinit decltype ' + 'concept co_await co_return co_yield requires ' + 'noexcept static_assert thread_local restrict final override ' + 'atomic_bool atomic_char atomic_schar ' + 'atomic_uchar atomic_short atomic_ushort atomic_int atomic_uint atomic_long atomic_ulong atomic_llong ' + 'atomic_ullong new throw return ' + 'and and_eq bitand bitor compl not not_eq or or_eq xor xor_eq',\n    built_in: '_Bool _Complex _Imaginary',\n    _relevance_hints: COMMON_CPP_HINTS,\n    literal: 'true false nullptr NULL'\n  };\n  const FUNCTION_DISPATCH = {\n    className: \"function.dispatch\",\n    relevance: 0,\n    keywords: CPP_KEYWORDS,\n    begin: concat(/\\b/, /(?!decltype)/, /(?!if)/, /(?!for)/, /(?!while)/, hljs.IDENT_RE, lookahead(/\\s*\\(/))\n  };\n  const EXPRESSION_CONTAINS = [FUNCTION_DISPATCH, PREPROCESSOR, CPP_PRIMITIVE_TYPES, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, NUMBERS, STRINGS];\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [{\n      begin: /=/,\n      end: /;/\n    }, {\n      begin: /\\(/,\n      end: /\\)/\n    }, {\n      beginKeywords: 'new throw return else',\n      end: /;/\n    }],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([{\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: CPP_KEYWORDS,\n      contains: EXPRESSION_CONTAINS.concat(['self']),\n      relevance: 0\n    }]),\n    relevance: 0\n  };\n  const FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [{\n      // to prevent it from being confused as the function title\n      begin: DECLTYPE_AUTO_RE,\n      keywords: CPP_KEYWORDS,\n      relevance: 0\n    }, {\n      begin: FUNCTION_TITLE,\n      returnBegin: true,\n      contains: [TITLE_MODE],\n      relevance: 0\n    },\n    // needed because we do not have look-behind on the below rule\n    // to prevent it from grabbing the final : in a :: pair\n    {\n      begin: /::/,\n      relevance: 0\n    },\n    // initializers\n    {\n      begin: /:/,\n      endsWithParent: true,\n      contains: [STRINGS, NUMBERS]\n    }, {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: CPP_KEYWORDS,\n      relevance: 0,\n      contains: [C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRINGS, NUMBERS, CPP_PRIMITIVE_TYPES,\n      // Count matching parentheses.\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: ['self', C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRINGS, NUMBERS, CPP_PRIMITIVE_TYPES]\n      }]\n    }, CPP_PRIMITIVE_TYPES, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, PREPROCESSOR]\n  };\n  return {\n    name: 'C++',\n    aliases: ['cc', 'c++', 'h++', 'hpp', 'hh', 'hxx', 'cxx'],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: {\n      \"function.dispatch\": \"built_in\"\n    },\n    contains: [].concat(EXPRESSION_CONTEXT, FUNCTION_DECLARATION, FUNCTION_DISPATCH, EXPRESSION_CONTAINS, [PREPROCESSOR, {\n      // containers: ie, `vector <int> rooms (9);`\n      begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array)\\\\s*<',\n      end: '>',\n      keywords: CPP_KEYWORDS,\n      contains: ['self', CPP_PRIMITIVE_TYPES]\n    }, {\n      begin: hljs.IDENT_RE + '::',\n      keywords: CPP_KEYWORDS\n    }, {\n      className: 'class',\n      beginKeywords: 'enum class struct union',\n      end: /[{;:<>=]/,\n      contains: [{\n        beginKeywords: \"final class struct\"\n      }, hljs.TITLE_MODE]\n    }]),\n    exports: {\n      preprocessor: PREPROCESSOR,\n      strings: STRINGS,\n      keywords: CPP_KEYWORDS\n    }\n  };\n}\n\n/*\nLanguage: C-like (deprecated, use C and C++ instead)\nAuthor: Ivan Sagalaev <<EMAIL>>\nContributors: Evgeny Stepanischev <<EMAIL>>, Zaven Muradyan <<EMAIL>>, Roel Deckers <<EMAIL>>, Sam Wu <<EMAIL>>, Jordi Petit <<EMAIL>>, Pieter Vantorre <<EMAIL>>, Google Inc. (David Benjamin) <<EMAIL>>\n*/\n\n/** @type LanguageFn */\nfunction cLike(hljs) {\n  const lang = cPlusPlus(hljs);\n  const C_ALIASES = [\"c\", \"h\"];\n  const CPP_ALIASES = ['cc', 'c++', 'h++', 'hpp', 'hh', 'hxx', 'cxx'];\n  lang.disableAutodetect = true;\n  lang.aliases = [];\n  // support users only loading c-like (legacy)\n  if (!hljs.getLanguage(\"c\")) lang.aliases.push(...C_ALIASES);\n  if (!hljs.getLanguage(\"cpp\")) lang.aliases.push(...CPP_ALIASES);\n\n  // if c and cpp are loaded after then they will reclaim these\n  // aliases for themselves\n\n  return lang;\n}\nmodule.exports = cLike;", "map": {"version": 3, "names": ["source", "re", "<PERSON><PERSON><PERSON>", "concat", "optional", "args", "joined", "map", "x", "join", "cPlusPlus", "hljs", "C_LINE_COMMENT_MODE", "COMMENT", "contains", "begin", "DECLTYPE_AUTO_RE", "NAMESPACE_RE", "TEMPLATE_ARGUMENT_RE", "FUNCTION_TYPE_RE", "CPP_PRIMITIVE_TYPES", "className", "CHARACTER_ESCAPES", "STRINGS", "variants", "end", "illegal", "BACKSLASH_ESCAPE", "END_SAME_AS_BEGIN", "NUMBERS", "relevance", "PREPROCESSOR", "keywords", "inherit", "C_BLOCK_COMMENT_MODE", "TITLE_MODE", "IDENT_RE", "FUNCTION_TITLE", "COMMON_CPP_HINTS", "CPP_KEYWORDS", "keyword", "built_in", "_relevance_hints", "literal", "FUNCTION_DISPATCH", "EXPRESSION_CONTAINS", "EXPRESSION_CONTEXT", "beginKeywords", "FUNCTION_DECLARATION", "returnBegin", "excludeEnd", "endsWithParent", "name", "aliases", "classNameAliases", "exports", "preprocessor", "strings", "cLike", "lang", "C_ALIASES", "CPP_ALIASES", "disableAutodetect", "getLanguage", "push", "module"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/c-like.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cPlusPlus(hljs) {\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', {\n    contains: [\n      {\n        begin: /\\\\\\n/\n      }\n    ]\n  });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '(' +\n    DECLTYPE_AUTO_RE + '|' +\n    optional(NAMESPACE_RE) +\n    '[a-zA-Z_]\\\\w*' + optional(TEMPLATE_ARGUMENT_RE) +\n  ')';\n  const CPP_PRIMITIVE_TYPES = {\n    className: 'keyword',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + \"|.)\",\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(0b[01\\']+)'\n      },\n      {\n        begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)'\n      },\n      {\n        begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n      }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword':\n        'if else elif endif define undef warning error line ' +\n        'pragma _Pragma ifdef ifndef include'\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, {\n        className: 'meta-string'\n      }),\n      {\n        className: 'meta-string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  const COMMON_CPP_HINTS = [\n    'asin',\n    'atan2',\n    'atan',\n    'calloc',\n    'ceil',\n    'cosh',\n    'cos',\n    'exit',\n    'exp',\n    'fabs',\n    'floor',\n    'fmod',\n    'fprintf',\n    'fputs',\n    'free',\n    'frexp',\n    'auto_ptr',\n    'deque',\n    'list',\n    'queue',\n    'stack',\n    'vector',\n    'map',\n    'set',\n    'pair',\n    'bitset',\n    'multiset',\n    'multimap',\n    'unordered_set',\n    'fscanf',\n    'future',\n    'isalnum',\n    'isalpha',\n    'iscntrl',\n    'isdigit',\n    'isgraph',\n    'islower',\n    'isprint',\n    'ispunct',\n    'isspace',\n    'isupper',\n    'isxdigit',\n    'tolower',\n    'toupper',\n    'labs',\n    'ldexp',\n    'log10',\n    'log',\n    'malloc',\n    'realloc',\n    'memchr',\n    'memcmp',\n    'memcpy',\n    'memset',\n    'modf',\n    'pow',\n    'printf',\n    'putchar',\n    'puts',\n    'scanf',\n    'sinh',\n    'sin',\n    'snprintf',\n    'sprintf',\n    'sqrt',\n    'sscanf',\n    'strcat',\n    'strchr',\n    'strcmp',\n    'strcpy',\n    'strcspn',\n    'strlen',\n    'strncat',\n    'strncmp',\n    'strncpy',\n    'strpbrk',\n    'strrchr',\n    'strspn',\n    'strstr',\n    'tanh',\n    'tan',\n    'unordered_map',\n    'unordered_multiset',\n    'unordered_multimap',\n    'priority_queue',\n    'make_pair',\n    'array',\n    'shared_ptr',\n    'abort',\n    'terminate',\n    'abs',\n    'acos',\n    'vfprintf',\n    'vprintf',\n    'vsprintf',\n    'endl',\n    'initializer_list',\n    'unique_ptr',\n    'complex',\n    'imaginary',\n    'std',\n    'string',\n    'wstring',\n    'cin',\n    'cout',\n    'cerr',\n    'clog',\n    'stdin',\n    'stdout',\n    'stderr',\n    'stringstream',\n    'istringstream',\n    'ostringstream'\n  ];\n\n  const CPP_KEYWORDS = {\n    keyword: 'int float while private char char8_t char16_t char32_t catch import module export virtual operator sizeof ' +\n      'dynamic_cast|10 typedef const_cast|10 const for static_cast|10 union namespace ' +\n      'unsigned long volatile static protected bool template mutable if public friend ' +\n      'do goto auto void enum else break extern using asm case typeid wchar_t ' +\n      'short reinterpret_cast|10 default double register explicit signed typename try this ' +\n      'switch continue inline delete alignas alignof constexpr consteval constinit decltype ' +\n      'concept co_await co_return co_yield requires ' +\n      'noexcept static_assert thread_local restrict final override ' +\n      'atomic_bool atomic_char atomic_schar ' +\n      'atomic_uchar atomic_short atomic_ushort atomic_int atomic_uint atomic_long atomic_ulong atomic_llong ' +\n      'atomic_ullong new throw return ' +\n      'and and_eq bitand bitor compl not not_eq or or_eq xor xor_eq',\n    built_in: '_Bool _Complex _Imaginary',\n    _relevance_hints: COMMON_CPP_HINTS,\n    literal: 'true false nullptr NULL'\n  };\n\n  const FUNCTION_DISPATCH = {\n    className: \"function.dispatch\",\n    relevance: 0,\n    keywords: CPP_KEYWORDS,\n    begin: concat(\n      /\\b/,\n      /(?!decltype)/,\n      /(?!if)/,\n      /(?!for)/,\n      /(?!while)/,\n      hljs.IDENT_RE,\n      lookahead(/\\s*\\(/))\n  };\n\n  const EXPRESSION_CONTAINS = [\n    FUNCTION_DISPATCH,\n    PREPROCESSOR,\n    CPP_PRIMITIVE_TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: CPP_KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ TITLE_MODE ],\n        relevance: 0\n      },\n      // needed because we do not have look-behind on the below rule\n      // to prevent it from grabbing the final : in a :: pair\n      {\n        begin: /::/,\n        relevance: 0\n      },\n      // initializers\n      {\n        begin: /:/,\n        endsWithParent: true,\n        contains: [\n          STRINGS,\n          NUMBERS\n        ]\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          CPP_PRIMITIVE_TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: CPP_KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              CPP_PRIMITIVE_TYPES\n            ]\n          }\n        ]\n      },\n      CPP_PRIMITIVE_TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: 'C++',\n    aliases: [\n      'cc',\n      'c++',\n      'h++',\n      'hpp',\n      'hh',\n      'hxx',\n      'cxx'\n    ],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: {\n      \"function.dispatch\": \"built_in\"\n    },\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      FUNCTION_DISPATCH,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        { // containers: ie, `vector <int> rooms (9);`\n          begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array)\\\\s*<',\n          end: '>',\n          keywords: CPP_KEYWORDS,\n          contains: [\n            'self',\n            CPP_PRIMITIVE_TYPES\n          ]\n        },\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: CPP_KEYWORDS\n        },\n        {\n          className: 'class',\n          beginKeywords: 'enum class struct union',\n          end: /[{;:<>=]/,\n          contains: [\n            {\n              beginKeywords: \"final class struct\"\n            },\n            hljs.TITLE_MODE\n          ]\n        }\n      ]),\n    exports: {\n      preprocessor: PREPROCESSOR,\n      strings: STRINGS,\n      keywords: CPP_KEYWORDS\n    }\n  };\n}\n\n/*\nLanguage: C-like (deprecated, use C and C++ instead)\nAuthor: Ivan Sagalaev <<EMAIL>>\nContributors: Evgeny Stepanischev <<EMAIL>>, Zaven Muradyan <<EMAIL>>, Roel Deckers <<EMAIL>>, Sam Wu <<EMAIL>>, Jordi Petit <<EMAIL>>, Pieter Vantorre <<EMAIL>>, Google Inc. (David Benjamin) <<EMAIL>>\n*/\n\n/** @type LanguageFn */\nfunction cLike(hljs) {\n  const lang = cPlusPlus(hljs);\n\n  const C_ALIASES = [\n    \"c\",\n    \"h\"\n  ];\n\n  const CPP_ALIASES = [\n    'cc',\n    'c++',\n    'h++',\n    'hpp',\n    'hh',\n    'hxx',\n    'cxx'\n  ];\n\n  lang.disableAutodetect = true;\n  lang.aliases = [];\n  // support users only loading c-like (legacy)\n  if (!hljs.getLanguage(\"c\")) lang.aliases.push(...C_ALIASES);\n  if (!hljs.getLanguage(\"cpp\")) lang.aliases.push(...CPP_ALIASES);\n\n  // if c and cpp are loaded after then they will reclaim these\n  // aliases for themselves\n\n  return lang;\n}\n\nmodule.exports = cLike;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOE,MAAM,CAAC,KAAK,EAAEF,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACH,EAAE,EAAE;EACpB,OAAOE,MAAM,CAAC,GAAG,EAAEF,EAAE,EAAE,IAAI,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGE,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKR,MAAM,CAACQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,SAASA,CAACC,IAAI,EAAE;EACvB;EACA;EACA;EACA,MAAMC,mBAAmB,GAAGD,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;IAClDC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG,oBAAoB;EAC7C,MAAMC,YAAY,GAAG,iBAAiB;EACtC,MAAMC,oBAAoB,GAAG,UAAU;EACvC,MAAMC,gBAAgB,GAAG,GAAG,GAC1BH,gBAAgB,GAAG,GAAG,GACtBZ,QAAQ,CAACa,YAAY,CAAC,GACtB,eAAe,GAAGb,QAAQ,CAACc,oBAAoB,CAAC,GAClD,GAAG;EACH,MAAME,mBAAmB,GAAG;IAC1BC,SAAS,EAAE,SAAS;IACpBN,KAAK,EAAE;EACT,CAAC;;EAED;EACA;EACA,MAAMO,iBAAiB,GAAG,sDAAsD;EAChF,MAAMC,OAAO,GAAG;IACdF,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MACET,KAAK,EAAE,aAAa;MACpBU,GAAG,EAAE,GAAG;MACRC,OAAO,EAAE,KAAK;MACdZ,QAAQ,EAAE,CAAEH,IAAI,CAACgB,gBAAgB;IACnC,CAAC,EACD;MACEZ,KAAK,EAAE,eAAe,GAAGO,iBAAiB,GAAG,KAAK;MAClDG,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;IACX,CAAC,EACDf,IAAI,CAACiB,iBAAiB,CAAC;MACrBb,KAAK,EAAE,kCAAkC;MACzCU,GAAG,EAAE;IACP,CAAC,CAAC;EAEN,CAAC;EAED,MAAMI,OAAO,GAAG;IACdR,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MACET,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,CACF;IACDe,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBV,SAAS,EAAE,MAAM;IACjBN,KAAK,EAAE,cAAc;IACrBU,GAAG,EAAE,GAAG;IACRO,QAAQ,EAAE;MACR,cAAc,EACZ,qDAAqD,GACrD;IACJ,CAAC;IACDlB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,MAAM;MACbe,SAAS,EAAE;IACb,CAAC,EACDnB,IAAI,CAACsB,OAAO,CAACV,OAAO,EAAE;MACpBF,SAAS,EAAE;IACb,CAAC,CAAC,EACF;MACEA,SAAS,EAAE,aAAa;MACxBN,KAAK,EAAE;IACT,CAAC,EACDH,mBAAmB,EACnBD,IAAI,CAACuB,oBAAoB;EAE7B,CAAC;EAED,MAAMC,UAAU,GAAG;IACjBd,SAAS,EAAE,OAAO;IAClBN,KAAK,EAAEX,QAAQ,CAACa,YAAY,CAAC,GAAGN,IAAI,CAACyB,QAAQ;IAC7CN,SAAS,EAAE;EACb,CAAC;EAED,MAAMO,cAAc,GAAGjC,QAAQ,CAACa,YAAY,CAAC,GAAGN,IAAI,CAACyB,QAAQ,GAAG,SAAS;EAEzE,MAAME,gBAAgB,GAAG,CACvB,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACP,MAAM,EACN,OAAO,EACP,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,KAAK,EACL,KAAK,EACL,MAAM,EACN,QAAQ,EACR,UAAU,EACV,UAAU,EACV,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,KAAK,EACL,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,KAAK,EACL,eAAe,EACf,oBAAoB,EACpB,oBAAoB,EACpB,gBAAgB,EAChB,WAAW,EACX,OAAO,EACP,YAAY,EACZ,OAAO,EACP,WAAW,EACX,KAAK,EACL,MAAM,EACN,UAAU,EACV,SAAS,EACT,UAAU,EACV,MAAM,EACN,kBAAkB,EAClB,YAAY,EACZ,SAAS,EACT,WAAW,EACX,KAAK,EACL,QAAQ,EACR,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,eAAe,EACf,eAAe,CAChB;EAED,MAAMC,YAAY,GAAG;IACnBC,OAAO,EAAE,4GAA4G,GACnH,iFAAiF,GACjF,iFAAiF,GACjF,yEAAyE,GACzE,sFAAsF,GACtF,uFAAuF,GACvF,+CAA+C,GAC/C,8DAA8D,GAC9D,uCAAuC,GACvC,uGAAuG,GACvG,iCAAiC,GACjC,8DAA8D;IAChEC,QAAQ,EAAE,2BAA2B;IACrCC,gBAAgB,EAAEJ,gBAAgB;IAClCK,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,iBAAiB,GAAG;IACxBvB,SAAS,EAAE,mBAAmB;IAC9BS,SAAS,EAAE,CAAC;IACZE,QAAQ,EAAEO,YAAY;IACtBxB,KAAK,EAAEZ,MAAM,CACX,IAAI,EACJ,cAAc,EACd,QAAQ,EACR,SAAS,EACT,WAAW,EACXQ,IAAI,CAACyB,QAAQ,EACblC,SAAS,CAAC,OAAO,CAAC;EACtB,CAAC;EAED,MAAM2C,mBAAmB,GAAG,CAC1BD,iBAAiB,EACjBb,YAAY,EACZX,mBAAmB,EACnBR,mBAAmB,EACnBD,IAAI,CAACuB,oBAAoB,EACzBL,OAAO,EACPN,OAAO,CACR;EAGD,MAAMuB,kBAAkB,GAAG;IACzB;IACA;IACA;IACAtB,QAAQ,EAAE,CACR;MACET,KAAK,EAAE,GAAG;MACVU,GAAG,EAAE;IACP,CAAC,EACD;MACEV,KAAK,EAAE,IAAI;MACXU,GAAG,EAAE;IACP,CAAC,EACD;MACEsB,aAAa,EAAE,uBAAuB;MACtCtB,GAAG,EAAE;IACP,CAAC,CACF;IACDO,QAAQ,EAAEO,YAAY;IACtBzB,QAAQ,EAAE+B,mBAAmB,CAAC1C,MAAM,CAAC,CACnC;MACEY,KAAK,EAAE,IAAI;MACXU,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAEO,YAAY;MACtBzB,QAAQ,EAAE+B,mBAAmB,CAAC1C,MAAM,CAAC,CAAE,MAAM,CAAE,CAAC;MAChD2B,SAAS,EAAE;IACb,CAAC,CACF,CAAC;IACFA,SAAS,EAAE;EACb,CAAC;EAED,MAAMkB,oBAAoB,GAAG;IAC3B3B,SAAS,EAAE,UAAU;IACrBN,KAAK,EAAE,GAAG,GAAGI,gBAAgB,GAAG,cAAc,GAAGkB,cAAc;IAC/DY,WAAW,EAAE,IAAI;IACjBxB,GAAG,EAAE,OAAO;IACZyB,UAAU,EAAE,IAAI;IAChBlB,QAAQ,EAAEO,YAAY;IACtBb,OAAO,EAAE,gBAAgB;IACzBZ,QAAQ,EAAE,CACR;MAAE;MACAC,KAAK,EAAEC,gBAAgB;MACvBgB,QAAQ,EAAEO,YAAY;MACtBT,SAAS,EAAE;IACb,CAAC,EACD;MACEf,KAAK,EAAEsB,cAAc;MACrBY,WAAW,EAAE,IAAI;MACjBnC,QAAQ,EAAE,CAAEqB,UAAU,CAAE;MACxBL,SAAS,EAAE;IACb,CAAC;IACD;IACA;IACA;MACEf,KAAK,EAAE,IAAI;MACXe,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEf,KAAK,EAAE,GAAG;MACVoC,cAAc,EAAE,IAAI;MACpBrC,QAAQ,EAAE,CACRS,OAAO,EACPM,OAAO;IAEX,CAAC,EACD;MACER,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAE,IAAI;MACXU,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAEO,YAAY;MACtBT,SAAS,EAAE,CAAC;MACZhB,QAAQ,EAAE,CACRF,mBAAmB,EACnBD,IAAI,CAACuB,oBAAoB,EACzBX,OAAO,EACPM,OAAO,EACPT,mBAAmB;MACnB;MACA;QACEL,KAAK,EAAE,IAAI;QACXU,GAAG,EAAE,IAAI;QACTO,QAAQ,EAAEO,YAAY;QACtBT,SAAS,EAAE,CAAC;QACZhB,QAAQ,EAAE,CACR,MAAM,EACNF,mBAAmB,EACnBD,IAAI,CAACuB,oBAAoB,EACzBX,OAAO,EACPM,OAAO,EACPT,mBAAmB;MAEvB,CAAC;IAEL,CAAC,EACDA,mBAAmB,EACnBR,mBAAmB,EACnBD,IAAI,CAACuB,oBAAoB,EACzBH,YAAY;EAEhB,CAAC;EAED,OAAO;IACLqB,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,CACP,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,CACN;IACDrB,QAAQ,EAAEO,YAAY;IACtBb,OAAO,EAAE,IAAI;IACb4B,gBAAgB,EAAE;MAChB,mBAAmB,EAAE;IACvB,CAAC;IACDxC,QAAQ,EAAE,EAAE,CAACX,MAAM,CACjB2C,kBAAkB,EAClBE,oBAAoB,EACpBJ,iBAAiB,EACjBC,mBAAmB,EACnB,CACEd,YAAY,EACZ;MAAE;MACAhB,KAAK,EAAE,sKAAsK;MAC7KU,GAAG,EAAE,GAAG;MACRO,QAAQ,EAAEO,YAAY;MACtBzB,QAAQ,EAAE,CACR,MAAM,EACNM,mBAAmB;IAEvB,CAAC,EACD;MACEL,KAAK,EAAEJ,IAAI,CAACyB,QAAQ,GAAG,IAAI;MAC3BJ,QAAQ,EAAEO;IACZ,CAAC,EACD;MACElB,SAAS,EAAE,OAAO;MAClB0B,aAAa,EAAE,yBAAyB;MACxCtB,GAAG,EAAE,UAAU;MACfX,QAAQ,EAAE,CACR;QACEiC,aAAa,EAAE;MACjB,CAAC,EACDpC,IAAI,CAACwB,UAAU;IAEnB,CAAC,CACF,CAAC;IACJoB,OAAO,EAAE;MACPC,YAAY,EAAEzB,YAAY;MAC1B0B,OAAO,EAAElC,OAAO;MAChBS,QAAQ,EAAEO;IACZ;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASmB,KAAKA,CAAC/C,IAAI,EAAE;EACnB,MAAMgD,IAAI,GAAGjD,SAAS,CAACC,IAAI,CAAC;EAE5B,MAAMiD,SAAS,GAAG,CAChB,GAAG,EACH,GAAG,CACJ;EAED,MAAMC,WAAW,GAAG,CAClB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,CACN;EAEDF,IAAI,CAACG,iBAAiB,GAAG,IAAI;EAC7BH,IAAI,CAACN,OAAO,GAAG,EAAE;EACjB;EACA,IAAI,CAAC1C,IAAI,CAACoD,WAAW,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAACN,OAAO,CAACW,IAAI,CAAC,GAAGJ,SAAS,CAAC;EAC3D,IAAI,CAACjD,IAAI,CAACoD,WAAW,CAAC,KAAK,CAAC,EAAEJ,IAAI,CAACN,OAAO,CAACW,IAAI,CAAC,GAAGH,WAAW,CAAC;;EAE/D;EACA;;EAEA,OAAOF,IAAI;AACb;AAEAM,MAAM,CAACV,OAAO,GAAGG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}