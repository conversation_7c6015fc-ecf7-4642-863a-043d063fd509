{"ast": null, "code": "'use strict';\n\nmodule.exports = bicep;\nbicep.displayName = 'bicep';\nbicep.aliases = [];\nfunction bicep(Prism) {\n  // based loosely upon: https://github.com/Azure/bicep/blob/main/src/textmate/bicep.tmlanguage\n  Prism.languages.bicep = {\n    comment: [{\n      // multiline comments eg /* ASDF */\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      lookbehind: true,\n      greedy: true\n    }, {\n      // singleline comments eg // ASDF\n      pattern: /(^|[^\\\\:])\\/\\/.*/,\n      lookbehind: true,\n      greedy: true\n    }],\n    property: [{\n      pattern: /([\\r\\n][ \\t]*)[a-z_]\\w*(?=[ \\t]*:)/i,\n      lookbehind: true\n    }, {\n      pattern: /([\\r\\n][ \\t]*)'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'(?=[ \\t]*:)/,\n      lookbehind: true,\n      greedy: true\n    }],\n    string: [{\n      pattern: /'''[^'][\\s\\S]*?'''/,\n      greedy: true\n    }, {\n      pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'/,\n      lookbehind: true,\n      greedy: true\n    }],\n    'interpolated-string': {\n      pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?:(?!\\{)|\\{[^{}\\r\\n]*\\})|[^'\\\\\\r\\n$])*'/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}\\r\\n]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true\n            },\n            punctuation: /^\\$\\{|\\}$/\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    datatype: {\n      pattern: /(\\b(?:output|param)\\b[ \\t]+\\w+[ \\t]+)\\w+\\b/,\n      lookbehind: true,\n      alias: 'class-name'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    // https://github.com/Azure/bicep/blob/114a3251b4e6e30082a58729f19a8cc4e374ffa6/src/textmate/bicep.tmlanguage#L184\n    keyword: /\\b(?:existing|for|if|in|module|null|output|param|resource|targetScope|var)\\b/,\n    decorator: /@\\w+\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*\\()/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i,\n    operator: /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/,\n    punctuation: /[{}[\\];(),.:]/\n  };\n  Prism.languages.bicep['interpolated-string'].inside['interpolation'].inside['expression'].inside = Prism.languages.bicep;\n}", "map": {"version": 3, "names": ["module", "exports", "bicep", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "property", "string", "inside", "interpolation", "expression", "punctuation", "datatype", "alias", "boolean", "keyword", "decorator", "function", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/bicep.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bicep\nbicep.displayName = 'bicep'\nbicep.aliases = []\nfunction bicep(Prism) {\n  // based loosely upon: https://github.com/Azure/bicep/blob/main/src/textmate/bicep.tmlanguage\n  Prism.languages.bicep = {\n    comment: [\n      {\n        // multiline comments eg /* ASDF */\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        // singleline comments eg // ASDF\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    property: [\n      {\n        pattern: /([\\r\\n][ \\t]*)[a-z_]\\w*(?=[ \\t]*:)/i,\n        lookbehind: true\n      },\n      {\n        pattern: /([\\r\\n][ \\t]*)'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'(?=[ \\t]*:)/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: [\n      {\n        pattern: /'''[^'][\\s\\S]*?'''/,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    'interpolated-string': {\n      pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?:(?!\\{)|\\{[^{}\\r\\n]*\\})|[^'\\\\\\r\\n$])*'/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}\\r\\n]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true\n            },\n            punctuation: /^\\$\\{|\\}$/\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    datatype: {\n      pattern: /(\\b(?:output|param)\\b[ \\t]+\\w+[ \\t]+)\\w+\\b/,\n      lookbehind: true,\n      alias: 'class-name'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    // https://github.com/Azure/bicep/blob/114a3251b4e6e30082a58729f19a8cc4e374ffa6/src/textmate/bicep.tmlanguage#L184\n    keyword:\n      /\\b(?:existing|for|if|in|module|null|output|param|resource|targetScope|var)\\b/,\n    decorator: /@\\w+\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*\\()/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i,\n    operator:\n      /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.bicep['interpolated-string'].inside['interpolation'].inside[\n    'expression'\n  ].inside = Prism.languages.bicep\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EACAA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE,CACP;MACE;MACAC,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,EACD;MACE;MACAF,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,QAAQ,EAAE,CACR;MACEH,OAAO,EAAE,qCAAqC;MAC9CC,UAAU,EAAE;IACd,CAAC,EACD;MACED,OAAO,EAAE,0DAA0D;MACnEC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACDE,MAAM,EAAE,CACN;MACEJ,OAAO,EAAE,oBAAoB;MAC7BE,MAAM,EAAE;IACV,CAAC,EACD;MACEF,OAAO,EAAE,2CAA2C;MACpDC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACD,qBAAqB,EAAE;MACrBF,OAAO,EAAE,8DAA8D;MACvEC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACNC,aAAa,EAAE;UACbN,OAAO,EAAE,kBAAkB;UAC3BK,MAAM,EAAE;YACNE,UAAU,EAAE;cACVP,OAAO,EAAE,uBAAuB;cAChCC,UAAU,EAAE;YACd,CAAC;YACDO,WAAW,EAAE;UACf;QACF,CAAC;QACDJ,MAAM,EAAE;MACV;IACF,CAAC;IACDK,QAAQ,EAAE;MACRT,OAAO,EAAE,4CAA4C;MACrDC,UAAU,EAAE,IAAI;MAChBS,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE,oBAAoB;IAC7B;IACAC,OAAO,EACL,8EAA8E;IAChFC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,0BAA0B;IACpCC,MAAM,EAAE,4CAA4C;IACpDC,QAAQ,EACN,2FAA2F;IAC7FR,WAAW,EAAE;EACf,CAAC;EACDX,KAAK,CAACC,SAAS,CAACJ,KAAK,CAAC,qBAAqB,CAAC,CAACW,MAAM,CAAC,eAAe,CAAC,CAACA,MAAM,CACzE,YAAY,CACb,CAACA,MAAM,GAAGR,KAAK,CAACC,SAAS,CAACJ,KAAK;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}