{"ast": null, "code": "/*\nLanguage: Clean\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: functional\nWebsite: http://clean.cs.ru.nl\n*/\n\n/** @type LanguageFn */\nfunction clean(hljs) {\n  return {\n    name: 'Clean',\n    aliases: ['icl', 'dcl'],\n    keywords: {\n      keyword: 'if let in with where case of class instance otherwise ' + 'implementation definition system module from import qualified as ' + 'special code inline foreign export ccall stdcall generic derive ' + 'infix infixl infixr',\n      built_in: 'Int Real Char Bool',\n      literal: 'True False'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE, {\n      // relevance booster\n      begin: '->|<-[|:]?|#!?|>>=|\\\\{\\\\||\\\\|\\\\}|:==|=:|<>'\n    }]\n  };\n}\nmodule.exports = clean;", "map": {"version": 3, "names": ["clean", "hljs", "name", "aliases", "keywords", "keyword", "built_in", "literal", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_NUMBER_MODE", "begin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/clean.js"], "sourcesContent": ["/*\nLanguage: Clean\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: functional\nWebsite: http://clean.cs.ru.nl\n*/\n\n/** @type LanguageFn */\nfunction clean(hljs) {\n  return {\n    name: 'Clean',\n    aliases: [\n      'icl',\n      'dcl'\n    ],\n    keywords: {\n      keyword:\n        'if let in with where case of class instance otherwise ' +\n        'implementation definition system module from import qualified as ' +\n        'special code inline foreign export ccall stdcall generic derive ' +\n        'infix infixl infixr',\n      built_in:\n        'Int Real Char Bool',\n      literal:\n        'True False'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      { // relevance booster\n        begin: '->|<-[|:]?|#!?|>>=|\\\\{\\\\||\\\\|\\\\}|:==|=:|<>'\n      }\n    ]\n  };\n}\n\nmodule.exports = clean;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,CACP,KAAK,EACL,KAAK,CACN;IACDC,QAAQ,EAAE;MACRC,OAAO,EACL,wDAAwD,GACxD,mEAAmE,GACnE,kEAAkE,GAClE,qBAAqB;MACvBC,QAAQ,EACN,oBAAoB;MACtBC,OAAO,EACL;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRP,IAAI,CAACQ,mBAAmB,EACxBR,IAAI,CAACS,oBAAoB,EACzBT,IAAI,CAACU,gBAAgB,EACrBV,IAAI,CAACW,iBAAiB,EACtBX,IAAI,CAACY,aAAa,EAClB;MAAE;MACAC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGhB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}