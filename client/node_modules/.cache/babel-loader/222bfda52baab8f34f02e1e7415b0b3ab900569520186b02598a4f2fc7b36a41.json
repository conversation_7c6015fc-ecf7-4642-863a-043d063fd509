{"ast": null, "code": "'use strict';\n\nmodule.exports = rip;\nrip.displayName = 'rip';\nrip.aliases = [];\nfunction rip(Prism) {\n  Prism.languages.rip = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    char: {\n      pattern: /\\B`[^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]\\b/,\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    regex: {\n      pattern: /(^|[^/])\\/(?!\\/)(?:\\[[^\\n\\r\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/(?=\\s*(?:$|[\\r\\n,.;})]))/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword: /(?:=>|->)|\\b(?:case|catch|class|else|exit|finally|if|raise|return|switch|try)\\b/,\n    builtin: /@|\\bSystem\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    date: /\\b\\d{4}-\\d{2}-\\d{2}\\b/,\n    time: /\\b\\d{2}:\\d{2}:\\d{2}\\b/,\n    datetime: /\\b\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\b/,\n    symbol: /:[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/,\n    number: /[+-]?\\b(?:\\d+\\.\\d+|\\d+)\\b/,\n    punctuation: /(?:\\.{2,3})|[`,.:;=\\/\\\\()<>\\[\\]{}]/,\n    reference: /[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "rip", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "char", "string", "regex", "lookbehind", "keyword", "builtin", "boolean", "date", "time", "datetime", "symbol", "number", "punctuation", "reference"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/rip.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = rip\nrip.displayName = 'rip'\nrip.aliases = []\nfunction rip(Prism) {\n  Prism.languages.rip = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    char: {\n      pattern: /\\B`[^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]\\b/,\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    regex: {\n      pattern:\n        /(^|[^/])\\/(?!\\/)(?:\\[[^\\n\\r\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/(?=\\s*(?:$|[\\r\\n,.;})]))/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /(?:=>|->)|\\b(?:case|catch|class|else|exit|finally|if|raise|return|switch|try)\\b/,\n    builtin: /@|\\bSystem\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    date: /\\b\\d{4}-\\d{2}-\\d{2}\\b/,\n    time: /\\b\\d{2}:\\d{2}:\\d{2}\\b/,\n    datetime: /\\b\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\b/,\n    symbol: /:[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/,\n    number: /[+-]?\\b(?:\\d+\\.\\d+|\\d+)\\b/,\n    punctuation: /(?:\\.{2,3})|[`,.:;=\\/\\\\()<>\\[\\]{}]/,\n    reference: /[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACDC,IAAI,EAAE;MACJF,OAAO,EAAE,kCAAkC;MAC3CC,MAAM,EAAE;IACV,CAAC;IACDE,MAAM,EAAE;MACNH,OAAO,EAAE,iCAAiC;MAC1CC,MAAM,EAAE;IACV,CAAC;IACDG,KAAK,EAAE;MACLJ,OAAO,EACL,gFAAgF;MAClFK,UAAU,EAAE,IAAI;MAChBJ,MAAM,EAAE;IACV,CAAC;IACDK,OAAO,EACL,iFAAiF;IACnFC,OAAO,EAAE,cAAc;IACvBC,OAAO,EAAE,oBAAoB;IAC7BC,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,uBAAuB;IAC7BC,QAAQ,EAAE,yCAAyC;IACnDC,MAAM,EAAE,4DAA4D;IACpEC,MAAM,EAAE,2BAA2B;IACnCC,WAAW,EAAE,oCAAoC;IACjDC,SAAS,EAAE;EACb,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}