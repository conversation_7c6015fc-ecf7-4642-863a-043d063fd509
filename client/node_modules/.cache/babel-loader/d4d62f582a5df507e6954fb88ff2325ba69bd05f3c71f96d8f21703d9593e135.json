{"ast": null, "code": "'use strict';\n\nmodule.exports = uorazor;\nuorazor.displayName = 'uorazor';\nuorazor.aliases = [];\nfunction uorazor(Prism) {\n  Prism.languages.uorazor = {\n    'comment-hash': {\n      pattern: /#.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    'comment-slash': {\n      pattern: /\\/\\/.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      inside: {\n        punctuation: /^['\"]|['\"]$/\n      },\n      greedy: true\n    },\n    'source-layers': {\n      pattern: /\\b(?:arms|backpack|blue|bracelet|cancel|clear|cloak|criminal|earrings|enemy|facialhair|friend|friendly|gloves|gray|grey|ground|hair|head|innerlegs|innertorso|innocent|lefthand|middletorso|murderer|neck|nonfriendly|onehandedsecondary|outerlegs|outertorso|pants|red|righthand|ring|self|shirt|shoes|talisman|waist)\\b/i,\n      alias: 'function'\n    },\n    'source-commands': {\n      pattern: /\\b(?:alliance|attack|cast|clearall|clearignore|clearjournal|clearlist|clearsysmsg|createlist|createtimer|dclick|dclicktype|dclickvar|dress|dressconfig|drop|droprelloc|emote|getlabel|guild|gumpclose|gumpresponse|hotkey|ignore|lasttarget|lift|lifttype|menu|menuresponse|msg|org|organize|organizer|overhead|pause|poplist|potion|promptresponse|pushlist|removelist|removetimer|rename|restock|say|scav|scavenger|script|setability|setlasttarget|setskill|settimer|setvar|sysmsg|target|targetloc|targetrelloc|targettype|undress|unignore|unsetvar|useobject|useonce|useskill|usetype|virtue|wait|waitforgump|waitformenu|waitforprompt|waitforstat|waitforsysmsg|waitfortarget|walk|wfsysmsg|wft|whisper|yell)\\b/,\n      alias: 'function'\n    },\n    'tag-name': {\n      pattern: /(^\\{%-?\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    delimiter: {\n      pattern: /^\\{[{%]-?|-?[%}]\\}$/,\n      alias: 'punctuation'\n    },\n    function: /\\b(?:atlist|close|closest|count|counter|counttype|dead|dex|diffhits|diffmana|diffstam|diffweight|find|findbuff|finddebuff|findlayer|findtype|findtypelist|followers|gumpexists|hidden|hits|hp|hue|human|humanoid|ingump|inlist|insysmessage|insysmsg|int|invul|lhandempty|list|listexists|mana|maxhits|maxhp|maxmana|maxstam|maxweight|monster|mounted|name|next|noto|paralyzed|poisoned|position|prev|previous|queued|rand|random|rhandempty|skill|stam|str|targetexists|timer|timerexists|varexist|warmode|weight)\\b/,\n    keyword: /\\b(?:and|as|break|continue|else|elseif|endfor|endif|endwhile|for|if|loop|not|or|replay|stop|while)\\b/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n    operator: [{\n      pattern: /(\\s)(?:and|b-and|b-or|b-xor|ends with|in|is|matches|not|or|same as|starts with)(?=\\s)/,\n      lookbehind: true\n    }, /[=<>]=?|!=|\\*\\*?|\\/\\/?|\\?:?|[-+~%|]/],\n    punctuation: /[()\\[\\]{}:.,]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "uorazor", "displayName", "aliases", "Prism", "languages", "pattern", "alias", "greedy", "string", "inside", "punctuation", "lookbehind", "delimiter", "function", "keyword", "boolean", "number", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/uorazor.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = uorazor\nuorazor.displayName = 'uorazor'\nuorazor.aliases = []\nfunction uorazor(Prism) {\n  Prism.languages.uorazor = {\n    'comment-hash': {\n      pattern: /#.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    'comment-slash': {\n      pattern: /\\/\\/.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      inside: {\n        punctuation: /^['\"]|['\"]$/\n      },\n      greedy: true\n    },\n    'source-layers': {\n      pattern:\n        /\\b(?:arms|backpack|blue|bracelet|cancel|clear|cloak|criminal|earrings|enemy|facialhair|friend|friendly|gloves|gray|grey|ground|hair|head|innerlegs|innertorso|innocent|lefthand|middletorso|murderer|neck|nonfriendly|onehandedsecondary|outerlegs|outertorso|pants|red|righthand|ring|self|shirt|shoes|talisman|waist)\\b/i,\n      alias: 'function'\n    },\n    'source-commands': {\n      pattern:\n        /\\b(?:alliance|attack|cast|clearall|clearignore|clearjournal|clearlist|clearsysmsg|createlist|createtimer|dclick|dclicktype|dclickvar|dress|dressconfig|drop|droprelloc|emote|getlabel|guild|gumpclose|gumpresponse|hotkey|ignore|lasttarget|lift|lifttype|menu|menuresponse|msg|org|organize|organizer|overhead|pause|poplist|potion|promptresponse|pushlist|removelist|removetimer|rename|restock|say|scav|scavenger|script|setability|setlasttarget|setskill|settimer|setvar|sysmsg|target|targetloc|targetrelloc|targettype|undress|unignore|unsetvar|useobject|useonce|useskill|usetype|virtue|wait|waitforgump|waitformenu|waitforprompt|waitforstat|waitforsysmsg|waitfortarget|walk|wfsysmsg|wft|whisper|yell)\\b/,\n      alias: 'function'\n    },\n    'tag-name': {\n      pattern: /(^\\{%-?\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    delimiter: {\n      pattern: /^\\{[{%]-?|-?[%}]\\}$/,\n      alias: 'punctuation'\n    },\n    function:\n      /\\b(?:atlist|close|closest|count|counter|counttype|dead|dex|diffhits|diffmana|diffstam|diffweight|find|findbuff|finddebuff|findlayer|findtype|findtypelist|followers|gumpexists|hidden|hits|hp|hue|human|humanoid|ingump|inlist|insysmessage|insysmsg|int|invul|lhandempty|list|listexists|mana|maxhits|maxhp|maxmana|maxstam|maxweight|monster|mounted|name|next|noto|paralyzed|poisoned|position|prev|previous|queued|rand|random|rhandempty|skill|stam|str|targetexists|timer|timerexists|varexist|warmode|weight)\\b/,\n    keyword:\n      /\\b(?:and|as|break|continue|else|elseif|endfor|endif|endwhile|for|if|loop|not|or|replay|stop|while)\\b/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n    operator: [\n      {\n        pattern:\n          /(\\s)(?:and|b-and|b-or|b-xor|ends with|in|is|matches|not|or|same as|starts with)(?=\\s)/,\n        lookbehind: true\n      },\n      /[=<>]=?|!=|\\*\\*?|\\/\\/?|\\?:?|[-+~%|]/\n    ],\n    punctuation: /[()\\[\\]{}:.,]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxB,cAAc,EAAE;MACdK,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,eAAe,EAAE;MACfF,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,iCAAiC;MAC1CI,MAAM,EAAE;QACNC,WAAW,EAAE;MACf,CAAC;MACDH,MAAM,EAAE;IACV,CAAC;IACD,eAAe,EAAE;MACfF,OAAO,EACL,4TAA4T;MAC9TC,KAAK,EAAE;IACT,CAAC;IACD,iBAAiB,EAAE;MACjBD,OAAO,EACL,yrBAAyrB;MAC3rBC,KAAK,EAAE;IACT,CAAC;IACD,UAAU,EAAE;MACVD,OAAO,EAAE,gBAAgB;MACzBM,UAAU,EAAE,IAAI;MAChBL,KAAK,EAAE;IACT,CAAC;IACDM,SAAS,EAAE;MACTP,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE;IACT,CAAC;IACDO,QAAQ,EACN,wfAAwf;IAC1fC,OAAO,EACL,sGAAsG;IACxGC,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE,8DAA8D;IACtEC,QAAQ,EAAE,CACR;MACEZ,OAAO,EACL,uFAAuF;MACzFM,UAAU,EAAE;IACd,CAAC,EACD,qCAAqC,CACtC;IACDD,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}