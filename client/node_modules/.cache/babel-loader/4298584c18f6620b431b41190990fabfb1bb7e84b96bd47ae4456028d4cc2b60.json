{"ast": null, "code": "'use strict';\n\nmodule.exports = neon;\nneon.displayName = 'neon';\nneon.aliases = [];\nfunction neon(Prism) {\n  Prism.languages.neon = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    datetime: {\n      pattern: /(^|[[{(=:,\\s])\\d\\d\\d\\d-\\d\\d?-\\d\\d?(?:(?:[Tt]| +)\\d\\d?:\\d\\d:\\d\\d(?:\\.\\d*)? *(?:Z|[-+]\\d\\d?(?::?\\d\\d)?)?)?(?=$|[\\]}),\\s])/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    key: {\n      pattern: /(^|[[{(,\\s])[^,:=[\\]{}()'\"\\s]+(?=\\s*:(?:$|[\\]}),\\s])|\\s*=)/,\n      lookbehind: true,\n      alias: 'atrule'\n    },\n    number: {\n      pattern: /(^|[[{(=:,\\s])[+-]?(?:0x[\\da-fA-F]+|0o[0-7]+|0b[01]+|(?:\\d+(?:\\.\\d*)?|\\.?\\d+)(?:[eE][+-]?\\d+)?)(?=$|[\\]}),:=\\s])/,\n      lookbehind: true\n    },\n    boolean: {\n      pattern: /(^|[[{(=:,\\s])(?:false|no|true|yes)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true\n    },\n    null: {\n      pattern: /(^|[[{(=:,\\s])(?:null)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    string: {\n      pattern: /(^|[[{(=:,\\s])(?:('''|\"\"\")\\r?\\n(?:(?:[^\\r\\n]|\\r?\\n(?![\\t ]*\\2))*\\r?\\n)?[\\t ]*\\2|'[^'\\r\\n]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    literal: {\n      pattern: /(^|[[{(=:,\\s])(?:[^#\"',:=[\\]{}()\\s`-]|[:-][^\"',=[\\]{}()\\s])(?:[^,:=\\]})(\\s]|:(?![\\s,\\]})]|$)|[ \\t]+[^#,:=\\]})(\\s])*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    punctuation: /[,:=[\\]{}()-]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "neon", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "datetime", "lookbehind", "alias", "key", "number", "boolean", "null", "string", "literal", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/neon.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = neon\nneon.displayName = 'neon'\nneon.aliases = []\nfunction neon(Prism) {\n  Prism.languages.neon = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    datetime: {\n      pattern:\n        /(^|[[{(=:,\\s])\\d\\d\\d\\d-\\d\\d?-\\d\\d?(?:(?:[Tt]| +)\\d\\d?:\\d\\d:\\d\\d(?:\\.\\d*)? *(?:Z|[-+]\\d\\d?(?::?\\d\\d)?)?)?(?=$|[\\]}),\\s])/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    key: {\n      pattern: /(^|[[{(,\\s])[^,:=[\\]{}()'\"\\s]+(?=\\s*:(?:$|[\\]}),\\s])|\\s*=)/,\n      lookbehind: true,\n      alias: 'atrule'\n    },\n    number: {\n      pattern:\n        /(^|[[{(=:,\\s])[+-]?(?:0x[\\da-fA-F]+|0o[0-7]+|0b[01]+|(?:\\d+(?:\\.\\d*)?|\\.?\\d+)(?:[eE][+-]?\\d+)?)(?=$|[\\]}),:=\\s])/,\n      lookbehind: true\n    },\n    boolean: {\n      pattern: /(^|[[{(=:,\\s])(?:false|no|true|yes)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true\n    },\n    null: {\n      pattern: /(^|[[{(=:,\\s])(?:null)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    string: {\n      pattern:\n        /(^|[[{(=:,\\s])(?:('''|\"\"\")\\r?\\n(?:(?:[^\\r\\n]|\\r?\\n(?![\\t ]*\\2))*\\r?\\n)?[\\t ]*\\2|'[^'\\r\\n]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    literal: {\n      pattern:\n        /(^|[[{(=:,\\s])(?:[^#\"',:=[\\]{}()\\s`-]|[:-][^\"',=[\\]{}()\\s])(?:[^,:=\\]})(\\s]|:(?![\\s,\\]})]|$)|[ \\t]+[^#,:=\\]})(\\s])*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    punctuation: /[,:=[\\]{}()-]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACRF,OAAO,EACL,yHAAyH;MAC3HG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,GAAG,EAAE;MACHL,OAAO,EAAE,4DAA4D;MACrEG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNN,OAAO,EACL,kHAAkH;MACpHG,UAAU,EAAE;IACd,CAAC;IACDI,OAAO,EAAE;MACPP,OAAO,EAAE,uDAAuD;MAChEG,UAAU,EAAE;IACd,CAAC;IACDK,IAAI,EAAE;MACJR,OAAO,EAAE,0CAA0C;MACnDG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDK,MAAM,EAAE;MACNT,OAAO,EACL,oHAAoH;MACtHG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;IACV,CAAC;IACDS,OAAO,EAAE;MACPV,OAAO,EACL,qHAAqH;MACvHG,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDO,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}