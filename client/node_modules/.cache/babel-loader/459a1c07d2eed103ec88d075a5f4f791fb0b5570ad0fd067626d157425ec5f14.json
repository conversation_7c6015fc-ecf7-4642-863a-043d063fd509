{"ast": null, "code": "/*\n Language: G-code (ISO 6983)\n Contributors: <PERSON> <<EMAIL>>\n Description: G-code syntax highlighter for Fanuc and other common CNC machine tool controls.\n Website: https://www.sis.se/api/document/preview/911952/\n */\n\nfunction gcode(hljs) {\n  const GCODE_IDENT_RE = '[A-Z_][A-Z0-9_.]*';\n  const GCODE_CLOSE_RE = '%';\n  const GCODE_KEYWORDS = {\n    $pattern: GCODE_IDENT_RE,\n    keyword: 'IF DO WHILE ENDWHILE CALL ENDIF SUB ENDSUB GOTO REPEAT ENDREPEAT ' + 'EQ LT GT NE GE LE OR XOR'\n  };\n  const GCODE_START = {\n    className: 'meta',\n    begin: '([O])([0-9]+)'\n  };\n  const NUMBER = hljs.inherit(hljs.C_NUMBER_MODE, {\n    begin: '([-+]?((\\\\.\\\\d+)|(\\\\d+)(\\\\.\\\\d*)?))|' + hljs.C_NUMBER_RE\n  });\n  const GCODE_CODE = [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.COMMENT(/\\(/, /\\)/), NUMBER, hljs.inherit(hljs.APOS_STRING_MODE, {\n    illegal: null\n  }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  }), {\n    className: 'name',\n    begin: '([G])([0-9]+\\\\.?[0-9]?)'\n  }, {\n    className: 'name',\n    begin: '([M])([0-9]+\\\\.?[0-9]?)'\n  }, {\n    className: 'attr',\n    begin: '(VC|VS|#)',\n    end: '(\\\\d+)'\n  }, {\n    className: 'attr',\n    begin: '(VZOFX|VZOFY|VZOFZ)'\n  }, {\n    className: 'built_in',\n    begin: '(ATAN|ABS|ACOS|ASIN|SIN|COS|EXP|FIX|FUP|ROUND|LN|TAN)(\\\\[)',\n    contains: [NUMBER],\n    end: '\\\\]'\n  }, {\n    className: 'symbol',\n    variants: [{\n      begin: 'N',\n      end: '\\\\d+',\n      illegal: '\\\\W'\n    }]\n  }];\n  return {\n    name: 'G-code (ISO 6983)',\n    aliases: ['nc'],\n    // Some implementations (CNC controls) of G-code are interoperable with uppercase and lowercase letters seamlessly.\n    // However, most prefer all uppercase and uppercase is customary.\n    case_insensitive: true,\n    keywords: GCODE_KEYWORDS,\n    contains: [{\n      className: 'meta',\n      begin: GCODE_CLOSE_RE\n    }, GCODE_START].concat(GCODE_CODE)\n  };\n}\nmodule.exports = gcode;", "map": {"version": 3, "names": ["gcode", "hljs", "GCODE_IDENT_RE", "GCODE_CLOSE_RE", "GCODE_KEYWORDS", "$pattern", "keyword", "GCODE_START", "className", "begin", "NUMBER", "inherit", "C_NUMBER_MODE", "C_NUMBER_RE", "GCODE_CODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "COMMENT", "APOS_STRING_MODE", "illegal", "QUOTE_STRING_MODE", "end", "contains", "variants", "name", "aliases", "case_insensitive", "keywords", "concat", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/gcode.js"], "sourcesContent": ["/*\n Language: G-code (ISO 6983)\n Contributors: <PERSON> <<EMAIL>>\n Description: G-code syntax highlighter for Fanuc and other common CNC machine tool controls.\n Website: https://www.sis.se/api/document/preview/911952/\n */\n\nfunction gcode(hljs) {\n  const GCODE_IDENT_RE = '[A-Z_][A-Z0-9_.]*';\n  const GCODE_CLOSE_RE = '%';\n  const GCODE_KEYWORDS = {\n    $pattern: GCODE_IDENT_RE,\n    keyword: 'IF DO WHILE ENDWHILE CALL ENDIF SUB ENDSUB GOTO REPEAT ENDREPEAT ' +\n      'EQ LT GT NE GE LE OR XOR'\n  };\n  const GCODE_START = {\n    className: 'meta',\n    begin: '([O])([0-9]+)'\n  };\n  const NUMBER = hljs.inherit(hljs.C_NUMBER_MODE, {\n    begin: '([-+]?((\\\\.\\\\d+)|(\\\\d+)(\\\\.\\\\d*)?))|' + hljs.C_NUMBER_RE\n  });\n  const GCODE_CODE = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.COMMENT(/\\(/, /\\)/),\n    NUMBER,\n    hljs.inherit(hljs.APOS_STRING_MODE, {\n      illegal: null\n    }),\n    hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    }),\n    {\n      className: 'name',\n      begin: '([G])([0-9]+\\\\.?[0-9]?)'\n    },\n    {\n      className: 'name',\n      begin: '([M])([0-9]+\\\\.?[0-9]?)'\n    },\n    {\n      className: 'attr',\n      begin: '(VC|VS|#)',\n      end: '(\\\\d+)'\n    },\n    {\n      className: 'attr',\n      begin: '(VZOFX|VZOFY|VZOFZ)'\n    },\n    {\n      className: 'built_in',\n      begin: '(ATAN|ABS|ACOS|ASIN|SIN|COS|EXP|FIX|FUP|ROUND|LN|TAN)(\\\\[)',\n      contains: [\n        NUMBER\n      ],\n      end: '\\\\]'\n    },\n    {\n      className: 'symbol',\n      variants: [\n        {\n          begin: 'N',\n          end: '\\\\d+',\n          illegal: '\\\\W'\n        }\n      ]\n    }\n  ];\n\n  return {\n    name: 'G-code (ISO 6983)',\n    aliases: ['nc'],\n    // Some implementations (CNC controls) of G-code are interoperable with uppercase and lowercase letters seamlessly.\n    // However, most prefer all uppercase and uppercase is customary.\n    case_insensitive: true,\n    keywords: GCODE_KEYWORDS,\n    contains: [\n      {\n        className: 'meta',\n        begin: GCODE_CLOSE_RE\n      },\n      GCODE_START\n    ].concat(GCODE_CODE)\n  };\n}\n\nmodule.exports = gcode;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,cAAc,GAAG,mBAAmB;EAC1C,MAAMC,cAAc,GAAG,GAAG;EAC1B,MAAMC,cAAc,GAAG;IACrBC,QAAQ,EAAEH,cAAc;IACxBI,OAAO,EAAE,mEAAmE,GAC1E;EACJ,CAAC;EACD,MAAMC,WAAW,GAAG;IAClBC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,MAAM,GAAGT,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,aAAa,EAAE;IAC9CH,KAAK,EAAE,sCAAsC,GAAGR,IAAI,CAACY;EACvD,CAAC,CAAC;EACF,MAAMC,UAAU,GAAG,CACjBb,IAAI,CAACc,mBAAmB,EACxBd,IAAI,CAACe,oBAAoB,EACzBf,IAAI,CAACgB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EACxBP,MAAM,EACNT,IAAI,CAACU,OAAO,CAACV,IAAI,CAACiB,gBAAgB,EAAE;IAClCC,OAAO,EAAE;EACX,CAAC,CAAC,EACFlB,IAAI,CAACU,OAAO,CAACV,IAAI,CAACmB,iBAAiB,EAAE;IACnCD,OAAO,EAAE;EACX,CAAC,CAAC,EACF;IACEX,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,WAAW;IAClBY,GAAG,EAAE;EACP,CAAC,EACD;IACEb,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,4DAA4D;IACnEa,QAAQ,EAAE,CACRZ,MAAM,CACP;IACDW,GAAG,EAAE;EACP,CAAC,EACD;IACEb,SAAS,EAAE,QAAQ;IACnBe,QAAQ,EAAE,CACR;MACEd,KAAK,EAAE,GAAG;MACVY,GAAG,EAAE,MAAM;MACXF,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,CACF;EAED,OAAO;IACLK,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,CAAC,IAAI,CAAC;IACf;IACA;IACAC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAEvB,cAAc;IACxBkB,QAAQ,EAAE,CACR;MACEd,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAEN;IACT,CAAC,EACDI,WAAW,CACZ,CAACqB,MAAM,CAACd,UAAU;EACrB,CAAC;AACH;AAEAe,MAAM,CAACC,OAAO,GAAG9B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}