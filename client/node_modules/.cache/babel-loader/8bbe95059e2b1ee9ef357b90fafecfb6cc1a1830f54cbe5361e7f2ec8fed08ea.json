{"ast": null, "code": "/*\nLanguage: Node REPL\nRequires: javascript.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction nodeRepl(hljs) {\n  return {\n    name: 'Node REPL',\n    contains: [{\n      className: 'meta',\n      starts: {\n        // a space separates the REPL prefix from the actual code\n        // this is purely for cleaner HTML output\n        end: / |$/,\n        starts: {\n          end: '$',\n          subLanguage: 'javascript'\n        }\n      },\n      variants: [{\n        begin: /^>(?=[ ]|$)/\n      }, {\n        begin: /^\\.\\.\\.(?=[ ]|$)/\n      }]\n    }]\n  };\n}\nmodule.exports = nodeRepl;", "map": {"version": 3, "names": ["nodeRepl", "hljs", "name", "contains", "className", "starts", "end", "subLanguage", "variants", "begin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/node-repl.js"], "sourcesContent": ["/*\nLanguage: Node REPL\nRequires: javascript.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction nodeRepl(hljs) {\n  return {\n    name: 'Node REPL',\n    contains: [\n      {\n        className: 'meta',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'javascript'\n          }\n        },\n        variants: [\n          {\n            begin: /^>(?=[ ]|$)/\n          },\n          {\n            begin: /^\\.\\.\\.(?=[ ]|$)/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = nodeRepl;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,OAAO;IACLC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAE;QACN;QACA;QACAC,GAAG,EAAE,KAAK;QACVD,MAAM,EAAE;UACNC,GAAG,EAAE,GAAG;UACRC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}