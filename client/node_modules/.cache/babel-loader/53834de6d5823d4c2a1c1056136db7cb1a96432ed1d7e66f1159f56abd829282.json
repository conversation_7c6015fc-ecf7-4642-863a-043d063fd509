{"ast": null, "code": "/*\nLanguage: Dust\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Matcher for dust.js templates.\nWebsite: https://www.dustjs.com\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction dust(hljs) {\n  const EXPRESSION_KEYWORDS = 'if eq ne lt lte gt gte select default math sep';\n  return {\n    name: 'Dust',\n    aliases: ['dst'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [{\n      className: 'template-tag',\n      begin: /\\{[#\\/]/,\n      end: /\\}/,\n      illegal: /;/,\n      contains: [{\n        className: 'name',\n        begin: /[a-zA-Z\\.-]+/,\n        starts: {\n          endsWithParent: true,\n          relevance: 0,\n          contains: [hljs.QUOTE_STRING_MODE]\n        }\n      }]\n    }, {\n      className: 'template-variable',\n      begin: /\\{/,\n      end: /\\}/,\n      illegal: /;/,\n      keywords: EXPRESSION_KEYWORDS\n    }]\n  };\n}\nmodule.exports = dust;", "map": {"version": 3, "names": ["dust", "hljs", "EXPRESSION_KEYWORDS", "name", "aliases", "case_insensitive", "subLanguage", "contains", "className", "begin", "end", "illegal", "starts", "endsWithParent", "relevance", "QUOTE_STRING_MODE", "keywords", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/dust.js"], "sourcesContent": ["/*\nLanguage: Dust\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Matcher for dust.js templates.\nWebsite: https://www.dustjs.com\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction dust(hljs) {\n  const EXPRESSION_KEYWORDS = 'if eq ne lt lte gt gte select default math sep';\n  return {\n    name: 'Dust',\n    aliases: ['dst'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      {\n        className: 'template-tag',\n        begin: /\\{[#\\/]/,\n        end: /\\}/,\n        illegal: /;/,\n        contains: [{\n          className: 'name',\n          begin: /[a-zA-Z\\.-]+/,\n          starts: {\n            endsWithParent: true,\n            relevance: 0,\n            contains: [hljs.QUOTE_STRING_MODE]\n          }\n        }]\n      },\n      {\n        className: 'template-variable',\n        begin: /\\{/,\n        end: /\\}/,\n        illegal: /;/,\n        keywords: EXPRESSION_KEYWORDS\n      }\n    ]\n  };\n}\n\nmodule.exports = dust;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,mBAAmB,GAAG,gDAAgD;EAC5E,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,cAAc;MACzBC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE,GAAG;MACZJ,QAAQ,EAAE,CAAC;QACTC,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAE,cAAc;QACrBG,MAAM,EAAE;UACNC,cAAc,EAAE,IAAI;UACpBC,SAAS,EAAE,CAAC;UACZP,QAAQ,EAAE,CAACN,IAAI,CAACc,iBAAiB;QACnC;MACF,CAAC;IACH,CAAC,EACD;MACEP,SAAS,EAAE,mBAAmB;MAC9BC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE,GAAG;MACZK,QAAQ,EAAEd;IACZ,CAAC;EAEL,CAAC;AACH;AAEAe,MAAM,CAACC,OAAO,GAAGlB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}