{"ast": null, "code": "/*\nLanguage: PHP\nAuthor: <PERSON> <<PERSON>.<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\n/**\n * @param {H<PERSON><PERSON><PERSON><PERSON>} hljs\n * @returns {LanguageDetail}\n * */\nfunction php(hljs) {\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*' +\n    // negative look-ahead tries to avoid matching patterns that are not\n    // Perl at all like $ident$, @ident@, etc.\n    `(?![A-Za-z0-9])(?![$])`\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    variants: [{\n      begin: /<\\?php/,\n      relevance: 10\n    },\n    // boost for obvious PHP\n    {\n      begin: /<\\?[=]?/\n    }, {\n      begin: /\\?>/\n    } // end php tag\n    ]\n  };\n  const SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: /\\$\\w+/\n    }, {\n      begin: /\\{\\$/,\n      end: /\\}/\n    }]\n  };\n  const SINGLE_QUOTED = hljs.inherit(hljs.APOS_STRING_MODE, {\n    illegal: null\n  });\n  const DOUBLE_QUOTED = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST)\n  });\n  const HEREDOC = hljs.END_SAME_AS_BEGIN({\n    begin: /<<<[ \\t]*(\\w+)\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST)\n  });\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, PREPROCESSOR],\n    variants: [hljs.inherit(SINGLE_QUOTED, {\n      begin: \"b'\",\n      end: \"'\"\n    }), hljs.inherit(DOUBLE_QUOTED, {\n      begin: 'b\"',\n      end: '\"'\n    }), DOUBLE_QUOTED, SINGLE_QUOTED, HEREDOC]\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [{\n      begin: `\\\\b0b[01]+(?:_[01]+)*\\\\b`\n    },\n    // Binary w/ underscore support\n    {\n      begin: `\\\\b0o[0-7]+(?:_[0-7]+)*\\\\b`\n    },\n    // Octals w/ underscore support\n    {\n      begin: `\\\\b0x[\\\\da-f]+(?:_[\\\\da-f]+)*\\\\b`\n    },\n    // Hex w/ underscore support\n    // Decimals w/ underscore support, with optional fragments and scientific exponent (e) suffix.\n    {\n      begin: `(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:e[+-]?\\\\d+)?`\n    }],\n    relevance: 0\n  };\n  const KEYWORDS = {\n    keyword:\n    // Magic constants:\n    // <https://www.php.net/manual/en/language.constants.predefined.php>\n    '__CLASS__ __DIR__ __FILE__ __FUNCTION__ __LINE__ __METHOD__ __NAMESPACE__ __TRAIT__ ' +\n    // Function that look like language construct or language construct that look like function:\n    // List of keywords that may not require parenthesis\n    'die echo exit include include_once print require require_once ' +\n    // These are not language construct (function) but operate on the currently-executing function and can access the current symbol table\n    // 'compact extract func_get_arg func_get_args func_num_args get_called_class get_parent_class ' +\n    // Other keywords:\n    // <https://www.php.net/manual/en/reserved.php>\n    // <https://www.php.net/manual/en/language.types.type-juggling.php>\n    'array abstract and as binary bool boolean break callable case catch class clone const continue declare ' + 'default do double else elseif empty enddeclare endfor endforeach endif endswitch endwhile enum eval extends ' + 'final finally float for foreach from global goto if implements instanceof insteadof int integer interface ' + 'isset iterable list match|0 mixed new object or private protected public real return string switch throw trait ' + 'try unset use var void while xor yield',\n    literal: 'false null true',\n    built_in:\n    // Standard PHP library:\n    // <https://www.php.net/manual/en/book.spl.php>\n    'Error|0 ' +\n    // error is too common a name esp since PHP is case in-sensitive\n    'AppendIterator ArgumentCountError ArithmeticError ArrayIterator ArrayObject AssertionError BadFunctionCallException BadMethodCallException CachingIterator CallbackFilterIterator CompileError Countable DirectoryIterator DivisionByZeroError DomainException EmptyIterator ErrorException Exception FilesystemIterator FilterIterator GlobIterator InfiniteIterator InvalidArgumentException IteratorIterator LengthException LimitIterator LogicException MultipleIterator NoRewindIterator OutOfBoundsException OutOfRangeException OuterIterator OverflowException ParentIterator ParseError RangeException RecursiveArrayIterator RecursiveCachingIterator RecursiveCallbackFilterIterator RecursiveDirectoryIterator RecursiveFilterIterator RecursiveIterator RecursiveIteratorIterator RecursiveRegexIterator RecursiveTreeIterator RegexIterator RuntimeException SeekableIterator SplDoublyLinkedList SplFileInfo SplFileObject SplFixedArray SplHeap SplMaxHeap SplMinHeap SplObjectStorage SplObserver SplObserver SplPriorityQueue SplQueue SplStack SplSubject SplSubject SplTempFileObject TypeError UnderflowException UnexpectedValueException UnhandledMatchError ' +\n    // Reserved interfaces:\n    // <https://www.php.net/manual/en/reserved.interfaces.php>\n    'ArrayAccess Closure Generator Iterator IteratorAggregate Serializable Stringable Throwable Traversable WeakReference WeakMap ' +\n    // Reserved classes:\n    // <https://www.php.net/manual/en/reserved.classes.php>\n    'Directory __PHP_Incomplete_Class parent php_user_filter self static stdClass'\n  };\n  return {\n    aliases: ['php3', 'php4', 'php5', 'php6', 'php7', 'php8'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: [hljs.HASH_COMMENT_MODE, hljs.COMMENT('//', '$', {\n      contains: [PREPROCESSOR]\n    }), hljs.COMMENT('/\\\\*', '\\\\*/', {\n      contains: [{\n        className: 'doctag',\n        begin: '@[A-Za-z]+'\n      }]\n    }), hljs.COMMENT('__halt_compiler.+?;', false, {\n      endsWithParent: true,\n      keywords: '__halt_compiler'\n    }), PREPROCESSOR, {\n      className: 'keyword',\n      begin: /\\$this\\b/\n    }, VARIABLE, {\n      // swallow composed identifiers to avoid parsing them as keywords\n      begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n    }, {\n      className: 'function',\n      relevance: 0,\n      beginKeywords: 'fn function',\n      end: /[;{]/,\n      excludeEnd: true,\n      illegal: '[$%\\\\[]',\n      contains: [{\n        beginKeywords: 'use'\n      }, hljs.UNDERSCORE_TITLE_MODE, {\n        begin: '=>',\n        // No markup, just a relevance booster\n        endsParent: true\n      }, {\n        className: 'params',\n        begin: '\\\\(',\n        end: '\\\\)',\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: ['self', VARIABLE, hljs.C_BLOCK_COMMENT_MODE, STRING, NUMBER]\n      }]\n    }, {\n      className: 'class',\n      variants: [{\n        beginKeywords: \"enum\",\n        illegal: /[($\"]/\n      }, {\n        beginKeywords: \"class interface trait\",\n        illegal: /[:($\"]/\n      }],\n      relevance: 0,\n      end: /\\{/,\n      excludeEnd: true,\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      beginKeywords: 'namespace',\n      relevance: 0,\n      end: ';',\n      illegal: /[.']/,\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      beginKeywords: 'use',\n      relevance: 0,\n      end: ';',\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, STRING, NUMBER]\n  };\n}\nmodule.exports = php;", "map": {"version": 3, "names": ["php", "hljs", "VARIABLE", "className", "begin", "PREPROCESSOR", "variants", "relevance", "SUBST", "end", "SINGLE_QUOTED", "inherit", "APOS_STRING_MODE", "illegal", "DOUBLE_QUOTED", "QUOTE_STRING_MODE", "contains", "concat", "HEREDOC", "END_SAME_AS_BEGIN", "STRING", "BACKSLASH_ESCAPE", "NUMBER", "KEYWORDS", "keyword", "literal", "built_in", "aliases", "case_insensitive", "keywords", "HASH_COMMENT_MODE", "COMMENT", "endsWithParent", "beginKeywords", "excludeEnd", "UNDERSCORE_TITLE_MODE", "endsParent", "excludeBegin", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/php.js"], "sourcesContent": ["/*\nLanguage: PHP\nAuthor: <PERSON> <<PERSON>.<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\n/**\n * @param {H<PERSON><PERSON><PERSON><PERSON>} hljs\n * @returns {LanguageDetail}\n * */\nfunction php(hljs) {\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*' +\n      // negative look-ahead tries to avoid matching patterns that are not\n      // Perl at all like $ident$, @ident@, etc.\n      `(?![A-Za-z0-9])(?![$])`\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    variants: [\n      { begin: /<\\?php/, relevance: 10 }, // boost for obvious PHP\n      { begin: /<\\?[=]?/ },\n      { begin: /\\?>/ } // end php tag\n    ]\n  };\n  const SUBST = {\n    className: 'subst',\n    variants: [\n      { begin: /\\$\\w+/ },\n      { begin: /\\{\\$/, end: /\\}/ }\n    ]\n  };\n  const SINGLE_QUOTED = hljs.inherit(hljs.APOS_STRING_MODE, {\n    illegal: null,\n  });\n  const DOUBLE_QUOTED = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n  const HEREDOC = hljs.END_SAME_AS_BEGIN({\n    begin: /<<<[ \\t]*(\\w+)\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, PREPROCESSOR],\n    variants: [\n      hljs.inherit(SINGLE_QUOTED, {\n        begin: \"b'\", end: \"'\",\n      }),\n      hljs.inherit(DOUBLE_QUOTED, {\n        begin: 'b\"', end: '\"',\n      }),\n      DOUBLE_QUOTED,\n      SINGLE_QUOTED,\n      HEREDOC\n    ]\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      { begin: `\\\\b0b[01]+(?:_[01]+)*\\\\b` }, // Binary w/ underscore support\n      { begin: `\\\\b0o[0-7]+(?:_[0-7]+)*\\\\b` }, // Octals w/ underscore support\n      { begin: `\\\\b0x[\\\\da-f]+(?:_[\\\\da-f]+)*\\\\b` }, // Hex w/ underscore support\n      // Decimals w/ underscore support, with optional fragments and scientific exponent (e) suffix.\n      { begin: `(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:e[+-]?\\\\d+)?` }\n    ],\n    relevance: 0\n  };\n  const KEYWORDS = {\n    keyword:\n    // Magic constants:\n    // <https://www.php.net/manual/en/language.constants.predefined.php>\n    '__CLASS__ __DIR__ __FILE__ __FUNCTION__ __LINE__ __METHOD__ __NAMESPACE__ __TRAIT__ ' +\n    // Function that look like language construct or language construct that look like function:\n    // List of keywords that may not require parenthesis\n    'die echo exit include include_once print require require_once ' +\n    // These are not language construct (function) but operate on the currently-executing function and can access the current symbol table\n    // 'compact extract func_get_arg func_get_args func_num_args get_called_class get_parent_class ' +\n    // Other keywords:\n    // <https://www.php.net/manual/en/reserved.php>\n    // <https://www.php.net/manual/en/language.types.type-juggling.php>\n    'array abstract and as binary bool boolean break callable case catch class clone const continue declare ' +\n    'default do double else elseif empty enddeclare endfor endforeach endif endswitch endwhile enum eval extends ' +\n    'final finally float for foreach from global goto if implements instanceof insteadof int integer interface ' +\n    'isset iterable list match|0 mixed new object or private protected public real return string switch throw trait ' +\n    'try unset use var void while xor yield',\n    literal: 'false null true',\n    built_in:\n    // Standard PHP library:\n    // <https://www.php.net/manual/en/book.spl.php>\n    'Error|0 ' + // error is too common a name esp since PHP is case in-sensitive\n    'AppendIterator ArgumentCountError ArithmeticError ArrayIterator ArrayObject AssertionError BadFunctionCallException BadMethodCallException CachingIterator CallbackFilterIterator CompileError Countable DirectoryIterator DivisionByZeroError DomainException EmptyIterator ErrorException Exception FilesystemIterator FilterIterator GlobIterator InfiniteIterator InvalidArgumentException IteratorIterator LengthException LimitIterator LogicException MultipleIterator NoRewindIterator OutOfBoundsException OutOfRangeException OuterIterator OverflowException ParentIterator ParseError RangeException RecursiveArrayIterator RecursiveCachingIterator RecursiveCallbackFilterIterator RecursiveDirectoryIterator RecursiveFilterIterator RecursiveIterator RecursiveIteratorIterator RecursiveRegexIterator RecursiveTreeIterator RegexIterator RuntimeException SeekableIterator SplDoublyLinkedList SplFileInfo SplFileObject SplFixedArray SplHeap SplMaxHeap SplMinHeap SplObjectStorage SplObserver SplObserver SplPriorityQueue SplQueue SplStack SplSubject SplSubject SplTempFileObject TypeError UnderflowException UnexpectedValueException UnhandledMatchError ' +\n    // Reserved interfaces:\n    // <https://www.php.net/manual/en/reserved.interfaces.php>\n    'ArrayAccess Closure Generator Iterator IteratorAggregate Serializable Stringable Throwable Traversable WeakReference WeakMap ' +\n    // Reserved classes:\n    // <https://www.php.net/manual/en/reserved.classes.php>\n    'Directory __PHP_Incomplete_Class parent php_user_filter self static stdClass'\n  };\n  return {\n    aliases: ['php3', 'php4', 'php5', 'php6', 'php7', 'php8'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.COMMENT('//', '$', {contains: [PREPROCESSOR]}),\n      hljs.COMMENT(\n        '/\\\\*',\n        '\\\\*/',\n        {\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      hljs.COMMENT(\n        '__halt_compiler.+?;',\n        false,\n        {\n          endsWithParent: true,\n          keywords: '__halt_compiler'\n        }\n      ),\n      PREPROCESSOR,\n      {\n        className: 'keyword', begin: /\\$this\\b/\n      },\n      VARIABLE,\n      {\n        // swallow composed identifiers to avoid parsing them as keywords\n        begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n      },\n      {\n        className: 'function',\n        relevance: 0,\n        beginKeywords: 'fn function', end: /[;{]/, excludeEnd: true,\n        illegal: '[$%\\\\[]',\n        contains: [\n          {\n            beginKeywords: 'use',\n          },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: '=>', // No markup, just a relevance booster\n            endsParent: true\n          },\n          {\n            className: 'params',\n            begin: '\\\\(', end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              VARIABLE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        variants: [\n          { beginKeywords: \"enum\", illegal: /[($\"]/ },\n          { beginKeywords: \"class interface trait\", illegal: /[:($\"]/ }\n        ],\n        relevance: 0,\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          {beginKeywords: 'extends implements'},\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: ';',\n        illegal: /[.']/,\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      },\n      {\n        beginKeywords: 'use',\n        relevance: 0,\n        end: ';',\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      },\n      STRING,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = php;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,8CAA8C;IACnD;IACA;IACA;EACJ,CAAC;EACD,MAAMC,YAAY,GAAG;IACnBF,SAAS,EAAE,MAAM;IACjBG,QAAQ,EAAE,CACR;MAAEF,KAAK,EAAE,QAAQ;MAAEG,SAAS,EAAE;IAAG,CAAC;IAAE;IACpC;MAAEH,KAAK,EAAE;IAAU,CAAC,EACpB;MAAEA,KAAK,EAAE;IAAM,CAAC,CAAC;IAAA;EAErB,CAAC;EACD,MAAMI,KAAK,GAAG;IACZL,SAAS,EAAE,OAAO;IAClBG,QAAQ,EAAE,CACR;MAAEF,KAAK,EAAE;IAAQ,CAAC,EAClB;MAAEA,KAAK,EAAE,MAAM;MAAEK,GAAG,EAAE;IAAK,CAAC;EAEhC,CAAC;EACD,MAAMC,aAAa,GAAGT,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,gBAAgB,EAAE;IACxDC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGb,IAAI,CAACU,OAAO,CAACV,IAAI,CAACc,iBAAiB,EAAE;IACzDF,OAAO,EAAE,IAAI;IACbG,QAAQ,EAAEf,IAAI,CAACc,iBAAiB,CAACC,QAAQ,CAACC,MAAM,CAACT,KAAK;EACxD,CAAC,CAAC;EACF,MAAMU,OAAO,GAAGjB,IAAI,CAACkB,iBAAiB,CAAC;IACrCf,KAAK,EAAE,kBAAkB;IACzBK,GAAG,EAAE,eAAe;IACpBO,QAAQ,EAAEf,IAAI,CAACc,iBAAiB,CAACC,QAAQ,CAACC,MAAM,CAACT,KAAK;EACxD,CAAC,CAAC;EACF,MAAMY,MAAM,GAAG;IACbjB,SAAS,EAAE,QAAQ;IACnBa,QAAQ,EAAE,CAACf,IAAI,CAACoB,gBAAgB,EAAEhB,YAAY,CAAC;IAC/CC,QAAQ,EAAE,CACRL,IAAI,CAACU,OAAO,CAACD,aAAa,EAAE;MAC1BN,KAAK,EAAE,IAAI;MAAEK,GAAG,EAAE;IACpB,CAAC,CAAC,EACFR,IAAI,CAACU,OAAO,CAACG,aAAa,EAAE;MAC1BV,KAAK,EAAE,IAAI;MAAEK,GAAG,EAAE;IACpB,CAAC,CAAC,EACFK,aAAa,EACbJ,aAAa,EACbQ,OAAO;EAEX,CAAC;EACD,MAAMI,MAAM,GAAG;IACbnB,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MAAEF,KAAK,EAAE;IAA2B,CAAC;IAAE;IACvC;MAAEA,KAAK,EAAE;IAA6B,CAAC;IAAE;IACzC;MAAEA,KAAK,EAAE;IAAmC,CAAC;IAAE;IAC/C;IACA;MAAEA,KAAK,EAAE;IAA0E,CAAC,CACrF;IACDG,SAAS,EAAE;EACb,CAAC;EACD,MAAMgB,QAAQ,GAAG;IACfC,OAAO;IACP;IACA;IACA,sFAAsF;IACtF;IACA;IACA,gEAAgE;IAChE;IACA;IACA;IACA;IACA;IACA,yGAAyG,GACzG,8GAA8G,GAC9G,4GAA4G,GAC5G,iHAAiH,GACjH,wCAAwC;IACxCC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ;IACR;IACA;IACA,UAAU;IAAG;IACb,unCAAunC;IACvnC;IACA;IACA,+HAA+H;IAC/H;IACA;IACA;EACF,CAAC;EACD,OAAO;IACLC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACzDC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAEN,QAAQ;IAClBP,QAAQ,EAAE,CACRf,IAAI,CAAC6B,iBAAiB,EACtB7B,IAAI,CAAC8B,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;MAACf,QAAQ,EAAE,CAACX,YAAY;IAAC,CAAC,CAAC,EACnDJ,IAAI,CAAC8B,OAAO,CACV,MAAM,EACN,MAAM,EACN;MACEf,QAAQ,EAAE,CACR;QACEb,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAC;IAEL,CACF,CAAC,EACDH,IAAI,CAAC8B,OAAO,CACV,qBAAqB,EACrB,KAAK,EACL;MACEC,cAAc,EAAE,IAAI;MACpBH,QAAQ,EAAE;IACZ,CACF,CAAC,EACDxB,YAAY,EACZ;MACEF,SAAS,EAAE,SAAS;MAAEC,KAAK,EAAE;IAC/B,CAAC,EACDF,QAAQ,EACR;MACE;MACAE,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,UAAU;MACrBI,SAAS,EAAE,CAAC;MACZ0B,aAAa,EAAE,aAAa;MAAExB,GAAG,EAAE,MAAM;MAAEyB,UAAU,EAAE,IAAI;MAC3DrB,OAAO,EAAE,SAAS;MAClBG,QAAQ,EAAE,CACR;QACEiB,aAAa,EAAE;MACjB,CAAC,EACDhC,IAAI,CAACkC,qBAAqB,EAC1B;QACE/B,KAAK,EAAE,IAAI;QAAE;QACbgC,UAAU,EAAE;MACd,CAAC,EACD;QACEjC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,KAAK;QAAEK,GAAG,EAAE,KAAK;QACxB4B,YAAY,EAAE,IAAI;QAClBH,UAAU,EAAE,IAAI;QAChBL,QAAQ,EAAEN,QAAQ;QAClBP,QAAQ,EAAE,CACR,MAAM,EACNd,QAAQ,EACRD,IAAI,CAACqC,oBAAoB,EACzBlB,MAAM,EACNE,MAAM;MAEV,CAAC;IAEL,CAAC,EACD;MACEnB,SAAS,EAAE,OAAO;MAClBG,QAAQ,EAAE,CACR;QAAE2B,aAAa,EAAE,MAAM;QAAEpB,OAAO,EAAE;MAAQ,CAAC,EAC3C;QAAEoB,aAAa,EAAE,uBAAuB;QAAEpB,OAAO,EAAE;MAAS,CAAC,CAC9D;MACDN,SAAS,EAAE,CAAC;MACZE,GAAG,EAAE,IAAI;MACTyB,UAAU,EAAE,IAAI;MAChBlB,QAAQ,EAAE,CACR;QAACiB,aAAa,EAAE;MAAoB,CAAC,EACrChC,IAAI,CAACkC,qBAAqB;IAE9B,CAAC,EACD;MACEF,aAAa,EAAE,WAAW;MAC1B1B,SAAS,EAAE,CAAC;MACZE,GAAG,EAAE,GAAG;MACRI,OAAO,EAAE,MAAM;MACfG,QAAQ,EAAE,CAACf,IAAI,CAACkC,qBAAqB;IACvC,CAAC,EACD;MACEF,aAAa,EAAE,KAAK;MACpB1B,SAAS,EAAE,CAAC;MACZE,GAAG,EAAE,GAAG;MACRO,QAAQ,EAAE,CAACf,IAAI,CAACkC,qBAAqB;IACvC,CAAC,EACDf,MAAM,EACNE,MAAM;EAEV,CAAC;AACH;AAEAiB,MAAM,CAACC,OAAO,GAAGxC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}