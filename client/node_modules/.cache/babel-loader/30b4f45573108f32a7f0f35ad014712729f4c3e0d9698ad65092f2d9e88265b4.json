{"ast": null, "code": "/*\nLanguage: Scala\nCategory: functional\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.scala-lang.org\n*/\n\nfunction scala(hljs) {\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@[A-Za-z]+'\n  };\n\n  // used in strings for escaping/interpolation/substitution\n  const SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: '\\\\$[A-Za-z0-9_]+'\n    }, {\n      begin: /\\$\\{/,\n      end: /\\}/\n    }]\n  };\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: '\"\"\"',\n      end: '\"\"\"'\n    }, {\n      begin: '\"',\n      end: '\"',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: '[a-z]+\"',\n      end: '\"',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST]\n    }, {\n      className: 'string',\n      begin: '[a-z]+\"\"\"',\n      end: '\"\"\"',\n      contains: [SUBST],\n      relevance: 10\n    }]\n  };\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '\\'\\\\w[\\\\w\\\\d_]*(?!\\')'\n  };\n  const TYPE = {\n    className: 'type',\n    begin: '\\\\b[A-Z][A-Za-z0-9_]*',\n    relevance: 0\n  };\n  const NAME = {\n    className: 'title',\n    begin: /[^0-9\\n\\t \"'(),.`{}\\[\\]:;][^\\n\\t \"'(),.`{}\\[\\]:;]+|[^0-9\\n\\t \"'(),.`{}\\[\\]:;=]/,\n    relevance: 0\n  };\n  const CLASS = {\n    className: 'class',\n    beginKeywords: 'class object trait type',\n    end: /[:={\\[\\n;]/,\n    excludeEnd: true,\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      beginKeywords: 'extends with',\n      relevance: 10\n    }, {\n      begin: /\\[/,\n      end: /\\]/,\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0,\n      contains: [TYPE]\n    }, {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0,\n      contains: [TYPE]\n    }, NAME]\n  };\n  const METHOD = {\n    className: 'function',\n    beginKeywords: 'def',\n    end: /[:={\\[(\\n;]/,\n    excludeEnd: true,\n    contains: [NAME]\n  };\n  return {\n    name: 'Scala',\n    keywords: {\n      literal: 'true false null',\n      keyword: 'type yield lazy override def with val var sealed abstract private trait object if forSome for while throw finally protected extends import final return else break new catch super class case package default try this match continue throws implicit'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRING, SYMBOL, TYPE, METHOD, CLASS, hljs.C_NUMBER_MODE, ANNOTATION]\n  };\n}\nmodule.exports = scala;", "map": {"version": 3, "names": ["scala", "hljs", "ANNOTATION", "className", "begin", "SUBST", "variants", "end", "STRING", "illegal", "contains", "BACKSLASH_ESCAPE", "relevance", "SYMBOL", "TYPE", "NAME", "CLASS", "beginKeywords", "excludeEnd", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "excludeBegin", "METHOD", "name", "keywords", "literal", "keyword", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/scala.js"], "sourcesContent": ["/*\nLanguage: Scala\nCategory: functional\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.scala-lang.org\n*/\n\nfunction scala(hljs) {\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@[A-Za-z]+'\n  };\n\n  // used in strings for escaping/interpolation/substitution\n  const SUBST = {\n    className: 'subst',\n    variants: [\n      {\n        begin: '\\\\$[A-Za-z0-9_]+'\n      },\n      {\n        begin: /\\$\\{/,\n        end: /\\}/\n      }\n    ]\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"'\n      },\n      {\n        begin: '\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '[a-z]+\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST\n        ]\n      },\n      {\n        className: 'string',\n        begin: '[a-z]+\"\"\"',\n        end: '\"\"\"',\n        contains: [ SUBST ],\n        relevance: 10\n      }\n    ]\n\n  };\n\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '\\'\\\\w[\\\\w\\\\d_]*(?!\\')'\n  };\n\n  const TYPE = {\n    className: 'type',\n    begin: '\\\\b[A-Z][A-Za-z0-9_]*',\n    relevance: 0\n  };\n\n  const NAME = {\n    className: 'title',\n    begin: /[^0-9\\n\\t \"'(),.`{}\\[\\]:;][^\\n\\t \"'(),.`{}\\[\\]:;]+|[^0-9\\n\\t \"'(),.`{}\\[\\]:;=]/,\n    relevance: 0\n  };\n\n  const CLASS = {\n    className: 'class',\n    beginKeywords: 'class object trait type',\n    end: /[:={\\[\\n;]/,\n    excludeEnd: true,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        beginKeywords: 'extends with',\n        relevance: 10\n      },\n      {\n        begin: /\\[/,\n        end: /\\]/,\n        excludeBegin: true,\n        excludeEnd: true,\n        relevance: 0,\n        contains: [ TYPE ]\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        relevance: 0,\n        contains: [ TYPE ]\n      },\n      NAME\n    ]\n  };\n\n  const METHOD = {\n    className: 'function',\n    beginKeywords: 'def',\n    end: /[:={\\[(\\n;]/,\n    excludeEnd: true,\n    contains: [ NAME ]\n  };\n\n  return {\n    name: 'Scala',\n    keywords: {\n      literal: 'true false null',\n      keyword: 'type yield lazy override def with val var sealed abstract private trait object if forSome for while throw finally protected extends import final return else break new catch super class case package default try this match continue throws implicit'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      STRING,\n      SYMBOL,\n      TYPE,\n      METHOD,\n      CLASS,\n      hljs.C_NUMBER_MODE,\n      ANNOTATION\n    ]\n  };\n}\n\nmodule.exports = scala;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,UAAU,GAAG;IACjBC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,KAAK,GAAG;IACZF,SAAS,EAAE,OAAO;IAClBG,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EAED,MAAMC,MAAM,GAAG;IACbL,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE,KAAK;MACZG,GAAG,EAAE;IACP,CAAC,EACD;MACEH,KAAK,EAAE,GAAG;MACVG,GAAG,EAAE,GAAG;MACRE,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,CAAET,IAAI,CAACU,gBAAgB;IACnC,CAAC,EACD;MACEP,KAAK,EAAE,SAAS;MAChBG,GAAG,EAAE,GAAG;MACRE,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,CACRT,IAAI,CAACU,gBAAgB,EACrBN,KAAK;IAET,CAAC,EACD;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,WAAW;MAClBG,GAAG,EAAE,KAAK;MACVG,QAAQ,EAAE,CAAEL,KAAK,CAAE;MACnBO,SAAS,EAAE;IACb,CAAC;EAGL,CAAC;EAED,MAAMC,MAAM,GAAG;IACbV,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMU,IAAI,GAAG;IACXX,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,uBAAuB;IAC9BQ,SAAS,EAAE;EACb,CAAC;EAED,MAAMG,IAAI,GAAG;IACXZ,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,gFAAgF;IACvFQ,SAAS,EAAE;EACb,CAAC;EAED,MAAMI,KAAK,GAAG;IACZb,SAAS,EAAE,OAAO;IAClBc,aAAa,EAAE,yBAAyB;IACxCV,GAAG,EAAE,YAAY;IACjBW,UAAU,EAAE,IAAI;IAChBR,QAAQ,EAAE,CACRT,IAAI,CAACkB,mBAAmB,EACxBlB,IAAI,CAACmB,oBAAoB,EACzB;MACEH,aAAa,EAAE,cAAc;MAC7BL,SAAS,EAAE;IACb,CAAC,EACD;MACER,KAAK,EAAE,IAAI;MACXG,GAAG,EAAE,IAAI;MACTc,YAAY,EAAE,IAAI;MAClBH,UAAU,EAAE,IAAI;MAChBN,SAAS,EAAE,CAAC;MACZF,QAAQ,EAAE,CAAEI,IAAI;IAClB,CAAC,EACD;MACEX,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,IAAI;MACXG,GAAG,EAAE,IAAI;MACTc,YAAY,EAAE,IAAI;MAClBH,UAAU,EAAE,IAAI;MAChBN,SAAS,EAAE,CAAC;MACZF,QAAQ,EAAE,CAAEI,IAAI;IAClB,CAAC,EACDC,IAAI;EAER,CAAC;EAED,MAAMO,MAAM,GAAG;IACbnB,SAAS,EAAE,UAAU;IACrBc,aAAa,EAAE,KAAK;IACpBV,GAAG,EAAE,aAAa;IAClBW,UAAU,EAAE,IAAI;IAChBR,QAAQ,EAAE,CAAEK,IAAI;EAClB,CAAC;EAED,OAAO;IACLQ,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE;MACRC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE;IACX,CAAC;IACDhB,QAAQ,EAAE,CACRT,IAAI,CAACkB,mBAAmB,EACxBlB,IAAI,CAACmB,oBAAoB,EACzBZ,MAAM,EACNK,MAAM,EACNC,IAAI,EACJQ,MAAM,EACNN,KAAK,EACLf,IAAI,CAAC0B,aAAa,EAClBzB,UAAU;EAEd,CAAC;AACH;AAEA0B,MAAM,CAACC,OAAO,GAAG7B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}