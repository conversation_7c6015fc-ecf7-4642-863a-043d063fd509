{"ast": null, "code": "'use strict';\n\nmodule.exports = avroIdl;\navroIdl.displayName = 'avroIdl';\navroIdl.aliases = [];\nfunction avroIdl(Prism) {\n  // GitHub: https://github.com/apache/avro\n  // Docs: https://avro.apache.org/docs/current/idl.html\n  Prism.languages['avro-idl'] = {\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    annotation: {\n      pattern: /@(?:[$\\w.-]|`[^\\r\\n`]+`)+/,\n      greedy: true,\n      alias: 'function'\n    },\n    'function-identifier': {\n      pattern: /`[^\\r\\n`]+`(?=\\s*\\()/,\n      greedy: true,\n      alias: 'function'\n    },\n    identifier: {\n      pattern: /`[^\\r\\n`]+`/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:enum|error|protocol|record|throws)\\b\\s+)[$\\w]+/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword: /\\b(?:array|boolean|bytes|date|decimal|double|enum|error|false|fixed|float|idl|import|int|local_timestamp_ms|long|map|null|oneway|protocol|record|schema|string|throws|time_ms|timestamp_ms|true|union|uuid|void)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: [{\n      pattern: /(^|[^\\w.])-?(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|0x(?:[a-f0-9]+(?:\\.[a-f0-9]*)?|\\.[a-f0-9]+)(?:p[+-]?\\d+)?)[dfl]?(?![\\w.])/i,\n      lookbehind: true\n    }, /-?\\b(?:Infinity|NaN)\\b/],\n    operator: /=/,\n    punctuation: /[()\\[\\]{}<>.:,;-]/\n  };\n  Prism.languages.avdl = Prism.languages['avro-idl'];\n}", "map": {"version": 3, "names": ["module", "exports", "avroIdl", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "lookbehind", "annotation", "alias", "identifier", "keyword", "function", "number", "operator", "punctuation", "avdl"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/avro-idl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = avroIdl\navroIdl.displayName = 'avroIdl'\navroIdl.aliases = []\nfunction avroIdl(Prism) {\n  // GitHub: https://github.com/apache/avro\n  // Docs: https://avro.apache.org/docs/current/idl.html\n  Prism.languages['avro-idl'] = {\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    annotation: {\n      pattern: /@(?:[$\\w.-]|`[^\\r\\n`]+`)+/,\n      greedy: true,\n      alias: 'function'\n    },\n    'function-identifier': {\n      pattern: /`[^\\r\\n`]+`(?=\\s*\\()/,\n      greedy: true,\n      alias: 'function'\n    },\n    identifier: {\n      pattern: /`[^\\r\\n`]+`/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:enum|error|protocol|record|throws)\\b\\s+)[$\\w]+/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:array|boolean|bytes|date|decimal|double|enum|error|false|fixed|float|idl|import|int|local_timestamp_ms|long|map|null|oneway|protocol|record|schema|string|throws|time_ms|timestamp_ms|true|union|uuid|void)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: [\n      {\n        pattern:\n          /(^|[^\\w.])-?(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|0x(?:[a-f0-9]+(?:\\.[a-f0-9]*)?|\\.[a-f0-9]+)(?:p[+-]?\\d+)?)[dfl]?(?![\\w.])/i,\n        lookbehind: true\n      },\n      /-?\\b(?:Infinity|NaN)\\b/\n    ],\n    operator: /=/,\n    punctuation: /[()\\[\\]{}<>.:,;-]/\n  }\n  Prism.languages.avdl = Prism.languages['avro-idl']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtB;EACA;EACAA,KAAK,CAACC,SAAS,CAAC,UAAU,CAAC,GAAG;IAC5BC,OAAO,EAAE;MACPC,OAAO,EAAE,yBAAyB;MAClCC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,gCAAgC;MACzCG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;IACV,CAAC;IACDG,UAAU,EAAE;MACVJ,OAAO,EAAE,2BAA2B;MACpCC,MAAM,EAAE,IAAI;MACZI,KAAK,EAAE;IACT,CAAC;IACD,qBAAqB,EAAE;MACrBL,OAAO,EAAE,sBAAsB;MAC/BC,MAAM,EAAE,IAAI;MACZI,KAAK,EAAE;IACT,CAAC;IACDC,UAAU,EAAE;MACVN,OAAO,EAAE,aAAa;MACtBC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZD,OAAO,EAAE,sDAAsD;MAC/DG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;IACV,CAAC;IACDM,OAAO,EACL,oNAAoN;IACtNC,QAAQ,EAAE,uBAAuB;IACjCC,MAAM,EAAE,CACN;MACET,OAAO,EACL,iIAAiI;MACnIG,UAAU,EAAE;IACd,CAAC,EACD,wBAAwB,CACzB;IACDO,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE;EACf,CAAC;EACDd,KAAK,CAACC,SAAS,CAACc,IAAI,GAAGf,KAAK,CAACC,SAAS,CAAC,UAAU,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}