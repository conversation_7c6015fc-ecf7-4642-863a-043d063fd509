{"ast": null, "code": "'use strict';\n\nmodule.exports = v;\nv.displayName = 'v';\nv.aliases = [];\nfunction v(Prism) {\n  ;\n  (function (Prism) {\n    var interpolationExpr = {\n      pattern: /[\\s\\S]+/,\n      inside: null\n    };\n    Prism.languages.v = Prism.languages.extend('clike', {\n      string: {\n        pattern: /r?([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        alias: 'quoted-string',\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[^{}]*\\}|\\w+(?:\\.\\w+(?:\\([^\\(\\)]*\\))?|\\[[^\\[\\]]+\\])*)/,\n            lookbehind: true,\n            inside: {\n              'interpolation-variable': {\n                pattern: /^\\$\\w[\\s\\S]*$/,\n                alias: 'variable'\n              },\n              'interpolation-punctuation': {\n                pattern: /^\\$\\{|\\}$/,\n                alias: 'punctuation'\n              },\n              'interpolation-expression': interpolationExpr\n            }\n          }\n        }\n      },\n      'class-name': {\n        pattern: /(\\b(?:enum|interface|struct|type)\\s+)(?:C\\.)?\\w+/,\n        lookbehind: true\n      },\n      keyword: /(?:\\b(?:__global|as|asm|assert|atomic|break|chan|const|continue|defer|else|embed|enum|fn|for|go(?:to)?|if|import|in|interface|is|lock|match|module|mut|none|or|pub|return|rlock|select|shared|sizeof|static|struct|type(?:of)?|union|unsafe)|\\$(?:else|for|if)|#(?:flag|include))\\b/,\n      number: /\\b(?:0x[a-f\\d]+(?:_[a-f\\d]+)*|0b[01]+(?:_[01]+)*|0o[0-7]+(?:_[0-7]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?)\\b/i,\n      operator: /~|\\?|[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\.?/,\n      builtin: /\\b(?:any(?:_float|_int)?|bool|byte(?:ptr)?|charptr|f(?:32|64)|i(?:8|16|64|128|nt)|rune|size_t|string|u(?:16|32|64|128)|voidptr)\\b/\n    });\n    interpolationExpr.inside = Prism.languages.v;\n    Prism.languages.insertBefore('v', 'string', {\n      char: {\n        pattern: /`(?:\\\\`|\\\\?[^`]{1,2})`/,\n        // using {1,2} instead of `u` flag for compatibility\n        alias: 'rune'\n      }\n    });\n    Prism.languages.insertBefore('v', 'operator', {\n      attribute: {\n        pattern: /(^[\\t ]*)\\[(?:deprecated|direct_array_access|flag|inline|live|ref_only|typedef|unsafe_fn|windows_stdcall)\\]/m,\n        lookbehind: true,\n        alias: 'annotation',\n        inside: {\n          punctuation: /[\\[\\]]/,\n          keyword: /\\w+/\n        }\n      },\n      generic: {\n        pattern: /<\\w+>(?=\\s*[\\)\\{])/,\n        inside: {\n          punctuation: /[<>]/,\n          'class-name': /\\w+/\n        }\n      }\n    });\n    Prism.languages.insertBefore('v', 'function', {\n      'generic-function': {\n        // e.g. foo<T>( ...\n        pattern: /\\b\\w+\\s*<\\w+>(?=\\()/,\n        inside: {\n          function: /^\\w+/,\n          generic: {\n            pattern: /<\\w+>/,\n            inside: Prism.languages.v.generic.inside\n          }\n        }\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "v", "displayName", "aliases", "Prism", "interpolationExpr", "pattern", "inside", "languages", "extend", "string", "alias", "greedy", "interpolation", "lookbehind", "keyword", "number", "operator", "builtin", "insertBefore", "char", "attribute", "punctuation", "generic", "function"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/v.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = v\nv.displayName = 'v'\nv.aliases = []\nfunction v(Prism) {\n  ;(function (Prism) {\n    var interpolationExpr = {\n      pattern: /[\\s\\S]+/,\n      inside: null\n    }\n    Prism.languages.v = Prism.languages.extend('clike', {\n      string: {\n        pattern: /r?([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        alias: 'quoted-string',\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[^{}]*\\}|\\w+(?:\\.\\w+(?:\\([^\\(\\)]*\\))?|\\[[^\\[\\]]+\\])*)/,\n            lookbehind: true,\n            inside: {\n              'interpolation-variable': {\n                pattern: /^\\$\\w[\\s\\S]*$/,\n                alias: 'variable'\n              },\n              'interpolation-punctuation': {\n                pattern: /^\\$\\{|\\}$/,\n                alias: 'punctuation'\n              },\n              'interpolation-expression': interpolationExpr\n            }\n          }\n        }\n      },\n      'class-name': {\n        pattern: /(\\b(?:enum|interface|struct|type)\\s+)(?:C\\.)?\\w+/,\n        lookbehind: true\n      },\n      keyword:\n        /(?:\\b(?:__global|as|asm|assert|atomic|break|chan|const|continue|defer|else|embed|enum|fn|for|go(?:to)?|if|import|in|interface|is|lock|match|module|mut|none|or|pub|return|rlock|select|shared|sizeof|static|struct|type(?:of)?|union|unsafe)|\\$(?:else|for|if)|#(?:flag|include))\\b/,\n      number:\n        /\\b(?:0x[a-f\\d]+(?:_[a-f\\d]+)*|0b[01]+(?:_[01]+)*|0o[0-7]+(?:_[0-7]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?)\\b/i,\n      operator:\n        /~|\\?|[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\.?/,\n      builtin:\n        /\\b(?:any(?:_float|_int)?|bool|byte(?:ptr)?|charptr|f(?:32|64)|i(?:8|16|64|128|nt)|rune|size_t|string|u(?:16|32|64|128)|voidptr)\\b/\n    })\n    interpolationExpr.inside = Prism.languages.v\n    Prism.languages.insertBefore('v', 'string', {\n      char: {\n        pattern: /`(?:\\\\`|\\\\?[^`]{1,2})`/,\n        // using {1,2} instead of `u` flag for compatibility\n        alias: 'rune'\n      }\n    })\n    Prism.languages.insertBefore('v', 'operator', {\n      attribute: {\n        pattern:\n          /(^[\\t ]*)\\[(?:deprecated|direct_array_access|flag|inline|live|ref_only|typedef|unsafe_fn|windows_stdcall)\\]/m,\n        lookbehind: true,\n        alias: 'annotation',\n        inside: {\n          punctuation: /[\\[\\]]/,\n          keyword: /\\w+/\n        }\n      },\n      generic: {\n        pattern: /<\\w+>(?=\\s*[\\)\\{])/,\n        inside: {\n          punctuation: /[<>]/,\n          'class-name': /\\w+/\n        }\n      }\n    })\n    Prism.languages.insertBefore('v', 'function', {\n      'generic-function': {\n        // e.g. foo<T>( ...\n        pattern: /\\b\\w+\\s*<\\w+>(?=\\()/,\n        inside: {\n          function: /^\\w+/,\n          generic: {\n            pattern: /<\\w+>/,\n            inside: Prism.languages.v.generic.inside\n          }\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,CAAC;AAClBA,CAAC,CAACC,WAAW,GAAG,GAAG;AACnBD,CAAC,CAACE,OAAO,GAAG,EAAE;AACd,SAASF,CAACA,CAACG,KAAK,EAAE;EAChB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,iBAAiB,GAAG;MACtBC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;IACV,CAAC;IACDH,KAAK,CAACI,SAAS,CAACP,CAAC,GAAGG,KAAK,CAACI,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MAClDC,MAAM,EAAE;QACNJ,OAAO,EAAE,kDAAkD;QAC3DK,KAAK,EAAE,eAAe;QACtBC,MAAM,EAAE,IAAI;QACZL,MAAM,EAAE;UACNM,aAAa,EAAE;YACbP,OAAO,EACL,qFAAqF;YACvFQ,UAAU,EAAE,IAAI;YAChBP,MAAM,EAAE;cACN,wBAAwB,EAAE;gBACxBD,OAAO,EAAE,eAAe;gBACxBK,KAAK,EAAE;cACT,CAAC;cACD,2BAA2B,EAAE;gBAC3BL,OAAO,EAAE,WAAW;gBACpBK,KAAK,EAAE;cACT,CAAC;cACD,0BAA0B,EAAEN;YAC9B;UACF;QACF;MACF,CAAC;MACD,YAAY,EAAE;QACZC,OAAO,EAAE,kDAAkD;QAC3DQ,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EACL,qRAAqR;MACvRC,MAAM,EACJ,2GAA2G;MAC7GC,QAAQ,EACN,6FAA6F;MAC/FC,OAAO,EACL;IACJ,CAAC,CAAC;IACFb,iBAAiB,CAACE,MAAM,GAAGH,KAAK,CAACI,SAAS,CAACP,CAAC;IAC5CG,KAAK,CAACI,SAAS,CAACW,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE;MAC1CC,IAAI,EAAE;QACJd,OAAO,EAAE,wBAAwB;QACjC;QACAK,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFP,KAAK,CAACI,SAAS,CAACW,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE;MAC5CE,SAAS,EAAE;QACTf,OAAO,EACL,8GAA8G;QAChHQ,UAAU,EAAE,IAAI;QAChBH,KAAK,EAAE,YAAY;QACnBJ,MAAM,EAAE;UACNe,WAAW,EAAE,QAAQ;UACrBP,OAAO,EAAE;QACX;MACF,CAAC;MACDQ,OAAO,EAAE;QACPjB,OAAO,EAAE,oBAAoB;QAC7BC,MAAM,EAAE;UACNe,WAAW,EAAE,MAAM;UACnB,YAAY,EAAE;QAChB;MACF;IACF,CAAC,CAAC;IACFlB,KAAK,CAACI,SAAS,CAACW,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE;MAC5C,kBAAkB,EAAE;QAClB;QACAb,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE;UACNiB,QAAQ,EAAE,MAAM;UAChBD,OAAO,EAAE;YACPjB,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAEH,KAAK,CAACI,SAAS,CAACP,CAAC,CAACsB,OAAO,CAAChB;UACpC;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAEH,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}