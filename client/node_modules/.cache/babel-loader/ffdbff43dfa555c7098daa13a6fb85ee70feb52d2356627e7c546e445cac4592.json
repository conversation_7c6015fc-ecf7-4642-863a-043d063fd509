{"ast": null, "code": "'use strict';\n\nvar formatter = require('format');\nvar fault = create(Error);\nmodule.exports = fault;\nfault.eval = create(EvalError);\nfault.range = create(RangeError);\nfault.reference = create(ReferenceError);\nfault.syntax = create(SyntaxError);\nfault.type = create(TypeError);\nfault.uri = create(URIError);\nfault.create = create;\n\n// Create a new `EConstructor`, with the formatted `format` as a first argument.\nfunction create(EConstructor) {\n  FormattedError.displayName = EConstructor.displayName || EConstructor.name;\n  return FormattedError;\n  function FormattedError(format) {\n    if (format) {\n      format = formatter.apply(null, arguments);\n    }\n    return new EConstructor(format);\n  }\n}", "map": {"version": 3, "names": ["formatter", "require", "fault", "create", "Error", "module", "exports", "eval", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "range", "RangeError", "reference", "ReferenceError", "syntax", "SyntaxError", "type", "TypeError", "uri", "URIError", "EConstructor", "FormattedError", "displayName", "name", "format", "apply", "arguments"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/fault/index.js"], "sourcesContent": ["'use strict'\n\nvar formatter = require('format')\n\nvar fault = create(Error)\n\nmodule.exports = fault\n\nfault.eval = create(EvalError)\nfault.range = create(RangeError)\nfault.reference = create(ReferenceError)\nfault.syntax = create(SyntaxError)\nfault.type = create(TypeError)\nfault.uri = create(URIError)\n\nfault.create = create\n\n// Create a new `EConstructor`, with the formatted `format` as a first argument.\nfunction create(EConstructor) {\n  FormattedError.displayName = EConstructor.displayName || EConstructor.name\n\n  return FormattedError\n\n  function FormattedError(format) {\n    if (format) {\n      format = formatter.apply(null, arguments)\n    }\n\n    return new EConstructor(format)\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAEjC,IAAIC,KAAK,GAAGC,MAAM,CAACC,KAAK,CAAC;AAEzBC,MAAM,CAACC,OAAO,GAAGJ,KAAK;AAEtBA,KAAK,CAACK,IAAI,GAAGJ,MAAM,CAACK,SAAS,CAAC;AAC9BN,KAAK,CAACO,KAAK,GAAGN,MAAM,CAACO,UAAU,CAAC;AAChCR,KAAK,CAACS,SAAS,GAAGR,MAAM,CAACS,cAAc,CAAC;AACxCV,KAAK,CAACW,MAAM,GAAGV,MAAM,CAACW,WAAW,CAAC;AAClCZ,KAAK,CAACa,IAAI,GAAGZ,MAAM,CAACa,SAAS,CAAC;AAC9Bd,KAAK,CAACe,GAAG,GAAGd,MAAM,CAACe,QAAQ,CAAC;AAE5BhB,KAAK,CAACC,MAAM,GAAGA,MAAM;;AAErB;AACA,SAASA,MAAMA,CAACgB,YAAY,EAAE;EAC5BC,cAAc,CAACC,WAAW,GAAGF,YAAY,CAACE,WAAW,IAAIF,YAAY,CAACG,IAAI;EAE1E,OAAOF,cAAc;EAErB,SAASA,cAAcA,CAACG,MAAM,EAAE;IAC9B,IAAIA,MAAM,EAAE;MACVA,MAAM,GAAGvB,SAAS,CAACwB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC3C;IAEA,OAAO,IAAIN,YAAY,CAACI,MAAM,CAAC;EACjC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}