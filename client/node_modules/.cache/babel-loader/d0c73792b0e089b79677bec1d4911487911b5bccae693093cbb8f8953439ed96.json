{"ast": null, "code": "/*\nLanguage: Hy\nDescription: Hy is a wonderful dialect of Lisp that’s embedded in Python.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://docs.hylang.org/en/stable/\nCategory: lisp\n*/\n\nfunction hy(hljs) {\n  var SYMBOLSTART = 'a-zA-Z_\\\\-!.?+*=<>&#\\'';\n  var SYMBOL_RE = '[' + SYMBOLSTART + '][' + SYMBOLSTART + '0-9/;:]*';\n  var keywords = {\n    $pattern: SYMBOL_RE,\n    'builtin-name':\n    // keywords\n    '!= % %= & &= * ** **= *= *map ' + '+ += , --build-class-- --import-- -= . / // //= ' + '/= < << <<= <= = > >= >> >>= ' + '@ @= ^ ^= abs accumulate all and any ap-compose ' + 'ap-dotimes ap-each ap-each-while ap-filter ap-first ap-if ap-last ap-map ap-map-when ap-pipe ' + 'ap-reduce ap-reject apply as-> ascii assert assoc bin break butlast ' + 'callable calling-module-name car case cdr chain chr coll? combinations compile ' + 'compress cond cons cons? continue count curry cut cycle dec ' + 'def default-method defclass defmacro defmacro-alias defmacro/g! defmain defmethod defmulti defn ' + 'defn-alias defnc defnr defreader defseq del delattr delete-route dict-comp dir ' + 'disassemble dispatch-reader-macro distinct divmod do doto drop drop-last drop-while empty? ' + 'end-sequence eval eval-and-compile eval-when-compile even? every? except exec filter first ' + 'flatten float? fn fnc fnr for for* format fraction genexpr ' + 'gensym get getattr global globals group-by hasattr hash hex id ' + 'identity if if* if-not if-python2 import in inc input instance? ' + 'integer integer-char? integer? interleave interpose is is-coll is-cons is-empty is-even ' + 'is-every is-float is-instance is-integer is-integer-char is-iterable is-iterator is-keyword is-neg is-none ' + 'is-not is-numeric is-odd is-pos is-string is-symbol is-zero isinstance islice issubclass ' + 'iter iterable? iterate iterator? keyword keyword? lambda last len let ' + 'lif lif-not list* list-comp locals loop macro-error macroexpand macroexpand-1 macroexpand-all ' + 'map max merge-with method-decorator min multi-decorator multicombinations name neg? next ' + 'none? nonlocal not not-in not? nth numeric? oct odd? open ' + 'or ord partition permutations pos? post-route postwalk pow prewalk print ' + 'product profile/calls profile/cpu put-route quasiquote quote raise range read read-str ' + 'recursive-replace reduce remove repeat repeatedly repr require rest round route ' + 'route-with-methods rwm second seq set-comp setattr setv some sorted string ' + 'string? sum switch symbol? take take-nth take-while tee try unless ' + 'unquote unquote-splicing vars walk when while with with* with-decorator with-gensyms ' + 'xi xor yield yield-from zero? zip zip-longest | |= ~'\n  };\n  var SIMPLE_NUMBER_RE = '[-+]?\\\\d+(\\\\.\\\\d+)?';\n  var SYMBOL = {\n    begin: SYMBOL_RE,\n    relevance: 0\n  };\n  var NUMBER = {\n    className: 'number',\n    begin: SIMPLE_NUMBER_RE,\n    relevance: 0\n  };\n  var STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  var COMMENT = hljs.COMMENT(';', '$', {\n    relevance: 0\n  });\n  var LITERAL = {\n    className: 'literal',\n    begin: /\\b([Tt]rue|[Ff]alse|nil|None)\\b/\n  };\n  var COLLECTION = {\n    begin: '[\\\\[\\\\{]',\n    end: '[\\\\]\\\\}]'\n  };\n  var HINT = {\n    className: 'comment',\n    begin: '\\\\^' + SYMBOL_RE\n  };\n  var HINT_COL = hljs.COMMENT('\\\\^\\\\{', '\\\\}');\n  var KEY = {\n    className: 'symbol',\n    begin: '[:]{1,2}' + SYMBOL_RE\n  };\n  var LIST = {\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n  var BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  var NAME = {\n    className: 'name',\n    relevance: 0,\n    keywords: keywords,\n    begin: SYMBOL_RE,\n    starts: BODY\n  };\n  var DEFAULT_CONTAINS = [LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL, SYMBOL];\n  LIST.contains = [hljs.COMMENT('comment', ''), NAME, BODY];\n  BODY.contains = DEFAULT_CONTAINS;\n  COLLECTION.contains = DEFAULT_CONTAINS;\n  return {\n    name: 'Hy',\n    aliases: ['hylang'],\n    illegal: /\\S/,\n    contains: [hljs.SHEBANG(), LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL]\n  };\n}\nmodule.exports = hy;", "map": {"version": 3, "names": ["hy", "hljs", "SYMBOLSTART", "SYMBOL_RE", "keywords", "$pattern", "SIMPLE_NUMBER_RE", "SYMBOL", "begin", "relevance", "NUMBER", "className", "STRING", "inherit", "QUOTE_STRING_MODE", "illegal", "COMMENT", "LITERAL", "COLLECTION", "end", "HINT", "HINT_COL", "KEY", "LIST", "BODY", "endsWithParent", "NAME", "starts", "DEFAULT_CONTAINS", "contains", "name", "aliases", "SHEBANG", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/hy.js"], "sourcesContent": ["/*\nLanguage: Hy\nDescription: Hy is a wonderful dialect of Lisp that’s embedded in Python.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://docs.hylang.org/en/stable/\nCategory: lisp\n*/\n\nfunction hy(hljs) {\n  var SYMBOLSTART = 'a-zA-Z_\\\\-!.?+*=<>&#\\'';\n  var SYMBOL_RE = '[' + SYMBOLSTART + '][' + SYMBOLSTART + '0-9/;:]*';\n  var keywords = {\n    $pattern: SYMBOL_RE,\n    'builtin-name':\n      // keywords\n      '!= % %= & &= * ** **= *= *map ' +\n      '+ += , --build-class-- --import-- -= . / // //= ' +\n      '/= < << <<= <= = > >= >> >>= ' +\n      '@ @= ^ ^= abs accumulate all and any ap-compose ' +\n      'ap-dotimes ap-each ap-each-while ap-filter ap-first ap-if ap-last ap-map ap-map-when ap-pipe ' +\n      'ap-reduce ap-reject apply as-> ascii assert assoc bin break butlast ' +\n      'callable calling-module-name car case cdr chain chr coll? combinations compile ' +\n      'compress cond cons cons? continue count curry cut cycle dec ' +\n      'def default-method defclass defmacro defmacro-alias defmacro/g! defmain defmethod defmulti defn ' +\n      'defn-alias defnc defnr defreader defseq del delattr delete-route dict-comp dir ' +\n      'disassemble dispatch-reader-macro distinct divmod do doto drop drop-last drop-while empty? ' +\n      'end-sequence eval eval-and-compile eval-when-compile even? every? except exec filter first ' +\n      'flatten float? fn fnc fnr for for* format fraction genexpr ' +\n      'gensym get getattr global globals group-by hasattr hash hex id ' +\n      'identity if if* if-not if-python2 import in inc input instance? ' +\n      'integer integer-char? integer? interleave interpose is is-coll is-cons is-empty is-even ' +\n      'is-every is-float is-instance is-integer is-integer-char is-iterable is-iterator is-keyword is-neg is-none ' +\n      'is-not is-numeric is-odd is-pos is-string is-symbol is-zero isinstance islice issubclass ' +\n      'iter iterable? iterate iterator? keyword keyword? lambda last len let ' +\n      'lif lif-not list* list-comp locals loop macro-error macroexpand macroexpand-1 macroexpand-all ' +\n      'map max merge-with method-decorator min multi-decorator multicombinations name neg? next ' +\n      'none? nonlocal not not-in not? nth numeric? oct odd? open ' +\n      'or ord partition permutations pos? post-route postwalk pow prewalk print ' +\n      'product profile/calls profile/cpu put-route quasiquote quote raise range read read-str ' +\n      'recursive-replace reduce remove repeat repeatedly repr require rest round route ' +\n      'route-with-methods rwm second seq set-comp setattr setv some sorted string ' +\n      'string? sum switch symbol? take take-nth take-while tee try unless ' +\n      'unquote unquote-splicing vars walk when while with with* with-decorator with-gensyms ' +\n      'xi xor yield yield-from zero? zip zip-longest | |= ~'\n   };\n\n  var SIMPLE_NUMBER_RE = '[-+]?\\\\d+(\\\\.\\\\d+)?';\n\n  var SYMBOL = {\n    begin: SYMBOL_RE,\n    relevance: 0\n  };\n  var NUMBER = {\n    className: 'number', begin: SIMPLE_NUMBER_RE,\n    relevance: 0\n  };\n  var STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {illegal: null});\n  var COMMENT = hljs.COMMENT(\n    ';',\n    '$',\n    {\n      relevance: 0\n    }\n  );\n  var LITERAL = {\n    className: 'literal',\n    begin: /\\b([Tt]rue|[Ff]alse|nil|None)\\b/\n  };\n  var COLLECTION = {\n    begin: '[\\\\[\\\\{]', end: '[\\\\]\\\\}]'\n  };\n  var HINT = {\n    className: 'comment',\n    begin: '\\\\^' + SYMBOL_RE\n  };\n  var HINT_COL = hljs.COMMENT('\\\\^\\\\{', '\\\\}');\n  var KEY = {\n    className: 'symbol',\n    begin: '[:]{1,2}' + SYMBOL_RE\n  };\n  var LIST = {\n    begin: '\\\\(', end: '\\\\)'\n  };\n  var BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  var NAME = {\n    className: 'name',\n    relevance: 0,\n    keywords: keywords,\n    begin: SYMBOL_RE,\n    starts: BODY\n  };\n  var DEFAULT_CONTAINS = [LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL, SYMBOL];\n\n  LIST.contains = [hljs.COMMENT('comment', ''), NAME, BODY];\n  BODY.contains = DEFAULT_CONTAINS;\n  COLLECTION.contains = DEFAULT_CONTAINS;\n\n  return {\n    name: 'Hy',\n    aliases: ['hylang'],\n    illegal: /\\S/,\n    contains: [hljs.SHEBANG(), LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL]\n  };\n}\n\nmodule.exports = hy;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,EAAEA,CAACC,IAAI,EAAE;EAChB,IAAIC,WAAW,GAAG,wBAAwB;EAC1C,IAAIC,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,IAAI,GAAGA,WAAW,GAAG,UAAU;EACnE,IAAIE,QAAQ,GAAG;IACbC,QAAQ,EAAEF,SAAS;IACnB,cAAc;IACZ;IACA,gCAAgC,GAChC,kDAAkD,GAClD,+BAA+B,GAC/B,kDAAkD,GAClD,+FAA+F,GAC/F,sEAAsE,GACtE,iFAAiF,GACjF,8DAA8D,GAC9D,kGAAkG,GAClG,iFAAiF,GACjF,6FAA6F,GAC7F,6FAA6F,GAC7F,6DAA6D,GAC7D,iEAAiE,GACjE,kEAAkE,GAClE,0FAA0F,GAC1F,6GAA6G,GAC7G,2FAA2F,GAC3F,wEAAwE,GACxE,gGAAgG,GAChG,2FAA2F,GAC3F,4DAA4D,GAC5D,2EAA2E,GAC3E,yFAAyF,GACzF,kFAAkF,GAClF,6EAA6E,GAC7E,qEAAqE,GACrE,uFAAuF,GACvF;EACH,CAAC;EAEF,IAAIG,gBAAgB,GAAG,qBAAqB;EAE5C,IAAIC,MAAM,GAAG;IACXC,KAAK,EAAEL,SAAS;IAChBM,SAAS,EAAE;EACb,CAAC;EACD,IAAIC,MAAM,GAAG;IACXC,SAAS,EAAE,QAAQ;IAAEH,KAAK,EAAEF,gBAAgB;IAC5CG,SAAS,EAAE;EACb,CAAC;EACD,IAAIG,MAAM,GAAGX,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACa,iBAAiB,EAAE;IAACC,OAAO,EAAE;EAAI,CAAC,CAAC;EAClE,IAAIC,OAAO,GAAGf,IAAI,CAACe,OAAO,CACxB,GAAG,EACH,GAAG,EACH;IACEP,SAAS,EAAE;EACb,CACF,CAAC;EACD,IAAIQ,OAAO,GAAG;IACZN,SAAS,EAAE,SAAS;IACpBH,KAAK,EAAE;EACT,CAAC;EACD,IAAIU,UAAU,GAAG;IACfV,KAAK,EAAE,UAAU;IAAEW,GAAG,EAAE;EAC1B,CAAC;EACD,IAAIC,IAAI,GAAG;IACTT,SAAS,EAAE,SAAS;IACpBH,KAAK,EAAE,KAAK,GAAGL;EACjB,CAAC;EACD,IAAIkB,QAAQ,GAAGpB,IAAI,CAACe,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC;EAC5C,IAAIM,GAAG,GAAG;IACRX,SAAS,EAAE,QAAQ;IACnBH,KAAK,EAAE,UAAU,GAAGL;EACtB,CAAC;EACD,IAAIoB,IAAI,GAAG;IACTf,KAAK,EAAE,KAAK;IAAEW,GAAG,EAAE;EACrB,CAAC;EACD,IAAIK,IAAI,GAAG;IACTC,cAAc,EAAE,IAAI;IACpBhB,SAAS,EAAE;EACb,CAAC;EACD,IAAIiB,IAAI,GAAG;IACTf,SAAS,EAAE,MAAM;IACjBF,SAAS,EAAE,CAAC;IACZL,QAAQ,EAAEA,QAAQ;IAClBI,KAAK,EAAEL,SAAS;IAChBwB,MAAM,EAAEH;EACV,CAAC;EACD,IAAII,gBAAgB,GAAG,CAACL,IAAI,EAAEX,MAAM,EAAEQ,IAAI,EAAEC,QAAQ,EAAEL,OAAO,EAAEM,GAAG,EAAEJ,UAAU,EAAER,MAAM,EAAEO,OAAO,EAAEV,MAAM,CAAC;EAExGgB,IAAI,CAACM,QAAQ,GAAG,CAAC5B,IAAI,CAACe,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAEU,IAAI,EAAEF,IAAI,CAAC;EACzDA,IAAI,CAACK,QAAQ,GAAGD,gBAAgB;EAChCV,UAAU,CAACW,QAAQ,GAAGD,gBAAgB;EAEtC,OAAO;IACLE,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnBhB,OAAO,EAAE,IAAI;IACbc,QAAQ,EAAE,CAAC5B,IAAI,CAAC+B,OAAO,CAAC,CAAC,EAAET,IAAI,EAAEX,MAAM,EAAEQ,IAAI,EAAEC,QAAQ,EAAEL,OAAO,EAAEM,GAAG,EAAEJ,UAAU,EAAER,MAAM,EAAEO,OAAO;EACpG,CAAC;AACH;AAEAgB,MAAM,CAACC,OAAO,GAAGlC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}