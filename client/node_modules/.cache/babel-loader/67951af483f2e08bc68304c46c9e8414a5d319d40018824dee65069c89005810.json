{"ast": null, "code": "/*\nLanguage: Golo\nAuthor: <PERSON> <<EMAIL>>\nDescription: a lightweight dynamic language for the JVM\nWebsite: http://golo-lang.org/\n*/\n\nfunction golo(hljs) {\n  return {\n    name: 'Golo',\n    keywords: {\n      keyword: 'println readln print import module function local return let var ' + 'while for foreach times in case when match with break continue ' + 'augment augmentation each find filter reduce ' + 'if then else otherwise try catch finally raise throw orIfNull ' + 'DynamicObject|10 DynamicVariable struct Observable map set vector list array',\n      literal: 'true false null'\n    },\n    contains: [hljs.HASH_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE, {\n      className: 'meta',\n      begin: '@[A-Za-z]+'\n    }]\n  };\n}\nmodule.exports = golo;", "map": {"version": 3, "names": ["golo", "hljs", "name", "keywords", "keyword", "literal", "contains", "HASH_COMMENT_MODE", "QUOTE_STRING_MODE", "C_NUMBER_MODE", "className", "begin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/golo.js"], "sourcesContent": ["/*\nLanguage: Golo\nAuthor: <PERSON> <<EMAIL>>\nDescription: a lightweight dynamic language for the JVM\nWebsite: http://golo-lang.org/\n*/\n\nfunction golo(hljs) {\n  return {\n    name: 'Golo',\n    keywords: {\n      keyword:\n          'println readln print import module function local return let var ' +\n          'while for foreach times in case when match with break continue ' +\n          'augment augmentation each find filter reduce ' +\n          'if then else otherwise try catch finally raise throw orIfNull ' +\n          'DynamicObject|10 DynamicVariable struct Observable map set vector list array',\n      literal:\n          'true false null'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '@[A-Za-z]+'\n      }\n    ]\n  };\n}\n\nmodule.exports = golo;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;MACRC,OAAO,EACH,mEAAmE,GACnE,iEAAiE,GACjE,+CAA+C,GAC/C,gEAAgE,GAChE,8EAA8E;MAClFC,OAAO,EACH;IACN,CAAC;IACDC,QAAQ,EAAE,CACRL,IAAI,CAACM,iBAAiB,EACtBN,IAAI,CAACO,iBAAiB,EACtBP,IAAI,CAACQ,aAAa,EAClB;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGb,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}