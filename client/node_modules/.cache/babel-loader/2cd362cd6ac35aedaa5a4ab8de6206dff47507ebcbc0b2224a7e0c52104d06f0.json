{"ast": null, "code": "'use strict';\n\nmodule.exports = actionscript;\nactionscript.displayName = 'actionscript';\nactionscript.aliases = [];\nfunction actionscript(Prism) {\n  Prism.languages.actionscript = Prism.languages.extend('javascript', {\n    keyword: /\\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\\b/,\n    operator: /\\+\\+|--|(?:[+\\-*\\/%^]|&&?|\\|\\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/\n  });\n  Prism.languages.actionscript['class-name'].alias = 'function'; // doesn't work with AS because AS is too complex\n  delete Prism.languages.actionscript['parameter'];\n  delete Prism.languages.actionscript['literal-property'];\n  if (Prism.languages.markup) {\n    Prism.languages.insertBefore('actionscript', 'string', {\n      xml: {\n        pattern: /(^|[^.])<\\/?\\w+(?:\\s+[^\\s>\\/=]+=(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2)*\\s*\\/?>/,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["module", "exports", "actionscript", "displayName", "aliases", "Prism", "languages", "extend", "keyword", "operator", "alias", "markup", "insertBefore", "xml", "pattern", "lookbehind", "inside"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/actionscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = actionscript\nactionscript.displayName = 'actionscript'\nactionscript.aliases = []\nfunction actionscript(Prism) {\n  Prism.languages.actionscript = Prism.languages.extend('javascript', {\n    keyword:\n      /\\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\\b/,\n    operator: /\\+\\+|--|(?:[+\\-*\\/%^]|&&?|\\|\\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/\n  })\n  Prism.languages.actionscript['class-name'].alias = 'function' // doesn't work with AS because AS is too complex\n  delete Prism.languages.actionscript['parameter']\n  delete Prism.languages.actionscript['literal-property']\n  if (Prism.languages.markup) {\n    Prism.languages.insertBefore('actionscript', 'string', {\n      xml: {\n        pattern:\n          /(^|[^.])<\\/?\\w+(?:\\s+[^\\s>\\/=]+=(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2)*\\s*\\/?>/,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      }\n    })\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3BA,KAAK,CAACC,SAAS,CAACJ,YAAY,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE;IAClEC,OAAO,EACL,sUAAsU;IACxUC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFJ,KAAK,CAACC,SAAS,CAACJ,YAAY,CAAC,YAAY,CAAC,CAACQ,KAAK,GAAG,UAAU,EAAC;EAC9D,OAAOL,KAAK,CAACC,SAAS,CAACJ,YAAY,CAAC,WAAW,CAAC;EAChD,OAAOG,KAAK,CAACC,SAAS,CAACJ,YAAY,CAAC,kBAAkB,CAAC;EACvD,IAAIG,KAAK,CAACC,SAAS,CAACK,MAAM,EAAE;IAC1BN,KAAK,CAACC,SAAS,CAACM,YAAY,CAAC,cAAc,EAAE,QAAQ,EAAE;MACrDC,GAAG,EAAE;QACHC,OAAO,EACL,2EAA2E;QAC7EC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEX,KAAK,CAACC,SAAS,CAACK;MAC1B;IACF,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}