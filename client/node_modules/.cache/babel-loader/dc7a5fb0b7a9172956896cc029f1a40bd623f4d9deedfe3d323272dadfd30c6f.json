{"ast": null, "code": "/*\nLanguage: Haskell\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.haskell.org\nCategory: functional\n*/\n\nfunction haskell(hljs) {\n  const COMMENT = {\n    variants: [hljs.COMMENT('--', '$'), hljs.COMMENT(/\\{-/, /-\\}/, {\n      contains: ['self']\n    })]\n  };\n  const PRAGMA = {\n    className: 'meta',\n    begin: /\\{-#/,\n    end: /#-\\}/\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '^#',\n    end: '$'\n  };\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*',\n    // TODO: other constructors (build-in, infix).\n    relevance: 0\n  };\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [PRAGMA, PREPROCESSOR, {\n      className: 'type',\n      begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n    }, hljs.inherit(hljs.TITLE_MODE, {\n      begin: '[_a-z][\\\\w\\']*'\n    }), COMMENT]\n  };\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n  return {\n    name: 'Haskell',\n    aliases: ['hs'],\n    keywords: 'let in if then else case of where do module import hiding ' + 'qualified type data newtype deriving class instance as default ' + 'infix infixl infixr foreign export ccall stdcall cplusplus ' + 'jvm dotnet safe unsafe family forall mdo proc rec',\n    contains: [\n    // Top-level constructions.\n    {\n      beginKeywords: 'module',\n      end: 'where',\n      keywords: 'module where',\n      contains: [LIST, COMMENT],\n      illegal: '\\\\W\\\\.|;'\n    }, {\n      begin: '\\\\bimport\\\\b',\n      end: '$',\n      keywords: 'import qualified as hiding',\n      contains: [LIST, COMMENT],\n      illegal: '\\\\W\\\\.|;'\n    }, {\n      className: 'class',\n      begin: '^(\\\\s*)?(class|instance)\\\\b',\n      end: 'where',\n      keywords: 'class family instance where',\n      contains: [CONSTRUCTOR, LIST, COMMENT]\n    }, {\n      className: 'class',\n      begin: '\\\\b(data|(new)?type)\\\\b',\n      end: '$',\n      keywords: 'data family type newtype deriving',\n      contains: [PRAGMA, CONSTRUCTOR, LIST, RECORD, COMMENT]\n    }, {\n      beginKeywords: 'default',\n      end: '$',\n      contains: [CONSTRUCTOR, LIST, COMMENT]\n    }, {\n      beginKeywords: 'infix infixl infixr',\n      end: '$',\n      contains: [hljs.C_NUMBER_MODE, COMMENT]\n    }, {\n      begin: '\\\\bforeign\\\\b',\n      end: '$',\n      keywords: 'foreign import export ccall stdcall cplusplus jvm ' + 'dotnet safe unsafe',\n      contains: [CONSTRUCTOR, hljs.QUOTE_STRING_MODE, COMMENT]\n    }, {\n      className: 'meta',\n      begin: '#!\\\\/usr\\\\/bin\\\\/env\\ runhaskell',\n      end: '$'\n    },\n    // \"Whitespaces\".\n    PRAGMA, PREPROCESSOR,\n    // Literals and names.\n\n    // TODO: characters.\n    hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE, CONSTRUCTOR, hljs.inherit(hljs.TITLE_MODE, {\n      begin: '^[_a-z][\\\\w\\']*'\n    }), COMMENT, {\n      // No markup, relevance booster\n      begin: '->|<-'\n    }]\n  };\n}\nmodule.exports = haskell;", "map": {"version": 3, "names": ["haskell", "hljs", "COMMENT", "variants", "contains", "PRAGMA", "className", "begin", "end", "PREPROCESSOR", "CONSTRUCTOR", "relevance", "LIST", "illegal", "inherit", "TITLE_MODE", "RECORD", "name", "aliases", "keywords", "beginKeywords", "C_NUMBER_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/haskell.js"], "sourcesContent": ["/*\nLanguage: Haskell\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.haskell.org\nCategory: functional\n*/\n\nfunction haskell(hljs) {\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT('--', '$'),\n      hljs.COMMENT(\n        /\\{-/,\n        /-\\}/,\n        {\n          contains: ['self']\n        }\n      )\n    ]\n  };\n\n  const PRAGMA = {\n    className: 'meta',\n    begin: /\\{-#/,\n    end: /#-\\}/\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '^#',\n    end: '$'\n  };\n\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*', // TODO: other constructors (build-in, infix).\n    relevance: 0\n  };\n\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [\n      PRAGMA,\n      PREPROCESSOR,\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n      },\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '[_a-z][\\\\w\\']*'\n      }),\n      COMMENT\n    ]\n  };\n\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n\n  return {\n    name: 'Haskell',\n    aliases: ['hs'],\n    keywords:\n      'let in if then else case of where do module import hiding ' +\n      'qualified type data newtype deriving class instance as default ' +\n      'infix infixl infixr foreign export ccall stdcall cplusplus ' +\n      'jvm dotnet safe unsafe family forall mdo proc rec',\n    contains: [\n      // Top-level constructions.\n      {\n        beginKeywords: 'module',\n        end: 'where',\n        keywords: 'module where',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: '\\\\bimport\\\\b',\n        end: '$',\n        keywords: 'import qualified as hiding',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        className: 'class',\n        begin: '^(\\\\s*)?(class|instance)\\\\b',\n        end: 'where',\n        keywords: 'class family instance where',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          COMMENT\n        ]\n      },\n      {\n        className: 'class',\n        begin: '\\\\b(data|(new)?type)\\\\b',\n        end: '$',\n        keywords: 'data family type newtype deriving',\n        contains: [\n          PRAGMA,\n          CONSTRUCTOR,\n          LIST,\n          RECORD,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'default',\n        end: '$',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'infix infixl infixr',\n        end: '$',\n        contains: [\n          hljs.C_NUMBER_MODE,\n          COMMENT\n        ]\n      },\n      {\n        begin: '\\\\bforeign\\\\b',\n        end: '$',\n        keywords: 'foreign import export ccall stdcall cplusplus jvm ' +\n                  'dotnet safe unsafe',\n        contains: [\n          CONSTRUCTOR,\n          hljs.QUOTE_STRING_MODE,\n          COMMENT\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '#!\\\\/usr\\\\/bin\\\\/env\\ runhaskell',\n        end: '$'\n      },\n      // \"Whitespaces\".\n      PRAGMA,\n      PREPROCESSOR,\n\n      // Literals and names.\n\n      // TODO: characters.\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      CONSTRUCTOR,\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '^[_a-z][\\\\w\\']*'\n      }),\n      COMMENT,\n      { // No markup, relevance booster\n        begin: '->|<-'\n      }\n    ]\n  };\n}\n\nmodule.exports = haskell;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,OAAO,GAAG;IACdC,QAAQ,EAAE,CACRF,IAAI,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EACvBD,IAAI,CAACC,OAAO,CACV,KAAK,EACL,KAAK,EACL;MACEE,QAAQ,EAAE,CAAC,MAAM;IACnB,CACF,CAAC;EAEL,CAAC;EAED,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBH,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE;EACP,CAAC;EAED,MAAME,WAAW,GAAG;IAClBJ,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,kBAAkB;IAAE;IAC3BI,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,KAAK;IACVK,OAAO,EAAE,GAAG;IACZT,QAAQ,EAAE,CACRC,MAAM,EACNI,YAAY,EACZ;MACEH,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC,EACDN,IAAI,CAACa,OAAO,CAACb,IAAI,CAACc,UAAU,EAAE;MAC5BR,KAAK,EAAE;IACT,CAAC,CAAC,EACFL,OAAO;EAEX,CAAC;EAED,MAAMc,MAAM,GAAG;IACbT,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTJ,QAAQ,EAAEQ,IAAI,CAACR;EACjB,CAAC;EAED,OAAO;IACLa,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,CAAC,IAAI,CAAC;IACfC,QAAQ,EACN,4DAA4D,GAC5D,iEAAiE,GACjE,6DAA6D,GAC7D,mDAAmD;IACrDf,QAAQ,EAAE;IACR;IACA;MACEgB,aAAa,EAAE,QAAQ;MACvBZ,GAAG,EAAE,OAAO;MACZW,QAAQ,EAAE,cAAc;MACxBf,QAAQ,EAAE,CACRQ,IAAI,EACJV,OAAO,CACR;MACDW,OAAO,EAAE;IACX,CAAC,EACD;MACEN,KAAK,EAAE,cAAc;MACrBC,GAAG,EAAE,GAAG;MACRW,QAAQ,EAAE,4BAA4B;MACtCf,QAAQ,EAAE,CACRQ,IAAI,EACJV,OAAO,CACR;MACDW,OAAO,EAAE;IACX,CAAC,EACD;MACEP,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,6BAA6B;MACpCC,GAAG,EAAE,OAAO;MACZW,QAAQ,EAAE,6BAA6B;MACvCf,QAAQ,EAAE,CACRM,WAAW,EACXE,IAAI,EACJV,OAAO;IAEX,CAAC,EACD;MACEI,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,yBAAyB;MAChCC,GAAG,EAAE,GAAG;MACRW,QAAQ,EAAE,mCAAmC;MAC7Cf,QAAQ,EAAE,CACRC,MAAM,EACNK,WAAW,EACXE,IAAI,EACJI,MAAM,EACNd,OAAO;IAEX,CAAC,EACD;MACEkB,aAAa,EAAE,SAAS;MACxBZ,GAAG,EAAE,GAAG;MACRJ,QAAQ,EAAE,CACRM,WAAW,EACXE,IAAI,EACJV,OAAO;IAEX,CAAC,EACD;MACEkB,aAAa,EAAE,qBAAqB;MACpCZ,GAAG,EAAE,GAAG;MACRJ,QAAQ,EAAE,CACRH,IAAI,CAACoB,aAAa,EAClBnB,OAAO;IAEX,CAAC,EACD;MACEK,KAAK,EAAE,eAAe;MACtBC,GAAG,EAAE,GAAG;MACRW,QAAQ,EAAE,oDAAoD,GACpD,oBAAoB;MAC9Bf,QAAQ,EAAE,CACRM,WAAW,EACXT,IAAI,CAACqB,iBAAiB,EACtBpB,OAAO;IAEX,CAAC,EACD;MACEI,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,kCAAkC;MACzCC,GAAG,EAAE;IACP,CAAC;IACD;IACAH,MAAM,EACNI,YAAY;IAEZ;;IAEA;IACAR,IAAI,CAACqB,iBAAiB,EACtBrB,IAAI,CAACoB,aAAa,EAClBX,WAAW,EACXT,IAAI,CAACa,OAAO,CAACb,IAAI,CAACc,UAAU,EAAE;MAC5BR,KAAK,EAAE;IACT,CAAC,CAAC,EACFL,OAAO,EACP;MAAE;MACAK,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAgB,MAAM,CAACC,OAAO,GAAGxB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}