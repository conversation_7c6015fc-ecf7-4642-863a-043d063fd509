{"ast": null, "code": "/*\nLanguage: Objective-C\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://developer.apple.com/documentation/objectivec\nCategory: common\n*/\n\nfunction objectivec(hljs) {\n  const API_CLASS = {\n    className: 'built_in',\n    begin: '\\\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\\\w+'\n  };\n  const IDENTIFIER_RE = /[a-zA-Z@][a-zA-Z0-9_]*/;\n  const OBJC_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword: 'int float while char export sizeof typedef const struct for union ' + 'unsigned long volatile static bool mutable if do return goto void ' + 'enum else break extern asm case short default double register explicit ' + 'signed typename this switch continue wchar_t inline readonly assign ' + 'readwrite self @synchronized id typeof ' + 'nonatomic super unichar IBOutlet IBAction strong weak copy ' + 'in out inout bycopy byref oneway __strong __weak __block __autoreleasing ' + '@private @protected @public @try @property @end @throw @catch @finally ' + '@autoreleasepool @synthesize @dynamic @selector @optional @required ' + '@encode @package @import @defs @compatibility_alias ' + '__bridge __bridge_transfer __bridge_retained __bridge_retain ' + '__covariant __contravariant __kindof ' + '_Nonnull _Nullable _Null_unspecified ' + '__FUNCTION__ __PRETTY_FUNCTION__ __attribute__ ' + 'getter setter retain unsafe_unretained ' + 'nonnull nullable null_unspecified null_resettable class instancetype ' + 'NS_DESIGNATED_INITIALIZER NS_UNAVAILABLE NS_REQUIRES_SUPER ' + 'NS_RETURNS_INNER_POINTER NS_INLINE NS_AVAILABLE NS_DEPRECATED ' + 'NS_ENUM NS_OPTIONS NS_SWIFT_UNAVAILABLE ' + 'NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END ' + 'NS_REFINED_FOR_SWIFT NS_SWIFT_NAME NS_SWIFT_NOTHROW ' + 'NS_DURING NS_HANDLER NS_ENDHANDLER NS_VALUERETURN NS_VOIDRETURN',\n    literal: 'false true FALSE TRUE nil YES NO NULL',\n    built_in: 'BOOL dispatch_once_t dispatch_queue_t dispatch_sync dispatch_async dispatch_once'\n  };\n  const CLASS_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword: '@interface @class @protocol @implementation'\n  };\n  return {\n    name: 'Objective-C',\n    aliases: ['mm', 'objc', 'obj-c', 'obj-c++', 'objective-c++'],\n    keywords: OBJC_KEYWORDS,\n    illegal: '</',\n    contains: [API_CLASS, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.C_NUMBER_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, {\n      className: 'string',\n      variants: [{\n        begin: '@\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [hljs.BACKSLASH_ESCAPE]\n      }]\n    }, {\n      className: 'meta',\n      begin: /#\\s*[a-z]+\\b/,\n      end: /$/,\n      keywords: {\n        'meta-keyword': 'if else elif endif define undef warning error line ' + 'pragma ifdef ifndef include'\n      },\n      contains: [{\n        begin: /\\\\\\n/,\n        relevance: 0\n      }, hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        className: 'meta-string'\n      }), {\n        className: 'meta-string',\n        begin: /<.*?>/,\n        end: /$/,\n        illegal: '\\\\n'\n      }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      className: 'class',\n      begin: '(' + CLASS_KEYWORDS.keyword.split(' ').join('|') + ')\\\\b',\n      end: /(\\{|$)/,\n      excludeEnd: true,\n      keywords: CLASS_KEYWORDS,\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      begin: '\\\\.' + hljs.UNDERSCORE_IDENT_RE,\n      relevance: 0\n    }]\n  };\n}\nmodule.exports = objectivec;", "map": {"version": 3, "names": ["objectivec", "hljs", "API_CLASS", "className", "begin", "IDENTIFIER_RE", "OBJC_KEYWORDS", "$pattern", "keyword", "literal", "built_in", "CLASS_KEYWORDS", "name", "aliases", "keywords", "illegal", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "C_NUMBER_MODE", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "variants", "end", "BACKSLASH_ESCAPE", "relevance", "inherit", "split", "join", "excludeEnd", "UNDERSCORE_TITLE_MODE", "UNDERSCORE_IDENT_RE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/objectivec.js"], "sourcesContent": ["/*\nLanguage: Objective-C\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://developer.apple.com/documentation/objectivec\nCategory: common\n*/\n\nfunction objectivec(hljs) {\n  const API_CLASS = {\n    className: 'built_in',\n    begin: '\\\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\\\w+'\n  };\n  const IDENTIFIER_RE = /[a-zA-Z@][a-zA-Z0-9_]*/;\n  const OBJC_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword:\n      'int float while char export sizeof typedef const struct for union ' +\n      'unsigned long volatile static bool mutable if do return goto void ' +\n      'enum else break extern asm case short default double register explicit ' +\n      'signed typename this switch continue wchar_t inline readonly assign ' +\n      'readwrite self @synchronized id typeof ' +\n      'nonatomic super unichar IBOutlet IBAction strong weak copy ' +\n      'in out inout bycopy byref oneway __strong __weak __block __autoreleasing ' +\n      '@private @protected @public @try @property @end @throw @catch @finally ' +\n      '@autoreleasepool @synthesize @dynamic @selector @optional @required ' +\n      '@encode @package @import @defs @compatibility_alias ' +\n      '__bridge __bridge_transfer __bridge_retained __bridge_retain ' +\n      '__covariant __contravariant __kindof ' +\n      '_Nonnull _Nullable _Null_unspecified ' +\n      '__FUNCTION__ __PRETTY_FUNCTION__ __attribute__ ' +\n      'getter setter retain unsafe_unretained ' +\n      'nonnull nullable null_unspecified null_resettable class instancetype ' +\n      'NS_DESIGNATED_INITIALIZER NS_UNAVAILABLE NS_REQUIRES_SUPER ' +\n      'NS_RETURNS_INNER_POINTER NS_INLINE NS_AVAILABLE NS_DEPRECATED ' +\n      'NS_ENUM NS_OPTIONS NS_SWIFT_UNAVAILABLE ' +\n      'NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END ' +\n      'NS_REFINED_FOR_SWIFT NS_SWIFT_NAME NS_SWIFT_NOTHROW ' +\n      'NS_DURING NS_HANDLER NS_ENDHANDLER NS_VALUERETURN NS_VOIDRETURN',\n    literal:\n      'false true FALSE TRUE nil YES NO NULL',\n    built_in:\n      'BOOL dispatch_once_t dispatch_queue_t dispatch_sync dispatch_async dispatch_once'\n  };\n  const CLASS_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword: '@interface @class @protocol @implementation'\n  };\n  return {\n    name: 'Objective-C',\n    aliases: [\n      'mm',\n      'objc',\n      'obj-c',\n      'obj-c++',\n      'objective-c++'\n    ],\n    keywords: OBJC_KEYWORDS,\n    illegal: '</',\n    contains: [\n      API_CLASS,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      {\n        className: 'string',\n        variants: [\n          {\n            begin: '@\"',\n            end: '\"',\n            illegal: '\\\\n',\n            contains: [ hljs.BACKSLASH_ESCAPE ]\n          }\n        ]\n      },\n      {\n        className: 'meta',\n        begin: /#\\s*[a-z]+\\b/,\n        end: /$/,\n        keywords: {\n          'meta-keyword':\n            'if else elif endif define undef warning error line ' +\n            'pragma ifdef ifndef include'\n        },\n        contains: [\n          {\n            begin: /\\\\\\n/,\n            relevance: 0\n          },\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {\n            className: 'meta-string'\n          }),\n          {\n            className: 'meta-string',\n            begin: /<.*?>/,\n            end: /$/,\n            illegal: '\\\\n'\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        className: 'class',\n        begin: '(' + CLASS_KEYWORDS.keyword.split(' ').join('|') + ')\\\\b',\n        end: /(\\{|$)/,\n        excludeEnd: true,\n        keywords: CLASS_KEYWORDS,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        begin: '\\\\.' + hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = objectivec;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,MAAMC,SAAS,GAAG;IAChBC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,aAAa,GAAG,wBAAwB;EAC9C,MAAMC,aAAa,GAAG;IACpBC,QAAQ,EAAEF,aAAa;IACvBG,OAAO,EACL,oEAAoE,GACpE,oEAAoE,GACpE,yEAAyE,GACzE,sEAAsE,GACtE,yCAAyC,GACzC,6DAA6D,GAC7D,2EAA2E,GAC3E,yEAAyE,GACzE,sEAAsE,GACtE,sDAAsD,GACtD,+DAA+D,GAC/D,uCAAuC,GACvC,uCAAuC,GACvC,iDAAiD,GACjD,yCAAyC,GACzC,uEAAuE,GACvE,6DAA6D,GAC7D,gEAAgE,GAChE,0CAA0C,GAC1C,gDAAgD,GAChD,sDAAsD,GACtD,iEAAiE;IACnEC,OAAO,EACL,uCAAuC;IACzCC,QAAQ,EACN;EACJ,CAAC;EACD,MAAMC,cAAc,GAAG;IACrBJ,QAAQ,EAAEF,aAAa;IACvBG,OAAO,EAAE;EACX,CAAC;EACD,OAAO;IACLI,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,CACP,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,eAAe,CAChB;IACDC,QAAQ,EAAER,aAAa;IACvBS,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRd,SAAS,EACTD,IAAI,CAACgB,mBAAmB,EACxBhB,IAAI,CAACiB,oBAAoB,EACzBjB,IAAI,CAACkB,aAAa,EAClBlB,IAAI,CAACmB,iBAAiB,EACtBnB,IAAI,CAACoB,gBAAgB,EACrB;MACElB,SAAS,EAAE,QAAQ;MACnBmB,QAAQ,EAAE,CACR;QACElB,KAAK,EAAE,IAAI;QACXmB,GAAG,EAAE,GAAG;QACRR,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,CAAEf,IAAI,CAACuB,gBAAgB;MACnC,CAAC;IAEL,CAAC,EACD;MACErB,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,cAAc;MACrBmB,GAAG,EAAE,GAAG;MACRT,QAAQ,EAAE;QACR,cAAc,EACZ,qDAAqD,GACrD;MACJ,CAAC;MACDE,QAAQ,EAAE,CACR;QACEZ,KAAK,EAAE,MAAM;QACbqB,SAAS,EAAE;MACb,CAAC,EACDxB,IAAI,CAACyB,OAAO,CAACzB,IAAI,CAACmB,iBAAiB,EAAE;QACnCjB,SAAS,EAAE;MACb,CAAC,CAAC,EACF;QACEA,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,OAAO;QACdmB,GAAG,EAAE,GAAG;QACRR,OAAO,EAAE;MACX,CAAC,EACDd,IAAI,CAACgB,mBAAmB,EACxBhB,IAAI,CAACiB,oBAAoB;IAE7B,CAAC,EACD;MACEf,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,GAAG,GAAGO,cAAc,CAACH,OAAO,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;MACjEL,GAAG,EAAE,QAAQ;MACbM,UAAU,EAAE,IAAI;MAChBf,QAAQ,EAAEH,cAAc;MACxBK,QAAQ,EAAE,CAAEf,IAAI,CAAC6B,qBAAqB;IACxC,CAAC,EACD;MACE1B,KAAK,EAAE,KAAK,GAAGH,IAAI,CAAC8B,mBAAmB;MACvCN,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAO,MAAM,CAACC,OAAO,GAAGjC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}