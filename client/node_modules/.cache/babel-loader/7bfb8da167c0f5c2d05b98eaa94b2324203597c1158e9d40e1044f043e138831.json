{"ast": null, "code": "'use strict';\n\nmodule.exports = cssExtras;\ncssExtras.displayName = 'cssExtras';\ncssExtras.aliases = [];\nfunction cssExtras(Prism) {\n  ;\n  (function (Prism) {\n    var string = /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/;\n    var selectorInside;\n    Prism.languages.css.selector = {\n      pattern: Prism.languages.css.selector.pattern,\n      lookbehind: true,\n      inside: selectorInside = {\n        'pseudo-element': /:(?:after|before|first-letter|first-line|selection)|::[-\\w]+/,\n        'pseudo-class': /:[-\\w]+/,\n        class: /\\.[-\\w]+/,\n        id: /#[-\\w]+/,\n        attribute: {\n          pattern: RegExp('\\\\[(?:[^[\\\\]\"\\']|' + string.source + ')*\\\\]'),\n          greedy: true,\n          inside: {\n            punctuation: /^\\[|\\]$/,\n            'case-sensitivity': {\n              pattern: /(\\s)[si]$/i,\n              lookbehind: true,\n              alias: 'keyword'\n            },\n            namespace: {\n              pattern: /^(\\s*)(?:(?!\\s)[-*\\w\\xA0-\\uFFFF])*\\|(?!=)/,\n              lookbehind: true,\n              inside: {\n                punctuation: /\\|$/\n              }\n            },\n            'attr-name': {\n              pattern: /^(\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+/,\n              lookbehind: true\n            },\n            'attr-value': [string, {\n              pattern: /(=\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+(?=\\s*$)/,\n              lookbehind: true\n            }],\n            operator: /[|~*^$]?=/\n          }\n        },\n        'n-th': [{\n          pattern: /(\\(\\s*)[+-]?\\d*[\\dn](?:\\s*[+-]\\s*\\d+)?(?=\\s*\\))/,\n          lookbehind: true,\n          inside: {\n            number: /[\\dn]+/,\n            operator: /[+-]/\n          }\n        }, {\n          pattern: /(\\(\\s*)(?:even|odd)(?=\\s*\\))/i,\n          lookbehind: true\n        }],\n        combinator: />|\\+|~|\\|\\|/,\n        // the `tag` token has been existed and removed.\n        // because we can't find a perfect tokenize to match it.\n        // if you want to add it, please read https://github.com/PrismJS/prism/pull/2373 first.\n        punctuation: /[(),]/\n      }\n    };\n    Prism.languages.css['atrule'].inside['selector-function-argument'].inside = selectorInside;\n    Prism.languages.insertBefore('css', 'property', {\n      variable: {\n        pattern: /(^|[^-\\w\\xA0-\\uFFFF])--(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*/i,\n        lookbehind: true\n      }\n    });\n    var unit = {\n      pattern: /(\\b\\d+)(?:%|[a-z]+(?![\\w-]))/,\n      lookbehind: true\n    }; // 123 -123 .123 -.123 12.3 -12.3\n    var number = {\n      pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n      lookbehind: true\n    };\n    Prism.languages.insertBefore('css', 'function', {\n      operator: {\n        pattern: /(\\s)[+\\-*\\/](?=\\s)/,\n        lookbehind: true\n      },\n      // CAREFUL!\n      // Previewers and Inline color use hexcode and color.\n      hexcode: {\n        pattern: /\\B#[\\da-f]{3,8}\\b/i,\n        alias: 'color'\n      },\n      color: [{\n        pattern: /(^|[^\\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\\w-])/i,\n        lookbehind: true\n      }, {\n        pattern: /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n        inside: {\n          unit: unit,\n          number: number,\n          function: /[\\w-]+(?=\\()/,\n          punctuation: /[(),]/\n        }\n      }],\n      // it's important that there is no boundary assertion after the hex digits\n      entity: /\\\\[\\da-f]{1,8}/i,\n      unit: unit,\n      number: number\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "cssExtras", "displayName", "aliases", "Prism", "string", "selectorInside", "languages", "css", "selector", "pattern", "lookbehind", "inside", "class", "id", "attribute", "RegExp", "source", "greedy", "punctuation", "alias", "namespace", "operator", "number", "combinator", "insertBefore", "variable", "unit", "hexcode", "color", "function", "entity"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/css-extras.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = cssExtras\ncssExtras.displayName = 'cssExtras'\ncssExtras.aliases = []\nfunction cssExtras(Prism) {\n  ;(function (Prism) {\n    var string = /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/\n    var selectorInside\n    Prism.languages.css.selector = {\n      pattern: Prism.languages.css.selector.pattern,\n      lookbehind: true,\n      inside: (selectorInside = {\n        'pseudo-element':\n          /:(?:after|before|first-letter|first-line|selection)|::[-\\w]+/,\n        'pseudo-class': /:[-\\w]+/,\n        class: /\\.[-\\w]+/,\n        id: /#[-\\w]+/,\n        attribute: {\n          pattern: RegExp('\\\\[(?:[^[\\\\]\"\\']|' + string.source + ')*\\\\]'),\n          greedy: true,\n          inside: {\n            punctuation: /^\\[|\\]$/,\n            'case-sensitivity': {\n              pattern: /(\\s)[si]$/i,\n              lookbehind: true,\n              alias: 'keyword'\n            },\n            namespace: {\n              pattern: /^(\\s*)(?:(?!\\s)[-*\\w\\xA0-\\uFFFF])*\\|(?!=)/,\n              lookbehind: true,\n              inside: {\n                punctuation: /\\|$/\n              }\n            },\n            'attr-name': {\n              pattern: /^(\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+/,\n              lookbehind: true\n            },\n            'attr-value': [\n              string,\n              {\n                pattern: /(=\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+(?=\\s*$)/,\n                lookbehind: true\n              }\n            ],\n            operator: /[|~*^$]?=/\n          }\n        },\n        'n-th': [\n          {\n            pattern: /(\\(\\s*)[+-]?\\d*[\\dn](?:\\s*[+-]\\s*\\d+)?(?=\\s*\\))/,\n            lookbehind: true,\n            inside: {\n              number: /[\\dn]+/,\n              operator: /[+-]/\n            }\n          },\n          {\n            pattern: /(\\(\\s*)(?:even|odd)(?=\\s*\\))/i,\n            lookbehind: true\n          }\n        ],\n        combinator: />|\\+|~|\\|\\|/,\n        // the `tag` token has been existed and removed.\n        // because we can't find a perfect tokenize to match it.\n        // if you want to add it, please read https://github.com/PrismJS/prism/pull/2373 first.\n        punctuation: /[(),]/\n      })\n    }\n    Prism.languages.css['atrule'].inside['selector-function-argument'].inside =\n      selectorInside\n    Prism.languages.insertBefore('css', 'property', {\n      variable: {\n        pattern:\n          /(^|[^-\\w\\xA0-\\uFFFF])--(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*/i,\n        lookbehind: true\n      }\n    })\n    var unit = {\n      pattern: /(\\b\\d+)(?:%|[a-z]+(?![\\w-]))/,\n      lookbehind: true\n    } // 123 -123 .123 -.123 12.3 -12.3\n    var number = {\n      pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n      lookbehind: true\n    }\n    Prism.languages.insertBefore('css', 'function', {\n      operator: {\n        pattern: /(\\s)[+\\-*\\/](?=\\s)/,\n        lookbehind: true\n      },\n      // CAREFUL!\n      // Previewers and Inline color use hexcode and color.\n      hexcode: {\n        pattern: /\\B#[\\da-f]{3,8}\\b/i,\n        alias: 'color'\n      },\n      color: [\n        {\n          pattern:\n            /(^|[^\\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\\w-])/i,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n          inside: {\n            unit: unit,\n            number: number,\n            function: /[\\w-]+(?=\\()/,\n            punctuation: /[(),]/\n          }\n        }\n      ],\n      // it's important that there is no boundary assertion after the hex digits\n      entity: /\\\\[\\da-f]{1,8}/i,\n      unit: unit,\n      number: number\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,MAAM,GAAG,+CAA+C;IAC5D,IAAIC,cAAc;IAClBF,KAAK,CAACG,SAAS,CAACC,GAAG,CAACC,QAAQ,GAAG;MAC7BC,OAAO,EAAEN,KAAK,CAACG,SAAS,CAACC,GAAG,CAACC,QAAQ,CAACC,OAAO;MAC7CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAGN,cAAc,GAAG;QACxB,gBAAgB,EACd,8DAA8D;QAChE,cAAc,EAAE,SAAS;QACzBO,KAAK,EAAE,UAAU;QACjBC,EAAE,EAAE,SAAS;QACbC,SAAS,EAAE;UACTL,OAAO,EAAEM,MAAM,CAAC,mBAAmB,GAAGX,MAAM,CAACY,MAAM,GAAG,OAAO,CAAC;UAC9DC,MAAM,EAAE,IAAI;UACZN,MAAM,EAAE;YACNO,WAAW,EAAE,SAAS;YACtB,kBAAkB,EAAE;cAClBT,OAAO,EAAE,YAAY;cACrBC,UAAU,EAAE,IAAI;cAChBS,KAAK,EAAE;YACT,CAAC;YACDC,SAAS,EAAE;cACTX,OAAO,EAAE,2CAA2C;cACpDC,UAAU,EAAE,IAAI;cAChBC,MAAM,EAAE;gBACNO,WAAW,EAAE;cACf;YACF,CAAC;YACD,WAAW,EAAE;cACXT,OAAO,EAAE,mCAAmC;cAC5CC,UAAU,EAAE;YACd,CAAC;YACD,YAAY,EAAE,CACZN,MAAM,EACN;cACEK,OAAO,EAAE,2CAA2C;cACpDC,UAAU,EAAE;YACd,CAAC,CACF;YACDW,QAAQ,EAAE;UACZ;QACF,CAAC;QACD,MAAM,EAAE,CACN;UACEZ,OAAO,EAAE,iDAAiD;UAC1DC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNW,MAAM,EAAE,QAAQ;YAChBD,QAAQ,EAAE;UACZ;QACF,CAAC,EACD;UACEZ,OAAO,EAAE,+BAA+B;UACxCC,UAAU,EAAE;QACd,CAAC,CACF;QACDa,UAAU,EAAE,aAAa;QACzB;QACA;QACA;QACAL,WAAW,EAAE;MACf;IACF,CAAC;IACDf,KAAK,CAACG,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACI,MAAM,CAAC,4BAA4B,CAAC,CAACA,MAAM,GACvEN,cAAc;IAChBF,KAAK,CAACG,SAAS,CAACkB,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE;MAC9CC,QAAQ,EAAE;QACRhB,OAAO,EACL,6EAA6E;QAC/EC,UAAU,EAAE;MACd;IACF,CAAC,CAAC;IACF,IAAIgB,IAAI,GAAG;MACTjB,OAAO,EAAE,8BAA8B;MACvCC,UAAU,EAAE;IACd,CAAC,EAAC;IACF,IAAIY,MAAM,GAAG;MACXb,OAAO,EAAE,sCAAsC;MAC/CC,UAAU,EAAE;IACd,CAAC;IACDP,KAAK,CAACG,SAAS,CAACkB,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE;MAC9CH,QAAQ,EAAE;QACRZ,OAAO,EAAE,oBAAoB;QAC7BC,UAAU,EAAE;MACd,CAAC;MACD;MACA;MACAiB,OAAO,EAAE;QACPlB,OAAO,EAAE,oBAAoB;QAC7BU,KAAK,EAAE;MACT,CAAC;MACDS,KAAK,EAAE,CACL;QACEnB,OAAO,EACL,m6CAAm6C;QACr6CC,UAAU,EAAE;MACd,CAAC,EACD;QACED,OAAO,EACL,2JAA2J;QAC7JE,MAAM,EAAE;UACNe,IAAI,EAAEA,IAAI;UACVJ,MAAM,EAAEA,MAAM;UACdO,QAAQ,EAAE,cAAc;UACxBX,WAAW,EAAE;QACf;MACF,CAAC,CACF;MACD;MACAY,MAAM,EAAE,iBAAiB;MACzBJ,IAAI,EAAEA,IAAI;MACVJ,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC,EAAEnB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}