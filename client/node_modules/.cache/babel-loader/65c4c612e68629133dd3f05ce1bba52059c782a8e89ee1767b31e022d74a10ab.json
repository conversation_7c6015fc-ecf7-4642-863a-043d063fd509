{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\nconst keywordWrapper = keyword => concat(/\\b/, keyword, /\\w$/.test(keyword) ? /\\b/ : /\\B/);\n\n// Keywords that require a leading dot.\nconst dotKeywords = ['Protocol',\n// contextual\n'Type' // contextual\n].map(keywordWrapper);\n\n// Keywords that may have a leading dot.\nconst optionalDotKeywords = ['init', 'self'].map(keywordWrapper);\n\n// should register as keyword, not type\nconst keywordTypes = ['Any', 'Self'];\n\n// Regular keywords and literals.\nconst keywords = [\n// strings below will be fed into the regular `keywords` engine while regex\n// will result in additional modes being created to scan for those keywords to\n// avoid conflicts with other rules\n'associatedtype', 'async', 'await', /as\\?/,\n// operator\n/as!/,\n// operator\n'as',\n// operator\n'break', 'case', 'catch', 'class', 'continue', 'convenience',\n// contextual\n'default', 'defer', 'deinit', 'didSet',\n// contextual\n'do', 'dynamic',\n// contextual\n'else', 'enum', 'extension', 'fallthrough', /fileprivate\\(set\\)/, 'fileprivate', 'final',\n// contextual\n'for', 'func', 'get',\n// contextual\n'guard', 'if', 'import', 'indirect',\n// contextual\n'infix',\n// contextual\n/init\\?/, /init!/, 'inout', /internal\\(set\\)/, 'internal', 'in', 'is',\n// operator\n'lazy',\n// contextual\n'let', 'mutating',\n// contextual\n'nonmutating',\n// contextual\n/open\\(set\\)/,\n// contextual\n'open',\n// contextual\n'operator', 'optional',\n// contextual\n'override',\n// contextual\n'postfix',\n// contextual\n'precedencegroup', 'prefix',\n// contextual\n/private\\(set\\)/, 'private', 'protocol', /public\\(set\\)/, 'public', 'repeat', 'required',\n// contextual\n'rethrows', 'return', 'set',\n// contextual\n'some',\n// contextual\n'static', 'struct', 'subscript', 'super', 'switch', 'throws', 'throw', /try\\?/,\n// operator\n/try!/,\n// operator\n'try',\n// operator\n'typealias', /unowned\\(safe\\)/,\n// contextual\n/unowned\\(unsafe\\)/,\n// contextual\n'unowned',\n// contextual\n'var', 'weak',\n// contextual\n'where', 'while', 'willSet' // contextual\n];\n\n// NOTE: Contextual keywords are reserved only in specific contexts.\n// Ideally, these should be matched using modes to avoid false positives.\n\n// Literals.\nconst literals = ['false', 'nil', 'true'];\n\n// Keywords used in precedence groups.\nconst precedencegroupKeywords = ['assignment', 'associativity', 'higherThan', 'left', 'lowerThan', 'none', 'right'];\n\n// Keywords that start with a number sign (#).\n// #available is handled separately.\nconst numberSignKeywords = ['#colorLiteral', '#column', '#dsohandle', '#else', '#elseif', '#endif', '#error', '#file', '#fileID', '#fileLiteral', '#filePath', '#function', '#if', '#imageLiteral', '#keyPath', '#line', '#selector', '#sourceLocation', '#warn_unqualified_access', '#warning'];\n\n// Global functions in the Standard Library.\nconst builtIns = ['abs', 'all', 'any', 'assert', 'assertionFailure', 'debugPrint', 'dump', 'fatalError', 'getVaList', 'isKnownUniquelyReferenced', 'max', 'min', 'numericCast', 'pointwiseMax', 'pointwiseMin', 'precondition', 'preconditionFailure', 'print', 'readLine', 'repeatElement', 'sequence', 'stride', 'swap', 'swift_unboxFromSwiftValueWithType', 'transcode', 'type', 'unsafeBitCast', 'unsafeDowncast', 'withExtendedLifetime', 'withUnsafeMutablePointer', 'withUnsafePointer', 'withVaList', 'withoutActuallyEscaping', 'zip'];\n\n// Valid first characters for operators.\nconst operatorHead = either(/[/=\\-+!*%<>&|^~?]/, /[\\u00A1-\\u00A7]/, /[\\u00A9\\u00AB]/, /[\\u00AC\\u00AE]/, /[\\u00B0\\u00B1]/, /[\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7]/, /[\\u2016-\\u2017]/, /[\\u2020-\\u2027]/, /[\\u2030-\\u203E]/, /[\\u2041-\\u2053]/, /[\\u2055-\\u205E]/, /[\\u2190-\\u23FF]/, /[\\u2500-\\u2775]/, /[\\u2794-\\u2BFF]/, /[\\u2E00-\\u2E7F]/, /[\\u3001-\\u3003]/, /[\\u3008-\\u3020]/, /[\\u3030]/);\n\n// Valid characters for operators.\nconst operatorCharacter = either(operatorHead, /[\\u0300-\\u036F]/, /[\\u1DC0-\\u1DFF]/, /[\\u20D0-\\u20FF]/, /[\\uFE00-\\uFE0F]/, /[\\uFE20-\\uFE2F]/\n// TODO: The following characters are also allowed, but the regex isn't supported yet.\n// /[\\u{E0100}-\\u{E01EF}]/u\n);\n\n// Valid operator.\nconst operator = concat(operatorHead, operatorCharacter, '*');\n\n// Valid first characters for identifiers.\nconst identifierHead = either(/[a-zA-Z_]/, /[\\u00A8\\u00AA\\u00AD\\u00AF\\u00B2-\\u00B5\\u00B7-\\u00BA]/, /[\\u00BC-\\u00BE\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u00FF]/, /[\\u0100-\\u02FF\\u0370-\\u167F\\u1681-\\u180D\\u180F-\\u1DBF]/, /[\\u1E00-\\u1FFF]/, /[\\u200B-\\u200D\\u202A-\\u202E\\u203F-\\u2040\\u2054\\u2060-\\u206F]/, /[\\u2070-\\u20CF\\u2100-\\u218F\\u2460-\\u24FF\\u2776-\\u2793]/, /[\\u2C00-\\u2DFF\\u2E80-\\u2FFF]/, /[\\u3004-\\u3007\\u3021-\\u302F\\u3031-\\u303F\\u3040-\\uD7FF]/, /[\\uF900-\\uFD3D\\uFD40-\\uFDCF\\uFDF0-\\uFE1F\\uFE30-\\uFE44]/, /[\\uFE47-\\uFEFE\\uFF00-\\uFFFD]/ // Should be /[\\uFE47-\\uFFFD]/, but we have to exclude FEFF.\n// The following characters are also allowed, but the regexes aren't supported yet.\n// /[\\u{10000}-\\u{1FFFD}\\u{20000-\\u{2FFFD}\\u{30000}-\\u{3FFFD}\\u{40000}-\\u{4FFFD}]/u,\n// /[\\u{50000}-\\u{5FFFD}\\u{60000-\\u{6FFFD}\\u{70000}-\\u{7FFFD}\\u{80000}-\\u{8FFFD}]/u,\n// /[\\u{90000}-\\u{9FFFD}\\u{A0000-\\u{AFFFD}\\u{B0000}-\\u{BFFFD}\\u{C0000}-\\u{CFFFD}]/u,\n// /[\\u{D0000}-\\u{DFFFD}\\u{E0000-\\u{EFFFD}]/u\n);\n\n// Valid characters for identifiers.\nconst identifierCharacter = either(identifierHead, /\\d/, /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE20-\\uFE2F]/);\n\n// Valid identifier.\nconst identifier = concat(identifierHead, identifierCharacter, '*');\n\n// Valid type identifier.\nconst typeIdentifier = concat(/[A-Z]/, identifierCharacter, '*');\n\n// Built-in attributes, which are highlighted as keywords.\n// @available is handled separately.\nconst keywordAttributes = ['autoclosure', concat(/convention\\(/, either('swift', 'block', 'c'), /\\)/), 'discardableResult', 'dynamicCallable', 'dynamicMemberLookup', 'escaping', 'frozen', 'GKInspectable', 'IBAction', 'IBDesignable', 'IBInspectable', 'IBOutlet', 'IBSegueAction', 'inlinable', 'main', 'nonobjc', 'NSApplicationMain', 'NSCopying', 'NSManaged', concat(/objc\\(/, identifier, /\\)/), 'objc', 'objcMembers', 'propertyWrapper', 'requires_stored_property_inits', 'testable', 'UIApplicationMain', 'unknown', 'usableFromInline'];\n\n// Contextual keywords used in @available and #available.\nconst availabilityKeywords = ['iOS', 'iOSApplicationExtension', 'macOS', 'macOSApplicationExtension', 'macCatalyst', 'macCatalystApplicationExtension', 'watchOS', 'watchOSApplicationExtension', 'tvOS', 'tvOSApplicationExtension', 'swift'];\n\n/*\nLanguage: Swift\nDescription: Swift is a general-purpose programming language built using a modern approach to safety, performance, and software design patterns.\nAuthor: Steven Van Impe <<EMAIL>>\nContributors: Chris Eidhof <<EMAIL>>, Nate Cook <<EMAIL>>, Alexander Lichter <<EMAIL>>, Richard Gibson <gibson042@github>\nWebsite: https://swift.org\nCategory: common, system\n*/\n\n/** @type LanguageFn */\nfunction swift(hljs) {\n  const WHITESPACE = {\n    match: /\\s+/,\n    relevance: 0\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID411\n  const BLOCK_COMMENT = hljs.COMMENT('/\\\\*', '\\\\*/', {\n    contains: ['self']\n  });\n  const COMMENTS = [hljs.C_LINE_COMMENT_MODE, BLOCK_COMMENT];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID413\n  // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html\n  const DOT_KEYWORD = {\n    className: 'keyword',\n    begin: concat(/\\./, lookahead(either(...dotKeywords, ...optionalDotKeywords))),\n    end: either(...dotKeywords, ...optionalDotKeywords),\n    excludeBegin: true\n  };\n  const KEYWORD_GUARD = {\n    // Consume .keyword to prevent highlighting properties and methods as keywords.\n    match: concat(/\\./, either(...keywords)),\n    relevance: 0\n  };\n  const PLAIN_KEYWORDS = keywords.filter(kw => typeof kw === 'string').concat([\"_|0\"]); // seems common, so 0 relevance\n  const REGEX_KEYWORDS = keywords.filter(kw => typeof kw !== 'string') // find regex\n  .concat(keywordTypes).map(keywordWrapper);\n  const KEYWORD = {\n    variants: [{\n      className: 'keyword',\n      match: either(...REGEX_KEYWORDS, ...optionalDotKeywords)\n    }]\n  };\n  // find all the regular keywords\n  const KEYWORDS = {\n    $pattern: either(/\\b\\w+/,\n    // regular keywords\n    /#\\w+/ // number keywords\n    ),\n    keyword: PLAIN_KEYWORDS.concat(numberSignKeywords),\n    literal: literals\n  };\n  const KEYWORD_MODES = [DOT_KEYWORD, KEYWORD_GUARD, KEYWORD];\n\n  // https://github.com/apple/swift/tree/main/stdlib/public/core\n  const BUILT_IN_GUARD = {\n    // Consume .built_in to prevent highlighting properties and methods.\n    match: concat(/\\./, either(...builtIns)),\n    relevance: 0\n  };\n  const BUILT_IN = {\n    className: 'built_in',\n    match: concat(/\\b/, either(...builtIns), /(?=\\()/)\n  };\n  const BUILT_INS = [BUILT_IN_GUARD, BUILT_IN];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID418\n  const OPERATOR_GUARD = {\n    // Prevent -> from being highlighting as an operator.\n    match: /->/,\n    relevance: 0\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    variants: [{\n      match: operator\n    }, {\n      // dot-operator: only operators that start with a dot are allowed to use dots as\n      // characters (..., ...<, .*, etc). So there rule here is: a dot followed by one or more\n      // characters that may also include dots.\n      match: `\\\\.(\\\\.|${operatorCharacter})+`\n    }]\n  };\n  const OPERATORS = [OPERATOR_GUARD, OPERATOR];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_numeric-literal\n  // TODO: Update for leading `-` after lookbehind is supported everywhere\n  const decimalDigits = '([0-9]_*)+';\n  const hexDigits = '([0-9a-fA-F]_*)+';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n    // decimal floating-point-literal (subsumes decimal-literal)\n    {\n      match: `\\\\b(${decimalDigits})(\\\\.(${decimalDigits}))?` + `([eE][+-]?(${decimalDigits}))?\\\\b`\n    },\n    // hexadecimal floating-point-literal (subsumes hexadecimal-literal)\n    {\n      match: `\\\\b0x(${hexDigits})(\\\\.(${hexDigits}))?` + `([pP][+-]?(${decimalDigits}))?\\\\b`\n    },\n    // octal-literal\n    {\n      match: /\\b0o([0-7]_*)+\\b/\n    },\n    // binary-literal\n    {\n      match: /\\b0b([01]_*)+\\b/\n    }]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_string-literal\n  const ESCAPED_CHARACTER = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    variants: [{\n      match: concat(/\\\\/, rawDelimiter, /[0\\\\tnr\"']/)\n    }, {\n      match: concat(/\\\\/, rawDelimiter, /u\\{[0-9a-fA-F]{1,8}\\}/)\n    }]\n  });\n  const ESCAPED_NEWLINE = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    match: concat(/\\\\/, rawDelimiter, /[\\t ]*(?:[\\r\\n]|\\r\\n)/)\n  });\n  const INTERPOLATION = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    label: \"interpol\",\n    begin: concat(/\\\\/, rawDelimiter, /\\(/),\n    end: /\\)/\n  });\n  const MULTILINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"\"\"/),\n    end: concat(/\"\"\"/, rawDelimiter),\n    contains: [ESCAPED_CHARACTER(rawDelimiter), ESCAPED_NEWLINE(rawDelimiter), INTERPOLATION(rawDelimiter)]\n  });\n  const SINGLE_LINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"/),\n    end: concat(/\"/, rawDelimiter),\n    contains: [ESCAPED_CHARACTER(rawDelimiter), INTERPOLATION(rawDelimiter)]\n  });\n  const STRING = {\n    className: 'string',\n    variants: [MULTILINE_STRING(), MULTILINE_STRING(\"#\"), MULTILINE_STRING(\"##\"), MULTILINE_STRING(\"###\"), SINGLE_LINE_STRING(), SINGLE_LINE_STRING(\"#\"), SINGLE_LINE_STRING(\"##\"), SINGLE_LINE_STRING(\"###\")]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID412\n  const QUOTED_IDENTIFIER = {\n    match: concat(/`/, identifier, /`/)\n  };\n  const IMPLICIT_PARAMETER = {\n    className: 'variable',\n    match: /\\$\\d+/\n  };\n  const PROPERTY_WRAPPER_PROJECTION = {\n    className: 'variable',\n    match: `\\\\$${identifierCharacter}+`\n  };\n  const IDENTIFIERS = [QUOTED_IDENTIFIER, IMPLICIT_PARAMETER, PROPERTY_WRAPPER_PROJECTION];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Attributes.html\n  const AVAILABLE_ATTRIBUTE = {\n    match: /(@|#)available/,\n    className: \"keyword\",\n    starts: {\n      contains: [{\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: availabilityKeywords,\n        contains: [...OPERATORS, NUMBER, STRING]\n      }]\n    }\n  };\n  const KEYWORD_ATTRIBUTE = {\n    className: 'keyword',\n    match: concat(/@/, either(...keywordAttributes))\n  };\n  const USER_DEFINED_ATTRIBUTE = {\n    className: 'meta',\n    match: concat(/@/, identifier)\n  };\n  const ATTRIBUTES = [AVAILABLE_ATTRIBUTE, KEYWORD_ATTRIBUTE, USER_DEFINED_ATTRIBUTE];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Types.html\n  const TYPE = {\n    match: lookahead(/\\b[A-Z]/),\n    relevance: 0,\n    contains: [{\n      // Common Apple frameworks, for relevance boost\n      className: 'type',\n      match: concat(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, identifierCharacter, '+')\n    }, {\n      // Type identifier\n      className: 'type',\n      match: typeIdentifier,\n      relevance: 0\n    }, {\n      // Optional type\n      match: /[?!]+/,\n      relevance: 0\n    }, {\n      // Variadic parameter\n      match: /\\.\\.\\./,\n      relevance: 0\n    }, {\n      // Protocol composition\n      match: concat(/\\s+&\\s+/, lookahead(typeIdentifier)),\n      relevance: 0\n    }]\n  };\n  const GENERIC_ARGUMENTS = {\n    begin: /</,\n    end: />/,\n    keywords: KEYWORDS,\n    contains: [...COMMENTS, ...KEYWORD_MODES, ...ATTRIBUTES, OPERATOR_GUARD, TYPE]\n  };\n  TYPE.contains.push(GENERIC_ARGUMENTS);\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Expressions.html#ID552\n  // Prevents element names from being highlighted as keywords.\n  const TUPLE_ELEMENT_NAME = {\n    match: concat(identifier, /\\s*:/),\n    keywords: \"_|0\",\n    relevance: 0\n  };\n  // Matches tuples as well as the parameter list of a function type.\n  const TUPLE = {\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: KEYWORDS,\n    contains: ['self', TUPLE_ELEMENT_NAME, ...COMMENTS, ...KEYWORD_MODES, ...BUILT_INS, ...OPERATORS, NUMBER, STRING, ...IDENTIFIERS, ...ATTRIBUTES, TYPE]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID362\n  // Matches both the keyword func and the function title.\n  // Grouping these lets us differentiate between the operator function <\n  // and the start of the generic parameter clause (also <).\n  const FUNC_PLUS_TITLE = {\n    beginKeywords: 'func',\n    contains: [{\n      className: 'title',\n      match: either(QUOTED_IDENTIFIER.match, identifier, operator),\n      // Required to make sure the opening < of the generic parameter clause\n      // isn't parsed as a second title.\n      endsParent: true,\n      relevance: 0\n    }, WHITESPACE]\n  };\n  const GENERIC_PARAMETERS = {\n    begin: /</,\n    end: />/,\n    contains: [...COMMENTS, TYPE]\n  };\n  const FUNCTION_PARAMETER_NAME = {\n    begin: either(lookahead(concat(identifier, /\\s*:/)), lookahead(concat(identifier, /\\s+/, identifier, /\\s*:/))),\n    end: /:/,\n    relevance: 0,\n    contains: [{\n      className: 'keyword',\n      match: /\\b_\\b/\n    }, {\n      className: 'params',\n      match: identifier\n    }]\n  };\n  const FUNCTION_PARAMETERS = {\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [FUNCTION_PARAMETER_NAME, ...COMMENTS, ...KEYWORD_MODES, ...OPERATORS, NUMBER, STRING, ...ATTRIBUTES, TYPE, TUPLE],\n    endsParent: true,\n    illegal: /[\"']/\n  };\n  const FUNCTION = {\n    className: 'function',\n    match: lookahead(/\\bfunc\\b/),\n    contains: [FUNC_PLUS_TITLE, GENERIC_PARAMETERS, FUNCTION_PARAMETERS, WHITESPACE],\n    illegal: [/\\[/, /%/]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID375\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID379\n  const INIT_SUBSCRIPT = {\n    className: 'function',\n    match: /\\b(subscript|init[?!]?)\\s*(?=[<(])/,\n    keywords: {\n      keyword: \"subscript init init? init!\",\n      $pattern: /\\w+[?!]?/\n    },\n    contains: [GENERIC_PARAMETERS, FUNCTION_PARAMETERS, WHITESPACE],\n    illegal: /\\[|%/\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID380\n  const OPERATOR_DECLARATION = {\n    beginKeywords: 'operator',\n    end: hljs.MATCH_NOTHING_RE,\n    contains: [{\n      className: 'title',\n      match: operator,\n      endsParent: true,\n      relevance: 0\n    }]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID550\n  const PRECEDENCEGROUP = {\n    beginKeywords: 'precedencegroup',\n    end: hljs.MATCH_NOTHING_RE,\n    contains: [{\n      className: 'title',\n      match: typeIdentifier,\n      relevance: 0\n    }, {\n      begin: /{/,\n      end: /}/,\n      relevance: 0,\n      endsParent: true,\n      keywords: [...precedencegroupKeywords, ...literals],\n      contains: [TYPE]\n    }]\n  };\n\n  // Add supported submodes to string interpolation.\n  for (const variant of STRING.variants) {\n    const interpolation = variant.contains.find(mode => mode.label === \"interpol\");\n    // TODO: Interpolation can contain any expression, so there's room for improvement here.\n    interpolation.keywords = KEYWORDS;\n    const submodes = [...KEYWORD_MODES, ...BUILT_INS, ...OPERATORS, NUMBER, STRING, ...IDENTIFIERS];\n    interpolation.contains = [...submodes, {\n      begin: /\\(/,\n      end: /\\)/,\n      contains: ['self', ...submodes]\n    }];\n  }\n  return {\n    name: 'Swift',\n    keywords: KEYWORDS,\n    contains: [...COMMENTS, FUNCTION, INIT_SUBSCRIPT, {\n      className: 'class',\n      beginKeywords: 'struct protocol class extension enum',\n      end: '\\\\{',\n      excludeEnd: true,\n      keywords: KEYWORDS,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        begin: /[A-Za-z$_][\\u00C0-\\u02B80-9A-Za-z$_]*/\n      }), ...KEYWORD_MODES]\n    }, OPERATOR_DECLARATION, PRECEDENCEGROUP, {\n      beginKeywords: 'import',\n      end: /$/,\n      contains: [...COMMENTS],\n      relevance: 0\n    }, ...KEYWORD_MODES, ...BUILT_INS, ...OPERATORS, NUMBER, STRING, ...IDENTIFIERS, ...ATTRIBUTES, TYPE, TUPLE]\n  };\n}\nmodule.exports = swift;", "map": {"version": 3, "names": ["source", "re", "<PERSON><PERSON><PERSON>", "concat", "args", "joined", "map", "x", "join", "either", "keywordWrapper", "keyword", "test", "dotKeywords", "optionalDotKeywords", "keywordTypes", "keywords", "literals", "precedencegroupKeywords", "numberSignKeywords", "builtIns", "operatorHead", "operatorCharacter", "operator", "identifierHead", "identifierCharacter", "identifier", "typeIdentifier", "keywordAttributes", "availabilityKeywords", "swift", "hljs", "WHITESPACE", "match", "relevance", "BLOCK_COMMENT", "COMMENT", "contains", "COMMENTS", "C_LINE_COMMENT_MODE", "DOT_KEYWORD", "className", "begin", "end", "excludeBegin", "KEYWORD_GUARD", "PLAIN_KEYWORDS", "filter", "kw", "REGEX_KEYWORDS", "KEYWORD", "variants", "KEYWORDS", "$pattern", "literal", "KEYWORD_MODES", "BUILT_IN_GUARD", "BUILT_IN", "BUILT_INS", "OPERATOR_GUARD", "OPERATOR", "OPERATORS", "decimalDigits", "hexDigits", "NUMBER", "ESCAPED_CHARACTER", "rawDelimiter", "ESCAPED_NEWLINE", "INTERPOLATION", "label", "MULTILINE_STRING", "SINGLE_LINE_STRING", "STRING", "QUOTED_IDENTIFIER", "IMPLICIT_PARAMETER", "PROPERTY_WRAPPER_PROJECTION", "IDENTIFIERS", "AVAILABLE_ATTRIBUTE", "starts", "KEYWORD_ATTRIBUTE", "USER_DEFINED_ATTRIBUTE", "ATTRIBUTES", "TYPE", "GENERIC_ARGUMENTS", "push", "TUPLE_ELEMENT_NAME", "TUPLE", "FUNC_PLUS_TITLE", "beginKeywords", "endsParent", "GENERIC_PARAMETERS", "FUNCTION_PARAMETER_NAME", "FUNCTION_PARAMETERS", "illegal", "FUNCTION", "INIT_SUBSCRIPT", "OPERATOR_DECLARATION", "MATCH_NOTHING_RE", "PRECEDENCEGROUP", "variant", "interpolation", "find", "mode", "submodes", "name", "excludeEnd", "inherit", "TITLE_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/swift.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\nconst keywordWrapper = keyword => concat(\n  /\\b/,\n  keyword,\n  /\\w$/.test(keyword) ? /\\b/ : /\\B/\n);\n\n// Keywords that require a leading dot.\nconst dotKeywords = [\n  'Protocol', // contextual\n  'Type' // contextual\n].map(keywordWrapper);\n\n// Keywords that may have a leading dot.\nconst optionalDotKeywords = [\n  'init',\n  'self'\n].map(keywordWrapper);\n\n// should register as keyword, not type\nconst keywordTypes = [\n  'Any',\n  'Self'\n];\n\n// Regular keywords and literals.\nconst keywords = [\n  // strings below will be fed into the regular `keywords` engine while regex\n  // will result in additional modes being created to scan for those keywords to\n  // avoid conflicts with other rules\n  'associatedtype',\n  'async',\n  'await',\n  /as\\?/, // operator\n  /as!/, // operator\n  'as', // operator\n  'break',\n  'case',\n  'catch',\n  'class',\n  'continue',\n  'convenience', // contextual\n  'default',\n  'defer',\n  'deinit',\n  'didSet', // contextual\n  'do',\n  'dynamic', // contextual\n  'else',\n  'enum',\n  'extension',\n  'fallthrough',\n  /fileprivate\\(set\\)/,\n  'fileprivate',\n  'final', // contextual\n  'for',\n  'func',\n  'get', // contextual\n  'guard',\n  'if',\n  'import',\n  'indirect', // contextual\n  'infix', // contextual\n  /init\\?/,\n  /init!/,\n  'inout',\n  /internal\\(set\\)/,\n  'internal',\n  'in',\n  'is', // operator\n  'lazy', // contextual\n  'let',\n  'mutating', // contextual\n  'nonmutating', // contextual\n  /open\\(set\\)/, // contextual\n  'open', // contextual\n  'operator',\n  'optional', // contextual\n  'override', // contextual\n  'postfix', // contextual\n  'precedencegroup',\n  'prefix', // contextual\n  /private\\(set\\)/,\n  'private',\n  'protocol',\n  /public\\(set\\)/,\n  'public',\n  'repeat',\n  'required', // contextual\n  'rethrows',\n  'return',\n  'set', // contextual\n  'some', // contextual\n  'static',\n  'struct',\n  'subscript',\n  'super',\n  'switch',\n  'throws',\n  'throw',\n  /try\\?/, // operator\n  /try!/, // operator\n  'try', // operator\n  'typealias',\n  /unowned\\(safe\\)/, // contextual\n  /unowned\\(unsafe\\)/, // contextual\n  'unowned', // contextual\n  'var',\n  'weak', // contextual\n  'where',\n  'while',\n  'willSet' // contextual\n];\n\n// NOTE: Contextual keywords are reserved only in specific contexts.\n// Ideally, these should be matched using modes to avoid false positives.\n\n// Literals.\nconst literals = [\n  'false',\n  'nil',\n  'true'\n];\n\n// Keywords used in precedence groups.\nconst precedencegroupKeywords = [\n  'assignment',\n  'associativity',\n  'higherThan',\n  'left',\n  'lowerThan',\n  'none',\n  'right'\n];\n\n// Keywords that start with a number sign (#).\n// #available is handled separately.\nconst numberSignKeywords = [\n  '#colorLiteral',\n  '#column',\n  '#dsohandle',\n  '#else',\n  '#elseif',\n  '#endif',\n  '#error',\n  '#file',\n  '#fileID',\n  '#fileLiteral',\n  '#filePath',\n  '#function',\n  '#if',\n  '#imageLiteral',\n  '#keyPath',\n  '#line',\n  '#selector',\n  '#sourceLocation',\n  '#warn_unqualified_access',\n  '#warning'\n];\n\n// Global functions in the Standard Library.\nconst builtIns = [\n  'abs',\n  'all',\n  'any',\n  'assert',\n  'assertionFailure',\n  'debugPrint',\n  'dump',\n  'fatalError',\n  'getVaList',\n  'isKnownUniquelyReferenced',\n  'max',\n  'min',\n  'numericCast',\n  'pointwiseMax',\n  'pointwiseMin',\n  'precondition',\n  'preconditionFailure',\n  'print',\n  'readLine',\n  'repeatElement',\n  'sequence',\n  'stride',\n  'swap',\n  'swift_unboxFromSwiftValueWithType',\n  'transcode',\n  'type',\n  'unsafeBitCast',\n  'unsafeDowncast',\n  'withExtendedLifetime',\n  'withUnsafeMutablePointer',\n  'withUnsafePointer',\n  'withVaList',\n  'withoutActuallyEscaping',\n  'zip'\n];\n\n// Valid first characters for operators.\nconst operatorHead = either(\n  /[/=\\-+!*%<>&|^~?]/,\n  /[\\u00A1-\\u00A7]/,\n  /[\\u00A9\\u00AB]/,\n  /[\\u00AC\\u00AE]/,\n  /[\\u00B0\\u00B1]/,\n  /[\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7]/,\n  /[\\u2016-\\u2017]/,\n  /[\\u2020-\\u2027]/,\n  /[\\u2030-\\u203E]/,\n  /[\\u2041-\\u2053]/,\n  /[\\u2055-\\u205E]/,\n  /[\\u2190-\\u23FF]/,\n  /[\\u2500-\\u2775]/,\n  /[\\u2794-\\u2BFF]/,\n  /[\\u2E00-\\u2E7F]/,\n  /[\\u3001-\\u3003]/,\n  /[\\u3008-\\u3020]/,\n  /[\\u3030]/\n);\n\n// Valid characters for operators.\nconst operatorCharacter = either(\n  operatorHead,\n  /[\\u0300-\\u036F]/,\n  /[\\u1DC0-\\u1DFF]/,\n  /[\\u20D0-\\u20FF]/,\n  /[\\uFE00-\\uFE0F]/,\n  /[\\uFE20-\\uFE2F]/\n  // TODO: The following characters are also allowed, but the regex isn't supported yet.\n  // /[\\u{E0100}-\\u{E01EF}]/u\n);\n\n// Valid operator.\nconst operator = concat(operatorHead, operatorCharacter, '*');\n\n// Valid first characters for identifiers.\nconst identifierHead = either(\n  /[a-zA-Z_]/,\n  /[\\u00A8\\u00AA\\u00AD\\u00AF\\u00B2-\\u00B5\\u00B7-\\u00BA]/,\n  /[\\u00BC-\\u00BE\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u00FF]/,\n  /[\\u0100-\\u02FF\\u0370-\\u167F\\u1681-\\u180D\\u180F-\\u1DBF]/,\n  /[\\u1E00-\\u1FFF]/,\n  /[\\u200B-\\u200D\\u202A-\\u202E\\u203F-\\u2040\\u2054\\u2060-\\u206F]/,\n  /[\\u2070-\\u20CF\\u2100-\\u218F\\u2460-\\u24FF\\u2776-\\u2793]/,\n  /[\\u2C00-\\u2DFF\\u2E80-\\u2FFF]/,\n  /[\\u3004-\\u3007\\u3021-\\u302F\\u3031-\\u303F\\u3040-\\uD7FF]/,\n  /[\\uF900-\\uFD3D\\uFD40-\\uFDCF\\uFDF0-\\uFE1F\\uFE30-\\uFE44]/,\n  /[\\uFE47-\\uFEFE\\uFF00-\\uFFFD]/ // Should be /[\\uFE47-\\uFFFD]/, but we have to exclude FEFF.\n  // The following characters are also allowed, but the regexes aren't supported yet.\n  // /[\\u{10000}-\\u{1FFFD}\\u{20000-\\u{2FFFD}\\u{30000}-\\u{3FFFD}\\u{40000}-\\u{4FFFD}]/u,\n  // /[\\u{50000}-\\u{5FFFD}\\u{60000-\\u{6FFFD}\\u{70000}-\\u{7FFFD}\\u{80000}-\\u{8FFFD}]/u,\n  // /[\\u{90000}-\\u{9FFFD}\\u{A0000-\\u{AFFFD}\\u{B0000}-\\u{BFFFD}\\u{C0000}-\\u{CFFFD}]/u,\n  // /[\\u{D0000}-\\u{DFFFD}\\u{E0000-\\u{EFFFD}]/u\n);\n\n// Valid characters for identifiers.\nconst identifierCharacter = either(\n  identifierHead,\n  /\\d/,\n  /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE20-\\uFE2F]/\n);\n\n// Valid identifier.\nconst identifier = concat(identifierHead, identifierCharacter, '*');\n\n// Valid type identifier.\nconst typeIdentifier = concat(/[A-Z]/, identifierCharacter, '*');\n\n// Built-in attributes, which are highlighted as keywords.\n// @available is handled separately.\nconst keywordAttributes = [\n  'autoclosure',\n  concat(/convention\\(/, either('swift', 'block', 'c'), /\\)/),\n  'discardableResult',\n  'dynamicCallable',\n  'dynamicMemberLookup',\n  'escaping',\n  'frozen',\n  'GKInspectable',\n  'IBAction',\n  'IBDesignable',\n  'IBInspectable',\n  'IBOutlet',\n  'IBSegueAction',\n  'inlinable',\n  'main',\n  'nonobjc',\n  'NSApplicationMain',\n  'NSCopying',\n  'NSManaged',\n  concat(/objc\\(/, identifier, /\\)/),\n  'objc',\n  'objcMembers',\n  'propertyWrapper',\n  'requires_stored_property_inits',\n  'testable',\n  'UIApplicationMain',\n  'unknown',\n  'usableFromInline'\n];\n\n// Contextual keywords used in @available and #available.\nconst availabilityKeywords = [\n  'iOS',\n  'iOSApplicationExtension',\n  'macOS',\n  'macOSApplicationExtension',\n  'macCatalyst',\n  'macCatalystApplicationExtension',\n  'watchOS',\n  'watchOSApplicationExtension',\n  'tvOS',\n  'tvOSApplicationExtension',\n  'swift'\n];\n\n/*\nLanguage: Swift\nDescription: Swift is a general-purpose programming language built using a modern approach to safety, performance, and software design patterns.\nAuthor: Steven Van Impe <<EMAIL>>\nContributors: Chris Eidhof <<EMAIL>>, Nate Cook <<EMAIL>>, Alexander Lichter <<EMAIL>>, Richard Gibson <gibson042@github>\nWebsite: https://swift.org\nCategory: common, system\n*/\n\n/** @type LanguageFn */\nfunction swift(hljs) {\n  const WHITESPACE = {\n    match: /\\s+/,\n    relevance: 0\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID411\n  const BLOCK_COMMENT = hljs.COMMENT(\n    '/\\\\*',\n    '\\\\*/',\n    {\n      contains: [ 'self' ]\n    }\n  );\n  const COMMENTS = [\n    hljs.C_LINE_COMMENT_MODE,\n    BLOCK_COMMENT\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID413\n  // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html\n  const DOT_KEYWORD = {\n    className: 'keyword',\n    begin: concat(/\\./, lookahead(either(...dotKeywords, ...optionalDotKeywords))),\n    end: either(...dotKeywords, ...optionalDotKeywords),\n    excludeBegin: true\n  };\n  const KEYWORD_GUARD = {\n    // Consume .keyword to prevent highlighting properties and methods as keywords.\n    match: concat(/\\./, either(...keywords)),\n    relevance: 0\n  };\n  const PLAIN_KEYWORDS = keywords\n    .filter(kw => typeof kw === 'string')\n    .concat([ \"_|0\" ]); // seems common, so 0 relevance\n  const REGEX_KEYWORDS = keywords\n    .filter(kw => typeof kw !== 'string') // find regex\n    .concat(keywordTypes)\n    .map(keywordWrapper);\n  const KEYWORD = {\n    variants: [\n      {\n        className: 'keyword',\n        match: either(...REGEX_KEYWORDS, ...optionalDotKeywords)\n      }\n    ]\n  };\n  // find all the regular keywords\n  const KEYWORDS = {\n    $pattern: either(\n      /\\b\\w+/, // regular keywords\n      /#\\w+/ // number keywords\n    ),\n    keyword: PLAIN_KEYWORDS\n      .concat(numberSignKeywords),\n    literal: literals\n  };\n  const KEYWORD_MODES = [\n    DOT_KEYWORD,\n    KEYWORD_GUARD,\n    KEYWORD\n  ];\n\n  // https://github.com/apple/swift/tree/main/stdlib/public/core\n  const BUILT_IN_GUARD = {\n    // Consume .built_in to prevent highlighting properties and methods.\n    match: concat(/\\./, either(...builtIns)),\n    relevance: 0\n  };\n  const BUILT_IN = {\n    className: 'built_in',\n    match: concat(/\\b/, either(...builtIns), /(?=\\()/)\n  };\n  const BUILT_INS = [\n    BUILT_IN_GUARD,\n    BUILT_IN\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID418\n  const OPERATOR_GUARD = {\n    // Prevent -> from being highlighting as an operator.\n    match: /->/,\n    relevance: 0\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    variants: [\n      {\n        match: operator\n      },\n      {\n        // dot-operator: only operators that start with a dot are allowed to use dots as\n        // characters (..., ...<, .*, etc). So there rule here is: a dot followed by one or more\n        // characters that may also include dots.\n        match: `\\\\.(\\\\.|${operatorCharacter})+`\n      }\n    ]\n  };\n  const OPERATORS = [\n    OPERATOR_GUARD,\n    OPERATOR\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_numeric-literal\n  // TODO: Update for leading `-` after lookbehind is supported everywhere\n  const decimalDigits = '([0-9]_*)+';\n  const hexDigits = '([0-9a-fA-F]_*)+';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal floating-point-literal (subsumes decimal-literal)\n      {\n        match: `\\\\b(${decimalDigits})(\\\\.(${decimalDigits}))?` + `([eE][+-]?(${decimalDigits}))?\\\\b`\n      },\n      // hexadecimal floating-point-literal (subsumes hexadecimal-literal)\n      {\n        match: `\\\\b0x(${hexDigits})(\\\\.(${hexDigits}))?` + `([pP][+-]?(${decimalDigits}))?\\\\b`\n      },\n      // octal-literal\n      {\n        match: /\\b0o([0-7]_*)+\\b/\n      },\n      // binary-literal\n      {\n        match: /\\b0b([01]_*)+\\b/\n      }\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_string-literal\n  const ESCAPED_CHARACTER = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    variants: [\n      {\n        match: concat(/\\\\/, rawDelimiter, /[0\\\\tnr\"']/)\n      },\n      {\n        match: concat(/\\\\/, rawDelimiter, /u\\{[0-9a-fA-F]{1,8}\\}/)\n      }\n    ]\n  });\n  const ESCAPED_NEWLINE = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    match: concat(/\\\\/, rawDelimiter, /[\\t ]*(?:[\\r\\n]|\\r\\n)/)\n  });\n  const INTERPOLATION = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    label: \"interpol\",\n    begin: concat(/\\\\/, rawDelimiter, /\\(/),\n    end: /\\)/\n  });\n  const MULTILINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"\"\"/),\n    end: concat(/\"\"\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      ESCAPED_NEWLINE(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const SINGLE_LINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"/),\n    end: concat(/\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const STRING = {\n    className: 'string',\n    variants: [\n      MULTILINE_STRING(),\n      MULTILINE_STRING(\"#\"),\n      MULTILINE_STRING(\"##\"),\n      MULTILINE_STRING(\"###\"),\n      SINGLE_LINE_STRING(),\n      SINGLE_LINE_STRING(\"#\"),\n      SINGLE_LINE_STRING(\"##\"),\n      SINGLE_LINE_STRING(\"###\")\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID412\n  const QUOTED_IDENTIFIER = {\n    match: concat(/`/, identifier, /`/)\n  };\n  const IMPLICIT_PARAMETER = {\n    className: 'variable',\n    match: /\\$\\d+/\n  };\n  const PROPERTY_WRAPPER_PROJECTION = {\n    className: 'variable',\n    match: `\\\\$${identifierCharacter}+`\n  };\n  const IDENTIFIERS = [\n    QUOTED_IDENTIFIER,\n    IMPLICIT_PARAMETER,\n    PROPERTY_WRAPPER_PROJECTION\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Attributes.html\n  const AVAILABLE_ATTRIBUTE = {\n    match: /(@|#)available/,\n    className: \"keyword\",\n    starts: {\n      contains: [\n        {\n          begin: /\\(/,\n          end: /\\)/,\n          keywords: availabilityKeywords,\n          contains: [\n            ...OPERATORS,\n            NUMBER,\n            STRING\n          ]\n        }\n      ]\n    }\n  };\n  const KEYWORD_ATTRIBUTE = {\n    className: 'keyword',\n    match: concat(/@/, either(...keywordAttributes))\n  };\n  const USER_DEFINED_ATTRIBUTE = {\n    className: 'meta',\n    match: concat(/@/, identifier)\n  };\n  const ATTRIBUTES = [\n    AVAILABLE_ATTRIBUTE,\n    KEYWORD_ATTRIBUTE,\n    USER_DEFINED_ATTRIBUTE\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Types.html\n  const TYPE = {\n    match: lookahead(/\\b[A-Z]/),\n    relevance: 0,\n    contains: [\n      { // Common Apple frameworks, for relevance boost\n        className: 'type',\n        match: concat(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, identifierCharacter, '+')\n      },\n      { // Type identifier\n        className: 'type',\n        match: typeIdentifier,\n        relevance: 0\n      },\n      { // Optional type\n        match: /[?!]+/,\n        relevance: 0\n      },\n      { // Variadic parameter\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      { // Protocol composition\n        match: concat(/\\s+&\\s+/, lookahead(typeIdentifier)),\n        relevance: 0\n      }\n    ]\n  };\n  const GENERIC_ARGUMENTS = {\n    begin: /</,\n    end: />/,\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...ATTRIBUTES,\n      OPERATOR_GUARD,\n      TYPE\n    ]\n  };\n  TYPE.contains.push(GENERIC_ARGUMENTS);\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Expressions.html#ID552\n  // Prevents element names from being highlighted as keywords.\n  const TUPLE_ELEMENT_NAME = {\n    match: concat(identifier, /\\s*:/),\n    keywords: \"_|0\",\n    relevance: 0\n  };\n  // Matches tuples as well as the parameter list of a function type.\n  const TUPLE = {\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: KEYWORDS,\n    contains: [\n      'self',\n      TUPLE_ELEMENT_NAME,\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID362\n  // Matches both the keyword func and the function title.\n  // Grouping these lets us differentiate between the operator function <\n  // and the start of the generic parameter clause (also <).\n  const FUNC_PLUS_TITLE = {\n    beginKeywords: 'func',\n    contains: [\n      {\n        className: 'title',\n        match: either(QUOTED_IDENTIFIER.match, identifier, operator),\n        // Required to make sure the opening < of the generic parameter clause\n        // isn't parsed as a second title.\n        endsParent: true,\n        relevance: 0\n      },\n      WHITESPACE\n    ]\n  };\n  const GENERIC_PARAMETERS = {\n    begin: /</,\n    end: />/,\n    contains: [\n      ...COMMENTS,\n      TYPE\n    ]\n  };\n  const FUNCTION_PARAMETER_NAME = {\n    begin: either(\n      lookahead(concat(identifier, /\\s*:/)),\n      lookahead(concat(identifier, /\\s+/, identifier, /\\s*:/))\n    ),\n    end: /:/,\n    relevance: 0,\n    contains: [\n      {\n        className: 'keyword',\n        match: /\\b_\\b/\n      },\n      {\n        className: 'params',\n        match: identifier\n      }\n    ]\n  };\n  const FUNCTION_PARAMETERS = {\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [\n      FUNCTION_PARAMETER_NAME,\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ],\n    endsParent: true,\n    illegal: /[\"']/\n  };\n  const FUNCTION = {\n    className: 'function',\n    match: lookahead(/\\bfunc\\b/),\n    contains: [\n      FUNC_PLUS_TITLE,\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: [\n      /\\[/,\n      /%/\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID375\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID379\n  const INIT_SUBSCRIPT = {\n    className: 'function',\n    match: /\\b(subscript|init[?!]?)\\s*(?=[<(])/,\n    keywords: {\n      keyword: \"subscript init init? init!\",\n      $pattern: /\\w+[?!]?/\n    },\n    contains: [\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: /\\[|%/\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID380\n  const OPERATOR_DECLARATION = {\n    beginKeywords: 'operator',\n    end: hljs.MATCH_NOTHING_RE,\n    contains: [\n      {\n        className: 'title',\n        match: operator,\n        endsParent: true,\n        relevance: 0\n      }\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID550\n  const PRECEDENCEGROUP = {\n    beginKeywords: 'precedencegroup',\n    end: hljs.MATCH_NOTHING_RE,\n    contains: [\n      {\n        className: 'title',\n        match: typeIdentifier,\n        relevance: 0\n      },\n      {\n        begin: /{/,\n        end: /}/,\n        relevance: 0,\n        endsParent: true,\n        keywords: [\n          ...precedencegroupKeywords,\n          ...literals\n        ],\n        contains: [ TYPE ]\n      }\n    ]\n  };\n\n  // Add supported submodes to string interpolation.\n  for (const variant of STRING.variants) {\n    const interpolation = variant.contains.find(mode => mode.label === \"interpol\");\n    // TODO: Interpolation can contain any expression, so there's room for improvement here.\n    interpolation.keywords = KEYWORDS;\n    const submodes = [\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS\n    ];\n    interpolation.contains = [\n      ...submodes,\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          'self',\n          ...submodes\n        ]\n      }\n    ];\n  }\n\n  return {\n    name: 'Swift',\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      FUNCTION,\n      INIT_SUBSCRIPT,\n      {\n        className: 'class',\n        beginKeywords: 'struct protocol class extension enum',\n        end: '\\\\{',\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: /[A-Za-z$_][\\u00C0-\\u02B80-9A-Za-z$_]*/\n          }),\n          ...KEYWORD_MODES\n        ]\n      },\n      OPERATOR_DECLARATION,\n      PRECEDENCEGROUP,\n      {\n        beginKeywords: 'import',\n        end: /$/,\n        contains: [ ...COMMENTS ],\n        relevance: 0\n      },\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ]\n  };\n}\n\nmodule.exports = swift;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOE,MAAM,CAAC,KAAK,EAAEF,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;AAEA,MAAMK,cAAc,GAAGC,OAAO,IAAIR,MAAM,CACtC,IAAI,EACJQ,OAAO,EACP,KAAK,CAACC,IAAI,CAACD,OAAO,CAAC,GAAG,IAAI,GAAG,IAC/B,CAAC;;AAED;AACA,MAAME,WAAW,GAAG,CAClB,UAAU;AAAE;AACZ,MAAM,CAAC;AAAA,CACR,CAACP,GAAG,CAACI,cAAc,CAAC;;AAErB;AACA,MAAMI,mBAAmB,GAAG,CAC1B,MAAM,EACN,MAAM,CACP,CAACR,GAAG,CAACI,cAAc,CAAC;;AAErB;AACA,MAAMK,YAAY,GAAG,CACnB,KAAK,EACL,MAAM,CACP;;AAED;AACA,MAAMC,QAAQ,GAAG;AACf;AACA;AACA;AACA,gBAAgB,EAChB,OAAO,EACP,OAAO,EACP,MAAM;AAAE;AACR,KAAK;AAAE;AACP,IAAI;AAAE;AACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,UAAU,EACV,aAAa;AAAE;AACf,SAAS,EACT,OAAO,EACP,QAAQ,EACR,QAAQ;AAAE;AACV,IAAI,EACJ,SAAS;AAAE;AACX,MAAM,EACN,MAAM,EACN,WAAW,EACX,aAAa,EACb,oBAAoB,EACpB,aAAa,EACb,OAAO;AAAE;AACT,KAAK,EACL,MAAM,EACN,KAAK;AAAE;AACP,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,UAAU;AAAE;AACZ,OAAO;AAAE;AACT,QAAQ,EACR,OAAO,EACP,OAAO,EACP,iBAAiB,EACjB,UAAU,EACV,IAAI,EACJ,IAAI;AAAE;AACN,MAAM;AAAE;AACR,KAAK,EACL,UAAU;AAAE;AACZ,aAAa;AAAE;AACf,aAAa;AAAE;AACf,MAAM;AAAE;AACR,UAAU,EACV,UAAU;AAAE;AACZ,UAAU;AAAE;AACZ,SAAS;AAAE;AACX,iBAAiB,EACjB,QAAQ;AAAE;AACV,gBAAgB,EAChB,SAAS,EACT,UAAU,EACV,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,UAAU;AAAE;AACZ,UAAU,EACV,QAAQ,EACR,KAAK;AAAE;AACP,MAAM;AAAE;AACR,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO;AAAE;AACT,MAAM;AAAE;AACR,KAAK;AAAE;AACP,WAAW,EACX,iBAAiB;AAAE;AACnB,mBAAmB;AAAE;AACrB,SAAS;AAAE;AACX,KAAK,EACL,MAAM;AAAE;AACR,OAAO,EACP,OAAO,EACP,SAAS,CAAC;AAAA,CACX;;AAED;AACA;;AAEA;AACA,MAAMC,QAAQ,GAAG,CACf,OAAO,EACP,KAAK,EACL,MAAM,CACP;;AAED;AACA,MAAMC,uBAAuB,GAAG,CAC9B,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,MAAM,EACN,WAAW,EACX,MAAM,EACN,OAAO,CACR;;AAED;AACA;AACA,MAAMC,kBAAkB,GAAG,CACzB,eAAe,EACf,SAAS,EACT,YAAY,EACZ,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,SAAS,EACT,cAAc,EACd,WAAW,EACX,WAAW,EACX,KAAK,EACL,eAAe,EACf,UAAU,EACV,OAAO,EACP,WAAW,EACX,iBAAiB,EACjB,0BAA0B,EAC1B,UAAU,CACX;;AAED;AACA,MAAMC,QAAQ,GAAG,CACf,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,kBAAkB,EAClB,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,WAAW,EACX,2BAA2B,EAC3B,KAAK,EACL,KAAK,EACL,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,qBAAqB,EACrB,OAAO,EACP,UAAU,EACV,eAAe,EACf,UAAU,EACV,QAAQ,EACR,MAAM,EACN,mCAAmC,EACnC,WAAW,EACX,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,sBAAsB,EACtB,0BAA0B,EAC1B,mBAAmB,EACnB,YAAY,EACZ,yBAAyB,EACzB,KAAK,CACN;;AAED;AACA,MAAMC,YAAY,GAAGZ,MAAM,CACzB,mBAAmB,EACnB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,kCAAkC,EAClC,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,UACF,CAAC;;AAED;AACA,MAAMa,iBAAiB,GAAGb,MAAM,CAC9BY,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB;AACA;AACA;AACF,CAAC;;AAED;AACA,MAAME,QAAQ,GAAGpB,MAAM,CAACkB,YAAY,EAAEC,iBAAiB,EAAE,GAAG,CAAC;;AAE7D;AACA,MAAME,cAAc,GAAGf,MAAM,CAC3B,WAAW,EACX,sDAAsD,EACtD,wDAAwD,EACxD,wDAAwD,EACxD,iBAAiB,EACjB,8DAA8D,EAC9D,wDAAwD,EACxD,8BAA8B,EAC9B,wDAAwD,EACxD,wDAAwD,EACxD,8BAA8B,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACF,CAAC;;AAED;AACA,MAAMgB,mBAAmB,GAAGhB,MAAM,CAChCe,cAAc,EACd,IAAI,EACJ,wDACF,CAAC;;AAED;AACA,MAAME,UAAU,GAAGvB,MAAM,CAACqB,cAAc,EAAEC,mBAAmB,EAAE,GAAG,CAAC;;AAEnE;AACA,MAAME,cAAc,GAAGxB,MAAM,CAAC,OAAO,EAAEsB,mBAAmB,EAAE,GAAG,CAAC;;AAEhE;AACA;AACA,MAAMG,iBAAiB,GAAG,CACxB,aAAa,EACbzB,MAAM,CAAC,cAAc,EAAEM,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAC3D,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACrB,UAAU,EACV,QAAQ,EACR,eAAe,EACf,UAAU,EACV,cAAc,EACd,eAAe,EACf,UAAU,EACV,eAAe,EACf,WAAW,EACX,MAAM,EACN,SAAS,EACT,mBAAmB,EACnB,WAAW,EACX,WAAW,EACXN,MAAM,CAAC,QAAQ,EAAEuB,UAAU,EAAE,IAAI,CAAC,EAClC,MAAM,EACN,aAAa,EACb,iBAAiB,EACjB,gCAAgC,EAChC,UAAU,EACV,mBAAmB,EACnB,SAAS,EACT,kBAAkB,CACnB;;AAED;AACA,MAAMG,oBAAoB,GAAG,CAC3B,KAAK,EACL,yBAAyB,EACzB,OAAO,EACP,2BAA2B,EAC3B,aAAa,EACb,iCAAiC,EACjC,SAAS,EACT,6BAA6B,EAC7B,MAAM,EACN,0BAA0B,EAC1B,OAAO,CACR;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC;EACD;EACA,MAAMC,aAAa,GAAGJ,IAAI,CAACK,OAAO,CAChC,MAAM,EACN,MAAM,EACN;IACEC,QAAQ,EAAE,CAAE,MAAM;EACpB,CACF,CAAC;EACD,MAAMC,QAAQ,GAAG,CACfP,IAAI,CAACQ,mBAAmB,EACxBJ,aAAa,CACd;;EAED;EACA;EACA,MAAMK,WAAW,GAAG;IAClBC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAEvC,MAAM,CAAC,IAAI,EAAED,SAAS,CAACO,MAAM,CAAC,GAAGI,WAAW,EAAE,GAAGC,mBAAmB,CAAC,CAAC,CAAC;IAC9E6B,GAAG,EAAElC,MAAM,CAAC,GAAGI,WAAW,EAAE,GAAGC,mBAAmB,CAAC;IACnD8B,YAAY,EAAE;EAChB,CAAC;EACD,MAAMC,aAAa,GAAG;IACpB;IACAZ,KAAK,EAAE9B,MAAM,CAAC,IAAI,EAAEM,MAAM,CAAC,GAAGO,QAAQ,CAAC,CAAC;IACxCkB,SAAS,EAAE;EACb,CAAC;EACD,MAAMY,cAAc,GAAG9B,QAAQ,CAC5B+B,MAAM,CAACC,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,CAAC,CACpC7C,MAAM,CAAC,CAAE,KAAK,CAAE,CAAC,CAAC,CAAC;EACtB,MAAM8C,cAAc,GAAGjC,QAAQ,CAC5B+B,MAAM,CAACC,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,CAAC,CAAC;EAAA,CACrC7C,MAAM,CAACY,YAAY,CAAC,CACpBT,GAAG,CAACI,cAAc,CAAC;EACtB,MAAMwC,OAAO,GAAG;IACdC,QAAQ,EAAE,CACR;MACEV,SAAS,EAAE,SAAS;MACpBR,KAAK,EAAExB,MAAM,CAAC,GAAGwC,cAAc,EAAE,GAAGnC,mBAAmB;IACzD,CAAC;EAEL,CAAC;EACD;EACA,MAAMsC,QAAQ,GAAG;IACfC,QAAQ,EAAE5C,MAAM,CACd,OAAO;IAAE;IACT,MAAM,CAAC;IACT,CAAC;IACDE,OAAO,EAAEmC,cAAc,CACpB3C,MAAM,CAACgB,kBAAkB,CAAC;IAC7BmC,OAAO,EAAErC;EACX,CAAC;EACD,MAAMsC,aAAa,GAAG,CACpBf,WAAW,EACXK,aAAa,EACbK,OAAO,CACR;;EAED;EACA,MAAMM,cAAc,GAAG;IACrB;IACAvB,KAAK,EAAE9B,MAAM,CAAC,IAAI,EAAEM,MAAM,CAAC,GAAGW,QAAQ,CAAC,CAAC;IACxCc,SAAS,EAAE;EACb,CAAC;EACD,MAAMuB,QAAQ,GAAG;IACfhB,SAAS,EAAE,UAAU;IACrBR,KAAK,EAAE9B,MAAM,CAAC,IAAI,EAAEM,MAAM,CAAC,GAAGW,QAAQ,CAAC,EAAE,QAAQ;EACnD,CAAC;EACD,MAAMsC,SAAS,GAAG,CAChBF,cAAc,EACdC,QAAQ,CACT;;EAED;EACA,MAAME,cAAc,GAAG;IACrB;IACA1B,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb,CAAC;EACD,MAAM0B,QAAQ,GAAG;IACfnB,SAAS,EAAE,UAAU;IACrBP,SAAS,EAAE,CAAC;IACZiB,QAAQ,EAAE,CACR;MACElB,KAAK,EAAEV;IACT,CAAC,EACD;MACE;MACA;MACA;MACAU,KAAK,EAAE,WAAWX,iBAAiB;IACrC,CAAC;EAEL,CAAC;EACD,MAAMuC,SAAS,GAAG,CAChBF,cAAc,EACdC,QAAQ,CACT;;EAED;EACA;EACA,MAAME,aAAa,GAAG,YAAY;EAClC,MAAMC,SAAS,GAAG,kBAAkB;EACpC,MAAMC,MAAM,GAAG;IACbvB,SAAS,EAAE,QAAQ;IACnBP,SAAS,EAAE,CAAC;IACZiB,QAAQ,EAAE;IACR;IACA;MACElB,KAAK,EAAE,OAAO6B,aAAa,SAASA,aAAa,KAAK,GAAG,cAAcA,aAAa;IACtF,CAAC;IACD;IACA;MACE7B,KAAK,EAAE,SAAS8B,SAAS,SAASA,SAAS,KAAK,GAAG,cAAcD,aAAa;IAChF,CAAC;IACD;IACA;MACE7B,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;;EAED;EACA,MAAMgC,iBAAiB,GAAGA,CAACC,YAAY,GAAG,EAAE,MAAM;IAChDzB,SAAS,EAAE,OAAO;IAClBU,QAAQ,EAAE,CACR;MACElB,KAAK,EAAE9B,MAAM,CAAC,IAAI,EAAE+D,YAAY,EAAE,YAAY;IAChD,CAAC,EACD;MACEjC,KAAK,EAAE9B,MAAM,CAAC,IAAI,EAAE+D,YAAY,EAAE,uBAAuB;IAC3D,CAAC;EAEL,CAAC,CAAC;EACF,MAAMC,eAAe,GAAGA,CAACD,YAAY,GAAG,EAAE,MAAM;IAC9CzB,SAAS,EAAE,OAAO;IAClBR,KAAK,EAAE9B,MAAM,CAAC,IAAI,EAAE+D,YAAY,EAAE,uBAAuB;EAC3D,CAAC,CAAC;EACF,MAAME,aAAa,GAAGA,CAACF,YAAY,GAAG,EAAE,MAAM;IAC5CzB,SAAS,EAAE,OAAO;IAClB4B,KAAK,EAAE,UAAU;IACjB3B,KAAK,EAAEvC,MAAM,CAAC,IAAI,EAAE+D,YAAY,EAAE,IAAI,CAAC;IACvCvB,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAM2B,gBAAgB,GAAGA,CAACJ,YAAY,GAAG,EAAE,MAAM;IAC/CxB,KAAK,EAAEvC,MAAM,CAAC+D,YAAY,EAAE,KAAK,CAAC;IAClCvB,GAAG,EAAExC,MAAM,CAAC,KAAK,EAAE+D,YAAY,CAAC;IAChC7B,QAAQ,EAAE,CACR4B,iBAAiB,CAACC,YAAY,CAAC,EAC/BC,eAAe,CAACD,YAAY,CAAC,EAC7BE,aAAa,CAACF,YAAY,CAAC;EAE/B,CAAC,CAAC;EACF,MAAMK,kBAAkB,GAAGA,CAACL,YAAY,GAAG,EAAE,MAAM;IACjDxB,KAAK,EAAEvC,MAAM,CAAC+D,YAAY,EAAE,GAAG,CAAC;IAChCvB,GAAG,EAAExC,MAAM,CAAC,GAAG,EAAE+D,YAAY,CAAC;IAC9B7B,QAAQ,EAAE,CACR4B,iBAAiB,CAACC,YAAY,CAAC,EAC/BE,aAAa,CAACF,YAAY,CAAC;EAE/B,CAAC,CAAC;EACF,MAAMM,MAAM,GAAG;IACb/B,SAAS,EAAE,QAAQ;IACnBU,QAAQ,EAAE,CACRmB,gBAAgB,CAAC,CAAC,EAClBA,gBAAgB,CAAC,GAAG,CAAC,EACrBA,gBAAgB,CAAC,IAAI,CAAC,EACtBA,gBAAgB,CAAC,KAAK,CAAC,EACvBC,kBAAkB,CAAC,CAAC,EACpBA,kBAAkB,CAAC,GAAG,CAAC,EACvBA,kBAAkB,CAAC,IAAI,CAAC,EACxBA,kBAAkB,CAAC,KAAK,CAAC;EAE7B,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAG;IACxBxC,KAAK,EAAE9B,MAAM,CAAC,GAAG,EAAEuB,UAAU,EAAE,GAAG;EACpC,CAAC;EACD,MAAMgD,kBAAkB,GAAG;IACzBjC,SAAS,EAAE,UAAU;IACrBR,KAAK,EAAE;EACT,CAAC;EACD,MAAM0C,2BAA2B,GAAG;IAClClC,SAAS,EAAE,UAAU;IACrBR,KAAK,EAAE,MAAMR,mBAAmB;EAClC,CAAC;EACD,MAAMmD,WAAW,GAAG,CAClBH,iBAAiB,EACjBC,kBAAkB,EAClBC,2BAA2B,CAC5B;;EAED;EACA,MAAME,mBAAmB,GAAG;IAC1B5C,KAAK,EAAE,gBAAgB;IACvBQ,SAAS,EAAE,SAAS;IACpBqC,MAAM,EAAE;MACNzC,QAAQ,EAAE,CACR;QACEK,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,IAAI;QACT3B,QAAQ,EAAEa,oBAAoB;QAC9BQ,QAAQ,EAAE,CACR,GAAGwB,SAAS,EACZG,MAAM,EACNQ,MAAM;MAEV,CAAC;IAEL;EACF,CAAC;EACD,MAAMO,iBAAiB,GAAG;IACxBtC,SAAS,EAAE,SAAS;IACpBR,KAAK,EAAE9B,MAAM,CAAC,GAAG,EAAEM,MAAM,CAAC,GAAGmB,iBAAiB,CAAC;EACjD,CAAC;EACD,MAAMoD,sBAAsB,GAAG;IAC7BvC,SAAS,EAAE,MAAM;IACjBR,KAAK,EAAE9B,MAAM,CAAC,GAAG,EAAEuB,UAAU;EAC/B,CAAC;EACD,MAAMuD,UAAU,GAAG,CACjBJ,mBAAmB,EACnBE,iBAAiB,EACjBC,sBAAsB,CACvB;;EAED;EACA,MAAME,IAAI,GAAG;IACXjD,KAAK,EAAE/B,SAAS,CAAC,SAAS,CAAC;IAC3BgC,SAAS,EAAE,CAAC;IACZG,QAAQ,EAAE,CACR;MAAE;MACAI,SAAS,EAAE,MAAM;MACjBR,KAAK,EAAE9B,MAAM,CAAC,+DAA+D,EAAEsB,mBAAmB,EAAE,GAAG;IACzG,CAAC,EACD;MAAE;MACAgB,SAAS,EAAE,MAAM;MACjBR,KAAK,EAAEN,cAAc;MACrBO,SAAS,EAAE;IACb,CAAC,EACD;MAAE;MACAD,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE;IACb,CAAC,EACD;MAAE;MACAD,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE;IACb,CAAC,EACD;MAAE;MACAD,KAAK,EAAE9B,MAAM,CAAC,SAAS,EAAED,SAAS,CAACyB,cAAc,CAAC,CAAC;MACnDO,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EACD,MAAMiD,iBAAiB,GAAG;IACxBzC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACR3B,QAAQ,EAAEoC,QAAQ;IAClBf,QAAQ,EAAE,CACR,GAAGC,QAAQ,EACX,GAAGiB,aAAa,EAChB,GAAG0B,UAAU,EACbtB,cAAc,EACduB,IAAI;EAER,CAAC;EACDA,IAAI,CAAC7C,QAAQ,CAAC+C,IAAI,CAACD,iBAAiB,CAAC;;EAErC;EACA;EACA,MAAME,kBAAkB,GAAG;IACzBpD,KAAK,EAAE9B,MAAM,CAACuB,UAAU,EAAE,MAAM,CAAC;IACjCV,QAAQ,EAAE,KAAK;IACfkB,SAAS,EAAE;EACb,CAAC;EACD;EACA,MAAMoD,KAAK,GAAG;IACZ5C,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTT,SAAS,EAAE,CAAC;IACZlB,QAAQ,EAAEoC,QAAQ;IAClBf,QAAQ,EAAE,CACR,MAAM,EACNgD,kBAAkB,EAClB,GAAG/C,QAAQ,EACX,GAAGiB,aAAa,EAChB,GAAGG,SAAS,EACZ,GAAGG,SAAS,EACZG,MAAM,EACNQ,MAAM,EACN,GAAGI,WAAW,EACd,GAAGK,UAAU,EACbC,IAAI;EAER,CAAC;;EAED;EACA;EACA;EACA;EACA,MAAMK,eAAe,GAAG;IACtBC,aAAa,EAAE,MAAM;IACrBnD,QAAQ,EAAE,CACR;MACEI,SAAS,EAAE,OAAO;MAClBR,KAAK,EAAExB,MAAM,CAACgE,iBAAiB,CAACxC,KAAK,EAAEP,UAAU,EAAEH,QAAQ,CAAC;MAC5D;MACA;MACAkE,UAAU,EAAE,IAAI;MAChBvD,SAAS,EAAE;IACb,CAAC,EACDF,UAAU;EAEd,CAAC;EACD,MAAM0D,kBAAkB,GAAG;IACzBhD,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRN,QAAQ,EAAE,CACR,GAAGC,QAAQ,EACX4C,IAAI;EAER,CAAC;EACD,MAAMS,uBAAuB,GAAG;IAC9BjD,KAAK,EAAEjC,MAAM,CACXP,SAAS,CAACC,MAAM,CAACuB,UAAU,EAAE,MAAM,CAAC,CAAC,EACrCxB,SAAS,CAACC,MAAM,CAACuB,UAAU,EAAE,KAAK,EAAEA,UAAU,EAAE,MAAM,CAAC,CACzD,CAAC;IACDiB,GAAG,EAAE,GAAG;IACRT,SAAS,EAAE,CAAC;IACZG,QAAQ,EAAE,CACR;MACEI,SAAS,EAAE,SAAS;MACpBR,KAAK,EAAE;IACT,CAAC,EACD;MACEQ,SAAS,EAAE,QAAQ;MACnBR,KAAK,EAAEP;IACT,CAAC;EAEL,CAAC;EACD,MAAMkE,mBAAmB,GAAG;IAC1BlD,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACT3B,QAAQ,EAAEoC,QAAQ;IAClBf,QAAQ,EAAE,CACRsD,uBAAuB,EACvB,GAAGrD,QAAQ,EACX,GAAGiB,aAAa,EAChB,GAAGM,SAAS,EACZG,MAAM,EACNQ,MAAM,EACN,GAAGS,UAAU,EACbC,IAAI,EACJI,KAAK,CACN;IACDG,UAAU,EAAE,IAAI;IAChBI,OAAO,EAAE;EACX,CAAC;EACD,MAAMC,QAAQ,GAAG;IACfrD,SAAS,EAAE,UAAU;IACrBR,KAAK,EAAE/B,SAAS,CAAC,UAAU,CAAC;IAC5BmC,QAAQ,EAAE,CACRkD,eAAe,EACfG,kBAAkB,EAClBE,mBAAmB,EACnB5D,UAAU,CACX;IACD6D,OAAO,EAAE,CACP,IAAI,EACJ,GAAG;EAEP,CAAC;;EAED;EACA;EACA,MAAME,cAAc,GAAG;IACrBtD,SAAS,EAAE,UAAU;IACrBR,KAAK,EAAE,oCAAoC;IAC3CjB,QAAQ,EAAE;MACRL,OAAO,EAAE,4BAA4B;MACrC0C,QAAQ,EAAE;IACZ,CAAC;IACDhB,QAAQ,EAAE,CACRqD,kBAAkB,EAClBE,mBAAmB,EACnB5D,UAAU,CACX;IACD6D,OAAO,EAAE;EACX,CAAC;EACD;EACA,MAAMG,oBAAoB,GAAG;IAC3BR,aAAa,EAAE,UAAU;IACzB7C,GAAG,EAAEZ,IAAI,CAACkE,gBAAgB;IAC1B5D,QAAQ,EAAE,CACR;MACEI,SAAS,EAAE,OAAO;MAClBR,KAAK,EAAEV,QAAQ;MACfkE,UAAU,EAAE,IAAI;MAChBvD,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;;EAED;EACA,MAAMgE,eAAe,GAAG;IACtBV,aAAa,EAAE,iBAAiB;IAChC7C,GAAG,EAAEZ,IAAI,CAACkE,gBAAgB;IAC1B5D,QAAQ,EAAE,CACR;MACEI,SAAS,EAAE,OAAO;MAClBR,KAAK,EAAEN,cAAc;MACrBO,SAAS,EAAE;IACb,CAAC,EACD;MACEQ,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRT,SAAS,EAAE,CAAC;MACZuD,UAAU,EAAE,IAAI;MAChBzE,QAAQ,EAAE,CACR,GAAGE,uBAAuB,EAC1B,GAAGD,QAAQ,CACZ;MACDoB,QAAQ,EAAE,CAAE6C,IAAI;IAClB,CAAC;EAEL,CAAC;;EAED;EACA,KAAK,MAAMiB,OAAO,IAAI3B,MAAM,CAACrB,QAAQ,EAAE;IACrC,MAAMiD,aAAa,GAAGD,OAAO,CAAC9D,QAAQ,CAACgE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACjC,KAAK,KAAK,UAAU,CAAC;IAC9E;IACA+B,aAAa,CAACpF,QAAQ,GAAGoC,QAAQ;IACjC,MAAMmD,QAAQ,GAAG,CACf,GAAGhD,aAAa,EAChB,GAAGG,SAAS,EACZ,GAAGG,SAAS,EACZG,MAAM,EACNQ,MAAM,EACN,GAAGI,WAAW,CACf;IACDwB,aAAa,CAAC/D,QAAQ,GAAG,CACvB,GAAGkE,QAAQ,EACX;MACE7D,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTN,QAAQ,EAAE,CACR,MAAM,EACN,GAAGkE,QAAQ;IAEf,CAAC,CACF;EACH;EAEA,OAAO;IACLC,IAAI,EAAE,OAAO;IACbxF,QAAQ,EAAEoC,QAAQ;IAClBf,QAAQ,EAAE,CACR,GAAGC,QAAQ,EACXwD,QAAQ,EACRC,cAAc,EACd;MACEtD,SAAS,EAAE,OAAO;MAClB+C,aAAa,EAAE,sCAAsC;MACrD7C,GAAG,EAAE,KAAK;MACV8D,UAAU,EAAE,IAAI;MAChBzF,QAAQ,EAAEoC,QAAQ;MAClBf,QAAQ,EAAE,CACRN,IAAI,CAAC2E,OAAO,CAAC3E,IAAI,CAAC4E,UAAU,EAAE;QAC5BjE,KAAK,EAAE;MACT,CAAC,CAAC,EACF,GAAGa,aAAa;IAEpB,CAAC,EACDyC,oBAAoB,EACpBE,eAAe,EACf;MACEV,aAAa,EAAE,QAAQ;MACvB7C,GAAG,EAAE,GAAG;MACRN,QAAQ,EAAE,CAAE,GAAGC,QAAQ,CAAE;MACzBJ,SAAS,EAAE;IACb,CAAC,EACD,GAAGqB,aAAa,EAChB,GAAGG,SAAS,EACZ,GAAGG,SAAS,EACZG,MAAM,EACNQ,MAAM,EACN,GAAGI,WAAW,EACd,GAAGK,UAAU,EACbC,IAAI,EACJI,KAAK;EAET,CAAC;AACH;AAEAsB,MAAM,CAACC,OAAO,GAAG/E,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}