{"ast": null, "code": "/*\nLanguage: Monkey\nDescription: Monkey2 is an easy to use, cross platform, games oriented programming language from Blitz Research.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://blitzresearch.itch.io/monkey2\n*/\n\nfunction monkey(hljs) {\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [{\n      begin: '[$][a-fA-F0-9]+'\n    }, hljs.NUMBER_MODE]\n  };\n  return {\n    name: 'Monkey',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'public private property continue exit extern new try catch ' + 'eachin not abstract final select case default const local global field ' + 'end if then else elseif endif while wend repeat until forever for ' + 'to step next return module inline throw import',\n      built_in: 'DebugLog DebugStop Error Print ACos ACosr ASin ASinr ATan ATan2 ATan2r ATanr Abs Abs Ceil ' + 'Clamp Clamp Cos Cosr Exp Floor Log Max Max Min Min Pow Sgn Sgn Sin Sinr Sqrt Tan Tanr Seed PI HALFPI TWOPI',\n      literal: 'true false null and or shl shr mod'\n    },\n    illegal: /\\/\\*/,\n    contains: [hljs.COMMENT('#rem', '#end'), hljs.COMMENT(\"'\", '$', {\n      relevance: 0\n    }), {\n      className: 'function',\n      beginKeywords: 'function method',\n      end: '[(=:]|$',\n      illegal: /\\n/,\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      className: 'class',\n      beginKeywords: 'class interface',\n      end: '$',\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      className: 'built_in',\n      begin: '\\\\b(self|super)\\\\b'\n    }, {\n      className: 'meta',\n      begin: '\\\\s*#',\n      end: '$',\n      keywords: {\n        'meta-keyword': 'if else elseif endif end then'\n      }\n    }, {\n      className: 'meta',\n      begin: '^\\\\s*strict\\\\b'\n    }, {\n      beginKeywords: 'alias',\n      end: '=',\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, hljs.QUOTE_STRING_MODE, NUMBER]\n  };\n}\nmodule.exports = monkey;", "map": {"version": 3, "names": ["monkey", "hljs", "NUMBER", "className", "relevance", "variants", "begin", "NUMBER_MODE", "name", "case_insensitive", "keywords", "keyword", "built_in", "literal", "illegal", "contains", "COMMENT", "beginKeywords", "end", "UNDERSCORE_TITLE_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/monkey.js"], "sourcesContent": ["/*\nLanguage: Monkey\nDescription: Monkey2 is an easy to use, cross platform, games oriented programming language from Blitz Research.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://blitzresearch.itch.io/monkey2\n*/\n\nfunction monkey(hljs) {\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        begin: '[$][a-fA-F0-9]+'\n      },\n      hljs.NUMBER_MODE\n    ]\n  };\n\n  return {\n    name: 'Monkey',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'public private property continue exit extern new try catch ' +\n        'eachin not abstract final select case default const local global field ' +\n        'end if then else elseif endif while wend repeat until forever for ' +\n        'to step next return module inline throw import',\n\n      built_in: 'DebugLog DebugStop Error Print ACos ACosr ASin ASinr ATan ATan2 ATan2r ATanr Abs Abs Ceil ' +\n        'Clamp Clamp Cos Cosr Exp Floor Log Max Max Min Min Pow Sgn Sgn Sin Sinr Sqrt Tan Tanr Seed PI HALFPI TWOPI',\n\n      literal: 'true false null and or shl shr mod'\n    },\n    illegal: /\\/\\*/,\n    contains: [\n      hljs.COMMENT('#rem', '#end'),\n      hljs.COMMENT(\n        \"'\",\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      {\n        className: 'function',\n        beginKeywords: 'function method',\n        end: '[(=:]|$',\n        illegal: /\\n/,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: '$',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        className: 'built_in',\n        begin: '\\\\b(self|super)\\\\b'\n      },\n      {\n        className: 'meta',\n        begin: '\\\\s*#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elseif endif end then'\n        }\n      },\n      {\n        className: 'meta',\n        begin: '^\\\\s*strict\\\\b'\n      },\n      {\n        beginKeywords: 'alias',\n        end: '=',\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      hljs.QUOTE_STRING_MODE,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = monkey;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE;IACT,CAAC,EACDL,IAAI,CAACM,WAAW;EAEpB,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,QAAQ;IACdC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO,EAAE,6DAA6D,GACpE,yEAAyE,GACzE,oEAAoE,GACpE,gDAAgD;MAElDC,QAAQ,EAAE,4FAA4F,GACpG,4GAA4G;MAE9GC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,CACRd,IAAI,CAACe,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,EAC5Bf,IAAI,CAACe,OAAO,CACV,GAAG,EACH,GAAG,EACH;MACEZ,SAAS,EAAE;IACb,CACF,CAAC,EACD;MACED,SAAS,EAAE,UAAU;MACrBc,aAAa,EAAE,iBAAiB;MAChCC,GAAG,EAAE,SAAS;MACdJ,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,CAAEd,IAAI,CAACkB,qBAAqB;IACxC,CAAC,EACD;MACEhB,SAAS,EAAE,OAAO;MAClBc,aAAa,EAAE,iBAAiB;MAChCC,GAAG,EAAE,GAAG;MACRH,QAAQ,EAAE,CACR;QACEE,aAAa,EAAE;MACjB,CAAC,EACDhB,IAAI,CAACkB,qBAAqB;IAE9B,CAAC,EACD;MACEhB,SAAS,EAAE,UAAU;MACrBG,KAAK,EAAE;IACT,CAAC,EACD;MACEH,SAAS,EAAE,MAAM;MACjBG,KAAK,EAAE,OAAO;MACdY,GAAG,EAAE,GAAG;MACRR,QAAQ,EAAE;QACR,cAAc,EAAE;MAClB;IACF,CAAC,EACD;MACEP,SAAS,EAAE,MAAM;MACjBG,KAAK,EAAE;IACT,CAAC,EACD;MACEW,aAAa,EAAE,OAAO;MACtBC,GAAG,EAAE,GAAG;MACRH,QAAQ,EAAE,CAAEd,IAAI,CAACkB,qBAAqB;IACxC,CAAC,EACDlB,IAAI,CAACmB,iBAAiB,EACtBlB,MAAM;EAEV,CAAC;AACH;AAEAmB,MAAM,CAACC,OAAO,GAAGtB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}