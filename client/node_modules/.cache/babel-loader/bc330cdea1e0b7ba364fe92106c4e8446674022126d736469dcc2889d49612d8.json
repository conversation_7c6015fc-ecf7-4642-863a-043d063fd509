{"ast": null, "code": "'use strict';\n\nmodule.exports = tcl;\ntcl.displayName = 'tcl';\ntcl.aliases = [];\nfunction tcl(Prism) {\n  Prism.languages.tcl = {\n    comment: {\n      pattern: /(^|[^\\\\])#.*/,\n      lookbehind: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"/,\n      greedy: true\n    },\n    variable: [{\n      pattern: /(\\$)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/,\n      lookbehind: true\n    }, {\n      pattern: /(\\$)\\{[^}]+\\}/,\n      lookbehind: true\n    }, {\n      pattern: /(^[\\t ]*set[ \\t]+)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/m,\n      lookbehind: true\n    }],\n    function: {\n      pattern: /(^[\\t ]*proc[ \\t]+)\\S+/m,\n      lookbehind: true\n    },\n    builtin: [{\n      pattern: /(^[\\t ]*)(?:break|class|continue|error|eval|exit|for|foreach|if|proc|return|switch|while)\\b/m,\n      lookbehind: true\n    }, /\\b(?:else|elseif)\\b/],\n    scope: {\n      pattern: /(^[\\t ]*)(?:global|upvar|variable)\\b/m,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    keyword: {\n      pattern: /(^[\\t ]*|\\[)(?:Safe_Base|Tcl|after|append|apply|array|auto_(?:execok|import|load|mkindex|qualify|reset)|automkindex_old|bgerror|binary|catch|cd|chan|clock|close|concat|dde|dict|encoding|eof|exec|expr|fblocked|fconfigure|fcopy|file(?:event|name)?|flush|gets|glob|history|http|incr|info|interp|join|lappend|lassign|lindex|linsert|list|llength|load|lrange|lrepeat|lreplace|lreverse|lsearch|lset|lsort|math(?:func|op)|memory|msgcat|namespace|open|package|parray|pid|pkg_mkIndex|platform|puts|pwd|re_syntax|read|refchan|regexp|registry|regsub|rename|scan|seek|set|socket|source|split|string|subst|tcl(?:_endOfWord|_findLibrary|startOf(?:Next|Previous)Word|test|vars|wordBreak(?:After|Before))|tell|time|tm|trace|unknown|unload|unset|update|uplevel|vwait)\\b/m,\n      lookbehind: true\n    },\n    operator: /!=?|\\*\\*?|==|&&?|\\|\\|?|<[=<]?|>[=>]?|[-+~\\/%?^]|\\b(?:eq|in|ne|ni)\\b/,\n    punctuation: /[{}()\\[\\]]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "tcl", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "string", "greedy", "variable", "function", "builtin", "scope", "alias", "keyword", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/tcl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = tcl\ntcl.displayName = 'tcl'\ntcl.aliases = []\nfunction tcl(Prism) {\n  Prism.languages.tcl = {\n    comment: {\n      pattern: /(^|[^\\\\])#.*/,\n      lookbehind: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"/,\n      greedy: true\n    },\n    variable: [\n      {\n        pattern: /(\\$)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/,\n        lookbehind: true\n      },\n      {\n        pattern: /(\\$)\\{[^}]+\\}/,\n        lookbehind: true\n      },\n      {\n        pattern: /(^[\\t ]*set[ \\t]+)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/m,\n        lookbehind: true\n      }\n    ],\n    function: {\n      pattern: /(^[\\t ]*proc[ \\t]+)\\S+/m,\n      lookbehind: true\n    },\n    builtin: [\n      {\n        pattern:\n          /(^[\\t ]*)(?:break|class|continue|error|eval|exit|for|foreach|if|proc|return|switch|while)\\b/m,\n        lookbehind: true\n      },\n      /\\b(?:else|elseif)\\b/\n    ],\n    scope: {\n      pattern: /(^[\\t ]*)(?:global|upvar|variable)\\b/m,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    keyword: {\n      pattern:\n        /(^[\\t ]*|\\[)(?:Safe_Base|Tcl|after|append|apply|array|auto_(?:execok|import|load|mkindex|qualify|reset)|automkindex_old|bgerror|binary|catch|cd|chan|clock|close|concat|dde|dict|encoding|eof|exec|expr|fblocked|fconfigure|fcopy|file(?:event|name)?|flush|gets|glob|history|http|incr|info|interp|join|lappend|lassign|lindex|linsert|list|llength|load|lrange|lrepeat|lreplace|lreverse|lsearch|lset|lsort|math(?:func|op)|memory|msgcat|namespace|open|package|parray|pid|pkg_mkIndex|platform|puts|pwd|re_syntax|read|refchan|regexp|registry|regsub|rename|scan|seek|set|socket|source|split|string|subst|tcl(?:_endOfWord|_findLibrary|startOf(?:Next|Previous)Word|test|vars|wordBreak(?:After|Before))|tell|time|tm|trace|unknown|unload|unset|update|uplevel|vwait)\\b/m,\n      lookbehind: true\n    },\n    operator:\n      /!=?|\\*\\*?|==|&&?|\\|\\|?|<[=<]?|>[=>]?|[-+~\\/%?^]|\\b(?:eq|in|ne|ni)\\b/,\n    punctuation: /[{}()\\[\\]]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE;MACPC,OAAO,EAAE,cAAc;MACvBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,qCAAqC;MAC9CG,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE,CACR;MACEJ,OAAO,EAAE,mCAAmC;MAC5CC,UAAU,EAAE;IACd,CAAC,EACD;MACED,OAAO,EAAE,eAAe;MACxBC,UAAU,EAAE;IACd,CAAC,EACD;MACED,OAAO,EAAE,kDAAkD;MAC3DC,UAAU,EAAE;IACd,CAAC,CACF;IACDI,QAAQ,EAAE;MACRL,OAAO,EAAE,yBAAyB;MAClCC,UAAU,EAAE;IACd,CAAC;IACDK,OAAO,EAAE,CACP;MACEN,OAAO,EACL,8FAA8F;MAChGC,UAAU,EAAE;IACd,CAAC,EACD,qBAAqB,CACtB;IACDM,KAAK,EAAE;MACLP,OAAO,EAAE,uCAAuC;MAChDC,UAAU,EAAE,IAAI;MAChBO,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPT,OAAO,EACL,kvBAAkvB;MACpvBC,UAAU,EAAE;IACd,CAAC;IACDS,QAAQ,EACN,qEAAqE;IACvEC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}