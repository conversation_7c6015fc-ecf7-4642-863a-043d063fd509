{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Bash\nAuthor: vah <<EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [\"self\", {\n      begin: /:-/,\n      contains: [VAR]\n    } // default values\n    ]\n  };\n  Object.assign(VAR, {\n    className: 'variable',\n    variants: [{\n      begin: concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n      // negative look-ahead tries to avoid matching patterns that are not\n      // Perl at all like $ident$, @ident@, etc.\n      `(?![\\\\w\\\\d])(?![$])`)\n    }, BRACED_VAR]\n  });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/,\n    end: /\\)/,\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: {\n      contains: [hljs.END_SAME_AS_BEGIN({\n        begin: /(\\w+)/,\n        end: /(\\w+)/,\n        className: 'string'\n      })]\n    }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [hljs.BACKSLASH_ESCAPE, VAR, SUBST]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    className: '',\n    begin: /\\\\\"/\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$\\(\\(/,\n    end: /\\)\\)/,\n    contains: [{\n      begin: /\\d+#[0-9a-f]+/,\n      className: \"number\"\n    }, hljs.NUMBER_MODE, VAR]\n  };\n  const SH_LIKE_SHELLS = [\"fish\", \"bash\", \"zsh\", \"sh\", \"csh\", \"ksh\", \"tcsh\", \"dash\", \"scsh\"];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [hljs.inherit(hljs.TITLE_MODE, {\n      begin: /\\w[\\w\\d_]*/\n    })],\n    relevance: 0\n  };\n  return {\n    name: 'Bash',\n    aliases: ['sh', 'zsh'],\n    keywords: {\n      $pattern: /\\b[a-z._-]+\\b/,\n      keyword: 'if then else elif fi for while in do done case esac function',\n      literal: 'true false',\n      built_in:\n      // Shell built-ins\n      // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n      'break cd continue eval exec exit export getopts hash pwd readonly return shift test times ' + 'trap umask unset ' +\n      // Bash built-ins\n      'alias bind builtin caller command declare echo enable help let local logout mapfile printf ' + 'read readarray source type typeset ulimit unalias ' +\n      // Shell modifiers\n      'set shopt ' +\n      // Zsh built-ins\n      'autoload bg bindkey bye cap chdir clone comparguments compcall compctl compdescribe compfiles ' + 'compgroups compquote comptags comptry compvalues dirs disable disown echotc echoti emulate ' + 'fc fg float functions getcap getln history integer jobs kill limit log noglob popd print ' + 'pushd pushln rehash sched setcap setopt stat suspend ttyctl unfunction unhash unlimit ' + 'unsetopt vared wait whence where which zcompile zformat zftp zle zmodload zparseopts zprof ' + 'zpty zregexparse zsocket zstyle ztcp'\n    },\n    contains: [KNOWN_SHEBANG,\n    // to catch known shells and boost relevancy\n    hljs.SHEBANG(),\n    // to catch unknown shells but still highlight the shebang\n    FUNCTION, ARITHMETIC, hljs.HASH_COMMENT_MODE, HERE_DOC, QUOTE_STRING, ESCAPED_QUOTE, APOS_STRING, VAR]\n  };\n}\nmodule.exports = bash;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "bash", "hljs", "VAR", "BRACED_VAR", "begin", "end", "contains", "Object", "assign", "className", "variants", "SUBST", "BACKSLASH_ESCAPE", "HERE_DOC", "starts", "END_SAME_AS_BEGIN", "QUOTE_STRING", "push", "ESCAPED_QUOTE", "APOS_STRING", "ARITHMETIC", "NUMBER_MODE", "SH_LIKE_SHELLS", "KNOWN_SHEBANG", "SHEBANG", "binary", "relevance", "FUNCTION", "returnBegin", "inherit", "TITLE_MODE", "name", "aliases", "keywords", "$pattern", "keyword", "literal", "built_in", "HASH_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/bash.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Bash\nAuthor: vah <<EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end:/\\}/,\n    contains: [\n      \"self\",\n      {\n        begin: /:-/,\n        contains: [ VAR ]\n      } // default values\n    ]\n  };\n  Object.assign(VAR,{\n    className: 'variable',\n    variants: [\n      {begin: concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![\\\\w\\\\d])(?![$])`) },\n      BRACED_VAR\n    ]\n  });\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/, end: /\\)/,\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: {\n      contains: [\n        hljs.END_SAME_AS_BEGIN({\n          begin: /(\\w+)/,\n          end: /(\\w+)/,\n          className: 'string'\n        })\n      ]\n    }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/, end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      SUBST\n    ]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    className: '',\n    begin: /\\\\\"/\n\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/, end: /'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$\\(\\(/,\n    end: /\\)\\)/,\n    contains: [\n      { begin: /\\d+#[0-9a-f]+/, className: \"number\" },\n      hljs.NUMBER_MODE,\n      VAR\n    ]\n  };\n  const SH_LIKE_SHELLS = [\n    \"fish\",\n    \"bash\",\n    \"zsh\",\n    \"sh\",\n    \"csh\",\n    \"ksh\",\n    \"tcsh\",\n    \"dash\",\n    \"scsh\",\n  ];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [hljs.inherit(hljs.TITLE_MODE, {begin: /\\w[\\w\\d_]*/})],\n    relevance: 0\n  };\n\n  return {\n    name: 'Bash',\n    aliases: ['sh', 'zsh'],\n    keywords: {\n      $pattern: /\\b[a-z._-]+\\b/,\n      keyword:\n        'if then else elif fi for while in do done case esac function',\n      literal:\n        'true false',\n      built_in:\n        // Shell built-ins\n        // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n        'break cd continue eval exec exit export getopts hash pwd readonly return shift test times ' +\n        'trap umask unset ' +\n        // Bash built-ins\n        'alias bind builtin caller command declare echo enable help let local logout mapfile printf ' +\n        'read readarray source type typeset ulimit unalias ' +\n        // Shell modifiers\n        'set shopt ' +\n        // Zsh built-ins\n        'autoload bg bindkey bye cap chdir clone comparguments compcall compctl compdescribe compfiles ' +\n        'compgroups compquote comptags comptry compvalues dirs disable disown echotc echoti emulate ' +\n        'fc fg float functions getcap getln history integer jobs kill limit log noglob popd print ' +\n        'pushd pushln rehash sched setcap setopt stat suspend ttyctl unfunction unhash unlimit ' +\n        'unsetopt vared wait whence where which zcompile zformat zftp zle zmodload zparseopts zprof ' +\n        'zpty zregexparse zsocket zstyle ztcp'\n    },\n    contains: [\n      KNOWN_SHEBANG, // to catch known shells and boost relevancy\n      hljs.SHEBANG(), // to catch unknown shells but still highlight the shebang\n      FUNCTION,\n      ARITHMETIC,\n      hljs.HASH_COMMENT_MODE,\n      HERE_DOC,\n      QUOTE_STRING,\n      ESCAPED_QUOTE,\n      APOS_STRING,\n      VAR\n    ]\n  };\n}\n\nmodule.exports = bash;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAC,IAAI;IACRC,QAAQ,EAAE,CACR,MAAM,EACN;MACEF,KAAK,EAAE,IAAI;MACXE,QAAQ,EAAE,CAAEJ,GAAG;IACjB,CAAC,CAAC;IAAA;EAEN,CAAC;EACDK,MAAM,CAACC,MAAM,CAACN,GAAG,EAAC;IAChBO,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,CACR;MAACN,KAAK,EAAEV,MAAM,CAAC,oBAAoB;MACjC;MACA;MACA,qBAAqB;IAAE,CAAC,EAC1BS,UAAU;EAEd,CAAC,CAAC;EAEF,MAAMQ,KAAK,GAAG;IACZF,SAAS,EAAE,OAAO;IAClBL,KAAK,EAAE,MAAM;IAAEC,GAAG,EAAE,IAAI;IACxBC,QAAQ,EAAE,CAACL,IAAI,CAACW,gBAAgB;EAClC,CAAC;EACD,MAAMC,QAAQ,GAAG;IACfT,KAAK,EAAE,gBAAgB;IACvBU,MAAM,EAAE;MACNR,QAAQ,EAAE,CACRL,IAAI,CAACc,iBAAiB,CAAC;QACrBX,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,OAAO;QACZI,SAAS,EAAE;MACb,CAAC,CAAC;IAEN;EACF,CAAC;EACD,MAAMO,YAAY,GAAG;IACnBP,SAAS,EAAE,QAAQ;IACnBL,KAAK,EAAE,GAAG;IAAEC,GAAG,EAAE,GAAG;IACpBC,QAAQ,EAAE,CACRL,IAAI,CAACW,gBAAgB,EACrBV,GAAG,EACHS,KAAK;EAET,CAAC;EACDA,KAAK,CAACL,QAAQ,CAACW,IAAI,CAACD,YAAY,CAAC;EACjC,MAAME,aAAa,GAAG;IACpBT,SAAS,EAAE,EAAE;IACbL,KAAK,EAAE;EAET,CAAC;EACD,MAAMe,WAAW,GAAG;IAClBV,SAAS,EAAE,QAAQ;IACnBL,KAAK,EAAE,GAAG;IAAEC,GAAG,EAAE;EACnB,CAAC;EACD,MAAMe,UAAU,GAAG;IACjBhB,KAAK,EAAE,QAAQ;IACfC,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE,CACR;MAAEF,KAAK,EAAE,eAAe;MAAEK,SAAS,EAAE;IAAS,CAAC,EAC/CR,IAAI,CAACoB,WAAW,EAChBnB,GAAG;EAEP,CAAC;EACD,MAAMoB,cAAc,GAAG,CACrB,MAAM,EACN,MAAM,EACN,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,CACP;EACD,MAAMC,aAAa,GAAGtB,IAAI,CAACuB,OAAO,CAAC;IACjCC,MAAM,EAAE,IAAIH,cAAc,CAACvB,IAAI,CAAC,GAAG,CAAC,GAAG;IACvC2B,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG;IACflB,SAAS,EAAE,UAAU;IACrBL,KAAK,EAAE,2BAA2B;IAClCwB,WAAW,EAAE,IAAI;IACjBtB,QAAQ,EAAE,CAACL,IAAI,CAAC4B,OAAO,CAAC5B,IAAI,CAAC6B,UAAU,EAAE;MAAC1B,KAAK,EAAE;IAAY,CAAC,CAAC,CAAC;IAChEsB,SAAS,EAAE;EACb,CAAC;EAED,OAAO;IACLK,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;IACtBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EACL,8DAA8D;MAChEC,OAAO,EACL,YAAY;MACdC,QAAQ;MACN;MACA;MACA,4FAA4F,GAC5F,mBAAmB;MACnB;MACA,6FAA6F,GAC7F,oDAAoD;MACpD;MACA,YAAY;MACZ;MACA,gGAAgG,GAChG,6FAA6F,GAC7F,2FAA2F,GAC3F,wFAAwF,GACxF,6FAA6F,GAC7F;IACJ,CAAC;IACD/B,QAAQ,EAAE,CACRiB,aAAa;IAAE;IACftB,IAAI,CAACuB,OAAO,CAAC,CAAC;IAAE;IAChBG,QAAQ,EACRP,UAAU,EACVnB,IAAI,CAACqC,iBAAiB,EACtBzB,QAAQ,EACRG,YAAY,EACZE,aAAa,EACbC,WAAW,EACXjB,GAAG;EAEP,CAAC;AACH;AAEAqC,MAAM,CAACC,OAAO,GAAGxC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}