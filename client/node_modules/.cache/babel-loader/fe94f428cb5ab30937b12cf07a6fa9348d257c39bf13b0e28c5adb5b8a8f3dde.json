{"ast": null, "code": "/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/en-us/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = ['bool', 'byte', 'char', 'decimal', 'delegate', 'double', 'dynamic', 'enum', 'float', 'int', 'long', 'nint', 'nuint', 'object', 'sbyte', 'short', 'string', 'ulong', 'uint', 'ushort'];\n  const FUNCTION_MODIFIERS = ['public', 'private', 'protected', 'static', 'internal', 'protected', 'abstract', 'async', 'extern', 'override', 'unsafe', 'virtual', 'new', 'sealed', 'partial'];\n  const LITERAL_KEYWORDS = ['default', 'false', 'null', 'true'];\n  const NORMAL_KEYWORDS = ['abstract', 'as', 'base', 'break', 'case', 'class', 'const', 'continue', 'do', 'else', 'event', 'explicit', 'extern', 'finally', 'fixed', 'for', 'foreach', 'goto', 'if', 'implicit', 'in', 'interface', 'internal', 'is', 'lock', 'namespace', 'new', 'operator', 'out', 'override', 'params', 'private', 'protected', 'public', 'readonly', 'record', 'ref', 'return', 'sealed', 'sizeof', 'stackalloc', 'static', 'struct', 'switch', 'this', 'throw', 'try', 'typeof', 'unchecked', 'unsafe', 'using', 'virtual', 'void', 'volatile', 'while'];\n  const CONTEXTUAL_KEYWORDS = ['add', 'alias', 'and', 'ascending', 'async', 'await', 'by', 'descending', 'equals', 'from', 'get', 'global', 'group', 'init', 'into', 'join', 'let', 'nameof', 'not', 'notnull', 'on', 'or', 'orderby', 'partial', 'remove', 'select', 'set', 'unmanaged', 'value|0', 'var', 'when', 'where', 'with', 'yield'];\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: '[a-zA-Z](\\\\.?\\\\w)*'\n  });\n  const NUMBERS = {\n    className: 'number',\n    variants: [{\n      begin: '\\\\b(0b[01\\']+)'\n    }, {\n      begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)'\n    }, {\n      begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n    }],\n    relevance: 0\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [{\n      begin: '\"\"'\n    }]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, {\n    illegal: /\\n/\n  });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, {\n    illegal: /\\n/\n  });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [{\n      begin: /\\{\\{/\n    }, {\n      begin: /\\}\\}/\n    }, hljs.BACKSLASH_ESCAPE, SUBST_NO_LF]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [{\n      begin: /\\{\\{/\n    }, {\n      begin: /\\}\\}/\n    }, {\n      begin: '\"\"'\n    }, SUBST]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [{\n      begin: /\\{\\{/\n    }, {\n      begin: /\\}\\}/\n    }, {\n      begin: '\"\"'\n    }, SUBST_NO_LF]\n  });\n  SUBST.contains = [INTERPOLATED_VERBATIM_STRING, INTERPOLATED_STRING, VERBATIM_STRING, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, NUMBERS, hljs.C_BLOCK_COMMENT_MODE];\n  SUBST_NO_LF.contains = [INTERPOLATED_VERBATIM_STRING_NO_LF, INTERPOLATED_STRING, VERBATIM_STRING_NO_LF, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, NUMBERS, hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, {\n    illegal: /\\n/\n  })];\n  const STRING = {\n    variants: [INTERPOLATED_VERBATIM_STRING, INTERPOLATED_STRING, VERBATIM_STRING, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n  };\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [{\n      beginKeywords: \"in out\"\n    }, TITLE_MODE]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n  return {\n    name: 'C#',\n    aliases: ['cs', 'c#'],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [hljs.COMMENT('///', '$', {\n      returnBegin: true,\n      contains: [{\n        className: 'doctag',\n        variants: [{\n          begin: '///',\n          relevance: 0\n        }, {\n          begin: '<!--|-->'\n        }, {\n          begin: '</?',\n          end: '>'\n        }]\n      }]\n    }), hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'meta',\n      begin: '#',\n      end: '$',\n      keywords: {\n        'meta-keyword': 'if else elif endif define undef warning error line region endregion pragma checksum'\n      }\n    }, STRING, NUMBERS, {\n      beginKeywords: 'class interface',\n      relevance: 0,\n      end: /[{;=]/,\n      illegal: /[^\\s:,]/,\n      contains: [{\n        beginKeywords: \"where class\"\n      }, TITLE_MODE, GENERIC_MODIFIER, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      beginKeywords: 'namespace',\n      relevance: 0,\n      end: /[{;=]/,\n      illegal: /[^\\s:]/,\n      contains: [TITLE_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      beginKeywords: 'record',\n      relevance: 0,\n      end: /[{;=]/,\n      illegal: /[^\\s:]/,\n      contains: [TITLE_MODE, GENERIC_MODIFIER, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      // [Attributes(\"\")]\n      className: 'meta',\n      begin: '^\\\\s*\\\\[',\n      excludeBegin: true,\n      end: '\\\\]',\n      excludeEnd: true,\n      contains: [{\n        className: 'meta-string',\n        begin: /\"/,\n        end: /\"/\n      }]\n    }, {\n      // Expression keywords prevent 'keyword Name(...)' from being\n      // recognized as a function definition\n      beginKeywords: 'new return throw await else',\n      relevance: 0\n    }, {\n      className: 'function',\n      begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n      returnBegin: true,\n      end: /\\s*[{;=]/,\n      excludeEnd: true,\n      keywords: KEYWORDS,\n      contains: [\n      // prevents these from being highlighted `title`\n      {\n        beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n        relevance: 0\n      }, {\n        begin: hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        contains: [hljs.TITLE_MODE, GENERIC_MODIFIER],\n        relevance: 0\n      }, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [STRING, NUMBERS, hljs.C_BLOCK_COMMENT_MODE]\n      }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, AT_IDENTIFIER]\n  };\n}\nmodule.exports = csharp;", "map": {"version": 3, "names": ["csharp", "hljs", "BUILT_IN_KEYWORDS", "FUNCTION_MODIFIERS", "LITERAL_KEYWORDS", "NORMAL_KEYWORDS", "CONTEXTUAL_KEYWORDS", "KEYWORDS", "keyword", "concat", "built_in", "literal", "TITLE_MODE", "inherit", "begin", "NUMBERS", "className", "variants", "relevance", "VERBATIM_STRING", "end", "contains", "VERBATIM_STRING_NO_LF", "illegal", "SUBST", "keywords", "SUBST_NO_LF", "INTERPOLATED_STRING", "BACKSLASH_ESCAPE", "INTERPOLATED_VERBATIM_STRING", "INTERPOLATED_VERBATIM_STRING_NO_LF", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_BLOCK_COMMENT_MODE", "STRING", "GENERIC_MODIFIER", "beginKeywords", "TYPE_IDENT_RE", "IDENT_RE", "AT_IDENTIFIER", "name", "aliases", "COMMENT", "returnBegin", "C_LINE_COMMENT_MODE", "excludeBegin", "excludeEnd", "join", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/csharp.js"], "sourcesContent": ["/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/en-us/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'equals',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'remove',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: '[a-zA-Z](\\\\.?\\\\w)*'\n  });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(0b[01\\']+)'\n      },\n      {\n        begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)'\n      },\n      {\n        begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n      }\n    ],\n    relevance: 0\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [\n      {\n        begin: '\"\"'\n      }\n    ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, {\n    illegal: /\\n/\n  });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, {\n    illegal: /\\n/\n  });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      {\n        begin: '\"\"'\n      },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      {\n        begin: '\"\"'\n      },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, {\n      illegal: /\\n/\n    })\n  ];\n  const STRING = {\n    variants: [\n      INTERPOLATED_VERBATIM_STRING,\n      INTERPOLATED_STRING,\n      VERBATIM_STRING,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      {\n        beginKeywords: \"in out\"\n      },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                {\n                  begin: '<!--|-->'\n                },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elif endif define undef warning error line region endregion pragma checksum'\n        }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          {\n            beginKeywords: \"where class\"\n          },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'meta-string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nmodule.exports = csharp;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,iBAAiB,GAAG,CACxB,MAAM,EACN,MAAM,EACN,MAAM,EACN,SAAS,EACT,UAAU,EACV,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,MAAM,EACN,QAAQ,CACT;EACD,MAAMC,kBAAkB,GAAG,CACzB,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,UAAU,EACV,WAAW,EACX,UAAU,EACV,OAAO,EACP,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,SAAS,EACT,KAAK,EACL,QAAQ,EACR,SAAS,CACV;EACD,MAAMC,gBAAgB,GAAG,CACvB,SAAS,EACT,OAAO,EACP,MAAM,EACN,MAAM,CACP;EACD,MAAMC,eAAe,GAAG,CACtB,UAAU,EACV,IAAI,EACJ,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,UAAU,EACV,IAAI,EACJ,MAAM,EACN,OAAO,EACP,UAAU,EACV,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EACL,SAAS,EACT,MAAM,EACN,IAAI,EACJ,UAAU,EACV,IAAI,EACJ,WAAW,EACX,UAAU,EACV,IAAI,EACJ,MAAM,EACN,WAAW,EACX,KAAK,EACL,UAAU,EACV,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,OAAO,CACR;EACD,MAAMC,mBAAmB,GAAG,CAC1B,KAAK,EACL,OAAO,EACP,KAAK,EACL,WAAW,EACX,OAAO,EACP,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,KAAK,EACL,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,WAAW,EACX,SAAS,EACT,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,CACR;EAED,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAEH,eAAe,CAACI,MAAM,CAACH,mBAAmB,CAAC;IACpDI,QAAQ,EAAER,iBAAiB;IAC3BS,OAAO,EAAEP;EACX,CAAC;EACD,MAAMQ,UAAU,GAAGX,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACW,UAAU,EAAE;IAC/CE,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MACEH,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,CACF;IACDI,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,eAAe,GAAG;IACtBH,SAAS,EAAE,QAAQ;IACnBF,KAAK,EAAE,IAAI;IACXM,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMQ,qBAAqB,GAAGrB,IAAI,CAACY,OAAO,CAACM,eAAe,EAAE;IAC1DI,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMC,KAAK,GAAG;IACZR,SAAS,EAAE,OAAO;IAClBF,KAAK,EAAE,IAAI;IACXM,GAAG,EAAE,IAAI;IACTK,QAAQ,EAAElB;EACZ,CAAC;EACD,MAAMmB,WAAW,GAAGzB,IAAI,CAACY,OAAO,CAACW,KAAK,EAAE;IACtCD,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMI,mBAAmB,GAAG;IAC1BX,SAAS,EAAE,QAAQ;IACnBF,KAAK,EAAE,KAAK;IACZM,GAAG,EAAE,GAAG;IACRG,OAAO,EAAE,IAAI;IACbF,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACDb,IAAI,CAAC2B,gBAAgB,EACrBF,WAAW;EAEf,CAAC;EACD,MAAMG,4BAA4B,GAAG;IACnCb,SAAS,EAAE,QAAQ;IACnBF,KAAK,EAAE,MAAM;IACbM,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACDU,KAAK;EAET,CAAC;EACD,MAAMM,kCAAkC,GAAG7B,IAAI,CAACY,OAAO,CAACgB,4BAA4B,EAAE;IACpFN,OAAO,EAAE,IAAI;IACbF,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACDY,WAAW;EAEf,CAAC,CAAC;EACFF,KAAK,CAACH,QAAQ,GAAG,CACfQ,4BAA4B,EAC5BF,mBAAmB,EACnBR,eAAe,EACflB,IAAI,CAAC8B,gBAAgB,EACrB9B,IAAI,CAAC+B,iBAAiB,EACtBjB,OAAO,EACPd,IAAI,CAACgC,oBAAoB,CAC1B;EACDP,WAAW,CAACL,QAAQ,GAAG,CACrBS,kCAAkC,EAClCH,mBAAmB,EACnBL,qBAAqB,EACrBrB,IAAI,CAAC8B,gBAAgB,EACrB9B,IAAI,CAAC+B,iBAAiB,EACtBjB,OAAO,EACPd,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACgC,oBAAoB,EAAE;IACtCV,OAAO,EAAE;EACX,CAAC,CAAC,CACH;EACD,MAAMW,MAAM,GAAG;IACbjB,QAAQ,EAAE,CACRY,4BAA4B,EAC5BF,mBAAmB,EACnBR,eAAe,EACflB,IAAI,CAAC8B,gBAAgB,EACrB9B,IAAI,CAAC+B,iBAAiB;EAE1B,CAAC;EAED,MAAMG,gBAAgB,GAAG;IACvBrB,KAAK,EAAE,GAAG;IACVM,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACR;MACEe,aAAa,EAAE;IACjB,CAAC,EACDxB,UAAU;EAEd,CAAC;EACD,MAAMyB,aAAa,GAAGpC,IAAI,CAACqC,QAAQ,GAAG,IAAI,GAAGrC,IAAI,CAACqC,QAAQ,GAAG,YAAY,GAAGrC,IAAI,CAACqC,QAAQ,GAAG,gBAAgB;EAC5G,MAAMC,aAAa,GAAG;IACpB;IACA;IACAzB,KAAK,EAAE,GAAG,GAAGb,IAAI,CAACqC,QAAQ;IAC1BpB,SAAS,EAAE;EACb,CAAC;EAED,OAAO;IACLsB,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,CACP,IAAI,EACJ,IAAI,CACL;IACDhB,QAAQ,EAAElB,QAAQ;IAClBgB,OAAO,EAAE,IAAI;IACbF,QAAQ,EAAE,CACRpB,IAAI,CAACyC,OAAO,CACV,KAAK,EACL,GAAG,EACH;MACEC,WAAW,EAAE,IAAI;MACjBtB,QAAQ,EAAE,CACR;QACEL,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE,CACR;UACEH,KAAK,EAAE,KAAK;UACZI,SAAS,EAAE;QACb,CAAC,EACD;UACEJ,KAAK,EAAE;QACT,CAAC,EACD;UACEA,KAAK,EAAE,KAAK;UACZM,GAAG,EAAE;QACP,CAAC;MAEL,CAAC;IAEL,CACF,CAAC,EACDnB,IAAI,CAAC2C,mBAAmB,EACxB3C,IAAI,CAACgC,oBAAoB,EACzB;MACEjB,SAAS,EAAE,MAAM;MACjBF,KAAK,EAAE,GAAG;MACVM,GAAG,EAAE,GAAG;MACRK,QAAQ,EAAE;QACR,cAAc,EAAE;MAClB;IACF,CAAC,EACDS,MAAM,EACNnB,OAAO,EACP;MACEqB,aAAa,EAAE,iBAAiB;MAChClB,SAAS,EAAE,CAAC;MACZE,GAAG,EAAE,OAAO;MACZG,OAAO,EAAE,SAAS;MAClBF,QAAQ,EAAE,CACR;QACEe,aAAa,EAAE;MACjB,CAAC,EACDxB,UAAU,EACVuB,gBAAgB,EAChBlC,IAAI,CAAC2C,mBAAmB,EACxB3C,IAAI,CAACgC,oBAAoB;IAE7B,CAAC,EACD;MACEG,aAAa,EAAE,WAAW;MAC1BlB,SAAS,EAAE,CAAC;MACZE,GAAG,EAAE,OAAO;MACZG,OAAO,EAAE,QAAQ;MACjBF,QAAQ,EAAE,CACRT,UAAU,EACVX,IAAI,CAAC2C,mBAAmB,EACxB3C,IAAI,CAACgC,oBAAoB;IAE7B,CAAC,EACD;MACEG,aAAa,EAAE,QAAQ;MACvBlB,SAAS,EAAE,CAAC;MACZE,GAAG,EAAE,OAAO;MACZG,OAAO,EAAE,QAAQ;MACjBF,QAAQ,EAAE,CACRT,UAAU,EACVuB,gBAAgB,EAChBlC,IAAI,CAAC2C,mBAAmB,EACxB3C,IAAI,CAACgC,oBAAoB;IAE7B,CAAC,EACD;MACE;MACAjB,SAAS,EAAE,MAAM;MACjBF,KAAK,EAAE,UAAU;MACjB+B,YAAY,EAAE,IAAI;MAClBzB,GAAG,EAAE,KAAK;MACV0B,UAAU,EAAE,IAAI;MAChBzB,QAAQ,EAAE,CACR;QACEL,SAAS,EAAE,aAAa;QACxBF,KAAK,EAAE,GAAG;QACVM,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACD;MACE;MACA;MACAgB,aAAa,EAAE,6BAA6B;MAC5ClB,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,UAAU;MACrBF,KAAK,EAAE,GAAG,GAAGuB,aAAa,GAAG,QAAQ,GAAGpC,IAAI,CAACqC,QAAQ,GAAG,oBAAoB;MAC5EK,WAAW,EAAE,IAAI;MACjBvB,GAAG,EAAE,UAAU;MACf0B,UAAU,EAAE,IAAI;MAChBrB,QAAQ,EAAElB,QAAQ;MAClBc,QAAQ,EAAE;MACR;MACA;QACEe,aAAa,EAAEjC,kBAAkB,CAAC4C,IAAI,CAAC,GAAG,CAAC;QAC3C7B,SAAS,EAAE;MACb,CAAC,EACD;QACEJ,KAAK,EAAEb,IAAI,CAACqC,QAAQ,GAAG,oBAAoB;QAC3CK,WAAW,EAAE,IAAI;QACjBtB,QAAQ,EAAE,CACRpB,IAAI,CAACW,UAAU,EACfuB,gBAAgB,CACjB;QACDjB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,SAAS,EAAE,QAAQ;QACnBF,KAAK,EAAE,IAAI;QACXM,GAAG,EAAE,IAAI;QACTyB,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBrB,QAAQ,EAAElB,QAAQ;QAClBW,SAAS,EAAE,CAAC;QACZG,QAAQ,EAAE,CACRa,MAAM,EACNnB,OAAO,EACPd,IAAI,CAACgC,oBAAoB;MAE7B,CAAC,EACDhC,IAAI,CAAC2C,mBAAmB,EACxB3C,IAAI,CAACgC,oBAAoB;IAE7B,CAAC,EACDM,aAAa;EAEjB,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGjD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}