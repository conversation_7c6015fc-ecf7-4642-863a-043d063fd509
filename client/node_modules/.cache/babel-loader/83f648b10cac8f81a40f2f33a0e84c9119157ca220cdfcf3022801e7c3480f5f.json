{"ast": null, "code": "'use strict';\n\nmodule.exports = ocaml;\nocaml.displayName = 'ocaml';\nocaml.aliases = [];\nfunction ocaml(Prism) {\n  // https://ocaml.org/manual/lex.html\n  Prism.languages.ocaml = {\n    comment: {\n      pattern: /\\(\\*[\\s\\S]*?\\*\\)/,\n      greedy: true\n    },\n    char: {\n      pattern: /'(?:[^\\\\\\r\\n']|\\\\(?:.|[ox]?[0-9a-f]{1,3}))'/i,\n      greedy: true\n    },\n    string: [{\n      pattern: /\"(?:\\\\(?:[\\s\\S]|\\r\\n)|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    }, {\n      pattern: /\\{([a-z_]*)\\|[\\s\\S]*?\\|\\1\\}/,\n      greedy: true\n    }],\n    number: [\n    // binary and octal\n    /\\b(?:0b[01][01_]*|0o[0-7][0-7_]*)\\b/i,\n    // hexadecimal\n    /\\b0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]*)?(?:p[+-]?\\d[\\d_]*)?(?!\\w)/i,\n    // decimal\n    /\\b\\d[\\d_]*(?:\\.[\\d_]*)?(?:e[+-]?\\d[\\d_]*)?(?!\\w)/i],\n    directive: {\n      pattern: /\\B#\\w+/,\n      alias: 'property'\n    },\n    label: {\n      pattern: /\\B~\\w+/,\n      alias: 'property'\n    },\n    'type-variable': {\n      pattern: /\\B'\\w+/,\n      alias: 'function'\n    },\n    variant: {\n      pattern: /`\\w+/,\n      alias: 'symbol'\n    },\n    // For the list of keywords and operators,\n    // see: http://caml.inria.fr/pub/docs/manual-ocaml/lex.html#sec84\n    keyword: /\\b(?:as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|match|method|module|mutable|new|nonrec|object|of|open|private|rec|sig|struct|then|to|try|type|val|value|virtual|when|where|while|with)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    'operator-like-punctuation': {\n      pattern: /\\[[<>|]|[>|]\\]|\\{<|>\\}/,\n      alias: 'punctuation'\n    },\n    // Custom operators are allowed\n    operator: /\\.[.~]|:[=>]|[=<>@^|&+\\-*\\/$%!?~][!$%&*+\\-.\\/:<=>?@^|~]*|\\b(?:and|asr|land|lor|lsl|lsr|lxor|mod|or)\\b/,\n    punctuation: /;;|::|[(){}\\[\\].,:;#]|\\b_\\b/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "ocaml", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "char", "string", "number", "directive", "alias", "label", "variant", "keyword", "boolean", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/ocaml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ocaml\nocaml.displayName = 'ocaml'\nocaml.aliases = []\nfunction ocaml(Prism) {\n  // https://ocaml.org/manual/lex.html\n  Prism.languages.ocaml = {\n    comment: {\n      pattern: /\\(\\*[\\s\\S]*?\\*\\)/,\n      greedy: true\n    },\n    char: {\n      pattern: /'(?:[^\\\\\\r\\n']|\\\\(?:.|[ox]?[0-9a-f]{1,3}))'/i,\n      greedy: true\n    },\n    string: [\n      {\n        pattern: /\"(?:\\\\(?:[\\s\\S]|\\r\\n)|[^\\\\\\r\\n\"])*\"/,\n        greedy: true\n      },\n      {\n        pattern: /\\{([a-z_]*)\\|[\\s\\S]*?\\|\\1\\}/,\n        greedy: true\n      }\n    ],\n    number: [\n      // binary and octal\n      /\\b(?:0b[01][01_]*|0o[0-7][0-7_]*)\\b/i, // hexadecimal\n      /\\b0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]*)?(?:p[+-]?\\d[\\d_]*)?(?!\\w)/i, // decimal\n      /\\b\\d[\\d_]*(?:\\.[\\d_]*)?(?:e[+-]?\\d[\\d_]*)?(?!\\w)/i\n    ],\n    directive: {\n      pattern: /\\B#\\w+/,\n      alias: 'property'\n    },\n    label: {\n      pattern: /\\B~\\w+/,\n      alias: 'property'\n    },\n    'type-variable': {\n      pattern: /\\B'\\w+/,\n      alias: 'function'\n    },\n    variant: {\n      pattern: /`\\w+/,\n      alias: 'symbol'\n    },\n    // For the list of keywords and operators,\n    // see: http://caml.inria.fr/pub/docs/manual-ocaml/lex.html#sec84\n    keyword:\n      /\\b(?:as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|match|method|module|mutable|new|nonrec|object|of|open|private|rec|sig|struct|then|to|try|type|val|value|virtual|when|where|while|with)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    'operator-like-punctuation': {\n      pattern: /\\[[<>|]|[>|]\\]|\\{<|>\\}/,\n      alias: 'punctuation'\n    },\n    // Custom operators are allowed\n    operator:\n      /\\.[.~]|:[=>]|[=<>@^|&+\\-*\\/$%!?~][!$%&*+\\-.\\/:<=>?@^|~]*|\\b(?:and|asr|land|lor|lsl|lsr|lxor|mod|or)\\b/,\n    punctuation: /;;|::|[(){}\\[\\].,:;#]|\\b_\\b/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EACAA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE;MACPC,OAAO,EAAE,kBAAkB;MAC3BC,MAAM,EAAE;IACV,CAAC;IACDC,IAAI,EAAE;MACJF,OAAO,EAAE,8CAA8C;MACvDC,MAAM,EAAE;IACV,CAAC;IACDE,MAAM,EAAE,CACN;MACEH,OAAO,EAAE,qCAAqC;MAC9CC,MAAM,EAAE;IACV,CAAC,EACD;MACED,OAAO,EAAE,6BAA6B;MACtCC,MAAM,EAAE;IACV,CAAC,CACF;IACDG,MAAM,EAAE;IACN;IACA,sCAAsC;IAAE;IACxC,mEAAmE;IAAE;IACrE,mDAAmD,CACpD;IACDC,SAAS,EAAE;MACTL,OAAO,EAAE,QAAQ;MACjBM,KAAK,EAAE;IACT,CAAC;IACDC,KAAK,EAAE;MACLP,OAAO,EAAE,QAAQ;MACjBM,KAAK,EAAE;IACT,CAAC;IACD,eAAe,EAAE;MACfN,OAAO,EAAE,QAAQ;MACjBM,KAAK,EAAE;IACT,CAAC;IACDE,OAAO,EAAE;MACPR,OAAO,EAAE,MAAM;MACfM,KAAK,EAAE;IACT,CAAC;IACD;IACA;IACAG,OAAO,EACL,+RAA+R;IACjSC,OAAO,EAAE,oBAAoB;IAC7B,2BAA2B,EAAE;MAC3BV,OAAO,EAAE,wBAAwB;MACjCM,KAAK,EAAE;IACT,CAAC;IACD;IACAK,QAAQ,EACN,uGAAuG;IACzGC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}