{"ast": null, "code": "import axios from 'axios';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || '/api',\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log(`🚀 API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  console.error('❌ Request Error:', error);\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  console.log(`✅ API Response: ${response.status} ${response.config.url}`);\n  return response.data;\n}, error => {\n  var _error$response, _error$response2, _error$response2$data;\n  console.error('❌ Response Error:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n\n  // 统一错误处理\n  const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || 'Network error';\n  return Promise.reject(new Error(errorMessage));\n});\n\n// 粘贴文本相关API\nexport const pasteAPI = {\n  /**\n   * 上传长文本\n   * @param {string} content - 文本内容\n   * @param {string} filename - 文件名\n   * @returns {Promise<Object>} 上传结果\n   */\n  async upload(content, filename) {\n    try {\n      const response = await api.post('/paste/upload', {\n        content,\n        filename: filename || `paste-${Date.now()}.txt`\n      });\n      return response;\n    } catch (error) {\n      throw new Error(`Upload failed: ${error.message}`);\n    }\n  },\n  /**\n   * 获取文件列表\n   * @returns {Promise<Object>} 文件列表\n   */\n  async getList() {\n    try {\n      const response = await api.get('/paste/list');\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to get file list: ${error.message}`);\n    }\n  },\n  /**\n   * 获取特定文件内容\n   * @param {string} fileId - 文件ID\n   * @returns {Promise<Object>} 文件内容\n   */\n  async getFile(fileId) {\n    try {\n      const response = await api.get(`/paste/${fileId}`);\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to get file: ${error.message}`);\n    }\n  },\n  /**\n   * 删除文件\n   * @param {string} fileId - 文件ID\n   * @returns {Promise<Object>} 删除结果\n   */\n  async deleteFile(fileId) {\n    try {\n      const response = await api.delete(`/paste/${fileId}`);\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to delete file: ${error.message}`);\n    }\n  },\n  /**\n   * 获取统计信息\n   * @returns {Promise<Object>} 统计信息\n   */\n  async getStats() {\n    try {\n      const response = await api.get('/stats');\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to get stats: ${error.message}`);\n    }\n  }\n};\n\n// 工具函数\nexport const utils = {\n  /**\n   * 检查是否为长文本\n   * @param {string} text - 文本内容\n   * @param {number} threshold - 阈值（默认1000字符）\n   * @returns {boolean} 是否为长文本\n   */\n  isLongText(text, threshold = 1000) {\n    return text && text.length > threshold;\n  },\n  /**\n   * 生成文本预览\n   * @param {string} text - 文本内容\n   * @param {number} maxLength - 最大长度（默认500字符）\n   * @returns {string} 预览文本\n   */\n  generatePreview(text, maxLength = 500) {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  },\n  /**\n   * 格式化文件大小\n   * @param {number} bytes - 字节数\n   * @returns {string} 格式化后的大小\n   */\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  },\n  /**\n   * 格式化时间戳\n   * @param {number} timestamp - 时间戳\n   * @returns {string} 格式化后的时间\n   */\n  formatTimestamp(timestamp) {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diff = now - date;\n\n    // 小于1分钟\n    if (diff < 60000) {\n      return 'Just now';\n    }\n\n    // 小于1小时\n    if (diff < 3600000) {\n      const minutes = Math.floor(diff / 60000);\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    }\n\n    // 小于1天\n    if (diff < 86400000) {\n      const hours = Math.floor(diff / 3600000);\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    }\n\n    // 超过1天，显示具体日期\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  },\n  /**\n   * 检测文本类型\n   * @param {string} content - 文本内容\n   * @returns {string} 文本类型\n   */\n  detectTextType(content) {\n    if (!content) return 'text';\n\n    // 检测JSON\n    try {\n      JSON.parse(content);\n      return 'json';\n    } catch (e) {}\n\n    // 检测代码\n    const codePatterns = [/function\\s+\\w+\\s*\\(/, /class\\s+\\w+/, /import\\s+.*from/, /<\\w+.*>/, /def\\s+\\w+\\s*\\(/, /public\\s+class/];\n    if (codePatterns.some(pattern => pattern.test(content))) {\n      return 'code';\n    }\n\n    // 检测Markdown\n    if (/^#{1,6}\\s/.test(content) || /\\*\\*.*\\*\\*/.test(content) || /\\[.*\\]\\(.*\\)/.test(content)) {\n      return 'markdown';\n    }\n    return 'text';\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "data", "_error$response", "_error$response2", "_error$response2$data", "message", "errorMessage", "Error", "pasteAPI", "upload", "content", "filename", "post", "Date", "now", "getList", "get", "getFile", "fileId", "deleteFile", "delete", "getStats", "utils", "isLongText", "text", "threshold", "length", "generatePreview", "max<PERSON><PERSON><PERSON>", "substring", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "formatTimestamp", "timestamp", "date", "diff", "minutes", "hours", "toLocaleDateString", "toLocaleTimeString", "detectTextType", "JSON", "parse", "e", "codePatterns", "some", "pattern", "test"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || '/api',\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('❌ Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    console.log(`✅ API Response: ${response.status} ${response.config.url}`);\n    return response.data;\n  },\n  (error) => {\n    console.error('❌ Response Error:', error.response?.data || error.message);\n    \n    // 统一错误处理\n    const errorMessage = error.response?.data?.error || error.message || 'Network error';\n    return Promise.reject(new Error(errorMessage));\n  }\n);\n\n// 粘贴文本相关API\nexport const pasteAPI = {\n  /**\n   * 上传长文本\n   * @param {string} content - 文本内容\n   * @param {string} filename - 文件名\n   * @returns {Promise<Object>} 上传结果\n   */\n  async upload(content, filename) {\n    try {\n      const response = await api.post('/paste/upload', {\n        content,\n        filename: filename || `paste-${Date.now()}.txt`\n      });\n      return response;\n    } catch (error) {\n      throw new Error(`Upload failed: ${error.message}`);\n    }\n  },\n\n  /**\n   * 获取文件列表\n   * @returns {Promise<Object>} 文件列表\n   */\n  async getList() {\n    try {\n      const response = await api.get('/paste/list');\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to get file list: ${error.message}`);\n    }\n  },\n\n  /**\n   * 获取特定文件内容\n   * @param {string} fileId - 文件ID\n   * @returns {Promise<Object>} 文件内容\n   */\n  async getFile(fileId) {\n    try {\n      const response = await api.get(`/paste/${fileId}`);\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to get file: ${error.message}`);\n    }\n  },\n\n  /**\n   * 删除文件\n   * @param {string} fileId - 文件ID\n   * @returns {Promise<Object>} 删除结果\n   */\n  async deleteFile(fileId) {\n    try {\n      const response = await api.delete(`/paste/${fileId}`);\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to delete file: ${error.message}`);\n    }\n  },\n\n  /**\n   * 获取统计信息\n   * @returns {Promise<Object>} 统计信息\n   */\n  async getStats() {\n    try {\n      const response = await api.get('/stats');\n      return response;\n    } catch (error) {\n      throw new Error(`Failed to get stats: ${error.message}`);\n    }\n  }\n};\n\n// 工具函数\nexport const utils = {\n  /**\n   * 检查是否为长文本\n   * @param {string} text - 文本内容\n   * @param {number} threshold - 阈值（默认1000字符）\n   * @returns {boolean} 是否为长文本\n   */\n  isLongText(text, threshold = 1000) {\n    return text && text.length > threshold;\n  },\n\n  /**\n   * 生成文本预览\n   * @param {string} text - 文本内容\n   * @param {number} maxLength - 最大长度（默认500字符）\n   * @returns {string} 预览文本\n   */\n  generatePreview(text, maxLength = 500) {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  },\n\n  /**\n   * 格式化文件大小\n   * @param {number} bytes - 字节数\n   * @returns {string} 格式化后的大小\n   */\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  },\n\n  /**\n   * 格式化时间戳\n   * @param {number} timestamp - 时间戳\n   * @returns {string} 格式化后的时间\n   */\n  formatTimestamp(timestamp) {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diff = now - date;\n    \n    // 小于1分钟\n    if (diff < 60000) {\n      return 'Just now';\n    }\n    \n    // 小于1小时\n    if (diff < 3600000) {\n      const minutes = Math.floor(diff / 60000);\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    }\n    \n    // 小于1天\n    if (diff < 86400000) {\n      const hours = Math.floor(diff / 3600000);\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    }\n    \n    // 超过1天，显示具体日期\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  },\n\n  /**\n   * 检测文本类型\n   * @param {string} content - 文本内容\n   * @returns {string} 文本类型\n   */\n  detectTextType(content) {\n    if (!content) return 'text';\n    \n    // 检测JSON\n    try {\n      JSON.parse(content);\n      return 'json';\n    } catch (e) {}\n    \n    // 检测代码\n    const codePatterns = [\n      /function\\s+\\w+\\s*\\(/,\n      /class\\s+\\w+/,\n      /import\\s+.*from/,\n      /<\\w+.*>/,\n      /def\\s+\\w+\\s*\\(/,\n      /public\\s+class/\n    ];\n    \n    if (codePatterns.some(pattern => pattern.test(content))) {\n      return 'code';\n    }\n    \n    // 检测Markdown\n    if (/^#{1,6}\\s/.test(content) || /\\*\\*.*\\*\\*/.test(content) || /\\[.*\\]\\(.*\\)/.test(content)) {\n      return 'markdown';\n    }\n    \n    return 'text';\n  }\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,MAAM;EAChDC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,oBAAAF,cAAA,GAAmBD,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIL,MAAM,CAACM,GAAG,EAAE,CAAC;EAC5E,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACTL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;EACxC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAlB,GAAG,CAACQ,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZR,OAAO,CAACC,GAAG,CAAC,mBAAmBO,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACV,MAAM,CAACM,GAAG,EAAE,CAAC;EACxE,OAAOI,QAAQ,CAACE,IAAI;AACtB,CAAC,EACAL,KAAK,IAAK;EAAA,IAAAM,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACTb,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAE,EAAAM,eAAA,GAAAN,KAAK,CAACG,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBD,IAAI,KAAIL,KAAK,CAACS,OAAO,CAAC;;EAEzE;EACA,MAAMC,YAAY,GAAG,EAAAH,gBAAA,GAAAP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBF,IAAI,cAAAG,qBAAA,uBAApBA,qBAAA,CAAsBR,KAAK,KAAIA,KAAK,CAACS,OAAO,IAAI,eAAe;EACpF,OAAOR,OAAO,CAACC,MAAM,CAAC,IAAIS,KAAK,CAACD,YAAY,CAAC,CAAC;AAChD,CACF,CAAC;;AAED;AACA,OAAO,MAAME,QAAQ,GAAG;EACtB;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,MAAMA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC9B,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMrB,GAAG,CAACkC,IAAI,CAAC,eAAe,EAAE;QAC/CF,OAAO;QACPC,QAAQ,EAAEA,QAAQ,IAAI,SAASE,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;MACF,OAAOf,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAM,IAAIW,KAAK,CAAC,kBAAkBX,KAAK,CAACS,OAAO,EAAE,CAAC;IACpD;EACF,CAAC;EAED;AACF;AACA;AACA;EACE,MAAMU,OAAOA,CAAA,EAAG;IACd,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMrB,GAAG,CAACsC,GAAG,CAAC,aAAa,CAAC;MAC7C,OAAOjB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAM,IAAIW,KAAK,CAAC,4BAA4BX,KAAK,CAACS,OAAO,EAAE,CAAC;IAC9D;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMY,OAAOA,CAACC,MAAM,EAAE;IACpB,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMrB,GAAG,CAACsC,GAAG,CAAC,UAAUE,MAAM,EAAE,CAAC;MAClD,OAAOnB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAM,IAAIW,KAAK,CAAC,uBAAuBX,KAAK,CAACS,OAAO,EAAE,CAAC;IACzD;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMc,UAAUA,CAACD,MAAM,EAAE;IACvB,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMrB,GAAG,CAAC0C,MAAM,CAAC,UAAUF,MAAM,EAAE,CAAC;MACrD,OAAOnB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAM,IAAIW,KAAK,CAAC,0BAA0BX,KAAK,CAACS,OAAO,EAAE,CAAC;IAC5D;EACF,CAAC;EAED;AACF;AACA;AACA;EACE,MAAMgB,QAAQA,CAAA,EAAG;IACf,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMrB,GAAG,CAACsC,GAAG,CAAC,QAAQ,CAAC;MACxC,OAAOjB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAM,IAAIW,KAAK,CAAC,wBAAwBX,KAAK,CAACS,OAAO,EAAE,CAAC;IAC1D;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMiB,KAAK,GAAG;EACnB;AACF;AACA;AACA;AACA;AACA;EACEC,UAAUA,CAACC,IAAI,EAAEC,SAAS,GAAG,IAAI,EAAE;IACjC,OAAOD,IAAI,IAAIA,IAAI,CAACE,MAAM,GAAGD,SAAS;EACxC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEE,eAAeA,CAACH,IAAI,EAAEI,SAAS,GAAG,GAAG,EAAE;IACrC,IAAI,CAACJ,IAAI,EAAE,OAAO,EAAE;IACpB,IAAIA,IAAI,CAACE,MAAM,IAAIE,SAAS,EAAE,OAAOJ,IAAI;IACzC,OAAOA,IAAI,CAACK,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,cAAcA,CAACC,KAAK,EAAE;IACpB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC3C,GAAG,CAACuC,KAAK,CAAC,GAAGI,IAAI,CAAC3C,GAAG,CAACwC,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED;AACF;AACA;AACA;AACA;EACEM,eAAeA,CAACC,SAAS,EAAE;IACzB,MAAMC,IAAI,GAAG,IAAI7B,IAAI,CAAC4B,SAAS,CAAC;IAChC,MAAM3B,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAM8B,IAAI,GAAG7B,GAAG,GAAG4B,IAAI;;IAEvB;IACA,IAAIC,IAAI,GAAG,KAAK,EAAE;MAChB,OAAO,UAAU;IACnB;;IAEA;IACA,IAAIA,IAAI,GAAG,OAAO,EAAE;MAClB,MAAMC,OAAO,GAAGT,IAAI,CAACC,KAAK,CAACO,IAAI,GAAG,KAAK,CAAC;MACxC,OAAO,GAAGC,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACzD;;IAEA;IACA,IAAID,IAAI,GAAG,QAAQ,EAAE;MACnB,MAAME,KAAK,GAAGV,IAAI,CAACC,KAAK,CAACO,IAAI,GAAG,OAAO,CAAC;MACxC,OAAO,GAAGE,KAAK,QAAQA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACnD;;IAEA;IACA,OAAOH,IAAI,CAACI,kBAAkB,CAAC,CAAC,GAAG,GAAG,GAAGJ,IAAI,CAACK,kBAAkB,CAAC,CAAC;EACpE,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAACtC,OAAO,EAAE;IACtB,IAAI,CAACA,OAAO,EAAE,OAAO,MAAM;;IAE3B;IACA,IAAI;MACFuC,IAAI,CAACC,KAAK,CAACxC,OAAO,CAAC;MACnB,OAAO,MAAM;IACf,CAAC,CAAC,OAAOyC,CAAC,EAAE,CAAC;;IAEb;IACA,MAAMC,YAAY,GAAG,CACnB,qBAAqB,EACrB,aAAa,EACb,iBAAiB,EACjB,SAAS,EACT,gBAAgB,EAChB,gBAAgB,CACjB;IAED,IAAIA,YAAY,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC7C,OAAO,CAAC,CAAC,EAAE;MACvD,OAAO,MAAM;IACf;;IAEA;IACA,IAAI,WAAW,CAAC6C,IAAI,CAAC7C,OAAO,CAAC,IAAI,YAAY,CAAC6C,IAAI,CAAC7C,OAAO,CAAC,IAAI,cAAc,CAAC6C,IAAI,CAAC7C,OAAO,CAAC,EAAE;MAC3F,OAAO,UAAU;IACnB;IAEA,OAAO,MAAM;EACf;AACF,CAAC;AAED,eAAehC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}