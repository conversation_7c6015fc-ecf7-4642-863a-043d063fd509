{"ast": null, "code": "'use strict';\n\nmodule.exports = Info;\nvar proto = Info.prototype;\nproto.space = null;\nproto.attribute = null;\nproto.property = null;\nproto.boolean = false;\nproto.booleanish = false;\nproto.overloadedBoolean = false;\nproto.number = false;\nproto.commaSeparated = false;\nproto.spaceSeparated = false;\nproto.commaOrSpaceSeparated = false;\nproto.mustUseProperty = false;\nproto.defined = false;\nfunction Info(property, attribute) {\n  this.property = property;\n  this.attribute = attribute;\n}", "map": {"version": 3, "names": ["module", "exports", "Info", "proto", "prototype", "space", "attribute", "property", "boolean", "booleanish", "overloadedBoolean", "number", "commaSeparated", "spaceSeparated", "commaOrSpaceSeparated", "mustUseProperty", "defined"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/lib/util/info.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = Info\n\nvar proto = Info.prototype\n\nproto.space = null\nproto.attribute = null\nproto.property = null\nproto.boolean = false\nproto.booleanish = false\nproto.overloadedBoolean = false\nproto.number = false\nproto.commaSeparated = false\nproto.spaceSeparated = false\nproto.commaOrSpaceSeparated = false\nproto.mustUseProperty = false\nproto.defined = false\n\nfunction Info(property, attribute) {\n  this.property = property\n  this.attribute = attribute\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AAErB,IAAIC,KAAK,GAAGD,IAAI,CAACE,SAAS;AAE1BD,KAAK,CAACE,KAAK,GAAG,IAAI;AAClBF,KAAK,CAACG,SAAS,GAAG,IAAI;AACtBH,KAAK,CAACI,QAAQ,GAAG,IAAI;AACrBJ,KAAK,CAACK,OAAO,GAAG,KAAK;AACrBL,KAAK,CAACM,UAAU,GAAG,KAAK;AACxBN,KAAK,CAACO,iBAAiB,GAAG,KAAK;AAC/BP,KAAK,CAACQ,MAAM,GAAG,KAAK;AACpBR,KAAK,CAACS,cAAc,GAAG,KAAK;AAC5BT,KAAK,CAACU,cAAc,GAAG,KAAK;AAC5BV,KAAK,CAACW,qBAAqB,GAAG,KAAK;AACnCX,KAAK,CAACY,eAAe,GAAG,KAAK;AAC7BZ,KAAK,CAACa,OAAO,GAAG,KAAK;AAErB,SAASd,IAAIA,CAACK,QAAQ,EAAED,SAAS,EAAE;EACjC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACD,SAAS,GAAGA,SAAS;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}