{"ast": null, "code": "'use strict';\n\nmodule.exports = elixir;\nelixir.displayName = 'elixir';\nelixir.aliases = [];\nfunction elixir(Prism) {\n  Prism.languages.elixir = {\n    doc: {\n      pattern: /@(?:doc|moduledoc)\\s+(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2)/,\n      inside: {\n        attribute: /^@\\w+/,\n        string: /['\"][\\s\\S]+/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    // ~r\"\"\"foo\"\"\" (multi-line), ~r'''foo''' (multi-line), ~r/foo/, ~r|foo|, ~r\"foo\", ~r'foo', ~r(foo), ~r[foo], ~r{foo}, ~r<foo>\n    regex: {\n      pattern: /~[rR](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|[^\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[uismxfr]*/,\n      greedy: true\n    },\n    string: [{\n      // ~s\"\"\"foo\"\"\" (multi-line), ~s'''foo''' (multi-line), ~s/foo/, ~s|foo|, ~s\"foo\", ~s'foo', ~s(foo), ~s[foo], ~s{foo} (with interpolation care), ~s<foo>\n      pattern: /~[cCsSwW](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|#\\{[^}]+\\}|#(?!\\{)|[^#\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[csa]?/,\n      greedy: true,\n      inside: {\n        // See interpolation below\n      }\n    }, {\n      pattern: /(\"\"\"|''')[\\s\\S]*?\\1/,\n      greedy: true,\n      inside: {\n        // See interpolation below\n      }\n    }, {\n      // Multi-line strings are allowed\n      pattern: /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        // See interpolation below\n      }\n    }],\n    atom: {\n      // Look-behind prevents bad highlighting of the :: operator\n      pattern: /(^|[^:]):\\w+/,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    module: {\n      pattern: /\\b[A-Z]\\w*\\b/,\n      alias: 'class-name'\n    },\n    // Look-ahead prevents bad highlighting of the :: operator\n    'attr-name': /\\b\\w+\\??:(?!:)/,\n    argument: {\n      // Look-behind prevents bad highlighting of the && operator\n      pattern: /(^|[^&])&\\d+/,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'variable'\n    },\n    function: /\\b[_a-zA-Z]\\w*[?!]?(?:(?=\\s*(?:\\.\\s*)?\\()|(?=\\/\\d))/,\n    number: /\\b(?:0[box][a-f\\d_]+|\\d[\\d_]*)(?:\\.[\\d_]+)?(?:e[+-]?[\\d_]+)?\\b/i,\n    keyword: /\\b(?:after|alias|and|case|catch|cond|def(?:callback|delegate|exception|impl|macro|module|n|np|p|protocol|struct)?|do|else|end|fn|for|if|import|not|or|quote|raise|require|rescue|try|unless|unquote|use|when)\\b/,\n    boolean: /\\b(?:false|nil|true)\\b/,\n    operator: [/\\bin\\b|&&?|\\|[|>]?|\\\\\\\\|::|\\.\\.\\.?|\\+\\+?|-[->]?|<[-=>]|>=|!==?|\\B!|=(?:==?|[>~])?|[*\\/^]/, {\n      // We don't want to match <<\n      pattern: /([^<])<(?!<)/,\n      lookbehind: true\n    }, {\n      // We don't want to match >>\n      pattern: /([^>])>(?!>)/,\n      lookbehind: true\n    }],\n    punctuation: /<<|>>|[.,%\\[\\]{}()]/\n  };\n  Prism.languages.elixir.string.forEach(function (o) {\n    o.inside = {\n      interpolation: {\n        pattern: /#\\{[^}]+\\}/,\n        inside: {\n          delimiter: {\n            pattern: /^#\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          rest: Prism.languages.elixir\n        }\n      }\n    };\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "elixir", "displayName", "aliases", "Prism", "languages", "doc", "pattern", "inside", "attribute", "string", "comment", "greedy", "regex", "atom", "lookbehind", "alias", "argument", "function", "number", "keyword", "boolean", "operator", "punctuation", "for<PERSON>ach", "o", "interpolation", "delimiter", "rest"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/elixir.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = elixir\nelixir.displayName = 'elixir'\nelixir.aliases = []\nfunction elixir(Prism) {\n  Prism.languages.elixir = {\n    doc: {\n      pattern:\n        /@(?:doc|moduledoc)\\s+(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2)/,\n      inside: {\n        attribute: /^@\\w+/,\n        string: /['\"][\\s\\S]+/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    // ~r\"\"\"foo\"\"\" (multi-line), ~r'''foo''' (multi-line), ~r/foo/, ~r|foo|, ~r\"foo\", ~r'foo', ~r(foo), ~r[foo], ~r{foo}, ~r<foo>\n    regex: {\n      pattern:\n        /~[rR](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|[^\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[uismxfr]*/,\n      greedy: true\n    },\n    string: [\n      {\n        // ~s\"\"\"foo\"\"\" (multi-line), ~s'''foo''' (multi-line), ~s/foo/, ~s|foo|, ~s\"foo\", ~s'foo', ~s(foo), ~s[foo], ~s{foo} (with interpolation care), ~s<foo>\n        pattern:\n          /~[cCsSwW](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|#\\{[^}]+\\}|#(?!\\{)|[^#\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[csa]?/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      },\n      {\n        pattern: /(\"\"\"|''')[\\s\\S]*?\\1/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      },\n      {\n        // Multi-line strings are allowed\n        pattern: /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      }\n    ],\n    atom: {\n      // Look-behind prevents bad highlighting of the :: operator\n      pattern: /(^|[^:]):\\w+/,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    module: {\n      pattern: /\\b[A-Z]\\w*\\b/,\n      alias: 'class-name'\n    },\n    // Look-ahead prevents bad highlighting of the :: operator\n    'attr-name': /\\b\\w+\\??:(?!:)/,\n    argument: {\n      // Look-behind prevents bad highlighting of the && operator\n      pattern: /(^|[^&])&\\d+/,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'variable'\n    },\n    function: /\\b[_a-zA-Z]\\w*[?!]?(?:(?=\\s*(?:\\.\\s*)?\\()|(?=\\/\\d))/,\n    number: /\\b(?:0[box][a-f\\d_]+|\\d[\\d_]*)(?:\\.[\\d_]+)?(?:e[+-]?[\\d_]+)?\\b/i,\n    keyword:\n      /\\b(?:after|alias|and|case|catch|cond|def(?:callback|delegate|exception|impl|macro|module|n|np|p|protocol|struct)?|do|else|end|fn|for|if|import|not|or|quote|raise|require|rescue|try|unless|unquote|use|when)\\b/,\n    boolean: /\\b(?:false|nil|true)\\b/,\n    operator: [\n      /\\bin\\b|&&?|\\|[|>]?|\\\\\\\\|::|\\.\\.\\.?|\\+\\+?|-[->]?|<[-=>]|>=|!==?|\\B!|=(?:==?|[>~])?|[*\\/^]/,\n      {\n        // We don't want to match <<\n        pattern: /([^<])<(?!<)/,\n        lookbehind: true\n      },\n      {\n        // We don't want to match >>\n        pattern: /([^>])>(?!>)/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /<<|>>|[.,%\\[\\]{}()]/\n  }\n  Prism.languages.elixir.string.forEach(function (o) {\n    o.inside = {\n      interpolation: {\n        pattern: /#\\{[^}]+\\}/,\n        inside: {\n          delimiter: {\n            pattern: /^#\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          rest: Prism.languages.elixir\n        }\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,GAAG,EAAE;MACHC,OAAO,EACL,4FAA4F;MAC9FC,MAAM,EAAE;QACNC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE;MACV;IACF,CAAC;IACDC,OAAO,EAAE;MACPJ,OAAO,EAAE,KAAK;MACdK,MAAM,EAAE;IACV,CAAC;IACD;IACAC,KAAK,EAAE;MACLN,OAAO,EACL,4LAA4L;MAC9LK,MAAM,EAAE;IACV,CAAC;IACDF,MAAM,EAAE,CACN;MACE;MACAH,OAAO,EACL,gNAAgN;MAClNK,MAAM,EAAE,IAAI;MACZJ,MAAM,EAAE;QACN;MAAA;IAEJ,CAAC,EACD;MACED,OAAO,EAAE,qBAAqB;MAC9BK,MAAM,EAAE,IAAI;MACZJ,MAAM,EAAE;QACN;MAAA;IAEJ,CAAC,EACD;MACE;MACAD,OAAO,EAAE,+CAA+C;MACxDK,MAAM,EAAE,IAAI;MACZJ,MAAM,EAAE;QACN;MAAA;IAEJ,CAAC,CACF;IACDM,IAAI,EAAE;MACJ;MACAP,OAAO,EAAE,cAAc;MACvBQ,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDjB,MAAM,EAAE;MACNQ,OAAO,EAAE,cAAc;MACvBS,KAAK,EAAE;IACT,CAAC;IACD;IACA,WAAW,EAAE,gBAAgB;IAC7BC,QAAQ,EAAE;MACR;MACAV,OAAO,EAAE,cAAc;MACvBQ,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDP,SAAS,EAAE;MACTF,OAAO,EAAE,MAAM;MACfS,KAAK,EAAE;IACT,CAAC;IACDE,QAAQ,EAAE,qDAAqD;IAC/DC,MAAM,EAAE,iEAAiE;IACzEC,OAAO,EACL,iNAAiN;IACnNC,OAAO,EAAE,wBAAwB;IACjCC,QAAQ,EAAE,CACR,0FAA0F,EAC1F;MACE;MACAf,OAAO,EAAE,cAAc;MACvBQ,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAR,OAAO,EAAE,cAAc;MACvBQ,UAAU,EAAE;IACd,CAAC,CACF;IACDQ,WAAW,EAAE;EACf,CAAC;EACDnB,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACS,MAAM,CAACc,OAAO,CAAC,UAAUC,CAAC,EAAE;IACjDA,CAAC,CAACjB,MAAM,GAAG;MACTkB,aAAa,EAAE;QACbnB,OAAO,EAAE,YAAY;QACrBC,MAAM,EAAE;UACNmB,SAAS,EAAE;YACTpB,OAAO,EAAE,UAAU;YACnBS,KAAK,EAAE;UACT,CAAC;UACDY,IAAI,EAAExB,KAAK,CAACC,SAAS,CAACJ;QACxB;MACF;IACF,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}