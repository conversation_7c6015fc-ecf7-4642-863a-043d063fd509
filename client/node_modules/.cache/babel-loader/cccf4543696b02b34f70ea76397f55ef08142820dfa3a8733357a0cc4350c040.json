{"ast": null, "code": "/*\nLanguage: HAML\nRequires: ruby.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://haml.info\nCategory: template\n*/\n\n// TODO support filter tags like :javascript, support inline HTML\nfunction haml(hljs) {\n  return {\n    name: 'HAML',\n    case_insensitive: true,\n    contains: [{\n      className: 'meta',\n      begin: '^!!!( (5|1\\\\.1|Strict|Frameset|Basic|Mobile|RDFa|XML\\\\b.*))?$',\n      relevance: 10\n    },\n    // FIXME these comments should be allowed to span indented lines\n    hljs.COMMENT('^\\\\s*(!=#|=#|-#|/).*$', false, {\n      relevance: 0\n    }), {\n      begin: '^\\\\s*(-|=|!=)(?!#)',\n      starts: {\n        end: '\\\\n',\n        subLanguage: 'ruby'\n      }\n    }, {\n      className: 'tag',\n      begin: '^\\\\s*%',\n      contains: [{\n        className: 'selector-tag',\n        begin: '\\\\w+'\n      }, {\n        className: 'selector-id',\n        begin: '#[\\\\w-]+'\n      }, {\n        className: 'selector-class',\n        begin: '\\\\.[\\\\w-]+'\n      }, {\n        begin: /\\{\\s*/,\n        end: /\\s*\\}/,\n        contains: [{\n          begin: ':\\\\w+\\\\s*=>',\n          end: ',\\\\s+',\n          returnBegin: true,\n          endsWithParent: true,\n          contains: [{\n            className: 'attr',\n            begin: ':\\\\w+'\n          }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n            begin: '\\\\w+',\n            relevance: 0\n          }]\n        }]\n      }, {\n        begin: '\\\\(\\\\s*',\n        end: '\\\\s*\\\\)',\n        excludeEnd: true,\n        contains: [{\n          begin: '\\\\w+\\\\s*=',\n          end: '\\\\s+',\n          returnBegin: true,\n          endsWithParent: true,\n          contains: [{\n            className: 'attr',\n            begin: '\\\\w+',\n            relevance: 0\n          }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n            begin: '\\\\w+',\n            relevance: 0\n          }]\n        }]\n      }]\n    }, {\n      begin: '^\\\\s*[=~]\\\\s*'\n    }, {\n      begin: /#\\{/,\n      starts: {\n        end: /\\}/,\n        subLanguage: 'ruby'\n      }\n    }]\n  };\n}\nmodule.exports = haml;", "map": {"version": 3, "names": ["haml", "hljs", "name", "case_insensitive", "contains", "className", "begin", "relevance", "COMMENT", "starts", "end", "subLanguage", "returnBegin", "endsWithParent", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "excludeEnd", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/haml.js"], "sourcesContent": ["/*\nLanguage: HAML\nRequires: ruby.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://haml.info\nCategory: template\n*/\n\n// TODO support filter tags like :javascript, support inline HTML\nfunction haml(hljs) {\n  return {\n    name: 'HAML',\n    case_insensitive: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: '^!!!( (5|1\\\\.1|Strict|Frameset|Basic|Mobile|RDFa|XML\\\\b.*))?$',\n        relevance: 10\n      },\n      // FIXME these comments should be allowed to span indented lines\n      hljs.COMMENT(\n        '^\\\\s*(!=#|=#|-#|/).*$',\n        false,\n        {\n          relevance: 0\n        }\n      ),\n      {\n        begin: '^\\\\s*(-|=|!=)(?!#)',\n        starts: {\n          end: '\\\\n',\n          subLanguage: 'ruby'\n        }\n      },\n      {\n        className: 'tag',\n        begin: '^\\\\s*%',\n        contains: [\n          {\n            className: 'selector-tag',\n            begin: '\\\\w+'\n          },\n          {\n            className: 'selector-id',\n            begin: '#[\\\\w-]+'\n          },\n          {\n            className: 'selector-class',\n            begin: '\\\\.[\\\\w-]+'\n          },\n          {\n            begin: /\\{\\s*/,\n            end: /\\s*\\}/,\n            contains: [\n              {\n                begin: ':\\\\w+\\\\s*=>',\n                end: ',\\\\s+',\n                returnBegin: true,\n                endsWithParent: true,\n                contains: [\n                  {\n                    className: 'attr',\n                    begin: ':\\\\w+'\n                  },\n                  hljs.APOS_STRING_MODE,\n                  hljs.QUOTE_STRING_MODE,\n                  {\n                    begin: '\\\\w+',\n                    relevance: 0\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            begin: '\\\\(\\\\s*',\n            end: '\\\\s*\\\\)',\n            excludeEnd: true,\n            contains: [\n              {\n                begin: '\\\\w+\\\\s*=',\n                end: '\\\\s+',\n                returnBegin: true,\n                endsWithParent: true,\n                contains: [\n                  {\n                    className: 'attr',\n                    begin: '\\\\w+',\n                    relevance: 0\n                  },\n                  hljs.APOS_STRING_MODE,\n                  hljs.QUOTE_STRING_MODE,\n                  {\n                    begin: '\\\\w+',\n                    relevance: 0\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      {\n        begin: '^\\\\s*[=~]\\\\s*'\n      },\n      {\n        begin: /#\\{/,\n        starts: {\n          end: /\\}/,\n          subLanguage: 'ruby'\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = haml;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,+DAA+D;MACtEC,SAAS,EAAE;IACb,CAAC;IACD;IACAN,IAAI,CAACO,OAAO,CACV,uBAAuB,EACvB,KAAK,EACL;MACED,SAAS,EAAE;IACb,CACF,CAAC,EACD;MACED,KAAK,EAAE,oBAAoB;MAC3BG,MAAM,EAAE;QACNC,GAAG,EAAE,KAAK;QACVC,WAAW,EAAE;MACf;IACF,CAAC,EACD;MACEN,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE,QAAQ;MACfF,QAAQ,EAAE,CACR;QACEC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,SAAS,EAAE,gBAAgB;QAC3BC,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE,OAAO;QACdI,GAAG,EAAE,OAAO;QACZN,QAAQ,EAAE,CACR;UACEE,KAAK,EAAE,aAAa;UACpBI,GAAG,EAAE,OAAO;UACZE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,IAAI;UACpBT,QAAQ,EAAE,CACR;YACEC,SAAS,EAAE,MAAM;YACjBC,KAAK,EAAE;UACT,CAAC,EACDL,IAAI,CAACa,gBAAgB,EACrBb,IAAI,CAACc,iBAAiB,EACtB;YACET,KAAK,EAAE,MAAM;YACbC,SAAS,EAAE;UACb,CAAC;QAEL,CAAC;MAEL,CAAC,EACD;QACED,KAAK,EAAE,SAAS;QAChBI,GAAG,EAAE,SAAS;QACdM,UAAU,EAAE,IAAI;QAChBZ,QAAQ,EAAE,CACR;UACEE,KAAK,EAAE,WAAW;UAClBI,GAAG,EAAE,MAAM;UACXE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,IAAI;UACpBT,QAAQ,EAAE,CACR;YACEC,SAAS,EAAE,MAAM;YACjBC,KAAK,EAAE,MAAM;YACbC,SAAS,EAAE;UACb,CAAC,EACDN,IAAI,CAACa,gBAAgB,EACrBb,IAAI,CAACc,iBAAiB,EACtB;YACET,KAAK,EAAE,MAAM;YACbC,SAAS,EAAE;UACb,CAAC;QAEL,CAAC;MAEL,CAAC;IAEL,CAAC,EACD;MACED,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,KAAK;MACZG,MAAM,EAAE;QACNC,GAAG,EAAE,IAAI;QACTC,WAAW,EAAE;MACf;IACF,CAAC;EAEL,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGlB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}