{"ast": null, "code": "'use strict';\n\nvar refractorC = require('./c.js');\nmodule.exports = bison;\nbison.displayName = 'bison';\nbison.aliases = [];\nfunction bison(Prism) {\n  Prism.register(refractorC);\n  Prism.languages.bison = Prism.languages.extend('c', {});\n  Prism.languages.insertBefore('bison', 'comment', {\n    bison: {\n      // This should match all the beginning of the file\n      // including the prologue(s), the bison declarations and\n      // the grammar rules.\n      pattern: /^(?:[^%]|%(?!%))*%%[\\s\\S]*?%%/,\n      inside: {\n        c: {\n          // Allow for one level of nested braces\n          pattern: /%\\{[\\s\\S]*?%\\}|\\{(?:\\{[^}]*\\}|[^{}])*\\}/,\n          inside: {\n            delimiter: {\n              pattern: /^%?\\{|%?\\}$/,\n              alias: 'punctuation'\n            },\n            'bison-variable': {\n              pattern: /[$@](?:<[^\\s>]+>)?[\\w$]+/,\n              alias: 'variable',\n              inside: {\n                punctuation: /<|>/\n              }\n            },\n            rest: Prism.languages.c\n          }\n        },\n        comment: Prism.languages.c.comment,\n        string: Prism.languages.c.string,\n        property: /\\S+(?=:)/,\n        keyword: /%\\w+/,\n        number: {\n          pattern: /(^|[^@])\\b(?:0x[\\da-f]+|\\d+)/i,\n          lookbehind: true\n        },\n        punctuation: /%[%?]|[|:;\\[\\]<>]/\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["refractorC", "require", "module", "exports", "bison", "displayName", "aliases", "Prism", "register", "languages", "extend", "insertBefore", "pattern", "inside", "c", "delimiter", "alias", "punctuation", "rest", "comment", "string", "property", "keyword", "number", "lookbehind"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/bison.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = bison\nbison.displayName = 'bison'\nbison.aliases = []\nfunction bison(Prism) {\n  Prism.register(refractorC)\n  Prism.languages.bison = Prism.languages.extend('c', {})\n  Prism.languages.insertBefore('bison', 'comment', {\n    bison: {\n      // This should match all the beginning of the file\n      // including the prologue(s), the bison declarations and\n      // the grammar rules.\n      pattern: /^(?:[^%]|%(?!%))*%%[\\s\\S]*?%%/,\n      inside: {\n        c: {\n          // Allow for one level of nested braces\n          pattern: /%\\{[\\s\\S]*?%\\}|\\{(?:\\{[^}]*\\}|[^{}])*\\}/,\n          inside: {\n            delimiter: {\n              pattern: /^%?\\{|%?\\}$/,\n              alias: 'punctuation'\n            },\n            'bison-variable': {\n              pattern: /[$@](?:<[^\\s>]+>)?[\\w$]+/,\n              alias: 'variable',\n              inside: {\n                punctuation: /<|>/\n              }\n            },\n            rest: Prism.languages.c\n          }\n        },\n        comment: Prism.languages.c.comment,\n        string: Prism.languages.c.string,\n        property: /\\S+(?=:)/,\n        keyword: /%\\w+/,\n        number: {\n          pattern: /(^|[^@])\\b(?:0x[\\da-f]+|\\d+)/i,\n          lookbehind: true\n        },\n        punctuation: /%[%?]|[|:;\\[\\]<>]/\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClCC,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACR,UAAU,CAAC;EAC1BO,KAAK,CAACE,SAAS,CAACL,KAAK,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACvDH,KAAK,CAACE,SAAS,CAACE,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE;IAC/CP,KAAK,EAAE;MACL;MACA;MACA;MACAQ,OAAO,EAAE,+BAA+B;MACxCC,MAAM,EAAE;QACNC,CAAC,EAAE;UACD;UACAF,OAAO,EAAE,yCAAyC;UAClDC,MAAM,EAAE;YACNE,SAAS,EAAE;cACTH,OAAO,EAAE,aAAa;cACtBI,KAAK,EAAE;YACT,CAAC;YACD,gBAAgB,EAAE;cAChBJ,OAAO,EAAE,0BAA0B;cACnCI,KAAK,EAAE,UAAU;cACjBH,MAAM,EAAE;gBACNI,WAAW,EAAE;cACf;YACF,CAAC;YACDC,IAAI,EAAEX,KAAK,CAACE,SAAS,CAACK;UACxB;QACF,CAAC;QACDK,OAAO,EAAEZ,KAAK,CAACE,SAAS,CAACK,CAAC,CAACK,OAAO;QAClCC,MAAM,EAAEb,KAAK,CAACE,SAAS,CAACK,CAAC,CAACM,MAAM;QAChCC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE;UACNX,OAAO,EAAE,+BAA+B;UACxCY,UAAU,EAAE;QACd,CAAC;QACDP,WAAW,EAAE;MACf;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}