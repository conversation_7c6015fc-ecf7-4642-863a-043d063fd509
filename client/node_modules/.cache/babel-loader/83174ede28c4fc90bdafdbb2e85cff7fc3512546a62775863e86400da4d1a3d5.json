{"ast": null, "code": "'use strict';\n\nmodule.exports = turtle;\nturtle.displayName = 'turtle';\nturtle.aliases = [];\nfunction turtle(Prism) {\n  Prism.languages.turtle = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'multiline-string': {\n      pattern: /\"\"\"(?:(?:\"\"?)?(?:[^\"\\\\]|\\\\.))*\"\"\"|'''(?:(?:''?)?(?:[^'\\\\]|\\\\.))*'''/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        comment: /#.*/\n      }\n    },\n    string: {\n      pattern: /\"(?:[^\\\\\"\\r\\n]|\\\\.)*\"|'(?:[^\\\\'\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    url: {\n      pattern: /<(?:[^\\x00-\\x20<>\"{}|^`\\\\]|\\\\(?:u[\\da-fA-F]{4}|U[\\da-fA-F]{8}))*>/,\n      greedy: true,\n      inside: {\n        punctuation: /[<>]/\n      }\n    },\n    function: {\n      pattern: /(?:(?![-.\\d\\xB7])[-.\\w\\xB7\\xC0-\\uFFFD]+)?:(?:(?![-.])(?:[-.:\\w\\xC0-\\uFFFD]|%[\\da-f]{2}|\\\\.)+)?/i,\n      inside: {\n        'local-name': {\n          pattern: /([^:]*:)[\\s\\S]+/,\n          lookbehind: true\n        },\n        prefix: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    },\n    number: /[+-]?\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    punctuation: /[{}.,;()[\\]]|\\^\\^/,\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: [/(?:\\ba|@prefix|@base)\\b|=/, /\\b(?:base|graph|prefix)\\b/i],\n    tag: {\n      pattern: /@[a-z]+(?:-[a-z\\d]+)*/i,\n      inside: {\n        punctuation: /@/\n      }\n    }\n  };\n  Prism.languages.trig = Prism.languages['turtle'];\n}", "map": {"version": 3, "names": ["module", "exports", "turtle", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "alias", "inside", "string", "url", "punctuation", "function", "lookbehind", "prefix", "number", "boolean", "keyword", "tag", "trig"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/turtle.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = turtle\nturtle.displayName = 'turtle'\nturtle.aliases = []\nfunction turtle(Prism) {\n  Prism.languages.turtle = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'multiline-string': {\n      pattern:\n        /\"\"\"(?:(?:\"\"?)?(?:[^\"\\\\]|\\\\.))*\"\"\"|'''(?:(?:''?)?(?:[^'\\\\]|\\\\.))*'''/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        comment: /#.*/\n      }\n    },\n    string: {\n      pattern: /\"(?:[^\\\\\"\\r\\n]|\\\\.)*\"|'(?:[^\\\\'\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    url: {\n      pattern:\n        /<(?:[^\\x00-\\x20<>\"{}|^`\\\\]|\\\\(?:u[\\da-fA-F]{4}|U[\\da-fA-F]{8}))*>/,\n      greedy: true,\n      inside: {\n        punctuation: /[<>]/\n      }\n    },\n    function: {\n      pattern:\n        /(?:(?![-.\\d\\xB7])[-.\\w\\xB7\\xC0-\\uFFFD]+)?:(?:(?![-.])(?:[-.:\\w\\xC0-\\uFFFD]|%[\\da-f]{2}|\\\\.)+)?/i,\n      inside: {\n        'local-name': {\n          pattern: /([^:]*:)[\\s\\S]+/,\n          lookbehind: true\n        },\n        prefix: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    },\n    number: /[+-]?\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    punctuation: /[{}.,;()[\\]]|\\^\\^/,\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: [/(?:\\ba|@prefix|@base)\\b|=/, /\\b(?:base|graph|prefix)\\b/i],\n    tag: {\n      pattern: /@[a-z]+(?:-[a-z\\d]+)*/i,\n      inside: {\n        punctuation: /@/\n      }\n    }\n  }\n  Prism.languages.trig = Prism.languages['turtle']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACD,kBAAkB,EAAE;MAClBD,OAAO,EACL,qEAAqE;MACvEC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE;QACNJ,OAAO,EAAE;MACX;IACF,CAAC;IACDK,MAAM,EAAE;MACNJ,OAAO,EAAE,6CAA6C;MACtDC,MAAM,EAAE;IACV,CAAC;IACDI,GAAG,EAAE;MACHL,OAAO,EACL,mEAAmE;MACrEC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACNG,WAAW,EAAE;MACf;IACF,CAAC;IACDC,QAAQ,EAAE;MACRP,OAAO,EACL,iGAAiG;MACnGG,MAAM,EAAE;QACN,YAAY,EAAE;UACZH,OAAO,EAAE,iBAAiB;UAC1BQ,UAAU,EAAE;QACd,CAAC;QACDC,MAAM,EAAE;UACNT,OAAO,EAAE,SAAS;UAClBG,MAAM,EAAE;YACNG,WAAW,EAAE;UACf;QACF;MACF;IACF,CAAC;IACDI,MAAM,EAAE,qCAAqC;IAC7CJ,WAAW,EAAE,mBAAmB;IAChCK,OAAO,EAAE,oBAAoB;IAC7BC,OAAO,EAAE,CAAC,2BAA2B,EAAE,4BAA4B,CAAC;IACpEC,GAAG,EAAE;MACHb,OAAO,EAAE,wBAAwB;MACjCG,MAAM,EAAE;QACNG,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACDT,KAAK,CAACC,SAAS,CAACgB,IAAI,GAAGjB,KAAK,CAACC,SAAS,CAAC,QAAQ,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}