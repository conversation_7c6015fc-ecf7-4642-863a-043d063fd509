{"ast": null, "code": "'use strict';\n\n/* global window, self */\n\n// istanbul ignore next - Don't allow Prism to run on page load in browser or\n// to start messaging from workers.\nvar ctx = typeof globalThis === 'object' ? globalThis : typeof self === 'object' ? self : typeof window === 'object' ? window : typeof global === 'object' ? global : {};\nvar restore = capture();\nctx.Prism = {\n  manual: true,\n  disableWorkerMessageHandler: true\n};\n\n// Load all stuff in `prism.js` itself, except for `prism-file-highlight.js`.\n// The wrapped non-leaky grammars are loaded instead of Prism’s originals.\nvar h = require('hastscript');\nvar decode = require('parse-entities');\nvar Prism = require('prismjs/components/prism-core');\nvar markup = require('./lang/markup');\nvar css = require('./lang/css');\nvar clike = require('./lang/clike');\nvar js = require('./lang/javascript');\nrestore();\nvar own = {}.hasOwnProperty;\n\n// Inherit.\nfunction Refractor() {}\nRefractor.prototype = Prism;\n\n// Construct.\nvar refract = new Refractor();\n\n// Expose.\nmodule.exports = refract;\n\n// Create.\nrefract.highlight = highlight;\nrefract.register = register;\nrefract.alias = alias;\nrefract.registered = registered;\nrefract.listLanguages = listLanguages;\n\n// Register bundled grammars.\nregister(markup);\nregister(css);\nregister(clike);\nregister(js);\nrefract.util.encode = encode;\nrefract.Token.stringify = stringify;\nfunction register(grammar) {\n  if (typeof grammar !== 'function' || !grammar.displayName) {\n    throw new Error('Expected `function` for `grammar`, got `' + grammar + '`');\n  }\n\n  // Do not duplicate registrations.\n  if (refract.languages[grammar.displayName] === undefined) {\n    grammar(refract);\n  }\n}\nfunction alias(name, alias) {\n  var languages = refract.languages;\n  var map = name;\n  var key;\n  var list;\n  var length;\n  var index;\n  if (alias) {\n    map = {};\n    map[name] = alias;\n  }\n  for (key in map) {\n    list = map[key];\n    list = typeof list === 'string' ? [list] : list;\n    length = list.length;\n    index = -1;\n    while (++index < length) {\n      languages[list[index]] = languages[key];\n    }\n  }\n}\nfunction highlight(value, name) {\n  var sup = Prism.highlight;\n  var grammar;\n  if (typeof value !== 'string') {\n    throw new Error('Expected `string` for `value`, got `' + value + '`');\n  }\n\n  // `name` is a grammar object.\n  if (refract.util.type(name) === 'Object') {\n    grammar = name;\n    name = null;\n  } else {\n    if (typeof name !== 'string') {\n      throw new Error('Expected `string` for `name`, got `' + name + '`');\n    }\n    if (own.call(refract.languages, name)) {\n      grammar = refract.languages[name];\n    } else {\n      throw new Error('Unknown language: `' + name + '` is not registered');\n    }\n  }\n  return sup.call(this, value, grammar, name);\n}\nfunction registered(language) {\n  if (typeof language !== 'string') {\n    throw new Error('Expected `string` for `language`, got `' + language + '`');\n  }\n  return own.call(refract.languages, language);\n}\nfunction listLanguages() {\n  var languages = refract.languages;\n  var list = [];\n  var language;\n  for (language in languages) {\n    if (own.call(languages, language) && typeof languages[language] === 'object') {\n      list.push(language);\n    }\n  }\n  return list;\n}\nfunction stringify(value, language, parent) {\n  var env;\n  if (typeof value === 'string') {\n    return {\n      type: 'text',\n      value: value\n    };\n  }\n  if (refract.util.type(value) === 'Array') {\n    return stringifyAll(value, language);\n  }\n  env = {\n    type: value.type,\n    content: refract.Token.stringify(value.content, language, parent),\n    tag: 'span',\n    classes: ['token', value.type],\n    attributes: {},\n    language: language,\n    parent: parent\n  };\n  if (value.alias) {\n    env.classes = env.classes.concat(value.alias);\n  }\n  refract.hooks.run('wrap', env);\n  return h(env.tag + '.' + env.classes.join('.'), attributes(env.attributes), env.content);\n}\nfunction stringifyAll(values, language) {\n  var result = [];\n  var length = values.length;\n  var index = -1;\n  var value;\n  while (++index < length) {\n    value = values[index];\n    if (value !== '' && value !== null && value !== undefined) {\n      result.push(value);\n    }\n  }\n  index = -1;\n  length = result.length;\n  while (++index < length) {\n    value = result[index];\n    result[index] = refract.Token.stringify(value, language, result);\n  }\n  return result;\n}\nfunction encode(tokens) {\n  return tokens;\n}\nfunction attributes(attrs) {\n  var key;\n  for (key in attrs) {\n    attrs[key] = decode(attrs[key]);\n  }\n  return attrs;\n}\nfunction capture() {\n  var defined = 'Prism' in ctx;\n  /* istanbul ignore next */\n  var current = defined ? ctx.Prism : undefined;\n  return restore;\n  function restore() {\n    /* istanbul ignore else - Clean leaks after Prism. */\n    if (defined) {\n      ctx.Prism = current;\n    } else {\n      delete ctx.Prism;\n    }\n    defined = undefined;\n    current = undefined;\n  }\n}", "map": {"version": 3, "names": ["ctx", "globalThis", "self", "window", "global", "restore", "capture", "Prism", "manual", "disableWorkerMessageHandler", "h", "require", "decode", "markup", "css", "clike", "js", "own", "hasOwnProperty", "Refractor", "prototype", "refract", "module", "exports", "highlight", "register", "alias", "registered", "listLanguages", "util", "encode", "Token", "stringify", "grammar", "displayName", "Error", "languages", "undefined", "name", "map", "key", "list", "length", "index", "value", "sup", "type", "call", "language", "push", "parent", "env", "stringifyAll", "content", "tag", "classes", "attributes", "concat", "hooks", "run", "join", "values", "result", "tokens", "attrs", "defined", "current"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/core.js"], "sourcesContent": ["'use strict'\n\n/* global window, self */\n\n// istanbul ignore next - Don't allow Prism to run on page load in browser or\n// to start messaging from workers.\nvar ctx =\n  typeof globalThis === 'object'\n    ? globalThis\n    : typeof self === 'object'\n    ? self\n    : typeof window === 'object'\n    ? window\n    : typeof global === 'object'\n    ? global\n    : {}\n\nvar restore = capture()\n\nctx.Prism = {manual: true, disableWorkerMessageHandler: true}\n\n// Load all stuff in `prism.js` itself, except for `prism-file-highlight.js`.\n// The wrapped non-leaky grammars are loaded instead of Prism’s originals.\nvar h = require('hastscript')\nvar decode = require('parse-entities')\nvar Prism = require('prismjs/components/prism-core')\nvar markup = require('./lang/markup')\nvar css = require('./lang/css')\nvar clike = require('./lang/clike')\nvar js = require('./lang/javascript')\n\nrestore()\n\nvar own = {}.hasOwnProperty\n\n// Inherit.\nfunction Refractor() {}\n\nRefractor.prototype = Prism\n\n// Construct.\nvar refract = new Refractor()\n\n// Expose.\nmodule.exports = refract\n\n// Create.\nrefract.highlight = highlight\nrefract.register = register\nrefract.alias = alias\nrefract.registered = registered\nrefract.listLanguages = listLanguages\n\n// Register bundled grammars.\nregister(markup)\nregister(css)\nregister(clike)\nregister(js)\n\nrefract.util.encode = encode\nrefract.Token.stringify = stringify\n\nfunction register(grammar) {\n  if (typeof grammar !== 'function' || !grammar.displayName) {\n    throw new Error('Expected `function` for `grammar`, got `' + grammar + '`')\n  }\n\n  // Do not duplicate registrations.\n  if (refract.languages[grammar.displayName] === undefined) {\n    grammar(refract)\n  }\n}\n\nfunction alias(name, alias) {\n  var languages = refract.languages\n  var map = name\n  var key\n  var list\n  var length\n  var index\n\n  if (alias) {\n    map = {}\n    map[name] = alias\n  }\n\n  for (key in map) {\n    list = map[key]\n    list = typeof list === 'string' ? [list] : list\n    length = list.length\n    index = -1\n\n    while (++index < length) {\n      languages[list[index]] = languages[key]\n    }\n  }\n}\n\nfunction highlight(value, name) {\n  var sup = Prism.highlight\n  var grammar\n\n  if (typeof value !== 'string') {\n    throw new Error('Expected `string` for `value`, got `' + value + '`')\n  }\n\n  // `name` is a grammar object.\n  if (refract.util.type(name) === 'Object') {\n    grammar = name\n    name = null\n  } else {\n    if (typeof name !== 'string') {\n      throw new Error('Expected `string` for `name`, got `' + name + '`')\n    }\n\n    if (own.call(refract.languages, name)) {\n      grammar = refract.languages[name]\n    } else {\n      throw new Error('Unknown language: `' + name + '` is not registered')\n    }\n  }\n\n  return sup.call(this, value, grammar, name)\n}\n\nfunction registered(language) {\n  if (typeof language !== 'string') {\n    throw new Error('Expected `string` for `language`, got `' + language + '`')\n  }\n\n  return own.call(refract.languages, language)\n}\n\nfunction listLanguages() {\n  var languages = refract.languages\n  var list = []\n  var language\n\n  for (language in languages) {\n    if (\n      own.call(languages, language) &&\n      typeof languages[language] === 'object'\n    ) {\n      list.push(language)\n    }\n  }\n\n  return list\n}\n\nfunction stringify(value, language, parent) {\n  var env\n\n  if (typeof value === 'string') {\n    return {type: 'text', value: value}\n  }\n\n  if (refract.util.type(value) === 'Array') {\n    return stringifyAll(value, language)\n  }\n\n  env = {\n    type: value.type,\n    content: refract.Token.stringify(value.content, language, parent),\n    tag: 'span',\n    classes: ['token', value.type],\n    attributes: {},\n    language: language,\n    parent: parent\n  }\n\n  if (value.alias) {\n    env.classes = env.classes.concat(value.alias)\n  }\n\n  refract.hooks.run('wrap', env)\n\n  return h(\n    env.tag + '.' + env.classes.join('.'),\n    attributes(env.attributes),\n    env.content\n  )\n}\n\nfunction stringifyAll(values, language) {\n  var result = []\n  var length = values.length\n  var index = -1\n  var value\n\n  while (++index < length) {\n    value = values[index]\n\n    if (value !== '' && value !== null && value !== undefined) {\n      result.push(value)\n    }\n  }\n\n  index = -1\n  length = result.length\n\n  while (++index < length) {\n    value = result[index]\n    result[index] = refract.Token.stringify(value, language, result)\n  }\n\n  return result\n}\n\nfunction encode(tokens) {\n  return tokens\n}\n\nfunction attributes(attrs) {\n  var key\n\n  for (key in attrs) {\n    attrs[key] = decode(attrs[key])\n  }\n\n  return attrs\n}\n\nfunction capture() {\n  var defined = 'Prism' in ctx\n  /* istanbul ignore next */\n  var current = defined ? ctx.Prism : undefined\n\n  return restore\n\n  function restore() {\n    /* istanbul ignore else - Clean leaks after Prism. */\n    if (defined) {\n      ctx.Prism = current\n    } else {\n      delete ctx.Prism\n    }\n\n    defined = undefined\n    current = undefined\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;;AAEA;AACA;AACA,IAAIA,GAAG,GACL,OAAOC,UAAU,KAAK,QAAQ,GAC1BA,UAAU,GACV,OAAOC,IAAI,KAAK,QAAQ,GACxBA,IAAI,GACJ,OAAOC,MAAM,KAAK,QAAQ,GAC1BA,MAAM,GACN,OAAOC,MAAM,KAAK,QAAQ,GAC1BA,MAAM,GACN,CAAC,CAAC;AAER,IAAIC,OAAO,GAAGC,OAAO,CAAC,CAAC;AAEvBN,GAAG,CAACO,KAAK,GAAG;EAACC,MAAM,EAAE,IAAI;EAAEC,2BAA2B,EAAE;AAAI,CAAC;;AAE7D;AACA;AACA,IAAIC,CAAC,GAAGC,OAAO,CAAC,YAAY,CAAC;AAC7B,IAAIC,MAAM,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACtC,IAAIJ,KAAK,GAAGI,OAAO,CAAC,+BAA+B,CAAC;AACpD,IAAIE,MAAM,GAAGF,OAAO,CAAC,eAAe,CAAC;AACrC,IAAIG,GAAG,GAAGH,OAAO,CAAC,YAAY,CAAC;AAC/B,IAAII,KAAK,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACnC,IAAIK,EAAE,GAAGL,OAAO,CAAC,mBAAmB,CAAC;AAErCN,OAAO,CAAC,CAAC;AAET,IAAIY,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE3B;AACA,SAASC,SAASA,CAAA,EAAG,CAAC;AAEtBA,SAAS,CAACC,SAAS,GAAGb,KAAK;;AAE3B;AACA,IAAIc,OAAO,GAAG,IAAIF,SAAS,CAAC,CAAC;;AAE7B;AACAG,MAAM,CAACC,OAAO,GAAGF,OAAO;;AAExB;AACAA,OAAO,CAACG,SAAS,GAAGA,SAAS;AAC7BH,OAAO,CAACI,QAAQ,GAAGA,QAAQ;AAC3BJ,OAAO,CAACK,KAAK,GAAGA,KAAK;AACrBL,OAAO,CAACM,UAAU,GAAGA,UAAU;AAC/BN,OAAO,CAACO,aAAa,GAAGA,aAAa;;AAErC;AACAH,QAAQ,CAACZ,MAAM,CAAC;AAChBY,QAAQ,CAACX,GAAG,CAAC;AACbW,QAAQ,CAACV,KAAK,CAAC;AACfU,QAAQ,CAACT,EAAE,CAAC;AAEZK,OAAO,CAACQ,IAAI,CAACC,MAAM,GAAGA,MAAM;AAC5BT,OAAO,CAACU,KAAK,CAACC,SAAS,GAAGA,SAAS;AAEnC,SAASP,QAAQA,CAACQ,OAAO,EAAE;EACzB,IAAI,OAAOA,OAAO,KAAK,UAAU,IAAI,CAACA,OAAO,CAACC,WAAW,EAAE;IACzD,MAAM,IAAIC,KAAK,CAAC,0CAA0C,GAAGF,OAAO,GAAG,GAAG,CAAC;EAC7E;;EAEA;EACA,IAAIZ,OAAO,CAACe,SAAS,CAACH,OAAO,CAACC,WAAW,CAAC,KAAKG,SAAS,EAAE;IACxDJ,OAAO,CAACZ,OAAO,CAAC;EAClB;AACF;AAEA,SAASK,KAAKA,CAACY,IAAI,EAAEZ,KAAK,EAAE;EAC1B,IAAIU,SAAS,GAAGf,OAAO,CAACe,SAAS;EACjC,IAAIG,GAAG,GAAGD,IAAI;EACd,IAAIE,GAAG;EACP,IAAIC,IAAI;EACR,IAAIC,MAAM;EACV,IAAIC,KAAK;EAET,IAAIjB,KAAK,EAAE;IACTa,GAAG,GAAG,CAAC,CAAC;IACRA,GAAG,CAACD,IAAI,CAAC,GAAGZ,KAAK;EACnB;EAEA,KAAKc,GAAG,IAAID,GAAG,EAAE;IACfE,IAAI,GAAGF,GAAG,CAACC,GAAG,CAAC;IACfC,IAAI,GAAG,OAAOA,IAAI,KAAK,QAAQ,GAAG,CAACA,IAAI,CAAC,GAAGA,IAAI;IAC/CC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACpBC,KAAK,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,KAAK,GAAGD,MAAM,EAAE;MACvBN,SAAS,CAACK,IAAI,CAACE,KAAK,CAAC,CAAC,GAAGP,SAAS,CAACI,GAAG,CAAC;IACzC;EACF;AACF;AAEA,SAAShB,SAASA,CAACoB,KAAK,EAAEN,IAAI,EAAE;EAC9B,IAAIO,GAAG,GAAGtC,KAAK,CAACiB,SAAS;EACzB,IAAIS,OAAO;EAEX,IAAI,OAAOW,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIT,KAAK,CAAC,sCAAsC,GAAGS,KAAK,GAAG,GAAG,CAAC;EACvE;;EAEA;EACA,IAAIvB,OAAO,CAACQ,IAAI,CAACiB,IAAI,CAACR,IAAI,CAAC,KAAK,QAAQ,EAAE;IACxCL,OAAO,GAAGK,IAAI;IACdA,IAAI,GAAG,IAAI;EACb,CAAC,MAAM;IACL,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIH,KAAK,CAAC,qCAAqC,GAAGG,IAAI,GAAG,GAAG,CAAC;IACrE;IAEA,IAAIrB,GAAG,CAAC8B,IAAI,CAAC1B,OAAO,CAACe,SAAS,EAAEE,IAAI,CAAC,EAAE;MACrCL,OAAO,GAAGZ,OAAO,CAACe,SAAS,CAACE,IAAI,CAAC;IACnC,CAAC,MAAM;MACL,MAAM,IAAIH,KAAK,CAAC,qBAAqB,GAAGG,IAAI,GAAG,qBAAqB,CAAC;IACvE;EACF;EAEA,OAAOO,GAAG,CAACE,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEX,OAAO,EAAEK,IAAI,CAAC;AAC7C;AAEA,SAASX,UAAUA,CAACqB,QAAQ,EAAE;EAC5B,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAChC,MAAM,IAAIb,KAAK,CAAC,yCAAyC,GAAGa,QAAQ,GAAG,GAAG,CAAC;EAC7E;EAEA,OAAO/B,GAAG,CAAC8B,IAAI,CAAC1B,OAAO,CAACe,SAAS,EAAEY,QAAQ,CAAC;AAC9C;AAEA,SAASpB,aAAaA,CAAA,EAAG;EACvB,IAAIQ,SAAS,GAAGf,OAAO,CAACe,SAAS;EACjC,IAAIK,IAAI,GAAG,EAAE;EACb,IAAIO,QAAQ;EAEZ,KAAKA,QAAQ,IAAIZ,SAAS,EAAE;IAC1B,IACEnB,GAAG,CAAC8B,IAAI,CAACX,SAAS,EAAEY,QAAQ,CAAC,IAC7B,OAAOZ,SAAS,CAACY,QAAQ,CAAC,KAAK,QAAQ,EACvC;MACAP,IAAI,CAACQ,IAAI,CAACD,QAAQ,CAAC;IACrB;EACF;EAEA,OAAOP,IAAI;AACb;AAEA,SAAST,SAASA,CAACY,KAAK,EAAEI,QAAQ,EAAEE,MAAM,EAAE;EAC1C,IAAIC,GAAG;EAEP,IAAI,OAAOP,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO;MAACE,IAAI,EAAE,MAAM;MAAEF,KAAK,EAAEA;IAAK,CAAC;EACrC;EAEA,IAAIvB,OAAO,CAACQ,IAAI,CAACiB,IAAI,CAACF,KAAK,CAAC,KAAK,OAAO,EAAE;IACxC,OAAOQ,YAAY,CAACR,KAAK,EAAEI,QAAQ,CAAC;EACtC;EAEAG,GAAG,GAAG;IACJL,IAAI,EAAEF,KAAK,CAACE,IAAI;IAChBO,OAAO,EAAEhC,OAAO,CAACU,KAAK,CAACC,SAAS,CAACY,KAAK,CAACS,OAAO,EAAEL,QAAQ,EAAEE,MAAM,CAAC;IACjEI,GAAG,EAAE,MAAM;IACXC,OAAO,EAAE,CAAC,OAAO,EAAEX,KAAK,CAACE,IAAI,CAAC;IAC9BU,UAAU,EAAE,CAAC,CAAC;IACdR,QAAQ,EAAEA,QAAQ;IAClBE,MAAM,EAAEA;EACV,CAAC;EAED,IAAIN,KAAK,CAAClB,KAAK,EAAE;IACfyB,GAAG,CAACI,OAAO,GAAGJ,GAAG,CAACI,OAAO,CAACE,MAAM,CAACb,KAAK,CAAClB,KAAK,CAAC;EAC/C;EAEAL,OAAO,CAACqC,KAAK,CAACC,GAAG,CAAC,MAAM,EAAER,GAAG,CAAC;EAE9B,OAAOzC,CAAC,CACNyC,GAAG,CAACG,GAAG,GAAG,GAAG,GAAGH,GAAG,CAACI,OAAO,CAACK,IAAI,CAAC,GAAG,CAAC,EACrCJ,UAAU,CAACL,GAAG,CAACK,UAAU,CAAC,EAC1BL,GAAG,CAACE,OACN,CAAC;AACH;AAEA,SAASD,YAAYA,CAACS,MAAM,EAAEb,QAAQ,EAAE;EACtC,IAAIc,MAAM,GAAG,EAAE;EACf,IAAIpB,MAAM,GAAGmB,MAAM,CAACnB,MAAM;EAC1B,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,KAAK;EAET,OAAO,EAAED,KAAK,GAAGD,MAAM,EAAE;IACvBE,KAAK,GAAGiB,MAAM,CAAClB,KAAK,CAAC;IAErB,IAAIC,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKP,SAAS,EAAE;MACzDyB,MAAM,CAACb,IAAI,CAACL,KAAK,CAAC;IACpB;EACF;EAEAD,KAAK,GAAG,CAAC,CAAC;EACVD,MAAM,GAAGoB,MAAM,CAACpB,MAAM;EAEtB,OAAO,EAAEC,KAAK,GAAGD,MAAM,EAAE;IACvBE,KAAK,GAAGkB,MAAM,CAACnB,KAAK,CAAC;IACrBmB,MAAM,CAACnB,KAAK,CAAC,GAAGtB,OAAO,CAACU,KAAK,CAACC,SAAS,CAACY,KAAK,EAAEI,QAAQ,EAAEc,MAAM,CAAC;EAClE;EAEA,OAAOA,MAAM;AACf;AAEA,SAAShC,MAAMA,CAACiC,MAAM,EAAE;EACtB,OAAOA,MAAM;AACf;AAEA,SAASP,UAAUA,CAACQ,KAAK,EAAE;EACzB,IAAIxB,GAAG;EAEP,KAAKA,GAAG,IAAIwB,KAAK,EAAE;IACjBA,KAAK,CAACxB,GAAG,CAAC,GAAG5B,MAAM,CAACoD,KAAK,CAACxB,GAAG,CAAC,CAAC;EACjC;EAEA,OAAOwB,KAAK;AACd;AAEA,SAAS1D,OAAOA,CAAA,EAAG;EACjB,IAAI2D,OAAO,GAAG,OAAO,IAAIjE,GAAG;EAC5B;EACA,IAAIkE,OAAO,GAAGD,OAAO,GAAGjE,GAAG,CAACO,KAAK,GAAG8B,SAAS;EAE7C,OAAOhC,OAAO;EAEd,SAASA,OAAOA,CAAA,EAAG;IACjB;IACA,IAAI4D,OAAO,EAAE;MACXjE,GAAG,CAACO,KAAK,GAAG2D,OAAO;IACrB,CAAC,MAAM;MACL,OAAOlE,GAAG,CAACO,KAAK;IAClB;IAEA0D,OAAO,GAAG5B,SAAS;IACnB6B,OAAO,GAAG7B,SAAS;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}