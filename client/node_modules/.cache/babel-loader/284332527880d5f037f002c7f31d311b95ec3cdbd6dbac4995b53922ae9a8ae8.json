{"ast": null, "code": "'use strict';\n\nmodule.exports = zig;\nzig.displayName = 'zig';\nzig.aliases = [];\nfunction zig(Prism) {\n  ;\n  (function (Prism) {\n    function literal(str) {\n      return function () {\n        return str;\n      };\n    }\n    var keyword = /\\b(?:align|allowzero|and|anyframe|anytype|asm|async|await|break|cancel|catch|comptime|const|continue|defer|else|enum|errdefer|error|export|extern|fn|for|if|inline|linksection|nakedcc|noalias|nosuspend|null|or|orelse|packed|promise|pub|resume|return|stdcallcc|struct|suspend|switch|test|threadlocal|try|undefined|union|unreachable|usingnamespace|var|volatile|while)\\b/;\n    var IDENTIFIER = '\\\\b(?!' + keyword.source + ')(?!\\\\d)\\\\w+\\\\b';\n    var ALIGN = /align\\s*\\((?:[^()]|\\([^()]*\\))*\\)/.source;\n    var PREFIX_TYPE_OP = /(?:\\?|\\bpromise->|(?:\\[[^[\\]]*\\]|\\*(?!\\*)|\\*\\*)(?:\\s*<ALIGN>|\\s*const\\b|\\s*volatile\\b|\\s*allowzero\\b)*)/.source.replace(/<ALIGN>/g, literal(ALIGN));\n    var SUFFIX_EXPR = /(?:\\bpromise\\b|(?:\\berror\\.)?<ID>(?:\\.<ID>)*(?!\\s+<ID>))/.source.replace(/<ID>/g, literal(IDENTIFIER));\n    var TYPE = '(?!\\\\s)(?:!?\\\\s*(?:' + PREFIX_TYPE_OP + '\\\\s*)*' + SUFFIX_EXPR + ')+';\n    /*\n     * A simplified grammar for Zig compile time type literals:\n     *\n     * TypeExpr = ( \"!\"? PREFIX_TYPE_OP* SUFFIX_EXPR )+\n     *\n     * SUFFIX_EXPR = ( \\b \"promise\" \\b | ( \\b \"error\" \".\" )? IDENTIFIER ( \".\" IDENTIFIER )* (?! \\s+ IDENTIFIER ) )\n     *\n     * PREFIX_TYPE_OP = \"?\"\n     *                | \\b \"promise\" \"->\"\n     *                | ( \"[\" [^\\[\\]]* \"]\" | \"*\" | \"**\" ) ( ALIGN | \"const\" \\b | \"volatile\" \\b | \"allowzero\" \\b )*\n     *\n     * ALIGN = \"align\" \"(\" ( [^()] | \"(\" [^()]* \")\" )* \")\"\n     *\n     * IDENTIFIER = \\b (?! KEYWORD ) [a-zA-Z_] \\w* \\b\n     *\n     */\n    Prism.languages.zig = {\n      comment: [{\n        pattern: /\\/\\/[/!].*/,\n        alias: 'doc-comment'\n      }, /\\/{2}.*/],\n      string: [{\n        // \"string\" and c\"string\"\n        pattern: /(^|[^\\\\@])c?\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"/,\n        lookbehind: true,\n        greedy: true\n      }, {\n        // multiline strings and c-strings\n        pattern: /([\\r\\n])([ \\t]+c?\\\\{2}).*(?:(?:\\r\\n?|\\n)\\2.*)*/,\n        lookbehind: true,\n        greedy: true\n      }],\n      char: {\n        // characters 'a', '\\n', '\\xFF', '\\u{10FFFF}'\n        pattern: /(^|[^\\\\])'(?:[^'\\\\\\r\\n]|[\\uD800-\\uDFFF]{2}|\\\\(?:.|x[a-fA-F\\d]{2}|u\\{[a-fA-F\\d]{1,6}\\}))'/,\n        lookbehind: true,\n        greedy: true\n      },\n      builtin: /\\B@(?!\\d)\\w+(?=\\s*\\()/,\n      label: {\n        pattern: /(\\b(?:break|continue)\\s*:\\s*)\\w+\\b|\\b(?!\\d)\\w+\\b(?=\\s*:\\s*(?:\\{|while\\b))/,\n        lookbehind: true\n      },\n      'class-name': [\n      // const Foo = struct {};\n      /\\b(?!\\d)\\w+(?=\\s*=\\s*(?:(?:extern|packed)\\s+)?(?:enum|struct|union)\\s*[({])/, {\n        // const x: i32 = 9;\n        // var x: Bar;\n        // fn foo(x: bool, y: f32) void {}\n        pattern: RegExp(/(:\\s*)<TYPE>(?=\\s*(?:<ALIGN>\\s*)?[=;,)])|<TYPE>(?=\\s*(?:<ALIGN>\\s*)?\\{)/.source.replace(/<TYPE>/g, literal(TYPE)).replace(/<ALIGN>/g, literal(ALIGN))),\n        lookbehind: true,\n        inside: null // see below\n      }, {\n        // extern fn foo(x: f64) f64; (optional alignment)\n        pattern: RegExp(/(\\)\\s*)<TYPE>(?=\\s*(?:<ALIGN>\\s*)?;)/.source.replace(/<TYPE>/g, literal(TYPE)).replace(/<ALIGN>/g, literal(ALIGN))),\n        lookbehind: true,\n        inside: null // see below\n      }],\n      'builtin-type': {\n        pattern: /\\b(?:anyerror|bool|c_u?(?:int|long|longlong|short)|c_longdouble|c_void|comptime_(?:float|int)|f(?:16|32|64|128)|[iu](?:8|16|32|64|128|size)|noreturn|type|void)\\b/,\n        alias: 'keyword'\n      },\n      keyword: keyword,\n      function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n      number: /\\b(?:0b[01]+|0o[0-7]+|0x[a-fA-F\\d]+(?:\\.[a-fA-F\\d]*)?(?:[pP][+-]?[a-fA-F\\d]+)?|\\d+(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: /\\.[*?]|\\.{2,3}|[-=]>|\\*\\*|\\+\\+|\\|\\||(?:<<|>>|[-+*]%|[-+*/%^&|<>!=])=?|[?~]/,\n      punctuation: /[.:,;(){}[\\]]/\n    };\n    Prism.languages.zig['class-name'].forEach(function (obj) {\n      if (obj.inside === null) {\n        obj.inside = Prism.languages.zig;\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "zig", "displayName", "aliases", "Prism", "literal", "str", "keyword", "IDENTIFIER", "source", "ALIGN", "PREFIX_TYPE_OP", "replace", "SUFFIX_EXPR", "TYPE", "languages", "comment", "pattern", "alias", "string", "lookbehind", "greedy", "char", "builtin", "label", "RegExp", "inside", "function", "number", "boolean", "operator", "punctuation", "for<PERSON>ach", "obj"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/zig.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = zig\nzig.displayName = 'zig'\nzig.aliases = []\nfunction zig(Prism) {\n  ;(function (Prism) {\n    function literal(str) {\n      return function () {\n        return str\n      }\n    }\n    var keyword =\n      /\\b(?:align|allowzero|and|anyframe|anytype|asm|async|await|break|cancel|catch|comptime|const|continue|defer|else|enum|errdefer|error|export|extern|fn|for|if|inline|linksection|nakedcc|noalias|nosuspend|null|or|orelse|packed|promise|pub|resume|return|stdcallcc|struct|suspend|switch|test|threadlocal|try|undefined|union|unreachable|usingnamespace|var|volatile|while)\\b/\n    var IDENTIFIER = '\\\\b(?!' + keyword.source + ')(?!\\\\d)\\\\w+\\\\b'\n    var ALIGN = /align\\s*\\((?:[^()]|\\([^()]*\\))*\\)/.source\n    var PREFIX_TYPE_OP =\n      /(?:\\?|\\bpromise->|(?:\\[[^[\\]]*\\]|\\*(?!\\*)|\\*\\*)(?:\\s*<ALIGN>|\\s*const\\b|\\s*volatile\\b|\\s*allowzero\\b)*)/.source.replace(\n        /<ALIGN>/g,\n        literal(ALIGN)\n      )\n    var SUFFIX_EXPR =\n      /(?:\\bpromise\\b|(?:\\berror\\.)?<ID>(?:\\.<ID>)*(?!\\s+<ID>))/.source.replace(\n        /<ID>/g,\n        literal(IDENTIFIER)\n      )\n    var TYPE =\n      '(?!\\\\s)(?:!?\\\\s*(?:' + PREFIX_TYPE_OP + '\\\\s*)*' + SUFFIX_EXPR + ')+'\n    /*\n     * A simplified grammar for Zig compile time type literals:\n     *\n     * TypeExpr = ( \"!\"? PREFIX_TYPE_OP* SUFFIX_EXPR )+\n     *\n     * SUFFIX_EXPR = ( \\b \"promise\" \\b | ( \\b \"error\" \".\" )? IDENTIFIER ( \".\" IDENTIFIER )* (?! \\s+ IDENTIFIER ) )\n     *\n     * PREFIX_TYPE_OP = \"?\"\n     *                | \\b \"promise\" \"->\"\n     *                | ( \"[\" [^\\[\\]]* \"]\" | \"*\" | \"**\" ) ( ALIGN | \"const\" \\b | \"volatile\" \\b | \"allowzero\" \\b )*\n     *\n     * ALIGN = \"align\" \"(\" ( [^()] | \"(\" [^()]* \")\" )* \")\"\n     *\n     * IDENTIFIER = \\b (?! KEYWORD ) [a-zA-Z_] \\w* \\b\n     *\n     */\n    Prism.languages.zig = {\n      comment: [\n        {\n          pattern: /\\/\\/[/!].*/,\n          alias: 'doc-comment'\n        },\n        /\\/{2}.*/\n      ],\n      string: [\n        {\n          // \"string\" and c\"string\"\n          pattern: /(^|[^\\\\@])c?\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // multiline strings and c-strings\n          pattern: /([\\r\\n])([ \\t]+c?\\\\{2}).*(?:(?:\\r\\n?|\\n)\\2.*)*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      char: {\n        // characters 'a', '\\n', '\\xFF', '\\u{10FFFF}'\n        pattern:\n          /(^|[^\\\\])'(?:[^'\\\\\\r\\n]|[\\uD800-\\uDFFF]{2}|\\\\(?:.|x[a-fA-F\\d]{2}|u\\{[a-fA-F\\d]{1,6}\\}))'/,\n        lookbehind: true,\n        greedy: true\n      },\n      builtin: /\\B@(?!\\d)\\w+(?=\\s*\\()/,\n      label: {\n        pattern:\n          /(\\b(?:break|continue)\\s*:\\s*)\\w+\\b|\\b(?!\\d)\\w+\\b(?=\\s*:\\s*(?:\\{|while\\b))/,\n        lookbehind: true\n      },\n      'class-name': [\n        // const Foo = struct {};\n        /\\b(?!\\d)\\w+(?=\\s*=\\s*(?:(?:extern|packed)\\s+)?(?:enum|struct|union)\\s*[({])/,\n        {\n          // const x: i32 = 9;\n          // var x: Bar;\n          // fn foo(x: bool, y: f32) void {}\n          pattern: RegExp(\n            /(:\\s*)<TYPE>(?=\\s*(?:<ALIGN>\\s*)?[=;,)])|<TYPE>(?=\\s*(?:<ALIGN>\\s*)?\\{)/.source\n              .replace(/<TYPE>/g, literal(TYPE))\n              .replace(/<ALIGN>/g, literal(ALIGN))\n          ),\n          lookbehind: true,\n          inside: null // see below\n        },\n        {\n          // extern fn foo(x: f64) f64; (optional alignment)\n          pattern: RegExp(\n            /(\\)\\s*)<TYPE>(?=\\s*(?:<ALIGN>\\s*)?;)/.source\n              .replace(/<TYPE>/g, literal(TYPE))\n              .replace(/<ALIGN>/g, literal(ALIGN))\n          ),\n          lookbehind: true,\n          inside: null // see below\n        }\n      ],\n      'builtin-type': {\n        pattern:\n          /\\b(?:anyerror|bool|c_u?(?:int|long|longlong|short)|c_longdouble|c_void|comptime_(?:float|int)|f(?:16|32|64|128)|[iu](?:8|16|32|64|128|size)|noreturn|type|void)\\b/,\n        alias: 'keyword'\n      },\n      keyword: keyword,\n      function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n      number:\n        /\\b(?:0b[01]+|0o[0-7]+|0x[a-fA-F\\d]+(?:\\.[a-fA-F\\d]*)?(?:[pP][+-]?[a-fA-F\\d]+)?|\\d+(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      operator:\n        /\\.[*?]|\\.{2,3}|[-=]>|\\*\\*|\\+\\+|\\|\\||(?:<<|>>|[-+*]%|[-+*/%^&|<>!=])=?|[?~]/,\n      punctuation: /[.:,;(){}[\\]]/\n    }\n    Prism.languages.zig['class-name'].forEach(function (obj) {\n      if (obj.inside === null) {\n        obj.inside = Prism.languages.zig\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,SAASC,OAAOA,CAACC,GAAG,EAAE;MACpB,OAAO,YAAY;QACjB,OAAOA,GAAG;MACZ,CAAC;IACH;IACA,IAAIC,OAAO,GACT,gXAAgX;IAClX,IAAIC,UAAU,GAAG,QAAQ,GAAGD,OAAO,CAACE,MAAM,GAAG,iBAAiB;IAC9D,IAAIC,KAAK,GAAG,mCAAmC,CAACD,MAAM;IACtD,IAAIE,cAAc,GAChB,yGAAyG,CAACF,MAAM,CAACG,OAAO,CACtH,UAAU,EACVP,OAAO,CAACK,KAAK,CACf,CAAC;IACH,IAAIG,WAAW,GACb,0DAA0D,CAACJ,MAAM,CAACG,OAAO,CACvE,OAAO,EACPP,OAAO,CAACG,UAAU,CACpB,CAAC;IACH,IAAIM,IAAI,GACN,qBAAqB,GAAGH,cAAc,GAAG,QAAQ,GAAGE,WAAW,GAAG,IAAI;IACxE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIT,KAAK,CAACW,SAAS,CAACd,GAAG,GAAG;MACpBe,OAAO,EAAE,CACP;QACEC,OAAO,EAAE,YAAY;QACrBC,KAAK,EAAE;MACT,CAAC,EACD,SAAS,CACV;MACDC,MAAM,EAAE,CACN;QACE;QACAF,OAAO,EAAE,mCAAmC;QAC5CG,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAJ,OAAO,EAAE,gDAAgD;QACzDG,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,IAAI,EAAE;QACJ;QACAL,OAAO,EACL,0FAA0F;QAC5FG,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC;MACDE,OAAO,EAAE,uBAAuB;MAChCC,KAAK,EAAE;QACLP,OAAO,EACL,2EAA2E;QAC7EG,UAAU,EAAE;MACd,CAAC;MACD,YAAY,EAAE;MACZ;MACA,6EAA6E,EAC7E;QACE;QACA;QACA;QACAH,OAAO,EAAEQ,MAAM,CACb,yEAAyE,CAAChB,MAAM,CAC7EG,OAAO,CAAC,SAAS,EAAEP,OAAO,CAACS,IAAI,CAAC,CAAC,CACjCF,OAAO,CAAC,UAAU,EAAEP,OAAO,CAACK,KAAK,CAAC,CACvC,CAAC;QACDU,UAAU,EAAE,IAAI;QAChBM,MAAM,EAAE,IAAI,CAAC;MACf,CAAC,EACD;QACE;QACAT,OAAO,EAAEQ,MAAM,CACb,sCAAsC,CAAChB,MAAM,CAC1CG,OAAO,CAAC,SAAS,EAAEP,OAAO,CAACS,IAAI,CAAC,CAAC,CACjCF,OAAO,CAAC,UAAU,EAAEP,OAAO,CAACK,KAAK,CAAC,CACvC,CAAC;QACDU,UAAU,EAAE,IAAI;QAChBM,MAAM,EAAE,IAAI,CAAC;MACf,CAAC,CACF;MACD,cAAc,EAAE;QACdT,OAAO,EACL,mKAAmK;QACrKC,KAAK,EAAE;MACT,CAAC;MACDX,OAAO,EAAEA,OAAO;MAChBoB,QAAQ,EAAE,sBAAsB;MAChCC,MAAM,EACJ,kHAAkH;MACpHC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EACN,4EAA4E;MAC9EC,WAAW,EAAE;IACf,CAAC;IACD3B,KAAK,CAACW,SAAS,CAACd,GAAG,CAAC,YAAY,CAAC,CAAC+B,OAAO,CAAC,UAAUC,GAAG,EAAE;MACvD,IAAIA,GAAG,CAACP,MAAM,KAAK,IAAI,EAAE;QACvBO,GAAG,CAACP,MAAM,GAAGtB,KAAK,CAACW,SAAS,CAACd,GAAG;MAClC;IACF,CAAC,CAAC;EACJ,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}