{"ast": null, "code": "/*\nLanguage: Plain text\nAuthor: <PERSON><PERSON> (<EMAIL>)\nDescription: Plain text without any highlighting.\nCategory: common\n*/\n\nfunction plaintext(hljs) {\n  return {\n    name: 'Plain text',\n    aliases: ['text', 'txt'],\n    disableAutodetect: true\n  };\n}\nmodule.exports = plaintext;", "map": {"version": 3, "names": ["plaintext", "hljs", "name", "aliases", "disableAutodetect", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/plaintext.js"], "sourcesContent": ["/*\nLanguage: Plain text\nAuthor: <PERSON><PERSON> (<EMAIL>)\nDescription: Plain text without any highlighting.\nCategory: common\n*/\n\nfunction plaintext(hljs) {\n  return {\n    name: 'Plain text',\n    aliases: [\n      'text',\n      'txt'\n    ],\n    disableAutodetect: true\n  };\n}\n\nmodule.exports = plaintext;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAASA,CAACC,IAAI,EAAE;EACvB,OAAO;IACLC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,CACP,MAAM,EACN,KAAK,CACN;IACDC,iBAAiB,EAAE;EACrB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}