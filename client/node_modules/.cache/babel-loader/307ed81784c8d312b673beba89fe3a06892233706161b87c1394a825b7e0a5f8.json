{"ast": null, "code": "'use strict';\n\nmodule.exports = wolfram;\nwolfram.displayName = 'wolfram';\nwolfram.aliases = ['mathematica', 'wl', 'nb'];\nfunction wolfram(Prism) {\n  Prism.languages.wolfram = {\n    // Allow one level of nesting - note: regex taken from applescipt\n    comment: /\\(\\*(?:\\(\\*(?:[^*]|\\*(?!\\)))*\\*\\)|(?!\\(\\*)[\\s\\S])*?\\*\\)/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    keyword: /\\b(?:Abs|AbsArg|Accuracy|Block|Do|For|Function|If|Manipulate|Module|Nest|NestList|None|Return|Switch|Table|Which|While)\\b/,\n    context: {\n      pattern: /\\b\\w+`+\\w*/,\n      alias: 'class-name'\n    },\n    blank: {\n      pattern: /\\b\\w+_\\b/,\n      alias: 'regex'\n    },\n    'global-variable': {\n      pattern: /\\$\\w+/,\n      alias: 'variable'\n    },\n    boolean: /\\b(?:False|True)\\b/,\n    number: /(?:\\b(?=\\d)|\\B(?=\\.))(?:0[bo])?(?:(?:\\d|0x[\\da-f])[\\da-f]*(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?j?\\b/i,\n    operator: /\\/\\.|;|=\\.|\\^=|\\^:=|:=|<<|>>|<\\||\\|>|:>|\\|->|->|<-|@@@|@@|@|\\/@|=!=|===|==|=|\\+|-|\\^|\\[\\/-+%=\\]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n    punctuation: /[{}[\\];(),.:]/\n  };\n  Prism.languages.mathematica = Prism.languages.wolfram;\n  Prism.languages.wl = Prism.languages.wolfram;\n  Prism.languages.nb = Prism.languages.wolfram;\n}", "map": {"version": 3, "names": ["module", "exports", "wolfram", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "keyword", "context", "alias", "blank", "boolean", "number", "operator", "punctuation", "mathematica", "wl", "nb"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/wolfram.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wolfram\nwolfram.displayName = 'wolfram'\nwolfram.aliases = ['mathematica', 'wl', 'nb']\nfunction wolfram(Prism) {\n  Prism.languages.wolfram = {\n    // Allow one level of nesting - note: regex taken from applescipt\n    comment: /\\(\\*(?:\\(\\*(?:[^*]|\\*(?!\\)))*\\*\\)|(?!\\(\\*)[\\s\\S])*?\\*\\)/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:Abs|AbsArg|Accuracy|Block|Do|For|Function|If|Manipulate|Module|Nest|NestList|None|Return|Switch|Table|Which|While)\\b/,\n    context: {\n      pattern: /\\b\\w+`+\\w*/,\n      alias: 'class-name'\n    },\n    blank: {\n      pattern: /\\b\\w+_\\b/,\n      alias: 'regex'\n    },\n    'global-variable': {\n      pattern: /\\$\\w+/,\n      alias: 'variable'\n    },\n    boolean: /\\b(?:False|True)\\b/,\n    number:\n      /(?:\\b(?=\\d)|\\B(?=\\.))(?:0[bo])?(?:(?:\\d|0x[\\da-f])[\\da-f]*(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?j?\\b/i,\n    operator:\n      /\\/\\.|;|=\\.|\\^=|\\^:=|:=|<<|>>|<\\||\\|>|:>|\\|->|->|<-|@@@|@@|@|\\/@|=!=|===|==|=|\\+|-|\\^|\\[\\/-+%=\\]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.mathematica = Prism.languages.wolfram\n  Prism.languages.wl = Prism.languages.wolfram\n  Prism.languages.nb = Prism.languages.wolfram\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;AAC7C,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxB;IACAK,OAAO,EAAE,yDAAyD;IAClEC,MAAM,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EACL,2HAA2H;IAC7HC,OAAO,EAAE;MACPH,OAAO,EAAE,YAAY;MACrBI,KAAK,EAAE;IACT,CAAC;IACDC,KAAK,EAAE;MACLL,OAAO,EAAE,UAAU;MACnBI,KAAK,EAAE;IACT,CAAC;IACD,iBAAiB,EAAE;MACjBJ,OAAO,EAAE,OAAO;MAChBI,KAAK,EAAE;IACT,CAAC;IACDE,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EACJ,gGAAgG;IAClGC,QAAQ,EACN,4IAA4I;IAC9IC,WAAW,EAAE;EACf,CAAC;EACDb,KAAK,CAACC,SAAS,CAACa,WAAW,GAAGd,KAAK,CAACC,SAAS,CAACJ,OAAO;EACrDG,KAAK,CAACC,SAAS,CAACc,EAAE,GAAGf,KAAK,CAACC,SAAS,CAACJ,OAAO;EAC5CG,KAAK,CAACC,SAAS,CAACe,EAAE,GAAGhB,KAAK,CAACC,SAAS,CAACJ,OAAO;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}