{"ast": null, "code": "'use strict';\n\nmodule.exports = t4Templating;\nt4Templating.displayName = 't4Templating';\nt4Templating.aliases = [];\nfunction t4Templating(Prism) {\n  ;\n  (function (Prism) {\n    function createBlock(prefix, inside, contentAlias) {\n      return {\n        pattern: RegExp('<#' + prefix + '[\\\\s\\\\S]*?#>'),\n        alias: 'block',\n        inside: {\n          delimiter: {\n            pattern: RegExp('^<#' + prefix + '|#>$'),\n            alias: 'important'\n          },\n          content: {\n            pattern: /[\\s\\S]+/,\n            inside: inside,\n            alias: contentAlia<PERSON>\n          }\n        }\n      };\n    }\n    function createT4(insideLang) {\n      var grammar = Prism.languages[insideLang];\n      var className = 'language-' + insideLang;\n      return {\n        block: {\n          pattern: /<#[\\s\\S]+?#>/,\n          inside: {\n            directive: createBlock('@', {\n              'attr-value': {\n                pattern: /=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/,\n                inside: {\n                  punctuation: /^=|^[\"']|[\"']$/\n                }\n              },\n              keyword: /\\b\\w+(?=\\s)/,\n              'attr-name': /\\b\\w+/\n            }),\n            expression: createBlock('=', grammar, className),\n            'class-feature': createBlock('\\\\+', grammar, className),\n            standard: createBlock('', grammar, className)\n          }\n        }\n      };\n    }\n    Prism.languages['t4-templating'] = Object.defineProperty({}, 'createT4', {\n      value: createT4\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "t4Templating", "displayName", "aliases", "Prism", "createBlock", "prefix", "inside", "contentAlias", "pattern", "RegExp", "alias", "delimiter", "content", "createT4", "insideLang", "grammar", "languages", "className", "block", "directive", "punctuation", "keyword", "expression", "standard", "Object", "defineProperty", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/t4-templating.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = t4Templating\nt4Templating.displayName = 't4Templating'\nt4Templating.aliases = []\nfunction t4Templating(Prism) {\n  ;(function (Prism) {\n    function createBlock(prefix, inside, contentAlias) {\n      return {\n        pattern: RegExp('<#' + prefix + '[\\\\s\\\\S]*?#>'),\n        alias: 'block',\n        inside: {\n          delimiter: {\n            pattern: RegExp('^<#' + prefix + '|#>$'),\n            alias: 'important'\n          },\n          content: {\n            pattern: /[\\s\\S]+/,\n            inside: inside,\n            alias: contentAlia<PERSON>\n          }\n        }\n      }\n    }\n    function createT4(insideLang) {\n      var grammar = Prism.languages[insideLang]\n      var className = 'language-' + insideLang\n      return {\n        block: {\n          pattern: /<#[\\s\\S]+?#>/,\n          inside: {\n            directive: createBlock('@', {\n              'attr-value': {\n                pattern: /=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/,\n                inside: {\n                  punctuation: /^=|^[\"']|[\"']$/\n                }\n              },\n              keyword: /\\b\\w+(?=\\s)/,\n              'attr-name': /\\b\\w+/\n            }),\n            expression: createBlock('=', grammar, className),\n            'class-feature': createBlock('\\\\+', grammar, className),\n            standard: createBlock('', grammar, className)\n          }\n        }\n      }\n    }\n    Prism.languages['t4-templating'] = Object.defineProperty({}, 'createT4', {\n      value: createT4\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3B;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAE;MACjD,OAAO;QACLC,OAAO,EAAEC,MAAM,CAAC,IAAI,GAAGJ,MAAM,GAAG,cAAc,CAAC;QAC/CK,KAAK,EAAE,OAAO;QACdJ,MAAM,EAAE;UACNK,SAAS,EAAE;YACTH,OAAO,EAAEC,MAAM,CAAC,KAAK,GAAGJ,MAAM,GAAG,MAAM,CAAC;YACxCK,KAAK,EAAE;UACT,CAAC;UACDE,OAAO,EAAE;YACPJ,OAAO,EAAE,SAAS;YAClBF,MAAM,EAAEA,MAAM;YACdI,KAAK,EAAEH;UACT;QACF;MACF,CAAC;IACH;IACA,SAASM,QAAQA,CAACC,UAAU,EAAE;MAC5B,IAAIC,OAAO,GAAGZ,KAAK,CAACa,SAAS,CAACF,UAAU,CAAC;MACzC,IAAIG,SAAS,GAAG,WAAW,GAAGH,UAAU;MACxC,OAAO;QACLI,KAAK,EAAE;UACLV,OAAO,EAAE,cAAc;UACvBF,MAAM,EAAE;YACNa,SAAS,EAAEf,WAAW,CAAC,GAAG,EAAE;cAC1B,YAAY,EAAE;gBACZI,OAAO,EAAE,kDAAkD;gBAC3DF,MAAM,EAAE;kBACNc,WAAW,EAAE;gBACf;cACF,CAAC;cACDC,OAAO,EAAE,aAAa;cACtB,WAAW,EAAE;YACf,CAAC,CAAC;YACFC,UAAU,EAAElB,WAAW,CAAC,GAAG,EAAEW,OAAO,EAAEE,SAAS,CAAC;YAChD,eAAe,EAAEb,WAAW,CAAC,KAAK,EAAEW,OAAO,EAAEE,SAAS,CAAC;YACvDM,QAAQ,EAAEnB,WAAW,CAAC,EAAE,EAAEW,OAAO,EAAEE,SAAS;UAC9C;QACF;MACF,CAAC;IACH;IACAd,KAAK,CAACa,SAAS,CAAC,eAAe,CAAC,GAAGQ,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE;MACvEC,KAAK,EAAEb;IACT,CAAC,CAAC;EACJ,CAAC,EAAEV,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}