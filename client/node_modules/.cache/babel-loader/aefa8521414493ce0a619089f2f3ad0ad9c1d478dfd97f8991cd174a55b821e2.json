{"ast": null, "code": "'use strict';\n\nmodule.exports = vala;\nvala.displayName = 'vala';\nvala.aliases = [];\nfunction vala(Prism) {\n  Prism.languages.vala = Prism.languages.extend('clike', {\n    // Classes copied from prism-csharp\n    'class-name': [{\n      // (Foo bar, Bar baz)\n      pattern: /\\b[A-Z]\\w*(?:\\.\\w+)*\\b(?=(?:\\?\\s+|\\*?\\s+\\*?)\\w)/,\n      inside: {\n        punctuation: /\\./\n      }\n    }, {\n      // [Foo]\n      pattern: /(\\[)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    }, {\n      // class Foo : Bar\n      pattern: /(\\b(?:class|interface)\\s+[A-Z]\\w*(?:\\.\\w+)*\\s*:\\s*)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    }, {\n      // class Foo\n      pattern: /((?:\\b(?:class|enum|interface|new|struct)\\s+)|(?:catch\\s+\\())[A-Z]\\w*(?:\\.\\w+)*\\b/,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    }],\n    keyword: /\\b(?:abstract|as|assert|async|base|bool|break|case|catch|char|class|const|construct|continue|default|delegate|delete|do|double|dynamic|else|ensures|enum|errordomain|extern|finally|float|for|foreach|get|if|in|inline|int|int16|int32|int64|int8|interface|internal|is|lock|long|namespace|new|null|out|override|owned|params|private|protected|public|ref|requires|return|set|short|signal|sizeof|size_t|ssize_t|static|string|struct|switch|this|throw|throws|try|typeof|uchar|uint|uint16|uint32|uint64|uint8|ulong|unichar|unowned|ushort|using|value|var|virtual|void|volatile|weak|while|yield)\\b/i,\n    function: /\\b\\w+(?=\\s*\\()/,\n    number: /(?:\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?:f|u?l?)?/i,\n    operator: /\\+\\+|--|&&|\\|\\||<<=?|>>=?|=>|->|~|[+\\-*\\/%&^|=!<>]=?|\\?\\??|\\.\\.\\./,\n    punctuation: /[{}[\\];(),.:]/,\n    constant: /\\b[A-Z0-9_]+\\b/\n  });\n  Prism.languages.insertBefore('vala', 'string', {\n    'raw-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    'template-string': {\n      pattern: /@\"[\\s\\S]*?\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$(?:\\([^)]*\\)|[a-zA-Z]\\w*)/,\n          inside: {\n            delimiter: {\n              pattern: /^\\$\\(?|\\)$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.vala\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  });\n  Prism.languages.insertBefore('vala', 'keyword', {\n    regex: {\n      pattern: /\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[imsx]{0,4}(?=\\s*(?:$|[\\r\\n,.;})\\]]))/,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\//,\n        'regex-flags': /^[a-z]+$/\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "vala", "displayName", "aliases", "Prism", "languages", "extend", "pattern", "inside", "punctuation", "lookbehind", "keyword", "function", "number", "operator", "constant", "insertBefore", "greedy", "alias", "interpolation", "delimiter", "rest", "string", "regex"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/vala.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = vala\nvala.displayName = 'vala'\nvala.aliases = []\nfunction vala(Prism) {\n  Prism.languages.vala = Prism.languages.extend('clike', {\n    // Classes copied from prism-csharp\n    'class-name': [\n      {\n        // (Foo bar, Bar baz)\n        pattern: /\\b[A-Z]\\w*(?:\\.\\w+)*\\b(?=(?:\\?\\s+|\\*?\\s+\\*?)\\w)/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // [Foo]\n        pattern: /(\\[)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // class Foo : Bar\n        pattern:\n          /(\\b(?:class|interface)\\s+[A-Z]\\w*(?:\\.\\w+)*\\s*:\\s*)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // class Foo\n        pattern:\n          /((?:\\b(?:class|enum|interface|new|struct)\\s+)|(?:catch\\s+\\())[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|as|assert|async|base|bool|break|case|catch|char|class|const|construct|continue|default|delegate|delete|do|double|dynamic|else|ensures|enum|errordomain|extern|finally|float|for|foreach|get|if|in|inline|int|int16|int32|int64|int8|interface|internal|is|lock|long|namespace|new|null|out|override|owned|params|private|protected|public|ref|requires|return|set|short|signal|sizeof|size_t|ssize_t|static|string|struct|switch|this|throw|throws|try|typeof|uchar|uint|uint16|uint32|uint64|uint8|ulong|unichar|unowned|ushort|using|value|var|virtual|void|volatile|weak|while|yield)\\b/i,\n    function: /\\b\\w+(?=\\s*\\()/,\n    number:\n      /(?:\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?:f|u?l?)?/i,\n    operator:\n      /\\+\\+|--|&&|\\|\\||<<=?|>>=?|=>|->|~|[+\\-*\\/%&^|=!<>]=?|\\?\\??|\\.\\.\\./,\n    punctuation: /[{}[\\];(),.:]/,\n    constant: /\\b[A-Z0-9_]+\\b/\n  })\n  Prism.languages.insertBefore('vala', 'string', {\n    'raw-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    'template-string': {\n      pattern: /@\"[\\s\\S]*?\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$(?:\\([^)]*\\)|[a-zA-Z]\\w*)/,\n          inside: {\n            delimiter: {\n              pattern: /^\\$\\(?|\\)$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.vala\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.insertBefore('vala', 'keyword', {\n    regex: {\n      pattern:\n        /\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[imsx]{0,4}(?=\\s*(?:$|[\\r\\n,.;})\\]]))/,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\//,\n        'regex-flags': /^[a-z]+$/\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACrD;IACA,YAAY,EAAE,CACZ;MACE;MACAC,OAAO,EAAE,iDAAiD;MAC1DC,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC,EACD;MACE;MACAF,OAAO,EAAE,0BAA0B;MACnCG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC,EACD;MACE;MACAF,OAAO,EACL,yEAAyE;MAC3EG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC,EACD;MACE;MACAF,OAAO,EACL,mFAAmF;MACrFG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC,CACF;IACDE,OAAO,EACL,2kBAA2kB;IAC7kBC,QAAQ,EAAE,gBAAgB;IAC1BC,MAAM,EACJ,0EAA0E;IAC5EC,QAAQ,EACN,mEAAmE;IACrEL,WAAW,EAAE,eAAe;IAC5BM,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFX,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE;IAC7C,YAAY,EAAE;MACZT,OAAO,EAAE,gBAAgB;MACzBU,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT,CAAC;IACD,iBAAiB,EAAE;MACjBX,OAAO,EAAE,aAAa;MACtBU,MAAM,EAAE,IAAI;MACZT,MAAM,EAAE;QACNW,aAAa,EAAE;UACbZ,OAAO,EAAE,6BAA6B;UACtCC,MAAM,EAAE;YACNY,SAAS,EAAE;cACTb,OAAO,EAAE,YAAY;cACrBW,KAAK,EAAE;YACT,CAAC;YACDG,IAAI,EAAEjB,KAAK,CAACC,SAAS,CAACJ;UACxB;QACF,CAAC;QACDqB,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;EACFlB,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE;IAC9CO,KAAK,EAAE;MACLhB,OAAO,EACL,yFAAyF;MAC3FU,MAAM,EAAE,IAAI;MACZT,MAAM,EAAE;QACN,cAAc,EAAE;UACdD,OAAO,EAAE,2BAA2B;UACpCG,UAAU,EAAE,IAAI;UAChBQ,KAAK,EAAE,gBAAgB;UACvBV,MAAM,EAAEJ,KAAK,CAACC,SAAS,CAACkB;QAC1B,CAAC;QACD,iBAAiB,EAAE,KAAK;QACxB,aAAa,EAAE;MACjB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}