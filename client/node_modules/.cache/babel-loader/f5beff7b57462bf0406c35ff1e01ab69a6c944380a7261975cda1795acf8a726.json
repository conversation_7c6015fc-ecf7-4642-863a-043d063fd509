{"ast": null, "code": "/*\nLanguage: OpenSCAD\nAuthor: <PERSON> <<EMAIL>>\nDescription: OpenSCAD is a language for the 3D CAD modeling software of the same name.\nWebsite: https://www.openscad.org\nCategory: scientific\n*/\n\nfunction openscad(hljs) {\n  const SPECIAL_VARS = {\n    className: 'keyword',\n    begin: '\\\\$(f[asn]|t|vp[rtd]|children)'\n  };\n  const LITERALS = {\n    className: 'literal',\n    begin: 'false|true|PI|undef'\n  };\n  const NUMBERS = {\n    className: 'number',\n    begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(e-?\\\\d+)?',\n    // adds 1e5, 1e-10\n    relevance: 0\n  };\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  const PREPRO = {\n    className: 'meta',\n    keywords: {\n      'meta-keyword': 'include use'\n    },\n    begin: 'include|use <',\n    end: '>'\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: ['self', NUMBERS, STRING, SPECIAL_VARS, LITERALS]\n  };\n  const MODIFIERS = {\n    begin: '[*!#%]',\n    relevance: 0\n  };\n  const FUNCTIONS = {\n    className: 'function',\n    beginKeywords: 'module function',\n    end: /=|\\{/,\n    contains: [PARAMS, hljs.UNDERSCORE_TITLE_MODE]\n  };\n  return {\n    name: 'OpenSCAD',\n    aliases: ['scad'],\n    keywords: {\n      keyword: 'function module include use for intersection_for if else \\\\%',\n      literal: 'false true PI undef',\n      built_in: 'circle square polygon text sphere cube cylinder polyhedron translate rotate scale resize mirror multmatrix color offset hull minkowski union difference intersection abs sign sin cos tan acos asin atan atan2 floor round ceil ln log pow sqrt exp rands min max concat lookup str chr search version version_num norm cross parent_module echo import import_dxf dxf_linear_extrude linear_extrude rotate_extrude surface projection render children dxf_cross dxf_dim let assign'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, NUMBERS, PREPRO, STRING, SPECIAL_VARS, MODIFIERS, FUNCTIONS]\n  };\n}\nmodule.exports = openscad;", "map": {"version": 3, "names": ["openscad", "hljs", "SPECIAL_VARS", "className", "begin", "LITERALS", "NUMBERS", "relevance", "STRING", "inherit", "QUOTE_STRING_MODE", "illegal", "PREPRO", "keywords", "end", "PARAMS", "contains", "MODIFIERS", "FUNCTIONS", "beginKeywords", "UNDERSCORE_TITLE_MODE", "name", "aliases", "keyword", "literal", "built_in", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/openscad.js"], "sourcesContent": ["/*\nLanguage: OpenSCAD\nAuthor: <PERSON> <<EMAIL>>\nDescription: OpenSCAD is a language for the 3D CAD modeling software of the same name.\nWebsite: https://www.openscad.org\nCategory: scientific\n*/\n\nfunction openscad(hljs) {\n  const SPECIAL_VARS = {\n    className: 'keyword',\n    begin: '\\\\$(f[asn]|t|vp[rtd]|children)'\n  };\n  const LITERALS = {\n    className: 'literal',\n    begin: 'false|true|PI|undef'\n  };\n  const NUMBERS = {\n    className: 'number',\n    begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(e-?\\\\d+)?', // adds 1e5, 1e-10\n    relevance: 0\n  };\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  const PREPRO = {\n    className: 'meta',\n    keywords: {\n      'meta-keyword': 'include use'\n    },\n    begin: 'include|use <',\n    end: '>'\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: [\n      'self',\n      NUMBERS,\n      STRING,\n      SPECIAL_VARS,\n      LITERALS\n    ]\n  };\n  const MODIFIERS = {\n    begin: '[*!#%]',\n    relevance: 0\n  };\n  const FUNCTIONS = {\n    className: 'function',\n    beginKeywords: 'module function',\n    end: /=|\\{/,\n    contains: [\n      PARAMS,\n      hljs.UNDERSCORE_TITLE_MODE\n    ]\n  };\n\n  return {\n    name: 'OpenSCAD',\n    aliases: [ 'scad' ],\n    keywords: {\n      keyword: 'function module include use for intersection_for if else \\\\%',\n      literal: 'false true PI undef',\n      built_in: 'circle square polygon text sphere cube cylinder polyhedron translate rotate scale resize mirror multmatrix color offset hull minkowski union difference intersection abs sign sin cos tan acos asin atan atan2 floor round ceil ln log pow sqrt exp rands min max concat lookup str chr search version version_num norm cross parent_module echo import import_dxf dxf_linear_extrude linear_extrude rotate_extrude surface projection render children dxf_cross dxf_dim let assign'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      NUMBERS,\n      PREPRO,\n      STRING,\n      SPECIAL_VARS,\n      MODIFIERS,\n      FUNCTIONS\n    ]\n  };\n}\n\nmodule.exports = openscad;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,YAAY,GAAG;IACnBC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,QAAQ,GAAG;IACfF,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE;EACT,CAAC;EACD,MAAME,OAAO,GAAG;IACdH,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,6BAA6B;IAAE;IACtCG,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,MAAM,GAAGP,IAAI,CAACQ,OAAO,CAACR,IAAI,CAACS,iBAAiB,EAAE;IAClDC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMC,MAAM,GAAG;IACbT,SAAS,EAAE,MAAM;IACjBU,QAAQ,EAAE;MACR,cAAc,EAAE;IAClB,CAAC;IACDT,KAAK,EAAE,eAAe;IACtBU,GAAG,EAAE;EACP,CAAC;EACD,MAAMC,MAAM,GAAG;IACbZ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZU,GAAG,EAAE,KAAK;IACVE,QAAQ,EAAE,CACR,MAAM,EACNV,OAAO,EACPE,MAAM,EACNN,YAAY,EACZG,QAAQ;EAEZ,CAAC;EACD,MAAMY,SAAS,GAAG;IAChBb,KAAK,EAAE,QAAQ;IACfG,SAAS,EAAE;EACb,CAAC;EACD,MAAMW,SAAS,GAAG;IAChBf,SAAS,EAAE,UAAU;IACrBgB,aAAa,EAAE,iBAAiB;IAChCL,GAAG,EAAE,MAAM;IACXE,QAAQ,EAAE,CACRD,MAAM,EACNd,IAAI,CAACmB,qBAAqB;EAE9B,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CAAE,MAAM,CAAE;IACnBT,QAAQ,EAAE;MACRU,OAAO,EAAE,8DAA8D;MACvEC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE;IACZ,CAAC;IACDT,QAAQ,EAAE,CACRf,IAAI,CAACyB,mBAAmB,EACxBzB,IAAI,CAAC0B,oBAAoB,EACzBrB,OAAO,EACPM,MAAM,EACNJ,MAAM,EACNN,YAAY,EACZe,SAAS,EACTC,SAAS;EAEb,CAAC;AACH;AAEAU,MAAM,CAACC,OAAO,GAAG7B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}