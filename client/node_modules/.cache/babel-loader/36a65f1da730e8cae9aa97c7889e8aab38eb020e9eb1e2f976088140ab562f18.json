{"ast": null, "code": "/*\nLanguage: FIX\nAuthor: <PERSON> <<EMAIL>>\n*/\n\n/** @type LanguageFn */\nfunction fix(hljs) {\n  return {\n    name: 'FIX',\n    contains: [{\n      begin: /[^\\u2401\\u0001]+/,\n      end: /[\\u2401\\u0001]/,\n      excludeEnd: true,\n      returnBegin: true,\n      returnEnd: false,\n      contains: [{\n        begin: /([^\\u2401\\u0001=]+)/,\n        end: /=([^\\u2401\\u0001=]+)/,\n        returnEnd: true,\n        returnBegin: false,\n        className: 'attr'\n      }, {\n        begin: /=/,\n        end: /([\\u2401\\u0001])/,\n        excludeEnd: true,\n        excludeBegin: true,\n        className: 'string'\n      }]\n    }],\n    case_insensitive: true\n  };\n}\nmodule.exports = fix;", "map": {"version": 3, "names": ["fix", "hljs", "name", "contains", "begin", "end", "excludeEnd", "returnBegin", "returnEnd", "className", "excludeBegin", "case_insensitive", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/fix.js"], "sourcesContent": ["/*\nLanguage: FIX\nAuthor: <PERSON> <<EMAIL>>\n*/\n\n/** @type LanguageFn */\nfunction fix(hljs) {\n  return {\n    name: 'FIX',\n    contains: [{\n      begin: /[^\\u2401\\u0001]+/,\n      end: /[\\u2401\\u0001]/,\n      excludeEnd: true,\n      returnBegin: true,\n      returnEnd: false,\n      contains: [\n        {\n          begin: /([^\\u2401\\u0001=]+)/,\n          end: /=([^\\u2401\\u0001=]+)/,\n          returnEnd: true,\n          returnBegin: false,\n          className: 'attr'\n        },\n        {\n          begin: /=/,\n          end: /([\\u2401\\u0001])/,\n          excludeEnd: true,\n          excludeBegin: true,\n          className: 'string'\n        }\n      ]\n    }],\n    case_insensitive: true\n  };\n}\n\nmodule.exports = fix;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAE,kBAAkB;MACzBC,GAAG,EAAE,gBAAgB;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,KAAK;MAChBL,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,qBAAqB;QAC5BC,GAAG,EAAE,sBAAsB;QAC3BG,SAAS,EAAE,IAAI;QACfD,WAAW,EAAE,KAAK;QAClBE,SAAS,EAAE;MACb,CAAC,EACD;QACEL,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE,kBAAkB;QACvBC,UAAU,EAAE,IAAI;QAChBI,YAAY,EAAE,IAAI;QAClBD,SAAS,EAAE;MACb,CAAC;IAEL,CAAC,CAAC;IACFE,gBAAgB,EAAE;EACpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}