{"ast": null, "code": "'use strict';\n\nvar refractorPhp = require('./php.js');\nmodule.exports = phpExtras;\nphpExtras.displayName = 'phpExtras';\nphpExtras.aliases = [];\nfunction phpExtras(Prism) {\n  Prism.register(refractorPhp);\n  Prism.languages.insertBefore('php', 'variable', {\n    this: {\n      pattern: /\\$this\\b/,\n      alias: 'keyword'\n    },\n    global: /\\$(?:GLOBALS|HTTP_RAW_POST_DATA|_(?:COOKIE|ENV|FILES|GET|POST|REQUEST|SERVER|SESSION)|argc|argv|http_response_header|php_errormsg)\\b/,\n    scope: {\n      pattern: /\\b[\\w\\\\]+::/,\n      inside: {\n        keyword: /\\b(?:parent|self|static)\\b/,\n        punctuation: /::|\\\\/\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["refractorPhp", "require", "module", "exports", "phpExtras", "displayName", "aliases", "Prism", "register", "languages", "insertBefore", "this", "pattern", "alias", "global", "scope", "inside", "keyword", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/php-extras.js"], "sourcesContent": ["'use strict'\nvar refractorPhp = require('./php.js')\nmodule.exports = phpExtras\nphpExtras.displayName = 'phpExtras'\nphpExtras.aliases = []\nfunction phpExtras(Prism) {\n  Prism.register(refractorPhp)\n  Prism.languages.insertBefore('php', 'variable', {\n    this: {\n      pattern: /\\$this\\b/,\n      alias: 'keyword'\n    },\n    global:\n      /\\$(?:GLOBALS|HTTP_RAW_POST_DATA|_(?:COOKIE|ENV|FILES|GET|POST|REQUEST|SERVER|SESSION)|argc|argv|http_response_header|php_errormsg)\\b/,\n    scope: {\n      pattern: /\\b[\\w\\\\]+::/,\n      inside: {\n        keyword: /\\b(?:parent|self|static)\\b/,\n        punctuation: /::|\\\\/\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,UAAU,CAAC;AACtCC,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxBA,KAAK,CAACC,QAAQ,CAACR,YAAY,CAAC;EAC5BO,KAAK,CAACE,SAAS,CAACC,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE;IAC9CC,IAAI,EAAE;MACJC,OAAO,EAAE,UAAU;MACnBC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EACJ,sIAAsI;IACxIC,KAAK,EAAE;MACLH,OAAO,EAAE,aAAa;MACtBI,MAAM,EAAE;QACNC,OAAO,EAAE,4BAA4B;QACrCC,WAAW,EAAE;MACf;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}