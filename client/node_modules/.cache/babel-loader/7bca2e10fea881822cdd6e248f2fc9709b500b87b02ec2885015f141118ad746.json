{"ast": null, "code": "'use strict';\n\nmodule.exports = diff;\ndiff.displayName = 'diff';\ndiff.aliases = [];\nfunction diff(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.diff = {\n      coord: [\n      // Match all kinds of coord lines (prefixed by \"+++\", \"---\" or \"***\").\n      /^(?:\\*{3}|-{3}|\\+{3}).*$/m,\n      // Match \"@@ ... @@\" coord lines in unified diff.\n      /^@@.*@@$/m,\n      // Match coord lines in normal diff (starts with a number).\n      /^\\d.*$/m] // deleted, inserted, unchanged, diff\n    };\n    /**\n     * A map from the name of a block to its line prefix.\n     *\n     * @type {Object<string, string>}\n     */\n    var PREFIXES = {\n      'deleted-sign': '-',\n      'deleted-arrow': '<',\n      'inserted-sign': '+',\n      'inserted-arrow': '>',\n      unchanged: ' ',\n      diff: '!'\n    }; // add a token for each prefix\n    Object.keys(PREFIXES).forEach(function (name) {\n      var prefix = PREFIXES[name];\n      var alias = [];\n      if (!/^\\w+$/.test(name)) {\n        // \"deleted-sign\" -> \"deleted\"\n        alias.push(/\\w+/.exec(name)[0]);\n      }\n      if (name === 'diff') {\n        alias.push('bold');\n      }\n      Prism.languages.diff[name] = {\n        pattern: RegExp('^(?:[' + prefix + '].*(?:\\r\\n?|\\n|(?![\\\\s\\\\S])))+', 'm'),\n        alias: alias,\n        inside: {\n          line: {\n            pattern: /(.)(?=[\\s\\S]).*(?:\\r\\n?|\\n)?/,\n            lookbehind: true\n          },\n          prefix: {\n            pattern: /[\\s\\S]/,\n            alias: /\\w+/.exec(name)[0]\n          }\n        }\n      };\n    }); // make prefixes available to Diff plugin\n    Object.defineProperty(Prism.languages.diff, 'PREFIXES', {\n      value: PREFIXES\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "diff", "displayName", "aliases", "Prism", "languages", "coord", "PREFIXES", "unchanged", "Object", "keys", "for<PERSON>ach", "name", "prefix", "alias", "test", "push", "exec", "pattern", "RegExp", "inside", "line", "lookbehind", "defineProperty", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/diff.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = diff\ndiff.displayName = 'diff'\ndiff.aliases = []\nfunction diff(Prism) {\n  ;(function (Prism) {\n    Prism.languages.diff = {\n      coord: [\n        // Match all kinds of coord lines (prefixed by \"+++\", \"---\" or \"***\").\n        /^(?:\\*{3}|-{3}|\\+{3}).*$/m, // Match \"@@ ... @@\" coord lines in unified diff.\n        /^@@.*@@$/m, // Match coord lines in normal diff (starts with a number).\n        /^\\d.*$/m\n      ] // deleted, inserted, unchanged, diff\n    }\n    /**\n     * A map from the name of a block to its line prefix.\n     *\n     * @type {Object<string, string>}\n     */\n    var PREFIXES = {\n      'deleted-sign': '-',\n      'deleted-arrow': '<',\n      'inserted-sign': '+',\n      'inserted-arrow': '>',\n      unchanged: ' ',\n      diff: '!'\n    } // add a token for each prefix\n    Object.keys(PREFIXES).forEach(function (name) {\n      var prefix = PREFIXES[name]\n      var alias = []\n      if (!/^\\w+$/.test(name)) {\n        // \"deleted-sign\" -> \"deleted\"\n        alias.push(/\\w+/.exec(name)[0])\n      }\n      if (name === 'diff') {\n        alias.push('bold')\n      }\n      Prism.languages.diff[name] = {\n        pattern: RegExp(\n          '^(?:[' + prefix + '].*(?:\\r\\n?|\\n|(?![\\\\s\\\\S])))+',\n          'm'\n        ),\n        alias: alias,\n        inside: {\n          line: {\n            pattern: /(.)(?=[\\s\\S]).*(?:\\r\\n?|\\n)?/,\n            lookbehind: true\n          },\n          prefix: {\n            pattern: /[\\s\\S]/,\n            alias: /\\w+/.exec(name)[0]\n          }\n        }\n      }\n    }) // make prefixes available to Diff plugin\n    Object.defineProperty(Prism.languages.diff, 'PREFIXES', {\n      value: PREFIXES\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;MACrBK,KAAK,EAAE;MACL;MACA,2BAA2B;MAAE;MAC7B,WAAW;MAAE;MACb,SAAS,CACV,CAAC;IACJ,CAAC;IACD;AACJ;AACA;AACA;AACA;IACI,IAAIC,QAAQ,GAAG;MACb,cAAc,EAAE,GAAG;MACnB,eAAe,EAAE,GAAG;MACpB,eAAe,EAAE,GAAG;MACpB,gBAAgB,EAAE,GAAG;MACrBC,SAAS,EAAE,GAAG;MACdP,IAAI,EAAE;IACR,CAAC,EAAC;IACFQ,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC5C,IAAIC,MAAM,GAAGN,QAAQ,CAACK,IAAI,CAAC;MAC3B,IAAIE,KAAK,GAAG,EAAE;MACd,IAAI,CAAC,OAAO,CAACC,IAAI,CAACH,IAAI,CAAC,EAAE;QACvB;QACAE,KAAK,CAACE,IAAI,CAAC,KAAK,CAACC,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC;MACA,IAAIA,IAAI,KAAK,MAAM,EAAE;QACnBE,KAAK,CAACE,IAAI,CAAC,MAAM,CAAC;MACpB;MACAZ,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACW,IAAI,CAAC,GAAG;QAC3BM,OAAO,EAAEC,MAAM,CACb,OAAO,GAAGN,MAAM,GAAG,gCAAgC,EACnD,GACF,CAAC;QACDC,KAAK,EAAEA,KAAK;QACZM,MAAM,EAAE;UACNC,IAAI,EAAE;YACJH,OAAO,EAAE,8BAA8B;YACvCI,UAAU,EAAE;UACd,CAAC;UACDT,MAAM,EAAE;YACNK,OAAO,EAAE,QAAQ;YACjBJ,KAAK,EAAE,KAAK,CAACG,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC;UAC3B;QACF;MACF,CAAC;IACH,CAAC,CAAC,EAAC;IACHH,MAAM,CAACc,cAAc,CAACnB,KAAK,CAACC,SAAS,CAACJ,IAAI,EAAE,UAAU,EAAE;MACtDuB,KAAK,EAAEjB;IACT,CAAC,CAAC;EACJ,CAAC,EAAEH,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}