{"ast": null, "code": "/*\nLanguage: Nginx config\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://www.nginx.com\n*/\n\nfunction nginx(hljs) {\n  const VAR = {\n    className: 'variable',\n    variants: [{\n      begin: /\\$\\d+/\n    }, {\n      begin: /\\$\\{/,\n      end: /\\}/\n    }, {\n      begin: /[$@]/ + hljs.UNDERSCORE_IDENT_RE\n    }]\n  };\n  const DEFAULT = {\n    endsWithParent: true,\n    keywords: {\n      $pattern: '[a-z/_]+',\n      literal: 'on off yes no true false none blocked debug info notice warn error crit ' + 'select break last permanent redirect kqueue rtsig epoll poll /dev/poll'\n    },\n    relevance: 0,\n    illegal: '=>',\n    contains: [hljs.HASH_COMMENT_MODE, {\n      className: 'string',\n      contains: [hljs.BACKSLASH_ESCAPE, VAR],\n      variants: [{\n        begin: /\"/,\n        end: /\"/\n      }, {\n        begin: /'/,\n        end: /'/\n      }]\n    },\n    // this swallows entire URLs to avoid detecting numbers within\n    {\n      begin: '([a-z]+):/',\n      end: '\\\\s',\n      endsWithParent: true,\n      excludeEnd: true,\n      contains: [VAR]\n    }, {\n      className: 'regexp',\n      contains: [hljs.BACKSLASH_ESCAPE, VAR],\n      variants: [{\n        begin: \"\\\\s\\\\^\",\n        end: \"\\\\s|\\\\{|;\",\n        returnEnd: true\n      },\n      // regexp locations (~, ~*)\n      {\n        begin: \"~\\\\*?\\\\s+\",\n        end: \"\\\\s|\\\\{|;\",\n        returnEnd: true\n      },\n      // *.example.com\n      {\n        begin: \"\\\\*(\\\\.[a-z\\\\-]+)+\"\n      },\n      // sub.example.*\n      {\n        begin: \"([a-z\\\\-]+\\\\.)+\\\\*\"\n      }]\n    },\n    // IP\n    {\n      className: 'number',\n      begin: '\\\\b\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3}(:\\\\d{1,5})?\\\\b'\n    },\n    // units\n    {\n      className: 'number',\n      begin: '\\\\b\\\\d+[kKmMgGdshdwy]*\\\\b',\n      relevance: 0\n    }, VAR]\n  };\n  return {\n    name: 'Nginx config',\n    aliases: ['nginxconf'],\n    contains: [hljs.HASH_COMMENT_MODE, {\n      begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s+\\\\{',\n      returnBegin: true,\n      end: /\\{/,\n      contains: [{\n        className: 'section',\n        begin: hljs.UNDERSCORE_IDENT_RE\n      }],\n      relevance: 0\n    }, {\n      begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s',\n      end: ';|\\\\{',\n      returnBegin: true,\n      contains: [{\n        className: 'attribute',\n        begin: hljs.UNDERSCORE_IDENT_RE,\n        starts: DEFAULT\n      }],\n      relevance: 0\n    }],\n    illegal: '[^\\\\s\\\\}]'\n  };\n}\nmodule.exports = nginx;", "map": {"version": 3, "names": ["nginx", "hljs", "VAR", "className", "variants", "begin", "end", "UNDERSCORE_IDENT_RE", "DEFAULT", "endsWithParent", "keywords", "$pattern", "literal", "relevance", "illegal", "contains", "HASH_COMMENT_MODE", "BACKSLASH_ESCAPE", "excludeEnd", "returnEnd", "name", "aliases", "returnBegin", "starts", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/nginx.js"], "sourcesContent": ["/*\nLanguage: Nginx config\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://www.nginx.com\n*/\n\nfunction nginx(hljs) {\n  const VAR = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$\\d+/\n      },\n      {\n        begin: /\\$\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /[$@]/ + hljs.UNDERSCORE_IDENT_RE\n      }\n    ]\n  };\n  const DEFAULT = {\n    endsWithParent: true,\n    keywords: {\n      $pattern: '[a-z/_]+',\n      literal:\n        'on off yes no true false none blocked debug info notice warn error crit ' +\n        'select break last permanent redirect kqueue rtsig epoll poll /dev/poll'\n    },\n    relevance: 0,\n    illegal: '=>',\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'string',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          VAR\n        ],\n        variants: [\n          {\n            begin: /\"/,\n            end: /\"/\n          },\n          {\n            begin: /'/,\n            end: /'/\n          }\n        ]\n      },\n      // this swallows entire URLs to avoid detecting numbers within\n      {\n        begin: '([a-z]+):/',\n        end: '\\\\s',\n        endsWithParent: true,\n        excludeEnd: true,\n        contains: [ VAR ]\n      },\n      {\n        className: 'regexp',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          VAR\n        ],\n        variants: [\n          {\n            begin: \"\\\\s\\\\^\",\n            end: \"\\\\s|\\\\{|;\",\n            returnEnd: true\n          },\n          // regexp locations (~, ~*)\n          {\n            begin: \"~\\\\*?\\\\s+\",\n            end: \"\\\\s|\\\\{|;\",\n            returnEnd: true\n          },\n          // *.example.com\n          {\n            begin: \"\\\\*(\\\\.[a-z\\\\-]+)+\"\n          },\n          // sub.example.*\n          {\n            begin: \"([a-z\\\\-]+\\\\.)+\\\\*\"\n          }\n        ]\n      },\n      // IP\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3}(:\\\\d{1,5})?\\\\b'\n      },\n      // units\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d+[kKmMgGdshdwy]*\\\\b',\n        relevance: 0\n      },\n      VAR\n    ]\n  };\n\n  return {\n    name: 'Nginx config',\n    aliases: [ 'nginxconf' ],\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s+\\\\{',\n        returnBegin: true,\n        end: /\\{/,\n        contains: [\n          {\n            className: 'section',\n            begin: hljs.UNDERSCORE_IDENT_RE\n          }\n        ],\n        relevance: 0\n      },\n      {\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s',\n        end: ';|\\\\{',\n        returnBegin: true,\n        contains: [\n          {\n            className: 'attribute',\n            begin: hljs.UNDERSCORE_IDENT_RE,\n            starts: DEFAULT\n          }\n        ],\n        relevance: 0\n      }\n    ],\n    illegal: '[^\\\\s\\\\}]'\n  };\n}\n\nmodule.exports = nginx;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,GAAG,GAAG;IACVC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,MAAM,GAAGJ,IAAI,CAACM;IACvB,CAAC;EAEL,CAAC;EACD,MAAMC,OAAO,GAAG;IACdC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EACL,0EAA0E,GAC1E;IACJ,CAAC;IACDC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRd,IAAI,CAACe,iBAAiB,EACtB;MACEb,SAAS,EAAE,QAAQ;MACnBY,QAAQ,EAAE,CACRd,IAAI,CAACgB,gBAAgB,EACrBf,GAAG,CACJ;MACDE,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE;MACP,CAAC,EACD;QACED,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE;MACP,CAAC;IAEL,CAAC;IACD;IACA;MACED,KAAK,EAAE,YAAY;MACnBC,GAAG,EAAE,KAAK;MACVG,cAAc,EAAE,IAAI;MACpBS,UAAU,EAAE,IAAI;MAChBH,QAAQ,EAAE,CAAEb,GAAG;IACjB,CAAC,EACD;MACEC,SAAS,EAAE,QAAQ;MACnBY,QAAQ,EAAE,CACRd,IAAI,CAACgB,gBAAgB,EACrBf,GAAG,CACJ;MACDE,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE,WAAW;QAChBa,SAAS,EAAE;MACb,CAAC;MACD;MACA;QACEd,KAAK,EAAE,WAAW;QAClBC,GAAG,EAAE,WAAW;QAChBa,SAAS,EAAE;MACb,CAAC;MACD;MACA;QACEd,KAAK,EAAE;MACT,CAAC;MACD;MACA;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnBE,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnBE,KAAK,EAAE,2BAA2B;MAClCQ,SAAS,EAAE;IACb,CAAC,EACDX,GAAG;EAEP,CAAC;EAED,OAAO;IACLkB,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,CAAE,WAAW,CAAE;IACxBN,QAAQ,EAAE,CACRd,IAAI,CAACe,iBAAiB,EACtB;MACEX,KAAK,EAAEJ,IAAI,CAACM,mBAAmB,GAAG,SAAS;MAC3Ce,WAAW,EAAE,IAAI;MACjBhB,GAAG,EAAE,IAAI;MACTS,QAAQ,EAAE,CACR;QACEZ,SAAS,EAAE,SAAS;QACpBE,KAAK,EAAEJ,IAAI,CAACM;MACd,CAAC,CACF;MACDM,SAAS,EAAE;IACb,CAAC,EACD;MACER,KAAK,EAAEJ,IAAI,CAACM,mBAAmB,GAAG,KAAK;MACvCD,GAAG,EAAE,OAAO;MACZgB,WAAW,EAAE,IAAI;MACjBP,QAAQ,EAAE,CACR;QACEZ,SAAS,EAAE,WAAW;QACtBE,KAAK,EAAEJ,IAAI,CAACM,mBAAmB;QAC/BgB,MAAM,EAAEf;MACV,CAAC,CACF;MACDK,SAAS,EAAE;IACb,CAAC,CACF;IACDC,OAAO,EAAE;EACX,CAAC;AACH;AAEAU,MAAM,CAACC,OAAO,GAAGzB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}