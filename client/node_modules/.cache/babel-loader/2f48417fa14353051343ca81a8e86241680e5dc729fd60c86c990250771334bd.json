{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Perl\nAuthor: <PERSON> <gojpe<PERSON>@yandex.ru>\nWebsite: https://www.perl.org\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction perl(hljs) {\n  const KEYWORDS = ['abs', 'accept', 'alarm', 'and', 'atan2', 'bind', 'binmode', 'bless', 'break', 'caller', 'chdir', 'chmod', 'chomp', 'chop', 'chown', 'chr', 'chroot', 'close', 'closedir', 'connect', 'continue', 'cos', 'crypt', 'dbmclose', 'dbmopen', 'defined', 'delete', 'die', 'do', 'dump', 'each', 'else', 'elsif', 'endgrent', 'endhostent', 'endnetent', 'endprotoent', 'endpwent', 'endservent', 'eof', 'eval', 'exec', 'exists', 'exit', 'exp', 'fcntl', 'fileno', 'flock', 'for', 'foreach', 'fork', 'format', 'formline', 'getc', 'getgrent', 'getgrgid', 'getgrnam', 'gethostbyaddr', 'gethostbyname', 'gethostent', 'getlogin', 'getnetbyaddr', 'getnetbyname', 'getnetent', 'getpeername', 'getpgrp', 'getpriority', 'getprotobyname', 'getprotobynumber', 'getprotoent', 'getpwent', 'getpwnam', 'getpwuid', 'getservbyname', 'getservbyport', 'getservent', 'getsockname', 'getsockopt', 'given', 'glob', 'gmtime', 'goto', 'grep', 'gt', 'hex', 'if', 'index', 'int', 'ioctl', 'join', 'keys', 'kill', 'last', 'lc', 'lcfirst', 'length', 'link', 'listen', 'local', 'localtime', 'log', 'lstat', 'lt', 'ma', 'map', 'mkdir', 'msgctl', 'msgget', 'msgrcv', 'msgsnd', 'my', 'ne', 'next', 'no', 'not', 'oct', 'open', 'opendir', 'or', 'ord', 'our', 'pack', 'package', 'pipe', 'pop', 'pos', 'print', 'printf', 'prototype', 'push', 'q|0', 'qq', 'quotemeta', 'qw', 'qx', 'rand', 'read', 'readdir', 'readline', 'readlink', 'readpipe', 'recv', 'redo', 'ref', 'rename', 'require', 'reset', 'return', 'reverse', 'rewinddir', 'rindex', 'rmdir', 'say', 'scalar', 'seek', 'seekdir', 'select', 'semctl', 'semget', 'semop', 'send', 'setgrent', 'sethostent', 'setnetent', 'setpgrp', 'setpriority', 'setprotoent', 'setpwent', 'setservent', 'setsockopt', 'shift', 'shmctl', 'shmget', 'shmread', 'shmwrite', 'shutdown', 'sin', 'sleep', 'socket', 'socketpair', 'sort', 'splice', 'split', 'sprintf', 'sqrt', 'srand', 'stat', 'state', 'study', 'sub', 'substr', 'symlink', 'syscall', 'sysopen', 'sysread', 'sysseek', 'system', 'syswrite', 'tell', 'telldir', 'tie', 'tied', 'time', 'times', 'tr', 'truncate', 'uc', 'ucfirst', 'umask', 'undef', 'unless', 'unlink', 'unpack', 'unshift', 'untie', 'until', 'use', 'utime', 'values', 'vec', 'wait', 'waitpid', 'wantarray', 'warn', 'when', 'while', 'write', 'x|0', 'xor', 'y|0'];\n\n  // https://perldoc.perl.org/perlre#Modifiers\n  const REGEX_MODIFIERS = /[dualxmsipngr]{0,12}/; // aa and xx are valid, making max length 12\n  const PERL_KEYWORDS = {\n    $pattern: /[\\w.]+/,\n    keyword: KEYWORDS.join(\" \")\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '[$@]\\\\{',\n    end: '\\\\}',\n    keywords: PERL_KEYWORDS\n  };\n  const METHOD = {\n    begin: /->\\{/,\n    end: /\\}/\n    // contains defined later\n  };\n  const VAR = {\n    variants: [{\n      begin: /\\$\\d/\n    }, {\n      begin: concat(/[$%@](\\^\\w\\b|#\\w+(::\\w+)*|\\{\\w+\\}|\\w+(::\\w*)*)/,\n      // negative look-ahead tries to avoid matching patterns that are not\n      // Perl at all like $ident$, @ident@, etc.\n      `(?![A-Za-z])(?![@$%])`)\n    }, {\n      begin: /[$%@][^\\s\\w{]/,\n      relevance: 0\n    }]\n  };\n  const STRING_CONTAINS = [hljs.BACKSLASH_ESCAPE, SUBST, VAR];\n  const REGEX_DELIMS = [/!/, /\\//, /\\|/, /\\?/, /'/, /\"/,\n  // valid but infrequent and weird\n  /#/ // valid but infrequent and weird\n  ];\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_DOUBLE_RE = (prefix, open, close = '\\\\1') => {\n    const middle = close === '\\\\1' ? close : concat(close, open);\n    return concat(concat(\"(?:\", prefix, \")\"), open, /(?:\\\\.|[^\\\\\\/])*?/, middle, /(?:\\\\.|[^\\\\\\/])*?/, close, REGEX_MODIFIERS);\n  };\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_RE = (prefix, open, close) => {\n    return concat(concat(\"(?:\", prefix, \")\"), open, /(?:\\\\.|[^\\\\\\/])*?/, close, REGEX_MODIFIERS);\n  };\n  const PERL_DEFAULT_CONTAINS = [VAR, hljs.HASH_COMMENT_MODE, hljs.COMMENT(/^=\\w/, /=cut/, {\n    endsWithParent: true\n  }), METHOD, {\n    className: 'string',\n    contains: STRING_CONTAINS,\n    variants: [{\n      begin: 'q[qwxr]?\\\\s*\\\\(',\n      end: '\\\\)',\n      relevance: 5\n    }, {\n      begin: 'q[qwxr]?\\\\s*\\\\[',\n      end: '\\\\]',\n      relevance: 5\n    }, {\n      begin: 'q[qwxr]?\\\\s*\\\\{',\n      end: '\\\\}',\n      relevance: 5\n    }, {\n      begin: 'q[qwxr]?\\\\s*\\\\|',\n      end: '\\\\|',\n      relevance: 5\n    }, {\n      begin: 'q[qwxr]?\\\\s*<',\n      end: '>',\n      relevance: 5\n    }, {\n      begin: 'qw\\\\s+q',\n      end: 'q',\n      relevance: 5\n    }, {\n      begin: '\\'',\n      end: '\\'',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: '\"',\n      end: '\"'\n    }, {\n      begin: '`',\n      end: '`',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: /\\{\\w+\\}/,\n      relevance: 0\n    }, {\n      begin: '-?\\\\w+\\\\s*=>',\n      relevance: 0\n    }]\n  }, {\n    className: 'number',\n    begin: '(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b',\n    relevance: 0\n  }, {\n    // regexp container\n    begin: '(\\\\/\\\\/|' + hljs.RE_STARTERS_RE + '|\\\\b(split|return|print|reverse|grep)\\\\b)\\\\s*',\n    keywords: 'split return print reverse grep',\n    relevance: 0,\n    contains: [hljs.HASH_COMMENT_MODE, {\n      className: 'regexp',\n      variants: [\n      // allow matching common delimiters\n      {\n        begin: PAIRED_DOUBLE_RE(\"s|tr|y\", either(...REGEX_DELIMS))\n      },\n      // and then paired delmis\n      {\n        begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\(\", \"\\\\)\")\n      }, {\n        begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\[\", \"\\\\]\")\n      }, {\n        begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\{\", \"\\\\}\")\n      }],\n      relevance: 2\n    }, {\n      className: 'regexp',\n      variants: [{\n        // could be a comment in many languages so do not count\n        // as relevant\n        begin: /(m|qr)\\/\\//,\n        relevance: 0\n      },\n      // prefix is optional with /regex/\n      {\n        begin: PAIRED_RE(\"(?:m|qr)?\", /\\//, /\\//)\n      },\n      // allow matching common delimiters\n      {\n        begin: PAIRED_RE(\"m|qr\", either(...REGEX_DELIMS), /\\1/)\n      },\n      // allow common paired delmins\n      {\n        begin: PAIRED_RE(\"m|qr\", /\\(/, /\\)/)\n      }, {\n        begin: PAIRED_RE(\"m|qr\", /\\[/, /\\]/)\n      }, {\n        begin: PAIRED_RE(\"m|qr\", /\\{/, /\\}/)\n      }]\n    }]\n  }, {\n    className: 'function',\n    beginKeywords: 'sub',\n    end: '(\\\\s*\\\\(.*?\\\\))?[;{]',\n    excludeEnd: true,\n    relevance: 5,\n    contains: [hljs.TITLE_MODE]\n  }, {\n    begin: '-\\\\w\\\\b',\n    relevance: 0\n  }, {\n    begin: \"^__DATA__$\",\n    end: \"^__END__$\",\n    subLanguage: 'mojolicious',\n    contains: [{\n      begin: \"^@@.*\",\n      end: \"$\",\n      className: \"comment\"\n    }]\n  }];\n  SUBST.contains = PERL_DEFAULT_CONTAINS;\n  METHOD.contains = PERL_DEFAULT_CONTAINS;\n  return {\n    name: 'Perl',\n    aliases: ['pl', 'pm'],\n    keywords: PERL_KEYWORDS,\n    contains: PERL_DEFAULT_CONTAINS\n  };\n}\nmodule.exports = perl;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "either", "perl", "hljs", "KEYWORDS", "REGEX_MODIFIERS", "PERL_KEYWORDS", "$pattern", "keyword", "SUBST", "className", "begin", "end", "keywords", "METHOD", "VAR", "variants", "relevance", "STRING_CONTAINS", "BACKSLASH_ESCAPE", "REGEX_DELIMS", "PAIRED_DOUBLE_RE", "prefix", "open", "close", "middle", "PAIRED_RE", "PERL_DEFAULT_CONTAINS", "HASH_COMMENT_MODE", "COMMENT", "endsWithParent", "contains", "RE_STARTERS_RE", "beginKeywords", "excludeEnd", "TITLE_MODE", "subLanguage", "name", "aliases", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/perl.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Perl\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.perl.org\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction perl(hljs) {\n  const KEYWORDS = [\n    'abs',\n    'accept',\n    'alarm',\n    'and',\n    'atan2',\n    'bind',\n    'binmode',\n    'bless',\n    'break',\n    'caller',\n    'chdir',\n    'chmod',\n    'chomp',\n    'chop',\n    'chown',\n    'chr',\n    'chroot',\n    'close',\n    'closedir',\n    'connect',\n    'continue',\n    'cos',\n    'crypt',\n    'dbmclose',\n    'dbmopen',\n    'defined',\n    'delete',\n    'die',\n    'do',\n    'dump',\n    'each',\n    'else',\n    'elsif',\n    'endgrent',\n    'endhostent',\n    'endnetent',\n    'endprotoent',\n    'endpwent',\n    'endservent',\n    'eof',\n    'eval',\n    'exec',\n    'exists',\n    'exit',\n    'exp',\n    'fcntl',\n    'fileno',\n    'flock',\n    'for',\n    'foreach',\n    'fork',\n    'format',\n    'formline',\n    'getc',\n    'getgrent',\n    'getgrgid',\n    'getgrnam',\n    'gethostbyaddr',\n    'gethostbyname',\n    'gethostent',\n    'getlogin',\n    'getnetbyaddr',\n    'getnetbyname',\n    'getnetent',\n    'getpeername',\n    'getpgrp',\n    'getpriority',\n    'getprotobyname',\n    'getprotobynumber',\n    'getprotoent',\n    'getpwent',\n    'getpwnam',\n    'getpwuid',\n    'getservbyname',\n    'getservbyport',\n    'getservent',\n    'getsockname',\n    'getsockopt',\n    'given',\n    'glob',\n    'gmtime',\n    'goto',\n    'grep',\n    'gt',\n    'hex',\n    'if',\n    'index',\n    'int',\n    'ioctl',\n    'join',\n    'keys',\n    'kill',\n    'last',\n    'lc',\n    'lcfirst',\n    'length',\n    'link',\n    'listen',\n    'local',\n    'localtime',\n    'log',\n    'lstat',\n    'lt',\n    'ma',\n    'map',\n    'mkdir',\n    'msgctl',\n    'msgget',\n    'msgrcv',\n    'msgsnd',\n    'my',\n    'ne',\n    'next',\n    'no',\n    'not',\n    'oct',\n    'open',\n    'opendir',\n    'or',\n    'ord',\n    'our',\n    'pack',\n    'package',\n    'pipe',\n    'pop',\n    'pos',\n    'print',\n    'printf',\n    'prototype',\n    'push',\n    'q|0',\n    'qq',\n    'quotemeta',\n    'qw',\n    'qx',\n    'rand',\n    'read',\n    'readdir',\n    'readline',\n    'readlink',\n    'readpipe',\n    'recv',\n    'redo',\n    'ref',\n    'rename',\n    'require',\n    'reset',\n    'return',\n    'reverse',\n    'rewinddir',\n    'rindex',\n    'rmdir',\n    'say',\n    'scalar',\n    'seek',\n    'seekdir',\n    'select',\n    'semctl',\n    'semget',\n    'semop',\n    'send',\n    'setgrent',\n    'sethostent',\n    'setnetent',\n    'setpgrp',\n    'setpriority',\n    'setprotoent',\n    'setpwent',\n    'setservent',\n    'setsockopt',\n    'shift',\n    'shmctl',\n    'shmget',\n    'shmread',\n    'shmwrite',\n    'shutdown',\n    'sin',\n    'sleep',\n    'socket',\n    'socketpair',\n    'sort',\n    'splice',\n    'split',\n    'sprintf',\n    'sqrt',\n    'srand',\n    'stat',\n    'state',\n    'study',\n    'sub',\n    'substr',\n    'symlink',\n    'syscall',\n    'sysopen',\n    'sysread',\n    'sysseek',\n    'system',\n    'syswrite',\n    'tell',\n    'telldir',\n    'tie',\n    'tied',\n    'time',\n    'times',\n    'tr',\n    'truncate',\n    'uc',\n    'ucfirst',\n    'umask',\n    'undef',\n    'unless',\n    'unlink',\n    'unpack',\n    'unshift',\n    'untie',\n    'until',\n    'use',\n    'utime',\n    'values',\n    'vec',\n    'wait',\n    'waitpid',\n    'wantarray',\n    'warn',\n    'when',\n    'while',\n    'write',\n    'x|0',\n    'xor',\n    'y|0'\n  ];\n\n  // https://perldoc.perl.org/perlre#Modifiers\n  const REGEX_MODIFIERS = /[dualxmsipngr]{0,12}/; // aa and xx are valid, making max length 12\n  const PERL_KEYWORDS = {\n    $pattern: /[\\w.]+/,\n    keyword: KEYWORDS.join(\" \")\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '[$@]\\\\{',\n    end: '\\\\}',\n    keywords: PERL_KEYWORDS\n  };\n  const METHOD = {\n    begin: /->\\{/,\n    end: /\\}/\n    // contains defined later\n  };\n  const VAR = {\n    variants: [\n      {\n        begin: /\\$\\d/\n      },\n      {\n        begin: concat(\n          /[$%@](\\^\\w\\b|#\\w+(::\\w+)*|\\{\\w+\\}|\\w+(::\\w*)*)/,\n          // negative look-ahead tries to avoid matching patterns that are not\n          // Perl at all like $ident$, @ident@, etc.\n          `(?![A-Za-z])(?![@$%])`\n        )\n      },\n      {\n        begin: /[$%@][^\\s\\w{]/,\n        relevance: 0\n      }\n    ]\n  };\n  const STRING_CONTAINS = [\n    hljs.BACKSLASH_ESCAPE,\n    SUBST,\n    VAR\n  ];\n  const REGEX_DELIMS = [\n    /!/,\n    /\\//,\n    /\\|/,\n    /\\?/,\n    /'/,\n    /\"/, // valid but infrequent and weird\n    /#/ // valid but infrequent and weird\n  ];\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_DOUBLE_RE = (prefix, open, close = '\\\\1') => {\n    const middle = (close === '\\\\1')\n      ? close\n      : concat(close, open);\n    return concat(\n      concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      middle,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_RE = (prefix, open, close) => {\n    return concat(\n      concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  const PERL_DEFAULT_CONTAINS = [\n    VAR,\n    hljs.HASH_COMMENT_MODE,\n    hljs.COMMENT(\n      /^=\\w/,\n      /=cut/,\n      {\n        endsWithParent: true\n      }\n    ),\n    METHOD,\n    {\n      className: 'string',\n      contains: STRING_CONTAINS,\n      variants: [\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\(',\n          end: '\\\\)',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\[',\n          end: '\\\\]',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\{',\n          end: '\\\\}',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\|',\n          end: '\\\\|',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*<',\n          end: '>',\n          relevance: 5\n        },\n        {\n          begin: 'qw\\\\s+q',\n          end: 'q',\n          relevance: 5\n        },\n        {\n          begin: '\\'',\n          end: '\\'',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: '\"',\n          end: '\"'\n        },\n        {\n          begin: '`',\n          end: '`',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: /\\{\\w+\\}/,\n          relevance: 0\n        },\n        {\n          begin: '-?\\\\w+\\\\s*=>',\n          relevance: 0\n        }\n      ]\n    },\n    {\n      className: 'number',\n      begin: '(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b',\n      relevance: 0\n    },\n    { // regexp container\n      begin: '(\\\\/\\\\/|' + hljs.RE_STARTERS_RE + '|\\\\b(split|return|print|reverse|grep)\\\\b)\\\\s*',\n      keywords: 'split return print reverse grep',\n      relevance: 0,\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        {\n          className: 'regexp',\n          variants: [\n            // allow matching common delimiters\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", either(...REGEX_DELIMS)) },\n            // and then paired delmis\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\(\", \"\\\\)\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\[\", \"\\\\]\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\{\", \"\\\\}\") }\n          ],\n          relevance: 2\n        },\n        {\n          className: 'regexp',\n          variants: [\n            {\n              // could be a comment in many languages so do not count\n              // as relevant\n              begin: /(m|qr)\\/\\//,\n              relevance: 0\n            },\n            // prefix is optional with /regex/\n            { begin: PAIRED_RE(\"(?:m|qr)?\", /\\//, /\\//)},\n            // allow matching common delimiters\n            { begin: PAIRED_RE(\"m|qr\", either(...REGEX_DELIMS), /\\1/)},\n            // allow common paired delmins\n            { begin: PAIRED_RE(\"m|qr\", /\\(/, /\\)/)},\n            { begin: PAIRED_RE(\"m|qr\", /\\[/, /\\]/)},\n            { begin: PAIRED_RE(\"m|qr\", /\\{/, /\\}/)}\n          ]\n        }\n      ]\n    },\n    {\n      className: 'function',\n      beginKeywords: 'sub',\n      end: '(\\\\s*\\\\(.*?\\\\))?[;{]',\n      excludeEnd: true,\n      relevance: 5,\n      contains: [ hljs.TITLE_MODE ]\n    },\n    {\n      begin: '-\\\\w\\\\b',\n      relevance: 0\n    },\n    {\n      begin: \"^__DATA__$\",\n      end: \"^__END__$\",\n      subLanguage: 'mojolicious',\n      contains: [\n        {\n          begin: \"^@@.*\",\n          end: \"$\",\n          className: \"comment\"\n        }\n      ]\n    }\n  ];\n  SUBST.contains = PERL_DEFAULT_CONTAINS;\n  METHOD.contains = PERL_DEFAULT_CONTAINS;\n\n  return {\n    name: 'Perl',\n    aliases: [\n      'pl',\n      'pm'\n    ],\n    keywords: PERL_KEYWORDS,\n    contains: PERL_DEFAULT_CONTAINS\n  };\n}\n\nmodule.exports = perl;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASK,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,QAAQ,GAAG,CACf,KAAK,EACL,QAAQ,EACR,OAAO,EACP,KAAK,EACL,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,SAAS,EACT,UAAU,EACV,KAAK,EACL,OAAO,EACP,UAAU,EACV,SAAS,EACT,SAAS,EACT,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,UAAU,EACV,YAAY,EACZ,WAAW,EACX,aAAa,EACb,UAAU,EACV,YAAY,EACZ,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,EACR,OAAO,EACP,KAAK,EACL,SAAS,EACT,MAAM,EACN,QAAQ,EACR,UAAU,EACV,MAAM,EACN,UAAU,EACV,UAAU,EACV,UAAU,EACV,eAAe,EACf,eAAe,EACf,YAAY,EACZ,UAAU,EACV,cAAc,EACd,cAAc,EACd,WAAW,EACX,aAAa,EACb,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,aAAa,EACb,UAAU,EACV,UAAU,EACV,UAAU,EACV,eAAe,EACf,eAAe,EACf,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,OAAO,EACP,KAAK,EACL,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,OAAO,EACP,WAAW,EACX,KAAK,EACL,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,SAAS,EACT,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,WAAW,EACX,MAAM,EACN,KAAK,EACL,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,MAAM,EACN,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,MAAM,EACN,UAAU,EACV,YAAY,EACZ,WAAW,EACX,SAAS,EACT,aAAa,EACb,aAAa,EACb,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,UAAU,EACV,UAAU,EACV,KAAK,EACL,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,OAAO,EACP,SAAS,EACT,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,UAAU,EACV,MAAM,EACN,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,IAAI,EACJ,UAAU,EACV,IAAI,EACJ,SAAS,EACT,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,OAAO,EACP,OAAO,EACP,KAAK,EACL,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,SAAS,EACT,WAAW,EACX,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,CACN;;EAED;EACA,MAAMC,eAAe,GAAG,sBAAsB,CAAC,CAAC;EAChD,MAAMC,aAAa,GAAG;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,OAAO,EAAEJ,QAAQ,CAACJ,IAAI,CAAC,GAAG;EAC5B,CAAC;EACD,MAAMS,KAAK,GAAG;IACZC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,SAAS;IAChBC,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAEP;EACZ,CAAC;EACD,MAAMQ,MAAM,GAAG;IACbH,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE;IACL;EACF,CAAC;EACD,MAAMG,GAAG,GAAG;IACVC,QAAQ,EAAE,CACR;MACEL,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAEhB,MAAM,CACX,gDAAgD;MAChD;MACA;MACA,uBACF;IACF,CAAC,EACD;MACEgB,KAAK,EAAE,eAAe;MACtBM,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EACD,MAAMC,eAAe,GAAG,CACtBf,IAAI,CAACgB,gBAAgB,EACrBV,KAAK,EACLM,GAAG,CACJ;EACD,MAAMK,YAAY,GAAG,CACnB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,GAAG;EAAE;EACL,GAAG,CAAC;EAAA,CACL;EACD;AACF;AACA;AACA;AACA;EACE,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,GAAG,KAAK,KAAK;IACxD,MAAMC,MAAM,GAAID,KAAK,KAAK,KAAK,GAC3BA,KAAK,GACL7B,MAAM,CAAC6B,KAAK,EAAED,IAAI,CAAC;IACvB,OAAO5B,MAAM,CACXA,MAAM,CAAC,KAAK,EAAE2B,MAAM,EAAE,GAAG,CAAC,EAC1BC,IAAI,EACJ,mBAAmB,EACnBE,MAAM,EACN,mBAAmB,EACnBD,KAAK,EACLnB,eACF,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;EACE,MAAMqB,SAAS,GAAGA,CAACJ,MAAM,EAAEC,IAAI,EAAEC,KAAK,KAAK;IACzC,OAAO7B,MAAM,CACXA,MAAM,CAAC,KAAK,EAAE2B,MAAM,EAAE,GAAG,CAAC,EAC1BC,IAAI,EACJ,mBAAmB,EACnBC,KAAK,EACLnB,eACF,CAAC;EACH,CAAC;EACD,MAAMsB,qBAAqB,GAAG,CAC5BZ,GAAG,EACHZ,IAAI,CAACyB,iBAAiB,EACtBzB,IAAI,CAAC0B,OAAO,CACV,MAAM,EACN,MAAM,EACN;IACEC,cAAc,EAAE;EAClB,CACF,CAAC,EACDhB,MAAM,EACN;IACEJ,SAAS,EAAE,QAAQ;IACnBqB,QAAQ,EAAEb,eAAe;IACzBF,QAAQ,EAAE,CACR;MACEL,KAAK,EAAE,iBAAiB;MACxBC,GAAG,EAAE,KAAK;MACVK,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,iBAAiB;MACxBC,GAAG,EAAE,KAAK;MACVK,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,iBAAiB;MACxBC,GAAG,EAAE,KAAK;MACVK,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,iBAAiB;MACxBC,GAAG,EAAE,KAAK;MACVK,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,eAAe;MACtBC,GAAG,EAAE,GAAG;MACRK,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,GAAG;MACRK,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTmB,QAAQ,EAAE,CAAE5B,IAAI,CAACgB,gBAAgB;IACnC,CAAC,EACD;MACER,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRmB,QAAQ,EAAE,CAAE5B,IAAI,CAACgB,gBAAgB;IACnC,CAAC,EACD;MACER,KAAK,EAAE,SAAS;MAChBM,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,cAAc;MACrBM,SAAS,EAAE;IACb,CAAC;EAEL,CAAC,EACD;IACEP,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,2EAA2E;IAClFM,SAAS,EAAE;EACb,CAAC,EACD;IAAE;IACAN,KAAK,EAAE,UAAU,GAAGR,IAAI,CAAC6B,cAAc,GAAG,+CAA+C;IACzFnB,QAAQ,EAAE,iCAAiC;IAC3CI,SAAS,EAAE,CAAC;IACZc,QAAQ,EAAE,CACR5B,IAAI,CAACyB,iBAAiB,EACtB;MACElB,SAAS,EAAE,QAAQ;MACnBM,QAAQ,EAAE;MACR;MACA;QAAEL,KAAK,EAAEU,gBAAgB,CAAC,QAAQ,EAAEpB,MAAM,CAAC,GAAGmB,YAAY,CAAC;MAAE,CAAC;MAC9D;MACA;QAAET,KAAK,EAAEU,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK;MAAE,CAAC,EACnD;QAAEV,KAAK,EAAEU,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK;MAAE,CAAC,EACnD;QAAEV,KAAK,EAAEU,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK;MAAE,CAAC,CACpD;MACDJ,SAAS,EAAE;IACb,CAAC,EACD;MACEP,SAAS,EAAE,QAAQ;MACnBM,QAAQ,EAAE,CACR;QACE;QACA;QACAL,KAAK,EAAE,YAAY;QACnBM,SAAS,EAAE;MACb,CAAC;MACD;MACA;QAAEN,KAAK,EAAEe,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI;MAAC,CAAC;MAC5C;MACA;QAAEf,KAAK,EAAEe,SAAS,CAAC,MAAM,EAAEzB,MAAM,CAAC,GAAGmB,YAAY,CAAC,EAAE,IAAI;MAAC,CAAC;MAC1D;MACA;QAAET,KAAK,EAAEe,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI;MAAC,CAAC,EACvC;QAAEf,KAAK,EAAEe,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI;MAAC,CAAC,EACvC;QAAEf,KAAK,EAAEe,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI;MAAC,CAAC;IAE3C,CAAC;EAEL,CAAC,EACD;IACEhB,SAAS,EAAE,UAAU;IACrBuB,aAAa,EAAE,KAAK;IACpBrB,GAAG,EAAE,sBAAsB;IAC3BsB,UAAU,EAAE,IAAI;IAChBjB,SAAS,EAAE,CAAC;IACZc,QAAQ,EAAE,CAAE5B,IAAI,CAACgC,UAAU;EAC7B,CAAC,EACD;IACExB,KAAK,EAAE,SAAS;IAChBM,SAAS,EAAE;EACb,CAAC,EACD;IACEN,KAAK,EAAE,YAAY;IACnBC,GAAG,EAAE,WAAW;IAChBwB,WAAW,EAAE,aAAa;IAC1BL,QAAQ,EAAE,CACR;MACEpB,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,GAAG;MACRF,SAAS,EAAE;IACb,CAAC;EAEL,CAAC,CACF;EACDD,KAAK,CAACsB,QAAQ,GAAGJ,qBAAqB;EACtCb,MAAM,CAACiB,QAAQ,GAAGJ,qBAAqB;EAEvC,OAAO;IACLU,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CACP,IAAI,EACJ,IAAI,CACL;IACDzB,QAAQ,EAAEP,aAAa;IACvByB,QAAQ,EAAEJ;EACZ,CAAC;AACH;AAEAY,MAAM,CAACC,OAAO,GAAGtC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}