{"ast": null, "code": "'use strict';\n\nmodule.exports = iecst;\niecst.displayName = 'iecst';\niecst.aliases = [];\nfunction iecst(Prism) {\n  Prism.languages.iecst = {\n    comment: [{\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\(\\*[\\s\\S]*?(?:\\*\\)|$)|\\{[\\s\\S]*?(?:\\}|$))/,\n      lookbehind: true,\n      greedy: true\n    }, {\n      pattern: /(^|[^\\\\:])\\/\\/.*/,\n      lookbehind: true,\n      greedy: true\n    }],\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword: [/\\b(?:END_)?(?:PROGRAM|CONFIGURATION|INTERFACE|FUNCTION_BLOCK|FUNCTION|ACTION|TRANSITION|TYPE|STRUCT|(?:INITIAL_)?STEP|NAMESPACE|LIBRARY|CHANNEL|FOLDER|RESOURCE|VAR_(?:ACCESS|CONFIG|EXTERNAL|GLOBAL|INPUT|IN_OUT|OUTPUT|TEMP)|VAR|METHOD|PROPERTY)\\b/i, /\\b(?:AT|BY|(?:END_)?(?:CASE|FOR|IF|REPEAT|WHILE)|CONSTANT|CONTINUE|DO|ELSE|ELSIF|EXIT|EXTENDS|FROM|GET|GOTO|IMPLEMENTS|JMP|NON_RETAIN|OF|PRIVATE|PROTECTED|PUBLIC|RETAIN|RETURN|SET|TASK|THEN|TO|UNTIL|USING|WITH|__CATCH|__ENDTRY|__FINALLY|__TRY)\\b/],\n    'class-name': /\\b(?:ANY|ARRAY|BOOL|BYTE|U?(?:D|L|S)?INT|(?:D|L)?WORD|DATE(?:_AND_TIME)?|DT|L?REAL|POINTER|STRING|TIME(?:_OF_DAY)?|TOD)\\b/,\n    address: {\n      pattern: /%[IQM][XBWDL][\\d.]*|%[IQ][\\d.]*/,\n      alias: 'symbol'\n    },\n    number: /\\b(?:16#[\\da-f]+|2#[01_]+|0x[\\da-f]+)\\b|\\b(?:D|DT|T|TOD)#[\\d_shmd:]*|\\b[A-Z]*#[\\d.,_]*|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    boolean: /\\b(?:FALSE|NULL|TRUE)\\b/,\n    operator: /S?R?:?=>?|&&?|\\*\\*?|<[=>]?|>=?|[-:^/+#]|\\b(?:AND|EQ|EXPT|GE|GT|LE|LT|MOD|NE|NOT|OR|XOR)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    punctuation: /[()[\\].,;]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "iecst", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "string", "keyword", "address", "alias", "number", "boolean", "operator", "function", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/iecst.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = iecst\niecst.displayName = 'iecst'\niecst.aliases = []\nfunction iecst(Prism) {\n  Prism.languages.iecst = {\n    comment: [\n      {\n        pattern:\n          /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\(\\*[\\s\\S]*?(?:\\*\\)|$)|\\{[\\s\\S]*?(?:\\}|$))/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword: [\n      /\\b(?:END_)?(?:PROGRAM|CONFIGURATION|INTERFACE|FUNCTION_BLOCK|FUNCTION|ACTION|TRANSITION|TYPE|STRUCT|(?:INITIAL_)?STEP|NAMESPACE|LIBRARY|CHANNEL|FOLDER|RESOURCE|VAR_(?:ACCESS|CONFIG|EXTERNAL|GLOBAL|INPUT|IN_OUT|OUTPUT|TEMP)|VAR|METHOD|PROPERTY)\\b/i,\n      /\\b(?:AT|BY|(?:END_)?(?:CASE|FOR|IF|REPEAT|WHILE)|CONSTANT|CONTINUE|DO|ELSE|ELSIF|EXIT|EXTENDS|FROM|GET|GOTO|IMPLEMENTS|JMP|NON_RETAIN|OF|PRIVATE|PROTECTED|PUBLIC|RETAIN|RETURN|SET|TASK|THEN|TO|UNTIL|USING|WITH|__CATCH|__ENDTRY|__FINALLY|__TRY)\\b/\n    ],\n    'class-name':\n      /\\b(?:ANY|ARRAY|BOOL|BYTE|U?(?:D|L|S)?INT|(?:D|L)?WORD|DATE(?:_AND_TIME)?|DT|L?REAL|POINTER|STRING|TIME(?:_OF_DAY)?|TOD)\\b/,\n    address: {\n      pattern: /%[IQM][XBWDL][\\d.]*|%[IQ][\\d.]*/,\n      alias: 'symbol'\n    },\n    number:\n      /\\b(?:16#[\\da-f]+|2#[01_]+|0x[\\da-f]+)\\b|\\b(?:D|DT|T|TOD)#[\\d_shmd:]*|\\b[A-Z]*#[\\d.,_]*|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    boolean: /\\b(?:FALSE|NULL|TRUE)\\b/,\n    operator:\n      /S?R?:?=>?|&&?|\\*\\*?|<[=>]?|>=?|[-:^/+#]|\\b(?:AND|EQ|EXPT|GE|GT|LE|LT|MOD|NE|NOT|OR|XOR)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    punctuation: /[()[\\].,;]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE,CACP;MACEC,OAAO,EACL,+EAA+E;MACjFC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,EACD;MACEF,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,gDAAgD;MACzDE,MAAM,EAAE;IACV,CAAC;IACDE,OAAO,EAAE,CACP,wPAAwP,EACxP,uPAAuP,CACxP;IACD,YAAY,EACV,2HAA2H;IAC7HC,OAAO,EAAE;MACPL,OAAO,EAAE,iCAAiC;MAC1CM,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EACJ,mIAAmI;IACrIC,OAAO,EAAE,yBAAyB;IAClCC,QAAQ,EACN,2FAA2F;IAC7FC,QAAQ,EAAE,uBAAuB;IACjCC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}