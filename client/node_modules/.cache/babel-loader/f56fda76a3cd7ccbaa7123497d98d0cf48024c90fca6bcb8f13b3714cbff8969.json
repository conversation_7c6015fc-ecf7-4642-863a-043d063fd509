{"ast": null, "code": "/*\nLanguage: Rust\nAuthor: <PERSON><PERSON> <andrey.v<PERSON><PERSON>@gmail.com>\nContributors: Roman Shmatov <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.rust-lang.org\nCategory: common, system\n*/\n\nfunction rust(hljs) {\n  const NUM_SUFFIX = '([ui](8|16|32|64|128|size)|f(32|64))\\?';\n  const KEYWORDS = 'abstract as async await become box break const continue crate do dyn ' + 'else enum extern false final fn for if impl in let loop macro match mod ' + 'move mut override priv pub ref return self Self static struct super ' + 'trait true try type typeof unsafe unsized use virtual where while yield';\n  const BUILTINS =\n  // functions\n  'drop ' +\n  // types\n  'i8 i16 i32 i64 i128 isize ' + 'u8 u16 u32 u64 u128 usize ' + 'f32 f64 ' + 'str char bool ' + 'Box Option Result String Vec ' +\n  // traits\n  'Copy Send Sized Sync Drop Fn FnMut FnOnce ToOwned Clone Debug ' + 'PartialEq PartialOrd Eq Ord AsRef AsMut Into From Default Iterator ' + 'Extend IntoIterator DoubleEndedIterator ExactSizeIterator ' + 'SliceConcatExt ToString ' +\n  // macros\n  'assert! assert_eq! bitflags! bytes! cfg! col! concat! concat_idents! ' + 'debug_assert! debug_assert_eq! env! panic! file! format! format_args! ' + 'include_bin! include_str! line! local_data_key! module_path! ' + 'option_env! print! println! select! stringify! try! unimplemented! ' + 'unreachable! vec! write! writeln! macro_rules! assert_ne! debug_assert_ne!';\n  return {\n    name: 'Rust',\n    aliases: ['rs'],\n    keywords: {\n      $pattern: hljs.IDENT_RE + '!?',\n      keyword: KEYWORDS,\n      literal: 'true false Some None Ok Err',\n      built_in: BUILTINS\n    },\n    illegal: '</',\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.COMMENT('/\\\\*', '\\\\*/', {\n      contains: ['self']\n    }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      begin: /b?\"/,\n      illegal: null\n    }), {\n      className: 'string',\n      variants: [{\n        begin: /r(#*)\"(.|\\n)*?\"\\1(?!#)/\n      }, {\n        begin: /b?'\\\\?(x\\w{2}|u\\w{4}|U\\w{8}|.)'/\n      }]\n    }, {\n      className: 'symbol',\n      begin: /'[a-zA-Z_][a-zA-Z0-9_]*/\n    }, {\n      className: 'number',\n      variants: [{\n        begin: '\\\\b0b([01_]+)' + NUM_SUFFIX\n      }, {\n        begin: '\\\\b0o([0-7_]+)' + NUM_SUFFIX\n      }, {\n        begin: '\\\\b0x([A-Fa-f0-9_]+)' + NUM_SUFFIX\n      }, {\n        begin: '\\\\b(\\\\d[\\\\d_]*(\\\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)' + NUM_SUFFIX\n      }],\n      relevance: 0\n    }, {\n      className: 'function',\n      beginKeywords: 'fn',\n      end: '(\\\\(|<)',\n      excludeEnd: true,\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      className: 'meta',\n      begin: '#!?\\\\[',\n      end: '\\\\]',\n      contains: [{\n        className: 'meta-string',\n        begin: /\"/,\n        end: /\"/\n      }]\n    }, {\n      className: 'class',\n      beginKeywords: 'type',\n      end: ';',\n      contains: [hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, {\n        endsParent: true\n      })],\n      illegal: '\\\\S'\n    }, {\n      className: 'class',\n      beginKeywords: 'trait enum struct union',\n      end: /\\{/,\n      contains: [hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, {\n        endsParent: true\n      })],\n      illegal: '[\\\\w\\\\d]'\n    }, {\n      begin: hljs.IDENT_RE + '::',\n      keywords: {\n        built_in: BUILTINS\n      }\n    }, {\n      begin: '->'\n    }]\n  };\n}\nmodule.exports = rust;", "map": {"version": 3, "names": ["rust", "hljs", "NUM_SUFFIX", "KEYWORDS", "BUILTINS", "name", "aliases", "keywords", "$pattern", "IDENT_RE", "keyword", "literal", "built_in", "illegal", "contains", "C_LINE_COMMENT_MODE", "COMMENT", "inherit", "QUOTE_STRING_MODE", "begin", "className", "variants", "relevance", "beginKeywords", "end", "excludeEnd", "UNDERSCORE_TITLE_MODE", "endsParent", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/rust.js"], "sourcesContent": ["/*\nLanguage: Rust\nAuthor: <PERSON><PERSON> <andrey.v<PERSON><PERSON>@gmail.com>\nContributors: Roman Shmatov <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.rust-lang.org\nCategory: common, system\n*/\n\nfunction rust(hljs) {\n  const NUM_SUFFIX = '([ui](8|16|32|64|128|size)|f(32|64))\\?';\n  const KEYWORDS =\n    'abstract as async await become box break const continue crate do dyn ' +\n    'else enum extern false final fn for if impl in let loop macro match mod ' +\n    'move mut override priv pub ref return self Self static struct super ' +\n    'trait true try type typeof unsafe unsized use virtual where while yield';\n  const BUILTINS =\n    // functions\n    'drop ' +\n    // types\n    'i8 i16 i32 i64 i128 isize ' +\n    'u8 u16 u32 u64 u128 usize ' +\n    'f32 f64 ' +\n    'str char bool ' +\n    'Box Option Result String Vec ' +\n    // traits\n    'Copy Send Sized Sync Drop Fn FnMut FnOnce ToOwned Clone Debug ' +\n    'PartialEq PartialOrd Eq Ord AsRef AsMut Into From Default Iterator ' +\n    'Extend IntoIterator DoubleEndedIterator ExactSizeIterator ' +\n    'SliceConcatExt ToString ' +\n    // macros\n    'assert! assert_eq! bitflags! bytes! cfg! col! concat! concat_idents! ' +\n    'debug_assert! debug_assert_eq! env! panic! file! format! format_args! ' +\n    'include_bin! include_str! line! local_data_key! module_path! ' +\n    'option_env! print! println! select! stringify! try! unimplemented! ' +\n    'unreachable! vec! write! writeln! macro_rules! assert_ne! debug_assert_ne!';\n  return {\n    name: 'Rust',\n    aliases: [ 'rs' ],\n    keywords: {\n      $pattern: hljs.IDENT_RE + '!?',\n      keyword:\n        KEYWORDS,\n      literal:\n        'true false Some None Ok Err',\n      built_in:\n        BUILTINS\n    },\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*', '\\\\*/', {\n        contains: [ 'self' ]\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        begin: /b?\"/,\n        illegal: null\n      }),\n      {\n        className: 'string',\n        variants: [\n          {\n            begin: /r(#*)\"(.|\\n)*?\"\\1(?!#)/\n          },\n          {\n            begin: /b?'\\\\?(x\\w{2}|u\\w{4}|U\\w{8}|.)'/\n          }\n        ]\n      },\n      {\n        className: 'symbol',\n        begin: /'[a-zA-Z_][a-zA-Z0-9_]*/\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: '\\\\b0b([01_]+)' + NUM_SUFFIX\n          },\n          {\n            begin: '\\\\b0o([0-7_]+)' + NUM_SUFFIX\n          },\n          {\n            begin: '\\\\b0x([A-Fa-f0-9_]+)' + NUM_SUFFIX\n          },\n          {\n            begin: '\\\\b(\\\\d[\\\\d_]*(\\\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)' +\n                   NUM_SUFFIX\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'function',\n        beginKeywords: 'fn',\n        end: '(\\\\(|<)',\n        excludeEnd: true,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        className: 'meta',\n        begin: '#!?\\\\[',\n        end: '\\\\]',\n        contains: [\n          {\n            className: 'meta-string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'type',\n        end: ';',\n        contains: [\n          hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, {\n            endsParent: true\n          })\n        ],\n        illegal: '\\\\S'\n      },\n      {\n        className: 'class',\n        beginKeywords: 'trait enum struct union',\n        end: /\\{/,\n        contains: [\n          hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, {\n            endsParent: true\n          })\n        ],\n        illegal: '[\\\\w\\\\d]'\n      },\n      {\n        begin: hljs.IDENT_RE + '::',\n        keywords: {\n          built_in: BUILTINS\n        }\n      },\n      {\n        begin: '->'\n      }\n    ]\n  };\n}\n\nmodule.exports = rust;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,UAAU,GAAG,wCAAwC;EAC3D,MAAMC,QAAQ,GACZ,uEAAuE,GACvE,0EAA0E,GAC1E,sEAAsE,GACtE,yEAAyE;EAC3E,MAAMC,QAAQ;EACZ;EACA,OAAO;EACP;EACA,4BAA4B,GAC5B,4BAA4B,GAC5B,UAAU,GACV,gBAAgB,GAChB,+BAA+B;EAC/B;EACA,gEAAgE,GAChE,qEAAqE,GACrE,4DAA4D,GAC5D,0BAA0B;EAC1B;EACA,uEAAuE,GACvE,wEAAwE,GACxE,+DAA+D,GAC/D,qEAAqE,GACrE,4EAA4E;EAC9E,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAE,IAAI,CAAE;IACjBC,QAAQ,EAAE;MACRC,QAAQ,EAAEP,IAAI,CAACQ,QAAQ,GAAG,IAAI;MAC9BC,OAAO,EACLP,QAAQ;MACVQ,OAAO,EACL,6BAA6B;MAC/BC,QAAQ,EACNR;IACJ,CAAC;IACDS,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRb,IAAI,CAACc,mBAAmB,EACxBd,IAAI,CAACe,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE;MAC3BF,QAAQ,EAAE,CAAE,MAAM;IACpB,CAAC,CAAC,EACFb,IAAI,CAACgB,OAAO,CAAChB,IAAI,CAACiB,iBAAiB,EAAE;MACnCC,KAAK,EAAE,KAAK;MACZN,OAAO,EAAE;IACX,CAAC,CAAC,EACF;MACEO,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,CACR;QACEF,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACD;MACEC,SAAS,EAAE,QAAQ;MACnBD,KAAK,EAAE;IACT,CAAC,EACD;MACEC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,CACR;QACEF,KAAK,EAAE,eAAe,GAAGjB;MAC3B,CAAC,EACD;QACEiB,KAAK,EAAE,gBAAgB,GAAGjB;MAC5B,CAAC,EACD;QACEiB,KAAK,EAAE,sBAAsB,GAAGjB;MAClC,CAAC,EACD;QACEiB,KAAK,EAAE,iDAAiD,GACjDjB;MACT,CAAC,CACF;MACDoB,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,UAAU;MACrBG,aAAa,EAAE,IAAI;MACnBC,GAAG,EAAE,SAAS;MACdC,UAAU,EAAE,IAAI;MAChBX,QAAQ,EAAE,CAAEb,IAAI,CAACyB,qBAAqB;IACxC,CAAC,EACD;MACEN,SAAS,EAAE,MAAM;MACjBD,KAAK,EAAE,QAAQ;MACfK,GAAG,EAAE,KAAK;MACVV,QAAQ,EAAE,CACR;QACEM,SAAS,EAAE,aAAa;QACxBD,KAAK,EAAE,GAAG;QACVK,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACD;MACEJ,SAAS,EAAE,OAAO;MAClBG,aAAa,EAAE,MAAM;MACrBC,GAAG,EAAE,GAAG;MACRV,QAAQ,EAAE,CACRb,IAAI,CAACgB,OAAO,CAAChB,IAAI,CAACyB,qBAAqB,EAAE;QACvCC,UAAU,EAAE;MACd,CAAC,CAAC,CACH;MACDd,OAAO,EAAE;IACX,CAAC,EACD;MACEO,SAAS,EAAE,OAAO;MAClBG,aAAa,EAAE,yBAAyB;MACxCC,GAAG,EAAE,IAAI;MACTV,QAAQ,EAAE,CACRb,IAAI,CAACgB,OAAO,CAAChB,IAAI,CAACyB,qBAAqB,EAAE;QACvCC,UAAU,EAAE;MACd,CAAC,CAAC,CACH;MACDd,OAAO,EAAE;IACX,CAAC,EACD;MACEM,KAAK,EAAElB,IAAI,CAACQ,QAAQ,GAAG,IAAI;MAC3BF,QAAQ,EAAE;QACRK,QAAQ,EAAER;MACZ;IACF,CAAC,EACD;MACEe,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAG7B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}