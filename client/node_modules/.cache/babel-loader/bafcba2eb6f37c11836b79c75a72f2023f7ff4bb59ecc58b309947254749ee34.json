{"ast": null, "code": "/*\nLanguage: Awk\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/gawk/manual/gawk.html\nDescription: language definition for Awk scripts\n*/\n\n/** @type LanguageFn */\nfunction awk(hljs) {\n  const VARIABLE = {\n    className: 'variable',\n    variants: [{\n      begin: /\\$[\\w\\d#@][\\w\\d_]*/\n    }, {\n      begin: /\\$\\{(.*?)\\}/\n    }]\n  };\n  const KEYWORDS = 'BEGIN END if else while do for in break continue delete next nextfile function func exit|10';\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [{\n      begin: /(u|b)?r?'''/,\n      end: /'''/,\n      relevance: 10\n    }, {\n      begin: /(u|b)?r?\"\"\"/,\n      end: /\"\"\"/,\n      relevance: 10\n    }, {\n      begin: /(u|r|ur)'/,\n      end: /'/,\n      relevance: 10\n    }, {\n      begin: /(u|r|ur)\"/,\n      end: /\"/,\n      relevance: 10\n    }, {\n      begin: /(b|br)'/,\n      end: /'/\n    }, {\n      begin: /(b|br)\"/,\n      end: /\"/\n    }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n  };\n  return {\n    name: 'Awk',\n    keywords: {\n      keyword: KEYWORDS\n    },\n    contains: [VARIABLE, STRING, hljs.REGEXP_MODE, hljs.HASH_COMMENT_MODE, hljs.NUMBER_MODE]\n  };\n}\nmodule.exports = awk;", "map": {"version": 3, "names": ["awk", "hljs", "VARIABLE", "className", "variants", "begin", "KEYWORDS", "STRING", "contains", "BACKSLASH_ESCAPE", "end", "relevance", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "name", "keywords", "keyword", "REGEXP_MODE", "HASH_COMMENT_MODE", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/awk.js"], "sourcesContent": ["/*\nLanguage: Awk\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/gawk/manual/gawk.html\nDescription: language definition for Awk scripts\n*/\n\n/** @type LanguageFn */\nfunction awk(hljs) {\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$[\\w\\d#@][\\w\\d_]*/\n      },\n      {\n        begin: /\\$\\{(.*?)\\}/\n      }\n    ]\n  };\n  const KEYWORDS = 'BEGIN END if else while do for in break continue delete next nextfile function func exit|10';\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [\n      {\n        begin: /(u|b)?r?'''/,\n        end: /'''/,\n        relevance: 10\n      },\n      {\n        begin: /(u|b)?r?\"\"\"/,\n        end: /\"\"\"/,\n        relevance: 10\n      },\n      {\n        begin: /(u|r|ur)'/,\n        end: /'/,\n        relevance: 10\n      },\n      {\n        begin: /(u|r|ur)\"/,\n        end: /\"/,\n        relevance: 10\n      },\n      {\n        begin: /(b|br)'/,\n        end: /'/\n      },\n      {\n        begin: /(b|br)\"/,\n        end: /\"/\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n  return {\n    name: 'Awk',\n    keywords: {\n      keyword: KEYWORDS\n    },\n    contains: [\n      VARIABLE,\n      STRING,\n      hljs.REGEXP_MODE,\n      hljs.HASH_COMMENT_MODE,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = awk;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMC,QAAQ,GAAG,6FAA6F;EAC9G,MAAMC,MAAM,GAAG;IACbJ,SAAS,EAAE,QAAQ;IACnBK,QAAQ,EAAE,CAACP,IAAI,CAACQ,gBAAgB,CAAC;IACjCL,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,aAAa;MACpBK,GAAG,EAAE,KAAK;MACVC,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,aAAa;MACpBK,GAAG,EAAE,KAAK;MACVC,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,WAAW;MAClBK,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,WAAW;MAClBK,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,SAAS;MAChBK,GAAG,EAAE;IACP,CAAC,EACD;MACEL,KAAK,EAAE,SAAS;MAChBK,GAAG,EAAE;IACP,CAAC,EACDT,IAAI,CAACW,gBAAgB,EACrBX,IAAI,CAACY,iBAAiB;EAE1B,CAAC;EACD,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MACRC,OAAO,EAAEV;IACX,CAAC;IACDE,QAAQ,EAAE,CACRN,QAAQ,EACRK,MAAM,EACNN,IAAI,CAACgB,WAAW,EAChBhB,IAAI,CAACiB,iBAAiB,EACtBjB,IAAI,CAACkB,WAAW;EAEpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGrB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}