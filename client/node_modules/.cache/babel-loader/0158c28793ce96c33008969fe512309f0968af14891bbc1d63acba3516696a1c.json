{"ast": null, "code": "'use strict';\n\nmodule.exports = kumir;\nkumir.displayName = 'kumir';\nkumir.aliases = ['kum'];\nfunction kumir(Prism) {\n  /* eslint-disable regexp/no-dupe-characters-character-class */\n  ;\n  (function (Prism) {\n    /**\n     * Regular expression for characters that are not allowed in identifiers.\n     *\n     * @type {string}\n     */\n    var nonId = /\\s\\x00-\\x1f\\x22-\\x2f\\x3a-\\x3f\\x5b-\\x5e\\x60\\x7b-\\x7e/.source;\n    /**\n     * Surround a regular expression for IDs with patterns for non-ID sequences.\n     *\n     * @param {string} pattern A regular expression for identifiers.\n     * @param {string} [flags] The regular expression flags.\n     * @returns {RegExp} A wrapped regular expression for identifiers.\n     */\n    function wrapId(pattern, flags) {\n      return RegExp(pattern.replace(/<nonId>/g, nonId), flags);\n    }\n    Prism.languages.kumir = {\n      comment: {\n        pattern: /\\|.*/\n      },\n      prolog: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\\n\\r\"]*\"|'[^\\n\\r']*'/,\n        greedy: true\n      },\n      boolean: {\n        pattern: wrapId(/(^|[<nonId>])(?:да|нет)(?=[<nonId>]|$)/.source),\n        lookbehind: true\n      },\n      'operator-word': {\n        pattern: wrapId(/(^|[<nonId>])(?:и|или|не)(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'system-variable': {\n        pattern: wrapId(/(^|[<nonId>])знач(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      type: [{\n        pattern: wrapId(/(^|[<nonId>])(?:вещ|лит|лог|сим|цел)(?:\\x20*таб)?(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'builtin'\n      }, {\n        pattern: wrapId(/(^|[<nonId>])(?:компл|сканкод|файл|цвет)(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'important'\n      }],\n      /**\n       * Should be performed after searching for type names because of \"таб\".\n       * \"таб\" is a reserved word, but never used without a preceding type name.\n       * \"НАЗНАЧИТЬ\", \"Фввод\", and \"Фвывод\" are not reserved words.\n       */\n      keyword: {\n        pattern: wrapId(/(^|[<nonId>])(?:алг|арг(?:\\x20*рез)?|ввод|ВКЛЮЧИТЬ|вс[её]|выбор|вывод|выход|дано|для|до|дс|если|иначе|исп|использовать|кон(?:(?:\\x20+|_)исп)?|кц(?:(?:\\x20+|_)при)?|надо|нач|нс|нц|от|пауза|пока|при|раза?|рез|стоп|таб|то|утв|шаг)(?=[<nonId>]|$)/.source),\n        lookbehind: true\n      },\n      /** Should be performed after searching for reserved words. */\n      name: {\n        // eslint-disable-next-line regexp/no-super-linear-backtracking\n        pattern: wrapId(/(^|[<nonId>])[^\\d<nonId>][^<nonId>]*(?:\\x20+[^<nonId>]+)*(?=[<nonId>]|$)/.source),\n        lookbehind: true\n      },\n      /** Should be performed after searching for names. */\n      number: {\n        pattern: wrapId(/(^|[<nonId>])(?:\\B\\$[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?=[<nonId>]|$)/.source, 'i'),\n        lookbehind: true\n      },\n      /** Should be performed after searching for words. */\n      punctuation: /:=|[(),:;\\[\\]]/,\n      /**\n       * Should be performed after searching for\n       * - numeric constants (because of \"+\" and \"-\");\n       * - punctuation marks (because of \":=\" and \"=\").\n       */\n      'operator-char': {\n        pattern: /\\*\\*?|<[=>]?|>=?|[-+/=]/,\n        alias: 'operator'\n      }\n    };\n    Prism.languages.kum = Prism.languages.kumir;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "kumir", "displayName", "aliases", "Prism", "nonId", "source", "wrapId", "pattern", "flags", "RegExp", "replace", "languages", "comment", "prolog", "greedy", "string", "boolean", "lookbehind", "alias", "type", "keyword", "name", "number", "punctuation", "kum"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/kumir.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = kumir\nkumir.displayName = 'kumir'\nkumir.aliases = ['kum']\nfunction kumir(Prism) {\n  /* eslint-disable regexp/no-dupe-characters-character-class */\n  ;(function (Prism) {\n    /**\n     * Regular expression for characters that are not allowed in identifiers.\n     *\n     * @type {string}\n     */\n    var nonId = /\\s\\x00-\\x1f\\x22-\\x2f\\x3a-\\x3f\\x5b-\\x5e\\x60\\x7b-\\x7e/.source\n    /**\n     * Surround a regular expression for IDs with patterns for non-ID sequences.\n     *\n     * @param {string} pattern A regular expression for identifiers.\n     * @param {string} [flags] The regular expression flags.\n     * @returns {RegExp} A wrapped regular expression for identifiers.\n     */\n    function wrapId(pattern, flags) {\n      return RegExp(pattern.replace(/<nonId>/g, nonId), flags)\n    }\n    Prism.languages.kumir = {\n      comment: {\n        pattern: /\\|.*/\n      },\n      prolog: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\\n\\r\"]*\"|'[^\\n\\r']*'/,\n        greedy: true\n      },\n      boolean: {\n        pattern: wrapId(/(^|[<nonId>])(?:да|нет)(?=[<nonId>]|$)/.source),\n        lookbehind: true\n      },\n      'operator-word': {\n        pattern: wrapId(/(^|[<nonId>])(?:и|или|не)(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'system-variable': {\n        pattern: wrapId(/(^|[<nonId>])знач(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      type: [\n        {\n          pattern: wrapId(\n            /(^|[<nonId>])(?:вещ|лит|лог|сим|цел)(?:\\x20*таб)?(?=[<nonId>]|$)/\n              .source\n          ),\n          lookbehind: true,\n          alias: 'builtin'\n        },\n        {\n          pattern: wrapId(\n            /(^|[<nonId>])(?:компл|сканкод|файл|цвет)(?=[<nonId>]|$)/.source\n          ),\n          lookbehind: true,\n          alias: 'important'\n        }\n      ],\n      /**\n       * Should be performed after searching for type names because of \"таб\".\n       * \"таб\" is a reserved word, but never used without a preceding type name.\n       * \"НАЗНАЧИТЬ\", \"Фввод\", and \"Фвывод\" are not reserved words.\n       */\n      keyword: {\n        pattern: wrapId(\n          /(^|[<nonId>])(?:алг|арг(?:\\x20*рез)?|ввод|ВКЛЮЧИТЬ|вс[её]|выбор|вывод|выход|дано|для|до|дс|если|иначе|исп|использовать|кон(?:(?:\\x20+|_)исп)?|кц(?:(?:\\x20+|_)при)?|надо|нач|нс|нц|от|пауза|пока|при|раза?|рез|стоп|таб|то|утв|шаг)(?=[<nonId>]|$)/\n            .source\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for reserved words. */\n      name: {\n        // eslint-disable-next-line regexp/no-super-linear-backtracking\n        pattern: wrapId(\n          /(^|[<nonId>])[^\\d<nonId>][^<nonId>]*(?:\\x20+[^<nonId>]+)*(?=[<nonId>]|$)/\n            .source\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for names. */\n      number: {\n        pattern: wrapId(\n          /(^|[<nonId>])(?:\\B\\$[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?=[<nonId>]|$)/\n            .source,\n          'i'\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for words. */\n      punctuation: /:=|[(),:;\\[\\]]/,\n      /**\n       * Should be performed after searching for\n       * - numeric constants (because of \"+\" and \"-\");\n       * - punctuation marks (because of \":=\" and \"=\").\n       */\n      'operator-char': {\n        pattern: /\\*\\*?|<[=>]?|>=?|[-+/=]/,\n        alias: 'operator'\n      }\n    }\n    Prism.languages.kum = Prism.languages.kumir\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AACvB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EACA;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACI,IAAIC,KAAK,GAAG,qDAAqD,CAACC,MAAM;IACxE;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,MAAMA,CAACC,OAAO,EAAEC,KAAK,EAAE;MAC9B,OAAOC,MAAM,CAACF,OAAO,CAACG,OAAO,CAAC,UAAU,EAAEN,KAAK,CAAC,EAAEI,KAAK,CAAC;IAC1D;IACAL,KAAK,CAACQ,SAAS,CAACX,KAAK,GAAG;MACtBY,OAAO,EAAE;QACPL,OAAO,EAAE;MACX,CAAC;MACDM,MAAM,EAAE;QACNN,OAAO,EAAE,KAAK;QACdO,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNR,OAAO,EAAE,yBAAyB;QAClCO,MAAM,EAAE;MACV,CAAC;MACDE,OAAO,EAAE;QACPT,OAAO,EAAED,MAAM,CAAC,wCAAwC,CAACD,MAAM,CAAC;QAChEY,UAAU,EAAE;MACd,CAAC;MACD,eAAe,EAAE;QACfV,OAAO,EAAED,MAAM,CAAC,0CAA0C,CAACD,MAAM,CAAC;QAClEY,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACD,iBAAiB,EAAE;QACjBX,OAAO,EAAED,MAAM,CAAC,kCAAkC,CAACD,MAAM,CAAC;QAC1DY,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,IAAI,EAAE,CACJ;QACEZ,OAAO,EAAED,MAAM,CACb,kEAAkE,CAC/DD,MACL,CAAC;QACDY,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEX,OAAO,EAAED,MAAM,CACb,yDAAyD,CAACD,MAC5D,CAAC;QACDY,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC,CACF;MACD;AACN;AACA;AACA;AACA;MACME,OAAO,EAAE;QACPb,OAAO,EAAED,MAAM,CACb,oPAAoP,CACjPD,MACL,CAAC;QACDY,UAAU,EAAE;MACd,CAAC;MACD;MACAI,IAAI,EAAE;QACJ;QACAd,OAAO,EAAED,MAAM,CACb,0EAA0E,CACvED,MACL,CAAC;QACDY,UAAU,EAAE;MACd,CAAC;MACD;MACAK,MAAM,EAAE;QACNf,OAAO,EAAED,MAAM,CACb,0FAA0F,CACvFD,MAAM,EACT,GACF,CAAC;QACDY,UAAU,EAAE;MACd,CAAC;MACD;MACAM,WAAW,EAAE,gBAAgB;MAC7B;AACN;AACA;AACA;AACA;MACM,eAAe,EAAE;QACfhB,OAAO,EAAE,yBAAyB;QAClCW,KAAK,EAAE;MACT;IACF,CAAC;IACDf,KAAK,CAACQ,SAAS,CAACa,GAAG,GAAGrB,KAAK,CAACQ,SAAS,CAACX,KAAK;EAC7C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}