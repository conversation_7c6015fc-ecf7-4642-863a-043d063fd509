{"ast": null, "code": "'use strict';\n\nmodule.exports = bsl;\nbsl.displayName = 'bsl';\nbsl.aliases = [];\nfunction bsl(Prism) {\n  /* eslint-disable no-misleading-character-class */\n  // 1C:Enterprise\n  // https://github.com/Diversus23/\n  //\n  Prism.languages.bsl = {\n    comment: /\\/\\/.*/,\n    string: [\n    // Строки\n    // Strings\n    {\n      pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    // Дата и время\n    // Date & time\n    {\n      pattern: /'(?:[^'\\r\\n\\\\]|\\\\.)*'/\n    }],\n    keyword: [{\n      // RU\n      pattern: /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:пока|для|новый|прервать|попытка|исключение|вызватьисключение|иначе|конецпопытки|неопределено|функция|перем|возврат|конецфункции|если|иначеесли|процедура|конецпроцедуры|тогда|знач|экспорт|конецесли|из|каждого|истина|ложь|по|цикл|конеццикла|выполнить)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n      lookbehind: true\n    }, {\n      // EN\n      pattern: /\\b(?:break|do|each|else|elseif|enddo|endfunction|endif|endprocedure|endtry|except|execute|export|false|for|function|if|in|new|null|procedure|raise|return|then|to|true|try|undefined|val|var|while)\\b/i\n    }],\n    number: {\n      pattern: /(^(?=\\d)|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:E[+-]?\\d+)?/i,\n      lookbehind: true\n    },\n    operator: [/[<>+\\-*/]=?|[%=]/,\n    // RU\n    {\n      pattern: /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:и|или|не)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n      lookbehind: true\n    },\n    // EN\n    {\n      pattern: /\\b(?:and|not|or)\\b/i\n    }],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/,\n    directive: [\n    // Теги препроцессора вида &Клиент, &Сервер, ...\n    // Preprocessor tags of the type &Client, &Server, ...\n    {\n      pattern: /^([ \\t]*)&.*/m,\n      lookbehind: true,\n      greedy: true,\n      alias: 'important'\n    },\n    // Инструкции препроцессора вида:\n    // #Если Сервер Тогда\n    // ...\n    // #КонецЕсли\n    // Preprocessor instructions of the form:\n    // #If Server Then\n    // ...\n    // #EndIf\n    {\n      pattern: /^([ \\t]*)#.*/gm,\n      lookbehind: true,\n      greedy: true,\n      alias: 'important'\n    }]\n  };\n  Prism.languages.oscript = Prism.languages['bsl'];\n}", "map": {"version": 3, "names": ["module", "exports", "bsl", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "keyword", "lookbehind", "number", "operator", "punctuation", "directive", "alias", "oscript"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/bsl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bsl\nbsl.displayName = 'bsl'\nbsl.aliases = []\nfunction bsl(Prism) {\n  /* eslint-disable no-misleading-character-class */\n  // 1C:Enterprise\n  // https://github.com/Diversus23/\n  //\n  Prism.languages.bsl = {\n    comment: /\\/\\/.*/,\n    string: [\n      // Строки\n      // Strings\n      {\n        pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n        greedy: true\n      }, // Дата и время\n      // Date & time\n      {\n        pattern: /'(?:[^'\\r\\n\\\\]|\\\\.)*'/\n      }\n    ],\n    keyword: [\n      {\n        // RU\n        pattern:\n          /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:пока|для|новый|прервать|попытка|исключение|вызватьисключение|иначе|конецпопытки|неопределено|функция|перем|возврат|конецфункции|если|иначеесли|процедура|конецпроцедуры|тогда|знач|экспорт|конецесли|из|каждого|истина|ложь|по|цикл|конеццикла|выполнить)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n        lookbehind: true\n      },\n      {\n        // EN\n        pattern:\n          /\\b(?:break|do|each|else|elseif|enddo|endfunction|endif|endprocedure|endtry|except|execute|export|false|for|function|if|in|new|null|procedure|raise|return|then|to|true|try|undefined|val|var|while)\\b/i\n      }\n    ],\n    number: {\n      pattern:\n        /(^(?=\\d)|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:E[+-]?\\d+)?/i,\n      lookbehind: true\n    },\n    operator: [\n      /[<>+\\-*/]=?|[%=]/, // RU\n      {\n        pattern:\n          /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:и|или|не)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n        lookbehind: true\n      }, // EN\n      {\n        pattern: /\\b(?:and|not|or)\\b/i\n      }\n    ],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/,\n    directive: [\n      // Теги препроцессора вида &Клиент, &Сервер, ...\n      // Preprocessor tags of the type &Client, &Server, ...\n      {\n        pattern: /^([ \\t]*)&.*/m,\n        lookbehind: true,\n        greedy: true,\n        alias: 'important'\n      }, // Инструкции препроцессора вида:\n      // #Если Сервер Тогда\n      // ...\n      // #КонецЕсли\n      // Preprocessor instructions of the form:\n      // #If Server Then\n      // ...\n      // #EndIf\n      {\n        pattern: /^([ \\t]*)#.*/gm,\n        lookbehind: true,\n        greedy: true,\n        alias: 'important'\n      }\n    ]\n  }\n  Prism.languages.oscript = Prism.languages['bsl']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EACA;EACA;EACA;EACAA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE;IACN;IACA;IACA;MACEC,OAAO,EAAE,qBAAqB;MAC9BC,MAAM,EAAE;IACV,CAAC;IAAE;IACH;IACA;MACED,OAAO,EAAE;IACX,CAAC,CACF;IACDE,OAAO,EAAE,CACP;MACE;MACAF,OAAO,EACL,waAAwa;MAC1aG,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAH,OAAO,EACL;IACJ,CAAC,CACF;IACDI,MAAM,EAAE;MACNJ,OAAO,EACL,mIAAmI;MACrIG,UAAU,EAAE;IACd,CAAC;IACDE,QAAQ,EAAE,CACR,kBAAkB;IAAE;IACpB;MACEL,OAAO,EACL,wLAAwL;MAC1LG,UAAU,EAAE;IACd,CAAC;IAAE;IACH;MACEH,OAAO,EAAE;IACX,CAAC,CACF;IACDM,WAAW,EAAE,wBAAwB;IACrCC,SAAS,EAAE;IACT;IACA;IACA;MACEP,OAAO,EAAE,eAAe;MACxBG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZO,KAAK,EAAE;IACT,CAAC;IAAE;IACH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACER,OAAO,EAAE,gBAAgB;MACzBG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZO,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACDZ,KAAK,CAACC,SAAS,CAACY,OAAO,GAAGb,KAAK,CAACC,SAAS,CAAC,KAAK,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}