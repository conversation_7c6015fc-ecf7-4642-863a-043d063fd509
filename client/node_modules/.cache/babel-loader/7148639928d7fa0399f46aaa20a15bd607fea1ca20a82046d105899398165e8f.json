{"ast": null, "code": "/*\nLanguage: Nix\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Nix functional language\nWebsite: http://nixos.org/nix\n*/\n\nfunction nix(hljs) {\n  const NIX_KEYWORDS = {\n    keyword: 'rec with let in inherit assert if else then',\n    literal: 'true false or and null',\n    built_in: 'import abort baseNameOf dirOf isNull builtins map removeAttrs throw ' + 'toString derivation'\n  };\n  const ANTIQUOTE = {\n    className: 'subst',\n    begin: /\\$\\{/,\n    end: /\\}/,\n    keywords: NIX_KEYWORDS\n  };\n  const ATTRS = {\n    begin: /[a-zA-Z0-9-_]+(\\s*=)/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [{\n      className: 'attr',\n      begin: /\\S+/\n    }]\n  };\n  const STRING = {\n    className: 'string',\n    contains: [ANTIQUOTE],\n    variants: [{\n      begin: \"''\",\n      end: \"''\"\n    }, {\n      begin: '\"',\n      end: '\"'\n    }]\n  };\n  const EXPRESSIONS = [hljs.NUMBER_MODE, hljs.HASH_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRING, ATTRS];\n  ANTIQUOTE.contains = EXPRESSIONS;\n  return {\n    name: 'Nix',\n    aliases: [\"nixos\"],\n    keywords: NIX_KEYWORDS,\n    contains: EXPRESSIONS\n  };\n}\nmodule.exports = nix;", "map": {"version": 3, "names": ["nix", "hljs", "NIX_KEYWORDS", "keyword", "literal", "built_in", "ANTIQUOTE", "className", "begin", "end", "keywords", "ATTRS", "returnBegin", "relevance", "contains", "STRING", "variants", "EXPRESSIONS", "NUMBER_MODE", "HASH_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "name", "aliases", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/nix.js"], "sourcesContent": ["/*\nLanguage: Nix\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Nix functional language\nWebsite: http://nixos.org/nix\n*/\n\nfunction nix(hljs) {\n  const NIX_KEYWORDS = {\n    keyword:\n      'rec with let in inherit assert if else then',\n    literal:\n      'true false or and null',\n    built_in:\n      'import abort baseNameOf dirOf isNull builtins map removeAttrs throw ' +\n      'toString derivation'\n  };\n  const ANTIQUOTE = {\n    className: 'subst',\n    begin: /\\$\\{/,\n    end: /\\}/,\n    keywords: NIX_KEYWORDS\n  };\n  const ATTRS = {\n    begin: /[a-zA-Z0-9-_]+(\\s*=)/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: /\\S+/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    contains: [ ANTIQUOTE ],\n    variants: [\n      {\n        begin: \"''\",\n        end: \"''\"\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      }\n    ]\n  };\n  const EXPRESSIONS = [\n    hljs.NUMBER_MODE,\n    hljs.HASH_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING,\n    ATTRS\n  ];\n  ANTIQUOTE.contains = EXPRESSIONS;\n  return {\n    name: 'Nix',\n    aliases: [ \"nixos\" ],\n    keywords: NIX_KEYWORDS,\n    contains: EXPRESSIONS\n  };\n}\n\nmodule.exports = nix;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,YAAY,GAAG;IACnBC,OAAO,EACL,6CAA6C;IAC/CC,OAAO,EACL,wBAAwB;IAC1BC,QAAQ,EACN,sEAAsE,GACtE;EACJ,CAAC;EACD,MAAMC,SAAS,GAAG;IAChBC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAER;EACZ,CAAC;EACD,MAAMS,KAAK,GAAG;IACZH,KAAK,EAAE,sBAAsB;IAC7BI,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACR;MACEP,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMO,MAAM,GAAG;IACbR,SAAS,EAAE,QAAQ;IACnBO,QAAQ,EAAE,CAAER,SAAS,CAAE;IACvBU,QAAQ,EAAE,CACR;MACER,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EACD,MAAMQ,WAAW,GAAG,CAClBhB,IAAI,CAACiB,WAAW,EAChBjB,IAAI,CAACkB,iBAAiB,EACtBlB,IAAI,CAACmB,oBAAoB,EACzBL,MAAM,EACNJ,KAAK,CACN;EACDL,SAAS,CAACQ,QAAQ,GAAGG,WAAW;EAChC,OAAO;IACLI,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,CAAE,OAAO,CAAE;IACpBZ,QAAQ,EAAER,YAAY;IACtBY,QAAQ,EAAEG;EACZ,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGxB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}