{"ast": null, "code": "'use strict';\n\nmodule.exports = wiki;\nwiki.displayName = 'wiki';\nwiki.aliases = [];\nfunction wiki(Prism) {\n  Prism.languages.wiki = Prism.languages.extend('markup', {\n    'block-comment': {\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    heading: {\n      pattern: /^(=+)[^=\\r\\n].*?\\1/m,\n      inside: {\n        punctuation: /^=+|=+$/,\n        important: /.+/\n      }\n    },\n    emphasis: {\n      // TODO Multi-line\n      pattern: /('{2,5}).+?\\1/,\n      inside: {\n        'bold-italic': {\n          pattern: /(''''').+?(?=\\1)/,\n          lookbehind: true,\n          alias: ['bold', 'italic']\n        },\n        bold: {\n          pattern: /(''')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        italic: {\n          pattern: /('')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        punctuation: /^''+|''+$/\n      }\n    },\n    hr: {\n      pattern: /^-{4,}/m,\n      alias: 'punctuation'\n    },\n    url: [/ISBN +(?:97[89][ -]?)?(?:\\d[ -]?){9}[\\dx]\\b|(?:PMID|RFC) +\\d+/i, /\\[\\[.+?\\]\\]|\\[.+?\\]/],\n    variable: [/__[A-Z]+__/,\n    // FIXME Nested structures should be handled\n    // {{formatnum:{{#expr:{{{3}}}}}}}\n    /\\{{3}.+?\\}{3}/, /\\{\\{.+?\\}\\}/],\n    symbol: [/^#redirect/im, /~{3,5}/],\n    // Handle table attrs:\n    // {|\n    // ! style=\"text-align:left;\"| Item\n    // |}\n    'table-tag': {\n      pattern: /((?:^|[|!])[|!])[^|\\r\\n]+\\|(?!\\|)/m,\n      lookbehind: true,\n      inside: {\n        'table-bar': {\n          pattern: /\\|$/,\n          alias: 'punctuation'\n        },\n        rest: Prism.languages.markup['tag'].inside\n      }\n    },\n    punctuation: /^(?:\\{\\||\\|\\}|\\|-|[*#:;!|])|\\|\\||!!/m\n  });\n  Prism.languages.insertBefore('wiki', 'tag', {\n    // Prevent highlighting inside <nowiki>, <source> and <pre> tags\n    nowiki: {\n      pattern: /<(nowiki|pre|source)\\b[^>]*>[\\s\\S]*?<\\/\\1>/i,\n      inside: {\n        tag: {\n          pattern: /<(?:nowiki|pre|source)\\b[^>]*>|<\\/(?:nowiki|pre|source)>/i,\n          inside: Prism.languages.markup['tag'].inside\n        }\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "wiki", "displayName", "aliases", "Prism", "languages", "extend", "pattern", "lookbehind", "alias", "heading", "inside", "punctuation", "important", "emphasis", "bold", "italic", "hr", "url", "variable", "symbol", "rest", "markup", "insertBefore", "nowiki", "tag"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/wiki.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wiki\nwiki.displayName = 'wiki'\nwiki.aliases = []\nfunction wiki(Prism) {\n  Prism.languages.wiki = Prism.languages.extend('markup', {\n    'block-comment': {\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    heading: {\n      pattern: /^(=+)[^=\\r\\n].*?\\1/m,\n      inside: {\n        punctuation: /^=+|=+$/,\n        important: /.+/\n      }\n    },\n    emphasis: {\n      // TODO Multi-line\n      pattern: /('{2,5}).+?\\1/,\n      inside: {\n        'bold-italic': {\n          pattern: /(''''').+?(?=\\1)/,\n          lookbehind: true,\n          alias: ['bold', 'italic']\n        },\n        bold: {\n          pattern: /(''')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        italic: {\n          pattern: /('')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        punctuation: /^''+|''+$/\n      }\n    },\n    hr: {\n      pattern: /^-{4,}/m,\n      alias: 'punctuation'\n    },\n    url: [\n      /ISBN +(?:97[89][ -]?)?(?:\\d[ -]?){9}[\\dx]\\b|(?:PMID|RFC) +\\d+/i,\n      /\\[\\[.+?\\]\\]|\\[.+?\\]/\n    ],\n    variable: [\n      /__[A-Z]+__/, // FIXME Nested structures should be handled\n      // {{formatnum:{{#expr:{{{3}}}}}}}\n      /\\{{3}.+?\\}{3}/,\n      /\\{\\{.+?\\}\\}/\n    ],\n    symbol: [/^#redirect/im, /~{3,5}/],\n    // Handle table attrs:\n    // {|\n    // ! style=\"text-align:left;\"| Item\n    // |}\n    'table-tag': {\n      pattern: /((?:^|[|!])[|!])[^|\\r\\n]+\\|(?!\\|)/m,\n      lookbehind: true,\n      inside: {\n        'table-bar': {\n          pattern: /\\|$/,\n          alias: 'punctuation'\n        },\n        rest: Prism.languages.markup['tag'].inside\n      }\n    },\n    punctuation: /^(?:\\{\\||\\|\\}|\\|-|[*#:;!|])|\\|\\||!!/m\n  })\n  Prism.languages.insertBefore('wiki', 'tag', {\n    // Prevent highlighting inside <nowiki>, <source> and <pre> tags\n    nowiki: {\n      pattern: /<(nowiki|pre|source)\\b[^>]*>[\\s\\S]*?<\\/\\1>/i,\n      inside: {\n        tag: {\n          pattern: /<(?:nowiki|pre|source)\\b[^>]*>|<\\/(?:nowiki|pre|source)>/i,\n          inside: Prism.languages.markup['tag'].inside\n        }\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;IACtD,eAAe,EAAE;MACfC,OAAO,EAAE,2BAA2B;MACpCC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPH,OAAO,EAAE,qBAAqB;MAC9BI,MAAM,EAAE;QACNC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,QAAQ,EAAE;MACR;MACAP,OAAO,EAAE,eAAe;MACxBI,MAAM,EAAE;QACN,aAAa,EAAE;UACbJ,OAAO,EAAE,kBAAkB;UAC3BC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ;QAC1B,CAAC;QACDM,IAAI,EAAE;UACJR,OAAO,EAAE,6BAA6B;UACtCC,UAAU,EAAE;QACd,CAAC;QACDQ,MAAM,EAAE;UACNT,OAAO,EAAE,4BAA4B;UACrCC,UAAU,EAAE;QACd,CAAC;QACDI,WAAW,EAAE;MACf;IACF,CAAC;IACDK,EAAE,EAAE;MACFV,OAAO,EAAE,SAAS;MAClBE,KAAK,EAAE;IACT,CAAC;IACDS,GAAG,EAAE,CACH,gEAAgE,EAChE,qBAAqB,CACtB;IACDC,QAAQ,EAAE,CACR,YAAY;IAAE;IACd;IACA,eAAe,EACf,aAAa,CACd;IACDC,MAAM,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC;IAClC;IACA;IACA;IACA;IACA,WAAW,EAAE;MACXb,OAAO,EAAE,oCAAoC;MAC7CC,UAAU,EAAE,IAAI;MAChBG,MAAM,EAAE;QACN,WAAW,EAAE;UACXJ,OAAO,EAAE,KAAK;UACdE,KAAK,EAAE;QACT,CAAC;QACDY,IAAI,EAAEjB,KAAK,CAACC,SAAS,CAACiB,MAAM,CAAC,KAAK,CAAC,CAACX;MACtC;IACF,CAAC;IACDC,WAAW,EAAE;EACf,CAAC,CAAC;EACFR,KAAK,CAACC,SAAS,CAACkB,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE;IAC1C;IACAC,MAAM,EAAE;MACNjB,OAAO,EAAE,6CAA6C;MACtDI,MAAM,EAAE;QACNc,GAAG,EAAE;UACHlB,OAAO,EAAE,2DAA2D;UACpEI,MAAM,EAAEP,KAAK,CAACC,SAAS,CAACiB,MAAM,CAAC,KAAK,CAAC,CAACX;QACxC;MACF;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}