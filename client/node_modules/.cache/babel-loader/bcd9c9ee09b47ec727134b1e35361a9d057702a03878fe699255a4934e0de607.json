{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = php;\nphp.displayName = 'php';\nphp.aliases = [];\nfunction php(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  /**\n   * Original by <PERSON>: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/\n   * Modified by <PERSON>: http://milesj.me\n   * Rewritten by <PERSON>\n   *\n   * Supports PHP 5.3 - 8.0\n   */;\n  (function (Prism) {\n    var comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/;\n    var constant = [{\n      pattern: /\\b(?:false|true)\\b/i,\n      alias: 'boolean'\n    }, {\n      pattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n      greedy: true,\n      lookbehind: true\n    }, {\n      pattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n      greedy: true,\n      lookbehind: true\n    }, /\\b(?:null)\\b/i, /\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/];\n    var number = /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i;\n    var operator = /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/;\n    var punctuation = /[{}\\[\\](),:;]/;\n    Prism.languages.php = {\n      delimiter: {\n        pattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n        alias: 'important'\n      },\n      comment: comment,\n      variable: /\\$+(?:\\w+\\b|(?=\\{))/,\n      package: {\n        pattern: /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      'class-name-definition': {\n        pattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      'function-definition': {\n        pattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      keyword: [{\n        pattern: /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n        alias: 'type-casting',\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n        alias: 'type-hint',\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string|void)\\b/i,\n        alias: 'return-type',\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n        alias: 'type-declaration',\n        greedy: true\n      }, {\n        pattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n        alias: 'type-declaration',\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n        alias: 'static-context',\n        greedy: true\n      }, {\n        // yield from\n        pattern: /(\\byield\\s+)from\\b/i,\n        lookbehind: true\n      },\n      // `class` is always a keyword unlike other keywords\n      /\\bclass\\b/i, {\n        // https://www.php.net/manual/en/reserved.keywords.php\n        //\n        // keywords cannot be preceded by \"->\"\n        // the complex lookbehind means `(?<!(?:->|::)\\s*)`\n        pattern: /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|new|or|parent|print|private|protected|public|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n        lookbehind: true\n      }],\n      'argument-name': {\n        pattern: /([(,]\\s+)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        lookbehind: true\n      },\n      'class-name': [{\n        pattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n        greedy: true\n      }, {\n        pattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n        alias: 'class-name-fully-qualified',\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      }, {\n        pattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n        alias: 'class-name-fully-qualified',\n        greedy: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      }, {\n        pattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n        alias: 'class-name-fully-qualified',\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      }, {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n        alias: 'type-declaration',\n        greedy: true\n      }, {\n        pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n        alias: ['class-name-fully-qualified', 'type-declaration'],\n        greedy: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      }, {\n        pattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n        alias: 'static-context',\n        greedy: true\n      }, {\n        pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n        alias: ['class-name-fully-qualified', 'static-context'],\n        greedy: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      }, {\n        pattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n        alias: 'type-hint',\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n        alias: ['class-name-fully-qualified', 'type-hint'],\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      }, {\n        pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n        alias: 'return-type',\n        greedy: true,\n        lookbehind: true\n      }, {\n        pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n        alias: ['class-name-fully-qualified', 'return-type'],\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      }],\n      constant: constant,\n      function: {\n        pattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      property: {\n        pattern: /(->\\s*)\\w+/,\n        lookbehind: true\n      },\n      number: number,\n      operator: operator,\n      punctuation: punctuation\n    };\n    var string_interpolation = {\n      pattern: /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n      lookbehind: true,\n      inside: Prism.languages.php\n    };\n    var string = [{\n      pattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n      alias: 'nowdoc-string',\n      greedy: true,\n      inside: {\n        delimiter: {\n          pattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n          alias: 'symbol',\n          inside: {\n            punctuation: /^<<<'?|[';]$/\n          }\n        }\n      }\n    }, {\n      pattern: /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n      alias: 'heredoc-string',\n      greedy: true,\n      inside: {\n        delimiter: {\n          pattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n          alias: 'symbol',\n          inside: {\n            punctuation: /^<<<\"?|[\";]$/\n          }\n        },\n        interpolation: string_interpolation\n      }\n    }, {\n      pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n      alias: 'backtick-quoted-string',\n      greedy: true\n    }, {\n      pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n      alias: 'single-quoted-string',\n      greedy: true\n    }, {\n      pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n      alias: 'double-quoted-string',\n      greedy: true,\n      inside: {\n        interpolation: string_interpolation\n      }\n    }];\n    Prism.languages.insertBefore('php', 'variable', {\n      string: string,\n      attribute: {\n        pattern: /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n        greedy: true,\n        inside: {\n          'attribute-content': {\n            pattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n            lookbehind: true,\n            // inside can appear subset of php\n            inside: {\n              comment: comment,\n              string: string,\n              'attribute-class-name': [{\n                pattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                alias: 'class-name',\n                greedy: true,\n                lookbehind: true\n              }, {\n                pattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n                alias: ['class-name', 'class-name-fully-qualified'],\n                greedy: true,\n                lookbehind: true,\n                inside: {\n                  punctuation: /\\\\/\n                }\n              }],\n              constant: constant,\n              number: number,\n              operator: operator,\n              punctuation: punctuation\n            }\n          },\n          delimiter: {\n            pattern: /^#\\[|\\]$/,\n            alias: 'punctuation'\n          }\n        }\n      }\n    });\n    Prism.hooks.add('before-tokenize', function (env) {\n      if (!/<\\?/.test(env.code)) {\n        return;\n      }\n      var phpPattern = /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'php', phpPattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'php');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "php", "displayName", "aliases", "Prism", "register", "comment", "constant", "pattern", "alias", "greedy", "lookbehind", "number", "operator", "punctuation", "languages", "delimiter", "variable", "package", "inside", "keyword", "function", "property", "string_interpolation", "string", "interpolation", "insertBefore", "attribute", "hooks", "add", "env", "test", "code", "phpPattern", "buildPlaceholders", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/php.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = php\nphp.displayName = 'php'\nphp.aliases = []\nfunction php(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  /**\n   * Original by <PERSON>: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/\n   * Modified by <PERSON>: http://milesj.me\n   * Rewritten by <PERSON>\n   *\n   * Supports PHP 5.3 - 8.0\n   */\n  ;(function (Prism) {\n    var comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/\n    var constant = [\n      {\n        pattern: /\\b(?:false|true)\\b/i,\n        alias: 'boolean'\n      },\n      {\n        pattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n        greedy: true,\n        lookbehind: true\n      },\n      {\n        pattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n        greedy: true,\n        lookbehind: true\n      },\n      /\\b(?:null)\\b/i,\n      /\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/\n    ]\n    var number =\n      /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i\n    var operator =\n      /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/\n    var punctuation = /[{}\\[\\](),:;]/\n    Prism.languages.php = {\n      delimiter: {\n        pattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n        alias: 'important'\n      },\n      comment: comment,\n      variable: /\\$+(?:\\w+\\b|(?=\\{))/,\n      package: {\n        pattern:\n          /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      'class-name-definition': {\n        pattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      'function-definition': {\n        pattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      keyword: [\n        {\n          pattern:\n            /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n          alias: 'type-casting',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n          alias: 'type-hint',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string|void)\\b/i,\n          alias: 'return-type',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n          alias: 'type-declaration',\n          greedy: true\n        },\n        {\n          pattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n          alias: 'type-declaration',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n          alias: 'static-context',\n          greedy: true\n        },\n        {\n          // yield from\n          pattern: /(\\byield\\s+)from\\b/i,\n          lookbehind: true\n        }, // `class` is always a keyword unlike other keywords\n        /\\bclass\\b/i,\n        {\n          // https://www.php.net/manual/en/reserved.keywords.php\n          //\n          // keywords cannot be preceded by \"->\"\n          // the complex lookbehind means `(?<!(?:->|::)\\s*)`\n          pattern:\n            /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|new|or|parent|print|private|protected|public|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n          lookbehind: true\n        }\n      ],\n      'argument-name': {\n        pattern: /([(,]\\s+)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        lookbehind: true\n      },\n      'class-name': [\n        {\n          pattern:\n            /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n          greedy: true\n        },\n        {\n          pattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern:\n            /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n          alias: 'type-declaration',\n          greedy: true\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n          alias: ['class-name-fully-qualified', 'type-declaration'],\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n          alias: 'static-context',\n          greedy: true\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n          alias: ['class-name-fully-qualified', 'static-context'],\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n          alias: 'type-hint',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n          alias: ['class-name-fully-qualified', 'type-hint'],\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          alias: 'return-type',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n          alias: ['class-name-fully-qualified', 'return-type'],\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        }\n      ],\n      constant: constant,\n      function: {\n        pattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      property: {\n        pattern: /(->\\s*)\\w+/,\n        lookbehind: true\n      },\n      number: number,\n      operator: operator,\n      punctuation: punctuation\n    }\n    var string_interpolation = {\n      pattern:\n        /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n      lookbehind: true,\n      inside: Prism.languages.php\n    }\n    var string = [\n      {\n        pattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n        alias: 'nowdoc-string',\n        greedy: true,\n        inside: {\n          delimiter: {\n            pattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n            alias: 'symbol',\n            inside: {\n              punctuation: /^<<<'?|[';]$/\n            }\n          }\n        }\n      },\n      {\n        pattern:\n          /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n        alias: 'heredoc-string',\n        greedy: true,\n        inside: {\n          delimiter: {\n            pattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n            alias: 'symbol',\n            inside: {\n              punctuation: /^<<<\"?|[\";]$/\n            }\n          },\n          interpolation: string_interpolation\n        }\n      },\n      {\n        pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n        alias: 'backtick-quoted-string',\n        greedy: true\n      },\n      {\n        pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n        alias: 'single-quoted-string',\n        greedy: true\n      },\n      {\n        pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n        alias: 'double-quoted-string',\n        greedy: true,\n        inside: {\n          interpolation: string_interpolation\n        }\n      }\n    ]\n    Prism.languages.insertBefore('php', 'variable', {\n      string: string,\n      attribute: {\n        pattern:\n          /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n        greedy: true,\n        inside: {\n          'attribute-content': {\n            pattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n            lookbehind: true,\n            // inside can appear subset of php\n            inside: {\n              comment: comment,\n              string: string,\n              'attribute-class-name': [\n                {\n                  pattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                  alias: 'class-name',\n                  greedy: true,\n                  lookbehind: true\n                },\n                {\n                  pattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n                  alias: ['class-name', 'class-name-fully-qualified'],\n                  greedy: true,\n                  lookbehind: true,\n                  inside: {\n                    punctuation: /\\\\/\n                  }\n                }\n              ],\n              constant: constant,\n              number: number,\n              operator: operator,\n              punctuation: punctuation\n            }\n          },\n          delimiter: {\n            pattern: /^#\\[|\\]$/,\n            alias: 'punctuation'\n          }\n        }\n      }\n    })\n    Prism.hooks.add('before-tokenize', function (env) {\n      if (!/<\\?/.test(env.code)) {\n        return\n      }\n      var phpPattern =\n        /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'php',\n        phpPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'php')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB;EACxC;AACF;AACA;AACA;AACA;AACA;AACA,KANE;EAOC,CAAC,UAAUO,KAAK,EAAE;IACjB,IAAIE,OAAO,GAAG,mCAAmC;IACjD,IAAIC,QAAQ,GAAG,CACb;MACEC,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE;IACT,CAAC,EACD;MACED,OAAO,EAAE,gCAAgC;MACzCE,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE;IACd,CAAC,EACD;MACEH,OAAO,EAAE,8CAA8C;MACvDE,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE;IACd,CAAC,EACD,eAAe,EACf,+BAA+B,CAChC;IACD,IAAIC,MAAM,GACR,4IAA4I;IAC9I,IAAIC,QAAQ,GACV,uFAAuF;IACzF,IAAIC,WAAW,GAAG,eAAe;IACjCV,KAAK,CAACW,SAAS,CAACd,GAAG,GAAG;MACpBe,SAAS,EAAE;QACTR,OAAO,EAAE,4BAA4B;QACrCC,KAAK,EAAE;MACT,CAAC;MACDH,OAAO,EAAEA,OAAO;MAChBW,QAAQ,EAAE,qBAAqB;MAC/BC,OAAO,EAAE;QACPV,OAAO,EACL,mEAAmE;QACrEG,UAAU,EAAE,IAAI;QAChBQ,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC;MACD,uBAAuB,EAAE;QACvBN,OAAO,EAAE,2DAA2D;QACpEG,UAAU,EAAE,IAAI;QAChBF,KAAK,EAAE;MACT,CAAC;MACD,qBAAqB,EAAE;QACrBD,OAAO,EAAE,oCAAoC;QAC7CG,UAAU,EAAE,IAAI;QAChBF,KAAK,EAAE;MACT,CAAC;MACDW,OAAO,EAAE,CACP;QACEZ,OAAO,EACL,6EAA6E;QAC/EC,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EACL,qIAAqI;QACvIC,KAAK,EAAE,WAAW;QAClBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EACL,4IAA4I;QAC9IC,KAAK,EAAE,aAAa;QACpBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EACL,0EAA0E;QAC5EC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE;MACV,CAAC,EACD;QACEF,OAAO,EAAE,oDAAoD;QAC7DC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EAAE,oCAAoC;QAC7CC,KAAK,EAAE,gBAAgB;QACvBC,MAAM,EAAE;MACV,CAAC,EACD;QACE;QACAF,OAAO,EAAE,qBAAqB;QAC9BG,UAAU,EAAE;MACd,CAAC;MAAE;MACH,YAAY,EACZ;QACE;QACA;QACA;QACA;QACAH,OAAO,EACL,6hBAA6hB;QAC/hBG,UAAU,EAAE;MACd,CAAC,CACF;MACD,eAAe,EAAE;QACfH,OAAO,EAAE,oCAAoC;QAC7CG,UAAU,EAAE;MACd,CAAC;MACD,YAAY,EAAE,CACZ;QACEH,OAAO,EACL,oGAAoG;QACtGE,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EAAE,6BAA6B;QACtCE,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EAAE,+BAA+B;QACxCE,MAAM,EAAE;MACV,CAAC,EACD;QACEF,OAAO,EAAE,+BAA+B;QACxCC,KAAK,EAAE,4BAA4B;QACnCC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBQ,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEN,OAAO,EAAE,iCAAiC;QAC1CC,KAAK,EAAE,4BAA4B;QACnCC,MAAM,EAAE,IAAI;QACZS,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEN,OAAO,EACL,gHAAgH;QAClHC,KAAK,EAAE,4BAA4B;QACnCC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBQ,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEN,OAAO,EAAE,uBAAuB;QAChCC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE;MACV,CAAC,EACD;QACEF,OAAO,EAAE,+BAA+B;QACxCC,KAAK,EAAE,CAAC,4BAA4B,EAAE,kBAAkB,CAAC;QACzDC,MAAM,EAAE,IAAI;QACZS,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEN,OAAO,EAAE,uBAAuB;QAChCC,KAAK,EAAE,gBAAgB;QACvBC,MAAM,EAAE;MACV,CAAC,EACD;QACEF,OAAO,EAAE,+BAA+B;QACxCC,KAAK,EAAE,CAAC,4BAA4B,EAAE,gBAAgB,CAAC;QACvDC,MAAM,EAAE,IAAI;QACZS,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEN,OAAO,EAAE,+BAA+B;QACxCC,KAAK,EAAE,WAAW;QAClBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EAAE,yCAAyC;QAClDC,KAAK,EAAE,CAAC,4BAA4B,EAAE,WAAW,CAAC;QAClDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBQ,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEN,OAAO,EAAE,2CAA2C;QACpDC,KAAK,EAAE,aAAa;QACpBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEH,OAAO,EAAE,mDAAmD;QAC5DC,KAAK,EAAE,CAAC,4BAA4B,EAAE,aAAa,CAAC;QACpDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAI;QAChBQ,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC,CACF;MACDP,QAAQ,EAAEA,QAAQ;MAClBc,QAAQ,EAAE;QACRb,OAAO,EAAE,8CAA8C;QACvDG,UAAU,EAAE,IAAI;QAChBQ,MAAM,EAAE;UACNL,WAAW,EAAE;QACf;MACF,CAAC;MACDQ,QAAQ,EAAE;QACRd,OAAO,EAAE,YAAY;QACrBG,UAAU,EAAE;MACd,CAAC;MACDC,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBC,WAAW,EAAEA;IACf,CAAC;IACD,IAAIS,oBAAoB,GAAG;MACzBf,OAAO,EACL,4FAA4F;MAC9FG,UAAU,EAAE,IAAI;MAChBQ,MAAM,EAAEf,KAAK,CAACW,SAAS,CAACd;IAC1B,CAAC;IACD,IAAIuB,MAAM,GAAG,CACX;MACEhB,OAAO,EAAE,qCAAqC;MAC9CC,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE,IAAI;MACZS,MAAM,EAAE;QACNH,SAAS,EAAE;UACTR,OAAO,EAAE,0BAA0B;UACnCC,KAAK,EAAE,QAAQ;UACfU,MAAM,EAAE;YACNL,WAAW,EAAE;UACf;QACF;MACF;IACF,CAAC,EACD;MACEN,OAAO,EACL,6EAA6E;MAC/EC,KAAK,EAAE,gBAAgB;MACvBC,MAAM,EAAE,IAAI;MACZS,MAAM,EAAE;QACNH,SAAS,EAAE;UACTR,OAAO,EAAE,wCAAwC;UACjDC,KAAK,EAAE,QAAQ;UACfU,MAAM,EAAE;YACNL,WAAW,EAAE;UACf;QACF,CAAC;QACDW,aAAa,EAAEF;MACjB;IACF,CAAC,EACD;MACEf,OAAO,EAAE,wBAAwB;MACjCC,KAAK,EAAE,wBAAwB;MAC/BC,MAAM,EAAE;IACV,CAAC,EACD;MACEF,OAAO,EAAE,wBAAwB;MACjCC,KAAK,EAAE,sBAAsB;MAC7BC,MAAM,EAAE;IACV,CAAC,EACD;MACEF,OAAO,EAAE,wBAAwB;MACjCC,KAAK,EAAE,sBAAsB;MAC7BC,MAAM,EAAE,IAAI;MACZS,MAAM,EAAE;QACNM,aAAa,EAAEF;MACjB;IACF,CAAC,CACF;IACDnB,KAAK,CAACW,SAAS,CAACW,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE;MAC9CF,MAAM,EAAEA,MAAM;MACdG,SAAS,EAAE;QACTnB,OAAO,EACL,2IAA2I;QAC7IE,MAAM,EAAE,IAAI;QACZS,MAAM,EAAE;UACN,mBAAmB,EAAE;YACnBX,OAAO,EAAE,sBAAsB;YAC/BG,UAAU,EAAE,IAAI;YAChB;YACAQ,MAAM,EAAE;cACNb,OAAO,EAAEA,OAAO;cAChBkB,MAAM,EAAEA,MAAM;cACd,sBAAsB,EAAE,CACtB;gBACEhB,OAAO,EAAE,8BAA8B;gBACvCC,KAAK,EAAE,YAAY;gBACnBC,MAAM,EAAE,IAAI;gBACZC,UAAU,EAAE;cACd,CAAC,EACD;gBACEH,OAAO,EAAE,8BAA8B;gBACvCC,KAAK,EAAE,CAAC,YAAY,EAAE,4BAA4B,CAAC;gBACnDC,MAAM,EAAE,IAAI;gBACZC,UAAU,EAAE,IAAI;gBAChBQ,MAAM,EAAE;kBACNL,WAAW,EAAE;gBACf;cACF,CAAC,CACF;cACDP,QAAQ,EAAEA,QAAQ;cAClBK,MAAM,EAAEA,MAAM;cACdC,QAAQ,EAAEA,QAAQ;cAClBC,WAAW,EAAEA;YACf;UACF,CAAC;UACDE,SAAS,EAAE;YACTR,OAAO,EAAE,UAAU;YACnBC,KAAK,EAAE;UACT;QACF;MACF;IACF,CAAC,CAAC;IACFL,KAAK,CAACwB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAI,CAAC,KAAK,CAACC,IAAI,CAACD,GAAG,CAACE,IAAI,CAAC,EAAE;QACzB;MACF;MACA,IAAIC,UAAU,GACZ,mKAAmK;MACrK7B,KAAK,CAACW,SAAS,CAAC,mBAAmB,CAAC,CAACmB,iBAAiB,CACpDJ,GAAG,EACH,KAAK,EACLG,UACF,CAAC;IACH,CAAC,CAAC;IACF7B,KAAK,CAACwB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/C1B,KAAK,CAACW,SAAS,CAAC,mBAAmB,CAAC,CAACoB,oBAAoB,CAACL,GAAG,EAAE,KAAK,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,EAAE1B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}