{"ast": null, "code": "'use strict';\n\nmodule.exports = n4js;\nn4js.displayName = 'n4js';\nn4js.aliases = ['n4jsd'];\nfunction n4js(Prism) {\n  Prism.languages.n4js = Prism.languages.extend('javascript', {\n    // Keywords from N4JS language spec: https://numberfour.github.io/n4js/spec/N4JSSpec.html\n    keyword: /\\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\\b/\n  });\n  Prism.languages.insertBefore('n4js', 'constant', {\n    // Annotations in N4JS spec: https://numberfour.github.io/n4js/spec/N4JSSpec.html#_annotations\n    annotation: {\n      pattern: /@+\\w+/,\n      alias: 'operator'\n    }\n  });\n  Prism.languages.n4jsd = Prism.languages.n4js;\n}", "map": {"version": 3, "names": ["module", "exports", "n4js", "displayName", "aliases", "Prism", "languages", "extend", "keyword", "insertBefore", "annotation", "pattern", "alias", "n4jsd"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/n4js.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = n4js\nn4js.displayName = 'n4js'\nn4js.aliases = ['n4jsd']\nfunction n4js(Prism) {\n  Prism.languages.n4js = Prism.languages.extend('javascript', {\n    // Keywords from N4JS language spec: https://numberfour.github.io/n4js/spec/N4JSSpec.html\n    keyword:\n      /\\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\\b/\n  })\n  Prism.languages.insertBefore('n4js', 'constant', {\n    // Annotations in N4JS spec: https://numberfour.github.io/n4js/spec/N4JSSpec.html#_annotations\n    annotation: {\n      pattern: /@+\\w+/,\n      alias: 'operator'\n    }\n  })\n  Prism.languages.n4jsd = Prism.languages.n4js\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,CAAC,OAAO,CAAC;AACxB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE;IAC1D;IACAC,OAAO,EACL;EACJ,CAAC,CAAC;EACFH,KAAK,CAACC,SAAS,CAACG,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE;IAC/C;IACAC,UAAU,EAAE;MACVC,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFP,KAAK,CAACC,SAAS,CAACO,KAAK,GAAGR,KAAK,CAACC,SAAS,CAACJ,IAAI;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}