{"ast": null, "code": "import createAsyncLoadingHighlighter from './async-syntax-highlighter';\nimport languageLoaders from './async-languages/hljs';\nimport checkForListedLanguage from './checkForListedLanguage';\nexport default createAsyncLoadingHighlighter({\n  loader: function loader() {\n    return import(/* webpackChunkName:\"react-syntax-highlighter/lowlight-import\" */\n    'lowlight/lib/core').then(function (module) {\n      // Webpack 3 returns module.exports as default as module, but webpack 4 returns module.exports as module.default\n      return module[\"default\"] || module;\n    });\n  },\n  isLanguageRegistered: function isLanguageRegistered(instance, language) {\n    return !!checkForListedLanguage(instance, language);\n  },\n  languageLoaders: languageLoaders,\n  registerLanguage: function registerLanguage(instance, name, language) {\n    return instance.registerLanguage(name, language);\n  }\n});", "map": {"version": 3, "names": ["createAsyncLoadingHighlighter", "languageLoaders", "checkForListedLanguage", "loader", "then", "module", "isLanguageRegistered", "instance", "language", "registerLanguage", "name"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/react-syntax-highlighter/dist/esm/light-async.js"], "sourcesContent": ["import createAsyncLoadingHighlighter from './async-syntax-highlighter';\nimport languageLoaders from './async-languages/hljs';\nimport checkForListedLanguage from './checkForListedLanguage';\nexport default createAsyncLoadingHighlighter({\n  loader: function loader() {\n    return import( /* webpackChunkName:\"react-syntax-highlighter/lowlight-import\" */\n    'lowlight/lib/core').then(function (module) {\n      // Webpack 3 returns module.exports as default as module, but webpack 4 returns module.exports as module.default\n      return module[\"default\"] || module;\n    });\n  },\n  isLanguageRegistered: function isLanguageRegistered(instance, language) {\n    return !!checkForListedLanguage(instance, language);\n  },\n  languageLoaders: languageLoaders,\n  registerLanguage: function registerLanguage(instance, name, language) {\n    return instance.registerLanguage(name, language);\n  }\n});"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,4BAA4B;AACtE,OAAOC,eAAe,MAAM,wBAAwB;AACpD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,eAAeF,6BAA6B,CAAC;EAC3CG,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO,MAAM,CAAE;IACf,mBAAmB,CAAC,CAACC,IAAI,CAAC,UAAUC,MAAM,EAAE;MAC1C;MACA,OAAOA,MAAM,CAAC,SAAS,CAAC,IAAIA,MAAM;IACpC,CAAC,CAAC;EACJ,CAAC;EACDC,oBAAoB,EAAE,SAASA,oBAAoBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IACtE,OAAO,CAAC,CAACN,sBAAsB,CAACK,QAAQ,EAAEC,QAAQ,CAAC;EACrD,CAAC;EACDP,eAAe,EAAEA,eAAe;EAChCQ,gBAAgB,EAAE,SAASA,gBAAgBA,CAACF,QAAQ,EAAEG,IAAI,EAAEF,QAAQ,EAAE;IACpE,OAAOD,QAAQ,CAACE,gBAAgB,CAACC,IAAI,EAAEF,QAAQ,CAAC;EAClD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}