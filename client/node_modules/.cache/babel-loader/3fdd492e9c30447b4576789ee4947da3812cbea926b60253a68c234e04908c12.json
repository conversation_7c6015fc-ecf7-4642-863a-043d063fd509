{"ast": null, "code": "'use strict';\n\nmodule.exports = bro;\nbro.displayName = 'bro';\nbro.aliases = [];\nfunction bro(Prism) {\n  Prism.languages.bro = {\n    comment: {\n      pattern: /(^|[^\\\\$])#.*/,\n      lookbehind: true,\n      inside: {\n        italic: /\\b(?:FIXME|TODO|XXX)\\b/\n      }\n    },\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    boolean: /\\b[TF]\\b/,\n    function: {\n      pattern: /(\\b(?:event|function|hook)[ \\t]+)\\w+(?:::\\w+)?/,\n      lookbehind: true\n    },\n    builtin: /(?:@(?:load(?:-(?:plugin|sigs))?|unload|prefixes|ifn?def|else|(?:end)?if|DIR|FILENAME))|(?:&?(?:add_func|create_expire|default|delete_func|encrypt|error_handler|expire_func|group|log|mergeable|optional|persistent|priority|raw_output|read_expire|redef|rotate_interval|rotate_size|synchronized|type_column|write_expire))/,\n    constant: {\n      pattern: /(\\bconst[ \\t]+)\\w+/i,\n      lookbehind: true\n    },\n    keyword: /\\b(?:add|addr|alarm|any|bool|break|const|continue|count|delete|double|else|enum|event|export|file|for|function|global|hook|if|in|int|interval|local|module|next|of|opaque|pattern|port|print|record|return|schedule|set|string|subnet|table|time|timeout|using|vector|when)\\b/,\n    operator: /--?|\\+\\+?|!=?=?|<=?|>=?|==?=?|&&|\\|\\|?|\\?|\\*|\\/|~|\\^|%/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    punctuation: /[{}[\\];(),.:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "bro", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "inside", "italic", "string", "greedy", "boolean", "function", "builtin", "constant", "keyword", "operator", "number", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/bro.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bro\nbro.displayName = 'bro'\nbro.aliases = []\nfunction bro(Prism) {\n  Prism.languages.bro = {\n    comment: {\n      pattern: /(^|[^\\\\$])#.*/,\n      lookbehind: true,\n      inside: {\n        italic: /\\b(?:FIXME|TODO|XXX)\\b/\n      }\n    },\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    boolean: /\\b[TF]\\b/,\n    function: {\n      pattern: /(\\b(?:event|function|hook)[ \\t]+)\\w+(?:::\\w+)?/,\n      lookbehind: true\n    },\n    builtin:\n      /(?:@(?:load(?:-(?:plugin|sigs))?|unload|prefixes|ifn?def|else|(?:end)?if|DIR|FILENAME))|(?:&?(?:add_func|create_expire|default|delete_func|encrypt|error_handler|expire_func|group|log|mergeable|optional|persistent|priority|raw_output|read_expire|redef|rotate_interval|rotate_size|synchronized|type_column|write_expire))/,\n    constant: {\n      pattern: /(\\bconst[ \\t]+)\\w+/i,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:add|addr|alarm|any|bool|break|const|continue|count|delete|double|else|enum|event|export|file|for|function|global|hook|if|in|int|interval|local|module|next|of|opaque|pattern|port|print|record|return|schedule|set|string|subnet|table|time|timeout|using|vector|when)\\b/,\n    operator: /--?|\\+\\+?|!=?=?|<=?|>=?|==?=?|&&|\\|\\|?|\\?|\\*|\\/|~|\\^|%/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    punctuation: /[{}[\\];(),.:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE;MACPC,OAAO,EAAE,eAAe;MACxBC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,MAAM,EAAE;MACV;IACF,CAAC;IACDC,MAAM,EAAE;MACNJ,OAAO,EAAE,gDAAgD;MACzDK,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE;MACRP,OAAO,EAAE,gDAAgD;MACzDC,UAAU,EAAE;IACd,CAAC;IACDO,OAAO,EACL,gUAAgU;IAClUC,QAAQ,EAAE;MACRT,OAAO,EAAE,qBAAqB;MAC9BC,UAAU,EAAE;IACd,CAAC;IACDS,OAAO,EACL,+QAA+Q;IACjRC,QAAQ,EAAE,wDAAwD;IAClEC,MAAM,EAAE,2DAA2D;IACnEC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}