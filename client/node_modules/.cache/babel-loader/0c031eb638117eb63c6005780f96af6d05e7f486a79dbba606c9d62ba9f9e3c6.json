{"ast": null, "code": "/*\nLanguage: Brainfuck\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://esolangs.org/wiki/Brainfuck\n*/\n\n/** @type LanguageFn */\nfunction brainfuck(hljs) {\n  const LITERAL = {\n    className: 'literal',\n    begin: /[+-]/,\n    relevance: 0\n  };\n  return {\n    name: '<PERSON>fu<PERSON>',\n    aliases: ['bf'],\n    contains: [hljs.COMMENT('[^\\\\[\\\\]\\\\.,\\\\+\\\\-<> \\r\\n]', '[\\\\[\\\\]\\\\.,\\\\+\\\\-<> \\r\\n]', {\n      returnEnd: true,\n      relevance: 0\n    }), {\n      className: 'title',\n      begin: '[\\\\[\\\\]]',\n      relevance: 0\n    }, {\n      className: 'string',\n      begin: '[\\\\.,]',\n      relevance: 0\n    }, {\n      // this mode works as the only relevance counter\n      begin: /(?:\\+\\+|--)/,\n      contains: [LITERAL]\n    }, LITERAL]\n  };\n}\nmodule.exports = brainfuck;", "map": {"version": 3, "names": ["brainfuck", "hljs", "LITERAL", "className", "begin", "relevance", "name", "aliases", "contains", "COMMENT", "returnEnd", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/brainfuck.js"], "sourcesContent": ["/*\nLanguage: Brainfuck\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://esolangs.org/wiki/Brainfuck\n*/\n\n/** @type LanguageFn */\nfunction brainfuck(hljs) {\n  const LITERAL = {\n    className: 'literal',\n    begin: /[+-]/,\n    relevance: 0\n  };\n  return {\n    name: '<PERSON>fu<PERSON>',\n    aliases: ['bf'],\n    contains: [\n      hljs.COMMENT(\n        '[^\\\\[\\\\]\\\\.,\\\\+\\\\-<> \\r\\n]',\n        '[\\\\[\\\\]\\\\.,\\\\+\\\\-<> \\r\\n]',\n        {\n          returnEnd: true,\n          relevance: 0\n        }\n      ),\n      {\n        className: 'title',\n        begin: '[\\\\[\\\\]]',\n        relevance: 0\n      },\n      {\n        className: 'string',\n        begin: '[\\\\.,]',\n        relevance: 0\n      },\n      {\n        // this mode works as the only relevance counter\n        begin: /(?:\\+\\+|--)/,\n        contains: [LITERAL]\n      },\n      LITERAL\n    ]\n  };\n}\n\nmodule.exports = brainfuck;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,SAASA,CAACC,IAAI,EAAE;EACvB,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC;EACD,OAAO;IACLC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,CAAC,IAAI,CAAC;IACfC,QAAQ,EAAE,CACRP,IAAI,CAACQ,OAAO,CACV,4BAA4B,EAC5B,2BAA2B,EAC3B;MACEC,SAAS,EAAE,IAAI;MACfL,SAAS,EAAE;IACb,CACF,CAAC,EACD;MACEF,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,UAAU;MACjBC,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACAD,KAAK,EAAE,aAAa;MACpBI,QAAQ,EAAE,CAACN,OAAO;IACpB,CAAC,EACDA,OAAO;EAEX,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}