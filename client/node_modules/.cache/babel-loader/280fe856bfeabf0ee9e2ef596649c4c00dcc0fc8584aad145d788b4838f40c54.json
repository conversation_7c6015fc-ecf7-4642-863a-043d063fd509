{"ast": null, "code": "'use strict';\n\nmodule.exports = yaml;\nyaml.displayName = 'yaml';\nyaml.aliases = ['yml'];\nfunction yaml(Prism) {\n  ;\n  (function (Prism) {\n    // https://yaml.org/spec/1.2/spec.html#c-ns-anchor-property\n    // https://yaml.org/spec/1.2/spec.html#c-ns-alias-node\n    var anchorOrAlias = /[*&][^\\s[\\]{},]+/; // https://yaml.org/spec/1.2/spec.html#c-ns-tag-property\n    var tag = /!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/; // https://yaml.org/spec/1.2/spec.html#c-ns-properties(n,c)\n    var properties = '(?:' + tag.source + '(?:[ \\t]+' + anchorOrAlias.source + ')?|' + anchorOrAlias.source + '(?:[ \\t]+' + tag.source + ')?)'; // https://yaml.org/spec/1.2/spec.html#ns-plain(n,c)\n    // This is a simplified version that doesn't support \"#\" and multiline keys\n    // All these long scarry character classes are simplified versions of YAML's characters\n    var plainKey = /(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g, function () {\n      return /[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/.source;\n    });\n    var string = /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source;\n    /**\n     *\n     * @param {string} value\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function createValuePattern(value, flags) {\n      flags = (flags || '').replace(/m/g, '') + 'm'; // add m flag\n      var pattern = /([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source.replace(/<<prop>>/g, function () {\n        return properties;\n      }).replace(/<<value>>/g, function () {\n        return value;\n      });\n      return RegExp(pattern, flags);\n    }\n    Prism.languages.yaml = {\n      scalar: {\n        pattern: RegExp(/([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source.replace(/<<prop>>/g, function () {\n          return properties;\n        })),\n        lookbehind: true,\n        alias: 'string'\n      },\n      comment: /#.*/,\n      key: {\n        pattern: RegExp(/((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source.replace(/<<prop>>/g, function () {\n          return properties;\n        }).replace(/<<key>>/g, function () {\n          return '(?:' + plainKey + '|' + string + ')';\n        })),\n        lookbehind: true,\n        greedy: true,\n        alias: 'atrule'\n      },\n      directive: {\n        pattern: /(^[ \\t]*)%.+/m,\n        lookbehind: true,\n        alias: 'important'\n      },\n      datetime: {\n        pattern: createValuePattern(/\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      boolean: {\n        pattern: createValuePattern(/false|true/.source, 'i'),\n        lookbehind: true,\n        alias: 'important'\n      },\n      null: {\n        pattern: createValuePattern(/null|~/.source, 'i'),\n        lookbehind: true,\n        alias: 'important'\n      },\n      string: {\n        pattern: createValuePattern(string),\n        lookbehind: true,\n        greedy: true\n      },\n      number: {\n        pattern: createValuePattern(/[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/.source, 'i'),\n        lookbehind: true\n      },\n      tag: tag,\n      important: anchorOrAlias,\n      punctuation: /---|[:[\\]{}\\-,|>?]|\\.\\.\\./\n    };\n    Prism.languages.yml = Prism.languages.yaml;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "yaml", "displayName", "aliases", "Prism", "anchorOr<PERSON>lias", "tag", "properties", "source", "plain<PERSON>ey", "replace", "string", "createValuePattern", "value", "flags", "pattern", "RegExp", "languages", "scalar", "lookbehind", "alias", "comment", "key", "greedy", "directive", "datetime", "boolean", "null", "number", "important", "punctuation", "yml"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/yaml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = yaml\nyaml.displayName = 'yaml'\nyaml.aliases = ['yml']\nfunction yaml(Prism) {\n  ;(function (Prism) {\n    // https://yaml.org/spec/1.2/spec.html#c-ns-anchor-property\n    // https://yaml.org/spec/1.2/spec.html#c-ns-alias-node\n    var anchorOrAlias = /[*&][^\\s[\\]{},]+/ // https://yaml.org/spec/1.2/spec.html#c-ns-tag-property\n    var tag =\n      /!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/ // https://yaml.org/spec/1.2/spec.html#c-ns-properties(n,c)\n    var properties =\n      '(?:' +\n      tag.source +\n      '(?:[ \\t]+' +\n      anchorOrAlias.source +\n      ')?|' +\n      anchorOrAlias.source +\n      '(?:[ \\t]+' +\n      tag.source +\n      ')?)' // https://yaml.org/spec/1.2/spec.html#ns-plain(n,c)\n    // This is a simplified version that doesn't support \"#\" and multiline keys\n    // All these long scarry character classes are simplified versions of YAML's characters\n    var plainKey =\n      /(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(\n        /<PLAIN>/g,\n        function () {\n          return /[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/\n            .source\n        }\n      )\n    var string = /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source\n    /**\n     *\n     * @param {string} value\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function createValuePattern(value, flags) {\n      flags = (flags || '').replace(/m/g, '') + 'm' // add m flag\n      var pattern =\n        /([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source\n          .replace(/<<prop>>/g, function () {\n            return properties\n          })\n          .replace(/<<value>>/g, function () {\n            return value\n          })\n      return RegExp(pattern, flags)\n    }\n    Prism.languages.yaml = {\n      scalar: {\n        pattern: RegExp(\n          /([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source.replace(\n            /<<prop>>/g,\n            function () {\n              return properties\n            }\n          )\n        ),\n        lookbehind: true,\n        alias: 'string'\n      },\n      comment: /#.*/,\n      key: {\n        pattern: RegExp(\n          /((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source\n            .replace(/<<prop>>/g, function () {\n              return properties\n            })\n            .replace(/<<key>>/g, function () {\n              return '(?:' + plainKey + '|' + string + ')'\n            })\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'atrule'\n      },\n      directive: {\n        pattern: /(^[ \\t]*)%.+/m,\n        lookbehind: true,\n        alias: 'important'\n      },\n      datetime: {\n        pattern: createValuePattern(\n          /\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/\n            .source\n        ),\n        lookbehind: true,\n        alias: 'number'\n      },\n      boolean: {\n        pattern: createValuePattern(/false|true/.source, 'i'),\n        lookbehind: true,\n        alias: 'important'\n      },\n      null: {\n        pattern: createValuePattern(/null|~/.source, 'i'),\n        lookbehind: true,\n        alias: 'important'\n      },\n      string: {\n        pattern: createValuePattern(string),\n        lookbehind: true,\n        greedy: true\n      },\n      number: {\n        pattern: createValuePattern(\n          /[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/\n            .source,\n          'i'\n        ),\n        lookbehind: true\n      },\n      tag: tag,\n      important: anchorOrAlias,\n      punctuation: /---|[:[\\]{}\\-,|>?]|\\.\\.\\./\n    }\n    Prism.languages.yml = Prism.languages.yaml\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AACtB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA;IACA,IAAIC,aAAa,GAAG,kBAAkB,EAAC;IACvC,IAAIC,GAAG,GACL,kFAAkF,EAAC;IACrF,IAAIC,UAAU,GACZ,KAAK,GACLD,GAAG,CAACE,MAAM,GACV,WAAW,GACXH,aAAa,CAACG,MAAM,GACpB,KAAK,GACLH,aAAa,CAACG,MAAM,GACpB,WAAW,GACXF,GAAG,CAACE,MAAM,GACV,KAAK,EAAC;IACR;IACA;IACA,IAAIC,QAAQ,GACV,iJAAiJ,CAACD,MAAM,CAACE,OAAO,CAC9J,UAAU,EACV,YAAY;MACV,OAAO,0EAA0E,CAC9EF,MAAM;IACX,CACF,CAAC;IACH,IAAIG,MAAM,GAAG,6CAA6C,CAACH,MAAM;IACjE;AACJ;AACA;AACA;AACA;AACA;IACI,SAASI,kBAAkBA,CAACC,KAAK,EAAEC,KAAK,EAAE;MACxCA,KAAK,GAAG,CAACA,KAAK,IAAI,EAAE,EAAEJ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,EAAC;MAC9C,IAAIK,OAAO,GACT,wFAAwF,CAACP,MAAM,CAC5FE,OAAO,CAAC,WAAW,EAAE,YAAY;QAChC,OAAOH,UAAU;MACnB,CAAC,CAAC,CACDG,OAAO,CAAC,YAAY,EAAE,YAAY;QACjC,OAAOG,KAAK;MACd,CAAC,CAAC;MACN,OAAOG,MAAM,CAACD,OAAO,EAAED,KAAK,CAAC;IAC/B;IACAV,KAAK,CAACa,SAAS,CAAChB,IAAI,GAAG;MACrBiB,MAAM,EAAE;QACNH,OAAO,EAAEC,MAAM,CACb,4FAA4F,CAACR,MAAM,CAACE,OAAO,CACzG,WAAW,EACX,YAAY;UACV,OAAOH,UAAU;QACnB,CACF,CACF,CAAC;QACDY,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,OAAO,EAAE,KAAK;MACdC,GAAG,EAAE;QACHP,OAAO,EAAEC,MAAM,CACb,iEAAiE,CAACR,MAAM,CACrEE,OAAO,CAAC,WAAW,EAAE,YAAY;UAChC,OAAOH,UAAU;QACnB,CAAC,CAAC,CACDG,OAAO,CAAC,UAAU,EAAE,YAAY;UAC/B,OAAO,KAAK,GAAGD,QAAQ,GAAG,GAAG,GAAGE,MAAM,GAAG,GAAG;QAC9C,CAAC,CACL,CAAC;QACDQ,UAAU,EAAE,IAAI;QAChBI,MAAM,EAAE,IAAI;QACZH,KAAK,EAAE;MACT,CAAC;MACDI,SAAS,EAAE;QACTT,OAAO,EAAE,eAAe;QACxBI,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDK,QAAQ,EAAE;QACRV,OAAO,EAAEH,kBAAkB,CACzB,qJAAqJ,CAClJJ,MACL,CAAC;QACDW,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDM,OAAO,EAAE;QACPX,OAAO,EAAEH,kBAAkB,CAAC,YAAY,CAACJ,MAAM,EAAE,GAAG,CAAC;QACrDW,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJZ,OAAO,EAAEH,kBAAkB,CAAC,QAAQ,CAACJ,MAAM,EAAE,GAAG,CAAC;QACjDW,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDT,MAAM,EAAE;QACNI,OAAO,EAAEH,kBAAkB,CAACD,MAAM,CAAC;QACnCQ,UAAU,EAAE,IAAI;QAChBI,MAAM,EAAE;MACV,CAAC;MACDK,MAAM,EAAE;QACNb,OAAO,EAAEH,kBAAkB,CACzB,gFAAgF,CAC7EJ,MAAM,EACT,GACF,CAAC;QACDW,UAAU,EAAE;MACd,CAAC;MACDb,GAAG,EAAEA,GAAG;MACRuB,SAAS,EAAExB,aAAa;MACxByB,WAAW,EAAE;IACf,CAAC;IACD1B,KAAK,CAACa,SAAS,CAACc,GAAG,GAAG3B,KAAK,CAACa,SAAS,CAAChB,IAAI;EAC5C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}