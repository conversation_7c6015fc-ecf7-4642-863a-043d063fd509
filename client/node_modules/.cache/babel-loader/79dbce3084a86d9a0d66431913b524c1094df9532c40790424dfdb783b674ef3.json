{"ast": null, "code": "/*\nLanguage: Mojolicious\nRequires: xml.js, perl.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Mojolicious .ep (Embedded Perl) templates\nWebsite: https://mojolicious.org\nCategory: template\n*/\nfunction mojolicious(hljs) {\n  return {\n    name: 'Mojolicious',\n    subLanguage: 'xml',\n    contains: [{\n      className: 'meta',\n      begin: '^__(END|DATA)__$'\n    },\n    // mojolicious line\n    {\n      begin: \"^\\\\s*%{1,2}={0,2}\",\n      end: '$',\n      subLanguage: 'perl'\n    },\n    // mojolicious block\n    {\n      begin: \"<%{1,2}={0,2}\",\n      end: \"={0,1}%>\",\n      subLanguage: 'perl',\n      excludeBegin: true,\n      excludeEnd: true\n    }]\n  };\n}\nmodule.exports = mojolicious;", "map": {"version": 3, "names": ["mojolicious", "hljs", "name", "subLanguage", "contains", "className", "begin", "end", "excludeBegin", "excludeEnd", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/mojolicious.js"], "sourcesContent": ["/*\nLanguage: Mojolicious\nRequires: xml.js, perl.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Mojolicious .ep (Embedded Perl) templates\nWebsite: https://mojolicious.org\nCategory: template\n*/\nfunction mojolicious(hljs) {\n  return {\n    name: 'Mojolicious',\n    subLanguage: 'xml',\n    contains: [\n      {\n        className: 'meta',\n        begin: '^__(END|DATA)__$'\n      },\n      // mojolicious line\n      {\n        begin: \"^\\\\s*%{1,2}={0,2}\",\n        end: '$',\n        subLanguage: 'perl'\n      },\n      // mojolicious block\n      {\n        begin: \"<%{1,2}={0,2}\",\n        end: \"={0,1}%>\",\n        subLanguage: 'perl',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n}\n\nmodule.exports = mojolicious;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,IAAI,EAAE;EACzB,OAAO;IACLC,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACEA,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE,GAAG;MACRJ,WAAW,EAAE;IACf,CAAC;IACD;IACA;MACEG,KAAK,EAAE,eAAe;MACtBC,GAAG,EAAE,UAAU;MACfJ,WAAW,EAAE,MAAM;MACnBK,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}