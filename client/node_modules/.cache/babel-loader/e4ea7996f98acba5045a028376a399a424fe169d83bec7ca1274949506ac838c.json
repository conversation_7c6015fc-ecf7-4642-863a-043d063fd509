{"ast": null, "code": "'use strict';\n\nmodule.exports = rust;\nrust.displayName = 'rust';\nrust.aliases = [];\nfunction rust(Prism) {\n  ;\n  (function (Prism) {\n    var multilineComment = /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\//.source;\n    for (var i = 0; i < 2; i++) {\n      // support 4 levels of nested comments\n      multilineComment = multilineComment.replace(/<self>/g, function () {\n        return multilineComment;\n      });\n    }\n    multilineComment = multilineComment.replace(/<self>/g, function () {\n      return /[^\\s\\S]/.source;\n    });\n    Prism.languages.rust = {\n      comment: [{\n        pattern: RegExp(/(^|[^\\\\])/.source + multilineComment),\n        lookbehind: true,\n        greedy: true\n      }, {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }],\n      string: {\n        pattern: /b?\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|b?r(#*)\"(?:[^\"]|\"(?!\\1))*\"\\1/,\n        greedy: true\n      },\n      char: {\n        pattern: /b?'(?:\\\\(?:x[0-7][\\da-fA-F]|u\\{(?:[\\da-fA-F]_*){1,6}\\}|.)|[^\\\\\\r\\n\\t'])'/,\n        greedy: true\n      },\n      attribute: {\n        pattern: /#!?\\[(?:[^\\[\\]\"]|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")*\\]/,\n        greedy: true,\n        alias: 'attr-name',\n        inside: {\n          string: null // see below\n        }\n      },\n      // Closure params should not be confused with bitwise OR |\n      'closure-params': {\n        pattern: /([=(,:]\\s*|\\bmove\\s*)\\|[^|]*\\||\\|[^|]*\\|(?=\\s*(?:\\{|->))/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          'closure-punctuation': {\n            pattern: /^\\||\\|$/,\n            alias: 'punctuation'\n          },\n          rest: null // see below\n        }\n      },\n      'lifetime-annotation': {\n        pattern: /'\\w+/,\n        alias: 'symbol'\n      },\n      'fragment-specifier': {\n        pattern: /(\\$\\w+:)[a-z]+/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      variable: /\\$\\w+/,\n      'function-definition': {\n        pattern: /(\\bfn\\s+)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      'type-definition': {\n        pattern: /(\\b(?:enum|struct|trait|type|union)\\s+)\\w+/,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      'module-declaration': [{\n        pattern: /(\\b(?:crate|mod)\\s+)[a-z][a-z_\\d]*/,\n        lookbehind: true,\n        alias: 'namespace'\n      }, {\n        pattern: /(\\b(?:crate|self|super)\\s*)::\\s*[a-z][a-z_\\d]*\\b(?:\\s*::(?:\\s*[a-z][a-z_\\d]*\\s*::)*)?/,\n        lookbehind: true,\n        alias: 'namespace',\n        inside: {\n          punctuation: /::/\n        }\n      }],\n      keyword: [\n      // https://github.com/rust-lang/reference/blob/master/src/keywords.md\n      /\\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\\b/,\n      // primitives and str\n      // https://doc.rust-lang.org/stable/rust-by-example/primitives.html\n      /\\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\\b/],\n      // functions can technically start with an upper-case letter, but this will introduce a lot of false positives\n      // and Rust's naming conventions recommend snake_case anyway.\n      // https://doc.rust-lang.org/1.0.0/style/style/naming/README.html\n      function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())/,\n      macro: {\n        pattern: /\\b\\w+!/,\n        alias: 'property'\n      },\n      constant: /\\b[A-Z_][A-Z_\\d]+\\b/,\n      'class-name': /\\b[A-Z]\\w*\\b/,\n      namespace: {\n        pattern: /(?:\\b[a-z][a-z_\\d]*\\s*::\\s*)*\\b[a-z][a-z_\\d]*\\s*::(?!\\s*<)/,\n        inside: {\n          punctuation: /::/\n        }\n      },\n      // Hex, oct, bin, dec numbers with visual separators and type suffix\n      number: /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      punctuation: /->|\\.\\.=|\\.{1,3}|::|[{}[\\];(),:]/,\n      operator: /[-+*\\/%!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?=?|[@?]/\n    };\n    Prism.languages.rust['closure-params'].inside.rest = Prism.languages.rust;\n    Prism.languages.rust['attribute'].inside['string'] = Prism.languages.rust['string'];\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "rust", "displayName", "aliases", "Prism", "multilineComment", "source", "i", "replace", "languages", "comment", "pattern", "RegExp", "lookbehind", "greedy", "string", "char", "attribute", "alias", "inside", "rest", "variable", "punctuation", "keyword", "function", "macro", "constant", "namespace", "number", "boolean", "operator"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/rust.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = rust\nrust.displayName = 'rust'\nrust.aliases = []\nfunction rust(Prism) {\n  ;(function (Prism) {\n    var multilineComment = /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\//.source\n    for (var i = 0; i < 2; i++) {\n      // support 4 levels of nested comments\n      multilineComment = multilineComment.replace(/<self>/g, function () {\n        return multilineComment\n      })\n    }\n    multilineComment = multilineComment.replace(/<self>/g, function () {\n      return /[^\\s\\S]/.source\n    })\n    Prism.languages.rust = {\n      comment: [\n        {\n          pattern: RegExp(/(^|[^\\\\])/.source + multilineComment),\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      string: {\n        pattern: /b?\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|b?r(#*)\"(?:[^\"]|\"(?!\\1))*\"\\1/,\n        greedy: true\n      },\n      char: {\n        pattern:\n          /b?'(?:\\\\(?:x[0-7][\\da-fA-F]|u\\{(?:[\\da-fA-F]_*){1,6}\\}|.)|[^\\\\\\r\\n\\t'])'/,\n        greedy: true\n      },\n      attribute: {\n        pattern: /#!?\\[(?:[^\\[\\]\"]|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")*\\]/,\n        greedy: true,\n        alias: 'attr-name',\n        inside: {\n          string: null // see below\n        }\n      },\n      // Closure params should not be confused with bitwise OR |\n      'closure-params': {\n        pattern: /([=(,:]\\s*|\\bmove\\s*)\\|[^|]*\\||\\|[^|]*\\|(?=\\s*(?:\\{|->))/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          'closure-punctuation': {\n            pattern: /^\\||\\|$/,\n            alias: 'punctuation'\n          },\n          rest: null // see below\n        }\n      },\n      'lifetime-annotation': {\n        pattern: /'\\w+/,\n        alias: 'symbol'\n      },\n      'fragment-specifier': {\n        pattern: /(\\$\\w+:)[a-z]+/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      variable: /\\$\\w+/,\n      'function-definition': {\n        pattern: /(\\bfn\\s+)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      'type-definition': {\n        pattern: /(\\b(?:enum|struct|trait|type|union)\\s+)\\w+/,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      'module-declaration': [\n        {\n          pattern: /(\\b(?:crate|mod)\\s+)[a-z][a-z_\\d]*/,\n          lookbehind: true,\n          alias: 'namespace'\n        },\n        {\n          pattern:\n            /(\\b(?:crate|self|super)\\s*)::\\s*[a-z][a-z_\\d]*\\b(?:\\s*::(?:\\s*[a-z][a-z_\\d]*\\s*::)*)?/,\n          lookbehind: true,\n          alias: 'namespace',\n          inside: {\n            punctuation: /::/\n          }\n        }\n      ],\n      keyword: [\n        // https://github.com/rust-lang/reference/blob/master/src/keywords.md\n        /\\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\\b/, // primitives and str\n        // https://doc.rust-lang.org/stable/rust-by-example/primitives.html\n        /\\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\\b/\n      ],\n      // functions can technically start with an upper-case letter, but this will introduce a lot of false positives\n      // and Rust's naming conventions recommend snake_case anyway.\n      // https://doc.rust-lang.org/1.0.0/style/style/naming/README.html\n      function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())/,\n      macro: {\n        pattern: /\\b\\w+!/,\n        alias: 'property'\n      },\n      constant: /\\b[A-Z_][A-Z_\\d]+\\b/,\n      'class-name': /\\b[A-Z]\\w*\\b/,\n      namespace: {\n        pattern: /(?:\\b[a-z][a-z_\\d]*\\s*::\\s*)*\\b[a-z][a-z_\\d]*\\s*::(?!\\s*<)/,\n        inside: {\n          punctuation: /::/\n        }\n      },\n      // Hex, oct, bin, dec numbers with visual separators and type suffix\n      number:\n        /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      punctuation: /->|\\.\\.=|\\.{1,3}|::|[{}[\\];(),:]/,\n      operator: /[-+*\\/%!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?=?|[@?]/\n    }\n    Prism.languages.rust['closure-params'].inside.rest = Prism.languages.rust\n    Prism.languages.rust['attribute'].inside['string'] =\n      Prism.languages.rust['string']\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,gBAAgB,GAAG,6CAA6C,CAACC,MAAM;IAC3E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B;MACAF,gBAAgB,GAAGA,gBAAgB,CAACG,OAAO,CAAC,SAAS,EAAE,YAAY;QACjE,OAAOH,gBAAgB;MACzB,CAAC,CAAC;IACJ;IACAA,gBAAgB,GAAGA,gBAAgB,CAACG,OAAO,CAAC,SAAS,EAAE,YAAY;MACjE,OAAO,SAAS,CAACF,MAAM;IACzB,CAAC,CAAC;IACFF,KAAK,CAACK,SAAS,CAACR,IAAI,GAAG;MACrBS,OAAO,EAAE,CACP;QACEC,OAAO,EAAEC,MAAM,CAAC,WAAW,CAACN,MAAM,GAAGD,gBAAgB,CAAC;QACtDQ,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACEH,OAAO,EAAE,kBAAkB;QAC3BE,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,MAAM,EAAE;QACNJ,OAAO,EAAE,uDAAuD;QAChEG,MAAM,EAAE;MACV,CAAC;MACDE,IAAI,EAAE;QACJL,OAAO,EACL,0EAA0E;QAC5EG,MAAM,EAAE;MACV,CAAC;MACDG,SAAS,EAAE;QACTN,OAAO,EAAE,6CAA6C;QACtDG,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE,WAAW;QAClBC,MAAM,EAAE;UACNJ,MAAM,EAAE,IAAI,CAAC;QACf;MACF,CAAC;MACD;MACA,gBAAgB,EAAE;QAChBJ,OAAO,EAAE,0DAA0D;QACnEE,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZK,MAAM,EAAE;UACN,qBAAqB,EAAE;YACrBR,OAAO,EAAE,SAAS;YAClBO,KAAK,EAAE;UACT,CAAC;UACDE,IAAI,EAAE,IAAI,CAAC;QACb;MACF,CAAC;MACD,qBAAqB,EAAE;QACrBT,OAAO,EAAE,MAAM;QACfO,KAAK,EAAE;MACT,CAAC;MACD,oBAAoB,EAAE;QACpBP,OAAO,EAAE,gBAAgB;QACzBE,UAAU,EAAE,IAAI;QAChBK,KAAK,EAAE;MACT,CAAC;MACDG,QAAQ,EAAE,OAAO;MACjB,qBAAqB,EAAE;QACrBV,OAAO,EAAE,cAAc;QACvBE,UAAU,EAAE,IAAI;QAChBK,KAAK,EAAE;MACT,CAAC;MACD,iBAAiB,EAAE;QACjBP,OAAO,EAAE,4CAA4C;QACrDE,UAAU,EAAE,IAAI;QAChBK,KAAK,EAAE;MACT,CAAC;MACD,oBAAoB,EAAE,CACpB;QACEP,OAAO,EAAE,oCAAoC;QAC7CE,UAAU,EAAE,IAAI;QAChBK,KAAK,EAAE;MACT,CAAC,EACD;QACEP,OAAO,EACL,uFAAuF;QACzFE,UAAU,EAAE,IAAI;QAChBK,KAAK,EAAE,WAAW;QAClBC,MAAM,EAAE;UACNG,WAAW,EAAE;QACf;MACF,CAAC,CACF;MACDC,OAAO,EAAE;MACP;MACA,6RAA6R;MAAE;MAC/R;MACA,8DAA8D,CAC/D;MACD;MACA;MACA;MACAC,QAAQ,EAAE,iCAAiC;MAC3CC,KAAK,EAAE;QACLd,OAAO,EAAE,QAAQ;QACjBO,KAAK,EAAE;MACT,CAAC;MACDQ,QAAQ,EAAE,qBAAqB;MAC/B,YAAY,EAAE,cAAc;MAC5BC,SAAS,EAAE;QACThB,OAAO,EAAE,4DAA4D;QACrEQ,MAAM,EAAE;UACNG,WAAW,EAAE;QACf;MACF,CAAC;MACD;MACAM,MAAM,EACJ,4KAA4K;MAC9KC,OAAO,EAAE,oBAAoB;MAC7BP,WAAW,EAAE,kCAAkC;MAC/CQ,QAAQ,EAAE;IACZ,CAAC;IACD1B,KAAK,CAACK,SAAS,CAACR,IAAI,CAAC,gBAAgB,CAAC,CAACkB,MAAM,CAACC,IAAI,GAAGhB,KAAK,CAACK,SAAS,CAACR,IAAI;IACzEG,KAAK,CAACK,SAAS,CAACR,IAAI,CAAC,WAAW,CAAC,CAACkB,MAAM,CAAC,QAAQ,CAAC,GAChDf,KAAK,CAACK,SAAS,CAACR,IAAI,CAAC,QAAQ,CAAC;EAClC,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}