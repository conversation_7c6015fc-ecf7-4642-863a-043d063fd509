{"ast": null, "code": "'use strict';\n\nmodule.exports = nand2tetrisHdl;\nnand2tetrisHdl.displayName = 'nand2tetrisHdl';\nnand2tetrisHdl.aliases = [];\nfunction nand2tetrisHdl(Prism) {\n  Prism.languages['nand2tetris-hdl'] = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    keyword: /\\b(?:BUILTIN|CHIP|CLOCKED|IN|OUT|PARTS)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b[A-Za-z][A-Za-z0-9]*(?=\\()/,\n    number: /\\b\\d+\\b/,\n    operator: /=|\\.\\./,\n    punctuation: /[{}[\\];(),:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "nand2tetrisHdl", "displayName", "aliases", "Prism", "languages", "comment", "keyword", "boolean", "function", "number", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/nand2tetris-hdl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nand2tetrisHdl\nnand2tetrisHdl.displayName = 'nand2tetrisHdl'\nnand2tetrisHdl.aliases = []\nfunction nand2tetrisHdl(Prism) {\n  Prism.languages['nand2tetris-hdl'] = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    keyword: /\\b(?:BUILTIN|CHIP|CLOCKED|IN|OUT|PARTS)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b[A-Za-z][A-Za-z0-9]*(?=\\()/,\n    number: /\\b\\d+\\b/,\n    operator: /=|\\.\\./,\n    punctuation: /[{}[\\];(),:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,cAAc;AAC/BA,cAAc,CAACC,WAAW,GAAG,gBAAgB;AAC7CD,cAAc,CAACE,OAAO,GAAG,EAAE;AAC3B,SAASF,cAAcA,CAACG,KAAK,EAAE;EAC7BA,KAAK,CAACC,SAAS,CAAC,iBAAiB,CAAC,GAAG;IACnCC,OAAO,EAAE,+BAA+B;IACxCC,OAAO,EAAE,2CAA2C;IACpDC,OAAO,EAAE,oBAAoB;IAC7BC,QAAQ,EAAE,8BAA8B;IACxCC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}