{"ast": null, "code": "/*\nLanguage: ISBL\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: built-in language DIRECTUM\nCategory: enterprise\n*/\n\nfunction isbl(hljs) {\n  // Определение идентификаторов\n  const UNDERSCORE_IDENT_RE = \"[A-Za-zА-Яа-яёЁ_!][A-Za-zА-Яа-яёЁ_0-9]*\";\n\n  // Определение имен функций\n  const FUNCTION_NAME_IDENT_RE = \"[A-Za-zА-Яа-яёЁ_][A-Za-zА-Яа-яёЁ_0-9]*\";\n\n  // keyword : ключевые слова\n  const KEYWORD = \"and и else иначе endexcept endfinally endforeach конецвсе endif конецесли endwhile конецпока \" + \"except exitfor finally foreach все if если in в not не or или try while пока \";\n\n  // SYSRES Constants\n  const sysres_constants = \"SYSRES_CONST_ACCES_RIGHT_TYPE_EDIT \" + \"SYSRES_CONST_ACCES_RIGHT_TYPE_FULL \" + \"SYSRES_CONST_ACCES_RIGHT_TYPE_VIEW \" + \"SYSRES_CONST_ACCESS_MODE_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_NO_ACCESS_VIEW \" + \"SYSRES_CONST_ACCESS_NO_ACCESS_VIEW_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_ADD_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_ADD_REQUISITE_YES_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_CHANGE_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_CHANGE_REQUISITE_YES_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_DELETE_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_DELETE_REQUISITE_YES_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_EXECUTE_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_EXECUTE_REQUISITE_YES_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_NO_ACCESS_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_NO_ACCESS_REQUISITE_YES_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_RATIFY_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_RATIFY_REQUISITE_YES_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_VIEW \" + \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_REQUISITE_CODE \" + \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_REQUISITE_YES_CODE \" + \"SYSRES_CONST_ACCESS_TYPE_CHANGE \" + \"SYSRES_CONST_ACCESS_TYPE_CHANGE_CODE \" + \"SYSRES_CONST_ACCESS_TYPE_EXISTS \" + \"SYSRES_CONST_ACCESS_TYPE_EXISTS_CODE \" + \"SYSRES_CONST_ACCESS_TYPE_FULL \" + \"SYSRES_CONST_ACCESS_TYPE_FULL_CODE \" + \"SYSRES_CONST_ACCESS_TYPE_VIEW \" + \"SYSRES_CONST_ACCESS_TYPE_VIEW_CODE \" + \"SYSRES_CONST_ACTION_TYPE_ABORT \" + \"SYSRES_CONST_ACTION_TYPE_ACCEPT \" + \"SYSRES_CONST_ACTION_TYPE_ACCESS_RIGHTS \" + \"SYSRES_CONST_ACTION_TYPE_ADD_ATTACHMENT \" + \"SYSRES_CONST_ACTION_TYPE_CHANGE_CARD \" + \"SYSRES_CONST_ACTION_TYPE_CHANGE_KIND \" + \"SYSRES_CONST_ACTION_TYPE_CHANGE_STORAGE \" + \"SYSRES_CONST_ACTION_TYPE_CONTINUE \" + \"SYSRES_CONST_ACTION_TYPE_COPY \" + \"SYSRES_CONST_ACTION_TYPE_CREATE \" + \"SYSRES_CONST_ACTION_TYPE_CREATE_VERSION \" + \"SYSRES_CONST_ACTION_TYPE_DELETE \" + \"SYSRES_CONST_ACTION_TYPE_DELETE_ATTACHMENT \" + \"SYSRES_CONST_ACTION_TYPE_DELETE_VERSION \" + \"SYSRES_CONST_ACTION_TYPE_DISABLE_DELEGATE_ACCESS_RIGHTS \" + \"SYSRES_CONST_ACTION_TYPE_ENABLE_DELEGATE_ACCESS_RIGHTS \" + \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_CERTIFICATE \" + \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_CERTIFICATE_AND_PASSWORD \" + \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_PASSWORD \" + \"SYSRES_CONST_ACTION_TYPE_EXPORT_WITH_LOCK \" + \"SYSRES_CONST_ACTION_TYPE_EXPORT_WITHOUT_LOCK \" + \"SYSRES_CONST_ACTION_TYPE_IMPORT_WITH_UNLOCK \" + \"SYSRES_CONST_ACTION_TYPE_IMPORT_WITHOUT_UNLOCK \" + \"SYSRES_CONST_ACTION_TYPE_LIFE_CYCLE_STAGE \" + \"SYSRES_CONST_ACTION_TYPE_LOCK \" + \"SYSRES_CONST_ACTION_TYPE_LOCK_FOR_SERVER \" + \"SYSRES_CONST_ACTION_TYPE_LOCK_MODIFY \" + \"SYSRES_CONST_ACTION_TYPE_MARK_AS_READED \" + \"SYSRES_CONST_ACTION_TYPE_MARK_AS_UNREADED \" + \"SYSRES_CONST_ACTION_TYPE_MODIFY \" + \"SYSRES_CONST_ACTION_TYPE_MODIFY_CARD \" + \"SYSRES_CONST_ACTION_TYPE_MOVE_TO_ARCHIVE \" + \"SYSRES_CONST_ACTION_TYPE_OFF_ENCRYPTION \" + \"SYSRES_CONST_ACTION_TYPE_PASSWORD_CHANGE \" + \"SYSRES_CONST_ACTION_TYPE_PERFORM \" + \"SYSRES_CONST_ACTION_TYPE_RECOVER_FROM_LOCAL_COPY \" + \"SYSRES_CONST_ACTION_TYPE_RESTART \" + \"SYSRES_CONST_ACTION_TYPE_RESTORE_FROM_ARCHIVE \" + \"SYSRES_CONST_ACTION_TYPE_REVISION \" + \"SYSRES_CONST_ACTION_TYPE_SEND_BY_MAIL \" + \"SYSRES_CONST_ACTION_TYPE_SIGN \" + \"SYSRES_CONST_ACTION_TYPE_START \" + \"SYSRES_CONST_ACTION_TYPE_UNLOCK \" + \"SYSRES_CONST_ACTION_TYPE_UNLOCK_FROM_SERVER \" + \"SYSRES_CONST_ACTION_TYPE_VERSION_STATE \" + \"SYSRES_CONST_ACTION_TYPE_VERSION_VISIBILITY \" + \"SYSRES_CONST_ACTION_TYPE_VIEW \" + \"SYSRES_CONST_ACTION_TYPE_VIEW_SHADOW_COPY \" + \"SYSRES_CONST_ACTION_TYPE_WORKFLOW_DESCRIPTION_MODIFY \" + \"SYSRES_CONST_ACTION_TYPE_WRITE_HISTORY \" + \"SYSRES_CONST_ACTIVE_VERSION_STATE_PICK_VALUE \" + \"SYSRES_CONST_ADD_REFERENCE_MODE_NAME \" + \"SYSRES_CONST_ADDITION_REQUISITE_CODE \" + \"SYSRES_CONST_ADDITIONAL_PARAMS_REQUISITE_CODE \" + \"SYSRES_CONST_ADITIONAL_JOB_END_DATE_REQUISITE_NAME \" + \"SYSRES_CONST_ADITIONAL_JOB_READ_REQUISITE_NAME \" + \"SYSRES_CONST_ADITIONAL_JOB_START_DATE_REQUISITE_NAME \" + \"SYSRES_CONST_ADITIONAL_JOB_STATE_REQUISITE_NAME \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_ADDING_USER_TO_GROUP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_ADDING_USER_TO_GROUP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_COMP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_COMP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_GROUP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_GROUP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_USER_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_USER_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_CREATION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_CREATION_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_DELETION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_DELETION_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_COMP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_COMP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_GROUP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_GROUP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_FROM_GROUP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_FROM_GROUP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_RESTRICTION_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_RESTRICTION_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_PRIVILEGE_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_PRIVILEGE_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_RIGHTS_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_RIGHTS_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_MAIN_SERVER_CHANGED_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_MAIN_SERVER_CHANGED_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_PUBLIC_CHANGED_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_PUBLIC_CHANGED_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_RESTRICTION_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_RESTRICTION_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_PRIVILEGE_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_PRIVILEGE_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_RIGHTS_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_RIGHTS_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_CREATION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_CREATION_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_DELETION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_DELETION_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_CATEGORY_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_CATEGORY_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_COMP_TITLE_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_COMP_TITLE_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_FULL_NAME_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_FULL_NAME_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_GROUP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_GROUP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_PARENT_GROUP_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_PARENT_GROUP_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_AUTH_TYPE_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_AUTH_TYPE_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_LOGIN_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_LOGIN_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_STATUS_ACTION \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_STATUS_ACTION_CODE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_USER_PASSWORD_CHANGE \" + \"SYSRES_CONST_ADMINISTRATION_HISTORY_USER_PASSWORD_CHANGE_ACTION \" + \"SYSRES_CONST_ALL_ACCEPT_CONDITION_RUS \" + \"SYSRES_CONST_ALL_USERS_GROUP \" + \"SYSRES_CONST_ALL_USERS_GROUP_NAME \" + \"SYSRES_CONST_ALL_USERS_SERVER_GROUP_NAME \" + \"SYSRES_CONST_ALLOWED_ACCESS_TYPE_CODE \" + \"SYSRES_CONST_ALLOWED_ACCESS_TYPE_NAME \" + \"SYSRES_CONST_APP_VIEWER_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_APPROVING_SIGNATURE_NAME \" + \"SYSRES_CONST_APPROVING_SIGNATURE_REQUISITE_CODE \" + \"SYSRES_CONST_ASSISTANT_SUBSTITUE_TYPE \" + \"SYSRES_CONST_ASSISTANT_SUBSTITUE_TYPE_CODE \" + \"SYSRES_CONST_ATTACH_TYPE_COMPONENT_TOKEN \" + \"SYSRES_CONST_ATTACH_TYPE_DOC \" + \"SYSRES_CONST_ATTACH_TYPE_EDOC \" + \"SYSRES_CONST_ATTACH_TYPE_FOLDER \" + \"SYSRES_CONST_ATTACH_TYPE_JOB \" + \"SYSRES_CONST_ATTACH_TYPE_REFERENCE \" + \"SYSRES_CONST_ATTACH_TYPE_TASK \" + \"SYSRES_CONST_AUTH_ENCODED_PASSWORD \" + \"SYSRES_CONST_AUTH_ENCODED_PASSWORD_CODE \" + \"SYSRES_CONST_AUTH_NOVELL \" + \"SYSRES_CONST_AUTH_PASSWORD \" + \"SYSRES_CONST_AUTH_PASSWORD_CODE \" + \"SYSRES_CONST_AUTH_WINDOWS \" + \"SYSRES_CONST_AUTHENTICATING_SIGNATURE_NAME \" + \"SYSRES_CONST_AUTHENTICATING_SIGNATURE_REQUISITE_CODE \" + \"SYSRES_CONST_AUTO_ENUM_METHOD_FLAG \" + \"SYSRES_CONST_AUTO_NUMERATION_CODE \" + \"SYSRES_CONST_AUTO_STRONG_ENUM_METHOD_FLAG \" + \"SYSRES_CONST_AUTOTEXT_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_AUTOTEXT_TEXT_REQUISITE_CODE \" + \"SYSRES_CONST_AUTOTEXT_USAGE_ALL \" + \"SYSRES_CONST_AUTOTEXT_USAGE_ALL_CODE \" + \"SYSRES_CONST_AUTOTEXT_USAGE_SIGN \" + \"SYSRES_CONST_AUTOTEXT_USAGE_SIGN_CODE \" + \"SYSRES_CONST_AUTOTEXT_USAGE_WORK \" + \"SYSRES_CONST_AUTOTEXT_USAGE_WORK_CODE \" + \"SYSRES_CONST_AUTOTEXT_USE_ANYWHERE_CODE \" + \"SYSRES_CONST_AUTOTEXT_USE_ON_SIGNING_CODE \" + \"SYSRES_CONST_AUTOTEXT_USE_ON_WORK_CODE \" + \"SYSRES_CONST_BEGIN_DATE_REQUISITE_CODE \" + \"SYSRES_CONST_BLACK_LIFE_CYCLE_STAGE_FONT_COLOR \" + \"SYSRES_CONST_BLUE_LIFE_CYCLE_STAGE_FONT_COLOR \" + \"SYSRES_CONST_BTN_PART \" + \"SYSRES_CONST_CALCULATED_ROLE_TYPE_CODE \" + \"SYSRES_CONST_CALL_TYPE_VARIABLE_BUTTON_VALUE \" + \"SYSRES_CONST_CALL_TYPE_VARIABLE_PROGRAM_VALUE \" + \"SYSRES_CONST_CANCEL_MESSAGE_FUNCTION_RESULT \" + \"SYSRES_CONST_CARD_PART \" + \"SYSRES_CONST_CARD_REFERENCE_MODE_NAME \" + \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_ENCRYPT_VALUE \" + \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_SIGN_AND_ENCRYPT_VALUE \" + \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_SIGN_VALUE \" + \"SYSRES_CONST_CHECK_PARAM_VALUE_DATE_PARAM_TYPE \" + \"SYSRES_CONST_CHECK_PARAM_VALUE_FLOAT_PARAM_TYPE \" + \"SYSRES_CONST_CHECK_PARAM_VALUE_INTEGER_PARAM_TYPE \" + \"SYSRES_CONST_CHECK_PARAM_VALUE_PICK_PARAM_TYPE \" + \"SYSRES_CONST_CHECK_PARAM_VALUE_REEFRENCE_PARAM_TYPE \" + \"SYSRES_CONST_CLOSED_RECORD_FLAG_VALUE_FEMININE \" + \"SYSRES_CONST_CLOSED_RECORD_FLAG_VALUE_MASCULINE \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_ADMIN \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_DEVELOPER \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_DOCS \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_EDOC_CARDS \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_EXTERNAL_EXECUTABLE \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_OTHER \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_REFERENCE \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_REPORT \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_SCRIPT \" + \"SYSRES_CONST_CODE_COMPONENT_TYPE_URL \" + \"SYSRES_CONST_CODE_REQUISITE_ACCESS \" + \"SYSRES_CONST_CODE_REQUISITE_CODE \" + \"SYSRES_CONST_CODE_REQUISITE_COMPONENT \" + \"SYSRES_CONST_CODE_REQUISITE_DESCRIPTION \" + \"SYSRES_CONST_CODE_REQUISITE_EXCLUDE_COMPONENT \" + \"SYSRES_CONST_CODE_REQUISITE_RECORD \" + \"SYSRES_CONST_COMMENT_REQ_CODE \" + \"SYSRES_CONST_COMMON_SETTINGS_REQUISITE_CODE \" + \"SYSRES_CONST_COMP_CODE_GRD \" + \"SYSRES_CONST_COMPONENT_GROUP_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_COMPONENT_TYPE_ADMIN_COMPONENTS \" + \"SYSRES_CONST_COMPONENT_TYPE_DEVELOPER_COMPONENTS \" + \"SYSRES_CONST_COMPONENT_TYPE_DOCS \" + \"SYSRES_CONST_COMPONENT_TYPE_EDOC_CARDS \" + \"SYSRES_CONST_COMPONENT_TYPE_EDOCS \" + \"SYSRES_CONST_COMPONENT_TYPE_EXTERNAL_EXECUTABLE \" + \"SYSRES_CONST_COMPONENT_TYPE_OTHER \" + \"SYSRES_CONST_COMPONENT_TYPE_REFERENCE_TYPES \" + \"SYSRES_CONST_COMPONENT_TYPE_REFERENCES \" + \"SYSRES_CONST_COMPONENT_TYPE_REPORTS \" + \"SYSRES_CONST_COMPONENT_TYPE_SCRIPTS \" + \"SYSRES_CONST_COMPONENT_TYPE_URL \" + \"SYSRES_CONST_COMPONENTS_REMOTE_SERVERS_VIEW_CODE \" + \"SYSRES_CONST_CONDITION_BLOCK_DESCRIPTION \" + \"SYSRES_CONST_CONST_FIRM_STATUS_COMMON \" + \"SYSRES_CONST_CONST_FIRM_STATUS_INDIVIDUAL \" + \"SYSRES_CONST_CONST_NEGATIVE_VALUE \" + \"SYSRES_CONST_CONST_POSITIVE_VALUE \" + \"SYSRES_CONST_CONST_SERVER_STATUS_DONT_REPLICATE \" + \"SYSRES_CONST_CONST_SERVER_STATUS_REPLICATE \" + \"SYSRES_CONST_CONTENTS_REQUISITE_CODE \" + \"SYSRES_CONST_DATA_TYPE_BOOLEAN \" + \"SYSRES_CONST_DATA_TYPE_DATE \" + \"SYSRES_CONST_DATA_TYPE_FLOAT \" + \"SYSRES_CONST_DATA_TYPE_INTEGER \" + \"SYSRES_CONST_DATA_TYPE_PICK \" + \"SYSRES_CONST_DATA_TYPE_REFERENCE \" + \"SYSRES_CONST_DATA_TYPE_STRING \" + \"SYSRES_CONST_DATA_TYPE_TEXT \" + \"SYSRES_CONST_DATA_TYPE_VARIANT \" + \"SYSRES_CONST_DATE_CLOSE_REQ_CODE \" + \"SYSRES_CONST_DATE_FORMAT_DATE_ONLY_CHAR \" + \"SYSRES_CONST_DATE_OPEN_REQ_CODE \" + \"SYSRES_CONST_DATE_REQUISITE \" + \"SYSRES_CONST_DATE_REQUISITE_CODE \" + \"SYSRES_CONST_DATE_REQUISITE_NAME \" + \"SYSRES_CONST_DATE_REQUISITE_TYPE \" + \"SYSRES_CONST_DATE_TYPE_CHAR \" + \"SYSRES_CONST_DATETIME_FORMAT_VALUE \" + \"SYSRES_CONST_DEA_ACCESS_RIGHTS_ACTION_CODE \" + \"SYSRES_CONST_DESCRIPTION_LOCALIZE_ID_REQUISITE_CODE \" + \"SYSRES_CONST_DESCRIPTION_REQUISITE_CODE \" + \"SYSRES_CONST_DET1_PART \" + \"SYSRES_CONST_DET2_PART \" + \"SYSRES_CONST_DET3_PART \" + \"SYSRES_CONST_DET4_PART \" + \"SYSRES_CONST_DET5_PART \" + \"SYSRES_CONST_DET6_PART \" + \"SYSRES_CONST_DETAIL_DATASET_KEY_REQUISITE_CODE \" + \"SYSRES_CONST_DETAIL_PICK_REQUISITE_CODE \" + \"SYSRES_CONST_DETAIL_REQ_CODE \" + \"SYSRES_CONST_DO_NOT_USE_ACCESS_TYPE_CODE \" + \"SYSRES_CONST_DO_NOT_USE_ACCESS_TYPE_NAME \" + \"SYSRES_CONST_DO_NOT_USE_ON_VIEW_ACCESS_TYPE_CODE \" + \"SYSRES_CONST_DO_NOT_USE_ON_VIEW_ACCESS_TYPE_NAME \" + \"SYSRES_CONST_DOCUMENT_STORAGES_CODE \" + \"SYSRES_CONST_DOCUMENT_TEMPLATES_TYPE_NAME \" + \"SYSRES_CONST_DOUBLE_REQUISITE_CODE \" + \"SYSRES_CONST_EDITOR_CLOSE_FILE_OBSERV_TYPE_CODE \" + \"SYSRES_CONST_EDITOR_CLOSE_PROCESS_OBSERV_TYPE_CODE \" + \"SYSRES_CONST_EDITOR_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_EDITORS_APPLICATION_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_EDITORS_CREATE_SEVERAL_PROCESSES_REQUISITE_CODE \" + \"SYSRES_CONST_EDITORS_EXTENSION_REQUISITE_CODE \" + \"SYSRES_CONST_EDITORS_OBSERVER_BY_PROCESS_TYPE \" + \"SYSRES_CONST_EDITORS_REFERENCE_CODE \" + \"SYSRES_CONST_EDITORS_REPLACE_SPEC_CHARS_REQUISITE_CODE \" + \"SYSRES_CONST_EDITORS_USE_PLUGINS_REQUISITE_CODE \" + \"SYSRES_CONST_EDITORS_VIEW_DOCUMENT_OPENED_TO_EDIT_CODE \" + \"SYSRES_CONST_EDOC_CARD_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_CARD_TYPES_LINK_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_CERTIFICATE_AND_PASSWORD_ENCODE_CODE \" + \"SYSRES_CONST_EDOC_CERTIFICATE_ENCODE_CODE \" + \"SYSRES_CONST_EDOC_DATE_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_KIND_REFERENCE_CODE \" + \"SYSRES_CONST_EDOC_KINDS_BY_TEMPLATE_ACTION_CODE \" + \"SYSRES_CONST_EDOC_MANAGE_ACCESS_CODE \" + \"SYSRES_CONST_EDOC_NONE_ENCODE_CODE \" + \"SYSRES_CONST_EDOC_NUMBER_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_PASSWORD_ENCODE_CODE \" + \"SYSRES_CONST_EDOC_READONLY_ACCESS_CODE \" + \"SYSRES_CONST_EDOC_SHELL_LIFE_TYPE_VIEW_VALUE \" + \"SYSRES_CONST_EDOC_SIZE_RESTRICTION_PRIORITY_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_STORAGE_CHECK_ACCESS_RIGHTS_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_STORAGE_COMPUTER_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_STORAGE_DATABASE_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_STORAGE_EDIT_IN_STORAGE_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_STORAGE_LOCAL_PATH_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_STORAGE_SHARED_SOURCE_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_TEMPLATE_REQUISITE_CODE \" + \"SYSRES_CONST_EDOC_TYPES_REFERENCE_CODE \" + \"SYSRES_CONST_EDOC_VERSION_ACTIVE_STAGE_CODE \" + \"SYSRES_CONST_EDOC_VERSION_DESIGN_STAGE_CODE \" + \"SYSRES_CONST_EDOC_VERSION_OBSOLETE_STAGE_CODE \" + \"SYSRES_CONST_EDOC_WRITE_ACCES_CODE \" + \"SYSRES_CONST_EDOCUMENT_CARD_REQUISITES_REFERENCE_CODE_SELECTED_REQUISITE \" + \"SYSRES_CONST_ENCODE_CERTIFICATE_TYPE_CODE \" + \"SYSRES_CONST_END_DATE_REQUISITE_CODE \" + \"SYSRES_CONST_ENUMERATION_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_EXECUTE_ACCESS_RIGHTS_TYPE_CODE \" + \"SYSRES_CONST_EXECUTIVE_FILE_STORAGE_TYPE \" + \"SYSRES_CONST_EXIST_CONST \" + \"SYSRES_CONST_EXIST_VALUE \" + \"SYSRES_CONST_EXPORT_LOCK_TYPE_ASK \" + \"SYSRES_CONST_EXPORT_LOCK_TYPE_WITH_LOCK \" + \"SYSRES_CONST_EXPORT_LOCK_TYPE_WITHOUT_LOCK \" + \"SYSRES_CONST_EXPORT_VERSION_TYPE_ASK \" + \"SYSRES_CONST_EXPORT_VERSION_TYPE_LAST \" + \"SYSRES_CONST_EXPORT_VERSION_TYPE_LAST_ACTIVE \" + \"SYSRES_CONST_EXTENSION_REQUISITE_CODE \" + \"SYSRES_CONST_FILTER_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_FILTER_REQUISITE_CODE \" + \"SYSRES_CONST_FILTER_TYPE_COMMON_CODE \" + \"SYSRES_CONST_FILTER_TYPE_COMMON_NAME \" + \"SYSRES_CONST_FILTER_TYPE_USER_CODE \" + \"SYSRES_CONST_FILTER_TYPE_USER_NAME \" + \"SYSRES_CONST_FILTER_VALUE_REQUISITE_NAME \" + \"SYSRES_CONST_FLOAT_NUMBER_FORMAT_CHAR \" + \"SYSRES_CONST_FLOAT_REQUISITE_TYPE \" + \"SYSRES_CONST_FOLDER_AUTHOR_VALUE \" + \"SYSRES_CONST_FOLDER_KIND_ANY_OBJECTS \" + \"SYSRES_CONST_FOLDER_KIND_COMPONENTS \" + \"SYSRES_CONST_FOLDER_KIND_EDOCS \" + \"SYSRES_CONST_FOLDER_KIND_JOBS \" + \"SYSRES_CONST_FOLDER_KIND_TASKS \" + \"SYSRES_CONST_FOLDER_TYPE_COMMON \" + \"SYSRES_CONST_FOLDER_TYPE_COMPONENT \" + \"SYSRES_CONST_FOLDER_TYPE_FAVORITES \" + \"SYSRES_CONST_FOLDER_TYPE_INBOX \" + \"SYSRES_CONST_FOLDER_TYPE_OUTBOX \" + \"SYSRES_CONST_FOLDER_TYPE_QUICK_LAUNCH \" + \"SYSRES_CONST_FOLDER_TYPE_SEARCH \" + \"SYSRES_CONST_FOLDER_TYPE_SHORTCUTS \" + \"SYSRES_CONST_FOLDER_TYPE_USER \" + \"SYSRES_CONST_FROM_DICTIONARY_ENUM_METHOD_FLAG \" + \"SYSRES_CONST_FULL_SUBSTITUTE_TYPE \" + \"SYSRES_CONST_FULL_SUBSTITUTE_TYPE_CODE \" + \"SYSRES_CONST_FUNCTION_CANCEL_RESULT \" + \"SYSRES_CONST_FUNCTION_CATEGORY_SYSTEM \" + \"SYSRES_CONST_FUNCTION_CATEGORY_USER \" + \"SYSRES_CONST_FUNCTION_FAILURE_RESULT \" + \"SYSRES_CONST_FUNCTION_SAVE_RESULT \" + \"SYSRES_CONST_GENERATED_REQUISITE \" + \"SYSRES_CONST_GREEN_LIFE_CYCLE_STAGE_FONT_COLOR \" + \"SYSRES_CONST_GROUP_ACCOUNT_TYPE_VALUE_CODE \" + \"SYSRES_CONST_GROUP_CATEGORY_NORMAL_CODE \" + \"SYSRES_CONST_GROUP_CATEGORY_NORMAL_NAME \" + \"SYSRES_CONST_GROUP_CATEGORY_SERVICE_CODE \" + \"SYSRES_CONST_GROUP_CATEGORY_SERVICE_NAME \" + \"SYSRES_CONST_GROUP_COMMON_CATEGORY_FIELD_VALUE \" + \"SYSRES_CONST_GROUP_FULL_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_GROUP_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_GROUP_RIGHTS_T_REQUISITE_CODE \" + \"SYSRES_CONST_GROUP_SERVER_CODES_REQUISITE_CODE \" + \"SYSRES_CONST_GROUP_SERVER_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_GROUP_SERVICE_CATEGORY_FIELD_VALUE \" + \"SYSRES_CONST_GROUP_USER_REQUISITE_CODE \" + \"SYSRES_CONST_GROUPS_REFERENCE_CODE \" + \"SYSRES_CONST_GROUPS_REQUISITE_CODE \" + \"SYSRES_CONST_HIDDEN_MODE_NAME \" + \"SYSRES_CONST_HIGH_LVL_REQUISITE_CODE \" + \"SYSRES_CONST_HISTORY_ACTION_CREATE_CODE \" + \"SYSRES_CONST_HISTORY_ACTION_DELETE_CODE \" + \"SYSRES_CONST_HISTORY_ACTION_EDIT_CODE \" + \"SYSRES_CONST_HOUR_CHAR \" + \"SYSRES_CONST_ID_REQUISITE_CODE \" + \"SYSRES_CONST_IDSPS_REQUISITE_CODE \" + \"SYSRES_CONST_IMAGE_MODE_COLOR \" + \"SYSRES_CONST_IMAGE_MODE_GREYSCALE \" + \"SYSRES_CONST_IMAGE_MODE_MONOCHROME \" + \"SYSRES_CONST_IMPORTANCE_HIGH \" + \"SYSRES_CONST_IMPORTANCE_LOW \" + \"SYSRES_CONST_IMPORTANCE_NORMAL \" + \"SYSRES_CONST_IN_DESIGN_VERSION_STATE_PICK_VALUE \" + \"SYSRES_CONST_INCOMING_WORK_RULE_TYPE_CODE \" + \"SYSRES_CONST_INT_REQUISITE \" + \"SYSRES_CONST_INT_REQUISITE_TYPE \" + \"SYSRES_CONST_INTEGER_NUMBER_FORMAT_CHAR \" + \"SYSRES_CONST_INTEGER_TYPE_CHAR \" + \"SYSRES_CONST_IS_GENERATED_REQUISITE_NEGATIVE_VALUE \" + \"SYSRES_CONST_IS_PUBLIC_ROLE_REQUISITE_CODE \" + \"SYSRES_CONST_IS_REMOTE_USER_NEGATIVE_VALUE \" + \"SYSRES_CONST_IS_REMOTE_USER_POSITIVE_VALUE \" + \"SYSRES_CONST_IS_STORED_REQUISITE_NEGATIVE_VALUE \" + \"SYSRES_CONST_IS_STORED_REQUISITE_STORED_VALUE \" + \"SYSRES_CONST_ITALIC_LIFE_CYCLE_STAGE_DRAW_STYLE \" + \"SYSRES_CONST_JOB_BLOCK_DESCRIPTION \" + \"SYSRES_CONST_JOB_KIND_CONTROL_JOB \" + \"SYSRES_CONST_JOB_KIND_JOB \" + \"SYSRES_CONST_JOB_KIND_NOTICE \" + \"SYSRES_CONST_JOB_STATE_ABORTED \" + \"SYSRES_CONST_JOB_STATE_COMPLETE \" + \"SYSRES_CONST_JOB_STATE_WORKING \" + \"SYSRES_CONST_KIND_REQUISITE_CODE \" + \"SYSRES_CONST_KIND_REQUISITE_NAME \" + \"SYSRES_CONST_KINDS_CREATE_SHADOW_COPIES_REQUISITE_CODE \" + \"SYSRES_CONST_KINDS_DEFAULT_EDOC_LIFE_STAGE_REQUISITE_CODE \" + \"SYSRES_CONST_KINDS_EDOC_ALL_TEPLATES_ALLOWED_REQUISITE_CODE \" + \"SYSRES_CONST_KINDS_EDOC_ALLOW_LIFE_CYCLE_STAGE_CHANGING_REQUISITE_CODE \" + \"SYSRES_CONST_KINDS_EDOC_ALLOW_MULTIPLE_ACTIVE_VERSIONS_REQUISITE_CODE \" + \"SYSRES_CONST_KINDS_EDOC_SHARE_ACCES_RIGHTS_BY_DEFAULT_CODE \" + \"SYSRES_CONST_KINDS_EDOC_TEMPLATE_REQUISITE_CODE \" + \"SYSRES_CONST_KINDS_EDOC_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_KINDS_SIGNERS_REQUISITES_CODE \" + \"SYSRES_CONST_KOD_INPUT_TYPE \" + \"SYSRES_CONST_LAST_UPDATE_DATE_REQUISITE_CODE \" + \"SYSRES_CONST_LIFE_CYCLE_START_STAGE_REQUISITE_CODE \" + \"SYSRES_CONST_LILAC_LIFE_CYCLE_STAGE_FONT_COLOR \" + \"SYSRES_CONST_LINK_OBJECT_KIND_COMPONENT \" + \"SYSRES_CONST_LINK_OBJECT_KIND_DOCUMENT \" + \"SYSRES_CONST_LINK_OBJECT_KIND_EDOC \" + \"SYSRES_CONST_LINK_OBJECT_KIND_FOLDER \" + \"SYSRES_CONST_LINK_OBJECT_KIND_JOB \" + \"SYSRES_CONST_LINK_OBJECT_KIND_REFERENCE \" + \"SYSRES_CONST_LINK_OBJECT_KIND_TASK \" + \"SYSRES_CONST_LINK_REF_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_LIST_REFERENCE_MODE_NAME \" + \"SYSRES_CONST_LOCALIZATION_DICTIONARY_MAIN_VIEW_CODE \" + \"SYSRES_CONST_MAIN_VIEW_CODE \" + \"SYSRES_CONST_MANUAL_ENUM_METHOD_FLAG \" + \"SYSRES_CONST_MASTER_COMP_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_MASTER_TABLE_REC_ID_REQUISITE_CODE \" + \"SYSRES_CONST_MAXIMIZED_MODE_NAME \" + \"SYSRES_CONST_ME_VALUE \" + \"SYSRES_CONST_MESSAGE_ATTENTION_CAPTION \" + \"SYSRES_CONST_MESSAGE_CONFIRMATION_CAPTION \" + \"SYSRES_CONST_MESSAGE_ERROR_CAPTION \" + \"SYSRES_CONST_MESSAGE_INFORMATION_CAPTION \" + \"SYSRES_CONST_MINIMIZED_MODE_NAME \" + \"SYSRES_CONST_MINUTE_CHAR \" + \"SYSRES_CONST_MODULE_REQUISITE_CODE \" + \"SYSRES_CONST_MONITORING_BLOCK_DESCRIPTION \" + \"SYSRES_CONST_MONTH_FORMAT_VALUE \" + \"SYSRES_CONST_NAME_LOCALIZE_ID_REQUISITE_CODE \" + \"SYSRES_CONST_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_NAME_SINGULAR_REQUISITE_CODE \" + \"SYSRES_CONST_NAMEAN_INPUT_TYPE \" + \"SYSRES_CONST_NEGATIVE_PICK_VALUE \" + \"SYSRES_CONST_NEGATIVE_VALUE \" + \"SYSRES_CONST_NO \" + \"SYSRES_CONST_NO_PICK_VALUE \" + \"SYSRES_CONST_NO_SIGNATURE_REQUISITE_CODE \" + \"SYSRES_CONST_NO_VALUE \" + \"SYSRES_CONST_NONE_ACCESS_RIGHTS_TYPE_CODE \" + \"SYSRES_CONST_NONOPERATING_RECORD_FLAG_VALUE \" + \"SYSRES_CONST_NONOPERATING_RECORD_FLAG_VALUE_MASCULINE \" + \"SYSRES_CONST_NORMAL_ACCESS_RIGHTS_TYPE_CODE \" + \"SYSRES_CONST_NORMAL_LIFE_CYCLE_STAGE_DRAW_STYLE \" + \"SYSRES_CONST_NORMAL_MODE_NAME \" + \"SYSRES_CONST_NOT_ALLOWED_ACCESS_TYPE_CODE \" + \"SYSRES_CONST_NOT_ALLOWED_ACCESS_TYPE_NAME \" + \"SYSRES_CONST_NOTE_REQUISITE_CODE \" + \"SYSRES_CONST_NOTICE_BLOCK_DESCRIPTION \" + \"SYSRES_CONST_NUM_REQUISITE \" + \"SYSRES_CONST_NUM_STR_REQUISITE_CODE \" + \"SYSRES_CONST_NUMERATION_AUTO_NOT_STRONG \" + \"SYSRES_CONST_NUMERATION_AUTO_STRONG \" + \"SYSRES_CONST_NUMERATION_FROM_DICTONARY \" + \"SYSRES_CONST_NUMERATION_MANUAL \" + \"SYSRES_CONST_NUMERIC_TYPE_CHAR \" + \"SYSRES_CONST_NUMREQ_REQUISITE_CODE \" + \"SYSRES_CONST_OBSOLETE_VERSION_STATE_PICK_VALUE \" + \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE \" + \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_CODE \" + \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_FEMININE \" + \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_MASCULINE \" + \"SYSRES_CONST_OPTIONAL_FORM_COMP_REQCODE_PREFIX \" + \"SYSRES_CONST_ORANGE_LIFE_CYCLE_STAGE_FONT_COLOR \" + \"SYSRES_CONST_ORIGINALREF_REQUISITE_CODE \" + \"SYSRES_CONST_OURFIRM_REF_CODE \" + \"SYSRES_CONST_OURFIRM_REQUISITE_CODE \" + \"SYSRES_CONST_OURFIRM_VAR \" + \"SYSRES_CONST_OUTGOING_WORK_RULE_TYPE_CODE \" + \"SYSRES_CONST_PICK_NEGATIVE_RESULT \" + \"SYSRES_CONST_PICK_POSITIVE_RESULT \" + \"SYSRES_CONST_PICK_REQUISITE \" + \"SYSRES_CONST_PICK_REQUISITE_TYPE \" + \"SYSRES_CONST_PICK_TYPE_CHAR \" + \"SYSRES_CONST_PLAN_STATUS_REQUISITE_CODE \" + \"SYSRES_CONST_PLATFORM_VERSION_COMMENT \" + \"SYSRES_CONST_PLUGINS_SETTINGS_DESCRIPTION_REQUISITE_CODE \" + \"SYSRES_CONST_POSITIVE_PICK_VALUE \" + \"SYSRES_CONST_POWER_TO_CREATE_ACTION_CODE \" + \"SYSRES_CONST_POWER_TO_SIGN_ACTION_CODE \" + \"SYSRES_CONST_PRIORITY_REQUISITE_CODE \" + \"SYSRES_CONST_QUALIFIED_TASK_TYPE \" + \"SYSRES_CONST_QUALIFIED_TASK_TYPE_CODE \" + \"SYSRES_CONST_RECSTAT_REQUISITE_CODE \" + \"SYSRES_CONST_RED_LIFE_CYCLE_STAGE_FONT_COLOR \" + \"SYSRES_CONST_REF_ID_T_REF_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_REF_REQUISITE \" + \"SYSRES_CONST_REF_REQUISITE_TYPE \" + \"SYSRES_CONST_REF_REQUISITES_REFERENCE_CODE_SELECTED_REQUISITE \" + \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_CREATE_ACTION_CODE \" + \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_DELETE_ACTION_CODE \" + \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_MODIFY_ACTION_CODE \" + \"SYSRES_CONST_REFERENCE_TYPE_CHAR \" + \"SYSRES_CONST_REFERENCE_TYPE_REQUISITE_NAME \" + \"SYSRES_CONST_REFERENCES_ADD_PARAMS_REQUISITE_CODE \" + \"SYSRES_CONST_REFERENCES_DISPLAY_REQUISITE_REQUISITE_CODE \" + \"SYSRES_CONST_REMOTE_SERVER_STATUS_WORKING \" + \"SYSRES_CONST_REMOTE_SERVER_TYPE_MAIN \" + \"SYSRES_CONST_REMOTE_SERVER_TYPE_SECONDARY \" + \"SYSRES_CONST_REMOTE_USER_FLAG_VALUE_CODE \" + \"SYSRES_CONST_REPORT_APP_EDITOR_INTERNAL \" + \"SYSRES_CONST_REPORT_BASE_REPORT_ID_REQUISITE_CODE \" + \"SYSRES_CONST_REPORT_BASE_REPORT_REQUISITE_CODE \" + \"SYSRES_CONST_REPORT_SCRIPT_REQUISITE_CODE \" + \"SYSRES_CONST_REPORT_TEMPLATE_REQUISITE_CODE \" + \"SYSRES_CONST_REPORT_VIEWER_CODE_REQUISITE_CODE \" + \"SYSRES_CONST_REQ_ALLOW_COMPONENT_DEFAULT_VALUE \" + \"SYSRES_CONST_REQ_ALLOW_RECORD_DEFAULT_VALUE \" + \"SYSRES_CONST_REQ_ALLOW_SERVER_COMPONENT_DEFAULT_VALUE \" + \"SYSRES_CONST_REQ_MODE_AVAILABLE_CODE \" + \"SYSRES_CONST_REQ_MODE_EDIT_CODE \" + \"SYSRES_CONST_REQ_MODE_HIDDEN_CODE \" + \"SYSRES_CONST_REQ_MODE_NOT_AVAILABLE_CODE \" + \"SYSRES_CONST_REQ_MODE_VIEW_CODE \" + \"SYSRES_CONST_REQ_NUMBER_REQUISITE_CODE \" + \"SYSRES_CONST_REQ_SECTION_VALUE \" + \"SYSRES_CONST_REQ_TYPE_VALUE \" + \"SYSRES_CONST_REQUISITE_FORMAT_BY_UNIT \" + \"SYSRES_CONST_REQUISITE_FORMAT_DATE_FULL \" + \"SYSRES_CONST_REQUISITE_FORMAT_DATE_TIME \" + \"SYSRES_CONST_REQUISITE_FORMAT_LEFT \" + \"SYSRES_CONST_REQUISITE_FORMAT_RIGHT \" + \"SYSRES_CONST_REQUISITE_FORMAT_WITHOUT_UNIT \" + \"SYSRES_CONST_REQUISITE_NUMBER_REQUISITE_CODE \" + \"SYSRES_CONST_REQUISITE_SECTION_ACTIONS \" + \"SYSRES_CONST_REQUISITE_SECTION_BUTTON \" + \"SYSRES_CONST_REQUISITE_SECTION_BUTTONS \" + \"SYSRES_CONST_REQUISITE_SECTION_CARD \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE10 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE11 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE12 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE13 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE14 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE15 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE16 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE17 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE18 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE19 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE2 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE20 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE21 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE22 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE23 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE24 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE3 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE4 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE5 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE6 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE7 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE8 \" + \"SYSRES_CONST_REQUISITE_SECTION_TABLE9 \" + \"SYSRES_CONST_REQUISITES_PSEUDOREFERENCE_REQUISITE_NUMBER_REQUISITE_CODE \" + \"SYSRES_CONST_RIGHT_ALIGNMENT_CODE \" + \"SYSRES_CONST_ROLES_REFERENCE_CODE \" + \"SYSRES_CONST_ROUTE_STEP_AFTER_RUS \" + \"SYSRES_CONST_ROUTE_STEP_AND_CONDITION_RUS \" + \"SYSRES_CONST_ROUTE_STEP_OR_CONDITION_RUS \" + \"SYSRES_CONST_ROUTE_TYPE_COMPLEX \" + \"SYSRES_CONST_ROUTE_TYPE_PARALLEL \" + \"SYSRES_CONST_ROUTE_TYPE_SERIAL \" + \"SYSRES_CONST_SBDATASETDESC_NEGATIVE_VALUE \" + \"SYSRES_CONST_SBDATASETDESC_POSITIVE_VALUE \" + \"SYSRES_CONST_SBVIEWSDESC_POSITIVE_VALUE \" + \"SYSRES_CONST_SCRIPT_BLOCK_DESCRIPTION \" + \"SYSRES_CONST_SEARCH_BY_TEXT_REQUISITE_CODE \" + \"SYSRES_CONST_SEARCHES_COMPONENT_CONTENT \" + \"SYSRES_CONST_SEARCHES_CRITERIA_ACTION_NAME \" + \"SYSRES_CONST_SEARCHES_EDOC_CONTENT \" + \"SYSRES_CONST_SEARCHES_FOLDER_CONTENT \" + \"SYSRES_CONST_SEARCHES_JOB_CONTENT \" + \"SYSRES_CONST_SEARCHES_REFERENCE_CODE \" + \"SYSRES_CONST_SEARCHES_TASK_CONTENT \" + \"SYSRES_CONST_SECOND_CHAR \" + \"SYSRES_CONST_SECTION_REQUISITE_ACTIONS_VALUE \" + \"SYSRES_CONST_SECTION_REQUISITE_CARD_VALUE \" + \"SYSRES_CONST_SECTION_REQUISITE_CODE \" + \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_1_VALUE \" + \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_2_VALUE \" + \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_3_VALUE \" + \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_4_VALUE \" + \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_5_VALUE \" + \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_6_VALUE \" + \"SYSRES_CONST_SELECT_REFERENCE_MODE_NAME \" + \"SYSRES_CONST_SELECT_TYPE_SELECTABLE \" + \"SYSRES_CONST_SELECT_TYPE_SELECTABLE_ONLY_CHILD \" + \"SYSRES_CONST_SELECT_TYPE_SELECTABLE_WITH_CHILD \" + \"SYSRES_CONST_SELECT_TYPE_UNSLECTABLE \" + \"SYSRES_CONST_SERVER_TYPE_MAIN \" + \"SYSRES_CONST_SERVICE_USER_CATEGORY_FIELD_VALUE \" + \"SYSRES_CONST_SETTINGS_USER_REQUISITE_CODE \" + \"SYSRES_CONST_SIGNATURE_AND_ENCODE_CERTIFICATE_TYPE_CODE \" + \"SYSRES_CONST_SIGNATURE_CERTIFICATE_TYPE_CODE \" + \"SYSRES_CONST_SINGULAR_TITLE_REQUISITE_CODE \" + \"SYSRES_CONST_SQL_SERVER_AUTHENTIFICATION_FLAG_VALUE_CODE \" + \"SYSRES_CONST_SQL_SERVER_ENCODE_AUTHENTIFICATION_FLAG_VALUE_CODE \" + \"SYSRES_CONST_STANDART_ROUTE_REFERENCE_CODE \" + \"SYSRES_CONST_STANDART_ROUTE_REFERENCE_COMMENT_REQUISITE_CODE \" + \"SYSRES_CONST_STANDART_ROUTES_GROUPS_REFERENCE_CODE \" + \"SYSRES_CONST_STATE_REQ_NAME \" + \"SYSRES_CONST_STATE_REQUISITE_ACTIVE_VALUE \" + \"SYSRES_CONST_STATE_REQUISITE_CLOSED_VALUE \" + \"SYSRES_CONST_STATE_REQUISITE_CODE \" + \"SYSRES_CONST_STATIC_ROLE_TYPE_CODE \" + \"SYSRES_CONST_STATUS_PLAN_DEFAULT_VALUE \" + \"SYSRES_CONST_STATUS_VALUE_AUTOCLEANING \" + \"SYSRES_CONST_STATUS_VALUE_BLUE_SQUARE \" + \"SYSRES_CONST_STATUS_VALUE_COMPLETE \" + \"SYSRES_CONST_STATUS_VALUE_GREEN_SQUARE \" + \"SYSRES_CONST_STATUS_VALUE_ORANGE_SQUARE \" + \"SYSRES_CONST_STATUS_VALUE_PURPLE_SQUARE \" + \"SYSRES_CONST_STATUS_VALUE_RED_SQUARE \" + \"SYSRES_CONST_STATUS_VALUE_SUSPEND \" + \"SYSRES_CONST_STATUS_VALUE_YELLOW_SQUARE \" + \"SYSRES_CONST_STDROUTE_SHOW_TO_USERS_REQUISITE_CODE \" + \"SYSRES_CONST_STORAGE_TYPE_FILE \" + \"SYSRES_CONST_STORAGE_TYPE_SQL_SERVER \" + \"SYSRES_CONST_STR_REQUISITE \" + \"SYSRES_CONST_STRIKEOUT_LIFE_CYCLE_STAGE_DRAW_STYLE \" + \"SYSRES_CONST_STRING_FORMAT_LEFT_ALIGN_CHAR \" + \"SYSRES_CONST_STRING_FORMAT_RIGHT_ALIGN_CHAR \" + \"SYSRES_CONST_STRING_REQUISITE_CODE \" + \"SYSRES_CONST_STRING_REQUISITE_TYPE \" + \"SYSRES_CONST_STRING_TYPE_CHAR \" + \"SYSRES_CONST_SUBSTITUTES_PSEUDOREFERENCE_CODE \" + \"SYSRES_CONST_SUBTASK_BLOCK_DESCRIPTION \" + \"SYSRES_CONST_SYSTEM_SETTING_CURRENT_USER_PARAM_VALUE \" + \"SYSRES_CONST_SYSTEM_SETTING_EMPTY_VALUE_PARAM_VALUE \" + \"SYSRES_CONST_SYSTEM_VERSION_COMMENT \" + \"SYSRES_CONST_TASK_ACCESS_TYPE_ALL \" + \"SYSRES_CONST_TASK_ACCESS_TYPE_ALL_MEMBERS \" + \"SYSRES_CONST_TASK_ACCESS_TYPE_MANUAL \" + \"SYSRES_CONST_TASK_ENCODE_TYPE_CERTIFICATION \" + \"SYSRES_CONST_TASK_ENCODE_TYPE_CERTIFICATION_AND_PASSWORD \" + \"SYSRES_CONST_TASK_ENCODE_TYPE_NONE \" + \"SYSRES_CONST_TASK_ENCODE_TYPE_PASSWORD \" + \"SYSRES_CONST_TASK_ROUTE_ALL_CONDITION \" + \"SYSRES_CONST_TASK_ROUTE_AND_CONDITION \" + \"SYSRES_CONST_TASK_ROUTE_OR_CONDITION \" + \"SYSRES_CONST_TASK_STATE_ABORTED \" + \"SYSRES_CONST_TASK_STATE_COMPLETE \" + \"SYSRES_CONST_TASK_STATE_CONTINUED \" + \"SYSRES_CONST_TASK_STATE_CONTROL \" + \"SYSRES_CONST_TASK_STATE_INIT \" + \"SYSRES_CONST_TASK_STATE_WORKING \" + \"SYSRES_CONST_TASK_TITLE \" + \"SYSRES_CONST_TASK_TYPES_GROUPS_REFERENCE_CODE \" + \"SYSRES_CONST_TASK_TYPES_REFERENCE_CODE \" + \"SYSRES_CONST_TEMPLATES_REFERENCE_CODE \" + \"SYSRES_CONST_TEST_DATE_REQUISITE_NAME \" + \"SYSRES_CONST_TEST_DEV_DATABASE_NAME \" + \"SYSRES_CONST_TEST_DEV_SYSTEM_CODE \" + \"SYSRES_CONST_TEST_EDMS_DATABASE_NAME \" + \"SYSRES_CONST_TEST_EDMS_MAIN_CODE \" + \"SYSRES_CONST_TEST_EDMS_MAIN_DB_NAME \" + \"SYSRES_CONST_TEST_EDMS_SECOND_CODE \" + \"SYSRES_CONST_TEST_EDMS_SECOND_DB_NAME \" + \"SYSRES_CONST_TEST_EDMS_SYSTEM_CODE \" + \"SYSRES_CONST_TEST_NUMERIC_REQUISITE_NAME \" + \"SYSRES_CONST_TEXT_REQUISITE \" + \"SYSRES_CONST_TEXT_REQUISITE_CODE \" + \"SYSRES_CONST_TEXT_REQUISITE_TYPE \" + \"SYSRES_CONST_TEXT_TYPE_CHAR \" + \"SYSRES_CONST_TYPE_CODE_REQUISITE_CODE \" + \"SYSRES_CONST_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_UNDEFINED_LIFE_CYCLE_STAGE_FONT_COLOR \" + \"SYSRES_CONST_UNITS_SECTION_ID_REQUISITE_CODE \" + \"SYSRES_CONST_UNITS_SECTION_REQUISITE_CODE \" + \"SYSRES_CONST_UNOPERATING_RECORD_FLAG_VALUE_CODE \" + \"SYSRES_CONST_UNSTORED_DATA_REQUISITE_CODE \" + \"SYSRES_CONST_UNSTORED_DATA_REQUISITE_NAME \" + \"SYSRES_CONST_USE_ACCESS_TYPE_CODE \" + \"SYSRES_CONST_USE_ACCESS_TYPE_NAME \" + \"SYSRES_CONST_USER_ACCOUNT_TYPE_VALUE_CODE \" + \"SYSRES_CONST_USER_ADDITIONAL_INFORMATION_REQUISITE_CODE \" + \"SYSRES_CONST_USER_AND_GROUP_ID_FROM_PSEUDOREFERENCE_REQUISITE_CODE \" + \"SYSRES_CONST_USER_CATEGORY_NORMAL \" + \"SYSRES_CONST_USER_CERTIFICATE_REQUISITE_CODE \" + \"SYSRES_CONST_USER_CERTIFICATE_STATE_REQUISITE_CODE \" + \"SYSRES_CONST_USER_CERTIFICATE_SUBJECT_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_USER_CERTIFICATE_THUMBPRINT_REQUISITE_CODE \" + \"SYSRES_CONST_USER_COMMON_CATEGORY \" + \"SYSRES_CONST_USER_COMMON_CATEGORY_CODE \" + \"SYSRES_CONST_USER_FULL_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_USER_GROUP_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_USER_LOGIN_REQUISITE_CODE \" + \"SYSRES_CONST_USER_REMOTE_CONTROLLER_REQUISITE_CODE \" + \"SYSRES_CONST_USER_REMOTE_SYSTEM_REQUISITE_CODE \" + \"SYSRES_CONST_USER_RIGHTS_T_REQUISITE_CODE \" + \"SYSRES_CONST_USER_SERVER_NAME_REQUISITE_CODE \" + \"SYSRES_CONST_USER_SERVICE_CATEGORY \" + \"SYSRES_CONST_USER_SERVICE_CATEGORY_CODE \" + \"SYSRES_CONST_USER_STATUS_ADMINISTRATOR_CODE \" + \"SYSRES_CONST_USER_STATUS_ADMINISTRATOR_NAME \" + \"SYSRES_CONST_USER_STATUS_DEVELOPER_CODE \" + \"SYSRES_CONST_USER_STATUS_DEVELOPER_NAME \" + \"SYSRES_CONST_USER_STATUS_DISABLED_CODE \" + \"SYSRES_CONST_USER_STATUS_DISABLED_NAME \" + \"SYSRES_CONST_USER_STATUS_SYSTEM_DEVELOPER_CODE \" + \"SYSRES_CONST_USER_STATUS_USER_CODE \" + \"SYSRES_CONST_USER_STATUS_USER_NAME \" + \"SYSRES_CONST_USER_STATUS_USER_NAME_DEPRECATED \" + \"SYSRES_CONST_USER_TYPE_FIELD_VALUE_USER \" + \"SYSRES_CONST_USER_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_CONTROLLER_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_IS_MAIN_SERVER_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_REFERENCE_CODE \" + \"SYSRES_CONST_USERS_REGISTRATION_CERTIFICATES_ACTION_NAME \" + \"SYSRES_CONST_USERS_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_SYSTEM_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_USER_ACCESS_RIGHTS_TYPR_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_USER_AUTHENTICATION_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_USER_COMPONENT_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_USER_GROUP_REQUISITE_CODE \" + \"SYSRES_CONST_USERS_VIEW_CERTIFICATES_ACTION_NAME \" + \"SYSRES_CONST_VIEW_DEFAULT_CODE \" + \"SYSRES_CONST_VIEW_DEFAULT_NAME \" + \"SYSRES_CONST_VIEWER_REQUISITE_CODE \" + \"SYSRES_CONST_WAITING_BLOCK_DESCRIPTION \" + \"SYSRES_CONST_WIZARD_FORM_LABEL_TEST_STRING  \" + \"SYSRES_CONST_WIZARD_QUERY_PARAM_HEIGHT_ETALON_STRING \" + \"SYSRES_CONST_WIZARD_REFERENCE_COMMENT_REQUISITE_CODE \" + \"SYSRES_CONST_WORK_RULES_DESCRIPTION_REQUISITE_CODE \" + \"SYSRES_CONST_WORK_TIME_CALENDAR_REFERENCE_CODE \" + \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE \" + \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE_CODE \" + \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE_CODE_RUS \" + \"SYSRES_CONST_WORK_WORKFLOW_SOFT_ROUTE_TYPE_VALUE_CODE_RUS \" + \"SYSRES_CONST_WORKFLOW_ROUTE_TYPR_HARD \" + \"SYSRES_CONST_WORKFLOW_ROUTE_TYPR_SOFT \" + \"SYSRES_CONST_XML_ENCODING \" + \"SYSRES_CONST_XREC_STAT_REQUISITE_CODE \" + \"SYSRES_CONST_XRECID_FIELD_NAME \" + \"SYSRES_CONST_YES \" + \"SYSRES_CONST_YES_NO_2_REQUISITE_CODE \" + \"SYSRES_CONST_YES_NO_REQUISITE_CODE \" + \"SYSRES_CONST_YES_NO_T_REF_TYPE_REQUISITE_CODE \" + \"SYSRES_CONST_YES_PICK_VALUE \" + \"SYSRES_CONST_YES_VALUE \";\n\n  // Base constant\n  const base_constants = \"CR FALSE nil NO_VALUE NULL TAB TRUE YES_VALUE \";\n\n  // Base group name\n  const base_group_name_constants = \"ADMINISTRATORS_GROUP_NAME CUSTOMIZERS_GROUP_NAME DEVELOPERS_GROUP_NAME SERVICE_USERS_GROUP_NAME \";\n\n  // Decision block properties\n  const decision_block_properties_constants = \"DECISION_BLOCK_FIRST_OPERAND_PROPERTY DECISION_BLOCK_NAME_PROPERTY DECISION_BLOCK_OPERATION_PROPERTY \" + \"DECISION_BLOCK_RESULT_TYPE_PROPERTY DECISION_BLOCK_SECOND_OPERAND_PROPERTY \";\n\n  // File extension\n  const file_extension_constants = \"ANY_FILE_EXTENTION COMPRESSED_DOCUMENT_EXTENSION EXTENDED_DOCUMENT_EXTENSION \" + \"SHORT_COMPRESSED_DOCUMENT_EXTENSION SHORT_EXTENDED_DOCUMENT_EXTENSION \";\n\n  // Job block properties\n  const job_block_properties_constants = \"JOB_BLOCK_ABORT_DEADLINE_PROPERTY \" + \"JOB_BLOCK_AFTER_FINISH_EVENT \" + \"JOB_BLOCK_AFTER_QUERY_PARAMETERS_EVENT \" + \"JOB_BLOCK_ATTACHMENT_PROPERTY \" + \"JOB_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" + \"JOB_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" + \"JOB_BLOCK_BEFORE_QUERY_PARAMETERS_EVENT \" + \"JOB_BLOCK_BEFORE_START_EVENT \" + \"JOB_BLOCK_CREATED_JOBS_PROPERTY \" + \"JOB_BLOCK_DEADLINE_PROPERTY \" + \"JOB_BLOCK_EXECUTION_RESULTS_PROPERTY \" + \"JOB_BLOCK_IS_PARALLEL_PROPERTY \" + \"JOB_BLOCK_IS_RELATIVE_ABORT_DEADLINE_PROPERTY \" + \"JOB_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" + \"JOB_BLOCK_JOB_TEXT_PROPERTY \" + \"JOB_BLOCK_NAME_PROPERTY \" + \"JOB_BLOCK_NEED_SIGN_ON_PERFORM_PROPERTY \" + \"JOB_BLOCK_PERFORMER_PROPERTY \" + \"JOB_BLOCK_RELATIVE_ABORT_DEADLINE_TYPE_PROPERTY \" + \"JOB_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" + \"JOB_BLOCK_SUBJECT_PROPERTY \";\n\n  // Language code\n  const language_code_constants = \"ENGLISH_LANGUAGE_CODE RUSSIAN_LANGUAGE_CODE \";\n\n  // Launching external applications\n  const launching_external_applications_constants = \"smHidden smMaximized smMinimized smNormal wmNo wmYes \";\n\n  // Link kind\n  const link_kind_constants = \"COMPONENT_TOKEN_LINK_KIND \" + \"DOCUMENT_LINK_KIND \" + \"EDOCUMENT_LINK_KIND \" + \"FOLDER_LINK_KIND \" + \"JOB_LINK_KIND \" + \"REFERENCE_LINK_KIND \" + \"TASK_LINK_KIND \";\n\n  // Lock type\n  const lock_type_constants = \"COMPONENT_TOKEN_LOCK_TYPE EDOCUMENT_VERSION_LOCK_TYPE \";\n\n  // Monitor block properties\n  const monitor_block_properties_constants = \"MONITOR_BLOCK_AFTER_FINISH_EVENT \" + \"MONITOR_BLOCK_BEFORE_START_EVENT \" + \"MONITOR_BLOCK_DEADLINE_PROPERTY \" + \"MONITOR_BLOCK_INTERVAL_PROPERTY \" + \"MONITOR_BLOCK_INTERVAL_TYPE_PROPERTY \" + \"MONITOR_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" + \"MONITOR_BLOCK_NAME_PROPERTY \" + \"MONITOR_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" + \"MONITOR_BLOCK_SEARCH_SCRIPT_PROPERTY \";\n\n  // Notice block properties\n  const notice_block_properties_constants = \"NOTICE_BLOCK_AFTER_FINISH_EVENT \" + \"NOTICE_BLOCK_ATTACHMENT_PROPERTY \" + \"NOTICE_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" + \"NOTICE_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" + \"NOTICE_BLOCK_BEFORE_START_EVENT \" + \"NOTICE_BLOCK_CREATED_NOTICES_PROPERTY \" + \"NOTICE_BLOCK_DEADLINE_PROPERTY \" + \"NOTICE_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" + \"NOTICE_BLOCK_NAME_PROPERTY \" + \"NOTICE_BLOCK_NOTICE_TEXT_PROPERTY \" + \"NOTICE_BLOCK_PERFORMER_PROPERTY \" + \"NOTICE_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" + \"NOTICE_BLOCK_SUBJECT_PROPERTY \";\n\n  // Object events\n  const object_events_constants = \"dseAfterCancel \" + \"dseAfterClose \" + \"dseAfterDelete \" + \"dseAfterDeleteOutOfTransaction \" + \"dseAfterInsert \" + \"dseAfterOpen \" + \"dseAfterScroll \" + \"dseAfterUpdate \" + \"dseAfterUpdateOutOfTransaction \" + \"dseBeforeCancel \" + \"dseBeforeClose \" + \"dseBeforeDelete \" + \"dseBeforeDetailUpdate \" + \"dseBeforeInsert \" + \"dseBeforeOpen \" + \"dseBeforeUpdate \" + \"dseOnAnyRequisiteChange \" + \"dseOnCloseRecord \" + \"dseOnDeleteError \" + \"dseOnOpenRecord \" + \"dseOnPrepareUpdate \" + \"dseOnUpdateError \" + \"dseOnUpdateRatifiedRecord \" + \"dseOnValidDelete \" + \"dseOnValidUpdate \" + \"reOnChange \" + \"reOnChangeValues \" + \"SELECTION_BEGIN_ROUTE_EVENT \" + \"SELECTION_END_ROUTE_EVENT \";\n\n  // Object params\n  const object_params_constants = \"CURRENT_PERIOD_IS_REQUIRED \" + \"PREVIOUS_CARD_TYPE_NAME \" + \"SHOW_RECORD_PROPERTIES_FORM \";\n\n  // Other\n  const other_constants = \"ACCESS_RIGHTS_SETTING_DIALOG_CODE \" + \"ADMINISTRATOR_USER_CODE \" + \"ANALYTIC_REPORT_TYPE \" + \"asrtHideLocal \" + \"asrtHideRemote \" + \"CALCULATED_ROLE_TYPE_CODE \" + \"COMPONENTS_REFERENCE_DEVELOPER_VIEW_CODE \" + \"DCTS_TEST_PROTOCOLS_FOLDER_PATH \" + \"E_EDOC_VERSION_ALREADY_APPROVINGLY_SIGNED \" + \"E_EDOC_VERSION_ALREADY_APPROVINGLY_SIGNED_BY_USER \" + \"E_EDOC_VERSION_ALREDY_SIGNED \" + \"E_EDOC_VERSION_ALREDY_SIGNED_BY_USER \" + \"EDOC_TYPES_CODE_REQUISITE_FIELD_NAME \" + \"EDOCUMENTS_ALIAS_NAME \" + \"FILES_FOLDER_PATH \" + \"FILTER_OPERANDS_DELIMITER \" + \"FILTER_OPERATIONS_DELIMITER \" + \"FORMCARD_NAME \" + \"FORMLIST_NAME \" + \"GET_EXTENDED_DOCUMENT_EXTENSION_CREATION_MODE \" + \"GET_EXTENDED_DOCUMENT_EXTENSION_IMPORT_MODE \" + \"INTEGRATED_REPORT_TYPE \" + \"IS_BUILDER_APPLICATION_ROLE \" + \"IS_BUILDER_APPLICATION_ROLE2 \" + \"IS_BUILDER_USERS \" + \"ISBSYSDEV \" + \"LOG_FOLDER_PATH \" + \"mbCancel \" + \"mbNo \" + \"mbNoToAll \" + \"mbOK \" + \"mbYes \" + \"mbYesToAll \" + \"MEMORY_DATASET_DESRIPTIONS_FILENAME \" + \"mrNo \" + \"mrNoToAll \" + \"mrYes \" + \"mrYesToAll \" + \"MULTIPLE_SELECT_DIALOG_CODE \" + \"NONOPERATING_RECORD_FLAG_FEMININE \" + \"NONOPERATING_RECORD_FLAG_MASCULINE \" + \"OPERATING_RECORD_FLAG_FEMININE \" + \"OPERATING_RECORD_FLAG_MASCULINE \" + \"PROFILING_SETTINGS_COMMON_SETTINGS_CODE_VALUE \" + \"PROGRAM_INITIATED_LOOKUP_ACTION \" + \"ratDelete \" + \"ratEdit \" + \"ratInsert \" + \"REPORT_TYPE \" + \"REQUIRED_PICK_VALUES_VARIABLE \" + \"rmCard \" + \"rmList \" + \"SBRTE_PROGID_DEV \" + \"SBRTE_PROGID_RELEASE \" + \"STATIC_ROLE_TYPE_CODE \" + \"SUPPRESS_EMPTY_TEMPLATE_CREATION \" + \"SYSTEM_USER_CODE \" + \"UPDATE_DIALOG_DATASET \" + \"USED_IN_OBJECT_HINT_PARAM \" + \"USER_INITIATED_LOOKUP_ACTION \" + \"USER_NAME_FORMAT \" + \"USER_SELECTION_RESTRICTIONS \" + \"WORKFLOW_TEST_PROTOCOLS_FOLDER_PATH \" + \"ELS_SUBTYPE_CONTROL_NAME \" + \"ELS_FOLDER_KIND_CONTROL_NAME \" + \"REPEAT_PROCESS_CURRENT_OBJECT_EXCEPTION_NAME \";\n\n  // Privileges\n  const privileges_constants = \"PRIVILEGE_COMPONENT_FULL_ACCESS \" + \"PRIVILEGE_DEVELOPMENT_EXPORT \" + \"PRIVILEGE_DEVELOPMENT_IMPORT \" + \"PRIVILEGE_DOCUMENT_DELETE \" + \"PRIVILEGE_ESD \" + \"PRIVILEGE_FOLDER_DELETE \" + \"PRIVILEGE_MANAGE_ACCESS_RIGHTS \" + \"PRIVILEGE_MANAGE_REPLICATION \" + \"PRIVILEGE_MANAGE_SESSION_SERVER \" + \"PRIVILEGE_OBJECT_FULL_ACCESS \" + \"PRIVILEGE_OBJECT_VIEW \" + \"PRIVILEGE_RESERVE_LICENSE \" + \"PRIVILEGE_SYSTEM_CUSTOMIZE \" + \"PRIVILEGE_SYSTEM_DEVELOP \" + \"PRIVILEGE_SYSTEM_INSTALL \" + \"PRIVILEGE_TASK_DELETE \" + \"PRIVILEGE_USER_PLUGIN_SETTINGS_CUSTOMIZE \" + \"PRIVILEGES_PSEUDOREFERENCE_CODE \";\n\n  // Pseudoreference code\n  const pseudoreference_code_constants = \"ACCESS_TYPES_PSEUDOREFERENCE_CODE \" + \"ALL_AVAILABLE_COMPONENTS_PSEUDOREFERENCE_CODE \" + \"ALL_AVAILABLE_PRIVILEGES_PSEUDOREFERENCE_CODE \" + \"ALL_REPLICATE_COMPONENTS_PSEUDOREFERENCE_CODE \" + \"AVAILABLE_DEVELOPERS_COMPONENTS_PSEUDOREFERENCE_CODE \" + \"COMPONENTS_PSEUDOREFERENCE_CODE \" + \"FILTRATER_SETTINGS_CONFLICTS_PSEUDOREFERENCE_CODE \" + \"GROUPS_PSEUDOREFERENCE_CODE \" + \"RECEIVE_PROTOCOL_PSEUDOREFERENCE_CODE \" + \"REFERENCE_REQUISITE_PSEUDOREFERENCE_CODE \" + \"REFERENCE_REQUISITES_PSEUDOREFERENCE_CODE \" + \"REFTYPES_PSEUDOREFERENCE_CODE \" + \"REPLICATION_SEANCES_DIARY_PSEUDOREFERENCE_CODE \" + \"SEND_PROTOCOL_PSEUDOREFERENCE_CODE \" + \"SUBSTITUTES_PSEUDOREFERENCE_CODE \" + \"SYSTEM_SETTINGS_PSEUDOREFERENCE_CODE \" + \"UNITS_PSEUDOREFERENCE_CODE \" + \"USERS_PSEUDOREFERENCE_CODE \" + \"VIEWERS_PSEUDOREFERENCE_CODE \";\n\n  // Requisite ISBCertificateType values\n  const requisite_ISBCertificateType_values_constants = \"CERTIFICATE_TYPE_ENCRYPT \" + \"CERTIFICATE_TYPE_SIGN \" + \"CERTIFICATE_TYPE_SIGN_AND_ENCRYPT \";\n\n  // Requisite ISBEDocStorageType values\n  const requisite_ISBEDocStorageType_values_constants = \"STORAGE_TYPE_FILE \" + \"STORAGE_TYPE_NAS_CIFS \" + \"STORAGE_TYPE_SAPERION \" + \"STORAGE_TYPE_SQL_SERVER \";\n\n  // Requisite CompType2 values\n  const requisite_compType2_values_constants = \"COMPTYPE2_REQUISITE_DOCUMENTS_VALUE \" + \"COMPTYPE2_REQUISITE_TASKS_VALUE \" + \"COMPTYPE2_REQUISITE_FOLDERS_VALUE \" + \"COMPTYPE2_REQUISITE_REFERENCES_VALUE \";\n\n  // Requisite name\n  const requisite_name_constants = \"SYSREQ_CODE \" + \"SYSREQ_COMPTYPE2 \" + \"SYSREQ_CONST_AVAILABLE_FOR_WEB \" + \"SYSREQ_CONST_COMMON_CODE \" + \"SYSREQ_CONST_COMMON_VALUE \" + \"SYSREQ_CONST_FIRM_CODE \" + \"SYSREQ_CONST_FIRM_STATUS \" + \"SYSREQ_CONST_FIRM_VALUE \" + \"SYSREQ_CONST_SERVER_STATUS \" + \"SYSREQ_CONTENTS \" + \"SYSREQ_DATE_OPEN \" + \"SYSREQ_DATE_CLOSE \" + \"SYSREQ_DESCRIPTION \" + \"SYSREQ_DESCRIPTION_LOCALIZE_ID \" + \"SYSREQ_DOUBLE \" + \"SYSREQ_EDOC_ACCESS_TYPE \" + \"SYSREQ_EDOC_AUTHOR \" + \"SYSREQ_EDOC_CREATED \" + \"SYSREQ_EDOC_DELEGATE_RIGHTS_REQUISITE_CODE \" + \"SYSREQ_EDOC_EDITOR \" + \"SYSREQ_EDOC_ENCODE_TYPE \" + \"SYSREQ_EDOC_ENCRYPTION_PLUGIN_NAME \" + \"SYSREQ_EDOC_ENCRYPTION_PLUGIN_VERSION \" + \"SYSREQ_EDOC_EXPORT_DATE \" + \"SYSREQ_EDOC_EXPORTER \" + \"SYSREQ_EDOC_KIND \" + \"SYSREQ_EDOC_LIFE_STAGE_NAME \" + \"SYSREQ_EDOC_LOCKED_FOR_SERVER_CODE \" + \"SYSREQ_EDOC_MODIFIED \" + \"SYSREQ_EDOC_NAME \" + \"SYSREQ_EDOC_NOTE \" + \"SYSREQ_EDOC_QUALIFIED_ID \" + \"SYSREQ_EDOC_SESSION_KEY \" + \"SYSREQ_EDOC_SESSION_KEY_ENCRYPTION_PLUGIN_NAME \" + \"SYSREQ_EDOC_SESSION_KEY_ENCRYPTION_PLUGIN_VERSION \" + \"SYSREQ_EDOC_SIGNATURE_TYPE \" + \"SYSREQ_EDOC_SIGNED \" + \"SYSREQ_EDOC_STORAGE \" + \"SYSREQ_EDOC_STORAGES_ARCHIVE_STORAGE \" + \"SYSREQ_EDOC_STORAGES_CHECK_RIGHTS \" + \"SYSREQ_EDOC_STORAGES_COMPUTER_NAME \" + \"SYSREQ_EDOC_STORAGES_EDIT_IN_STORAGE \" + \"SYSREQ_EDOC_STORAGES_EXECUTIVE_STORAGE \" + \"SYSREQ_EDOC_STORAGES_FUNCTION \" + \"SYSREQ_EDOC_STORAGES_INITIALIZED \" + \"SYSREQ_EDOC_STORAGES_LOCAL_PATH \" + \"SYSREQ_EDOC_STORAGES_SAPERION_DATABASE_NAME \" + \"SYSREQ_EDOC_STORAGES_SEARCH_BY_TEXT \" + \"SYSREQ_EDOC_STORAGES_SERVER_NAME \" + \"SYSREQ_EDOC_STORAGES_SHARED_SOURCE_NAME \" + \"SYSREQ_EDOC_STORAGES_TYPE \" + \"SYSREQ_EDOC_TEXT_MODIFIED \" + \"SYSREQ_EDOC_TYPE_ACT_CODE \" + \"SYSREQ_EDOC_TYPE_ACT_DESCRIPTION \" + \"SYSREQ_EDOC_TYPE_ACT_DESCRIPTION_LOCALIZE_ID \" + \"SYSREQ_EDOC_TYPE_ACT_ON_EXECUTE \" + \"SYSREQ_EDOC_TYPE_ACT_ON_EXECUTE_EXISTS \" + \"SYSREQ_EDOC_TYPE_ACT_SECTION \" + \"SYSREQ_EDOC_TYPE_ADD_PARAMS \" + \"SYSREQ_EDOC_TYPE_COMMENT \" + \"SYSREQ_EDOC_TYPE_EVENT_TEXT \" + \"SYSREQ_EDOC_TYPE_NAME_IN_SINGULAR \" + \"SYSREQ_EDOC_TYPE_NAME_IN_SINGULAR_LOCALIZE_ID \" + \"SYSREQ_EDOC_TYPE_NAME_LOCALIZE_ID \" + \"SYSREQ_EDOC_TYPE_NUMERATION_METHOD \" + \"SYSREQ_EDOC_TYPE_PSEUDO_REQUISITE_CODE \" + \"SYSREQ_EDOC_TYPE_REQ_CODE \" + \"SYSREQ_EDOC_TYPE_REQ_DESCRIPTION \" + \"SYSREQ_EDOC_TYPE_REQ_DESCRIPTION_LOCALIZE_ID \" + \"SYSREQ_EDOC_TYPE_REQ_IS_LEADING \" + \"SYSREQ_EDOC_TYPE_REQ_IS_REQUIRED \" + \"SYSREQ_EDOC_TYPE_REQ_NUMBER \" + \"SYSREQ_EDOC_TYPE_REQ_ON_CHANGE \" + \"SYSREQ_EDOC_TYPE_REQ_ON_CHANGE_EXISTS \" + \"SYSREQ_EDOC_TYPE_REQ_ON_SELECT \" + \"SYSREQ_EDOC_TYPE_REQ_ON_SELECT_KIND \" + \"SYSREQ_EDOC_TYPE_REQ_SECTION \" + \"SYSREQ_EDOC_TYPE_VIEW_CARD \" + \"SYSREQ_EDOC_TYPE_VIEW_CODE \" + \"SYSREQ_EDOC_TYPE_VIEW_COMMENT \" + \"SYSREQ_EDOC_TYPE_VIEW_IS_MAIN \" + \"SYSREQ_EDOC_TYPE_VIEW_NAME \" + \"SYSREQ_EDOC_TYPE_VIEW_NAME_LOCALIZE_ID \" + \"SYSREQ_EDOC_VERSION_AUTHOR \" + \"SYSREQ_EDOC_VERSION_CRC \" + \"SYSREQ_EDOC_VERSION_DATA \" + \"SYSREQ_EDOC_VERSION_EDITOR \" + \"SYSREQ_EDOC_VERSION_EXPORT_DATE \" + \"SYSREQ_EDOC_VERSION_EXPORTER \" + \"SYSREQ_EDOC_VERSION_HIDDEN \" + \"SYSREQ_EDOC_VERSION_LIFE_STAGE \" + \"SYSREQ_EDOC_VERSION_MODIFIED \" + \"SYSREQ_EDOC_VERSION_NOTE \" + \"SYSREQ_EDOC_VERSION_SIGNATURE_TYPE \" + \"SYSREQ_EDOC_VERSION_SIGNED \" + \"SYSREQ_EDOC_VERSION_SIZE \" + \"SYSREQ_EDOC_VERSION_SOURCE \" + \"SYSREQ_EDOC_VERSION_TEXT_MODIFIED \" + \"SYSREQ_EDOCKIND_DEFAULT_VERSION_STATE_CODE \" + \"SYSREQ_FOLDER_KIND \" + \"SYSREQ_FUNC_CATEGORY \" + \"SYSREQ_FUNC_COMMENT \" + \"SYSREQ_FUNC_GROUP \" + \"SYSREQ_FUNC_GROUP_COMMENT \" + \"SYSREQ_FUNC_GROUP_NUMBER \" + \"SYSREQ_FUNC_HELP \" + \"SYSREQ_FUNC_PARAM_DEF_VALUE \" + \"SYSREQ_FUNC_PARAM_IDENT \" + \"SYSREQ_FUNC_PARAM_NUMBER \" + \"SYSREQ_FUNC_PARAM_TYPE \" + \"SYSREQ_FUNC_TEXT \" + \"SYSREQ_GROUP_CATEGORY \" + \"SYSREQ_ID \" + \"SYSREQ_LAST_UPDATE \" + \"SYSREQ_LEADER_REFERENCE \" + \"SYSREQ_LINE_NUMBER \" + \"SYSREQ_MAIN_RECORD_ID \" + \"SYSREQ_NAME \" + \"SYSREQ_NAME_LOCALIZE_ID \" + \"SYSREQ_NOTE \" + \"SYSREQ_ORIGINAL_RECORD \" + \"SYSREQ_OUR_FIRM \" + \"SYSREQ_PROFILING_SETTINGS_BATCH_LOGING \" + \"SYSREQ_PROFILING_SETTINGS_BATCH_SIZE \" + \"SYSREQ_PROFILING_SETTINGS_PROFILING_ENABLED \" + \"SYSREQ_PROFILING_SETTINGS_SQL_PROFILING_ENABLED \" + \"SYSREQ_PROFILING_SETTINGS_START_LOGGED \" + \"SYSREQ_RECORD_STATUS \" + \"SYSREQ_REF_REQ_FIELD_NAME \" + \"SYSREQ_REF_REQ_FORMAT \" + \"SYSREQ_REF_REQ_GENERATED \" + \"SYSREQ_REF_REQ_LENGTH \" + \"SYSREQ_REF_REQ_PRECISION \" + \"SYSREQ_REF_REQ_REFERENCE \" + \"SYSREQ_REF_REQ_SECTION \" + \"SYSREQ_REF_REQ_STORED \" + \"SYSREQ_REF_REQ_TOKENS \" + \"SYSREQ_REF_REQ_TYPE \" + \"SYSREQ_REF_REQ_VIEW \" + \"SYSREQ_REF_TYPE_ACT_CODE \" + \"SYSREQ_REF_TYPE_ACT_DESCRIPTION \" + \"SYSREQ_REF_TYPE_ACT_DESCRIPTION_LOCALIZE_ID \" + \"SYSREQ_REF_TYPE_ACT_ON_EXECUTE \" + \"SYSREQ_REF_TYPE_ACT_ON_EXECUTE_EXISTS \" + \"SYSREQ_REF_TYPE_ACT_SECTION \" + \"SYSREQ_REF_TYPE_ADD_PARAMS \" + \"SYSREQ_REF_TYPE_COMMENT \" + \"SYSREQ_REF_TYPE_COMMON_SETTINGS \" + \"SYSREQ_REF_TYPE_DISPLAY_REQUISITE_NAME \" + \"SYSREQ_REF_TYPE_EVENT_TEXT \" + \"SYSREQ_REF_TYPE_MAIN_LEADING_REF \" + \"SYSREQ_REF_TYPE_NAME_IN_SINGULAR \" + \"SYSREQ_REF_TYPE_NAME_IN_SINGULAR_LOCALIZE_ID \" + \"SYSREQ_REF_TYPE_NAME_LOCALIZE_ID \" + \"SYSREQ_REF_TYPE_NUMERATION_METHOD \" + \"SYSREQ_REF_TYPE_REQ_CODE \" + \"SYSREQ_REF_TYPE_REQ_DESCRIPTION \" + \"SYSREQ_REF_TYPE_REQ_DESCRIPTION_LOCALIZE_ID \" + \"SYSREQ_REF_TYPE_REQ_IS_CONTROL \" + \"SYSREQ_REF_TYPE_REQ_IS_FILTER \" + \"SYSREQ_REF_TYPE_REQ_IS_LEADING \" + \"SYSREQ_REF_TYPE_REQ_IS_REQUIRED \" + \"SYSREQ_REF_TYPE_REQ_NUMBER \" + \"SYSREQ_REF_TYPE_REQ_ON_CHANGE \" + \"SYSREQ_REF_TYPE_REQ_ON_CHANGE_EXISTS \" + \"SYSREQ_REF_TYPE_REQ_ON_SELECT \" + \"SYSREQ_REF_TYPE_REQ_ON_SELECT_KIND \" + \"SYSREQ_REF_TYPE_REQ_SECTION \" + \"SYSREQ_REF_TYPE_VIEW_CARD \" + \"SYSREQ_REF_TYPE_VIEW_CODE \" + \"SYSREQ_REF_TYPE_VIEW_COMMENT \" + \"SYSREQ_REF_TYPE_VIEW_IS_MAIN \" + \"SYSREQ_REF_TYPE_VIEW_NAME \" + \"SYSREQ_REF_TYPE_VIEW_NAME_LOCALIZE_ID \" + \"SYSREQ_REFERENCE_TYPE_ID \" + \"SYSREQ_STATE \" + \"SYSREQ_STATЕ \" + \"SYSREQ_SYSTEM_SETTINGS_VALUE \" + \"SYSREQ_TYPE \" + \"SYSREQ_UNIT \" + \"SYSREQ_UNIT_ID \" + \"SYSREQ_USER_GROUPS_GROUP_FULL_NAME \" + \"SYSREQ_USER_GROUPS_GROUP_NAME \" + \"SYSREQ_USER_GROUPS_GROUP_SERVER_NAME \" + \"SYSREQ_USERS_ACCESS_RIGHTS \" + \"SYSREQ_USERS_AUTHENTICATION \" + \"SYSREQ_USERS_CATEGORY \" + \"SYSREQ_USERS_COMPONENT \" + \"SYSREQ_USERS_COMPONENT_USER_IS_PUBLIC \" + \"SYSREQ_USERS_DOMAIN \" + \"SYSREQ_USERS_FULL_USER_NAME \" + \"SYSREQ_USERS_GROUP \" + \"SYSREQ_USERS_IS_MAIN_SERVER \" + \"SYSREQ_USERS_LOGIN \" + \"SYSREQ_USERS_REFERENCE_USER_IS_PUBLIC \" + \"SYSREQ_USERS_STATUS \" + \"SYSREQ_USERS_USER_CERTIFICATE \" + \"SYSREQ_USERS_USER_CERTIFICATE_INFO \" + \"SYSREQ_USERS_USER_CERTIFICATE_PLUGIN_NAME \" + \"SYSREQ_USERS_USER_CERTIFICATE_PLUGIN_VERSION \" + \"SYSREQ_USERS_USER_CERTIFICATE_STATE \" + \"SYSREQ_USERS_USER_CERTIFICATE_SUBJECT_NAME \" + \"SYSREQ_USERS_USER_CERTIFICATE_THUMBPRINT \" + \"SYSREQ_USERS_USER_DEFAULT_CERTIFICATE \" + \"SYSREQ_USERS_USER_DESCRIPTION \" + \"SYSREQ_USERS_USER_GLOBAL_NAME \" + \"SYSREQ_USERS_USER_LOGIN \" + \"SYSREQ_USERS_USER_MAIN_SERVER \" + \"SYSREQ_USERS_USER_TYPE \" + \"SYSREQ_WORK_RULES_FOLDER_ID \";\n\n  // Result\n  const result_constants = \"RESULT_VAR_NAME RESULT_VAR_NAME_ENG \";\n\n  // Rule identification\n  const rule_identification_constants = \"AUTO_NUMERATION_RULE_ID \" + \"CANT_CHANGE_ID_REQUISITE_RULE_ID \" + \"CANT_CHANGE_OURFIRM_REQUISITE_RULE_ID \" + \"CHECK_CHANGING_REFERENCE_RECORD_USE_RULE_ID \" + \"CHECK_CODE_REQUISITE_RULE_ID \" + \"CHECK_DELETING_REFERENCE_RECORD_USE_RULE_ID \" + \"CHECK_FILTRATER_CHANGES_RULE_ID \" + \"CHECK_RECORD_INTERVAL_RULE_ID \" + \"CHECK_REFERENCE_INTERVAL_RULE_ID \" + \"CHECK_REQUIRED_DATA_FULLNESS_RULE_ID \" + \"CHECK_REQUIRED_REQUISITES_FULLNESS_RULE_ID \" + \"MAKE_RECORD_UNRATIFIED_RULE_ID \" + \"RESTORE_AUTO_NUMERATION_RULE_ID \" + \"SET_FIRM_CONTEXT_FROM_RECORD_RULE_ID \" + \"SET_FIRST_RECORD_IN_LIST_FORM_RULE_ID \" + \"SET_IDSPS_VALUE_RULE_ID \" + \"SET_NEXT_CODE_VALUE_RULE_ID \" + \"SET_OURFIRM_BOUNDS_RULE_ID \" + \"SET_OURFIRM_REQUISITE_RULE_ID \";\n\n  // Script block properties\n  const script_block_properties_constants = \"SCRIPT_BLOCK_AFTER_FINISH_EVENT \" + \"SCRIPT_BLOCK_BEFORE_START_EVENT \" + \"SCRIPT_BLOCK_EXECUTION_RESULTS_PROPERTY \" + \"SCRIPT_BLOCK_NAME_PROPERTY \" + \"SCRIPT_BLOCK_SCRIPT_PROPERTY \";\n\n  // Subtask block properties\n  const subtask_block_properties_constants = \"SUBTASK_BLOCK_ABORT_DEADLINE_PROPERTY \" + \"SUBTASK_BLOCK_AFTER_FINISH_EVENT \" + \"SUBTASK_BLOCK_ASSIGN_PARAMS_EVENT \" + \"SUBTASK_BLOCK_ATTACHMENTS_PROPERTY \" + \"SUBTASK_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" + \"SUBTASK_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" + \"SUBTASK_BLOCK_BEFORE_START_EVENT \" + \"SUBTASK_BLOCK_CREATED_TASK_PROPERTY \" + \"SUBTASK_BLOCK_CREATION_EVENT \" + \"SUBTASK_BLOCK_DEADLINE_PROPERTY \" + \"SUBTASK_BLOCK_IMPORTANCE_PROPERTY \" + \"SUBTASK_BLOCK_INITIATOR_PROPERTY \" + \"SUBTASK_BLOCK_IS_RELATIVE_ABORT_DEADLINE_PROPERTY \" + \"SUBTASK_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" + \"SUBTASK_BLOCK_JOBS_TYPE_PROPERTY \" + \"SUBTASK_BLOCK_NAME_PROPERTY \" + \"SUBTASK_BLOCK_PARALLEL_ROUTE_PROPERTY \" + \"SUBTASK_BLOCK_PERFORMERS_PROPERTY \" + \"SUBTASK_BLOCK_RELATIVE_ABORT_DEADLINE_TYPE_PROPERTY \" + \"SUBTASK_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" + \"SUBTASK_BLOCK_REQUIRE_SIGN_PROPERTY \" + \"SUBTASK_BLOCK_STANDARD_ROUTE_PROPERTY \" + \"SUBTASK_BLOCK_START_EVENT \" + \"SUBTASK_BLOCK_STEP_CONTROL_PROPERTY \" + \"SUBTASK_BLOCK_SUBJECT_PROPERTY \" + \"SUBTASK_BLOCK_TASK_CONTROL_PROPERTY \" + \"SUBTASK_BLOCK_TEXT_PROPERTY \" + \"SUBTASK_BLOCK_UNLOCK_ATTACHMENTS_ON_STOP_PROPERTY \" + \"SUBTASK_BLOCK_USE_STANDARD_ROUTE_PROPERTY \" + \"SUBTASK_BLOCK_WAIT_FOR_TASK_COMPLETE_PROPERTY \";\n\n  // System component\n  const system_component_constants = \"SYSCOMP_CONTROL_JOBS \" + \"SYSCOMP_FOLDERS \" + \"SYSCOMP_JOBS \" + \"SYSCOMP_NOTICES \" + \"SYSCOMP_TASKS \";\n\n  // System dialogs\n  const system_dialogs_constants = \"SYSDLG_CREATE_EDOCUMENT \" + \"SYSDLG_CREATE_EDOCUMENT_VERSION \" + \"SYSDLG_CURRENT_PERIOD \" + \"SYSDLG_EDIT_FUNCTION_HELP \" + \"SYSDLG_EDOCUMENT_KINDS_FOR_TEMPLATE \" + \"SYSDLG_EXPORT_MULTIPLE_EDOCUMENTS \" + \"SYSDLG_EXPORT_SINGLE_EDOCUMENT \" + \"SYSDLG_IMPORT_EDOCUMENT \" + \"SYSDLG_MULTIPLE_SELECT \" + \"SYSDLG_SETUP_ACCESS_RIGHTS \" + \"SYSDLG_SETUP_DEFAULT_RIGHTS \" + \"SYSDLG_SETUP_FILTER_CONDITION \" + \"SYSDLG_SETUP_SIGN_RIGHTS \" + \"SYSDLG_SETUP_TASK_OBSERVERS \" + \"SYSDLG_SETUP_TASK_ROUTE \" + \"SYSDLG_SETUP_USERS_LIST \" + \"SYSDLG_SIGN_EDOCUMENT \" + \"SYSDLG_SIGN_MULTIPLE_EDOCUMENTS \";\n\n  // System reference names\n  const system_reference_names_constants = \"SYSREF_ACCESS_RIGHTS_TYPES \" + \"SYSREF_ADMINISTRATION_HISTORY \" + \"SYSREF_ALL_AVAILABLE_COMPONENTS \" + \"SYSREF_ALL_AVAILABLE_PRIVILEGES \" + \"SYSREF_ALL_REPLICATING_COMPONENTS \" + \"SYSREF_AVAILABLE_DEVELOPERS_COMPONENTS \" + \"SYSREF_CALENDAR_EVENTS \" + \"SYSREF_COMPONENT_TOKEN_HISTORY \" + \"SYSREF_COMPONENT_TOKENS \" + \"SYSREF_COMPONENTS \" + \"SYSREF_CONSTANTS \" + \"SYSREF_DATA_RECEIVE_PROTOCOL \" + \"SYSREF_DATA_SEND_PROTOCOL \" + \"SYSREF_DIALOGS \" + \"SYSREF_DIALOGS_REQUISITES \" + \"SYSREF_EDITORS \" + \"SYSREF_EDOC_CARDS \" + \"SYSREF_EDOC_TYPES \" + \"SYSREF_EDOCUMENT_CARD_REQUISITES \" + \"SYSREF_EDOCUMENT_CARD_TYPES \" + \"SYSREF_EDOCUMENT_CARD_TYPES_REFERENCE \" + \"SYSREF_EDOCUMENT_CARDS \" + \"SYSREF_EDOCUMENT_HISTORY \" + \"SYSREF_EDOCUMENT_KINDS \" + \"SYSREF_EDOCUMENT_REQUISITES \" + \"SYSREF_EDOCUMENT_SIGNATURES \" + \"SYSREF_EDOCUMENT_TEMPLATES \" + \"SYSREF_EDOCUMENT_TEXT_STORAGES \" + \"SYSREF_EDOCUMENT_VIEWS \" + \"SYSREF_FILTERER_SETUP_CONFLICTS \" + \"SYSREF_FILTRATER_SETTING_CONFLICTS \" + \"SYSREF_FOLDER_HISTORY \" + \"SYSREF_FOLDERS \" + \"SYSREF_FUNCTION_GROUPS \" + \"SYSREF_FUNCTION_PARAMS \" + \"SYSREF_FUNCTIONS \" + \"SYSREF_JOB_HISTORY \" + \"SYSREF_LINKS \" + \"SYSREF_LOCALIZATION_DICTIONARY \" + \"SYSREF_LOCALIZATION_LANGUAGES \" + \"SYSREF_MODULES \" + \"SYSREF_PRIVILEGES \" + \"SYSREF_RECORD_HISTORY \" + \"SYSREF_REFERENCE_REQUISITES \" + \"SYSREF_REFERENCE_TYPE_VIEWS \" + \"SYSREF_REFERENCE_TYPES \" + \"SYSREF_REFERENCES \" + \"SYSREF_REFERENCES_REQUISITES \" + \"SYSREF_REMOTE_SERVERS \" + \"SYSREF_REPLICATION_SESSIONS_LOG \" + \"SYSREF_REPLICATION_SESSIONS_PROTOCOL \" + \"SYSREF_REPORTS \" + \"SYSREF_ROLES \" + \"SYSREF_ROUTE_BLOCK_GROUPS \" + \"SYSREF_ROUTE_BLOCKS \" + \"SYSREF_SCRIPTS \" + \"SYSREF_SEARCHES \" + \"SYSREF_SERVER_EVENTS \" + \"SYSREF_SERVER_EVENTS_HISTORY \" + \"SYSREF_STANDARD_ROUTE_GROUPS \" + \"SYSREF_STANDARD_ROUTES \" + \"SYSREF_STATUSES \" + \"SYSREF_SYSTEM_SETTINGS \" + \"SYSREF_TASK_HISTORY \" + \"SYSREF_TASK_KIND_GROUPS \" + \"SYSREF_TASK_KINDS \" + \"SYSREF_TASK_RIGHTS \" + \"SYSREF_TASK_SIGNATURES \" + \"SYSREF_TASKS \" + \"SYSREF_UNITS \" + \"SYSREF_USER_GROUPS \" + \"SYSREF_USER_GROUPS_REFERENCE \" + \"SYSREF_USER_SUBSTITUTION \" + \"SYSREF_USERS \" + \"SYSREF_USERS_REFERENCE \" + \"SYSREF_VIEWERS \" + \"SYSREF_WORKING_TIME_CALENDARS \";\n\n  // Table name\n  const table_name_constants = \"ACCESS_RIGHTS_TABLE_NAME \" + \"EDMS_ACCESS_TABLE_NAME \" + \"EDOC_TYPES_TABLE_NAME \";\n\n  // Test\n  const test_constants = \"TEST_DEV_DB_NAME \" + \"TEST_DEV_SYSTEM_CODE \" + \"TEST_EDMS_DB_NAME \" + \"TEST_EDMS_MAIN_CODE \" + \"TEST_EDMS_MAIN_DB_NAME \" + \"TEST_EDMS_SECOND_CODE \" + \"TEST_EDMS_SECOND_DB_NAME \" + \"TEST_EDMS_SYSTEM_CODE \" + \"TEST_ISB5_MAIN_CODE \" + \"TEST_ISB5_SECOND_CODE \" + \"TEST_SQL_SERVER_2005_NAME \" + \"TEST_SQL_SERVER_NAME \";\n\n  // Using the dialog windows\n  const using_the_dialog_windows_constants = \"ATTENTION_CAPTION \" + \"cbsCommandLinks \" + \"cbsDefault \" + \"CONFIRMATION_CAPTION \" + \"ERROR_CAPTION \" + \"INFORMATION_CAPTION \" + \"mrCancel \" + \"mrOk \";\n\n  // Using the document\n  const using_the_document_constants = \"EDOC_VERSION_ACTIVE_STAGE_CODE \" + \"EDOC_VERSION_DESIGN_STAGE_CODE \" + \"EDOC_VERSION_OBSOLETE_STAGE_CODE \";\n\n  // Using the EA and encryption\n  const using_the_EA_and_encryption_constants = \"cpDataEnciphermentEnabled \" + \"cpDigitalSignatureEnabled \" + \"cpID \" + \"cpIssuer \" + \"cpPluginVersion \" + \"cpSerial \" + \"cpSubjectName \" + \"cpSubjSimpleName \" + \"cpValidFromDate \" + \"cpValidToDate \";\n\n  // Using the ISBL-editor\n  const using_the_ISBL_editor_constants = \"ISBL_SYNTAX \" + \"NO_SYNTAX \" + \"XML_SYNTAX \";\n\n  // Wait block properties\n  const wait_block_properties_constants = \"WAIT_BLOCK_AFTER_FINISH_EVENT \" + \"WAIT_BLOCK_BEFORE_START_EVENT \" + \"WAIT_BLOCK_DEADLINE_PROPERTY \" + \"WAIT_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" + \"WAIT_BLOCK_NAME_PROPERTY \" + \"WAIT_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \";\n\n  // SYSRES Common\n  const sysres_common_constants = \"SYSRES_COMMON \" + \"SYSRES_CONST \" + \"SYSRES_MBFUNC \" + \"SYSRES_SBDATA \" + \"SYSRES_SBGUI \" + \"SYSRES_SBINTF \" + \"SYSRES_SBREFDSC \" + \"SYSRES_SQLERRORS \" + \"SYSRES_SYSCOMP \";\n\n  // Константы ==> built_in\n  const CONSTANTS = sysres_constants + base_constants + base_group_name_constants + decision_block_properties_constants + file_extension_constants + job_block_properties_constants + language_code_constants + launching_external_applications_constants + link_kind_constants + lock_type_constants + monitor_block_properties_constants + notice_block_properties_constants + object_events_constants + object_params_constants + other_constants + privileges_constants + pseudoreference_code_constants + requisite_ISBCertificateType_values_constants + requisite_ISBEDocStorageType_values_constants + requisite_compType2_values_constants + requisite_name_constants + result_constants + rule_identification_constants + script_block_properties_constants + subtask_block_properties_constants + system_component_constants + system_dialogs_constants + system_reference_names_constants + table_name_constants + test_constants + using_the_dialog_windows_constants + using_the_document_constants + using_the_EA_and_encryption_constants + using_the_ISBL_editor_constants + wait_block_properties_constants + sysres_common_constants;\n\n  // enum TAccountType\n  const TAccountType = \"atUser atGroup atRole \";\n\n  // enum TActionEnabledMode\n  const TActionEnabledMode = \"aemEnabledAlways \" + \"aemDisabledAlways \" + \"aemEnabledOnBrowse \" + \"aemEnabledOnEdit \" + \"aemDisabledOnBrowseEmpty \";\n\n  // enum TAddPosition\n  const TAddPosition = \"apBegin apEnd \";\n\n  // enum TAlignment\n  const TAlignment = \"alLeft alRight \";\n\n  // enum TAreaShowMode\n  const TAreaShowMode = \"asmNever \" + \"asmNoButCustomize \" + \"asmAsLastTime \" + \"asmYesButCustomize \" + \"asmAlways \";\n\n  // enum TCertificateInvalidationReason\n  const TCertificateInvalidationReason = \"cirCommon cirRevoked \";\n\n  // enum TCertificateType\n  const TCertificateType = \"ctSignature ctEncode ctSignatureEncode \";\n\n  // enum TCheckListBoxItemState\n  const TCheckListBoxItemState = \"clbUnchecked clbChecked clbGrayed \";\n\n  // enum TCloseOnEsc\n  const TCloseOnEsc = \"ceISB ceAlways ceNever \";\n\n  // enum TCompType\n  const TCompType = \"ctDocument \" + \"ctReference \" + \"ctScript \" + \"ctUnknown \" + \"ctReport \" + \"ctDialog \" + \"ctFunction \" + \"ctFolder \" + \"ctEDocument \" + \"ctTask \" + \"ctJob \" + \"ctNotice \" + \"ctControlJob \";\n\n  // enum TConditionFormat\n  const TConditionFormat = \"cfInternal cfDisplay \";\n\n  // enum TConnectionIntent\n  const TConnectionIntent = \"ciUnspecified ciWrite ciRead \";\n\n  // enum TContentKind\n  const TContentKind = \"ckFolder \" + \"ckEDocument \" + \"ckTask \" + \"ckJob \" + \"ckComponentToken \" + \"ckAny \" + \"ckReference \" + \"ckScript \" + \"ckReport \" + \"ckDialog \";\n\n  // enum TControlType\n  const TControlType = \"ctISBLEditor \" + \"ctBevel \" + \"ctButton \" + \"ctCheckListBox \" + \"ctComboBox \" + \"ctComboEdit \" + \"ctGrid \" + \"ctDBCheckBox \" + \"ctDBComboBox \" + \"ctDBEdit \" + \"ctDBEllipsis \" + \"ctDBMemo \" + \"ctDBNavigator \" + \"ctDBRadioGroup \" + \"ctDBStatusLabel \" + \"ctEdit \" + \"ctGroupBox \" + \"ctInplaceHint \" + \"ctMemo \" + \"ctPanel \" + \"ctListBox \" + \"ctRadioButton \" + \"ctRichEdit \" + \"ctTabSheet \" + \"ctWebBrowser \" + \"ctImage \" + \"ctHyperLink \" + \"ctLabel \" + \"ctDBMultiEllipsis \" + \"ctRibbon \" + \"ctRichView \" + \"ctInnerPanel \" + \"ctPanelGroup \" + \"ctBitButton \";\n\n  // enum TCriterionContentType\n  const TCriterionContentType = \"cctDate \" + \"cctInteger \" + \"cctNumeric \" + \"cctPick \" + \"cctReference \" + \"cctString \" + \"cctText \";\n\n  // enum TCultureType\n  const TCultureType = \"cltInternal cltPrimary cltGUI \";\n\n  // enum TDataSetEventType\n  const TDataSetEventType = \"dseBeforeOpen \" + \"dseAfterOpen \" + \"dseBeforeClose \" + \"dseAfterClose \" + \"dseOnValidDelete \" + \"dseBeforeDelete \" + \"dseAfterDelete \" + \"dseAfterDeleteOutOfTransaction \" + \"dseOnDeleteError \" + \"dseBeforeInsert \" + \"dseAfterInsert \" + \"dseOnValidUpdate \" + \"dseBeforeUpdate \" + \"dseOnUpdateRatifiedRecord \" + \"dseAfterUpdate \" + \"dseAfterUpdateOutOfTransaction \" + \"dseOnUpdateError \" + \"dseAfterScroll \" + \"dseOnOpenRecord \" + \"dseOnCloseRecord \" + \"dseBeforeCancel \" + \"dseAfterCancel \" + \"dseOnUpdateDeadlockError \" + \"dseBeforeDetailUpdate \" + \"dseOnPrepareUpdate \" + \"dseOnAnyRequisiteChange \";\n\n  // enum TDataSetState\n  const TDataSetState = \"dssEdit dssInsert dssBrowse dssInActive \";\n\n  // enum TDateFormatType\n  const TDateFormatType = \"dftDate dftShortDate dftDateTime dftTimeStamp \";\n\n  // enum TDateOffsetType\n  const TDateOffsetType = \"dotDays dotHours dotMinutes dotSeconds \";\n\n  // enum TDateTimeKind\n  const TDateTimeKind = \"dtkndLocal dtkndUTC \";\n\n  // enum TDeaAccessRights\n  const TDeaAccessRights = \"arNone arView arEdit arFull \";\n\n  // enum TDocumentDefaultAction\n  const TDocumentDefaultAction = \"ddaView ddaEdit \";\n\n  // enum TEditMode\n  const TEditMode = \"emLock \" + \"emEdit \" + \"emSign \" + \"emExportWithLock \" + \"emImportWithUnlock \" + \"emChangeVersionNote \" + \"emOpenForModify \" + \"emChangeLifeStage \" + \"emDelete \" + \"emCreateVersion \" + \"emImport \" + \"emUnlockExportedWithLock \" + \"emStart \" + \"emAbort \" + \"emReInit \" + \"emMarkAsReaded \" + \"emMarkAsUnreaded \" + \"emPerform \" + \"emAccept \" + \"emResume \" + \"emChangeRights \" + \"emEditRoute \" + \"emEditObserver \" + \"emRecoveryFromLocalCopy \" + \"emChangeWorkAccessType \" + \"emChangeEncodeTypeToCertificate \" + \"emChangeEncodeTypeToPassword \" + \"emChangeEncodeTypeToNone \" + \"emChangeEncodeTypeToCertificatePassword \" + \"emChangeStandardRoute \" + \"emGetText \" + \"emOpenForView \" + \"emMoveToStorage \" + \"emCreateObject \" + \"emChangeVersionHidden \" + \"emDeleteVersion \" + \"emChangeLifeCycleStage \" + \"emApprovingSign \" + \"emExport \" + \"emContinue \" + \"emLockFromEdit \" + \"emUnLockForEdit \" + \"emLockForServer \" + \"emUnlockFromServer \" + \"emDelegateAccessRights \" + \"emReEncode \";\n\n  // enum TEditorCloseObservType\n  const TEditorCloseObservType = \"ecotFile ecotProcess \";\n\n  // enum TEdmsApplicationAction\n  const TEdmsApplicationAction = \"eaGet eaCopy eaCreate eaCreateStandardRoute \";\n\n  // enum TEDocumentLockType\n  const TEDocumentLockType = \"edltAll edltNothing edltQuery \";\n\n  // enum TEDocumentStepShowMode\n  const TEDocumentStepShowMode = \"essmText essmCard \";\n\n  // enum TEDocumentStepVersionType\n  const TEDocumentStepVersionType = \"esvtLast esvtLastActive esvtSpecified \";\n\n  // enum TEDocumentStorageFunction\n  const TEDocumentStorageFunction = \"edsfExecutive edsfArchive \";\n\n  // enum TEDocumentStorageType\n  const TEDocumentStorageType = \"edstSQLServer edstFile \";\n\n  // enum TEDocumentVersionSourceType\n  const TEDocumentVersionSourceType = \"edvstNone edvstEDocumentVersionCopy edvstFile edvstTemplate edvstScannedFile \";\n\n  // enum TEDocumentVersionState\n  const TEDocumentVersionState = \"vsDefault vsDesign vsActive vsObsolete \";\n\n  // enum TEncodeType\n  const TEncodeType = \"etNone etCertificate etPassword etCertificatePassword \";\n\n  // enum TExceptionCategory\n  const TExceptionCategory = \"ecException ecWarning ecInformation \";\n\n  // enum TExportedSignaturesType\n  const TExportedSignaturesType = \"estAll estApprovingOnly \";\n\n  // enum TExportedVersionType\n  const TExportedVersionType = \"evtLast evtLastActive evtQuery \";\n\n  // enum TFieldDataType\n  const TFieldDataType = \"fdtString \" + \"fdtNumeric \" + \"fdtInteger \" + \"fdtDate \" + \"fdtText \" + \"fdtUnknown \" + \"fdtWideString \" + \"fdtLargeInteger \";\n\n  // enum TFolderType\n  const TFolderType = \"ftInbox \" + \"ftOutbox \" + \"ftFavorites \" + \"ftCommonFolder \" + \"ftUserFolder \" + \"ftComponents \" + \"ftQuickLaunch \" + \"ftShortcuts \" + \"ftSearch \";\n\n  // enum TGridRowHeight\n  const TGridRowHeight = \"grhAuto \" + \"grhX1 \" + \"grhX2 \" + \"grhX3 \";\n\n  // enum THyperlinkType\n  const THyperlinkType = \"hltText \" + \"hltRTF \" + \"hltHTML \";\n\n  // enum TImageFileFormat\n  const TImageFileFormat = \"iffBMP \" + \"iffJPEG \" + \"iffMultiPageTIFF \" + \"iffSinglePageTIFF \" + \"iffTIFF \" + \"iffPNG \";\n\n  // enum TImageMode\n  const TImageMode = \"im8bGrayscale \" + \"im24bRGB \" + \"im1bMonochrome \";\n\n  // enum TImageType\n  const TImageType = \"itBMP \" + \"itJPEG \" + \"itWMF \" + \"itPNG \";\n\n  // enum TInplaceHintKind\n  const TInplaceHintKind = \"ikhInformation \" + \"ikhWarning \" + \"ikhError \" + \"ikhNoIcon \";\n\n  // enum TISBLContext\n  const TISBLContext = \"icUnknown \" + \"icScript \" + \"icFunction \" + \"icIntegratedReport \" + \"icAnalyticReport \" + \"icDataSetEventHandler \" + \"icActionHandler \" + \"icFormEventHandler \" + \"icLookUpEventHandler \" + \"icRequisiteChangeEventHandler \" + \"icBeforeSearchEventHandler \" + \"icRoleCalculation \" + \"icSelectRouteEventHandler \" + \"icBlockPropertyCalculation \" + \"icBlockQueryParamsEventHandler \" + \"icChangeSearchResultEventHandler \" + \"icBlockEventHandler \" + \"icSubTaskInitEventHandler \" + \"icEDocDataSetEventHandler \" + \"icEDocLookUpEventHandler \" + \"icEDocActionHandler \" + \"icEDocFormEventHandler \" + \"icEDocRequisiteChangeEventHandler \" + \"icStructuredConversionRule \" + \"icStructuredConversionEventBefore \" + \"icStructuredConversionEventAfter \" + \"icWizardEventHandler \" + \"icWizardFinishEventHandler \" + \"icWizardStepEventHandler \" + \"icWizardStepFinishEventHandler \" + \"icWizardActionEnableEventHandler \" + \"icWizardActionExecuteEventHandler \" + \"icCreateJobsHandler \" + \"icCreateNoticesHandler \" + \"icBeforeLookUpEventHandler \" + \"icAfterLookUpEventHandler \" + \"icTaskAbortEventHandler \" + \"icWorkflowBlockActionHandler \" + \"icDialogDataSetEventHandler \" + \"icDialogActionHandler \" + \"icDialogLookUpEventHandler \" + \"icDialogRequisiteChangeEventHandler \" + \"icDialogFormEventHandler \" + \"icDialogValidCloseEventHandler \" + \"icBlockFormEventHandler \" + \"icTaskFormEventHandler \" + \"icReferenceMethod \" + \"icEDocMethod \" + \"icDialogMethod \" + \"icProcessMessageHandler \";\n\n  // enum TItemShow\n  const TItemShow = \"isShow \" + \"isHide \" + \"isByUserSettings \";\n\n  // enum TJobKind\n  const TJobKind = \"jkJob \" + \"jkNotice \" + \"jkControlJob \";\n\n  // enum TJoinType\n  const TJoinType = \"jtInner \" + \"jtLeft \" + \"jtRight \" + \"jtFull \" + \"jtCross \";\n\n  // enum TLabelPos\n  const TLabelPos = \"lbpAbove \" + \"lbpBelow \" + \"lbpLeft \" + \"lbpRight \";\n\n  // enum TLicensingType\n  const TLicensingType = \"eltPerConnection \" + \"eltPerUser \";\n\n  // enum TLifeCycleStageFontColor\n  const TLifeCycleStageFontColor = \"sfcUndefined \" + \"sfcBlack \" + \"sfcGreen \" + \"sfcRed \" + \"sfcBlue \" + \"sfcOrange \" + \"sfcLilac \";\n\n  // enum TLifeCycleStageFontStyle\n  const TLifeCycleStageFontStyle = \"sfsItalic \" + \"sfsStrikeout \" + \"sfsNormal \";\n\n  // enum TLockableDevelopmentComponentType\n  const TLockableDevelopmentComponentType = \"ldctStandardRoute \" + \"ldctWizard \" + \"ldctScript \" + \"ldctFunction \" + \"ldctRouteBlock \" + \"ldctIntegratedReport \" + \"ldctAnalyticReport \" + \"ldctReferenceType \" + \"ldctEDocumentType \" + \"ldctDialog \" + \"ldctServerEvents \";\n\n  // enum TMaxRecordCountRestrictionType\n  const TMaxRecordCountRestrictionType = \"mrcrtNone \" + \"mrcrtUser \" + \"mrcrtMaximal \" + \"mrcrtCustom \";\n\n  // enum TRangeValueType\n  const TRangeValueType = \"vtEqual \" + \"vtGreaterOrEqual \" + \"vtLessOrEqual \" + \"vtRange \";\n\n  // enum TRelativeDate\n  const TRelativeDate = \"rdYesterday \" + \"rdToday \" + \"rdTomorrow \" + \"rdThisWeek \" + \"rdThisMonth \" + \"rdThisYear \" + \"rdNextMonth \" + \"rdNextWeek \" + \"rdLastWeek \" + \"rdLastMonth \";\n\n  // enum TReportDestination\n  const TReportDestination = \"rdWindow \" + \"rdFile \" + \"rdPrinter \";\n\n  // enum TReqDataType\n  const TReqDataType = \"rdtString \" + \"rdtNumeric \" + \"rdtInteger \" + \"rdtDate \" + \"rdtReference \" + \"rdtAccount \" + \"rdtText \" + \"rdtPick \" + \"rdtUnknown \" + \"rdtLargeInteger \" + \"rdtDocument \";\n\n  // enum TRequisiteEventType\n  const TRequisiteEventType = \"reOnChange \" + \"reOnChangeValues \";\n\n  // enum TSBTimeType\n  const TSBTimeType = \"ttGlobal \" + \"ttLocal \" + \"ttUser \" + \"ttSystem \";\n\n  // enum TSearchShowMode\n  const TSearchShowMode = \"ssmBrowse \" + \"ssmSelect \" + \"ssmMultiSelect \" + \"ssmBrowseModal \";\n\n  // enum TSelectMode\n  const TSelectMode = \"smSelect \" + \"smLike \" + \"smCard \";\n\n  // enum TSignatureType\n  const TSignatureType = \"stNone \" + \"stAuthenticating \" + \"stApproving \";\n\n  // enum TSignerContentType\n  const TSignerContentType = \"sctString \" + \"sctStream \";\n\n  // enum TStringsSortType\n  const TStringsSortType = \"sstAnsiSort \" + \"sstNaturalSort \";\n\n  // enum TStringValueType\n  const TStringValueType = \"svtEqual \" + \"svtContain \";\n\n  // enum TStructuredObjectAttributeType\n  const TStructuredObjectAttributeType = \"soatString \" + \"soatNumeric \" + \"soatInteger \" + \"soatDatetime \" + \"soatReferenceRecord \" + \"soatText \" + \"soatPick \" + \"soatBoolean \" + \"soatEDocument \" + \"soatAccount \" + \"soatIntegerCollection \" + \"soatNumericCollection \" + \"soatStringCollection \" + \"soatPickCollection \" + \"soatDatetimeCollection \" + \"soatBooleanCollection \" + \"soatReferenceRecordCollection \" + \"soatEDocumentCollection \" + \"soatAccountCollection \" + \"soatContents \" + \"soatUnknown \";\n\n  // enum TTaskAbortReason\n  const TTaskAbortReason = \"tarAbortByUser \" + \"tarAbortByWorkflowException \";\n\n  // enum TTextValueType\n  const TTextValueType = \"tvtAllWords \" + \"tvtExactPhrase \" + \"tvtAnyWord \";\n\n  // enum TUserObjectStatus\n  const TUserObjectStatus = \"usNone \" + \"usCompleted \" + \"usRedSquare \" + \"usBlueSquare \" + \"usYellowSquare \" + \"usGreenSquare \" + \"usOrangeSquare \" + \"usPurpleSquare \" + \"usFollowUp \";\n\n  // enum TUserType\n  const TUserType = \"utUnknown \" + \"utUser \" + \"utDeveloper \" + \"utAdministrator \" + \"utSystemDeveloper \" + \"utDisconnected \";\n\n  // enum TValuesBuildType\n  const TValuesBuildType = \"btAnd \" + \"btDetailAnd \" + \"btOr \" + \"btNotOr \" + \"btOnly \";\n\n  // enum TViewMode\n  const TViewMode = \"vmView \" + \"vmSelect \" + \"vmNavigation \";\n\n  // enum TViewSelectionMode\n  const TViewSelectionMode = \"vsmSingle \" + \"vsmMultiple \" + \"vsmMultipleCheck \" + \"vsmNoSelection \";\n\n  // enum TWizardActionType\n  const TWizardActionType = \"wfatPrevious \" + \"wfatNext \" + \"wfatCancel \" + \"wfatFinish \";\n\n  // enum TWizardFormElementProperty\n  const TWizardFormElementProperty = \"wfepUndefined \" + \"wfepText3 \" + \"wfepText6 \" + \"wfepText9 \" + \"wfepSpinEdit \" + \"wfepDropDown \" + \"wfepRadioGroup \" + \"wfepFlag \" + \"wfepText12 \" + \"wfepText15 \" + \"wfepText18 \" + \"wfepText21 \" + \"wfepText24 \" + \"wfepText27 \" + \"wfepText30 \" + \"wfepRadioGroupColumn1 \" + \"wfepRadioGroupColumn2 \" + \"wfepRadioGroupColumn3 \";\n\n  // enum TWizardFormElementType\n  const TWizardFormElementType = \"wfetQueryParameter \" + \"wfetText \" + \"wfetDelimiter \" + \"wfetLabel \";\n\n  // enum TWizardParamType\n  const TWizardParamType = \"wptString \" + \"wptInteger \" + \"wptNumeric \" + \"wptBoolean \" + \"wptDateTime \" + \"wptPick \" + \"wptText \" + \"wptUser \" + \"wptUserList \" + \"wptEDocumentInfo \" + \"wptEDocumentInfoList \" + \"wptReferenceRecordInfo \" + \"wptReferenceRecordInfoList \" + \"wptFolderInfo \" + \"wptTaskInfo \" + \"wptContents \" + \"wptFileName \" + \"wptDate \";\n\n  // enum TWizardStepResult\n  const TWizardStepResult = \"wsrComplete \" + \"wsrGoNext \" + \"wsrGoPrevious \" + \"wsrCustom \" + \"wsrCancel \" + \"wsrGoFinal \";\n\n  // enum TWizardStepType\n  const TWizardStepType = \"wstForm \" + \"wstEDocument \" + \"wstTaskCard \" + \"wstReferenceRecordCard \" + \"wstFinal \";\n\n  // enum TWorkAccessType\n  const TWorkAccessType = \"waAll \" + \"waPerformers \" + \"waManual \";\n\n  // enum TWorkflowBlockType\n  const TWorkflowBlockType = \"wsbStart \" + \"wsbFinish \" + \"wsbNotice \" + \"wsbStep \" + \"wsbDecision \" + \"wsbWait \" + \"wsbMonitor \" + \"wsbScript \" + \"wsbConnector \" + \"wsbSubTask \" + \"wsbLifeCycleStage \" + \"wsbPause \";\n\n  // enum TWorkflowDataType\n  const TWorkflowDataType = \"wdtInteger \" + \"wdtFloat \" + \"wdtString \" + \"wdtPick \" + \"wdtDateTime \" + \"wdtBoolean \" + \"wdtTask \" + \"wdtJob \" + \"wdtFolder \" + \"wdtEDocument \" + \"wdtReferenceRecord \" + \"wdtUser \" + \"wdtGroup \" + \"wdtRole \" + \"wdtIntegerCollection \" + \"wdtFloatCollection \" + \"wdtStringCollection \" + \"wdtPickCollection \" + \"wdtDateTimeCollection \" + \"wdtBooleanCollection \" + \"wdtTaskCollection \" + \"wdtJobCollection \" + \"wdtFolderCollection \" + \"wdtEDocumentCollection \" + \"wdtReferenceRecordCollection \" + \"wdtUserCollection \" + \"wdtGroupCollection \" + \"wdtRoleCollection \" + \"wdtContents \" + \"wdtUserList \" + \"wdtSearchDescription \" + \"wdtDeadLine \" + \"wdtPickSet \" + \"wdtAccountCollection \";\n\n  // enum TWorkImportance\n  const TWorkImportance = \"wiLow \" + \"wiNormal \" + \"wiHigh \";\n\n  // enum TWorkRouteType\n  const TWorkRouteType = \"wrtSoft \" + \"wrtHard \";\n\n  // enum TWorkState\n  const TWorkState = \"wsInit \" + \"wsRunning \" + \"wsDone \" + \"wsControlled \" + \"wsAborted \" + \"wsContinued \";\n\n  // enum TWorkTextBuildingMode\n  const TWorkTextBuildingMode = \"wtmFull \" + \"wtmFromCurrent \" + \"wtmOnlyCurrent \";\n\n  // Перечисления\n  const ENUMS = TAccountType + TActionEnabledMode + TAddPosition + TAlignment + TAreaShowMode + TCertificateInvalidationReason + TCertificateType + TCheckListBoxItemState + TCloseOnEsc + TCompType + TConditionFormat + TConnectionIntent + TContentKind + TControlType + TCriterionContentType + TCultureType + TDataSetEventType + TDataSetState + TDateFormatType + TDateOffsetType + TDateTimeKind + TDeaAccessRights + TDocumentDefaultAction + TEditMode + TEditorCloseObservType + TEdmsApplicationAction + TEDocumentLockType + TEDocumentStepShowMode + TEDocumentStepVersionType + TEDocumentStorageFunction + TEDocumentStorageType + TEDocumentVersionSourceType + TEDocumentVersionState + TEncodeType + TExceptionCategory + TExportedSignaturesType + TExportedVersionType + TFieldDataType + TFolderType + TGridRowHeight + THyperlinkType + TImageFileFormat + TImageMode + TImageType + TInplaceHintKind + TISBLContext + TItemShow + TJobKind + TJoinType + TLabelPos + TLicensingType + TLifeCycleStageFontColor + TLifeCycleStageFontStyle + TLockableDevelopmentComponentType + TMaxRecordCountRestrictionType + TRangeValueType + TRelativeDate + TReportDestination + TReqDataType + TRequisiteEventType + TSBTimeType + TSearchShowMode + TSelectMode + TSignatureType + TSignerContentType + TStringsSortType + TStringValueType + TStructuredObjectAttributeType + TTaskAbortReason + TTextValueType + TUserObjectStatus + TUserType + TValuesBuildType + TViewMode + TViewSelectionMode + TWizardActionType + TWizardFormElementProperty + TWizardFormElementType + TWizardParamType + TWizardStepResult + TWizardStepType + TWorkAccessType + TWorkflowBlockType + TWorkflowDataType + TWorkImportance + TWorkRouteType + TWorkState + TWorkTextBuildingMode;\n\n  // Системные функции ==> SYSFUNCTIONS\n  const system_functions = \"AddSubString \" + \"AdjustLineBreaks \" + \"AmountInWords \" + \"Analysis \" + \"ArrayDimCount \" + \"ArrayHighBound \" + \"ArrayLowBound \" + \"ArrayOf \" + \"ArrayReDim \" + \"Assert \" + \"Assigned \" + \"BeginOfMonth \" + \"BeginOfPeriod \" + \"BuildProfilingOperationAnalysis \" + \"CallProcedure \" + \"CanReadFile \" + \"CArrayElement \" + \"CDataSetRequisite \" + \"ChangeDate \" + \"ChangeReferenceDataset \" + \"Char \" + \"CharPos \" + \"CheckParam \" + \"CheckParamValue \" + \"CompareStrings \" + \"ConstantExists \" + \"ControlState \" + \"ConvertDateStr \" + \"Copy \" + \"CopyFile \" + \"CreateArray \" + \"CreateCachedReference \" + \"CreateConnection \" + \"CreateDialog \" + \"CreateDualListDialog \" + \"CreateEditor \" + \"CreateException \" + \"CreateFile \" + \"CreateFolderDialog \" + \"CreateInputDialog \" + \"CreateLinkFile \" + \"CreateList \" + \"CreateLock \" + \"CreateMemoryDataSet \" + \"CreateObject \" + \"CreateOpenDialog \" + \"CreateProgress \" + \"CreateQuery \" + \"CreateReference \" + \"CreateReport \" + \"CreateSaveDialog \" + \"CreateScript \" + \"CreateSQLPivotFunction \" + \"CreateStringList \" + \"CreateTreeListSelectDialog \" + \"CSelectSQL \" + \"CSQL \" + \"CSubString \" + \"CurrentUserID \" + \"CurrentUserName \" + \"CurrentVersion \" + \"DataSetLocateEx \" + \"DateDiff \" + \"DateTimeDiff \" + \"DateToStr \" + \"DayOfWeek \" + \"DeleteFile \" + \"DirectoryExists \" + \"DisableCheckAccessRights \" + \"DisableCheckFullShowingRestriction \" + \"DisableMassTaskSendingRestrictions \" + \"DropTable \" + \"DupeString \" + \"EditText \" + \"EnableCheckAccessRights \" + \"EnableCheckFullShowingRestriction \" + \"EnableMassTaskSendingRestrictions \" + \"EndOfMonth \" + \"EndOfPeriod \" + \"ExceptionExists \" + \"ExceptionsOff \" + \"ExceptionsOn \" + \"Execute \" + \"ExecuteProcess \" + \"Exit \" + \"ExpandEnvironmentVariables \" + \"ExtractFileDrive \" + \"ExtractFileExt \" + \"ExtractFileName \" + \"ExtractFilePath \" + \"ExtractParams \" + \"FileExists \" + \"FileSize \" + \"FindFile \" + \"FindSubString \" + \"FirmContext \" + \"ForceDirectories \" + \"Format \" + \"FormatDate \" + \"FormatNumeric \" + \"FormatSQLDate \" + \"FormatString \" + \"FreeException \" + \"GetComponent \" + \"GetComponentLaunchParam \" + \"GetConstant \" + \"GetLastException \" + \"GetReferenceRecord \" + \"GetRefTypeByRefID \" + \"GetTableID \" + \"GetTempFolder \" + \"IfThen \" + \"In \" + \"IndexOf \" + \"InputDialog \" + \"InputDialogEx \" + \"InteractiveMode \" + \"IsFileLocked \" + \"IsGraphicFile \" + \"IsNumeric \" + \"Length \" + \"LoadString \" + \"LoadStringFmt \" + \"LocalTimeToUTC \" + \"LowerCase \" + \"Max \" + \"MessageBox \" + \"MessageBoxEx \" + \"MimeDecodeBinary \" + \"MimeDecodeString \" + \"MimeEncodeBinary \" + \"MimeEncodeString \" + \"Min \" + \"MoneyInWords \" + \"MoveFile \" + \"NewID \" + \"Now \" + \"OpenFile \" + \"Ord \" + \"Precision \" + \"Raise \" + \"ReadCertificateFromFile \" + \"ReadFile \" + \"ReferenceCodeByID \" + \"ReferenceNumber \" + \"ReferenceRequisiteMode \" + \"ReferenceRequisiteValue \" + \"RegionDateSettings \" + \"RegionNumberSettings \" + \"RegionTimeSettings \" + \"RegRead \" + \"RegWrite \" + \"RenameFile \" + \"Replace \" + \"Round \" + \"SelectServerCode \" + \"SelectSQL \" + \"ServerDateTime \" + \"SetConstant \" + \"SetManagedFolderFieldsState \" + \"ShowConstantsInputDialog \" + \"ShowMessage \" + \"Sleep \" + \"Split \" + \"SQL \" + \"SQL2XLSTAB \" + \"SQLProfilingSendReport \" + \"StrToDate \" + \"SubString \" + \"SubStringCount \" + \"SystemSetting \" + \"Time \" + \"TimeDiff \" + \"Today \" + \"Transliterate \" + \"Trim \" + \"UpperCase \" + \"UserStatus \" + \"UTCToLocalTime \" + \"ValidateXML \" + \"VarIsClear \" + \"VarIsEmpty \" + \"VarIsNull \" + \"WorkTimeDiff \" + \"WriteFile \" + \"WriteFileEx \" + \"WriteObjectHistory \" + \"Анализ \" + \"БазаДанных \" + \"БлокЕсть \" + \"БлокЕстьРасш \" + \"БлокИнфо \" + \"БлокСнять \" + \"БлокСнятьРасш \" + \"БлокУстановить \" + \"Ввод \" + \"ВводМеню \" + \"ВедС \" + \"ВедСпр \" + \"ВерхняяГраницаМассива \" + \"ВнешПрогр \" + \"Восст \" + \"ВременнаяПапка \" + \"Время \" + \"ВыборSQL \" + \"ВыбратьЗапись \" + \"ВыделитьСтр \" + \"Вызвать \" + \"Выполнить \" + \"ВыпПрогр \" + \"ГрафическийФайл \" + \"ГруппаДополнительно \" + \"ДатаВремяСерв \" + \"ДеньНедели \" + \"ДиалогДаНет \" + \"ДлинаСтр \" + \"ДобПодстр \" + \"ЕПусто \" + \"ЕслиТо \" + \"ЕЧисло \" + \"ЗамПодстр \" + \"ЗаписьСправочника \" + \"ЗначПоляСпр \" + \"ИДТипСпр \" + \"ИзвлечьДиск \" + \"ИзвлечьИмяФайла \" + \"ИзвлечьПуть \" + \"ИзвлечьРасширение \" + \"ИзмДат \" + \"ИзменитьРазмерМассива \" + \"ИзмеренийМассива \" + \"ИмяОрг \" + \"ИмяПоляСпр \" + \"Индекс \" + \"ИндикаторЗакрыть \" + \"ИндикаторОткрыть \" + \"ИндикаторШаг \" + \"ИнтерактивныйРежим \" + \"ИтогТблСпр \" + \"КодВидВедСпр \" + \"КодВидСпрПоИД \" + \"КодПоAnalit \" + \"КодСимвола \" + \"КодСпр \" + \"КолПодстр \" + \"КолПроп \" + \"КонМес \" + \"Конст \" + \"КонстЕсть \" + \"КонстЗнач \" + \"КонТран \" + \"КопироватьФайл \" + \"КопияСтр \" + \"КПериод \" + \"КСтрТблСпр \" + \"Макс \" + \"МаксСтрТблСпр \" + \"Массив \" + \"Меню \" + \"МенюРасш \" + \"Мин \" + \"НаборДанныхНайтиРасш \" + \"НаимВидСпр \" + \"НаимПоAnalit \" + \"НаимСпр \" + \"НастроитьПереводыСтрок \" + \"НачМес \" + \"НачТран \" + \"НижняяГраницаМассива \" + \"НомерСпр \" + \"НПериод \" + \"Окно \" + \"Окр \" + \"Окружение \" + \"ОтлИнфДобавить \" + \"ОтлИнфУдалить \" + \"Отчет \" + \"ОтчетАнал \" + \"ОтчетИнт \" + \"ПапкаСуществует \" + \"Пауза \" + \"ПВыборSQL \" + \"ПереименоватьФайл \" + \"Переменные \" + \"ПереместитьФайл \" + \"Подстр \" + \"ПоискПодстр \" + \"ПоискСтр \" + \"ПолучитьИДТаблицы \" + \"ПользовательДополнительно \" + \"ПользовательИД \" + \"ПользовательИмя \" + \"ПользовательСтатус \" + \"Прервать \" + \"ПроверитьПараметр \" + \"ПроверитьПараметрЗнач \" + \"ПроверитьУсловие \" + \"РазбСтр \" + \"РазнВремя \" + \"РазнДат \" + \"РазнДатаВремя \" + \"РазнРабВремя \" + \"РегУстВрем \" + \"РегУстДат \" + \"РегУстЧсл \" + \"РедТекст \" + \"РеестрЗапись \" + \"РеестрСписокИменПарам \" + \"РеестрЧтение \" + \"РеквСпр \" + \"РеквСпрПр \" + \"Сегодня \" + \"Сейчас \" + \"Сервер \" + \"СерверПроцессИД \" + \"СертификатФайлСчитать \" + \"СжПроб \" + \"Символ \" + \"СистемаДиректумКод \" + \"СистемаИнформация \" + \"СистемаКод \" + \"Содержит \" + \"СоединениеЗакрыть \" + \"СоединениеОткрыть \" + \"СоздатьДиалог \" + \"СоздатьДиалогВыбораИзДвухСписков \" + \"СоздатьДиалогВыбораПапки \" + \"СоздатьДиалогОткрытияФайла \" + \"СоздатьДиалогСохраненияФайла \" + \"СоздатьЗапрос \" + \"СоздатьИндикатор \" + \"СоздатьИсключение \" + \"СоздатьКэшированныйСправочник \" + \"СоздатьМассив \" + \"СоздатьНаборДанных \" + \"СоздатьОбъект \" + \"СоздатьОтчет \" + \"СоздатьПапку \" + \"СоздатьРедактор \" + \"СоздатьСоединение \" + \"СоздатьСписок \" + \"СоздатьСписокСтрок \" + \"СоздатьСправочник \" + \"СоздатьСценарий \" + \"СоздСпр \" + \"СостСпр \" + \"Сохр \" + \"СохрСпр \" + \"СписокСистем \" + \"Спр \" + \"Справочник \" + \"СпрБлокЕсть \" + \"СпрБлокСнять \" + \"СпрБлокСнятьРасш \" + \"СпрБлокУстановить \" + \"СпрИзмНабДан \" + \"СпрКод \" + \"СпрНомер \" + \"СпрОбновить \" + \"СпрОткрыть \" + \"СпрОтменить \" + \"СпрПарам \" + \"СпрПолеЗнач \" + \"СпрПолеИмя \" + \"СпрРекв \" + \"СпрРеквВведЗн \" + \"СпрРеквНовые \" + \"СпрРеквПр \" + \"СпрРеквПредЗн \" + \"СпрРеквРежим \" + \"СпрРеквТипТекст \" + \"СпрСоздать \" + \"СпрСост \" + \"СпрСохранить \" + \"СпрТблИтог \" + \"СпрТблСтр \" + \"СпрТблСтрКол \" + \"СпрТблСтрМакс \" + \"СпрТблСтрМин \" + \"СпрТблСтрПред \" + \"СпрТблСтрСлед \" + \"СпрТблСтрСозд \" + \"СпрТблСтрУд \" + \"СпрТекПредст \" + \"СпрУдалить \" + \"СравнитьСтр \" + \"СтрВерхРегистр \" + \"СтрНижнРегистр \" + \"СтрТблСпр \" + \"СумПроп \" + \"Сценарий \" + \"СценарийПарам \" + \"ТекВерсия \" + \"ТекОрг \" + \"Точн \" + \"Тран \" + \"Транслитерация \" + \"УдалитьТаблицу \" + \"УдалитьФайл \" + \"УдСпр \" + \"УдСтрТблСпр \" + \"Уст \" + \"УстановкиКонстант \" + \"ФайлАтрибутСчитать \" + \"ФайлАтрибутУстановить \" + \"ФайлВремя \" + \"ФайлВремяУстановить \" + \"ФайлВыбрать \" + \"ФайлЗанят \" + \"ФайлЗаписать \" + \"ФайлИскать \" + \"ФайлКопировать \" + \"ФайлМожноЧитать \" + \"ФайлОткрыть \" + \"ФайлПереименовать \" + \"ФайлПерекодировать \" + \"ФайлПереместить \" + \"ФайлПросмотреть \" + \"ФайлРазмер \" + \"ФайлСоздать \" + \"ФайлСсылкаСоздать \" + \"ФайлСуществует \" + \"ФайлСчитать \" + \"ФайлУдалить \" + \"ФмтSQLДат \" + \"ФмтДат \" + \"ФмтСтр \" + \"ФмтЧсл \" + \"Формат \" + \"ЦМассивЭлемент \" + \"ЦНаборДанныхРеквизит \" + \"ЦПодстр \";\n\n  // Предопределенные переменные ==> built_in\n  const predefined_variables = \"AltState \" + \"Application \" + \"CallType \" + \"ComponentTokens \" + \"CreatedJobs \" + \"CreatedNotices \" + \"ControlState \" + \"DialogResult \" + \"Dialogs \" + \"EDocuments \" + \"EDocumentVersionSource \" + \"Folders \" + \"GlobalIDs \" + \"Job \" + \"Jobs \" + \"InputValue \" + \"LookUpReference \" + \"LookUpRequisiteNames \" + \"LookUpSearch \" + \"Object \" + \"ParentComponent \" + \"Processes \" + \"References \" + \"Requisite \" + \"ReportName \" + \"Reports \" + \"Result \" + \"Scripts \" + \"Searches \" + \"SelectedAttachments \" + \"SelectedItems \" + \"SelectMode \" + \"Sender \" + \"ServerEvents \" + \"ServiceFactory \" + \"ShiftState \" + \"SubTask \" + \"SystemDialogs \" + \"Tasks \" + \"Wizard \" + \"Wizards \" + \"Work \" + \"ВызовСпособ \" + \"ИмяОтчета \" + \"РеквЗнач \";\n\n  // Интерфейсы ==> type\n  const interfaces = \"IApplication \" + \"IAccessRights \" + \"IAccountRepository \" + \"IAccountSelectionRestrictions \" + \"IAction \" + \"IActionList \" + \"IAdministrationHistoryDescription \" + \"IAnchors \" + \"IApplication \" + \"IArchiveInfo \" + \"IAttachment \" + \"IAttachmentList \" + \"ICheckListBox \" + \"ICheckPointedList \" + \"IColumn \" + \"IComponent \" + \"IComponentDescription \" + \"IComponentToken \" + \"IComponentTokenFactory \" + \"IComponentTokenInfo \" + \"ICompRecordInfo \" + \"IConnection \" + \"IContents \" + \"IControl \" + \"IControlJob \" + \"IControlJobInfo \" + \"IControlList \" + \"ICrypto \" + \"ICrypto2 \" + \"ICustomJob \" + \"ICustomJobInfo \" + \"ICustomListBox \" + \"ICustomObjectWizardStep \" + \"ICustomWork \" + \"ICustomWorkInfo \" + \"IDataSet \" + \"IDataSetAccessInfo \" + \"IDataSigner \" + \"IDateCriterion \" + \"IDateRequisite \" + \"IDateRequisiteDescription \" + \"IDateValue \" + \"IDeaAccessRights \" + \"IDeaObjectInfo \" + \"IDevelopmentComponentLock \" + \"IDialog \" + \"IDialogFactory \" + \"IDialogPickRequisiteItems \" + \"IDialogsFactory \" + \"IDICSFactory \" + \"IDocRequisite \" + \"IDocumentInfo \" + \"IDualListDialog \" + \"IECertificate \" + \"IECertificateInfo \" + \"IECertificates \" + \"IEditControl \" + \"IEditorForm \" + \"IEdmsExplorer \" + \"IEdmsObject \" + \"IEdmsObjectDescription \" + \"IEdmsObjectFactory \" + \"IEdmsObjectInfo \" + \"IEDocument \" + \"IEDocumentAccessRights \" + \"IEDocumentDescription \" + \"IEDocumentEditor \" + \"IEDocumentFactory \" + \"IEDocumentInfo \" + \"IEDocumentStorage \" + \"IEDocumentVersion \" + \"IEDocumentVersionListDialog \" + \"IEDocumentVersionSource \" + \"IEDocumentWizardStep \" + \"IEDocVerSignature \" + \"IEDocVersionState \" + \"IEnabledMode \" + \"IEncodeProvider \" + \"IEncrypter \" + \"IEvent \" + \"IEventList \" + \"IException \" + \"IExternalEvents \" + \"IExternalHandler \" + \"IFactory \" + \"IField \" + \"IFileDialog \" + \"IFolder \" + \"IFolderDescription \" + \"IFolderDialog \" + \"IFolderFactory \" + \"IFolderInfo \" + \"IForEach \" + \"IForm \" + \"IFormTitle \" + \"IFormWizardStep \" + \"IGlobalIDFactory \" + \"IGlobalIDInfo \" + \"IGrid \" + \"IHasher \" + \"IHistoryDescription \" + \"IHyperLinkControl \" + \"IImageButton \" + \"IImageControl \" + \"IInnerPanel \" + \"IInplaceHint \" + \"IIntegerCriterion \" + \"IIntegerList \" + \"IIntegerRequisite \" + \"IIntegerValue \" + \"IISBLEditorForm \" + \"IJob \" + \"IJobDescription \" + \"IJobFactory \" + \"IJobForm \" + \"IJobInfo \" + \"ILabelControl \" + \"ILargeIntegerCriterion \" + \"ILargeIntegerRequisite \" + \"ILargeIntegerValue \" + \"ILicenseInfo \" + \"ILifeCycleStage \" + \"IList \" + \"IListBox \" + \"ILocalIDInfo \" + \"ILocalization \" + \"ILock \" + \"IMemoryDataSet \" + \"IMessagingFactory \" + \"IMetadataRepository \" + \"INotice \" + \"INoticeInfo \" + \"INumericCriterion \" + \"INumericRequisite \" + \"INumericValue \" + \"IObject \" + \"IObjectDescription \" + \"IObjectImporter \" + \"IObjectInfo \" + \"IObserver \" + \"IPanelGroup \" + \"IPickCriterion \" + \"IPickProperty \" + \"IPickRequisite \" + \"IPickRequisiteDescription \" + \"IPickRequisiteItem \" + \"IPickRequisiteItems \" + \"IPickValue \" + \"IPrivilege \" + \"IPrivilegeList \" + \"IProcess \" + \"IProcessFactory \" + \"IProcessMessage \" + \"IProgress \" + \"IProperty \" + \"IPropertyChangeEvent \" + \"IQuery \" + \"IReference \" + \"IReferenceCriterion \" + \"IReferenceEnabledMode \" + \"IReferenceFactory \" + \"IReferenceHistoryDescription \" + \"IReferenceInfo \" + \"IReferenceRecordCardWizardStep \" + \"IReferenceRequisiteDescription \" + \"IReferencesFactory \" + \"IReferenceValue \" + \"IRefRequisite \" + \"IReport \" + \"IReportFactory \" + \"IRequisite \" + \"IRequisiteDescription \" + \"IRequisiteDescriptionList \" + \"IRequisiteFactory \" + \"IRichEdit \" + \"IRouteStep \" + \"IRule \" + \"IRuleList \" + \"ISchemeBlock \" + \"IScript \" + \"IScriptFactory \" + \"ISearchCriteria \" + \"ISearchCriterion \" + \"ISearchDescription \" + \"ISearchFactory \" + \"ISearchFolderInfo \" + \"ISearchForObjectDescription \" + \"ISearchResultRestrictions \" + \"ISecuredContext \" + \"ISelectDialog \" + \"IServerEvent \" + \"IServerEventFactory \" + \"IServiceDialog \" + \"IServiceFactory \" + \"ISignature \" + \"ISignProvider \" + \"ISignProvider2 \" + \"ISignProvider3 \" + \"ISimpleCriterion \" + \"IStringCriterion \" + \"IStringList \" + \"IStringRequisite \" + \"IStringRequisiteDescription \" + \"IStringValue \" + \"ISystemDialogsFactory \" + \"ISystemInfo \" + \"ITabSheet \" + \"ITask \" + \"ITaskAbortReasonInfo \" + \"ITaskCardWizardStep \" + \"ITaskDescription \" + \"ITaskFactory \" + \"ITaskInfo \" + \"ITaskRoute \" + \"ITextCriterion \" + \"ITextRequisite \" + \"ITextValue \" + \"ITreeListSelectDialog \" + \"IUser \" + \"IUserList \" + \"IValue \" + \"IView \" + \"IWebBrowserControl \" + \"IWizard \" + \"IWizardAction \" + \"IWizardFactory \" + \"IWizardFormElement \" + \"IWizardParam \" + \"IWizardPickParam \" + \"IWizardReferenceParam \" + \"IWizardStep \" + \"IWorkAccessRights \" + \"IWorkDescription \" + \"IWorkflowAskableParam \" + \"IWorkflowAskableParams \" + \"IWorkflowBlock \" + \"IWorkflowBlockResult \" + \"IWorkflowEnabledMode \" + \"IWorkflowParam \" + \"IWorkflowPickParam \" + \"IWorkflowReferenceParam \" + \"IWorkState \" + \"IWorkTreeCustomNode \" + \"IWorkTreeJobNode \" + \"IWorkTreeTaskNode \" + \"IXMLEditorForm \" + \"SBCrypto \";\n\n  // built_in : встроенные или библиотечные объекты (константы, перечисления)\n  const BUILTIN = CONSTANTS + ENUMS;\n\n  // class: встроенные наборы значений, системные объекты, фабрики\n  const CLASS = predefined_variables;\n\n  // literal : примитивные типы\n  const LITERAL = \"null true false nil \";\n\n  // number : числа\n  const NUMBERS = {\n    className: \"number\",\n    begin: hljs.NUMBER_RE,\n    relevance: 0\n  };\n\n  // string : строки\n  const STRINGS = {\n    className: \"string\",\n    variants: [{\n      begin: '\"',\n      end: '\"'\n    }, {\n      begin: \"'\",\n      end: \"'\"\n    }]\n  };\n\n  // Токены\n  const DOCTAGS = {\n    className: \"doctag\",\n    begin: \"\\\\b(?:TODO|DONE|BEGIN|END|STUB|CHG|FIXME|NOTE|BUG|XXX)\\\\b\",\n    relevance: 0\n  };\n\n  // Однострочный комментарий\n  const ISBL_LINE_COMMENT_MODE = {\n    className: \"comment\",\n    begin: \"//\",\n    end: \"$\",\n    relevance: 0,\n    contains: [hljs.PHRASAL_WORDS_MODE, DOCTAGS]\n  };\n\n  // Многострочный комментарий\n  const ISBL_BLOCK_COMMENT_MODE = {\n    className: \"comment\",\n    begin: \"/\\\\*\",\n    end: \"\\\\*/\",\n    relevance: 0,\n    contains: [hljs.PHRASAL_WORDS_MODE, DOCTAGS]\n  };\n\n  // comment : комментарии\n  const COMMENTS = {\n    variants: [ISBL_LINE_COMMENT_MODE, ISBL_BLOCK_COMMENT_MODE]\n  };\n\n  // keywords : ключевые слова\n  const KEYWORDS = {\n    $pattern: UNDERSCORE_IDENT_RE,\n    keyword: KEYWORD,\n    built_in: BUILTIN,\n    class: CLASS,\n    literal: LITERAL\n  };\n\n  // methods : методы\n  const METHODS = {\n    begin: \"\\\\.\\\\s*\" + hljs.UNDERSCORE_IDENT_RE,\n    keywords: KEYWORDS,\n    relevance: 0\n  };\n\n  // type : встроенные типы\n  const TYPES = {\n    className: \"type\",\n    begin: \":[ \\\\t]*(\" + interfaces.trim().replace(/\\s/g, \"|\") + \")\",\n    end: \"[ \\\\t]*=\",\n    excludeEnd: true\n  };\n\n  // variables : переменные\n  const VARIABLES = {\n    className: \"variable\",\n    keywords: KEYWORDS,\n    begin: UNDERSCORE_IDENT_RE,\n    relevance: 0,\n    contains: [TYPES, METHODS]\n  };\n\n  // Имена функций\n  const FUNCTION_TITLE = FUNCTION_NAME_IDENT_RE + \"\\\\(\";\n  const TITLE_MODE = {\n    className: \"title\",\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      built_in: system_functions\n    },\n    begin: FUNCTION_TITLE,\n    end: \"\\\\(\",\n    returnBegin: true,\n    excludeEnd: true\n  };\n\n  // function : функции\n  const FUNCTIONS = {\n    className: \"function\",\n    begin: FUNCTION_TITLE,\n    end: \"\\\\)$\",\n    returnBegin: true,\n    keywords: KEYWORDS,\n    illegal: \"[\\\\[\\\\]\\\\|\\\\$\\\\?%,~#@]\",\n    contains: [TITLE_MODE, METHODS, VARIABLES, STRINGS, NUMBERS, COMMENTS]\n  };\n  return {\n    name: 'ISBL',\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    illegal: \"\\\\$|\\\\?|%|,|;$|~|#|@|</\",\n    contains: [FUNCTIONS, TYPES, METHODS, VARIABLES, STRINGS, NUMBERS, COMMENTS]\n  };\n}\nmodule.exports = isbl;", "map": {"version": 3, "names": ["isbl", "hljs", "UNDERSCORE_IDENT_RE", "FUNCTION_NAME_IDENT_RE", "KEYWORD", "sysres_constants", "base_constants", "base_group_name_constants", "decision_block_properties_constants", "file_extension_constants", "job_block_properties_constants", "language_code_constants", "launching_external_applications_constants", "link_kind_constants", "lock_type_constants", "monitor_block_properties_constants", "notice_block_properties_constants", "object_events_constants", "object_params_constants", "other_constants", "privileges_constants", "pseudoreference_code_constants", "requisite_ISBCertificateType_values_constants", "requisite_ISBEDocStorageType_values_constants", "requisite_compType2_values_constants", "requisite_name_constants", "result_constants", "rule_identification_constants", "script_block_properties_constants", "subtask_block_properties_constants", "system_component_constants", "system_dialogs_constants", "system_reference_names_constants", "table_name_constants", "test_constants", "using_the_dialog_windows_constants", "using_the_document_constants", "using_the_EA_and_encryption_constants", "using_the_ISBL_editor_constants", "wait_block_properties_constants", "sysres_common_constants", "CONSTANTS", "TAccountType", "TActionEnabledMode", "TAddPosition", "TAlignment", "TAreaShowMode", "TCertificateInvalidationReason", "TCertificateType", "TCheckListBoxItemState", "TCloseOnEsc", "TCompType", "TConditionFormat", "TConnectionIntent", "TContentKind", "TControlType", "TCriterionContentType", "TCultureType", "TDataSetEventType", "TDataSetState", "TDateFormatType", "TDateOffsetType", "TDateTimeKind", "TDeaAccessRights", "TDocumentDefaultAction", "TEditMode", "TEditorCloseObservType", "TEdmsApplicationAction", "TEDocumentLockType", "TEDocumentStepShowMode", "TEDocumentStepVersionType", "TEDocumentStorageFunction", "TEDocumentStorageType", "TEDocumentVersionSourceType", "TEDocumentVersionState", "TEncodeType", "TExceptionCategory", "TExportedSignaturesType", "TExportedVersionType", "TFieldDataType", "TFolderType", "TGridRowHeight", "THyperlinkType", "TImageFileFormat", "TImageMode", "TImageType", "TInplaceHintKind", "TISBLContext", "TItemShow", "TJob<PERSON><PERSON>", "TJoinType", "TLabelPos", "TLicensingType", "TLifeCycleStageFontColor", "TLifeCycleStageFontStyle", "TLockableDevelopmentComponentType", "TMaxRecordCountRestrictionType", "TRangeValueType", "TRelativeDate", "TReportDestination", "TReqDataType", "TRequisiteEventType", "TSBTimeType", "TSearchShowMode", "TSelectMode", "TSignatureType", "TSignerContentType", "TStringsSortType", "TStringValueType", "TStructuredObjectAttributeType", "TTaskAbortReason", "TTextValueType", "TUserObjectStatus", "TUserType", "TValuesBuildType", "TViewMode", "TViewSelectionMode", "TWizardActionType", "TWizardFormElementProperty", "TWizardFormElementType", "TWizardParamType", "TWizardStepResult", "TWizardStepType", "TWorkAccessType", "TWorkflowBlockType", "TWorkflowDataType", "TWorkImportance", "TWorkRouteType", "TWorkState", "TWorkTextBuildingMode", "ENUMS", "system_functions", "predefined_variables", "interfaces", "BUILTIN", "CLASS", "LITERAL", "NUMBERS", "className", "begin", "NUMBER_RE", "relevance", "STRINGS", "variants", "end", "DOCTAGS", "ISBL_LINE_COMMENT_MODE", "contains", "PHRASAL_WORDS_MODE", "ISBL_BLOCK_COMMENT_MODE", "COMMENTS", "KEYWORDS", "$pattern", "keyword", "built_in", "class", "literal", "METHODS", "keywords", "TYPES", "trim", "replace", "excludeEnd", "VARIABLES", "FUNCTION_TITLE", "TITLE_MODE", "returnBegin", "FUNCTIONS", "illegal", "name", "case_insensitive", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/isbl.js"], "sourcesContent": ["/*\nLanguage: ISBL\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: built-in language DIRECTUM\nCategory: enterprise\n*/\n\nfunction isbl(hljs) {\n  // Определение идентификаторов\n  const UNDERSCORE_IDENT_RE = \"[A-Za-zА-Яа-яёЁ_!][A-Za-zА-Яа-яёЁ_0-9]*\";\n\n  // Определение имен функций\n  const FUNCTION_NAME_IDENT_RE = \"[A-Za-zА-Яа-яёЁ_][A-Za-zА-Яа-яёЁ_0-9]*\";\n\n  // keyword : ключевые слова\n  const KEYWORD =\n    \"and и else иначе endexcept endfinally endforeach конецвсе endif конецесли endwhile конецпока \" +\n    \"except exitfor finally foreach все if если in в not не or или try while пока \";\n\n  // SYSRES Constants\n  const sysres_constants =\n    \"SYSRES_CONST_ACCES_RIGHT_TYPE_EDIT \" +\n    \"SYSRES_CONST_ACCES_RIGHT_TYPE_FULL \" +\n    \"SYSRES_CONST_ACCES_RIGHT_TYPE_VIEW \" +\n    \"SYSRES_CONST_ACCESS_MODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_NO_ACCESS_VIEW \" +\n    \"SYSRES_CONST_ACCESS_NO_ACCESS_VIEW_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_ADD_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_ADD_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_CHANGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_CHANGE_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_DELETE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_DELETE_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_EXECUTE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_EXECUTE_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_NO_ACCESS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_NO_ACCESS_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_RATIFY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_RATIFY_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_CHANGE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_CHANGE_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_EXISTS \" +\n    \"SYSRES_CONST_ACCESS_TYPE_EXISTS_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_FULL \" +\n    \"SYSRES_CONST_ACCESS_TYPE_FULL_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_VIEW \" +\n    \"SYSRES_CONST_ACCESS_TYPE_VIEW_CODE \" +\n    \"SYSRES_CONST_ACTION_TYPE_ABORT \" +\n    \"SYSRES_CONST_ACTION_TYPE_ACCEPT \" +\n    \"SYSRES_CONST_ACTION_TYPE_ACCESS_RIGHTS \" +\n    \"SYSRES_CONST_ACTION_TYPE_ADD_ATTACHMENT \" +\n    \"SYSRES_CONST_ACTION_TYPE_CHANGE_CARD \" +\n    \"SYSRES_CONST_ACTION_TYPE_CHANGE_KIND \" +\n    \"SYSRES_CONST_ACTION_TYPE_CHANGE_STORAGE \" +\n    \"SYSRES_CONST_ACTION_TYPE_CONTINUE \" +\n    \"SYSRES_CONST_ACTION_TYPE_COPY \" +\n    \"SYSRES_CONST_ACTION_TYPE_CREATE \" +\n    \"SYSRES_CONST_ACTION_TYPE_CREATE_VERSION \" +\n    \"SYSRES_CONST_ACTION_TYPE_DELETE \" +\n    \"SYSRES_CONST_ACTION_TYPE_DELETE_ATTACHMENT \" +\n    \"SYSRES_CONST_ACTION_TYPE_DELETE_VERSION \" +\n    \"SYSRES_CONST_ACTION_TYPE_DISABLE_DELEGATE_ACCESS_RIGHTS \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENABLE_DELEGATE_ACCESS_RIGHTS \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_CERTIFICATE \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_CERTIFICATE_AND_PASSWORD \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_PASSWORD \" +\n    \"SYSRES_CONST_ACTION_TYPE_EXPORT_WITH_LOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_EXPORT_WITHOUT_LOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_IMPORT_WITH_UNLOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_IMPORT_WITHOUT_UNLOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_LIFE_CYCLE_STAGE \" +\n    \"SYSRES_CONST_ACTION_TYPE_LOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_LOCK_FOR_SERVER \" +\n    \"SYSRES_CONST_ACTION_TYPE_LOCK_MODIFY \" +\n    \"SYSRES_CONST_ACTION_TYPE_MARK_AS_READED \" +\n    \"SYSRES_CONST_ACTION_TYPE_MARK_AS_UNREADED \" +\n    \"SYSRES_CONST_ACTION_TYPE_MODIFY \" +\n    \"SYSRES_CONST_ACTION_TYPE_MODIFY_CARD \" +\n    \"SYSRES_CONST_ACTION_TYPE_MOVE_TO_ARCHIVE \" +\n    \"SYSRES_CONST_ACTION_TYPE_OFF_ENCRYPTION \" +\n    \"SYSRES_CONST_ACTION_TYPE_PASSWORD_CHANGE \" +\n    \"SYSRES_CONST_ACTION_TYPE_PERFORM \" +\n    \"SYSRES_CONST_ACTION_TYPE_RECOVER_FROM_LOCAL_COPY \" +\n    \"SYSRES_CONST_ACTION_TYPE_RESTART \" +\n    \"SYSRES_CONST_ACTION_TYPE_RESTORE_FROM_ARCHIVE \" +\n    \"SYSRES_CONST_ACTION_TYPE_REVISION \" +\n    \"SYSRES_CONST_ACTION_TYPE_SEND_BY_MAIL \" +\n    \"SYSRES_CONST_ACTION_TYPE_SIGN \" +\n    \"SYSRES_CONST_ACTION_TYPE_START \" +\n    \"SYSRES_CONST_ACTION_TYPE_UNLOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_UNLOCK_FROM_SERVER \" +\n    \"SYSRES_CONST_ACTION_TYPE_VERSION_STATE \" +\n    \"SYSRES_CONST_ACTION_TYPE_VERSION_VISIBILITY \" +\n    \"SYSRES_CONST_ACTION_TYPE_VIEW \" +\n    \"SYSRES_CONST_ACTION_TYPE_VIEW_SHADOW_COPY \" +\n    \"SYSRES_CONST_ACTION_TYPE_WORKFLOW_DESCRIPTION_MODIFY \" +\n    \"SYSRES_CONST_ACTION_TYPE_WRITE_HISTORY \" +\n    \"SYSRES_CONST_ACTIVE_VERSION_STATE_PICK_VALUE \" +\n    \"SYSRES_CONST_ADD_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_ADDITION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ADDITIONAL_PARAMS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_END_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_READ_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_START_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_STATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_ADDING_USER_TO_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_ADDING_USER_TO_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_COMP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_COMP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_USER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_USER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_CREATION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_CREATION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_DELETION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_DELETION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_COMP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_COMP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_FROM_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_FROM_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_RESTRICTION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_RESTRICTION_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_PRIVILEGE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_PRIVILEGE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_RIGHTS_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_RIGHTS_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_MAIN_SERVER_CHANGED_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_MAIN_SERVER_CHANGED_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_PUBLIC_CHANGED_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_PUBLIC_CHANGED_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_RESTRICTION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_RESTRICTION_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_PRIVILEGE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_PRIVILEGE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_RIGHTS_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_RIGHTS_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_CREATION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_CREATION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_DELETION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_DELETION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_CATEGORY_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_CATEGORY_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_COMP_TITLE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_COMP_TITLE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_FULL_NAME_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_FULL_NAME_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_PARENT_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_PARENT_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_AUTH_TYPE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_AUTH_TYPE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_LOGIN_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_LOGIN_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_STATUS_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_STATUS_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_USER_PASSWORD_CHANGE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_USER_PASSWORD_CHANGE_ACTION \" +\n    \"SYSRES_CONST_ALL_ACCEPT_CONDITION_RUS \" +\n    \"SYSRES_CONST_ALL_USERS_GROUP \" +\n    \"SYSRES_CONST_ALL_USERS_GROUP_NAME \" +\n    \"SYSRES_CONST_ALL_USERS_SERVER_GROUP_NAME \" +\n    \"SYSRES_CONST_ALLOWED_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_ALLOWED_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_APP_VIEWER_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_APPROVING_SIGNATURE_NAME \" +\n    \"SYSRES_CONST_APPROVING_SIGNATURE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ASSISTANT_SUBSTITUE_TYPE \" +\n    \"SYSRES_CONST_ASSISTANT_SUBSTITUE_TYPE_CODE \" +\n    \"SYSRES_CONST_ATTACH_TYPE_COMPONENT_TOKEN \" +\n    \"SYSRES_CONST_ATTACH_TYPE_DOC \" +\n    \"SYSRES_CONST_ATTACH_TYPE_EDOC \" +\n    \"SYSRES_CONST_ATTACH_TYPE_FOLDER \" +\n    \"SYSRES_CONST_ATTACH_TYPE_JOB \" +\n    \"SYSRES_CONST_ATTACH_TYPE_REFERENCE \" +\n    \"SYSRES_CONST_ATTACH_TYPE_TASK \" +\n    \"SYSRES_CONST_AUTH_ENCODED_PASSWORD \" +\n    \"SYSRES_CONST_AUTH_ENCODED_PASSWORD_CODE \" +\n    \"SYSRES_CONST_AUTH_NOVELL \" +\n    \"SYSRES_CONST_AUTH_PASSWORD \" +\n    \"SYSRES_CONST_AUTH_PASSWORD_CODE \" +\n    \"SYSRES_CONST_AUTH_WINDOWS \" +\n    \"SYSRES_CONST_AUTHENTICATING_SIGNATURE_NAME \" +\n    \"SYSRES_CONST_AUTHENTICATING_SIGNATURE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_AUTO_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_AUTO_NUMERATION_CODE \" +\n    \"SYSRES_CONST_AUTO_STRONG_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_AUTOTEXT_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_TEXT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_ALL \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_ALL_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_SIGN \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_SIGN_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_WORK \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_WORK_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USE_ANYWHERE_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USE_ON_SIGNING_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USE_ON_WORK_CODE \" +\n    \"SYSRES_CONST_BEGIN_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_BLACK_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_BLUE_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_BTN_PART \" +\n    \"SYSRES_CONST_CALCULATED_ROLE_TYPE_CODE \" +\n    \"SYSRES_CONST_CALL_TYPE_VARIABLE_BUTTON_VALUE \" +\n    \"SYSRES_CONST_CALL_TYPE_VARIABLE_PROGRAM_VALUE \" +\n    \"SYSRES_CONST_CANCEL_MESSAGE_FUNCTION_RESULT \" +\n    \"SYSRES_CONST_CARD_PART \" +\n    \"SYSRES_CONST_CARD_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_ENCRYPT_VALUE \" +\n    \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_SIGN_AND_ENCRYPT_VALUE \" +\n    \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_SIGN_VALUE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_DATE_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_FLOAT_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_INTEGER_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_PICK_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_REEFRENCE_PARAM_TYPE \" +\n    \"SYSRES_CONST_CLOSED_RECORD_FLAG_VALUE_FEMININE \" +\n    \"SYSRES_CONST_CLOSED_RECORD_FLAG_VALUE_MASCULINE \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_ADMIN \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_DEVELOPER \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_DOCS \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_EDOC_CARDS \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_EXTERNAL_EXECUTABLE \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_OTHER \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_REFERENCE \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_REPORT \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_SCRIPT \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_URL \" +\n    \"SYSRES_CONST_CODE_REQUISITE_ACCESS \" +\n    \"SYSRES_CONST_CODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_CODE_REQUISITE_COMPONENT \" +\n    \"SYSRES_CONST_CODE_REQUISITE_DESCRIPTION \" +\n    \"SYSRES_CONST_CODE_REQUISITE_EXCLUDE_COMPONENT \" +\n    \"SYSRES_CONST_CODE_REQUISITE_RECORD \" +\n    \"SYSRES_CONST_COMMENT_REQ_CODE \" +\n    \"SYSRES_CONST_COMMON_SETTINGS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_COMP_CODE_GRD \" +\n    \"SYSRES_CONST_COMPONENT_GROUP_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_ADMIN_COMPONENTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_DEVELOPER_COMPONENTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_DOCS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_EDOC_CARDS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_EDOCS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_EXTERNAL_EXECUTABLE \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_OTHER \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_REFERENCE_TYPES \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_REFERENCES \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_REPORTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_SCRIPTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_URL \" +\n    \"SYSRES_CONST_COMPONENTS_REMOTE_SERVERS_VIEW_CODE \" +\n    \"SYSRES_CONST_CONDITION_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_CONST_FIRM_STATUS_COMMON \" +\n    \"SYSRES_CONST_CONST_FIRM_STATUS_INDIVIDUAL \" +\n    \"SYSRES_CONST_CONST_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_CONST_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_CONST_SERVER_STATUS_DONT_REPLICATE \" +\n    \"SYSRES_CONST_CONST_SERVER_STATUS_REPLICATE \" +\n    \"SYSRES_CONST_CONTENTS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DATA_TYPE_BOOLEAN \" +\n    \"SYSRES_CONST_DATA_TYPE_DATE \" +\n    \"SYSRES_CONST_DATA_TYPE_FLOAT \" +\n    \"SYSRES_CONST_DATA_TYPE_INTEGER \" +\n    \"SYSRES_CONST_DATA_TYPE_PICK \" +\n    \"SYSRES_CONST_DATA_TYPE_REFERENCE \" +\n    \"SYSRES_CONST_DATA_TYPE_STRING \" +\n    \"SYSRES_CONST_DATA_TYPE_TEXT \" +\n    \"SYSRES_CONST_DATA_TYPE_VARIANT \" +\n    \"SYSRES_CONST_DATE_CLOSE_REQ_CODE \" +\n    \"SYSRES_CONST_DATE_FORMAT_DATE_ONLY_CHAR \" +\n    \"SYSRES_CONST_DATE_OPEN_REQ_CODE \" +\n    \"SYSRES_CONST_DATE_REQUISITE \" +\n    \"SYSRES_CONST_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_DATE_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_DATE_TYPE_CHAR \" +\n    \"SYSRES_CONST_DATETIME_FORMAT_VALUE \" +\n    \"SYSRES_CONST_DEA_ACCESS_RIGHTS_ACTION_CODE \" +\n    \"SYSRES_CONST_DESCRIPTION_LOCALIZE_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DESCRIPTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DET1_PART \" +\n    \"SYSRES_CONST_DET2_PART \" +\n    \"SYSRES_CONST_DET3_PART \" +\n    \"SYSRES_CONST_DET4_PART \" +\n    \"SYSRES_CONST_DET5_PART \" +\n    \"SYSRES_CONST_DET6_PART \" +\n    \"SYSRES_CONST_DETAIL_DATASET_KEY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DETAIL_PICK_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DETAIL_REQ_CODE \" +\n    \"SYSRES_CONST_DO_NOT_USE_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_DO_NOT_USE_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_DO_NOT_USE_ON_VIEW_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_DO_NOT_USE_ON_VIEW_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_DOCUMENT_STORAGES_CODE \" +\n    \"SYSRES_CONST_DOCUMENT_TEMPLATES_TYPE_NAME \" +\n    \"SYSRES_CONST_DOUBLE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITOR_CLOSE_FILE_OBSERV_TYPE_CODE \" +\n    \"SYSRES_CONST_EDITOR_CLOSE_PROCESS_OBSERV_TYPE_CODE \" +\n    \"SYSRES_CONST_EDITOR_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_APPLICATION_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_CREATE_SEVERAL_PROCESSES_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_EXTENSION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_OBSERVER_BY_PROCESS_TYPE \" +\n    \"SYSRES_CONST_EDITORS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_EDITORS_REPLACE_SPEC_CHARS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_USE_PLUGINS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_VIEW_DOCUMENT_OPENED_TO_EDIT_CODE \" +\n    \"SYSRES_CONST_EDOC_CARD_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_CARD_TYPES_LINK_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_CERTIFICATE_AND_PASSWORD_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_CERTIFICATE_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_KIND_REFERENCE_CODE \" +\n    \"SYSRES_CONST_EDOC_KINDS_BY_TEMPLATE_ACTION_CODE \" +\n    \"SYSRES_CONST_EDOC_MANAGE_ACCESS_CODE \" +\n    \"SYSRES_CONST_EDOC_NONE_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_PASSWORD_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_READONLY_ACCESS_CODE \" +\n    \"SYSRES_CONST_EDOC_SHELL_LIFE_TYPE_VIEW_VALUE \" +\n    \"SYSRES_CONST_EDOC_SIZE_RESTRICTION_PRIORITY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_CHECK_ACCESS_RIGHTS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_COMPUTER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_DATABASE_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_EDIT_IN_STORAGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_LOCAL_PATH_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_SHARED_SOURCE_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_TEMPLATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_TYPES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_EDOC_VERSION_ACTIVE_STAGE_CODE \" +\n    \"SYSRES_CONST_EDOC_VERSION_DESIGN_STAGE_CODE \" +\n    \"SYSRES_CONST_EDOC_VERSION_OBSOLETE_STAGE_CODE \" +\n    \"SYSRES_CONST_EDOC_WRITE_ACCES_CODE \" +\n    \"SYSRES_CONST_EDOCUMENT_CARD_REQUISITES_REFERENCE_CODE_SELECTED_REQUISITE \" +\n    \"SYSRES_CONST_ENCODE_CERTIFICATE_TYPE_CODE \" +\n    \"SYSRES_CONST_END_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ENUMERATION_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EXECUTE_ACCESS_RIGHTS_TYPE_CODE \" +\n    \"SYSRES_CONST_EXECUTIVE_FILE_STORAGE_TYPE \" +\n    \"SYSRES_CONST_EXIST_CONST \" +\n    \"SYSRES_CONST_EXIST_VALUE \" +\n    \"SYSRES_CONST_EXPORT_LOCK_TYPE_ASK \" +\n    \"SYSRES_CONST_EXPORT_LOCK_TYPE_WITH_LOCK \" +\n    \"SYSRES_CONST_EXPORT_LOCK_TYPE_WITHOUT_LOCK \" +\n    \"SYSRES_CONST_EXPORT_VERSION_TYPE_ASK \" +\n    \"SYSRES_CONST_EXPORT_VERSION_TYPE_LAST \" +\n    \"SYSRES_CONST_EXPORT_VERSION_TYPE_LAST_ACTIVE \" +\n    \"SYSRES_CONST_EXTENSION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_FILTER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_FILTER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_FILTER_TYPE_COMMON_CODE \" +\n    \"SYSRES_CONST_FILTER_TYPE_COMMON_NAME \" +\n    \"SYSRES_CONST_FILTER_TYPE_USER_CODE \" +\n    \"SYSRES_CONST_FILTER_TYPE_USER_NAME \" +\n    \"SYSRES_CONST_FILTER_VALUE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_FLOAT_NUMBER_FORMAT_CHAR \" +\n    \"SYSRES_CONST_FLOAT_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_FOLDER_AUTHOR_VALUE \" +\n    \"SYSRES_CONST_FOLDER_KIND_ANY_OBJECTS \" +\n    \"SYSRES_CONST_FOLDER_KIND_COMPONENTS \" +\n    \"SYSRES_CONST_FOLDER_KIND_EDOCS \" +\n    \"SYSRES_CONST_FOLDER_KIND_JOBS \" +\n    \"SYSRES_CONST_FOLDER_KIND_TASKS \" +\n    \"SYSRES_CONST_FOLDER_TYPE_COMMON \" +\n    \"SYSRES_CONST_FOLDER_TYPE_COMPONENT \" +\n    \"SYSRES_CONST_FOLDER_TYPE_FAVORITES \" +\n    \"SYSRES_CONST_FOLDER_TYPE_INBOX \" +\n    \"SYSRES_CONST_FOLDER_TYPE_OUTBOX \" +\n    \"SYSRES_CONST_FOLDER_TYPE_QUICK_LAUNCH \" +\n    \"SYSRES_CONST_FOLDER_TYPE_SEARCH \" +\n    \"SYSRES_CONST_FOLDER_TYPE_SHORTCUTS \" +\n    \"SYSRES_CONST_FOLDER_TYPE_USER \" +\n    \"SYSRES_CONST_FROM_DICTIONARY_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_FULL_SUBSTITUTE_TYPE \" +\n    \"SYSRES_CONST_FULL_SUBSTITUTE_TYPE_CODE \" +\n    \"SYSRES_CONST_FUNCTION_CANCEL_RESULT \" +\n    \"SYSRES_CONST_FUNCTION_CATEGORY_SYSTEM \" +\n    \"SYSRES_CONST_FUNCTION_CATEGORY_USER \" +\n    \"SYSRES_CONST_FUNCTION_FAILURE_RESULT \" +\n    \"SYSRES_CONST_FUNCTION_SAVE_RESULT \" +\n    \"SYSRES_CONST_GENERATED_REQUISITE \" +\n    \"SYSRES_CONST_GREEN_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_GROUP_ACCOUNT_TYPE_VALUE_CODE \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_NORMAL_CODE \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_NORMAL_NAME \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_SERVICE_CODE \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_SERVICE_NAME \" +\n    \"SYSRES_CONST_GROUP_COMMON_CATEGORY_FIELD_VALUE \" +\n    \"SYSRES_CONST_GROUP_FULL_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_RIGHTS_T_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_SERVER_CODES_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_SERVER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_SERVICE_CATEGORY_FIELD_VALUE \" +\n    \"SYSRES_CONST_GROUP_USER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUPS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_GROUPS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_HIDDEN_MODE_NAME \" +\n    \"SYSRES_CONST_HIGH_LVL_REQUISITE_CODE \" +\n    \"SYSRES_CONST_HISTORY_ACTION_CREATE_CODE \" +\n    \"SYSRES_CONST_HISTORY_ACTION_DELETE_CODE \" +\n    \"SYSRES_CONST_HISTORY_ACTION_EDIT_CODE \" +\n    \"SYSRES_CONST_HOUR_CHAR \" +\n    \"SYSRES_CONST_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_IDSPS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_IMAGE_MODE_COLOR \" +\n    \"SYSRES_CONST_IMAGE_MODE_GREYSCALE \" +\n    \"SYSRES_CONST_IMAGE_MODE_MONOCHROME \" +\n    \"SYSRES_CONST_IMPORTANCE_HIGH \" +\n    \"SYSRES_CONST_IMPORTANCE_LOW \" +\n    \"SYSRES_CONST_IMPORTANCE_NORMAL \" +\n    \"SYSRES_CONST_IN_DESIGN_VERSION_STATE_PICK_VALUE \" +\n    \"SYSRES_CONST_INCOMING_WORK_RULE_TYPE_CODE \" +\n    \"SYSRES_CONST_INT_REQUISITE \" +\n    \"SYSRES_CONST_INT_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_INTEGER_NUMBER_FORMAT_CHAR \" +\n    \"SYSRES_CONST_INTEGER_TYPE_CHAR \" +\n    \"SYSRES_CONST_IS_GENERATED_REQUISITE_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_IS_PUBLIC_ROLE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_IS_REMOTE_USER_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_IS_REMOTE_USER_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_IS_STORED_REQUISITE_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_IS_STORED_REQUISITE_STORED_VALUE \" +\n    \"SYSRES_CONST_ITALIC_LIFE_CYCLE_STAGE_DRAW_STYLE \" +\n    \"SYSRES_CONST_JOB_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_JOB_KIND_CONTROL_JOB \" +\n    \"SYSRES_CONST_JOB_KIND_JOB \" +\n    \"SYSRES_CONST_JOB_KIND_NOTICE \" +\n    \"SYSRES_CONST_JOB_STATE_ABORTED \" +\n    \"SYSRES_CONST_JOB_STATE_COMPLETE \" +\n    \"SYSRES_CONST_JOB_STATE_WORKING \" +\n    \"SYSRES_CONST_KIND_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KIND_REQUISITE_NAME \" +\n    \"SYSRES_CONST_KINDS_CREATE_SHADOW_COPIES_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_DEFAULT_EDOC_LIFE_STAGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_ALL_TEPLATES_ALLOWED_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_ALLOW_LIFE_CYCLE_STAGE_CHANGING_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_ALLOW_MULTIPLE_ACTIVE_VERSIONS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_SHARE_ACCES_RIGHTS_BY_DEFAULT_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_TEMPLATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_SIGNERS_REQUISITES_CODE \" +\n    \"SYSRES_CONST_KOD_INPUT_TYPE \" +\n    \"SYSRES_CONST_LAST_UPDATE_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_LIFE_CYCLE_START_STAGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_LILAC_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_COMPONENT \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_DOCUMENT \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_EDOC \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_FOLDER \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_JOB \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_REFERENCE \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_TASK \" +\n    \"SYSRES_CONST_LINK_REF_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_LIST_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_LOCALIZATION_DICTIONARY_MAIN_VIEW_CODE \" +\n    \"SYSRES_CONST_MAIN_VIEW_CODE \" +\n    \"SYSRES_CONST_MANUAL_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_MASTER_COMP_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_MASTER_TABLE_REC_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_MAXIMIZED_MODE_NAME \" +\n    \"SYSRES_CONST_ME_VALUE \" +\n    \"SYSRES_CONST_MESSAGE_ATTENTION_CAPTION \" +\n    \"SYSRES_CONST_MESSAGE_CONFIRMATION_CAPTION \" +\n    \"SYSRES_CONST_MESSAGE_ERROR_CAPTION \" +\n    \"SYSRES_CONST_MESSAGE_INFORMATION_CAPTION \" +\n    \"SYSRES_CONST_MINIMIZED_MODE_NAME \" +\n    \"SYSRES_CONST_MINUTE_CHAR \" +\n    \"SYSRES_CONST_MODULE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_MONITORING_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_MONTH_FORMAT_VALUE \" +\n    \"SYSRES_CONST_NAME_LOCALIZE_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NAME_SINGULAR_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NAMEAN_INPUT_TYPE \" +\n    \"SYSRES_CONST_NEGATIVE_PICK_VALUE \" +\n    \"SYSRES_CONST_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_NO \" +\n    \"SYSRES_CONST_NO_PICK_VALUE \" +\n    \"SYSRES_CONST_NO_SIGNATURE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NO_VALUE \" +\n    \"SYSRES_CONST_NONE_ACCESS_RIGHTS_TYPE_CODE \" +\n    \"SYSRES_CONST_NONOPERATING_RECORD_FLAG_VALUE \" +\n    \"SYSRES_CONST_NONOPERATING_RECORD_FLAG_VALUE_MASCULINE \" +\n    \"SYSRES_CONST_NORMAL_ACCESS_RIGHTS_TYPE_CODE \" +\n    \"SYSRES_CONST_NORMAL_LIFE_CYCLE_STAGE_DRAW_STYLE \" +\n    \"SYSRES_CONST_NORMAL_MODE_NAME \" +\n    \"SYSRES_CONST_NOT_ALLOWED_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_NOT_ALLOWED_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_NOTE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NOTICE_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_NUM_REQUISITE \" +\n    \"SYSRES_CONST_NUM_STR_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NUMERATION_AUTO_NOT_STRONG \" +\n    \"SYSRES_CONST_NUMERATION_AUTO_STRONG \" +\n    \"SYSRES_CONST_NUMERATION_FROM_DICTONARY \" +\n    \"SYSRES_CONST_NUMERATION_MANUAL \" +\n    \"SYSRES_CONST_NUMERIC_TYPE_CHAR \" +\n    \"SYSRES_CONST_NUMREQ_REQUISITE_CODE \" +\n    \"SYSRES_CONST_OBSOLETE_VERSION_STATE_PICK_VALUE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_FEMININE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_MASCULINE \" +\n    \"SYSRES_CONST_OPTIONAL_FORM_COMP_REQCODE_PREFIX \" +\n    \"SYSRES_CONST_ORANGE_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_ORIGINALREF_REQUISITE_CODE \" +\n    \"SYSRES_CONST_OURFIRM_REF_CODE \" +\n    \"SYSRES_CONST_OURFIRM_REQUISITE_CODE \" +\n    \"SYSRES_CONST_OURFIRM_VAR \" +\n    \"SYSRES_CONST_OUTGOING_WORK_RULE_TYPE_CODE \" +\n    \"SYSRES_CONST_PICK_NEGATIVE_RESULT \" +\n    \"SYSRES_CONST_PICK_POSITIVE_RESULT \" +\n    \"SYSRES_CONST_PICK_REQUISITE \" +\n    \"SYSRES_CONST_PICK_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_PICK_TYPE_CHAR \" +\n    \"SYSRES_CONST_PLAN_STATUS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_PLATFORM_VERSION_COMMENT \" +\n    \"SYSRES_CONST_PLUGINS_SETTINGS_DESCRIPTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_POSITIVE_PICK_VALUE \" +\n    \"SYSRES_CONST_POWER_TO_CREATE_ACTION_CODE \" +\n    \"SYSRES_CONST_POWER_TO_SIGN_ACTION_CODE \" +\n    \"SYSRES_CONST_PRIORITY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_QUALIFIED_TASK_TYPE \" +\n    \"SYSRES_CONST_QUALIFIED_TASK_TYPE_CODE \" +\n    \"SYSRES_CONST_RECSTAT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_RED_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_REF_ID_T_REF_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REF_REQUISITE \" +\n    \"SYSRES_CONST_REF_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_REF_REQUISITES_REFERENCE_CODE_SELECTED_REQUISITE \" +\n    \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_CREATE_ACTION_CODE \" +\n    \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_DELETE_ACTION_CODE \" +\n    \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_MODIFY_ACTION_CODE \" +\n    \"SYSRES_CONST_REFERENCE_TYPE_CHAR \" +\n    \"SYSRES_CONST_REFERENCE_TYPE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_REFERENCES_ADD_PARAMS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REFERENCES_DISPLAY_REQUISITE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REMOTE_SERVER_STATUS_WORKING \" +\n    \"SYSRES_CONST_REMOTE_SERVER_TYPE_MAIN \" +\n    \"SYSRES_CONST_REMOTE_SERVER_TYPE_SECONDARY \" +\n    \"SYSRES_CONST_REMOTE_USER_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_REPORT_APP_EDITOR_INTERNAL \" +\n    \"SYSRES_CONST_REPORT_BASE_REPORT_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_BASE_REPORT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_SCRIPT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_TEMPLATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_VIEWER_CODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REQ_ALLOW_COMPONENT_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_REQ_ALLOW_RECORD_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_REQ_ALLOW_SERVER_COMPONENT_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_REQ_MODE_AVAILABLE_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_EDIT_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_HIDDEN_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_NOT_AVAILABLE_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_VIEW_CODE \" +\n    \"SYSRES_CONST_REQ_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REQ_SECTION_VALUE \" +\n    \"SYSRES_CONST_REQ_TYPE_VALUE \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_BY_UNIT \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_DATE_FULL \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_DATE_TIME \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_LEFT \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_RIGHT \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_WITHOUT_UNIT \" +\n    \"SYSRES_CONST_REQUISITE_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_ACTIONS \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_BUTTON \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_BUTTONS \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_CARD \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE10 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE11 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE12 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE13 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE14 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE15 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE16 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE17 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE18 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE19 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE2 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE20 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE21 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE22 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE23 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE24 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE3 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE4 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE5 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE6 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE7 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE8 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE9 \" +\n    \"SYSRES_CONST_REQUISITES_PSEUDOREFERENCE_REQUISITE_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_RIGHT_ALIGNMENT_CODE \" +\n    \"SYSRES_CONST_ROLES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_ROUTE_STEP_AFTER_RUS \" +\n    \"SYSRES_CONST_ROUTE_STEP_AND_CONDITION_RUS \" +\n    \"SYSRES_CONST_ROUTE_STEP_OR_CONDITION_RUS \" +\n    \"SYSRES_CONST_ROUTE_TYPE_COMPLEX \" +\n    \"SYSRES_CONST_ROUTE_TYPE_PARALLEL \" +\n    \"SYSRES_CONST_ROUTE_TYPE_SERIAL \" +\n    \"SYSRES_CONST_SBDATASETDESC_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_SBDATASETDESC_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_SBVIEWSDESC_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_SCRIPT_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_SEARCH_BY_TEXT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SEARCHES_COMPONENT_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_CRITERIA_ACTION_NAME \" +\n    \"SYSRES_CONST_SEARCHES_EDOC_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_FOLDER_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_JOB_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_SEARCHES_TASK_CONTENT \" +\n    \"SYSRES_CONST_SECOND_CHAR \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_ACTIONS_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_CARD_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_1_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_2_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_3_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_4_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_5_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_6_VALUE \" +\n    \"SYSRES_CONST_SELECT_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_SELECT_TYPE_SELECTABLE \" +\n    \"SYSRES_CONST_SELECT_TYPE_SELECTABLE_ONLY_CHILD \" +\n    \"SYSRES_CONST_SELECT_TYPE_SELECTABLE_WITH_CHILD \" +\n    \"SYSRES_CONST_SELECT_TYPE_UNSLECTABLE \" +\n    \"SYSRES_CONST_SERVER_TYPE_MAIN \" +\n    \"SYSRES_CONST_SERVICE_USER_CATEGORY_FIELD_VALUE \" +\n    \"SYSRES_CONST_SETTINGS_USER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SIGNATURE_AND_ENCODE_CERTIFICATE_TYPE_CODE \" +\n    \"SYSRES_CONST_SIGNATURE_CERTIFICATE_TYPE_CODE \" +\n    \"SYSRES_CONST_SINGULAR_TITLE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SQL_SERVER_AUTHENTIFICATION_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_SQL_SERVER_ENCODE_AUTHENTIFICATION_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_STANDART_ROUTE_REFERENCE_CODE \" +\n    \"SYSRES_CONST_STANDART_ROUTE_REFERENCE_COMMENT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STANDART_ROUTES_GROUPS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_STATE_REQ_NAME \" +\n    \"SYSRES_CONST_STATE_REQUISITE_ACTIVE_VALUE \" +\n    \"SYSRES_CONST_STATE_REQUISITE_CLOSED_VALUE \" +\n    \"SYSRES_CONST_STATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STATIC_ROLE_TYPE_CODE \" +\n    \"SYSRES_CONST_STATUS_PLAN_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_STATUS_VALUE_AUTOCLEANING \" +\n    \"SYSRES_CONST_STATUS_VALUE_BLUE_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_COMPLETE \" +\n    \"SYSRES_CONST_STATUS_VALUE_GREEN_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_ORANGE_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_PURPLE_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_RED_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_SUSPEND \" +\n    \"SYSRES_CONST_STATUS_VALUE_YELLOW_SQUARE \" +\n    \"SYSRES_CONST_STDROUTE_SHOW_TO_USERS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STORAGE_TYPE_FILE \" +\n    \"SYSRES_CONST_STORAGE_TYPE_SQL_SERVER \" +\n    \"SYSRES_CONST_STR_REQUISITE \" +\n    \"SYSRES_CONST_STRIKEOUT_LIFE_CYCLE_STAGE_DRAW_STYLE \" +\n    \"SYSRES_CONST_STRING_FORMAT_LEFT_ALIGN_CHAR \" +\n    \"SYSRES_CONST_STRING_FORMAT_RIGHT_ALIGN_CHAR \" +\n    \"SYSRES_CONST_STRING_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STRING_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_STRING_TYPE_CHAR \" +\n    \"SYSRES_CONST_SUBSTITUTES_PSEUDOREFERENCE_CODE \" +\n    \"SYSRES_CONST_SUBTASK_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_SYSTEM_SETTING_CURRENT_USER_PARAM_VALUE \" +\n    \"SYSRES_CONST_SYSTEM_SETTING_EMPTY_VALUE_PARAM_VALUE \" +\n    \"SYSRES_CONST_SYSTEM_VERSION_COMMENT \" +\n    \"SYSRES_CONST_TASK_ACCESS_TYPE_ALL \" +\n    \"SYSRES_CONST_TASK_ACCESS_TYPE_ALL_MEMBERS \" +\n    \"SYSRES_CONST_TASK_ACCESS_TYPE_MANUAL \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_CERTIFICATION \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_CERTIFICATION_AND_PASSWORD \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_NONE \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_PASSWORD \" +\n    \"SYSRES_CONST_TASK_ROUTE_ALL_CONDITION \" +\n    \"SYSRES_CONST_TASK_ROUTE_AND_CONDITION \" +\n    \"SYSRES_CONST_TASK_ROUTE_OR_CONDITION \" +\n    \"SYSRES_CONST_TASK_STATE_ABORTED \" +\n    \"SYSRES_CONST_TASK_STATE_COMPLETE \" +\n    \"SYSRES_CONST_TASK_STATE_CONTINUED \" +\n    \"SYSRES_CONST_TASK_STATE_CONTROL \" +\n    \"SYSRES_CONST_TASK_STATE_INIT \" +\n    \"SYSRES_CONST_TASK_STATE_WORKING \" +\n    \"SYSRES_CONST_TASK_TITLE \" +\n    \"SYSRES_CONST_TASK_TYPES_GROUPS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_TASK_TYPES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_TEMPLATES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_TEST_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_TEST_DEV_DATABASE_NAME \" +\n    \"SYSRES_CONST_TEST_DEV_SYSTEM_CODE \" +\n    \"SYSRES_CONST_TEST_EDMS_DATABASE_NAME \" +\n    \"SYSRES_CONST_TEST_EDMS_MAIN_CODE \" +\n    \"SYSRES_CONST_TEST_EDMS_MAIN_DB_NAME \" +\n    \"SYSRES_CONST_TEST_EDMS_SECOND_CODE \" +\n    \"SYSRES_CONST_TEST_EDMS_SECOND_DB_NAME \" +\n    \"SYSRES_CONST_TEST_EDMS_SYSTEM_CODE \" +\n    \"SYSRES_CONST_TEST_NUMERIC_REQUISITE_NAME \" +\n    \"SYSRES_CONST_TEXT_REQUISITE \" +\n    \"SYSRES_CONST_TEXT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_TEXT_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_TEXT_TYPE_CHAR \" +\n    \"SYSRES_CONST_TYPE_CODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNDEFINED_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_UNITS_SECTION_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNITS_SECTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNOPERATING_RECORD_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_UNSTORED_DATA_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNSTORED_DATA_REQUISITE_NAME \" +\n    \"SYSRES_CONST_USE_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_USE_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_USER_ACCOUNT_TYPE_VALUE_CODE \" +\n    \"SYSRES_CONST_USER_ADDITIONAL_INFORMATION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_AND_GROUP_ID_FROM_PSEUDOREFERENCE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CATEGORY_NORMAL \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_STATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_SUBJECT_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_THUMBPRINT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_COMMON_CATEGORY \" +\n    \"SYSRES_CONST_USER_COMMON_CATEGORY_CODE \" +\n    \"SYSRES_CONST_USER_FULL_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_GROUP_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_LOGIN_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_REMOTE_CONTROLLER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_REMOTE_SYSTEM_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_RIGHTS_T_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_SERVER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_SERVICE_CATEGORY \" +\n    \"SYSRES_CONST_USER_SERVICE_CATEGORY_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_ADMINISTRATOR_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_ADMINISTRATOR_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_DEVELOPER_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_DEVELOPER_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_DISABLED_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_DISABLED_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_SYSTEM_DEVELOPER_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_USER_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_USER_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_USER_NAME_DEPRECATED \" +\n    \"SYSRES_CONST_USER_TYPE_FIELD_VALUE_USER \" +\n    \"SYSRES_CONST_USER_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_CONTROLLER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_IS_MAIN_SERVER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_USERS_REGISTRATION_CERTIFICATES_ACTION_NAME \" +\n    \"SYSRES_CONST_USERS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_SYSTEM_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_ACCESS_RIGHTS_TYPR_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_AUTHENTICATION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_COMPONENT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_GROUP_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_VIEW_CERTIFICATES_ACTION_NAME \" +\n    \"SYSRES_CONST_VIEW_DEFAULT_CODE \" +\n    \"SYSRES_CONST_VIEW_DEFAULT_NAME \" +\n    \"SYSRES_CONST_VIEWER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_WAITING_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_WIZARD_FORM_LABEL_TEST_STRING  \" +\n    \"SYSRES_CONST_WIZARD_QUERY_PARAM_HEIGHT_ETALON_STRING \" +\n    \"SYSRES_CONST_WIZARD_REFERENCE_COMMENT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_WORK_RULES_DESCRIPTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_WORK_TIME_CALENDAR_REFERENCE_CODE \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE_CODE \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE_CODE_RUS \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_SOFT_ROUTE_TYPE_VALUE_CODE_RUS \" +\n    \"SYSRES_CONST_WORKFLOW_ROUTE_TYPR_HARD \" +\n    \"SYSRES_CONST_WORKFLOW_ROUTE_TYPR_SOFT \" +\n    \"SYSRES_CONST_XML_ENCODING \" +\n    \"SYSRES_CONST_XREC_STAT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_XRECID_FIELD_NAME \" +\n    \"SYSRES_CONST_YES \" +\n    \"SYSRES_CONST_YES_NO_2_REQUISITE_CODE \" +\n    \"SYSRES_CONST_YES_NO_REQUISITE_CODE \" +\n    \"SYSRES_CONST_YES_NO_T_REF_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_YES_PICK_VALUE \" +\n    \"SYSRES_CONST_YES_VALUE \";\n\n  // Base constant\n  const base_constants = \"CR FALSE nil NO_VALUE NULL TAB TRUE YES_VALUE \";\n\n  // Base group name\n  const base_group_name_constants =\n    \"ADMINISTRATORS_GROUP_NAME CUSTOMIZERS_GROUP_NAME DEVELOPERS_GROUP_NAME SERVICE_USERS_GROUP_NAME \";\n\n  // Decision block properties\n  const decision_block_properties_constants =\n    \"DECISION_BLOCK_FIRST_OPERAND_PROPERTY DECISION_BLOCK_NAME_PROPERTY DECISION_BLOCK_OPERATION_PROPERTY \" +\n    \"DECISION_BLOCK_RESULT_TYPE_PROPERTY DECISION_BLOCK_SECOND_OPERAND_PROPERTY \";\n\n  // File extension\n  const file_extension_constants =\n    \"ANY_FILE_EXTENTION COMPRESSED_DOCUMENT_EXTENSION EXTENDED_DOCUMENT_EXTENSION \" +\n    \"SHORT_COMPRESSED_DOCUMENT_EXTENSION SHORT_EXTENDED_DOCUMENT_EXTENSION \";\n\n  // Job block properties\n  const job_block_properties_constants =\n    \"JOB_BLOCK_ABORT_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_AFTER_FINISH_EVENT \" +\n    \"JOB_BLOCK_AFTER_QUERY_PARAMETERS_EVENT \" +\n    \"JOB_BLOCK_ATTACHMENT_PROPERTY \" +\n    \"JOB_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" +\n    \"JOB_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" +\n    \"JOB_BLOCK_BEFORE_QUERY_PARAMETERS_EVENT \" +\n    \"JOB_BLOCK_BEFORE_START_EVENT \" +\n    \"JOB_BLOCK_CREATED_JOBS_PROPERTY \" +\n    \"JOB_BLOCK_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_EXECUTION_RESULTS_PROPERTY \" +\n    \"JOB_BLOCK_IS_PARALLEL_PROPERTY \" +\n    \"JOB_BLOCK_IS_RELATIVE_ABORT_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_JOB_TEXT_PROPERTY \" +\n    \"JOB_BLOCK_NAME_PROPERTY \" +\n    \"JOB_BLOCK_NEED_SIGN_ON_PERFORM_PROPERTY \" +\n    \"JOB_BLOCK_PERFORMER_PROPERTY \" +\n    \"JOB_BLOCK_RELATIVE_ABORT_DEADLINE_TYPE_PROPERTY \" +\n    \"JOB_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"JOB_BLOCK_SUBJECT_PROPERTY \";\n\n  // Language code\n  const language_code_constants = \"ENGLISH_LANGUAGE_CODE RUSSIAN_LANGUAGE_CODE \";\n\n  // Launching external applications\n  const launching_external_applications_constants =\n    \"smHidden smMaximized smMinimized smNormal wmNo wmYes \";\n\n  // Link kind\n  const link_kind_constants =\n    \"COMPONENT_TOKEN_LINK_KIND \" +\n    \"DOCUMENT_LINK_KIND \" +\n    \"EDOCUMENT_LINK_KIND \" +\n    \"FOLDER_LINK_KIND \" +\n    \"JOB_LINK_KIND \" +\n    \"REFERENCE_LINK_KIND \" +\n    \"TASK_LINK_KIND \";\n\n  // Lock type\n  const lock_type_constants =\n    \"COMPONENT_TOKEN_LOCK_TYPE EDOCUMENT_VERSION_LOCK_TYPE \";\n\n  // Monitor block properties\n  const monitor_block_properties_constants =\n    \"MONITOR_BLOCK_AFTER_FINISH_EVENT \" +\n    \"MONITOR_BLOCK_BEFORE_START_EVENT \" +\n    \"MONITOR_BLOCK_DEADLINE_PROPERTY \" +\n    \"MONITOR_BLOCK_INTERVAL_PROPERTY \" +\n    \"MONITOR_BLOCK_INTERVAL_TYPE_PROPERTY \" +\n    \"MONITOR_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"MONITOR_BLOCK_NAME_PROPERTY \" +\n    \"MONITOR_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"MONITOR_BLOCK_SEARCH_SCRIPT_PROPERTY \";\n\n  // Notice block properties\n  const notice_block_properties_constants =\n    \"NOTICE_BLOCK_AFTER_FINISH_EVENT \" +\n    \"NOTICE_BLOCK_ATTACHMENT_PROPERTY \" +\n    \"NOTICE_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" +\n    \"NOTICE_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" +\n    \"NOTICE_BLOCK_BEFORE_START_EVENT \" +\n    \"NOTICE_BLOCK_CREATED_NOTICES_PROPERTY \" +\n    \"NOTICE_BLOCK_DEADLINE_PROPERTY \" +\n    \"NOTICE_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"NOTICE_BLOCK_NAME_PROPERTY \" +\n    \"NOTICE_BLOCK_NOTICE_TEXT_PROPERTY \" +\n    \"NOTICE_BLOCK_PERFORMER_PROPERTY \" +\n    \"NOTICE_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"NOTICE_BLOCK_SUBJECT_PROPERTY \";\n\n  // Object events\n  const object_events_constants =\n    \"dseAfterCancel \" +\n    \"dseAfterClose \" +\n    \"dseAfterDelete \" +\n    \"dseAfterDeleteOutOfTransaction \" +\n    \"dseAfterInsert \" +\n    \"dseAfterOpen \" +\n    \"dseAfterScroll \" +\n    \"dseAfterUpdate \" +\n    \"dseAfterUpdateOutOfTransaction \" +\n    \"dseBeforeCancel \" +\n    \"dseBeforeClose \" +\n    \"dseBeforeDelete \" +\n    \"dseBeforeDetailUpdate \" +\n    \"dseBeforeInsert \" +\n    \"dseBeforeOpen \" +\n    \"dseBeforeUpdate \" +\n    \"dseOnAnyRequisiteChange \" +\n    \"dseOnCloseRecord \" +\n    \"dseOnDeleteError \" +\n    \"dseOnOpenRecord \" +\n    \"dseOnPrepareUpdate \" +\n    \"dseOnUpdateError \" +\n    \"dseOnUpdateRatifiedRecord \" +\n    \"dseOnValidDelete \" +\n    \"dseOnValidUpdate \" +\n    \"reOnChange \" +\n    \"reOnChangeValues \" +\n    \"SELECTION_BEGIN_ROUTE_EVENT \" +\n    \"SELECTION_END_ROUTE_EVENT \";\n\n  // Object params\n  const object_params_constants =\n    \"CURRENT_PERIOD_IS_REQUIRED \" +\n    \"PREVIOUS_CARD_TYPE_NAME \" +\n    \"SHOW_RECORD_PROPERTIES_FORM \";\n\n  // Other\n  const other_constants =\n    \"ACCESS_RIGHTS_SETTING_DIALOG_CODE \" +\n    \"ADMINISTRATOR_USER_CODE \" +\n    \"ANALYTIC_REPORT_TYPE \" +\n    \"asrtHideLocal \" +\n    \"asrtHideRemote \" +\n    \"CALCULATED_ROLE_TYPE_CODE \" +\n    \"COMPONENTS_REFERENCE_DEVELOPER_VIEW_CODE \" +\n    \"DCTS_TEST_PROTOCOLS_FOLDER_PATH \" +\n    \"E_EDOC_VERSION_ALREADY_APPROVINGLY_SIGNED \" +\n    \"E_EDOC_VERSION_ALREADY_APPROVINGLY_SIGNED_BY_USER \" +\n    \"E_EDOC_VERSION_ALREDY_SIGNED \" +\n    \"E_EDOC_VERSION_ALREDY_SIGNED_BY_USER \" +\n    \"EDOC_TYPES_CODE_REQUISITE_FIELD_NAME \" +\n    \"EDOCUMENTS_ALIAS_NAME \" +\n    \"FILES_FOLDER_PATH \" +\n    \"FILTER_OPERANDS_DELIMITER \" +\n    \"FILTER_OPERATIONS_DELIMITER \" +\n    \"FORMCARD_NAME \" +\n    \"FORMLIST_NAME \" +\n    \"GET_EXTENDED_DOCUMENT_EXTENSION_CREATION_MODE \" +\n    \"GET_EXTENDED_DOCUMENT_EXTENSION_IMPORT_MODE \" +\n    \"INTEGRATED_REPORT_TYPE \" +\n    \"IS_BUILDER_APPLICATION_ROLE \" +\n    \"IS_BUILDER_APPLICATION_ROLE2 \" +\n    \"IS_BUILDER_USERS \" +\n    \"ISBSYSDEV \" +\n    \"LOG_FOLDER_PATH \" +\n    \"mbCancel \" +\n    \"mbNo \" +\n    \"mbNoToAll \" +\n    \"mbOK \" +\n    \"mbYes \" +\n    \"mbYesToAll \" +\n    \"MEMORY_DATASET_DESRIPTIONS_FILENAME \" +\n    \"mrNo \" +\n    \"mrNoToAll \" +\n    \"mrYes \" +\n    \"mrYesToAll \" +\n    \"MULTIPLE_SELECT_DIALOG_CODE \" +\n    \"NONOPERATING_RECORD_FLAG_FEMININE \" +\n    \"NONOPERATING_RECORD_FLAG_MASCULINE \" +\n    \"OPERATING_RECORD_FLAG_FEMININE \" +\n    \"OPERATING_RECORD_FLAG_MASCULINE \" +\n    \"PROFILING_SETTINGS_COMMON_SETTINGS_CODE_VALUE \" +\n    \"PROGRAM_INITIATED_LOOKUP_ACTION \" +\n    \"ratDelete \" +\n    \"ratEdit \" +\n    \"ratInsert \" +\n    \"REPORT_TYPE \" +\n    \"REQUIRED_PICK_VALUES_VARIABLE \" +\n    \"rmCard \" +\n    \"rmList \" +\n    \"SBRTE_PROGID_DEV \" +\n    \"SBRTE_PROGID_RELEASE \" +\n    \"STATIC_ROLE_TYPE_CODE \" +\n    \"SUPPRESS_EMPTY_TEMPLATE_CREATION \" +\n    \"SYSTEM_USER_CODE \" +\n    \"UPDATE_DIALOG_DATASET \" +\n    \"USED_IN_OBJECT_HINT_PARAM \" +\n    \"USER_INITIATED_LOOKUP_ACTION \" +\n    \"USER_NAME_FORMAT \" +\n    \"USER_SELECTION_RESTRICTIONS \" +\n    \"WORKFLOW_TEST_PROTOCOLS_FOLDER_PATH \" +\n    \"ELS_SUBTYPE_CONTROL_NAME \" +\n    \"ELS_FOLDER_KIND_CONTROL_NAME \" +\n    \"REPEAT_PROCESS_CURRENT_OBJECT_EXCEPTION_NAME \";\n\n  // Privileges\n  const privileges_constants =\n    \"PRIVILEGE_COMPONENT_FULL_ACCESS \" +\n    \"PRIVILEGE_DEVELOPMENT_EXPORT \" +\n    \"PRIVILEGE_DEVELOPMENT_IMPORT \" +\n    \"PRIVILEGE_DOCUMENT_DELETE \" +\n    \"PRIVILEGE_ESD \" +\n    \"PRIVILEGE_FOLDER_DELETE \" +\n    \"PRIVILEGE_MANAGE_ACCESS_RIGHTS \" +\n    \"PRIVILEGE_MANAGE_REPLICATION \" +\n    \"PRIVILEGE_MANAGE_SESSION_SERVER \" +\n    \"PRIVILEGE_OBJECT_FULL_ACCESS \" +\n    \"PRIVILEGE_OBJECT_VIEW \" +\n    \"PRIVILEGE_RESERVE_LICENSE \" +\n    \"PRIVILEGE_SYSTEM_CUSTOMIZE \" +\n    \"PRIVILEGE_SYSTEM_DEVELOP \" +\n    \"PRIVILEGE_SYSTEM_INSTALL \" +\n    \"PRIVILEGE_TASK_DELETE \" +\n    \"PRIVILEGE_USER_PLUGIN_SETTINGS_CUSTOMIZE \" +\n    \"PRIVILEGES_PSEUDOREFERENCE_CODE \";\n\n  // Pseudoreference code\n  const pseudoreference_code_constants =\n    \"ACCESS_TYPES_PSEUDOREFERENCE_CODE \" +\n    \"ALL_AVAILABLE_COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"ALL_AVAILABLE_PRIVILEGES_PSEUDOREFERENCE_CODE \" +\n    \"ALL_REPLICATE_COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"AVAILABLE_DEVELOPERS_COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"FILTRATER_SETTINGS_CONFLICTS_PSEUDOREFERENCE_CODE \" +\n    \"GROUPS_PSEUDOREFERENCE_CODE \" +\n    \"RECEIVE_PROTOCOL_PSEUDOREFERENCE_CODE \" +\n    \"REFERENCE_REQUISITE_PSEUDOREFERENCE_CODE \" +\n    \"REFERENCE_REQUISITES_PSEUDOREFERENCE_CODE \" +\n    \"REFTYPES_PSEUDOREFERENCE_CODE \" +\n    \"REPLICATION_SEANCES_DIARY_PSEUDOREFERENCE_CODE \" +\n    \"SEND_PROTOCOL_PSEUDOREFERENCE_CODE \" +\n    \"SUBSTITUTES_PSEUDOREFERENCE_CODE \" +\n    \"SYSTEM_SETTINGS_PSEUDOREFERENCE_CODE \" +\n    \"UNITS_PSEUDOREFERENCE_CODE \" +\n    \"USERS_PSEUDOREFERENCE_CODE \" +\n    \"VIEWERS_PSEUDOREFERENCE_CODE \";\n\n  // Requisite ISBCertificateType values\n  const requisite_ISBCertificateType_values_constants =\n    \"CERTIFICATE_TYPE_ENCRYPT \" +\n    \"CERTIFICATE_TYPE_SIGN \" +\n    \"CERTIFICATE_TYPE_SIGN_AND_ENCRYPT \";\n\n  // Requisite ISBEDocStorageType values\n  const requisite_ISBEDocStorageType_values_constants =\n    \"STORAGE_TYPE_FILE \" +\n    \"STORAGE_TYPE_NAS_CIFS \" +\n    \"STORAGE_TYPE_SAPERION \" +\n    \"STORAGE_TYPE_SQL_SERVER \";\n\n  // Requisite CompType2 values\n  const requisite_compType2_values_constants =\n    \"COMPTYPE2_REQUISITE_DOCUMENTS_VALUE \" +\n    \"COMPTYPE2_REQUISITE_TASKS_VALUE \" +\n    \"COMPTYPE2_REQUISITE_FOLDERS_VALUE \" +\n    \"COMPTYPE2_REQUISITE_REFERENCES_VALUE \";\n\n  // Requisite name\n  const requisite_name_constants =\n    \"SYSREQ_CODE \" +\n    \"SYSREQ_COMPTYPE2 \" +\n    \"SYSREQ_CONST_AVAILABLE_FOR_WEB \" +\n    \"SYSREQ_CONST_COMMON_CODE \" +\n    \"SYSREQ_CONST_COMMON_VALUE \" +\n    \"SYSREQ_CONST_FIRM_CODE \" +\n    \"SYSREQ_CONST_FIRM_STATUS \" +\n    \"SYSREQ_CONST_FIRM_VALUE \" +\n    \"SYSREQ_CONST_SERVER_STATUS \" +\n    \"SYSREQ_CONTENTS \" +\n    \"SYSREQ_DATE_OPEN \" +\n    \"SYSREQ_DATE_CLOSE \" +\n    \"SYSREQ_DESCRIPTION \" +\n    \"SYSREQ_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_DOUBLE \" +\n    \"SYSREQ_EDOC_ACCESS_TYPE \" +\n    \"SYSREQ_EDOC_AUTHOR \" +\n    \"SYSREQ_EDOC_CREATED \" +\n    \"SYSREQ_EDOC_DELEGATE_RIGHTS_REQUISITE_CODE \" +\n    \"SYSREQ_EDOC_EDITOR \" +\n    \"SYSREQ_EDOC_ENCODE_TYPE \" +\n    \"SYSREQ_EDOC_ENCRYPTION_PLUGIN_NAME \" +\n    \"SYSREQ_EDOC_ENCRYPTION_PLUGIN_VERSION \" +\n    \"SYSREQ_EDOC_EXPORT_DATE \" +\n    \"SYSREQ_EDOC_EXPORTER \" +\n    \"SYSREQ_EDOC_KIND \" +\n    \"SYSREQ_EDOC_LIFE_STAGE_NAME \" +\n    \"SYSREQ_EDOC_LOCKED_FOR_SERVER_CODE \" +\n    \"SYSREQ_EDOC_MODIFIED \" +\n    \"SYSREQ_EDOC_NAME \" +\n    \"SYSREQ_EDOC_NOTE \" +\n    \"SYSREQ_EDOC_QUALIFIED_ID \" +\n    \"SYSREQ_EDOC_SESSION_KEY \" +\n    \"SYSREQ_EDOC_SESSION_KEY_ENCRYPTION_PLUGIN_NAME \" +\n    \"SYSREQ_EDOC_SESSION_KEY_ENCRYPTION_PLUGIN_VERSION \" +\n    \"SYSREQ_EDOC_SIGNATURE_TYPE \" +\n    \"SYSREQ_EDOC_SIGNED \" +\n    \"SYSREQ_EDOC_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_ARCHIVE_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_CHECK_RIGHTS \" +\n    \"SYSREQ_EDOC_STORAGES_COMPUTER_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_EDIT_IN_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_EXECUTIVE_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_FUNCTION \" +\n    \"SYSREQ_EDOC_STORAGES_INITIALIZED \" +\n    \"SYSREQ_EDOC_STORAGES_LOCAL_PATH \" +\n    \"SYSREQ_EDOC_STORAGES_SAPERION_DATABASE_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_SEARCH_BY_TEXT \" +\n    \"SYSREQ_EDOC_STORAGES_SERVER_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_SHARED_SOURCE_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_TYPE \" +\n    \"SYSREQ_EDOC_TEXT_MODIFIED \" +\n    \"SYSREQ_EDOC_TYPE_ACT_CODE \" +\n    \"SYSREQ_EDOC_TYPE_ACT_DESCRIPTION \" +\n    \"SYSREQ_EDOC_TYPE_ACT_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_ACT_ON_EXECUTE \" +\n    \"SYSREQ_EDOC_TYPE_ACT_ON_EXECUTE_EXISTS \" +\n    \"SYSREQ_EDOC_TYPE_ACT_SECTION \" +\n    \"SYSREQ_EDOC_TYPE_ADD_PARAMS \" +\n    \"SYSREQ_EDOC_TYPE_COMMENT \" +\n    \"SYSREQ_EDOC_TYPE_EVENT_TEXT \" +\n    \"SYSREQ_EDOC_TYPE_NAME_IN_SINGULAR \" +\n    \"SYSREQ_EDOC_TYPE_NAME_IN_SINGULAR_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_NUMERATION_METHOD \" +\n    \"SYSREQ_EDOC_TYPE_PSEUDO_REQUISITE_CODE \" +\n    \"SYSREQ_EDOC_TYPE_REQ_CODE \" +\n    \"SYSREQ_EDOC_TYPE_REQ_DESCRIPTION \" +\n    \"SYSREQ_EDOC_TYPE_REQ_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_REQ_IS_LEADING \" +\n    \"SYSREQ_EDOC_TYPE_REQ_IS_REQUIRED \" +\n    \"SYSREQ_EDOC_TYPE_REQ_NUMBER \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_CHANGE \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_CHANGE_EXISTS \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_SELECT \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_SELECT_KIND \" +\n    \"SYSREQ_EDOC_TYPE_REQ_SECTION \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_CARD \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_CODE \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_COMMENT \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_IS_MAIN \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_NAME \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_VERSION_AUTHOR \" +\n    \"SYSREQ_EDOC_VERSION_CRC \" +\n    \"SYSREQ_EDOC_VERSION_DATA \" +\n    \"SYSREQ_EDOC_VERSION_EDITOR \" +\n    \"SYSREQ_EDOC_VERSION_EXPORT_DATE \" +\n    \"SYSREQ_EDOC_VERSION_EXPORTER \" +\n    \"SYSREQ_EDOC_VERSION_HIDDEN \" +\n    \"SYSREQ_EDOC_VERSION_LIFE_STAGE \" +\n    \"SYSREQ_EDOC_VERSION_MODIFIED \" +\n    \"SYSREQ_EDOC_VERSION_NOTE \" +\n    \"SYSREQ_EDOC_VERSION_SIGNATURE_TYPE \" +\n    \"SYSREQ_EDOC_VERSION_SIGNED \" +\n    \"SYSREQ_EDOC_VERSION_SIZE \" +\n    \"SYSREQ_EDOC_VERSION_SOURCE \" +\n    \"SYSREQ_EDOC_VERSION_TEXT_MODIFIED \" +\n    \"SYSREQ_EDOCKIND_DEFAULT_VERSION_STATE_CODE \" +\n    \"SYSREQ_FOLDER_KIND \" +\n    \"SYSREQ_FUNC_CATEGORY \" +\n    \"SYSREQ_FUNC_COMMENT \" +\n    \"SYSREQ_FUNC_GROUP \" +\n    \"SYSREQ_FUNC_GROUP_COMMENT \" +\n    \"SYSREQ_FUNC_GROUP_NUMBER \" +\n    \"SYSREQ_FUNC_HELP \" +\n    \"SYSREQ_FUNC_PARAM_DEF_VALUE \" +\n    \"SYSREQ_FUNC_PARAM_IDENT \" +\n    \"SYSREQ_FUNC_PARAM_NUMBER \" +\n    \"SYSREQ_FUNC_PARAM_TYPE \" +\n    \"SYSREQ_FUNC_TEXT \" +\n    \"SYSREQ_GROUP_CATEGORY \" +\n    \"SYSREQ_ID \" +\n    \"SYSREQ_LAST_UPDATE \" +\n    \"SYSREQ_LEADER_REFERENCE \" +\n    \"SYSREQ_LINE_NUMBER \" +\n    \"SYSREQ_MAIN_RECORD_ID \" +\n    \"SYSREQ_NAME \" +\n    \"SYSREQ_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_NOTE \" +\n    \"SYSREQ_ORIGINAL_RECORD \" +\n    \"SYSREQ_OUR_FIRM \" +\n    \"SYSREQ_PROFILING_SETTINGS_BATCH_LOGING \" +\n    \"SYSREQ_PROFILING_SETTINGS_BATCH_SIZE \" +\n    \"SYSREQ_PROFILING_SETTINGS_PROFILING_ENABLED \" +\n    \"SYSREQ_PROFILING_SETTINGS_SQL_PROFILING_ENABLED \" +\n    \"SYSREQ_PROFILING_SETTINGS_START_LOGGED \" +\n    \"SYSREQ_RECORD_STATUS \" +\n    \"SYSREQ_REF_REQ_FIELD_NAME \" +\n    \"SYSREQ_REF_REQ_FORMAT \" +\n    \"SYSREQ_REF_REQ_GENERATED \" +\n    \"SYSREQ_REF_REQ_LENGTH \" +\n    \"SYSREQ_REF_REQ_PRECISION \" +\n    \"SYSREQ_REF_REQ_REFERENCE \" +\n    \"SYSREQ_REF_REQ_SECTION \" +\n    \"SYSREQ_REF_REQ_STORED \" +\n    \"SYSREQ_REF_REQ_TOKENS \" +\n    \"SYSREQ_REF_REQ_TYPE \" +\n    \"SYSREQ_REF_REQ_VIEW \" +\n    \"SYSREQ_REF_TYPE_ACT_CODE \" +\n    \"SYSREQ_REF_TYPE_ACT_DESCRIPTION \" +\n    \"SYSREQ_REF_TYPE_ACT_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_ACT_ON_EXECUTE \" +\n    \"SYSREQ_REF_TYPE_ACT_ON_EXECUTE_EXISTS \" +\n    \"SYSREQ_REF_TYPE_ACT_SECTION \" +\n    \"SYSREQ_REF_TYPE_ADD_PARAMS \" +\n    \"SYSREQ_REF_TYPE_COMMENT \" +\n    \"SYSREQ_REF_TYPE_COMMON_SETTINGS \" +\n    \"SYSREQ_REF_TYPE_DISPLAY_REQUISITE_NAME \" +\n    \"SYSREQ_REF_TYPE_EVENT_TEXT \" +\n    \"SYSREQ_REF_TYPE_MAIN_LEADING_REF \" +\n    \"SYSREQ_REF_TYPE_NAME_IN_SINGULAR \" +\n    \"SYSREQ_REF_TYPE_NAME_IN_SINGULAR_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_NUMERATION_METHOD \" +\n    \"SYSREQ_REF_TYPE_REQ_CODE \" +\n    \"SYSREQ_REF_TYPE_REQ_DESCRIPTION \" +\n    \"SYSREQ_REF_TYPE_REQ_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_CONTROL \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_FILTER \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_LEADING \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_REQUIRED \" +\n    \"SYSREQ_REF_TYPE_REQ_NUMBER \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_CHANGE \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_CHANGE_EXISTS \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_SELECT \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_SELECT_KIND \" +\n    \"SYSREQ_REF_TYPE_REQ_SECTION \" +\n    \"SYSREQ_REF_TYPE_VIEW_CARD \" +\n    \"SYSREQ_REF_TYPE_VIEW_CODE \" +\n    \"SYSREQ_REF_TYPE_VIEW_COMMENT \" +\n    \"SYSREQ_REF_TYPE_VIEW_IS_MAIN \" +\n    \"SYSREQ_REF_TYPE_VIEW_NAME \" +\n    \"SYSREQ_REF_TYPE_VIEW_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_REFERENCE_TYPE_ID \" +\n    \"SYSREQ_STATE \" +\n    \"SYSREQ_STATЕ \" +\n    \"SYSREQ_SYSTEM_SETTINGS_VALUE \" +\n    \"SYSREQ_TYPE \" +\n    \"SYSREQ_UNIT \" +\n    \"SYSREQ_UNIT_ID \" +\n    \"SYSREQ_USER_GROUPS_GROUP_FULL_NAME \" +\n    \"SYSREQ_USER_GROUPS_GROUP_NAME \" +\n    \"SYSREQ_USER_GROUPS_GROUP_SERVER_NAME \" +\n    \"SYSREQ_USERS_ACCESS_RIGHTS \" +\n    \"SYSREQ_USERS_AUTHENTICATION \" +\n    \"SYSREQ_USERS_CATEGORY \" +\n    \"SYSREQ_USERS_COMPONENT \" +\n    \"SYSREQ_USERS_COMPONENT_USER_IS_PUBLIC \" +\n    \"SYSREQ_USERS_DOMAIN \" +\n    \"SYSREQ_USERS_FULL_USER_NAME \" +\n    \"SYSREQ_USERS_GROUP \" +\n    \"SYSREQ_USERS_IS_MAIN_SERVER \" +\n    \"SYSREQ_USERS_LOGIN \" +\n    \"SYSREQ_USERS_REFERENCE_USER_IS_PUBLIC \" +\n    \"SYSREQ_USERS_STATUS \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_INFO \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_PLUGIN_NAME \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_PLUGIN_VERSION \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_STATE \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_SUBJECT_NAME \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_THUMBPRINT \" +\n    \"SYSREQ_USERS_USER_DEFAULT_CERTIFICATE \" +\n    \"SYSREQ_USERS_USER_DESCRIPTION \" +\n    \"SYSREQ_USERS_USER_GLOBAL_NAME \" +\n    \"SYSREQ_USERS_USER_LOGIN \" +\n    \"SYSREQ_USERS_USER_MAIN_SERVER \" +\n    \"SYSREQ_USERS_USER_TYPE \" +\n    \"SYSREQ_WORK_RULES_FOLDER_ID \";\n\n  // Result\n  const result_constants = \"RESULT_VAR_NAME RESULT_VAR_NAME_ENG \";\n\n  // Rule identification\n  const rule_identification_constants =\n    \"AUTO_NUMERATION_RULE_ID \" +\n    \"CANT_CHANGE_ID_REQUISITE_RULE_ID \" +\n    \"CANT_CHANGE_OURFIRM_REQUISITE_RULE_ID \" +\n    \"CHECK_CHANGING_REFERENCE_RECORD_USE_RULE_ID \" +\n    \"CHECK_CODE_REQUISITE_RULE_ID \" +\n    \"CHECK_DELETING_REFERENCE_RECORD_USE_RULE_ID \" +\n    \"CHECK_FILTRATER_CHANGES_RULE_ID \" +\n    \"CHECK_RECORD_INTERVAL_RULE_ID \" +\n    \"CHECK_REFERENCE_INTERVAL_RULE_ID \" +\n    \"CHECK_REQUIRED_DATA_FULLNESS_RULE_ID \" +\n    \"CHECK_REQUIRED_REQUISITES_FULLNESS_RULE_ID \" +\n    \"MAKE_RECORD_UNRATIFIED_RULE_ID \" +\n    \"RESTORE_AUTO_NUMERATION_RULE_ID \" +\n    \"SET_FIRM_CONTEXT_FROM_RECORD_RULE_ID \" +\n    \"SET_FIRST_RECORD_IN_LIST_FORM_RULE_ID \" +\n    \"SET_IDSPS_VALUE_RULE_ID \" +\n    \"SET_NEXT_CODE_VALUE_RULE_ID \" +\n    \"SET_OURFIRM_BOUNDS_RULE_ID \" +\n    \"SET_OURFIRM_REQUISITE_RULE_ID \";\n\n  // Script block properties\n  const script_block_properties_constants =\n    \"SCRIPT_BLOCK_AFTER_FINISH_EVENT \" +\n    \"SCRIPT_BLOCK_BEFORE_START_EVENT \" +\n    \"SCRIPT_BLOCK_EXECUTION_RESULTS_PROPERTY \" +\n    \"SCRIPT_BLOCK_NAME_PROPERTY \" +\n    \"SCRIPT_BLOCK_SCRIPT_PROPERTY \";\n\n  // Subtask block properties\n  const subtask_block_properties_constants =\n    \"SUBTASK_BLOCK_ABORT_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_AFTER_FINISH_EVENT \" +\n    \"SUBTASK_BLOCK_ASSIGN_PARAMS_EVENT \" +\n    \"SUBTASK_BLOCK_ATTACHMENTS_PROPERTY \" +\n    \"SUBTASK_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" +\n    \"SUBTASK_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_BEFORE_START_EVENT \" +\n    \"SUBTASK_BLOCK_CREATED_TASK_PROPERTY \" +\n    \"SUBTASK_BLOCK_CREATION_EVENT \" +\n    \"SUBTASK_BLOCK_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_IMPORTANCE_PROPERTY \" +\n    \"SUBTASK_BLOCK_INITIATOR_PROPERTY \" +\n    \"SUBTASK_BLOCK_IS_RELATIVE_ABORT_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_JOBS_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_NAME_PROPERTY \" +\n    \"SUBTASK_BLOCK_PARALLEL_ROUTE_PROPERTY \" +\n    \"SUBTASK_BLOCK_PERFORMERS_PROPERTY \" +\n    \"SUBTASK_BLOCK_RELATIVE_ABORT_DEADLINE_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_REQUIRE_SIGN_PROPERTY \" +\n    \"SUBTASK_BLOCK_STANDARD_ROUTE_PROPERTY \" +\n    \"SUBTASK_BLOCK_START_EVENT \" +\n    \"SUBTASK_BLOCK_STEP_CONTROL_PROPERTY \" +\n    \"SUBTASK_BLOCK_SUBJECT_PROPERTY \" +\n    \"SUBTASK_BLOCK_TASK_CONTROL_PROPERTY \" +\n    \"SUBTASK_BLOCK_TEXT_PROPERTY \" +\n    \"SUBTASK_BLOCK_UNLOCK_ATTACHMENTS_ON_STOP_PROPERTY \" +\n    \"SUBTASK_BLOCK_USE_STANDARD_ROUTE_PROPERTY \" +\n    \"SUBTASK_BLOCK_WAIT_FOR_TASK_COMPLETE_PROPERTY \";\n\n  // System component\n  const system_component_constants =\n    \"SYSCOMP_CONTROL_JOBS \" +\n    \"SYSCOMP_FOLDERS \" +\n    \"SYSCOMP_JOBS \" +\n    \"SYSCOMP_NOTICES \" +\n    \"SYSCOMP_TASKS \";\n\n  // System dialogs\n  const system_dialogs_constants =\n    \"SYSDLG_CREATE_EDOCUMENT \" +\n    \"SYSDLG_CREATE_EDOCUMENT_VERSION \" +\n    \"SYSDLG_CURRENT_PERIOD \" +\n    \"SYSDLG_EDIT_FUNCTION_HELP \" +\n    \"SYSDLG_EDOCUMENT_KINDS_FOR_TEMPLATE \" +\n    \"SYSDLG_EXPORT_MULTIPLE_EDOCUMENTS \" +\n    \"SYSDLG_EXPORT_SINGLE_EDOCUMENT \" +\n    \"SYSDLG_IMPORT_EDOCUMENT \" +\n    \"SYSDLG_MULTIPLE_SELECT \" +\n    \"SYSDLG_SETUP_ACCESS_RIGHTS \" +\n    \"SYSDLG_SETUP_DEFAULT_RIGHTS \" +\n    \"SYSDLG_SETUP_FILTER_CONDITION \" +\n    \"SYSDLG_SETUP_SIGN_RIGHTS \" +\n    \"SYSDLG_SETUP_TASK_OBSERVERS \" +\n    \"SYSDLG_SETUP_TASK_ROUTE \" +\n    \"SYSDLG_SETUP_USERS_LIST \" +\n    \"SYSDLG_SIGN_EDOCUMENT \" +\n    \"SYSDLG_SIGN_MULTIPLE_EDOCUMENTS \";\n\n  // System reference names\n  const system_reference_names_constants =\n    \"SYSREF_ACCESS_RIGHTS_TYPES \" +\n    \"SYSREF_ADMINISTRATION_HISTORY \" +\n    \"SYSREF_ALL_AVAILABLE_COMPONENTS \" +\n    \"SYSREF_ALL_AVAILABLE_PRIVILEGES \" +\n    \"SYSREF_ALL_REPLICATING_COMPONENTS \" +\n    \"SYSREF_AVAILABLE_DEVELOPERS_COMPONENTS \" +\n    \"SYSREF_CALENDAR_EVENTS \" +\n    \"SYSREF_COMPONENT_TOKEN_HISTORY \" +\n    \"SYSREF_COMPONENT_TOKENS \" +\n    \"SYSREF_COMPONENTS \" +\n    \"SYSREF_CONSTANTS \" +\n    \"SYSREF_DATA_RECEIVE_PROTOCOL \" +\n    \"SYSREF_DATA_SEND_PROTOCOL \" +\n    \"SYSREF_DIALOGS \" +\n    \"SYSREF_DIALOGS_REQUISITES \" +\n    \"SYSREF_EDITORS \" +\n    \"SYSREF_EDOC_CARDS \" +\n    \"SYSREF_EDOC_TYPES \" +\n    \"SYSREF_EDOCUMENT_CARD_REQUISITES \" +\n    \"SYSREF_EDOCUMENT_CARD_TYPES \" +\n    \"SYSREF_EDOCUMENT_CARD_TYPES_REFERENCE \" +\n    \"SYSREF_EDOCUMENT_CARDS \" +\n    \"SYSREF_EDOCUMENT_HISTORY \" +\n    \"SYSREF_EDOCUMENT_KINDS \" +\n    \"SYSREF_EDOCUMENT_REQUISITES \" +\n    \"SYSREF_EDOCUMENT_SIGNATURES \" +\n    \"SYSREF_EDOCUMENT_TEMPLATES \" +\n    \"SYSREF_EDOCUMENT_TEXT_STORAGES \" +\n    \"SYSREF_EDOCUMENT_VIEWS \" +\n    \"SYSREF_FILTERER_SETUP_CONFLICTS \" +\n    \"SYSREF_FILTRATER_SETTING_CONFLICTS \" +\n    \"SYSREF_FOLDER_HISTORY \" +\n    \"SYSREF_FOLDERS \" +\n    \"SYSREF_FUNCTION_GROUPS \" +\n    \"SYSREF_FUNCTION_PARAMS \" +\n    \"SYSREF_FUNCTIONS \" +\n    \"SYSREF_JOB_HISTORY \" +\n    \"SYSREF_LINKS \" +\n    \"SYSREF_LOCALIZATION_DICTIONARY \" +\n    \"SYSREF_LOCALIZATION_LANGUAGES \" +\n    \"SYSREF_MODULES \" +\n    \"SYSREF_PRIVILEGES \" +\n    \"SYSREF_RECORD_HISTORY \" +\n    \"SYSREF_REFERENCE_REQUISITES \" +\n    \"SYSREF_REFERENCE_TYPE_VIEWS \" +\n    \"SYSREF_REFERENCE_TYPES \" +\n    \"SYSREF_REFERENCES \" +\n    \"SYSREF_REFERENCES_REQUISITES \" +\n    \"SYSREF_REMOTE_SERVERS \" +\n    \"SYSREF_REPLICATION_SESSIONS_LOG \" +\n    \"SYSREF_REPLICATION_SESSIONS_PROTOCOL \" +\n    \"SYSREF_REPORTS \" +\n    \"SYSREF_ROLES \" +\n    \"SYSREF_ROUTE_BLOCK_GROUPS \" +\n    \"SYSREF_ROUTE_BLOCKS \" +\n    \"SYSREF_SCRIPTS \" +\n    \"SYSREF_SEARCHES \" +\n    \"SYSREF_SERVER_EVENTS \" +\n    \"SYSREF_SERVER_EVENTS_HISTORY \" +\n    \"SYSREF_STANDARD_ROUTE_GROUPS \" +\n    \"SYSREF_STANDARD_ROUTES \" +\n    \"SYSREF_STATUSES \" +\n    \"SYSREF_SYSTEM_SETTINGS \" +\n    \"SYSREF_TASK_HISTORY \" +\n    \"SYSREF_TASK_KIND_GROUPS \" +\n    \"SYSREF_TASK_KINDS \" +\n    \"SYSREF_TASK_RIGHTS \" +\n    \"SYSREF_TASK_SIGNATURES \" +\n    \"SYSREF_TASKS \" +\n    \"SYSREF_UNITS \" +\n    \"SYSREF_USER_GROUPS \" +\n    \"SYSREF_USER_GROUPS_REFERENCE \" +\n    \"SYSREF_USER_SUBSTITUTION \" +\n    \"SYSREF_USERS \" +\n    \"SYSREF_USERS_REFERENCE \" +\n    \"SYSREF_VIEWERS \" +\n    \"SYSREF_WORKING_TIME_CALENDARS \";\n\n  // Table name\n  const table_name_constants =\n    \"ACCESS_RIGHTS_TABLE_NAME \" +\n    \"EDMS_ACCESS_TABLE_NAME \" +\n    \"EDOC_TYPES_TABLE_NAME \";\n\n  // Test\n  const test_constants =\n    \"TEST_DEV_DB_NAME \" +\n    \"TEST_DEV_SYSTEM_CODE \" +\n    \"TEST_EDMS_DB_NAME \" +\n    \"TEST_EDMS_MAIN_CODE \" +\n    \"TEST_EDMS_MAIN_DB_NAME \" +\n    \"TEST_EDMS_SECOND_CODE \" +\n    \"TEST_EDMS_SECOND_DB_NAME \" +\n    \"TEST_EDMS_SYSTEM_CODE \" +\n    \"TEST_ISB5_MAIN_CODE \" +\n    \"TEST_ISB5_SECOND_CODE \" +\n    \"TEST_SQL_SERVER_2005_NAME \" +\n    \"TEST_SQL_SERVER_NAME \";\n\n  // Using the dialog windows\n  const using_the_dialog_windows_constants =\n    \"ATTENTION_CAPTION \" +\n    \"cbsCommandLinks \" +\n    \"cbsDefault \" +\n    \"CONFIRMATION_CAPTION \" +\n    \"ERROR_CAPTION \" +\n    \"INFORMATION_CAPTION \" +\n    \"mrCancel \" +\n    \"mrOk \";\n\n  // Using the document\n  const using_the_document_constants =\n    \"EDOC_VERSION_ACTIVE_STAGE_CODE \" +\n    \"EDOC_VERSION_DESIGN_STAGE_CODE \" +\n    \"EDOC_VERSION_OBSOLETE_STAGE_CODE \";\n\n  // Using the EA and encryption\n  const using_the_EA_and_encryption_constants =\n    \"cpDataEnciphermentEnabled \" +\n    \"cpDigitalSignatureEnabled \" +\n    \"cpID \" +\n    \"cpIssuer \" +\n    \"cpPluginVersion \" +\n    \"cpSerial \" +\n    \"cpSubjectName \" +\n    \"cpSubjSimpleName \" +\n    \"cpValidFromDate \" +\n    \"cpValidToDate \";\n\n  // Using the ISBL-editor\n  const using_the_ISBL_editor_constants =\n    \"ISBL_SYNTAX \" + \"NO_SYNTAX \" + \"XML_SYNTAX \";\n\n  // Wait block properties\n  const wait_block_properties_constants =\n    \"WAIT_BLOCK_AFTER_FINISH_EVENT \" +\n    \"WAIT_BLOCK_BEFORE_START_EVENT \" +\n    \"WAIT_BLOCK_DEADLINE_PROPERTY \" +\n    \"WAIT_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"WAIT_BLOCK_NAME_PROPERTY \" +\n    \"WAIT_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \";\n\n  // SYSRES Common\n  const sysres_common_constants =\n    \"SYSRES_COMMON \" +\n    \"SYSRES_CONST \" +\n    \"SYSRES_MBFUNC \" +\n    \"SYSRES_SBDATA \" +\n    \"SYSRES_SBGUI \" +\n    \"SYSRES_SBINTF \" +\n    \"SYSRES_SBREFDSC \" +\n    \"SYSRES_SQLERRORS \" +\n    \"SYSRES_SYSCOMP \";\n\n  // Константы ==> built_in\n  const CONSTANTS =\n    sysres_constants +\n    base_constants +\n    base_group_name_constants +\n    decision_block_properties_constants +\n    file_extension_constants +\n    job_block_properties_constants +\n    language_code_constants +\n    launching_external_applications_constants +\n    link_kind_constants +\n    lock_type_constants +\n    monitor_block_properties_constants +\n    notice_block_properties_constants +\n    object_events_constants +\n    object_params_constants +\n    other_constants +\n    privileges_constants +\n    pseudoreference_code_constants +\n    requisite_ISBCertificateType_values_constants +\n    requisite_ISBEDocStorageType_values_constants +\n    requisite_compType2_values_constants +\n    requisite_name_constants +\n    result_constants +\n    rule_identification_constants +\n    script_block_properties_constants +\n    subtask_block_properties_constants +\n    system_component_constants +\n    system_dialogs_constants +\n    system_reference_names_constants +\n    table_name_constants +\n    test_constants +\n    using_the_dialog_windows_constants +\n    using_the_document_constants +\n    using_the_EA_and_encryption_constants +\n    using_the_ISBL_editor_constants +\n    wait_block_properties_constants +\n    sysres_common_constants;\n\n  // enum TAccountType\n  const TAccountType = \"atUser atGroup atRole \";\n\n  // enum TActionEnabledMode\n  const TActionEnabledMode =\n    \"aemEnabledAlways \" +\n    \"aemDisabledAlways \" +\n    \"aemEnabledOnBrowse \" +\n    \"aemEnabledOnEdit \" +\n    \"aemDisabledOnBrowseEmpty \";\n\n  // enum TAddPosition\n  const TAddPosition = \"apBegin apEnd \";\n\n  // enum TAlignment\n  const TAlignment = \"alLeft alRight \";\n\n  // enum TAreaShowMode\n  const TAreaShowMode =\n    \"asmNever \" +\n    \"asmNoButCustomize \" +\n    \"asmAsLastTime \" +\n    \"asmYesButCustomize \" +\n    \"asmAlways \";\n\n  // enum TCertificateInvalidationReason\n  const TCertificateInvalidationReason = \"cirCommon cirRevoked \";\n\n  // enum TCertificateType\n  const TCertificateType = \"ctSignature ctEncode ctSignatureEncode \";\n\n  // enum TCheckListBoxItemState\n  const TCheckListBoxItemState = \"clbUnchecked clbChecked clbGrayed \";\n\n  // enum TCloseOnEsc\n  const TCloseOnEsc = \"ceISB ceAlways ceNever \";\n\n  // enum TCompType\n  const TCompType =\n    \"ctDocument \" +\n    \"ctReference \" +\n    \"ctScript \" +\n    \"ctUnknown \" +\n    \"ctReport \" +\n    \"ctDialog \" +\n    \"ctFunction \" +\n    \"ctFolder \" +\n    \"ctEDocument \" +\n    \"ctTask \" +\n    \"ctJob \" +\n    \"ctNotice \" +\n    \"ctControlJob \";\n\n  // enum TConditionFormat\n  const TConditionFormat = \"cfInternal cfDisplay \";\n\n  // enum TConnectionIntent\n  const TConnectionIntent = \"ciUnspecified ciWrite ciRead \";\n\n  // enum TContentKind\n  const TContentKind =\n    \"ckFolder \" +\n    \"ckEDocument \" +\n    \"ckTask \" +\n    \"ckJob \" +\n    \"ckComponentToken \" +\n    \"ckAny \" +\n    \"ckReference \" +\n    \"ckScript \" +\n    \"ckReport \" +\n    \"ckDialog \";\n\n  // enum TControlType\n  const TControlType =\n    \"ctISBLEditor \" +\n    \"ctBevel \" +\n    \"ctButton \" +\n    \"ctCheckListBox \" +\n    \"ctComboBox \" +\n    \"ctComboEdit \" +\n    \"ctGrid \" +\n    \"ctDBCheckBox \" +\n    \"ctDBComboBox \" +\n    \"ctDBEdit \" +\n    \"ctDBEllipsis \" +\n    \"ctDBMemo \" +\n    \"ctDBNavigator \" +\n    \"ctDBRadioGroup \" +\n    \"ctDBStatusLabel \" +\n    \"ctEdit \" +\n    \"ctGroupBox \" +\n    \"ctInplaceHint \" +\n    \"ctMemo \" +\n    \"ctPanel \" +\n    \"ctListBox \" +\n    \"ctRadioButton \" +\n    \"ctRichEdit \" +\n    \"ctTabSheet \" +\n    \"ctWebBrowser \" +\n    \"ctImage \" +\n    \"ctHyperLink \" +\n    \"ctLabel \" +\n    \"ctDBMultiEllipsis \" +\n    \"ctRibbon \" +\n    \"ctRichView \" +\n    \"ctInnerPanel \" +\n    \"ctPanelGroup \" +\n    \"ctBitButton \";\n\n  // enum TCriterionContentType\n  const TCriterionContentType =\n    \"cctDate \" +\n    \"cctInteger \" +\n    \"cctNumeric \" +\n    \"cctPick \" +\n    \"cctReference \" +\n    \"cctString \" +\n    \"cctText \";\n\n  // enum TCultureType\n  const TCultureType = \"cltInternal cltPrimary cltGUI \";\n\n  // enum TDataSetEventType\n  const TDataSetEventType =\n    \"dseBeforeOpen \" +\n    \"dseAfterOpen \" +\n    \"dseBeforeClose \" +\n    \"dseAfterClose \" +\n    \"dseOnValidDelete \" +\n    \"dseBeforeDelete \" +\n    \"dseAfterDelete \" +\n    \"dseAfterDeleteOutOfTransaction \" +\n    \"dseOnDeleteError \" +\n    \"dseBeforeInsert \" +\n    \"dseAfterInsert \" +\n    \"dseOnValidUpdate \" +\n    \"dseBeforeUpdate \" +\n    \"dseOnUpdateRatifiedRecord \" +\n    \"dseAfterUpdate \" +\n    \"dseAfterUpdateOutOfTransaction \" +\n    \"dseOnUpdateError \" +\n    \"dseAfterScroll \" +\n    \"dseOnOpenRecord \" +\n    \"dseOnCloseRecord \" +\n    \"dseBeforeCancel \" +\n    \"dseAfterCancel \" +\n    \"dseOnUpdateDeadlockError \" +\n    \"dseBeforeDetailUpdate \" +\n    \"dseOnPrepareUpdate \" +\n    \"dseOnAnyRequisiteChange \";\n\n  // enum TDataSetState\n  const TDataSetState = \"dssEdit dssInsert dssBrowse dssInActive \";\n\n  // enum TDateFormatType\n  const TDateFormatType = \"dftDate dftShortDate dftDateTime dftTimeStamp \";\n\n  // enum TDateOffsetType\n  const TDateOffsetType = \"dotDays dotHours dotMinutes dotSeconds \";\n\n  // enum TDateTimeKind\n  const TDateTimeKind = \"dtkndLocal dtkndUTC \";\n\n  // enum TDeaAccessRights\n  const TDeaAccessRights = \"arNone arView arEdit arFull \";\n\n  // enum TDocumentDefaultAction\n  const TDocumentDefaultAction = \"ddaView ddaEdit \";\n\n  // enum TEditMode\n  const TEditMode =\n    \"emLock \" +\n    \"emEdit \" +\n    \"emSign \" +\n    \"emExportWithLock \" +\n    \"emImportWithUnlock \" +\n    \"emChangeVersionNote \" +\n    \"emOpenForModify \" +\n    \"emChangeLifeStage \" +\n    \"emDelete \" +\n    \"emCreateVersion \" +\n    \"emImport \" +\n    \"emUnlockExportedWithLock \" +\n    \"emStart \" +\n    \"emAbort \" +\n    \"emReInit \" +\n    \"emMarkAsReaded \" +\n    \"emMarkAsUnreaded \" +\n    \"emPerform \" +\n    \"emAccept \" +\n    \"emResume \" +\n    \"emChangeRights \" +\n    \"emEditRoute \" +\n    \"emEditObserver \" +\n    \"emRecoveryFromLocalCopy \" +\n    \"emChangeWorkAccessType \" +\n    \"emChangeEncodeTypeToCertificate \" +\n    \"emChangeEncodeTypeToPassword \" +\n    \"emChangeEncodeTypeToNone \" +\n    \"emChangeEncodeTypeToCertificatePassword \" +\n    \"emChangeStandardRoute \" +\n    \"emGetText \" +\n    \"emOpenForView \" +\n    \"emMoveToStorage \" +\n    \"emCreateObject \" +\n    \"emChangeVersionHidden \" +\n    \"emDeleteVersion \" +\n    \"emChangeLifeCycleStage \" +\n    \"emApprovingSign \" +\n    \"emExport \" +\n    \"emContinue \" +\n    \"emLockFromEdit \" +\n    \"emUnLockForEdit \" +\n    \"emLockForServer \" +\n    \"emUnlockFromServer \" +\n    \"emDelegateAccessRights \" +\n    \"emReEncode \";\n\n  // enum TEditorCloseObservType\n  const TEditorCloseObservType = \"ecotFile ecotProcess \";\n\n  // enum TEdmsApplicationAction\n  const TEdmsApplicationAction = \"eaGet eaCopy eaCreate eaCreateStandardRoute \";\n\n  // enum TEDocumentLockType\n  const TEDocumentLockType = \"edltAll edltNothing edltQuery \";\n\n  // enum TEDocumentStepShowMode\n  const TEDocumentStepShowMode = \"essmText essmCard \";\n\n  // enum TEDocumentStepVersionType\n  const TEDocumentStepVersionType = \"esvtLast esvtLastActive esvtSpecified \";\n\n  // enum TEDocumentStorageFunction\n  const TEDocumentStorageFunction = \"edsfExecutive edsfArchive \";\n\n  // enum TEDocumentStorageType\n  const TEDocumentStorageType = \"edstSQLServer edstFile \";\n\n  // enum TEDocumentVersionSourceType\n  const TEDocumentVersionSourceType =\n    \"edvstNone edvstEDocumentVersionCopy edvstFile edvstTemplate edvstScannedFile \";\n\n  // enum TEDocumentVersionState\n  const TEDocumentVersionState = \"vsDefault vsDesign vsActive vsObsolete \";\n\n  // enum TEncodeType\n  const TEncodeType = \"etNone etCertificate etPassword etCertificatePassword \";\n\n  // enum TExceptionCategory\n  const TExceptionCategory = \"ecException ecWarning ecInformation \";\n\n  // enum TExportedSignaturesType\n  const TExportedSignaturesType = \"estAll estApprovingOnly \";\n\n  // enum TExportedVersionType\n  const TExportedVersionType = \"evtLast evtLastActive evtQuery \";\n\n  // enum TFieldDataType\n  const TFieldDataType =\n    \"fdtString \" +\n    \"fdtNumeric \" +\n    \"fdtInteger \" +\n    \"fdtDate \" +\n    \"fdtText \" +\n    \"fdtUnknown \" +\n    \"fdtWideString \" +\n    \"fdtLargeInteger \";\n\n  // enum TFolderType\n  const TFolderType =\n    \"ftInbox \" +\n    \"ftOutbox \" +\n    \"ftFavorites \" +\n    \"ftCommonFolder \" +\n    \"ftUserFolder \" +\n    \"ftComponents \" +\n    \"ftQuickLaunch \" +\n    \"ftShortcuts \" +\n    \"ftSearch \";\n\n  // enum TGridRowHeight\n  const TGridRowHeight = \"grhAuto \" + \"grhX1 \" + \"grhX2 \" + \"grhX3 \";\n\n  // enum THyperlinkType\n  const THyperlinkType = \"hltText \" + \"hltRTF \" + \"hltHTML \";\n\n  // enum TImageFileFormat\n  const TImageFileFormat =\n    \"iffBMP \" +\n    \"iffJPEG \" +\n    \"iffMultiPageTIFF \" +\n    \"iffSinglePageTIFF \" +\n    \"iffTIFF \" +\n    \"iffPNG \";\n\n  // enum TImageMode\n  const TImageMode = \"im8bGrayscale \" + \"im24bRGB \" + \"im1bMonochrome \";\n\n  // enum TImageType\n  const TImageType = \"itBMP \" + \"itJPEG \" + \"itWMF \" + \"itPNG \";\n\n  // enum TInplaceHintKind\n  const TInplaceHintKind =\n    \"ikhInformation \" + \"ikhWarning \" + \"ikhError \" + \"ikhNoIcon \";\n\n  // enum TISBLContext\n  const TISBLContext =\n    \"icUnknown \" +\n    \"icScript \" +\n    \"icFunction \" +\n    \"icIntegratedReport \" +\n    \"icAnalyticReport \" +\n    \"icDataSetEventHandler \" +\n    \"icActionHandler \" +\n    \"icFormEventHandler \" +\n    \"icLookUpEventHandler \" +\n    \"icRequisiteChangeEventHandler \" +\n    \"icBeforeSearchEventHandler \" +\n    \"icRoleCalculation \" +\n    \"icSelectRouteEventHandler \" +\n    \"icBlockPropertyCalculation \" +\n    \"icBlockQueryParamsEventHandler \" +\n    \"icChangeSearchResultEventHandler \" +\n    \"icBlockEventHandler \" +\n    \"icSubTaskInitEventHandler \" +\n    \"icEDocDataSetEventHandler \" +\n    \"icEDocLookUpEventHandler \" +\n    \"icEDocActionHandler \" +\n    \"icEDocFormEventHandler \" +\n    \"icEDocRequisiteChangeEventHandler \" +\n    \"icStructuredConversionRule \" +\n    \"icStructuredConversionEventBefore \" +\n    \"icStructuredConversionEventAfter \" +\n    \"icWizardEventHandler \" +\n    \"icWizardFinishEventHandler \" +\n    \"icWizardStepEventHandler \" +\n    \"icWizardStepFinishEventHandler \" +\n    \"icWizardActionEnableEventHandler \" +\n    \"icWizardActionExecuteEventHandler \" +\n    \"icCreateJobsHandler \" +\n    \"icCreateNoticesHandler \" +\n    \"icBeforeLookUpEventHandler \" +\n    \"icAfterLookUpEventHandler \" +\n    \"icTaskAbortEventHandler \" +\n    \"icWorkflowBlockActionHandler \" +\n    \"icDialogDataSetEventHandler \" +\n    \"icDialogActionHandler \" +\n    \"icDialogLookUpEventHandler \" +\n    \"icDialogRequisiteChangeEventHandler \" +\n    \"icDialogFormEventHandler \" +\n    \"icDialogValidCloseEventHandler \" +\n    \"icBlockFormEventHandler \" +\n    \"icTaskFormEventHandler \" +\n    \"icReferenceMethod \" +\n    \"icEDocMethod \" +\n    \"icDialogMethod \" +\n    \"icProcessMessageHandler \";\n\n  // enum TItemShow\n  const TItemShow = \"isShow \" + \"isHide \" + \"isByUserSettings \";\n\n  // enum TJobKind\n  const TJobKind = \"jkJob \" + \"jkNotice \" + \"jkControlJob \";\n\n  // enum TJoinType\n  const TJoinType = \"jtInner \" + \"jtLeft \" + \"jtRight \" + \"jtFull \" + \"jtCross \";\n\n  // enum TLabelPos\n  const TLabelPos = \"lbpAbove \" + \"lbpBelow \" + \"lbpLeft \" + \"lbpRight \";\n\n  // enum TLicensingType\n  const TLicensingType = \"eltPerConnection \" + \"eltPerUser \";\n\n  // enum TLifeCycleStageFontColor\n  const TLifeCycleStageFontColor =\n    \"sfcUndefined \" +\n    \"sfcBlack \" +\n    \"sfcGreen \" +\n    \"sfcRed \" +\n    \"sfcBlue \" +\n    \"sfcOrange \" +\n    \"sfcLilac \";\n\n  // enum TLifeCycleStageFontStyle\n  const TLifeCycleStageFontStyle = \"sfsItalic \" + \"sfsStrikeout \" + \"sfsNormal \";\n\n  // enum TLockableDevelopmentComponentType\n  const TLockableDevelopmentComponentType =\n    \"ldctStandardRoute \" +\n    \"ldctWizard \" +\n    \"ldctScript \" +\n    \"ldctFunction \" +\n    \"ldctRouteBlock \" +\n    \"ldctIntegratedReport \" +\n    \"ldctAnalyticReport \" +\n    \"ldctReferenceType \" +\n    \"ldctEDocumentType \" +\n    \"ldctDialog \" +\n    \"ldctServerEvents \";\n\n  // enum TMaxRecordCountRestrictionType\n  const TMaxRecordCountRestrictionType =\n    \"mrcrtNone \" + \"mrcrtUser \" + \"mrcrtMaximal \" + \"mrcrtCustom \";\n\n  // enum TRangeValueType\n  const TRangeValueType =\n    \"vtEqual \" + \"vtGreaterOrEqual \" + \"vtLessOrEqual \" + \"vtRange \";\n\n  // enum TRelativeDate\n  const TRelativeDate =\n    \"rdYesterday \" +\n    \"rdToday \" +\n    \"rdTomorrow \" +\n    \"rdThisWeek \" +\n    \"rdThisMonth \" +\n    \"rdThisYear \" +\n    \"rdNextMonth \" +\n    \"rdNextWeek \" +\n    \"rdLastWeek \" +\n    \"rdLastMonth \";\n\n  // enum TReportDestination\n  const TReportDestination = \"rdWindow \" + \"rdFile \" + \"rdPrinter \";\n\n  // enum TReqDataType\n  const TReqDataType =\n    \"rdtString \" +\n    \"rdtNumeric \" +\n    \"rdtInteger \" +\n    \"rdtDate \" +\n    \"rdtReference \" +\n    \"rdtAccount \" +\n    \"rdtText \" +\n    \"rdtPick \" +\n    \"rdtUnknown \" +\n    \"rdtLargeInteger \" +\n    \"rdtDocument \";\n\n  // enum TRequisiteEventType\n  const TRequisiteEventType = \"reOnChange \" + \"reOnChangeValues \";\n\n  // enum TSBTimeType\n  const TSBTimeType = \"ttGlobal \" + \"ttLocal \" + \"ttUser \" + \"ttSystem \";\n\n  // enum TSearchShowMode\n  const TSearchShowMode =\n    \"ssmBrowse \" + \"ssmSelect \" + \"ssmMultiSelect \" + \"ssmBrowseModal \";\n\n  // enum TSelectMode\n  const TSelectMode = \"smSelect \" + \"smLike \" + \"smCard \";\n\n  // enum TSignatureType\n  const TSignatureType = \"stNone \" + \"stAuthenticating \" + \"stApproving \";\n\n  // enum TSignerContentType\n  const TSignerContentType = \"sctString \" + \"sctStream \";\n\n  // enum TStringsSortType\n  const TStringsSortType = \"sstAnsiSort \" + \"sstNaturalSort \";\n\n  // enum TStringValueType\n  const TStringValueType = \"svtEqual \" + \"svtContain \";\n\n  // enum TStructuredObjectAttributeType\n  const TStructuredObjectAttributeType =\n    \"soatString \" +\n    \"soatNumeric \" +\n    \"soatInteger \" +\n    \"soatDatetime \" +\n    \"soatReferenceRecord \" +\n    \"soatText \" +\n    \"soatPick \" +\n    \"soatBoolean \" +\n    \"soatEDocument \" +\n    \"soatAccount \" +\n    \"soatIntegerCollection \" +\n    \"soatNumericCollection \" +\n    \"soatStringCollection \" +\n    \"soatPickCollection \" +\n    \"soatDatetimeCollection \" +\n    \"soatBooleanCollection \" +\n    \"soatReferenceRecordCollection \" +\n    \"soatEDocumentCollection \" +\n    \"soatAccountCollection \" +\n    \"soatContents \" +\n    \"soatUnknown \";\n\n  // enum TTaskAbortReason\n  const TTaskAbortReason = \"tarAbortByUser \" + \"tarAbortByWorkflowException \";\n\n  // enum TTextValueType\n  const TTextValueType = \"tvtAllWords \" + \"tvtExactPhrase \" + \"tvtAnyWord \";\n\n  // enum TUserObjectStatus\n  const TUserObjectStatus =\n    \"usNone \" +\n    \"usCompleted \" +\n    \"usRedSquare \" +\n    \"usBlueSquare \" +\n    \"usYellowSquare \" +\n    \"usGreenSquare \" +\n    \"usOrangeSquare \" +\n    \"usPurpleSquare \" +\n    \"usFollowUp \";\n\n  // enum TUserType\n  const TUserType =\n    \"utUnknown \" +\n    \"utUser \" +\n    \"utDeveloper \" +\n    \"utAdministrator \" +\n    \"utSystemDeveloper \" +\n    \"utDisconnected \";\n\n  // enum TValuesBuildType\n  const TValuesBuildType =\n    \"btAnd \" + \"btDetailAnd \" + \"btOr \" + \"btNotOr \" + \"btOnly \";\n\n  // enum TViewMode\n  const TViewMode = \"vmView \" + \"vmSelect \" + \"vmNavigation \";\n\n  // enum TViewSelectionMode\n  const TViewSelectionMode =\n    \"vsmSingle \" + \"vsmMultiple \" + \"vsmMultipleCheck \" + \"vsmNoSelection \";\n\n  // enum TWizardActionType\n  const TWizardActionType =\n    \"wfatPrevious \" + \"wfatNext \" + \"wfatCancel \" + \"wfatFinish \";\n\n  // enum TWizardFormElementProperty\n  const TWizardFormElementProperty =\n    \"wfepUndefined \" +\n    \"wfepText3 \" +\n    \"wfepText6 \" +\n    \"wfepText9 \" +\n    \"wfepSpinEdit \" +\n    \"wfepDropDown \" +\n    \"wfepRadioGroup \" +\n    \"wfepFlag \" +\n    \"wfepText12 \" +\n    \"wfepText15 \" +\n    \"wfepText18 \" +\n    \"wfepText21 \" +\n    \"wfepText24 \" +\n    \"wfepText27 \" +\n    \"wfepText30 \" +\n    \"wfepRadioGroupColumn1 \" +\n    \"wfepRadioGroupColumn2 \" +\n    \"wfepRadioGroupColumn3 \";\n\n  // enum TWizardFormElementType\n  const TWizardFormElementType =\n    \"wfetQueryParameter \" + \"wfetText \" + \"wfetDelimiter \" + \"wfetLabel \";\n\n  // enum TWizardParamType\n  const TWizardParamType =\n    \"wptString \" +\n    \"wptInteger \" +\n    \"wptNumeric \" +\n    \"wptBoolean \" +\n    \"wptDateTime \" +\n    \"wptPick \" +\n    \"wptText \" +\n    \"wptUser \" +\n    \"wptUserList \" +\n    \"wptEDocumentInfo \" +\n    \"wptEDocumentInfoList \" +\n    \"wptReferenceRecordInfo \" +\n    \"wptReferenceRecordInfoList \" +\n    \"wptFolderInfo \" +\n    \"wptTaskInfo \" +\n    \"wptContents \" +\n    \"wptFileName \" +\n    \"wptDate \";\n\n  // enum TWizardStepResult\n  const TWizardStepResult =\n    \"wsrComplete \" +\n    \"wsrGoNext \" +\n    \"wsrGoPrevious \" +\n    \"wsrCustom \" +\n    \"wsrCancel \" +\n    \"wsrGoFinal \";\n\n  // enum TWizardStepType\n  const TWizardStepType =\n    \"wstForm \" +\n    \"wstEDocument \" +\n    \"wstTaskCard \" +\n    \"wstReferenceRecordCard \" +\n    \"wstFinal \";\n\n  // enum TWorkAccessType\n  const TWorkAccessType = \"waAll \" + \"waPerformers \" + \"waManual \";\n\n  // enum TWorkflowBlockType\n  const TWorkflowBlockType =\n    \"wsbStart \" +\n    \"wsbFinish \" +\n    \"wsbNotice \" +\n    \"wsbStep \" +\n    \"wsbDecision \" +\n    \"wsbWait \" +\n    \"wsbMonitor \" +\n    \"wsbScript \" +\n    \"wsbConnector \" +\n    \"wsbSubTask \" +\n    \"wsbLifeCycleStage \" +\n    \"wsbPause \";\n\n  // enum TWorkflowDataType\n  const TWorkflowDataType =\n    \"wdtInteger \" +\n    \"wdtFloat \" +\n    \"wdtString \" +\n    \"wdtPick \" +\n    \"wdtDateTime \" +\n    \"wdtBoolean \" +\n    \"wdtTask \" +\n    \"wdtJob \" +\n    \"wdtFolder \" +\n    \"wdtEDocument \" +\n    \"wdtReferenceRecord \" +\n    \"wdtUser \" +\n    \"wdtGroup \" +\n    \"wdtRole \" +\n    \"wdtIntegerCollection \" +\n    \"wdtFloatCollection \" +\n    \"wdtStringCollection \" +\n    \"wdtPickCollection \" +\n    \"wdtDateTimeCollection \" +\n    \"wdtBooleanCollection \" +\n    \"wdtTaskCollection \" +\n    \"wdtJobCollection \" +\n    \"wdtFolderCollection \" +\n    \"wdtEDocumentCollection \" +\n    \"wdtReferenceRecordCollection \" +\n    \"wdtUserCollection \" +\n    \"wdtGroupCollection \" +\n    \"wdtRoleCollection \" +\n    \"wdtContents \" +\n    \"wdtUserList \" +\n    \"wdtSearchDescription \" +\n    \"wdtDeadLine \" +\n    \"wdtPickSet \" +\n    \"wdtAccountCollection \";\n\n  // enum TWorkImportance\n  const TWorkImportance = \"wiLow \" + \"wiNormal \" + \"wiHigh \";\n\n  // enum TWorkRouteType\n  const TWorkRouteType = \"wrtSoft \" + \"wrtHard \";\n\n  // enum TWorkState\n  const TWorkState =\n    \"wsInit \" +\n    \"wsRunning \" +\n    \"wsDone \" +\n    \"wsControlled \" +\n    \"wsAborted \" +\n    \"wsContinued \";\n\n  // enum TWorkTextBuildingMode\n  const TWorkTextBuildingMode =\n    \"wtmFull \" + \"wtmFromCurrent \" + \"wtmOnlyCurrent \";\n\n  // Перечисления\n  const ENUMS =\n    TAccountType +\n    TActionEnabledMode +\n    TAddPosition +\n    TAlignment +\n    TAreaShowMode +\n    TCertificateInvalidationReason +\n    TCertificateType +\n    TCheckListBoxItemState +\n    TCloseOnEsc +\n    TCompType +\n    TConditionFormat +\n    TConnectionIntent +\n    TContentKind +\n    TControlType +\n    TCriterionContentType +\n    TCultureType +\n    TDataSetEventType +\n    TDataSetState +\n    TDateFormatType +\n    TDateOffsetType +\n    TDateTimeKind +\n    TDeaAccessRights +\n    TDocumentDefaultAction +\n    TEditMode +\n    TEditorCloseObservType +\n    TEdmsApplicationAction +\n    TEDocumentLockType +\n    TEDocumentStepShowMode +\n    TEDocumentStepVersionType +\n    TEDocumentStorageFunction +\n    TEDocumentStorageType +\n    TEDocumentVersionSourceType +\n    TEDocumentVersionState +\n    TEncodeType +\n    TExceptionCategory +\n    TExportedSignaturesType +\n    TExportedVersionType +\n    TFieldDataType +\n    TFolderType +\n    TGridRowHeight +\n    THyperlinkType +\n    TImageFileFormat +\n    TImageMode +\n    TImageType +\n    TInplaceHintKind +\n    TISBLContext +\n    TItemShow +\n    TJobKind +\n    TJoinType +\n    TLabelPos +\n    TLicensingType +\n    TLifeCycleStageFontColor +\n    TLifeCycleStageFontStyle +\n    TLockableDevelopmentComponentType +\n    TMaxRecordCountRestrictionType +\n    TRangeValueType +\n    TRelativeDate +\n    TReportDestination +\n    TReqDataType +\n    TRequisiteEventType +\n    TSBTimeType +\n    TSearchShowMode +\n    TSelectMode +\n    TSignatureType +\n    TSignerContentType +\n    TStringsSortType +\n    TStringValueType +\n    TStructuredObjectAttributeType +\n    TTaskAbortReason +\n    TTextValueType +\n    TUserObjectStatus +\n    TUserType +\n    TValuesBuildType +\n    TViewMode +\n    TViewSelectionMode +\n    TWizardActionType +\n    TWizardFormElementProperty +\n    TWizardFormElementType +\n    TWizardParamType +\n    TWizardStepResult +\n    TWizardStepType +\n    TWorkAccessType +\n    TWorkflowBlockType +\n    TWorkflowDataType +\n    TWorkImportance +\n    TWorkRouteType +\n    TWorkState +\n    TWorkTextBuildingMode;\n\n  // Системные функции ==> SYSFUNCTIONS\n  const system_functions =\n    \"AddSubString \" +\n    \"AdjustLineBreaks \" +\n    \"AmountInWords \" +\n    \"Analysis \" +\n    \"ArrayDimCount \" +\n    \"ArrayHighBound \" +\n    \"ArrayLowBound \" +\n    \"ArrayOf \" +\n    \"ArrayReDim \" +\n    \"Assert \" +\n    \"Assigned \" +\n    \"BeginOfMonth \" +\n    \"BeginOfPeriod \" +\n    \"BuildProfilingOperationAnalysis \" +\n    \"CallProcedure \" +\n    \"CanReadFile \" +\n    \"CArrayElement \" +\n    \"CDataSetRequisite \" +\n    \"ChangeDate \" +\n    \"ChangeReferenceDataset \" +\n    \"Char \" +\n    \"CharPos \" +\n    \"CheckParam \" +\n    \"CheckParamValue \" +\n    \"CompareStrings \" +\n    \"ConstantExists \" +\n    \"ControlState \" +\n    \"ConvertDateStr \" +\n    \"Copy \" +\n    \"CopyFile \" +\n    \"CreateArray \" +\n    \"CreateCachedReference \" +\n    \"CreateConnection \" +\n    \"CreateDialog \" +\n    \"CreateDualListDialog \" +\n    \"CreateEditor \" +\n    \"CreateException \" +\n    \"CreateFile \" +\n    \"CreateFolderDialog \" +\n    \"CreateInputDialog \" +\n    \"CreateLinkFile \" +\n    \"CreateList \" +\n    \"CreateLock \" +\n    \"CreateMemoryDataSet \" +\n    \"CreateObject \" +\n    \"CreateOpenDialog \" +\n    \"CreateProgress \" +\n    \"CreateQuery \" +\n    \"CreateReference \" +\n    \"CreateReport \" +\n    \"CreateSaveDialog \" +\n    \"CreateScript \" +\n    \"CreateSQLPivotFunction \" +\n    \"CreateStringList \" +\n    \"CreateTreeListSelectDialog \" +\n    \"CSelectSQL \" +\n    \"CSQL \" +\n    \"CSubString \" +\n    \"CurrentUserID \" +\n    \"CurrentUserName \" +\n    \"CurrentVersion \" +\n    \"DataSetLocateEx \" +\n    \"DateDiff \" +\n    \"DateTimeDiff \" +\n    \"DateToStr \" +\n    \"DayOfWeek \" +\n    \"DeleteFile \" +\n    \"DirectoryExists \" +\n    \"DisableCheckAccessRights \" +\n    \"DisableCheckFullShowingRestriction \" +\n    \"DisableMassTaskSendingRestrictions \" +\n    \"DropTable \" +\n    \"DupeString \" +\n    \"EditText \" +\n    \"EnableCheckAccessRights \" +\n    \"EnableCheckFullShowingRestriction \" +\n    \"EnableMassTaskSendingRestrictions \" +\n    \"EndOfMonth \" +\n    \"EndOfPeriod \" +\n    \"ExceptionExists \" +\n    \"ExceptionsOff \" +\n    \"ExceptionsOn \" +\n    \"Execute \" +\n    \"ExecuteProcess \" +\n    \"Exit \" +\n    \"ExpandEnvironmentVariables \" +\n    \"ExtractFileDrive \" +\n    \"ExtractFileExt \" +\n    \"ExtractFileName \" +\n    \"ExtractFilePath \" +\n    \"ExtractParams \" +\n    \"FileExists \" +\n    \"FileSize \" +\n    \"FindFile \" +\n    \"FindSubString \" +\n    \"FirmContext \" +\n    \"ForceDirectories \" +\n    \"Format \" +\n    \"FormatDate \" +\n    \"FormatNumeric \" +\n    \"FormatSQLDate \" +\n    \"FormatString \" +\n    \"FreeException \" +\n    \"GetComponent \" +\n    \"GetComponentLaunchParam \" +\n    \"GetConstant \" +\n    \"GetLastException \" +\n    \"GetReferenceRecord \" +\n    \"GetRefTypeByRefID \" +\n    \"GetTableID \" +\n    \"GetTempFolder \" +\n    \"IfThen \" +\n    \"In \" +\n    \"IndexOf \" +\n    \"InputDialog \" +\n    \"InputDialogEx \" +\n    \"InteractiveMode \" +\n    \"IsFileLocked \" +\n    \"IsGraphicFile \" +\n    \"IsNumeric \" +\n    \"Length \" +\n    \"LoadString \" +\n    \"LoadStringFmt \" +\n    \"LocalTimeToUTC \" +\n    \"LowerCase \" +\n    \"Max \" +\n    \"MessageBox \" +\n    \"MessageBoxEx \" +\n    \"MimeDecodeBinary \" +\n    \"MimeDecodeString \" +\n    \"MimeEncodeBinary \" +\n    \"MimeEncodeString \" +\n    \"Min \" +\n    \"MoneyInWords \" +\n    \"MoveFile \" +\n    \"NewID \" +\n    \"Now \" +\n    \"OpenFile \" +\n    \"Ord \" +\n    \"Precision \" +\n    \"Raise \" +\n    \"ReadCertificateFromFile \" +\n    \"ReadFile \" +\n    \"ReferenceCodeByID \" +\n    \"ReferenceNumber \" +\n    \"ReferenceRequisiteMode \" +\n    \"ReferenceRequisiteValue \" +\n    \"RegionDateSettings \" +\n    \"RegionNumberSettings \" +\n    \"RegionTimeSettings \" +\n    \"RegRead \" +\n    \"RegWrite \" +\n    \"RenameFile \" +\n    \"Replace \" +\n    \"Round \" +\n    \"SelectServerCode \" +\n    \"SelectSQL \" +\n    \"ServerDateTime \" +\n    \"SetConstant \" +\n    \"SetManagedFolderFieldsState \" +\n    \"ShowConstantsInputDialog \" +\n    \"ShowMessage \" +\n    \"Sleep \" +\n    \"Split \" +\n    \"SQL \" +\n    \"SQL2XLSTAB \" +\n    \"SQLProfilingSendReport \" +\n    \"StrToDate \" +\n    \"SubString \" +\n    \"SubStringCount \" +\n    \"SystemSetting \" +\n    \"Time \" +\n    \"TimeDiff \" +\n    \"Today \" +\n    \"Transliterate \" +\n    \"Trim \" +\n    \"UpperCase \" +\n    \"UserStatus \" +\n    \"UTCToLocalTime \" +\n    \"ValidateXML \" +\n    \"VarIsClear \" +\n    \"VarIsEmpty \" +\n    \"VarIsNull \" +\n    \"WorkTimeDiff \" +\n    \"WriteFile \" +\n    \"WriteFileEx \" +\n    \"WriteObjectHistory \" +\n    \"Анализ \" +\n    \"БазаДанных \" +\n    \"БлокЕсть \" +\n    \"БлокЕстьРасш \" +\n    \"БлокИнфо \" +\n    \"БлокСнять \" +\n    \"БлокСнятьРасш \" +\n    \"БлокУстановить \" +\n    \"Ввод \" +\n    \"ВводМеню \" +\n    \"ВедС \" +\n    \"ВедСпр \" +\n    \"ВерхняяГраницаМассива \" +\n    \"ВнешПрогр \" +\n    \"Восст \" +\n    \"ВременнаяПапка \" +\n    \"Время \" +\n    \"ВыборSQL \" +\n    \"ВыбратьЗапись \" +\n    \"ВыделитьСтр \" +\n    \"Вызвать \" +\n    \"Выполнить \" +\n    \"ВыпПрогр \" +\n    \"ГрафическийФайл \" +\n    \"ГруппаДополнительно \" +\n    \"ДатаВремяСерв \" +\n    \"ДеньНедели \" +\n    \"ДиалогДаНет \" +\n    \"ДлинаСтр \" +\n    \"ДобПодстр \" +\n    \"ЕПусто \" +\n    \"ЕслиТо \" +\n    \"ЕЧисло \" +\n    \"ЗамПодстр \" +\n    \"ЗаписьСправочника \" +\n    \"ЗначПоляСпр \" +\n    \"ИДТипСпр \" +\n    \"ИзвлечьДиск \" +\n    \"ИзвлечьИмяФайла \" +\n    \"ИзвлечьПуть \" +\n    \"ИзвлечьРасширение \" +\n    \"ИзмДат \" +\n    \"ИзменитьРазмерМассива \" +\n    \"ИзмеренийМассива \" +\n    \"ИмяОрг \" +\n    \"ИмяПоляСпр \" +\n    \"Индекс \" +\n    \"ИндикаторЗакрыть \" +\n    \"ИндикаторОткрыть \" +\n    \"ИндикаторШаг \" +\n    \"ИнтерактивныйРежим \" +\n    \"ИтогТблСпр \" +\n    \"КодВидВедСпр \" +\n    \"КодВидСпрПоИД \" +\n    \"КодПоAnalit \" +\n    \"КодСимвола \" +\n    \"КодСпр \" +\n    \"КолПодстр \" +\n    \"КолПроп \" +\n    \"КонМес \" +\n    \"Конст \" +\n    \"КонстЕсть \" +\n    \"КонстЗнач \" +\n    \"КонТран \" +\n    \"КопироватьФайл \" +\n    \"КопияСтр \" +\n    \"КПериод \" +\n    \"КСтрТблСпр \" +\n    \"Макс \" +\n    \"МаксСтрТблСпр \" +\n    \"Массив \" +\n    \"Меню \" +\n    \"МенюРасш \" +\n    \"Мин \" +\n    \"НаборДанныхНайтиРасш \" +\n    \"НаимВидСпр \" +\n    \"НаимПоAnalit \" +\n    \"НаимСпр \" +\n    \"НастроитьПереводыСтрок \" +\n    \"НачМес \" +\n    \"НачТран \" +\n    \"НижняяГраницаМассива \" +\n    \"НомерСпр \" +\n    \"НПериод \" +\n    \"Окно \" +\n    \"Окр \" +\n    \"Окружение \" +\n    \"ОтлИнфДобавить \" +\n    \"ОтлИнфУдалить \" +\n    \"Отчет \" +\n    \"ОтчетАнал \" +\n    \"ОтчетИнт \" +\n    \"ПапкаСуществует \" +\n    \"Пауза \" +\n    \"ПВыборSQL \" +\n    \"ПереименоватьФайл \" +\n    \"Переменные \" +\n    \"ПереместитьФайл \" +\n    \"Подстр \" +\n    \"ПоискПодстр \" +\n    \"ПоискСтр \" +\n    \"ПолучитьИДТаблицы \" +\n    \"ПользовательДополнительно \" +\n    \"ПользовательИД \" +\n    \"ПользовательИмя \" +\n    \"ПользовательСтатус \" +\n    \"Прервать \" +\n    \"ПроверитьПараметр \" +\n    \"ПроверитьПараметрЗнач \" +\n    \"ПроверитьУсловие \" +\n    \"РазбСтр \" +\n    \"РазнВремя \" +\n    \"РазнДат \" +\n    \"РазнДатаВремя \" +\n    \"РазнРабВремя \" +\n    \"РегУстВрем \" +\n    \"РегУстДат \" +\n    \"РегУстЧсл \" +\n    \"РедТекст \" +\n    \"РеестрЗапись \" +\n    \"РеестрСписокИменПарам \" +\n    \"РеестрЧтение \" +\n    \"РеквСпр \" +\n    \"РеквСпрПр \" +\n    \"Сегодня \" +\n    \"Сейчас \" +\n    \"Сервер \" +\n    \"СерверПроцессИД \" +\n    \"СертификатФайлСчитать \" +\n    \"СжПроб \" +\n    \"Символ \" +\n    \"СистемаДиректумКод \" +\n    \"СистемаИнформация \" +\n    \"СистемаКод \" +\n    \"Содержит \" +\n    \"СоединениеЗакрыть \" +\n    \"СоединениеОткрыть \" +\n    \"СоздатьДиалог \" +\n    \"СоздатьДиалогВыбораИзДвухСписков \" +\n    \"СоздатьДиалогВыбораПапки \" +\n    \"СоздатьДиалогОткрытияФайла \" +\n    \"СоздатьДиалогСохраненияФайла \" +\n    \"СоздатьЗапрос \" +\n    \"СоздатьИндикатор \" +\n    \"СоздатьИсключение \" +\n    \"СоздатьКэшированныйСправочник \" +\n    \"СоздатьМассив \" +\n    \"СоздатьНаборДанных \" +\n    \"СоздатьОбъект \" +\n    \"СоздатьОтчет \" +\n    \"СоздатьПапку \" +\n    \"СоздатьРедактор \" +\n    \"СоздатьСоединение \" +\n    \"СоздатьСписок \" +\n    \"СоздатьСписокСтрок \" +\n    \"СоздатьСправочник \" +\n    \"СоздатьСценарий \" +\n    \"СоздСпр \" +\n    \"СостСпр \" +\n    \"Сохр \" +\n    \"СохрСпр \" +\n    \"СписокСистем \" +\n    \"Спр \" +\n    \"Справочник \" +\n    \"СпрБлокЕсть \" +\n    \"СпрБлокСнять \" +\n    \"СпрБлокСнятьРасш \" +\n    \"СпрБлокУстановить \" +\n    \"СпрИзмНабДан \" +\n    \"СпрКод \" +\n    \"СпрНомер \" +\n    \"СпрОбновить \" +\n    \"СпрОткрыть \" +\n    \"СпрОтменить \" +\n    \"СпрПарам \" +\n    \"СпрПолеЗнач \" +\n    \"СпрПолеИмя \" +\n    \"СпрРекв \" +\n    \"СпрРеквВведЗн \" +\n    \"СпрРеквНовые \" +\n    \"СпрРеквПр \" +\n    \"СпрРеквПредЗн \" +\n    \"СпрРеквРежим \" +\n    \"СпрРеквТипТекст \" +\n    \"СпрСоздать \" +\n    \"СпрСост \" +\n    \"СпрСохранить \" +\n    \"СпрТблИтог \" +\n    \"СпрТблСтр \" +\n    \"СпрТблСтрКол \" +\n    \"СпрТблСтрМакс \" +\n    \"СпрТблСтрМин \" +\n    \"СпрТблСтрПред \" +\n    \"СпрТблСтрСлед \" +\n    \"СпрТблСтрСозд \" +\n    \"СпрТблСтрУд \" +\n    \"СпрТекПредст \" +\n    \"СпрУдалить \" +\n    \"СравнитьСтр \" +\n    \"СтрВерхРегистр \" +\n    \"СтрНижнРегистр \" +\n    \"СтрТблСпр \" +\n    \"СумПроп \" +\n    \"Сценарий \" +\n    \"СценарийПарам \" +\n    \"ТекВерсия \" +\n    \"ТекОрг \" +\n    \"Точн \" +\n    \"Тран \" +\n    \"Транслитерация \" +\n    \"УдалитьТаблицу \" +\n    \"УдалитьФайл \" +\n    \"УдСпр \" +\n    \"УдСтрТблСпр \" +\n    \"Уст \" +\n    \"УстановкиКонстант \" +\n    \"ФайлАтрибутСчитать \" +\n    \"ФайлАтрибутУстановить \" +\n    \"ФайлВремя \" +\n    \"ФайлВремяУстановить \" +\n    \"ФайлВыбрать \" +\n    \"ФайлЗанят \" +\n    \"ФайлЗаписать \" +\n    \"ФайлИскать \" +\n    \"ФайлКопировать \" +\n    \"ФайлМожноЧитать \" +\n    \"ФайлОткрыть \" +\n    \"ФайлПереименовать \" +\n    \"ФайлПерекодировать \" +\n    \"ФайлПереместить \" +\n    \"ФайлПросмотреть \" +\n    \"ФайлРазмер \" +\n    \"ФайлСоздать \" +\n    \"ФайлСсылкаСоздать \" +\n    \"ФайлСуществует \" +\n    \"ФайлСчитать \" +\n    \"ФайлУдалить \" +\n    \"ФмтSQLДат \" +\n    \"ФмтДат \" +\n    \"ФмтСтр \" +\n    \"ФмтЧсл \" +\n    \"Формат \" +\n    \"ЦМассивЭлемент \" +\n    \"ЦНаборДанныхРеквизит \" +\n    \"ЦПодстр \";\n\n  // Предопределенные переменные ==> built_in\n  const predefined_variables =\n    \"AltState \" +\n    \"Application \" +\n    \"CallType \" +\n    \"ComponentTokens \" +\n    \"CreatedJobs \" +\n    \"CreatedNotices \" +\n    \"ControlState \" +\n    \"DialogResult \" +\n    \"Dialogs \" +\n    \"EDocuments \" +\n    \"EDocumentVersionSource \" +\n    \"Folders \" +\n    \"GlobalIDs \" +\n    \"Job \" +\n    \"Jobs \" +\n    \"InputValue \" +\n    \"LookUpReference \" +\n    \"LookUpRequisiteNames \" +\n    \"LookUpSearch \" +\n    \"Object \" +\n    \"ParentComponent \" +\n    \"Processes \" +\n    \"References \" +\n    \"Requisite \" +\n    \"ReportName \" +\n    \"Reports \" +\n    \"Result \" +\n    \"Scripts \" +\n    \"Searches \" +\n    \"SelectedAttachments \" +\n    \"SelectedItems \" +\n    \"SelectMode \" +\n    \"Sender \" +\n    \"ServerEvents \" +\n    \"ServiceFactory \" +\n    \"ShiftState \" +\n    \"SubTask \" +\n    \"SystemDialogs \" +\n    \"Tasks \" +\n    \"Wizard \" +\n    \"Wizards \" +\n    \"Work \" +\n    \"ВызовСпособ \" +\n    \"ИмяОтчета \" +\n    \"РеквЗнач \";\n\n  // Интерфейсы ==> type\n  const interfaces =\n    \"IApplication \" +\n    \"IAccessRights \" +\n    \"IAccountRepository \" +\n    \"IAccountSelectionRestrictions \" +\n    \"IAction \" +\n    \"IActionList \" +\n    \"IAdministrationHistoryDescription \" +\n    \"IAnchors \" +\n    \"IApplication \" +\n    \"IArchiveInfo \" +\n    \"IAttachment \" +\n    \"IAttachmentList \" +\n    \"ICheckListBox \" +\n    \"ICheckPointedList \" +\n    \"IColumn \" +\n    \"IComponent \" +\n    \"IComponentDescription \" +\n    \"IComponentToken \" +\n    \"IComponentTokenFactory \" +\n    \"IComponentTokenInfo \" +\n    \"ICompRecordInfo \" +\n    \"IConnection \" +\n    \"IContents \" +\n    \"IControl \" +\n    \"IControlJob \" +\n    \"IControlJobInfo \" +\n    \"IControlList \" +\n    \"ICrypto \" +\n    \"ICrypto2 \" +\n    \"ICustomJob \" +\n    \"ICustomJobInfo \" +\n    \"ICustomListBox \" +\n    \"ICustomObjectWizardStep \" +\n    \"ICustomWork \" +\n    \"ICustomWorkInfo \" +\n    \"IDataSet \" +\n    \"IDataSetAccessInfo \" +\n    \"IDataSigner \" +\n    \"IDateCriterion \" +\n    \"IDateRequisite \" +\n    \"IDateRequisiteDescription \" +\n    \"IDateValue \" +\n    \"IDeaAccessRights \" +\n    \"IDeaObjectInfo \" +\n    \"IDevelopmentComponentLock \" +\n    \"IDialog \" +\n    \"IDialogFactory \" +\n    \"IDialogPickRequisiteItems \" +\n    \"IDialogsFactory \" +\n    \"IDICSFactory \" +\n    \"IDocRequisite \" +\n    \"IDocumentInfo \" +\n    \"IDualListDialog \" +\n    \"IECertificate \" +\n    \"IECertificateInfo \" +\n    \"IECertificates \" +\n    \"IEditControl \" +\n    \"IEditorForm \" +\n    \"IEdmsExplorer \" +\n    \"IEdmsObject \" +\n    \"IEdmsObjectDescription \" +\n    \"IEdmsObjectFactory \" +\n    \"IEdmsObjectInfo \" +\n    \"IEDocument \" +\n    \"IEDocumentAccessRights \" +\n    \"IEDocumentDescription \" +\n    \"IEDocumentEditor \" +\n    \"IEDocumentFactory \" +\n    \"IEDocumentInfo \" +\n    \"IEDocumentStorage \" +\n    \"IEDocumentVersion \" +\n    \"IEDocumentVersionListDialog \" +\n    \"IEDocumentVersionSource \" +\n    \"IEDocumentWizardStep \" +\n    \"IEDocVerSignature \" +\n    \"IEDocVersionState \" +\n    \"IEnabledMode \" +\n    \"IEncodeProvider \" +\n    \"IEncrypter \" +\n    \"IEvent \" +\n    \"IEventList \" +\n    \"IException \" +\n    \"IExternalEvents \" +\n    \"IExternalHandler \" +\n    \"IFactory \" +\n    \"IField \" +\n    \"IFileDialog \" +\n    \"IFolder \" +\n    \"IFolderDescription \" +\n    \"IFolderDialog \" +\n    \"IFolderFactory \" +\n    \"IFolderInfo \" +\n    \"IForEach \" +\n    \"IForm \" +\n    \"IFormTitle \" +\n    \"IFormWizardStep \" +\n    \"IGlobalIDFactory \" +\n    \"IGlobalIDInfo \" +\n    \"IGrid \" +\n    \"IHasher \" +\n    \"IHistoryDescription \" +\n    \"IHyperLinkControl \" +\n    \"IImageButton \" +\n    \"IImageControl \" +\n    \"IInnerPanel \" +\n    \"IInplaceHint \" +\n    \"IIntegerCriterion \" +\n    \"IIntegerList \" +\n    \"IIntegerRequisite \" +\n    \"IIntegerValue \" +\n    \"IISBLEditorForm \" +\n    \"IJob \" +\n    \"IJobDescription \" +\n    \"IJobFactory \" +\n    \"IJobForm \" +\n    \"IJobInfo \" +\n    \"ILabelControl \" +\n    \"ILargeIntegerCriterion \" +\n    \"ILargeIntegerRequisite \" +\n    \"ILargeIntegerValue \" +\n    \"ILicenseInfo \" +\n    \"ILifeCycleStage \" +\n    \"IList \" +\n    \"IListBox \" +\n    \"ILocalIDInfo \" +\n    \"ILocalization \" +\n    \"ILock \" +\n    \"IMemoryDataSet \" +\n    \"IMessagingFactory \" +\n    \"IMetadataRepository \" +\n    \"INotice \" +\n    \"INoticeInfo \" +\n    \"INumericCriterion \" +\n    \"INumericRequisite \" +\n    \"INumericValue \" +\n    \"IObject \" +\n    \"IObjectDescription \" +\n    \"IObjectImporter \" +\n    \"IObjectInfo \" +\n    \"IObserver \" +\n    \"IPanelGroup \" +\n    \"IPickCriterion \" +\n    \"IPickProperty \" +\n    \"IPickRequisite \" +\n    \"IPickRequisiteDescription \" +\n    \"IPickRequisiteItem \" +\n    \"IPickRequisiteItems \" +\n    \"IPickValue \" +\n    \"IPrivilege \" +\n    \"IPrivilegeList \" +\n    \"IProcess \" +\n    \"IProcessFactory \" +\n    \"IProcessMessage \" +\n    \"IProgress \" +\n    \"IProperty \" +\n    \"IPropertyChangeEvent \" +\n    \"IQuery \" +\n    \"IReference \" +\n    \"IReferenceCriterion \" +\n    \"IReferenceEnabledMode \" +\n    \"IReferenceFactory \" +\n    \"IReferenceHistoryDescription \" +\n    \"IReferenceInfo \" +\n    \"IReferenceRecordCardWizardStep \" +\n    \"IReferenceRequisiteDescription \" +\n    \"IReferencesFactory \" +\n    \"IReferenceValue \" +\n    \"IRefRequisite \" +\n    \"IReport \" +\n    \"IReportFactory \" +\n    \"IRequisite \" +\n    \"IRequisiteDescription \" +\n    \"IRequisiteDescriptionList \" +\n    \"IRequisiteFactory \" +\n    \"IRichEdit \" +\n    \"IRouteStep \" +\n    \"IRule \" +\n    \"IRuleList \" +\n    \"ISchemeBlock \" +\n    \"IScript \" +\n    \"IScriptFactory \" +\n    \"ISearchCriteria \" +\n    \"ISearchCriterion \" +\n    \"ISearchDescription \" +\n    \"ISearchFactory \" +\n    \"ISearchFolderInfo \" +\n    \"ISearchForObjectDescription \" +\n    \"ISearchResultRestrictions \" +\n    \"ISecuredContext \" +\n    \"ISelectDialog \" +\n    \"IServerEvent \" +\n    \"IServerEventFactory \" +\n    \"IServiceDialog \" +\n    \"IServiceFactory \" +\n    \"ISignature \" +\n    \"ISignProvider \" +\n    \"ISignProvider2 \" +\n    \"ISignProvider3 \" +\n    \"ISimpleCriterion \" +\n    \"IStringCriterion \" +\n    \"IStringList \" +\n    \"IStringRequisite \" +\n    \"IStringRequisiteDescription \" +\n    \"IStringValue \" +\n    \"ISystemDialogsFactory \" +\n    \"ISystemInfo \" +\n    \"ITabSheet \" +\n    \"ITask \" +\n    \"ITaskAbortReasonInfo \" +\n    \"ITaskCardWizardStep \" +\n    \"ITaskDescription \" +\n    \"ITaskFactory \" +\n    \"ITaskInfo \" +\n    \"ITaskRoute \" +\n    \"ITextCriterion \" +\n    \"ITextRequisite \" +\n    \"ITextValue \" +\n    \"ITreeListSelectDialog \" +\n    \"IUser \" +\n    \"IUserList \" +\n    \"IValue \" +\n    \"IView \" +\n    \"IWebBrowserControl \" +\n    \"IWizard \" +\n    \"IWizardAction \" +\n    \"IWizardFactory \" +\n    \"IWizardFormElement \" +\n    \"IWizardParam \" +\n    \"IWizardPickParam \" +\n    \"IWizardReferenceParam \" +\n    \"IWizardStep \" +\n    \"IWorkAccessRights \" +\n    \"IWorkDescription \" +\n    \"IWorkflowAskableParam \" +\n    \"IWorkflowAskableParams \" +\n    \"IWorkflowBlock \" +\n    \"IWorkflowBlockResult \" +\n    \"IWorkflowEnabledMode \" +\n    \"IWorkflowParam \" +\n    \"IWorkflowPickParam \" +\n    \"IWorkflowReferenceParam \" +\n    \"IWorkState \" +\n    \"IWorkTreeCustomNode \" +\n    \"IWorkTreeJobNode \" +\n    \"IWorkTreeTaskNode \" +\n    \"IXMLEditorForm \" +\n    \"SBCrypto \";\n\n  // built_in : встроенные или библиотечные объекты (константы, перечисления)\n  const BUILTIN = CONSTANTS + ENUMS;\n\n  // class: встроенные наборы значений, системные объекты, фабрики\n  const CLASS = predefined_variables;\n\n  // literal : примитивные типы\n  const LITERAL = \"null true false nil \";\n\n  // number : числа\n  const NUMBERS = {\n    className: \"number\",\n    begin: hljs.NUMBER_RE,\n    relevance: 0\n  };\n\n  // string : строки\n  const STRINGS = {\n    className: \"string\",\n    variants: [\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: \"'\",\n        end: \"'\"\n      }\n    ]\n  };\n\n  // Токены\n  const DOCTAGS = {\n    className: \"doctag\",\n    begin: \"\\\\b(?:TODO|DONE|BEGIN|END|STUB|CHG|FIXME|NOTE|BUG|XXX)\\\\b\",\n    relevance: 0\n  };\n\n  // Однострочный комментарий\n  const ISBL_LINE_COMMENT_MODE = {\n    className: \"comment\",\n    begin: \"//\",\n    end: \"$\",\n    relevance: 0,\n    contains: [\n      hljs.PHRASAL_WORDS_MODE,\n      DOCTAGS\n    ]\n  };\n\n  // Многострочный комментарий\n  const ISBL_BLOCK_COMMENT_MODE = {\n    className: \"comment\",\n    begin: \"/\\\\*\",\n    end: \"\\\\*/\",\n    relevance: 0,\n    contains: [\n      hljs.PHRASAL_WORDS_MODE,\n      DOCTAGS\n    ]\n  };\n\n  // comment : комментарии\n  const COMMENTS = {\n    variants: [\n      ISBL_LINE_COMMENT_MODE,\n      ISBL_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  // keywords : ключевые слова\n  const KEYWORDS = {\n    $pattern: UNDERSCORE_IDENT_RE,\n    keyword: KEYWORD,\n    built_in: BUILTIN,\n    class: CLASS,\n    literal: LITERAL\n  };\n\n  // methods : методы\n  const METHODS = {\n    begin: \"\\\\.\\\\s*\" + hljs.UNDERSCORE_IDENT_RE,\n    keywords: KEYWORDS,\n    relevance: 0\n  };\n\n  // type : встроенные типы\n  const TYPES = {\n    className: \"type\",\n    begin: \":[ \\\\t]*(\" + interfaces.trim().replace(/\\s/g, \"|\") + \")\",\n    end: \"[ \\\\t]*=\",\n    excludeEnd: true\n  };\n\n  // variables : переменные\n  const VARIABLES = {\n    className: \"variable\",\n    keywords: KEYWORDS,\n    begin: UNDERSCORE_IDENT_RE,\n    relevance: 0,\n    contains: [\n      TYPES,\n      METHODS\n    ]\n  };\n\n  // Имена функций\n  const FUNCTION_TITLE = FUNCTION_NAME_IDENT_RE + \"\\\\(\";\n\n  const TITLE_MODE = {\n    className: \"title\",\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      built_in: system_functions\n    },\n    begin: FUNCTION_TITLE,\n    end: \"\\\\(\",\n    returnBegin: true,\n    excludeEnd: true\n  };\n\n  // function : функции\n  const FUNCTIONS = {\n    className: \"function\",\n    begin: FUNCTION_TITLE,\n    end: \"\\\\)$\",\n    returnBegin: true,\n    keywords: KEYWORDS,\n    illegal: \"[\\\\[\\\\]\\\\|\\\\$\\\\?%,~#@]\",\n    contains: [\n      TITLE_MODE,\n      METHODS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      COMMENTS\n    ]\n  };\n\n  return {\n    name: 'ISBL',\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    illegal: \"\\\\$|\\\\?|%|,|;$|~|#|@|</\",\n    contains: [\n      FUNCTIONS,\n      TYPES,\n      METHODS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      COMMENTS\n    ]\n  };\n}\n\nmodule.exports = isbl;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB;EACA,MAAMC,mBAAmB,GAAG,yCAAyC;;EAErE;EACA,MAAMC,sBAAsB,GAAG,wCAAwC;;EAEvE;EACA,MAAMC,OAAO,GACX,+FAA+F,GAC/F,+EAA+E;;EAEjF;EACA,MAAMC,gBAAgB,GACpB,qCAAqC,GACrC,qCAAqC,GACrC,qCAAqC,GACrC,0CAA0C,GAC1C,qCAAqC,GACrC,0CAA0C,GAC1C,gDAAgD,GAChD,oDAAoD,GACpD,mDAAmD,GACnD,uDAAuD,GACvD,mDAAmD,GACnD,uDAAuD,GACvD,oDAAoD,GACpD,wDAAwD,GACxD,sDAAsD,GACtD,0DAA0D,GAC1D,mDAAmD,GACnD,uDAAuD,GACvD,4CAA4C,GAC5C,kCAAkC,GAClC,uCAAuC,GACvC,iDAAiD,GACjD,qDAAqD,GACrD,kCAAkC,GAClC,uCAAuC,GACvC,kCAAkC,GAClC,uCAAuC,GACvC,gCAAgC,GAChC,qCAAqC,GACrC,gCAAgC,GAChC,qCAAqC,GACrC,iCAAiC,GACjC,kCAAkC,GAClC,yCAAyC,GACzC,0CAA0C,GAC1C,uCAAuC,GACvC,uCAAuC,GACvC,0CAA0C,GAC1C,oCAAoC,GACpC,gCAAgC,GAChC,kCAAkC,GAClC,0CAA0C,GAC1C,kCAAkC,GAClC,6CAA6C,GAC7C,0CAA0C,GAC1C,0DAA0D,GAC1D,yDAAyD,GACzD,qDAAqD,GACrD,kEAAkE,GAClE,kDAAkD,GAClD,4CAA4C,GAC5C,+CAA+C,GAC/C,8CAA8C,GAC9C,iDAAiD,GACjD,4CAA4C,GAC5C,gCAAgC,GAChC,2CAA2C,GAC3C,uCAAuC,GACvC,0CAA0C,GAC1C,4CAA4C,GAC5C,kCAAkC,GAClC,uCAAuC,GACvC,2CAA2C,GAC3C,0CAA0C,GAC1C,2CAA2C,GAC3C,mCAAmC,GACnC,mDAAmD,GACnD,mCAAmC,GACnC,gDAAgD,GAChD,oCAAoC,GACpC,wCAAwC,GACxC,gCAAgC,GAChC,iCAAiC,GACjC,kCAAkC,GAClC,8CAA8C,GAC9C,yCAAyC,GACzC,8CAA8C,GAC9C,gCAAgC,GAChC,4CAA4C,GAC5C,uDAAuD,GACvD,yCAAyC,GACzC,+CAA+C,GAC/C,uCAAuC,GACvC,uCAAuC,GACvC,gDAAgD,GAChD,qDAAqD,GACrD,iDAAiD,GACjD,uDAAuD,GACvD,kDAAkD,GAClD,kEAAkE,GAClE,uEAAuE,GACvE,2DAA2D,GAC3D,gEAAgE,GAChE,4DAA4D,GAC5D,iEAAiE,GACjE,2DAA2D,GAC3D,gEAAgE,GAChE,6DAA6D,GAC7D,oEAAoE,GACpE,6DAA6D,GAC7D,oEAAoE,GACpE,2DAA2D,GAC3D,gEAAgE,GAChE,4DAA4D,GAC5D,iEAAiE,GACjE,2DAA2D,GAC3D,gEAAgE,GAChE,sEAAsE,GACtE,2EAA2E,GAC3E,+DAA+D,GAC/D,oEAAoE,GACpE,2EAA2E,GAC3E,gFAAgF,GAChF,gEAAgE,GAChE,qEAAqE,GACrE,6DAA6D,GAC7D,kEAAkE,GAClE,oEAAoE,GACpE,yEAAyE,GACzE,+DAA+D,GAC/D,oEAAoE,GACpE,+DAA+D,GAC/D,oEAAoE,GACpE,2EAA2E,GAC3E,gFAAgF,GAChF,gEAAgE,GAChE,qEAAqE,GACrE,6DAA6D,GAC7D,kEAAkE,GAClE,4DAA4D,GAC5D,mEAAmE,GACnE,4DAA4D,GAC5D,mEAAmE,GACnE,+DAA+D,GAC/D,oEAAoE,GACpE,iEAAiE,GACjE,sEAAsE,GACtE,gEAAgE,GAChE,qEAAqE,GACrE,4DAA4D,GAC5D,iEAAiE,GACjE,mEAAmE,GACnE,wEAAwE,GACxE,qEAAqE,GACrE,0EAA0E,GAC1E,iEAAiE,GACjE,sEAAsE,GACtE,kEAAkE,GAClE,uEAAuE,GACvE,2DAA2D,GAC3D,kEAAkE,GAClE,wCAAwC,GACxC,+BAA+B,GAC/B,oCAAoC,GACpC,2CAA2C,GAC3C,wCAAwC,GACxC,wCAAwC,GACxC,8CAA8C,GAC9C,wCAAwC,GACxC,kDAAkD,GAClD,wCAAwC,GACxC,6CAA6C,GAC7C,2CAA2C,GAC3C,+BAA+B,GAC/B,gCAAgC,GAChC,kCAAkC,GAClC,+BAA+B,GAC/B,qCAAqC,GACrC,gCAAgC,GAChC,qCAAqC,GACrC,0CAA0C,GAC1C,2BAA2B,GAC3B,6BAA6B,GAC7B,kCAAkC,GAClC,4BAA4B,GAC5B,6CAA6C,GAC7C,uDAAuD,GACvD,qCAAqC,GACrC,oCAAoC,GACpC,4CAA4C,GAC5C,4CAA4C,GAC5C,4CAA4C,GAC5C,kCAAkC,GAClC,uCAAuC,GACvC,mCAAmC,GACnC,wCAAwC,GACxC,mCAAmC,GACnC,wCAAwC,GACxC,0CAA0C,GAC1C,4CAA4C,GAC5C,yCAAyC,GACzC,yCAAyC,GACzC,iDAAiD,GACjD,gDAAgD,GAChD,wBAAwB,GACxB,yCAAyC,GACzC,+CAA+C,GAC/C,gDAAgD,GAChD,8CAA8C,GAC9C,yBAAyB,GACzB,wCAAwC,GACxC,wDAAwD,GACxD,iEAAiE,GACjE,qDAAqD,GACrD,iDAAiD,GACjD,kDAAkD,GAClD,oDAAoD,GACpD,iDAAiD,GACjD,sDAAsD,GACtD,iDAAiD,GACjD,kDAAkD,GAClD,yCAAyC,GACzC,6CAA6C,GAC7C,wCAAwC,GACxC,8CAA8C,GAC9C,uDAAuD,GACvD,yCAAyC,GACzC,6CAA6C,GAC7C,0CAA0C,GAC1C,0CAA0C,GAC1C,uCAAuC,GACvC,qCAAqC,GACrC,mCAAmC,GACnC,wCAAwC,GACxC,0CAA0C,GAC1C,gDAAgD,GAChD,qCAAqC,GACrC,gCAAgC,GAChC,8CAA8C,GAC9C,6BAA6B,GAC7B,mDAAmD,GACnD,+CAA+C,GAC/C,mDAAmD,GACnD,mCAAmC,GACnC,yCAAyC,GACzC,oCAAoC,GACpC,kDAAkD,GAClD,oCAAoC,GACpC,8CAA8C,GAC9C,yCAAyC,GACzC,sCAAsC,GACtC,sCAAsC,GACtC,kCAAkC,GAClC,mDAAmD,GACnD,2CAA2C,GAC3C,wCAAwC,GACxC,4CAA4C,GAC5C,oCAAoC,GACpC,oCAAoC,GACpC,kDAAkD,GAClD,6CAA6C,GAC7C,uCAAuC,GACvC,iCAAiC,GACjC,8BAA8B,GAC9B,+BAA+B,GAC/B,iCAAiC,GACjC,8BAA8B,GAC9B,mCAAmC,GACnC,gCAAgC,GAChC,8BAA8B,GAC9B,iCAAiC,GACjC,mCAAmC,GACnC,0CAA0C,GAC1C,kCAAkC,GAClC,8BAA8B,GAC9B,mCAAmC,GACnC,mCAAmC,GACnC,mCAAmC,GACnC,8BAA8B,GAC9B,qCAAqC,GACrC,6CAA6C,GAC7C,sDAAsD,GACtD,0CAA0C,GAC1C,yBAAyB,GACzB,yBAAyB,GACzB,yBAAyB,GACzB,yBAAyB,GACzB,yBAAyB,GACzB,yBAAyB,GACzB,iDAAiD,GACjD,0CAA0C,GAC1C,+BAA+B,GAC/B,2CAA2C,GAC3C,2CAA2C,GAC3C,mDAAmD,GACnD,mDAAmD,GACnD,sCAAsC,GACtC,4CAA4C,GAC5C,qCAAqC,GACrC,kDAAkD,GAClD,qDAAqD,GACrD,0CAA0C,GAC1C,uDAAuD,GACvD,+DAA+D,GAC/D,gDAAgD,GAChD,gDAAgD,GAChD,sCAAsC,GACtC,yDAAyD,GACzD,kDAAkD,GAClD,yDAAyD,GACzD,6CAA6C,GAC7C,mDAAmD,GACnD,yDAAyD,GACzD,4CAA4C,GAC5C,wCAAwC,GACxC,wCAAwC,GACxC,kDAAkD,GAClD,uCAAuC,GACvC,qCAAqC,GACrC,0CAA0C,GAC1C,yCAAyC,GACzC,yCAAyC,GACzC,+CAA+C,GAC/C,6DAA6D,GAC7D,+DAA+D,GAC/D,yDAAyD,GACzD,yDAAyD,GACzD,2DAA2D,GAC3D,sDAAsD,GACtD,8DAA8D,GAC9D,4CAA4C,GAC5C,yCAAyC,GACzC,8CAA8C,GAC9C,8CAA8C,GAC9C,gDAAgD,GAChD,qCAAqC,GACrC,2EAA2E,GAC3E,4CAA4C,GAC5C,uCAAuC,GACvC,+CAA+C,GAC/C,+CAA+C,GAC/C,2CAA2C,GAC3C,2BAA2B,GAC3B,2BAA2B,GAC3B,oCAAoC,GACpC,0CAA0C,GAC1C,6CAA6C,GAC7C,uCAAuC,GACvC,wCAAwC,GACxC,+CAA+C,GAC/C,wCAAwC,GACxC,0CAA0C,GAC1C,qCAAqC,GACrC,uCAAuC,GACvC,uCAAuC,GACvC,qCAAqC,GACrC,qCAAqC,GACrC,2CAA2C,GAC3C,wCAAwC,GACxC,oCAAoC,GACpC,mCAAmC,GACnC,uCAAuC,GACvC,sCAAsC,GACtC,iCAAiC,GACjC,gCAAgC,GAChC,iCAAiC,GACjC,kCAAkC,GAClC,qCAAqC,GACrC,qCAAqC,GACrC,iCAAiC,GACjC,kCAAkC,GAClC,wCAAwC,GACxC,kCAAkC,GAClC,qCAAqC,GACrC,gCAAgC,GAChC,gDAAgD,GAChD,oCAAoC,GACpC,yCAAyC,GACzC,sCAAsC,GACtC,wCAAwC,GACxC,sCAAsC,GACtC,uCAAuC,GACvC,oCAAoC,GACpC,mCAAmC,GACnC,iDAAiD,GACjD,6CAA6C,GAC7C,0CAA0C,GAC1C,0CAA0C,GAC1C,2CAA2C,GAC3C,2CAA2C,GAC3C,iDAAiD,GACjD,8CAA8C,GAC9C,yCAAyC,GACzC,6CAA6C,GAC7C,iDAAiD,GACjD,gDAAgD,GAChD,kDAAkD,GAClD,yCAAyC,GACzC,qCAAqC,GACrC,qCAAqC,GACrC,gCAAgC,GAChC,uCAAuC,GACvC,0CAA0C,GAC1C,0CAA0C,GAC1C,wCAAwC,GACxC,yBAAyB,GACzB,iCAAiC,GACjC,oCAAoC,GACpC,gCAAgC,GAChC,oCAAoC,GACpC,qCAAqC,GACrC,+BAA+B,GAC/B,8BAA8B,GAC9B,iCAAiC,GACjC,kDAAkD,GAClD,4CAA4C,GAC5C,6BAA6B,GAC7B,kCAAkC,GAClC,0CAA0C,GAC1C,iCAAiC,GACjC,qDAAqD,GACrD,6CAA6C,GAC7C,6CAA6C,GAC7C,6CAA6C,GAC7C,kDAAkD,GAClD,gDAAgD,GAChD,kDAAkD,GAClD,qCAAqC,GACrC,oCAAoC,GACpC,4BAA4B,GAC5B,+BAA+B,GAC/B,iCAAiC,GACjC,kCAAkC,GAClC,iCAAiC,GACjC,mCAAmC,GACnC,mCAAmC,GACnC,yDAAyD,GACzD,4DAA4D,GAC5D,8DAA8D,GAC9D,yEAAyE,GACzE,wEAAwE,GACxE,6DAA6D,GAC7D,kDAAkD,GAClD,8CAA8C,GAC9C,6CAA6C,GAC7C,8BAA8B,GAC9B,+CAA+C,GAC/C,qDAAqD,GACrD,iDAAiD,GACjD,0CAA0C,GAC1C,yCAAyC,GACzC,qCAAqC,GACrC,uCAAuC,GACvC,oCAAoC,GACpC,0CAA0C,GAC1C,qCAAqC,GACrC,4CAA4C,GAC5C,wCAAwC,GACxC,sDAAsD,GACtD,8BAA8B,GAC9B,uCAAuC,GACvC,+CAA+C,GAC/C,kDAAkD,GAClD,mCAAmC,GACnC,wBAAwB,GACxB,yCAAyC,GACzC,4CAA4C,GAC5C,qCAAqC,GACrC,2CAA2C,GAC3C,mCAAmC,GACnC,2BAA2B,GAC3B,qCAAqC,GACrC,4CAA4C,GAC5C,kCAAkC,GAClC,+CAA+C,GAC/C,mCAAmC,GACnC,4CAA4C,GAC5C,iCAAiC,GACjC,mCAAmC,GACnC,8BAA8B,GAC9B,kBAAkB,GAClB,6BAA6B,GAC7B,2CAA2C,GAC3C,wBAAwB,GACxB,4CAA4C,GAC5C,8CAA8C,GAC9C,wDAAwD,GACxD,8CAA8C,GAC9C,kDAAkD,GAClD,gCAAgC,GAChC,4CAA4C,GAC5C,4CAA4C,GAC5C,mCAAmC,GACnC,wCAAwC,GACxC,6BAA6B,GAC7B,sCAAsC,GACtC,0CAA0C,GAC1C,sCAAsC,GACtC,yCAAyC,GACzC,iCAAiC,GACjC,iCAAiC,GACjC,qCAAqC,GACrC,iDAAiD,GACjD,2CAA2C,GAC3C,gDAAgD,GAChD,oDAAoD,GACpD,qDAAqD,GACrD,iDAAiD,GACjD,kDAAkD,GAClD,0CAA0C,GAC1C,gCAAgC,GAChC,sCAAsC,GACtC,2BAA2B,GAC3B,4CAA4C,GAC5C,oCAAoC,GACpC,oCAAoC,GACpC,8BAA8B,GAC9B,mCAAmC,GACnC,8BAA8B,GAC9B,0CAA0C,GAC1C,wCAAwC,GACxC,2DAA2D,GAC3D,mCAAmC,GACnC,2CAA2C,GAC3C,yCAAyC,GACzC,uCAAuC,GACvC,mCAAmC,GACnC,wCAAwC,GACxC,sCAAsC,GACtC,+CAA+C,GAC/C,gDAAgD,GAChD,6BAA6B,GAC7B,kCAAkC,GAClC,gEAAgE,GAChE,2DAA2D,GAC3D,2DAA2D,GAC3D,2DAA2D,GAC3D,mCAAmC,GACnC,6CAA6C,GAC7C,oDAAoD,GACpD,2DAA2D,GAC3D,4CAA4C,GAC5C,uCAAuC,GACvC,4CAA4C,GAC5C,2CAA2C,GAC3C,0CAA0C,GAC1C,oDAAoD,GACpD,iDAAiD,GACjD,4CAA4C,GAC5C,8CAA8C,GAC9C,iDAAiD,GACjD,iDAAiD,GACjD,8CAA8C,GAC9C,wDAAwD,GACxD,uCAAuC,GACvC,kCAAkC,GAClC,oCAAoC,GACpC,2CAA2C,GAC3C,kCAAkC,GAClC,yCAAyC,GACzC,iCAAiC,GACjC,8BAA8B,GAC9B,wCAAwC,GACxC,0CAA0C,GAC1C,0CAA0C,GAC1C,qCAAqC,GACrC,sCAAsC,GACtC,6CAA6C,GAC7C,+CAA+C,GAC/C,yCAAyC,GACzC,wCAAwC,GACxC,yCAAyC,GACzC,sCAAsC,GACtC,uCAAuC,GACvC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,wCAAwC,GACxC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,yCAAyC,GACzC,wCAAwC,GACxC,wCAAwC,GACxC,wCAAwC,GACxC,wCAAwC,GACxC,wCAAwC,GACxC,wCAAwC,GACxC,wCAAwC,GACxC,0EAA0E,GAC1E,oCAAoC,GACpC,oCAAoC,GACpC,oCAAoC,GACpC,4CAA4C,GAC5C,2CAA2C,GAC3C,kCAAkC,GAClC,mCAAmC,GACnC,iCAAiC,GACjC,4CAA4C,GAC5C,4CAA4C,GAC5C,0CAA0C,GAC1C,wCAAwC,GACxC,6CAA6C,GAC7C,0CAA0C,GAC1C,6CAA6C,GAC7C,qCAAqC,GACrC,uCAAuC,GACvC,oCAAoC,GACpC,uCAAuC,GACvC,qCAAqC,GACrC,2BAA2B,GAC3B,+CAA+C,GAC/C,4CAA4C,GAC5C,sCAAsC,GACtC,gDAAgD,GAChD,gDAAgD,GAChD,gDAAgD,GAChD,gDAAgD,GAChD,gDAAgD,GAChD,gDAAgD,GAChD,0CAA0C,GAC1C,sCAAsC,GACtC,iDAAiD,GACjD,iDAAiD,GACjD,uCAAuC,GACvC,gCAAgC,GAChC,iDAAiD,GACjD,4CAA4C,GAC5C,0DAA0D,GAC1D,+CAA+C,GAC/C,6CAA6C,GAC7C,2DAA2D,GAC3D,kEAAkE,GAClE,6CAA6C,GAC7C,+DAA+D,GAC/D,qDAAqD,GACrD,8BAA8B,GAC9B,4CAA4C,GAC5C,4CAA4C,GAC5C,oCAAoC,GACpC,qCAAqC,GACrC,yCAAyC,GACzC,yCAAyC,GACzC,wCAAwC,GACxC,qCAAqC,GACrC,yCAAyC,GACzC,0CAA0C,GAC1C,0CAA0C,GAC1C,uCAAuC,GACvC,oCAAoC,GACpC,0CAA0C,GAC1C,qDAAqD,GACrD,iCAAiC,GACjC,uCAAuC,GACvC,6BAA6B,GAC7B,qDAAqD,GACrD,6CAA6C,GAC7C,8CAA8C,GAC9C,qCAAqC,GACrC,qCAAqC,GACrC,gCAAgC,GAChC,gDAAgD,GAChD,yCAAyC,GACzC,uDAAuD,GACvD,sDAAsD,GACtD,sCAAsC,GACtC,oCAAoC,GACpC,4CAA4C,GAC5C,uCAAuC,GACvC,8CAA8C,GAC9C,2DAA2D,GAC3D,qCAAqC,GACrC,yCAAyC,GACzC,wCAAwC,GACxC,wCAAwC,GACxC,uCAAuC,GACvC,kCAAkC,GAClC,mCAAmC,GACnC,oCAAoC,GACpC,kCAAkC,GAClC,+BAA+B,GAC/B,kCAAkC,GAClC,0BAA0B,GAC1B,gDAAgD,GAChD,yCAAyC,GACzC,wCAAwC,GACxC,wCAAwC,GACxC,sCAAsC,GACtC,oCAAoC,GACpC,uCAAuC,GACvC,mCAAmC,GACnC,sCAAsC,GACtC,qCAAqC,GACrC,wCAAwC,GACxC,qCAAqC,GACrC,2CAA2C,GAC3C,8BAA8B,GAC9B,mCAAmC,GACnC,mCAAmC,GACnC,8BAA8B,GAC9B,wCAAwC,GACxC,mCAAmC,GACnC,qDAAqD,GACrD,+CAA+C,GAC/C,4CAA4C,GAC5C,kDAAkD,GAClD,4CAA4C,GAC5C,4CAA4C,GAC5C,oCAAoC,GACpC,oCAAoC,GACpC,4CAA4C,GAC5C,0DAA0D,GAC1D,qEAAqE,GACrE,oCAAoC,GACpC,+CAA+C,GAC/C,qDAAqD,GACrD,4DAA4D,GAC5D,0DAA0D,GAC1D,oCAAoC,GACpC,yCAAyC,GACzC,6CAA6C,GAC7C,8CAA8C,GAC9C,yCAAyC,GACzC,qDAAqD,GACrD,iDAAiD,GACjD,4CAA4C,GAC5C,+CAA+C,GAC/C,qCAAqC,GACrC,0CAA0C,GAC1C,8CAA8C,GAC9C,8CAA8C,GAC9C,0CAA0C,GAC1C,0CAA0C,GAC1C,yCAAyC,GACzC,yCAAyC,GACzC,iDAAiD,GACjD,qCAAqC,GACrC,qCAAqC,GACrC,gDAAgD,GAChD,0CAA0C,GAC1C,wCAAwC,GACxC,+CAA+C,GAC/C,mDAAmD,GACnD,oCAAoC,GACpC,2DAA2D,GAC3D,oCAAoC,GACpC,2CAA2C,GAC3C,4DAA4D,GAC5D,wDAAwD,GACxD,mDAAmD,GACnD,+CAA+C,GAC/C,mDAAmD,GACnD,iCAAiC,GACjC,iCAAiC,GACjC,qCAAqC,GACrC,yCAAyC,GACzC,8CAA8C,GAC9C,uDAAuD,GACvD,uDAAuD,GACvD,qDAAqD,GACrD,iDAAiD,GACjD,mDAAmD,GACnD,wDAAwD,GACxD,4DAA4D,GAC5D,4DAA4D,GAC5D,wCAAwC,GACxC,wCAAwC,GACxC,4BAA4B,GAC5B,wCAAwC,GACxC,iCAAiC,GACjC,mBAAmB,GACnB,uCAAuC,GACvC,qCAAqC,GACrC,gDAAgD,GAChD,8BAA8B,GAC9B,yBAAyB;;EAE3B;EACA,MAAMC,cAAc,GAAG,gDAAgD;;EAEvE;EACA,MAAMC,yBAAyB,GAC7B,kGAAkG;;EAEpG;EACA,MAAMC,mCAAmC,GACvC,uGAAuG,GACvG,6EAA6E;;EAE/E;EACA,MAAMC,wBAAwB,GAC5B,+EAA+E,GAC/E,wEAAwE;;EAE1E;EACA,MAAMC,8BAA8B,GAClC,oCAAoC,GACpC,+BAA+B,GAC/B,yCAAyC,GACzC,gCAAgC,GAChC,8CAA8C,GAC9C,6CAA6C,GAC7C,0CAA0C,GAC1C,+BAA+B,GAC/B,kCAAkC,GAClC,8BAA8B,GAC9B,uCAAuC,GACvC,iCAAiC,GACjC,gDAAgD,GAChD,0CAA0C,GAC1C,8BAA8B,GAC9B,0BAA0B,GAC1B,0CAA0C,GAC1C,+BAA+B,GAC/B,kDAAkD,GAClD,4CAA4C,GAC5C,6BAA6B;;EAE/B;EACA,MAAMC,uBAAuB,GAAG,8CAA8C;;EAE9E;EACA,MAAMC,yCAAyC,GAC7C,uDAAuD;;EAEzD;EACA,MAAMC,mBAAmB,GACvB,4BAA4B,GAC5B,qBAAqB,GACrB,sBAAsB,GACtB,mBAAmB,GACnB,gBAAgB,GAChB,sBAAsB,GACtB,iBAAiB;;EAEnB;EACA,MAAMC,mBAAmB,GACvB,wDAAwD;;EAE1D;EACA,MAAMC,kCAAkC,GACtC,mCAAmC,GACnC,mCAAmC,GACnC,kCAAkC,GAClC,kCAAkC,GAClC,uCAAuC,GACvC,8CAA8C,GAC9C,8BAA8B,GAC9B,gDAAgD,GAChD,uCAAuC;;EAEzC;EACA,MAAMC,iCAAiC,GACrC,kCAAkC,GAClC,mCAAmC,GACnC,iDAAiD,GACjD,gDAAgD,GAChD,kCAAkC,GAClC,wCAAwC,GACxC,iCAAiC,GACjC,6CAA6C,GAC7C,6BAA6B,GAC7B,oCAAoC,GACpC,kCAAkC,GAClC,+CAA+C,GAC/C,gCAAgC;;EAElC;EACA,MAAMC,uBAAuB,GAC3B,iBAAiB,GACjB,gBAAgB,GAChB,iBAAiB,GACjB,iCAAiC,GACjC,iBAAiB,GACjB,eAAe,GACf,iBAAiB,GACjB,iBAAiB,GACjB,iCAAiC,GACjC,kBAAkB,GAClB,iBAAiB,GACjB,kBAAkB,GAClB,wBAAwB,GACxB,kBAAkB,GAClB,gBAAgB,GAChB,kBAAkB,GAClB,0BAA0B,GAC1B,mBAAmB,GACnB,mBAAmB,GACnB,kBAAkB,GAClB,qBAAqB,GACrB,mBAAmB,GACnB,4BAA4B,GAC5B,mBAAmB,GACnB,mBAAmB,GACnB,aAAa,GACb,mBAAmB,GACnB,8BAA8B,GAC9B,4BAA4B;;EAE9B;EACA,MAAMC,uBAAuB,GAC3B,6BAA6B,GAC7B,0BAA0B,GAC1B,8BAA8B;;EAEhC;EACA,MAAMC,eAAe,GACnB,oCAAoC,GACpC,0BAA0B,GAC1B,uBAAuB,GACvB,gBAAgB,GAChB,iBAAiB,GACjB,4BAA4B,GAC5B,2CAA2C,GAC3C,kCAAkC,GAClC,4CAA4C,GAC5C,oDAAoD,GACpD,+BAA+B,GAC/B,uCAAuC,GACvC,uCAAuC,GACvC,wBAAwB,GACxB,oBAAoB,GACpB,4BAA4B,GAC5B,8BAA8B,GAC9B,gBAAgB,GAChB,gBAAgB,GAChB,gDAAgD,GAChD,8CAA8C,GAC9C,yBAAyB,GACzB,8BAA8B,GAC9B,+BAA+B,GAC/B,mBAAmB,GACnB,YAAY,GACZ,kBAAkB,GAClB,WAAW,GACX,OAAO,GACP,YAAY,GACZ,OAAO,GACP,QAAQ,GACR,aAAa,GACb,sCAAsC,GACtC,OAAO,GACP,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,8BAA8B,GAC9B,oCAAoC,GACpC,qCAAqC,GACrC,iCAAiC,GACjC,kCAAkC,GAClC,gDAAgD,GAChD,kCAAkC,GAClC,YAAY,GACZ,UAAU,GACV,YAAY,GACZ,cAAc,GACd,gCAAgC,GAChC,SAAS,GACT,SAAS,GACT,mBAAmB,GACnB,uBAAuB,GACvB,wBAAwB,GACxB,mCAAmC,GACnC,mBAAmB,GACnB,wBAAwB,GACxB,4BAA4B,GAC5B,+BAA+B,GAC/B,mBAAmB,GACnB,8BAA8B,GAC9B,sCAAsC,GACtC,2BAA2B,GAC3B,+BAA+B,GAC/B,+CAA+C;;EAEjD;EACA,MAAMC,oBAAoB,GACxB,kCAAkC,GAClC,+BAA+B,GAC/B,+BAA+B,GAC/B,4BAA4B,GAC5B,gBAAgB,GAChB,0BAA0B,GAC1B,iCAAiC,GACjC,+BAA+B,GAC/B,kCAAkC,GAClC,+BAA+B,GAC/B,wBAAwB,GACxB,4BAA4B,GAC5B,6BAA6B,GAC7B,2BAA2B,GAC3B,2BAA2B,GAC3B,wBAAwB,GACxB,2CAA2C,GAC3C,kCAAkC;;EAEpC;EACA,MAAMC,8BAA8B,GAClC,oCAAoC,GACpC,gDAAgD,GAChD,gDAAgD,GAChD,gDAAgD,GAChD,uDAAuD,GACvD,kCAAkC,GAClC,oDAAoD,GACpD,8BAA8B,GAC9B,wCAAwC,GACxC,2CAA2C,GAC3C,4CAA4C,GAC5C,gCAAgC,GAChC,iDAAiD,GACjD,qCAAqC,GACrC,mCAAmC,GACnC,uCAAuC,GACvC,6BAA6B,GAC7B,6BAA6B,GAC7B,+BAA+B;;EAEjC;EACA,MAAMC,6CAA6C,GACjD,2BAA2B,GAC3B,wBAAwB,GACxB,oCAAoC;;EAEtC;EACA,MAAMC,6CAA6C,GACjD,oBAAoB,GACpB,wBAAwB,GACxB,wBAAwB,GACxB,0BAA0B;;EAE5B;EACA,MAAMC,oCAAoC,GACxC,sCAAsC,GACtC,kCAAkC,GAClC,oCAAoC,GACpC,uCAAuC;;EAEzC;EACA,MAAMC,wBAAwB,GAC5B,cAAc,GACd,mBAAmB,GACnB,iCAAiC,GACjC,2BAA2B,GAC3B,4BAA4B,GAC5B,yBAAyB,GACzB,2BAA2B,GAC3B,0BAA0B,GAC1B,6BAA6B,GAC7B,kBAAkB,GAClB,mBAAmB,GACnB,oBAAoB,GACpB,qBAAqB,GACrB,iCAAiC,GACjC,gBAAgB,GAChB,0BAA0B,GAC1B,qBAAqB,GACrB,sBAAsB,GACtB,6CAA6C,GAC7C,qBAAqB,GACrB,0BAA0B,GAC1B,qCAAqC,GACrC,wCAAwC,GACxC,0BAA0B,GAC1B,uBAAuB,GACvB,mBAAmB,GACnB,8BAA8B,GAC9B,qCAAqC,GACrC,uBAAuB,GACvB,mBAAmB,GACnB,mBAAmB,GACnB,2BAA2B,GAC3B,0BAA0B,GAC1B,iDAAiD,GACjD,oDAAoD,GACpD,6BAA6B,GAC7B,qBAAqB,GACrB,sBAAsB,GACtB,uCAAuC,GACvC,oCAAoC,GACpC,qCAAqC,GACrC,uCAAuC,GACvC,yCAAyC,GACzC,gCAAgC,GAChC,mCAAmC,GACnC,kCAAkC,GAClC,8CAA8C,GAC9C,sCAAsC,GACtC,mCAAmC,GACnC,0CAA0C,GAC1C,4BAA4B,GAC5B,4BAA4B,GAC5B,4BAA4B,GAC5B,mCAAmC,GACnC,+CAA+C,GAC/C,kCAAkC,GAClC,yCAAyC,GACzC,+BAA+B,GAC/B,8BAA8B,GAC9B,2BAA2B,GAC3B,8BAA8B,GAC9B,oCAAoC,GACpC,gDAAgD,GAChD,oCAAoC,GACpC,qCAAqC,GACrC,yCAAyC,GACzC,4BAA4B,GAC5B,mCAAmC,GACnC,+CAA+C,GAC/C,kCAAkC,GAClC,mCAAmC,GACnC,8BAA8B,GAC9B,iCAAiC,GACjC,wCAAwC,GACxC,iCAAiC,GACjC,sCAAsC,GACtC,+BAA+B,GAC/B,6BAA6B,GAC7B,6BAA6B,GAC7B,gCAAgC,GAChC,gCAAgC,GAChC,6BAA6B,GAC7B,yCAAyC,GACzC,6BAA6B,GAC7B,0BAA0B,GAC1B,2BAA2B,GAC3B,6BAA6B,GAC7B,kCAAkC,GAClC,+BAA+B,GAC/B,6BAA6B,GAC7B,iCAAiC,GACjC,+BAA+B,GAC/B,2BAA2B,GAC3B,qCAAqC,GACrC,6BAA6B,GAC7B,2BAA2B,GAC3B,6BAA6B,GAC7B,oCAAoC,GACpC,6CAA6C,GAC7C,qBAAqB,GACrB,uBAAuB,GACvB,sBAAsB,GACtB,oBAAoB,GACpB,4BAA4B,GAC5B,2BAA2B,GAC3B,mBAAmB,GACnB,8BAA8B,GAC9B,0BAA0B,GAC1B,2BAA2B,GAC3B,yBAAyB,GACzB,mBAAmB,GACnB,wBAAwB,GACxB,YAAY,GACZ,qBAAqB,GACrB,0BAA0B,GAC1B,qBAAqB,GACrB,wBAAwB,GACxB,cAAc,GACd,0BAA0B,GAC1B,cAAc,GACd,yBAAyB,GACzB,kBAAkB,GAClB,yCAAyC,GACzC,uCAAuC,GACvC,8CAA8C,GAC9C,kDAAkD,GAClD,yCAAyC,GACzC,uBAAuB,GACvB,4BAA4B,GAC5B,wBAAwB,GACxB,2BAA2B,GAC3B,wBAAwB,GACxB,2BAA2B,GAC3B,2BAA2B,GAC3B,yBAAyB,GACzB,wBAAwB,GACxB,wBAAwB,GACxB,sBAAsB,GACtB,sBAAsB,GACtB,2BAA2B,GAC3B,kCAAkC,GAClC,8CAA8C,GAC9C,iCAAiC,GACjC,wCAAwC,GACxC,8BAA8B,GAC9B,6BAA6B,GAC7B,0BAA0B,GAC1B,kCAAkC,GAClC,yCAAyC,GACzC,6BAA6B,GAC7B,mCAAmC,GACnC,mCAAmC,GACnC,+CAA+C,GAC/C,mCAAmC,GACnC,oCAAoC,GACpC,2BAA2B,GAC3B,kCAAkC,GAClC,8CAA8C,GAC9C,iCAAiC,GACjC,gCAAgC,GAChC,iCAAiC,GACjC,kCAAkC,GAClC,6BAA6B,GAC7B,gCAAgC,GAChC,uCAAuC,GACvC,gCAAgC,GAChC,qCAAqC,GACrC,8BAA8B,GAC9B,4BAA4B,GAC5B,4BAA4B,GAC5B,+BAA+B,GAC/B,+BAA+B,GAC/B,4BAA4B,GAC5B,wCAAwC,GACxC,2BAA2B,GAC3B,eAAe,GACf,eAAe,GACf,+BAA+B,GAC/B,cAAc,GACd,cAAc,GACd,iBAAiB,GACjB,qCAAqC,GACrC,gCAAgC,GAChC,uCAAuC,GACvC,6BAA6B,GAC7B,8BAA8B,GAC9B,wBAAwB,GACxB,yBAAyB,GACzB,wCAAwC,GACxC,sBAAsB,GACtB,8BAA8B,GAC9B,qBAAqB,GACrB,8BAA8B,GAC9B,qBAAqB,GACrB,wCAAwC,GACxC,sBAAsB,GACtB,gCAAgC,GAChC,qCAAqC,GACrC,4CAA4C,GAC5C,+CAA+C,GAC/C,sCAAsC,GACtC,6CAA6C,GAC7C,2CAA2C,GAC3C,wCAAwC,GACxC,gCAAgC,GAChC,gCAAgC,GAChC,0BAA0B,GAC1B,gCAAgC,GAChC,yBAAyB,GACzB,8BAA8B;;EAEhC;EACA,MAAMC,gBAAgB,GAAG,sCAAsC;;EAE/D;EACA,MAAMC,6BAA6B,GACjC,0BAA0B,GAC1B,mCAAmC,GACnC,wCAAwC,GACxC,8CAA8C,GAC9C,+BAA+B,GAC/B,8CAA8C,GAC9C,kCAAkC,GAClC,gCAAgC,GAChC,mCAAmC,GACnC,uCAAuC,GACvC,6CAA6C,GAC7C,iCAAiC,GACjC,kCAAkC,GAClC,uCAAuC,GACvC,wCAAwC,GACxC,0BAA0B,GAC1B,8BAA8B,GAC9B,6BAA6B,GAC7B,gCAAgC;;EAElC;EACA,MAAMC,iCAAiC,GACrC,kCAAkC,GAClC,kCAAkC,GAClC,0CAA0C,GAC1C,6BAA6B,GAC7B,+BAA+B;;EAEjC;EACA,MAAMC,kCAAkC,GACtC,wCAAwC,GACxC,mCAAmC,GACnC,oCAAoC,GACpC,qCAAqC,GACrC,kDAAkD,GAClD,iDAAiD,GACjD,mCAAmC,GACnC,sCAAsC,GACtC,+BAA+B,GAC/B,kCAAkC,GAClC,oCAAoC,GACpC,mCAAmC,GACnC,oDAAoD,GACpD,8CAA8C,GAC9C,mCAAmC,GACnC,8BAA8B,GAC9B,wCAAwC,GACxC,oCAAoC,GACpC,sDAAsD,GACtD,gDAAgD,GAChD,sCAAsC,GACtC,wCAAwC,GACxC,4BAA4B,GAC5B,sCAAsC,GACtC,iCAAiC,GACjC,sCAAsC,GACtC,8BAA8B,GAC9B,oDAAoD,GACpD,4CAA4C,GAC5C,gDAAgD;;EAElD;EACA,MAAMC,0BAA0B,GAC9B,uBAAuB,GACvB,kBAAkB,GAClB,eAAe,GACf,kBAAkB,GAClB,gBAAgB;;EAElB;EACA,MAAMC,wBAAwB,GAC5B,0BAA0B,GAC1B,kCAAkC,GAClC,wBAAwB,GACxB,4BAA4B,GAC5B,sCAAsC,GACtC,oCAAoC,GACpC,iCAAiC,GACjC,0BAA0B,GAC1B,yBAAyB,GACzB,6BAA6B,GAC7B,8BAA8B,GAC9B,gCAAgC,GAChC,2BAA2B,GAC3B,8BAA8B,GAC9B,0BAA0B,GAC1B,0BAA0B,GAC1B,wBAAwB,GACxB,kCAAkC;;EAEpC;EACA,MAAMC,gCAAgC,GACpC,6BAA6B,GAC7B,gCAAgC,GAChC,kCAAkC,GAClC,kCAAkC,GAClC,oCAAoC,GACpC,yCAAyC,GACzC,yBAAyB,GACzB,iCAAiC,GACjC,0BAA0B,GAC1B,oBAAoB,GACpB,mBAAmB,GACnB,+BAA+B,GAC/B,4BAA4B,GAC5B,iBAAiB,GACjB,4BAA4B,GAC5B,iBAAiB,GACjB,oBAAoB,GACpB,oBAAoB,GACpB,mCAAmC,GACnC,8BAA8B,GAC9B,wCAAwC,GACxC,yBAAyB,GACzB,2BAA2B,GAC3B,yBAAyB,GACzB,8BAA8B,GAC9B,8BAA8B,GAC9B,6BAA6B,GAC7B,iCAAiC,GACjC,yBAAyB,GACzB,kCAAkC,GAClC,qCAAqC,GACrC,wBAAwB,GACxB,iBAAiB,GACjB,yBAAyB,GACzB,yBAAyB,GACzB,mBAAmB,GACnB,qBAAqB,GACrB,eAAe,GACf,iCAAiC,GACjC,gCAAgC,GAChC,iBAAiB,GACjB,oBAAoB,GACpB,wBAAwB,GACxB,8BAA8B,GAC9B,8BAA8B,GAC9B,yBAAyB,GACzB,oBAAoB,GACpB,+BAA+B,GAC/B,wBAAwB,GACxB,kCAAkC,GAClC,uCAAuC,GACvC,iBAAiB,GACjB,eAAe,GACf,4BAA4B,GAC5B,sBAAsB,GACtB,iBAAiB,GACjB,kBAAkB,GAClB,uBAAuB,GACvB,+BAA+B,GAC/B,+BAA+B,GAC/B,yBAAyB,GACzB,kBAAkB,GAClB,yBAAyB,GACzB,sBAAsB,GACtB,0BAA0B,GAC1B,oBAAoB,GACpB,qBAAqB,GACrB,yBAAyB,GACzB,eAAe,GACf,eAAe,GACf,qBAAqB,GACrB,+BAA+B,GAC/B,2BAA2B,GAC3B,eAAe,GACf,yBAAyB,GACzB,iBAAiB,GACjB,gCAAgC;;EAElC;EACA,MAAMC,oBAAoB,GACxB,2BAA2B,GAC3B,yBAAyB,GACzB,wBAAwB;;EAE1B;EACA,MAAMC,cAAc,GAClB,mBAAmB,GACnB,uBAAuB,GACvB,oBAAoB,GACpB,sBAAsB,GACtB,yBAAyB,GACzB,wBAAwB,GACxB,2BAA2B,GAC3B,wBAAwB,GACxB,sBAAsB,GACtB,wBAAwB,GACxB,4BAA4B,GAC5B,uBAAuB;;EAEzB;EACA,MAAMC,kCAAkC,GACtC,oBAAoB,GACpB,kBAAkB,GAClB,aAAa,GACb,uBAAuB,GACvB,gBAAgB,GAChB,sBAAsB,GACtB,WAAW,GACX,OAAO;;EAET;EACA,MAAMC,4BAA4B,GAChC,iCAAiC,GACjC,iCAAiC,GACjC,mCAAmC;;EAErC;EACA,MAAMC,qCAAqC,GACzC,4BAA4B,GAC5B,4BAA4B,GAC5B,OAAO,GACP,WAAW,GACX,kBAAkB,GAClB,WAAW,GACX,gBAAgB,GAChB,mBAAmB,GACnB,kBAAkB,GAClB,gBAAgB;;EAElB;EACA,MAAMC,+BAA+B,GACnC,cAAc,GAAG,YAAY,GAAG,aAAa;;EAE/C;EACA,MAAMC,+BAA+B,GACnC,gCAAgC,GAChC,gCAAgC,GAChC,+BAA+B,GAC/B,2CAA2C,GAC3C,2BAA2B,GAC3B,6CAA6C;;EAE/C;EACA,MAAMC,uBAAuB,GAC3B,gBAAgB,GAChB,eAAe,GACf,gBAAgB,GAChB,gBAAgB,GAChB,eAAe,GACf,gBAAgB,GAChB,kBAAkB,GAClB,mBAAmB,GACnB,iBAAiB;;EAEnB;EACA,MAAMC,SAAS,GACbpC,gBAAgB,GAChBC,cAAc,GACdC,yBAAyB,GACzBC,mCAAmC,GACnCC,wBAAwB,GACxBC,8BAA8B,GAC9BC,uBAAuB,GACvBC,yCAAyC,GACzCC,mBAAmB,GACnBC,mBAAmB,GACnBC,kCAAkC,GAClCC,iCAAiC,GACjCC,uBAAuB,GACvBC,uBAAuB,GACvBC,eAAe,GACfC,oBAAoB,GACpBC,8BAA8B,GAC9BC,6CAA6C,GAC7CC,6CAA6C,GAC7CC,oCAAoC,GACpCC,wBAAwB,GACxBC,gBAAgB,GAChBC,6BAA6B,GAC7BC,iCAAiC,GACjCC,kCAAkC,GAClCC,0BAA0B,GAC1BC,wBAAwB,GACxBC,gCAAgC,GAChCC,oBAAoB,GACpBC,cAAc,GACdC,kCAAkC,GAClCC,4BAA4B,GAC5BC,qCAAqC,GACrCC,+BAA+B,GAC/BC,+BAA+B,GAC/BC,uBAAuB;;EAEzB;EACA,MAAME,YAAY,GAAG,wBAAwB;;EAE7C;EACA,MAAMC,kBAAkB,GACtB,mBAAmB,GACnB,oBAAoB,GACpB,qBAAqB,GACrB,mBAAmB,GACnB,2BAA2B;;EAE7B;EACA,MAAMC,YAAY,GAAG,gBAAgB;;EAErC;EACA,MAAMC,UAAU,GAAG,iBAAiB;;EAEpC;EACA,MAAMC,aAAa,GACjB,WAAW,GACX,oBAAoB,GACpB,gBAAgB,GAChB,qBAAqB,GACrB,YAAY;;EAEd;EACA,MAAMC,8BAA8B,GAAG,uBAAuB;;EAE9D;EACA,MAAMC,gBAAgB,GAAG,yCAAyC;;EAElE;EACA,MAAMC,sBAAsB,GAAG,oCAAoC;;EAEnE;EACA,MAAMC,WAAW,GAAG,yBAAyB;;EAE7C;EACA,MAAMC,SAAS,GACb,aAAa,GACb,cAAc,GACd,WAAW,GACX,YAAY,GACZ,WAAW,GACX,WAAW,GACX,aAAa,GACb,WAAW,GACX,cAAc,GACd,SAAS,GACT,QAAQ,GACR,WAAW,GACX,eAAe;;EAEjB;EACA,MAAMC,gBAAgB,GAAG,uBAAuB;;EAEhD;EACA,MAAMC,iBAAiB,GAAG,+BAA+B;;EAEzD;EACA,MAAMC,YAAY,GAChB,WAAW,GACX,cAAc,GACd,SAAS,GACT,QAAQ,GACR,mBAAmB,GACnB,QAAQ,GACR,cAAc,GACd,WAAW,GACX,WAAW,GACX,WAAW;;EAEb;EACA,MAAMC,YAAY,GAChB,eAAe,GACf,UAAU,GACV,WAAW,GACX,iBAAiB,GACjB,aAAa,GACb,cAAc,GACd,SAAS,GACT,eAAe,GACf,eAAe,GACf,WAAW,GACX,eAAe,GACf,WAAW,GACX,gBAAgB,GAChB,iBAAiB,GACjB,kBAAkB,GAClB,SAAS,GACT,aAAa,GACb,gBAAgB,GAChB,SAAS,GACT,UAAU,GACV,YAAY,GACZ,gBAAgB,GAChB,aAAa,GACb,aAAa,GACb,eAAe,GACf,UAAU,GACV,cAAc,GACd,UAAU,GACV,oBAAoB,GACpB,WAAW,GACX,aAAa,GACb,eAAe,GACf,eAAe,GACf,cAAc;;EAEhB;EACA,MAAMC,qBAAqB,GACzB,UAAU,GACV,aAAa,GACb,aAAa,GACb,UAAU,GACV,eAAe,GACf,YAAY,GACZ,UAAU;;EAEZ;EACA,MAAMC,YAAY,GAAG,gCAAgC;;EAErD;EACA,MAAMC,iBAAiB,GACrB,gBAAgB,GAChB,eAAe,GACf,iBAAiB,GACjB,gBAAgB,GAChB,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,iCAAiC,GACjC,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,mBAAmB,GACnB,kBAAkB,GAClB,4BAA4B,GAC5B,iBAAiB,GACjB,iCAAiC,GACjC,mBAAmB,GACnB,iBAAiB,GACjB,kBAAkB,GAClB,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,2BAA2B,GAC3B,wBAAwB,GACxB,qBAAqB,GACrB,0BAA0B;;EAE5B;EACA,MAAMC,aAAa,GAAG,0CAA0C;;EAEhE;EACA,MAAMC,eAAe,GAAG,gDAAgD;;EAExE;EACA,MAAMC,eAAe,GAAG,yCAAyC;;EAEjE;EACA,MAAMC,aAAa,GAAG,sBAAsB;;EAE5C;EACA,MAAMC,gBAAgB,GAAG,8BAA8B;;EAEvD;EACA,MAAMC,sBAAsB,GAAG,kBAAkB;;EAEjD;EACA,MAAMC,SAAS,GACb,SAAS,GACT,SAAS,GACT,SAAS,GACT,mBAAmB,GACnB,qBAAqB,GACrB,sBAAsB,GACtB,kBAAkB,GAClB,oBAAoB,GACpB,WAAW,GACX,kBAAkB,GAClB,WAAW,GACX,2BAA2B,GAC3B,UAAU,GACV,UAAU,GACV,WAAW,GACX,iBAAiB,GACjB,mBAAmB,GACnB,YAAY,GACZ,WAAW,GACX,WAAW,GACX,iBAAiB,GACjB,cAAc,GACd,iBAAiB,GACjB,0BAA0B,GAC1B,yBAAyB,GACzB,kCAAkC,GAClC,+BAA+B,GAC/B,2BAA2B,GAC3B,0CAA0C,GAC1C,wBAAwB,GACxB,YAAY,GACZ,gBAAgB,GAChB,kBAAkB,GAClB,iBAAiB,GACjB,wBAAwB,GACxB,kBAAkB,GAClB,yBAAyB,GACzB,kBAAkB,GAClB,WAAW,GACX,aAAa,GACb,iBAAiB,GACjB,kBAAkB,GAClB,kBAAkB,GAClB,qBAAqB,GACrB,yBAAyB,GACzB,aAAa;;EAEf;EACA,MAAMC,sBAAsB,GAAG,uBAAuB;;EAEtD;EACA,MAAMC,sBAAsB,GAAG,8CAA8C;;EAE7E;EACA,MAAMC,kBAAkB,GAAG,gCAAgC;;EAE3D;EACA,MAAMC,sBAAsB,GAAG,oBAAoB;;EAEnD;EACA,MAAMC,yBAAyB,GAAG,wCAAwC;;EAE1E;EACA,MAAMC,yBAAyB,GAAG,4BAA4B;;EAE9D;EACA,MAAMC,qBAAqB,GAAG,yBAAyB;;EAEvD;EACA,MAAMC,2BAA2B,GAC/B,+EAA+E;;EAEjF;EACA,MAAMC,sBAAsB,GAAG,yCAAyC;;EAExE;EACA,MAAMC,WAAW,GAAG,wDAAwD;;EAE5E;EACA,MAAMC,kBAAkB,GAAG,sCAAsC;;EAEjE;EACA,MAAMC,uBAAuB,GAAG,0BAA0B;;EAE1D;EACA,MAAMC,oBAAoB,GAAG,iCAAiC;;EAE9D;EACA,MAAMC,cAAc,GAClB,YAAY,GACZ,aAAa,GACb,aAAa,GACb,UAAU,GACV,UAAU,GACV,aAAa,GACb,gBAAgB,GAChB,kBAAkB;;EAEpB;EACA,MAAMC,WAAW,GACf,UAAU,GACV,WAAW,GACX,cAAc,GACd,iBAAiB,GACjB,eAAe,GACf,eAAe,GACf,gBAAgB,GAChB,cAAc,GACd,WAAW;;EAEb;EACA,MAAMC,cAAc,GAAG,UAAU,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;;EAElE;EACA,MAAMC,cAAc,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU;;EAE1D;EACA,MAAMC,gBAAgB,GACpB,SAAS,GACT,UAAU,GACV,mBAAmB,GACnB,oBAAoB,GACpB,UAAU,GACV,SAAS;;EAEX;EACA,MAAMC,UAAU,GAAG,gBAAgB,GAAG,WAAW,GAAG,iBAAiB;;EAErE;EACA,MAAMC,UAAU,GAAG,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,QAAQ;;EAE7D;EACA,MAAMC,gBAAgB,GACpB,iBAAiB,GAAG,aAAa,GAAG,WAAW,GAAG,YAAY;;EAEhE;EACA,MAAMC,YAAY,GAChB,YAAY,GACZ,WAAW,GACX,aAAa,GACb,qBAAqB,GACrB,mBAAmB,GACnB,wBAAwB,GACxB,kBAAkB,GAClB,qBAAqB,GACrB,uBAAuB,GACvB,gCAAgC,GAChC,6BAA6B,GAC7B,oBAAoB,GACpB,4BAA4B,GAC5B,6BAA6B,GAC7B,iCAAiC,GACjC,mCAAmC,GACnC,sBAAsB,GACtB,4BAA4B,GAC5B,4BAA4B,GAC5B,2BAA2B,GAC3B,sBAAsB,GACtB,yBAAyB,GACzB,oCAAoC,GACpC,6BAA6B,GAC7B,oCAAoC,GACpC,mCAAmC,GACnC,uBAAuB,GACvB,6BAA6B,GAC7B,2BAA2B,GAC3B,iCAAiC,GACjC,mCAAmC,GACnC,oCAAoC,GACpC,sBAAsB,GACtB,yBAAyB,GACzB,6BAA6B,GAC7B,4BAA4B,GAC5B,0BAA0B,GAC1B,+BAA+B,GAC/B,8BAA8B,GAC9B,wBAAwB,GACxB,6BAA6B,GAC7B,sCAAsC,GACtC,2BAA2B,GAC3B,iCAAiC,GACjC,0BAA0B,GAC1B,yBAAyB,GACzB,oBAAoB,GACpB,eAAe,GACf,iBAAiB,GACjB,0BAA0B;;EAE5B;EACA,MAAMC,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,mBAAmB;;EAE7D;EACA,MAAMC,QAAQ,GAAG,QAAQ,GAAG,WAAW,GAAG,eAAe;;EAEzD;EACA,MAAMC,SAAS,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU;;EAE9E;EACA,MAAMC,SAAS,GAAG,WAAW,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW;;EAEtE;EACA,MAAMC,cAAc,GAAG,mBAAmB,GAAG,aAAa;;EAE1D;EACA,MAAMC,wBAAwB,GAC5B,eAAe,GACf,WAAW,GACX,WAAW,GACX,SAAS,GACT,UAAU,GACV,YAAY,GACZ,WAAW;;EAEb;EACA,MAAMC,wBAAwB,GAAG,YAAY,GAAG,eAAe,GAAG,YAAY;;EAE9E;EACA,MAAMC,iCAAiC,GACrC,oBAAoB,GACpB,aAAa,GACb,aAAa,GACb,eAAe,GACf,iBAAiB,GACjB,uBAAuB,GACvB,qBAAqB,GACrB,oBAAoB,GACpB,oBAAoB,GACpB,aAAa,GACb,mBAAmB;;EAErB;EACA,MAAMC,8BAA8B,GAClC,YAAY,GAAG,YAAY,GAAG,eAAe,GAAG,cAAc;;EAEhE;EACA,MAAMC,eAAe,GACnB,UAAU,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,UAAU;;EAElE;EACA,MAAMC,aAAa,GACjB,cAAc,GACd,UAAU,GACV,aAAa,GACb,aAAa,GACb,cAAc,GACd,aAAa,GACb,cAAc,GACd,aAAa,GACb,aAAa,GACb,cAAc;;EAEhB;EACA,MAAMC,kBAAkB,GAAG,WAAW,GAAG,SAAS,GAAG,YAAY;;EAEjE;EACA,MAAMC,YAAY,GAChB,YAAY,GACZ,aAAa,GACb,aAAa,GACb,UAAU,GACV,eAAe,GACf,aAAa,GACb,UAAU,GACV,UAAU,GACV,aAAa,GACb,kBAAkB,GAClB,cAAc;;EAEhB;EACA,MAAMC,mBAAmB,GAAG,aAAa,GAAG,mBAAmB;;EAE/D;EACA,MAAMC,WAAW,GAAG,WAAW,GAAG,UAAU,GAAG,SAAS,GAAG,WAAW;;EAEtE;EACA,MAAMC,eAAe,GACnB,YAAY,GAAG,YAAY,GAAG,iBAAiB,GAAG,iBAAiB;;EAErE;EACA,MAAMC,WAAW,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS;;EAEvD;EACA,MAAMC,cAAc,GAAG,SAAS,GAAG,mBAAmB,GAAG,cAAc;;EAEvE;EACA,MAAMC,kBAAkB,GAAG,YAAY,GAAG,YAAY;;EAEtD;EACA,MAAMC,gBAAgB,GAAG,cAAc,GAAG,iBAAiB;;EAE3D;EACA,MAAMC,gBAAgB,GAAG,WAAW,GAAG,aAAa;;EAEpD;EACA,MAAMC,8BAA8B,GAClC,aAAa,GACb,cAAc,GACd,cAAc,GACd,eAAe,GACf,sBAAsB,GACtB,WAAW,GACX,WAAW,GACX,cAAc,GACd,gBAAgB,GAChB,cAAc,GACd,wBAAwB,GACxB,wBAAwB,GACxB,uBAAuB,GACvB,qBAAqB,GACrB,yBAAyB,GACzB,wBAAwB,GACxB,gCAAgC,GAChC,0BAA0B,GAC1B,wBAAwB,GACxB,eAAe,GACf,cAAc;;EAEhB;EACA,MAAMC,gBAAgB,GAAG,iBAAiB,GAAG,8BAA8B;;EAE3E;EACA,MAAMC,cAAc,GAAG,cAAc,GAAG,iBAAiB,GAAG,aAAa;;EAEzE;EACA,MAAMC,iBAAiB,GACrB,SAAS,GACT,cAAc,GACd,cAAc,GACd,eAAe,GACf,iBAAiB,GACjB,gBAAgB,GAChB,iBAAiB,GACjB,iBAAiB,GACjB,aAAa;;EAEf;EACA,MAAMC,SAAS,GACb,YAAY,GACZ,SAAS,GACT,cAAc,GACd,kBAAkB,GAClB,oBAAoB,GACpB,iBAAiB;;EAEnB;EACA,MAAMC,gBAAgB,GACpB,QAAQ,GAAG,cAAc,GAAG,OAAO,GAAG,UAAU,GAAG,SAAS;;EAE9D;EACA,MAAMC,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,eAAe;;EAE3D;EACA,MAAMC,kBAAkB,GACtB,YAAY,GAAG,cAAc,GAAG,mBAAmB,GAAG,iBAAiB;;EAEzE;EACA,MAAMC,iBAAiB,GACrB,eAAe,GAAG,WAAW,GAAG,aAAa,GAAG,aAAa;;EAE/D;EACA,MAAMC,0BAA0B,GAC9B,gBAAgB,GAChB,YAAY,GACZ,YAAY,GACZ,YAAY,GACZ,eAAe,GACf,eAAe,GACf,iBAAiB,GACjB,WAAW,GACX,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,aAAa,GACb,wBAAwB,GACxB,wBAAwB,GACxB,wBAAwB;;EAE1B;EACA,MAAMC,sBAAsB,GAC1B,qBAAqB,GAAG,WAAW,GAAG,gBAAgB,GAAG,YAAY;;EAEvE;EACA,MAAMC,gBAAgB,GACpB,YAAY,GACZ,aAAa,GACb,aAAa,GACb,aAAa,GACb,cAAc,GACd,UAAU,GACV,UAAU,GACV,UAAU,GACV,cAAc,GACd,mBAAmB,GACnB,uBAAuB,GACvB,yBAAyB,GACzB,6BAA6B,GAC7B,gBAAgB,GAChB,cAAc,GACd,cAAc,GACd,cAAc,GACd,UAAU;;EAEZ;EACA,MAAMC,iBAAiB,GACrB,cAAc,GACd,YAAY,GACZ,gBAAgB,GAChB,YAAY,GACZ,YAAY,GACZ,aAAa;;EAEf;EACA,MAAMC,eAAe,GACnB,UAAU,GACV,eAAe,GACf,cAAc,GACd,yBAAyB,GACzB,WAAW;;EAEb;EACA,MAAMC,eAAe,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW;;EAEhE;EACA,MAAMC,kBAAkB,GACtB,WAAW,GACX,YAAY,GACZ,YAAY,GACZ,UAAU,GACV,cAAc,GACd,UAAU,GACV,aAAa,GACb,YAAY,GACZ,eAAe,GACf,aAAa,GACb,oBAAoB,GACpB,WAAW;;EAEb;EACA,MAAMC,iBAAiB,GACrB,aAAa,GACb,WAAW,GACX,YAAY,GACZ,UAAU,GACV,cAAc,GACd,aAAa,GACb,UAAU,GACV,SAAS,GACT,YAAY,GACZ,eAAe,GACf,qBAAqB,GACrB,UAAU,GACV,WAAW,GACX,UAAU,GACV,uBAAuB,GACvB,qBAAqB,GACrB,sBAAsB,GACtB,oBAAoB,GACpB,wBAAwB,GACxB,uBAAuB,GACvB,oBAAoB,GACpB,mBAAmB,GACnB,sBAAsB,GACtB,yBAAyB,GACzB,+BAA+B,GAC/B,oBAAoB,GACpB,qBAAqB,GACrB,oBAAoB,GACpB,cAAc,GACd,cAAc,GACd,uBAAuB,GACvB,cAAc,GACd,aAAa,GACb,uBAAuB;;EAEzB;EACA,MAAMC,eAAe,GAAG,QAAQ,GAAG,WAAW,GAAG,SAAS;;EAE1D;EACA,MAAMC,cAAc,GAAG,UAAU,GAAG,UAAU;;EAE9C;EACA,MAAMC,UAAU,GACd,SAAS,GACT,YAAY,GACZ,SAAS,GACT,eAAe,GACf,YAAY,GACZ,cAAc;;EAEhB;EACA,MAAMC,qBAAqB,GACzB,UAAU,GAAG,iBAAiB,GAAG,iBAAiB;;EAEpD;EACA,MAAMC,KAAK,GACTxF,YAAY,GACZC,kBAAkB,GAClBC,YAAY,GACZC,UAAU,GACVC,aAAa,GACbC,8BAA8B,GAC9BC,gBAAgB,GAChBC,sBAAsB,GACtBC,WAAW,GACXC,SAAS,GACTC,gBAAgB,GAChBC,iBAAiB,GACjBC,YAAY,GACZC,YAAY,GACZC,qBAAqB,GACrBC,YAAY,GACZC,iBAAiB,GACjBC,aAAa,GACbC,eAAe,GACfC,eAAe,GACfC,aAAa,GACbC,gBAAgB,GAChBC,sBAAsB,GACtBC,SAAS,GACTC,sBAAsB,GACtBC,sBAAsB,GACtBC,kBAAkB,GAClBC,sBAAsB,GACtBC,yBAAyB,GACzBC,yBAAyB,GACzBC,qBAAqB,GACrBC,2BAA2B,GAC3BC,sBAAsB,GACtBC,WAAW,GACXC,kBAAkB,GAClBC,uBAAuB,GACvBC,oBAAoB,GACpBC,cAAc,GACdC,WAAW,GACXC,cAAc,GACdC,cAAc,GACdC,gBAAgB,GAChBC,UAAU,GACVC,UAAU,GACVC,gBAAgB,GAChBC,YAAY,GACZC,SAAS,GACTC,QAAQ,GACRC,SAAS,GACTC,SAAS,GACTC,cAAc,GACdC,wBAAwB,GACxBC,wBAAwB,GACxBC,iCAAiC,GACjCC,8BAA8B,GAC9BC,eAAe,GACfC,aAAa,GACbC,kBAAkB,GAClBC,YAAY,GACZC,mBAAmB,GACnBC,WAAW,GACXC,eAAe,GACfC,WAAW,GACXC,cAAc,GACdC,kBAAkB,GAClBC,gBAAgB,GAChBC,gBAAgB,GAChBC,8BAA8B,GAC9BC,gBAAgB,GAChBC,cAAc,GACdC,iBAAiB,GACjBC,SAAS,GACTC,gBAAgB,GAChBC,SAAS,GACTC,kBAAkB,GAClBC,iBAAiB,GACjBC,0BAA0B,GAC1BC,sBAAsB,GACtBC,gBAAgB,GAChBC,iBAAiB,GACjBC,eAAe,GACfC,eAAe,GACfC,kBAAkB,GAClBC,iBAAiB,GACjBC,eAAe,GACfC,cAAc,GACdC,UAAU,GACVC,qBAAqB;;EAEvB;EACA,MAAME,gBAAgB,GACpB,eAAe,GACf,mBAAmB,GACnB,gBAAgB,GAChB,WAAW,GACX,gBAAgB,GAChB,iBAAiB,GACjB,gBAAgB,GAChB,UAAU,GACV,aAAa,GACb,SAAS,GACT,WAAW,GACX,eAAe,GACf,gBAAgB,GAChB,kCAAkC,GAClC,gBAAgB,GAChB,cAAc,GACd,gBAAgB,GAChB,oBAAoB,GACpB,aAAa,GACb,yBAAyB,GACzB,OAAO,GACP,UAAU,GACV,aAAa,GACb,kBAAkB,GAClB,iBAAiB,GACjB,iBAAiB,GACjB,eAAe,GACf,iBAAiB,GACjB,OAAO,GACP,WAAW,GACX,cAAc,GACd,wBAAwB,GACxB,mBAAmB,GACnB,eAAe,GACf,uBAAuB,GACvB,eAAe,GACf,kBAAkB,GAClB,aAAa,GACb,qBAAqB,GACrB,oBAAoB,GACpB,iBAAiB,GACjB,aAAa,GACb,aAAa,GACb,sBAAsB,GACtB,eAAe,GACf,mBAAmB,GACnB,iBAAiB,GACjB,cAAc,GACd,kBAAkB,GAClB,eAAe,GACf,mBAAmB,GACnB,eAAe,GACf,yBAAyB,GACzB,mBAAmB,GACnB,6BAA6B,GAC7B,aAAa,GACb,OAAO,GACP,aAAa,GACb,gBAAgB,GAChB,kBAAkB,GAClB,iBAAiB,GACjB,kBAAkB,GAClB,WAAW,GACX,eAAe,GACf,YAAY,GACZ,YAAY,GACZ,aAAa,GACb,kBAAkB,GAClB,2BAA2B,GAC3B,qCAAqC,GACrC,qCAAqC,GACrC,YAAY,GACZ,aAAa,GACb,WAAW,GACX,0BAA0B,GAC1B,oCAAoC,GACpC,oCAAoC,GACpC,aAAa,GACb,cAAc,GACd,kBAAkB,GAClB,gBAAgB,GAChB,eAAe,GACf,UAAU,GACV,iBAAiB,GACjB,OAAO,GACP,6BAA6B,GAC7B,mBAAmB,GACnB,iBAAiB,GACjB,kBAAkB,GAClB,kBAAkB,GAClB,gBAAgB,GAChB,aAAa,GACb,WAAW,GACX,WAAW,GACX,gBAAgB,GAChB,cAAc,GACd,mBAAmB,GACnB,SAAS,GACT,aAAa,GACb,gBAAgB,GAChB,gBAAgB,GAChB,eAAe,GACf,gBAAgB,GAChB,eAAe,GACf,0BAA0B,GAC1B,cAAc,GACd,mBAAmB,GACnB,qBAAqB,GACrB,oBAAoB,GACpB,aAAa,GACb,gBAAgB,GAChB,SAAS,GACT,KAAK,GACL,UAAU,GACV,cAAc,GACd,gBAAgB,GAChB,kBAAkB,GAClB,eAAe,GACf,gBAAgB,GAChB,YAAY,GACZ,SAAS,GACT,aAAa,GACb,gBAAgB,GAChB,iBAAiB,GACjB,YAAY,GACZ,MAAM,GACN,aAAa,GACb,eAAe,GACf,mBAAmB,GACnB,mBAAmB,GACnB,mBAAmB,GACnB,mBAAmB,GACnB,MAAM,GACN,eAAe,GACf,WAAW,GACX,QAAQ,GACR,MAAM,GACN,WAAW,GACX,MAAM,GACN,YAAY,GACZ,QAAQ,GACR,0BAA0B,GAC1B,WAAW,GACX,oBAAoB,GACpB,kBAAkB,GAClB,yBAAyB,GACzB,0BAA0B,GAC1B,qBAAqB,GACrB,uBAAuB,GACvB,qBAAqB,GACrB,UAAU,GACV,WAAW,GACX,aAAa,GACb,UAAU,GACV,QAAQ,GACR,mBAAmB,GACnB,YAAY,GACZ,iBAAiB,GACjB,cAAc,GACd,8BAA8B,GAC9B,2BAA2B,GAC3B,cAAc,GACd,QAAQ,GACR,QAAQ,GACR,MAAM,GACN,aAAa,GACb,yBAAyB,GACzB,YAAY,GACZ,YAAY,GACZ,iBAAiB,GACjB,gBAAgB,GAChB,OAAO,GACP,WAAW,GACX,QAAQ,GACR,gBAAgB,GAChB,OAAO,GACP,YAAY,GACZ,aAAa,GACb,iBAAiB,GACjB,cAAc,GACd,aAAa,GACb,aAAa,GACb,YAAY,GACZ,eAAe,GACf,YAAY,GACZ,cAAc,GACd,qBAAqB,GACrB,SAAS,GACT,aAAa,GACb,WAAW,GACX,eAAe,GACf,WAAW,GACX,YAAY,GACZ,gBAAgB,GAChB,iBAAiB,GACjB,OAAO,GACP,WAAW,GACX,OAAO,GACP,SAAS,GACT,wBAAwB,GACxB,YAAY,GACZ,QAAQ,GACR,iBAAiB,GACjB,QAAQ,GACR,WAAW,GACX,gBAAgB,GAChB,cAAc,GACd,UAAU,GACV,YAAY,GACZ,WAAW,GACX,kBAAkB,GAClB,sBAAsB,GACtB,gBAAgB,GAChB,aAAa,GACb,cAAc,GACd,WAAW,GACX,YAAY,GACZ,SAAS,GACT,SAAS,GACT,SAAS,GACT,YAAY,GACZ,oBAAoB,GACpB,cAAc,GACd,WAAW,GACX,cAAc,GACd,kBAAkB,GAClB,cAAc,GACd,oBAAoB,GACpB,SAAS,GACT,wBAAwB,GACxB,mBAAmB,GACnB,SAAS,GACT,aAAa,GACb,SAAS,GACT,mBAAmB,GACnB,mBAAmB,GACnB,eAAe,GACf,qBAAqB,GACrB,aAAa,GACb,eAAe,GACf,gBAAgB,GAChB,cAAc,GACd,aAAa,GACb,SAAS,GACT,YAAY,GACZ,UAAU,GACV,SAAS,GACT,QAAQ,GACR,YAAY,GACZ,YAAY,GACZ,UAAU,GACV,iBAAiB,GACjB,WAAW,GACX,UAAU,GACV,aAAa,GACb,OAAO,GACP,gBAAgB,GAChB,SAAS,GACT,OAAO,GACP,WAAW,GACX,MAAM,GACN,uBAAuB,GACvB,aAAa,GACb,eAAe,GACf,UAAU,GACV,yBAAyB,GACzB,SAAS,GACT,UAAU,GACV,uBAAuB,GACvB,WAAW,GACX,UAAU,GACV,OAAO,GACP,MAAM,GACN,YAAY,GACZ,iBAAiB,GACjB,gBAAgB,GAChB,QAAQ,GACR,YAAY,GACZ,WAAW,GACX,kBAAkB,GAClB,QAAQ,GACR,YAAY,GACZ,oBAAoB,GACpB,aAAa,GACb,kBAAkB,GAClB,SAAS,GACT,cAAc,GACd,WAAW,GACX,oBAAoB,GACpB,4BAA4B,GAC5B,iBAAiB,GACjB,kBAAkB,GAClB,qBAAqB,GACrB,WAAW,GACX,oBAAoB,GACpB,wBAAwB,GACxB,mBAAmB,GACnB,UAAU,GACV,YAAY,GACZ,UAAU,GACV,gBAAgB,GAChB,eAAe,GACf,aAAa,GACb,YAAY,GACZ,YAAY,GACZ,WAAW,GACX,eAAe,GACf,wBAAwB,GACxB,eAAe,GACf,UAAU,GACV,YAAY,GACZ,UAAU,GACV,SAAS,GACT,SAAS,GACT,kBAAkB,GAClB,wBAAwB,GACxB,SAAS,GACT,SAAS,GACT,qBAAqB,GACrB,oBAAoB,GACpB,aAAa,GACb,WAAW,GACX,oBAAoB,GACpB,oBAAoB,GACpB,gBAAgB,GAChB,mCAAmC,GACnC,2BAA2B,GAC3B,6BAA6B,GAC7B,+BAA+B,GAC/B,gBAAgB,GAChB,mBAAmB,GACnB,oBAAoB,GACpB,gCAAgC,GAChC,gBAAgB,GAChB,qBAAqB,GACrB,gBAAgB,GAChB,eAAe,GACf,eAAe,GACf,kBAAkB,GAClB,oBAAoB,GACpB,gBAAgB,GAChB,qBAAqB,GACrB,oBAAoB,GACpB,kBAAkB,GAClB,UAAU,GACV,UAAU,GACV,OAAO,GACP,UAAU,GACV,eAAe,GACf,MAAM,GACN,aAAa,GACb,cAAc,GACd,eAAe,GACf,mBAAmB,GACnB,oBAAoB,GACpB,eAAe,GACf,SAAS,GACT,WAAW,GACX,cAAc,GACd,aAAa,GACb,cAAc,GACd,WAAW,GACX,cAAc,GACd,aAAa,GACb,UAAU,GACV,gBAAgB,GAChB,eAAe,GACf,YAAY,GACZ,gBAAgB,GAChB,eAAe,GACf,kBAAkB,GAClB,aAAa,GACb,UAAU,GACV,eAAe,GACf,aAAa,GACb,YAAY,GACZ,eAAe,GACf,gBAAgB,GAChB,eAAe,GACf,gBAAgB,GAChB,gBAAgB,GAChB,gBAAgB,GAChB,cAAc,GACd,eAAe,GACf,aAAa,GACb,cAAc,GACd,iBAAiB,GACjB,iBAAiB,GACjB,YAAY,GACZ,UAAU,GACV,WAAW,GACX,gBAAgB,GAChB,YAAY,GACZ,SAAS,GACT,OAAO,GACP,OAAO,GACP,iBAAiB,GACjB,iBAAiB,GACjB,cAAc,GACd,QAAQ,GACR,cAAc,GACd,MAAM,GACN,oBAAoB,GACpB,qBAAqB,GACrB,wBAAwB,GACxB,YAAY,GACZ,sBAAsB,GACtB,cAAc,GACd,YAAY,GACZ,eAAe,GACf,aAAa,GACb,iBAAiB,GACjB,kBAAkB,GAClB,cAAc,GACd,oBAAoB,GACpB,qBAAqB,GACrB,kBAAkB,GAClB,kBAAkB,GAClB,aAAa,GACb,cAAc,GACd,oBAAoB,GACpB,iBAAiB,GACjB,cAAc,GACd,cAAc,GACd,YAAY,GACZ,SAAS,GACT,SAAS,GACT,SAAS,GACT,SAAS,GACT,iBAAiB,GACjB,uBAAuB,GACvB,UAAU;;EAEZ;EACA,MAAMC,oBAAoB,GACxB,WAAW,GACX,cAAc,GACd,WAAW,GACX,kBAAkB,GAClB,cAAc,GACd,iBAAiB,GACjB,eAAe,GACf,eAAe,GACf,UAAU,GACV,aAAa,GACb,yBAAyB,GACzB,UAAU,GACV,YAAY,GACZ,MAAM,GACN,OAAO,GACP,aAAa,GACb,kBAAkB,GAClB,uBAAuB,GACvB,eAAe,GACf,SAAS,GACT,kBAAkB,GAClB,YAAY,GACZ,aAAa,GACb,YAAY,GACZ,aAAa,GACb,UAAU,GACV,SAAS,GACT,UAAU,GACV,WAAW,GACX,sBAAsB,GACtB,gBAAgB,GAChB,aAAa,GACb,SAAS,GACT,eAAe,GACf,iBAAiB,GACjB,aAAa,GACb,UAAU,GACV,gBAAgB,GAChB,QAAQ,GACR,SAAS,GACT,UAAU,GACV,OAAO,GACP,cAAc,GACd,YAAY,GACZ,WAAW;;EAEb;EACA,MAAMC,UAAU,GACd,eAAe,GACf,gBAAgB,GAChB,qBAAqB,GACrB,gCAAgC,GAChC,UAAU,GACV,cAAc,GACd,oCAAoC,GACpC,WAAW,GACX,eAAe,GACf,eAAe,GACf,cAAc,GACd,kBAAkB,GAClB,gBAAgB,GAChB,oBAAoB,GACpB,UAAU,GACV,aAAa,GACb,wBAAwB,GACxB,kBAAkB,GAClB,yBAAyB,GACzB,sBAAsB,GACtB,kBAAkB,GAClB,cAAc,GACd,YAAY,GACZ,WAAW,GACX,cAAc,GACd,kBAAkB,GAClB,eAAe,GACf,UAAU,GACV,WAAW,GACX,aAAa,GACb,iBAAiB,GACjB,iBAAiB,GACjB,0BAA0B,GAC1B,cAAc,GACd,kBAAkB,GAClB,WAAW,GACX,qBAAqB,GACrB,cAAc,GACd,iBAAiB,GACjB,iBAAiB,GACjB,4BAA4B,GAC5B,aAAa,GACb,mBAAmB,GACnB,iBAAiB,GACjB,4BAA4B,GAC5B,UAAU,GACV,iBAAiB,GACjB,4BAA4B,GAC5B,kBAAkB,GAClB,eAAe,GACf,gBAAgB,GAChB,gBAAgB,GAChB,kBAAkB,GAClB,gBAAgB,GAChB,oBAAoB,GACpB,iBAAiB,GACjB,eAAe,GACf,cAAc,GACd,gBAAgB,GAChB,cAAc,GACd,yBAAyB,GACzB,qBAAqB,GACrB,kBAAkB,GAClB,aAAa,GACb,yBAAyB,GACzB,wBAAwB,GACxB,mBAAmB,GACnB,oBAAoB,GACpB,iBAAiB,GACjB,oBAAoB,GACpB,oBAAoB,GACpB,8BAA8B,GAC9B,0BAA0B,GAC1B,uBAAuB,GACvB,oBAAoB,GACpB,oBAAoB,GACpB,eAAe,GACf,kBAAkB,GAClB,aAAa,GACb,SAAS,GACT,aAAa,GACb,aAAa,GACb,kBAAkB,GAClB,mBAAmB,GACnB,WAAW,GACX,SAAS,GACT,cAAc,GACd,UAAU,GACV,qBAAqB,GACrB,gBAAgB,GAChB,iBAAiB,GACjB,cAAc,GACd,WAAW,GACX,QAAQ,GACR,aAAa,GACb,kBAAkB,GAClB,mBAAmB,GACnB,gBAAgB,GAChB,QAAQ,GACR,UAAU,GACV,sBAAsB,GACtB,oBAAoB,GACpB,eAAe,GACf,gBAAgB,GAChB,cAAc,GACd,eAAe,GACf,oBAAoB,GACpB,eAAe,GACf,oBAAoB,GACpB,gBAAgB,GAChB,kBAAkB,GAClB,OAAO,GACP,kBAAkB,GAClB,cAAc,GACd,WAAW,GACX,WAAW,GACX,gBAAgB,GAChB,yBAAyB,GACzB,yBAAyB,GACzB,qBAAqB,GACrB,eAAe,GACf,kBAAkB,GAClB,QAAQ,GACR,WAAW,GACX,eAAe,GACf,gBAAgB,GAChB,QAAQ,GACR,iBAAiB,GACjB,oBAAoB,GACpB,sBAAsB,GACtB,UAAU,GACV,cAAc,GACd,oBAAoB,GACpB,oBAAoB,GACpB,gBAAgB,GAChB,UAAU,GACV,qBAAqB,GACrB,kBAAkB,GAClB,cAAc,GACd,YAAY,GACZ,cAAc,GACd,iBAAiB,GACjB,gBAAgB,GAChB,iBAAiB,GACjB,4BAA4B,GAC5B,qBAAqB,GACrB,sBAAsB,GACtB,aAAa,GACb,aAAa,GACb,iBAAiB,GACjB,WAAW,GACX,kBAAkB,GAClB,kBAAkB,GAClB,YAAY,GACZ,YAAY,GACZ,uBAAuB,GACvB,SAAS,GACT,aAAa,GACb,sBAAsB,GACtB,wBAAwB,GACxB,oBAAoB,GACpB,+BAA+B,GAC/B,iBAAiB,GACjB,iCAAiC,GACjC,iCAAiC,GACjC,qBAAqB,GACrB,kBAAkB,GAClB,gBAAgB,GAChB,UAAU,GACV,iBAAiB,GACjB,aAAa,GACb,wBAAwB,GACxB,4BAA4B,GAC5B,oBAAoB,GACpB,YAAY,GACZ,aAAa,GACb,QAAQ,GACR,YAAY,GACZ,eAAe,GACf,UAAU,GACV,iBAAiB,GACjB,kBAAkB,GAClB,mBAAmB,GACnB,qBAAqB,GACrB,iBAAiB,GACjB,oBAAoB,GACpB,8BAA8B,GAC9B,4BAA4B,GAC5B,kBAAkB,GAClB,gBAAgB,GAChB,eAAe,GACf,sBAAsB,GACtB,iBAAiB,GACjB,kBAAkB,GAClB,aAAa,GACb,gBAAgB,GAChB,iBAAiB,GACjB,iBAAiB,GACjB,mBAAmB,GACnB,mBAAmB,GACnB,cAAc,GACd,mBAAmB,GACnB,8BAA8B,GAC9B,eAAe,GACf,wBAAwB,GACxB,cAAc,GACd,YAAY,GACZ,QAAQ,GACR,uBAAuB,GACvB,sBAAsB,GACtB,mBAAmB,GACnB,eAAe,GACf,YAAY,GACZ,aAAa,GACb,iBAAiB,GACjB,iBAAiB,GACjB,aAAa,GACb,wBAAwB,GACxB,QAAQ,GACR,YAAY,GACZ,SAAS,GACT,QAAQ,GACR,qBAAqB,GACrB,UAAU,GACV,gBAAgB,GAChB,iBAAiB,GACjB,qBAAqB,GACrB,eAAe,GACf,mBAAmB,GACnB,wBAAwB,GACxB,cAAc,GACd,oBAAoB,GACpB,mBAAmB,GACnB,wBAAwB,GACxB,yBAAyB,GACzB,iBAAiB,GACjB,uBAAuB,GACvB,uBAAuB,GACvB,iBAAiB,GACjB,qBAAqB,GACrB,0BAA0B,GAC1B,aAAa,GACb,sBAAsB,GACtB,mBAAmB,GACnB,oBAAoB,GACpB,iBAAiB,GACjB,WAAW;;EAEb;EACA,MAAMC,OAAO,GAAG7F,SAAS,GAAGyF,KAAK;;EAEjC;EACA,MAAMK,KAAK,GAAGH,oBAAoB;;EAElC;EACA,MAAMI,OAAO,GAAG,sBAAsB;;EAEtC;EACA,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE1I,IAAI,CAAC2I,SAAS;IACrBC,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG;IACdJ,SAAS,EAAE,QAAQ;IACnBK,QAAQ,EAAE,CACR;MACEJ,KAAK,EAAE,GAAG;MACVK,GAAG,EAAE;IACP,CAAC,EACD;MACEL,KAAK,EAAE,GAAG;MACVK,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG;IACdP,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,2DAA2D;IAClEE,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMK,sBAAsB,GAAG;IAC7BR,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,IAAI;IACXK,GAAG,EAAE,GAAG;IACRH,SAAS,EAAE,CAAC;IACZM,QAAQ,EAAE,CACRlJ,IAAI,CAACmJ,kBAAkB,EACvBH,OAAO;EAEX,CAAC;;EAED;EACA,MAAMI,uBAAuB,GAAG;IAC9BX,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,MAAM;IACbK,GAAG,EAAE,MAAM;IACXH,SAAS,EAAE,CAAC;IACZM,QAAQ,EAAE,CACRlJ,IAAI,CAACmJ,kBAAkB,EACvBH,OAAO;EAEX,CAAC;;EAED;EACA,MAAMK,QAAQ,GAAG;IACfP,QAAQ,EAAE,CACRG,sBAAsB,EACtBG,uBAAuB;EAE3B,CAAC;;EAED;EACA,MAAME,QAAQ,GAAG;IACfC,QAAQ,EAAEtJ,mBAAmB;IAC7BuJ,OAAO,EAAErJ,OAAO;IAChBsJ,QAAQ,EAAEpB,OAAO;IACjBqB,KAAK,EAAEpB,KAAK;IACZqB,OAAO,EAAEpB;EACX,CAAC;;EAED;EACA,MAAMqB,OAAO,GAAG;IACdlB,KAAK,EAAE,SAAS,GAAG1I,IAAI,CAACC,mBAAmB;IAC3C4J,QAAQ,EAAEP,QAAQ;IAClBV,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMkB,KAAK,GAAG;IACZrB,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,WAAW,GAAGN,UAAU,CAAC2B,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG;IAChEjB,GAAG,EAAE,UAAU;IACfkB,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG;IAChBzB,SAAS,EAAE,UAAU;IACrBoB,QAAQ,EAAEP,QAAQ;IAClBZ,KAAK,EAAEzI,mBAAmB;IAC1B2I,SAAS,EAAE,CAAC;IACZM,QAAQ,EAAE,CACRY,KAAK,EACLF,OAAO;EAEX,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGjK,sBAAsB,GAAG,KAAK;EAErD,MAAMkK,UAAU,GAAG;IACjB3B,SAAS,EAAE,OAAO;IAClBoB,QAAQ,EAAE;MACRN,QAAQ,EAAEtJ,mBAAmB;MAC7BwJ,QAAQ,EAAEvB;IACZ,CAAC;IACDQ,KAAK,EAAEyB,cAAc;IACrBpB,GAAG,EAAE,KAAK;IACVsB,WAAW,EAAE,IAAI;IACjBJ,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMK,SAAS,GAAG;IAChB7B,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAEyB,cAAc;IACrBpB,GAAG,EAAE,MAAM;IACXsB,WAAW,EAAE,IAAI;IACjBR,QAAQ,EAAEP,QAAQ;IAClBiB,OAAO,EAAE,wBAAwB;IACjCrB,QAAQ,EAAE,CACRkB,UAAU,EACVR,OAAO,EACPM,SAAS,EACTrB,OAAO,EACPL,OAAO,EACPa,QAAQ;EAEZ,CAAC;EAED,OAAO;IACLmB,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBZ,QAAQ,EAAEP,QAAQ;IAClBiB,OAAO,EAAE,yBAAyB;IAClCrB,QAAQ,EAAE,CACRoB,SAAS,EACTR,KAAK,EACLF,OAAO,EACPM,SAAS,EACTrB,OAAO,EACPL,OAAO,EACPa,QAAQ;EAEZ,CAAC;AACH;AAEAqB,MAAM,CAACC,OAAO,GAAG5K,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}