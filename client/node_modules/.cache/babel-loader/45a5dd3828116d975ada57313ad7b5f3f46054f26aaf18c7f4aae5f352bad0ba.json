{"ast": null, "code": "/*\nLanguage: Lasso\nAuthor: <PERSON> <<EMAIL>>\nDescription: Lasso is a language and server platform for database-driven web applications. This definition handles Lasso 9 syntax and LassoScript for Lasso 8.6 and earlier.\nWebsite: http://www.lassosoft.com/What-Is-Lasso\n*/\n\nfunction lasso(hljs) {\n  const LASSO_IDENT_RE = '[a-zA-Z_][\\\\w.]*';\n  const LASSO_ANGLE_RE = '<\\\\?(lasso(script)?|=)';\n  const LASSO_CLOSE_RE = '\\\\]|\\\\?>';\n  const LASSO_KEYWORDS = {\n    $pattern: LASSO_IDENT_RE + '|&[lg]t;',\n    literal: 'true false none minimal full all void and or not ' + 'bw nbw ew new cn ncn lt lte gt gte eq neq rx nrx ft',\n    built_in: 'array date decimal duration integer map pair string tag xml null ' + 'boolean bytes keyword list locale queue set stack staticarray ' + 'local var variable global data self inherited currentcapture givenblock',\n    keyword: 'cache database_names database_schemanames database_tablenames ' + 'define_tag define_type email_batch encode_set html_comment handle ' + 'handle_error header if inline iterate ljax_target link ' + 'link_currentaction link_currentgroup link_currentrecord link_detail ' + 'link_firstgroup link_firstrecord link_lastgroup link_lastrecord ' + 'link_nextgroup link_nextrecord link_prevgroup link_prevrecord log ' + 'loop namespace_using output_none portal private protect records ' + 'referer referrer repeating resultset rows search_args ' + 'search_arguments select sort_args sort_arguments thread_atomic ' + 'value_list while abort case else fail_if fail_ifnot fail if_empty ' + 'if_false if_null if_true loop_abort loop_continue loop_count params ' + 'params_up return return_value run_children soap_definetag ' + 'soap_lastrequest soap_lastresponse tag_name ascending average by ' + 'define descending do equals frozen group handle_failure import in ' + 'into join let match max min on order parent protected provide public ' + 'require returnhome skip split_thread sum take thread to trait type ' + 'where with yield yieldhome'\n  };\n  const HTML_COMMENT = hljs.COMMENT('<!--', '-->', {\n    relevance: 0\n  });\n  const LASSO_NOPROCESS = {\n    className: 'meta',\n    begin: '\\\\[noprocess\\\\]',\n    starts: {\n      end: '\\\\[/noprocess\\\\]',\n      returnEnd: true,\n      contains: [HTML_COMMENT]\n    }\n  };\n  const LASSO_START = {\n    className: 'meta',\n    begin: '\\\\[/noprocess|' + LASSO_ANGLE_RE\n  };\n  const LASSO_DATAMEMBER = {\n    className: 'symbol',\n    begin: '\\'' + LASSO_IDENT_RE + '\\''\n  };\n  const LASSO_CODE = [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.inherit(hljs.C_NUMBER_MODE, {\n    begin: hljs.C_NUMBER_RE + '|(-?infinity|NaN)\\\\b'\n  }), hljs.inherit(hljs.APOS_STRING_MODE, {\n    illegal: null\n  }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  }), {\n    className: 'string',\n    begin: '`',\n    end: '`'\n  }, {\n    // variables\n    variants: [{\n      begin: '[#$]' + LASSO_IDENT_RE\n    }, {\n      begin: '#',\n      end: '\\\\d+',\n      illegal: '\\\\W'\n    }]\n  }, {\n    className: 'type',\n    begin: '::\\\\s*',\n    end: LASSO_IDENT_RE,\n    illegal: '\\\\W'\n  }, {\n    className: 'params',\n    variants: [{\n      begin: '-(?!infinity)' + LASSO_IDENT_RE,\n      relevance: 0\n    }, {\n      begin: '(\\\\.\\\\.\\\\.)'\n    }]\n  }, {\n    begin: /(->|\\.)\\s*/,\n    relevance: 0,\n    contains: [LASSO_DATAMEMBER]\n  }, {\n    className: 'class',\n    beginKeywords: 'define',\n    returnEnd: true,\n    end: '\\\\(|=>',\n    contains: [hljs.inherit(hljs.TITLE_MODE, {\n      begin: LASSO_IDENT_RE + '(=(?!>))?|[-+*/%](?!>)'\n    })]\n  }];\n  return {\n    name: 'Lasso',\n    aliases: ['ls', 'lassoscript'],\n    case_insensitive: true,\n    keywords: LASSO_KEYWORDS,\n    contains: [{\n      className: 'meta',\n      begin: LASSO_CLOSE_RE,\n      relevance: 0,\n      starts: {\n        // markup\n        end: '\\\\[|' + LASSO_ANGLE_RE,\n        returnEnd: true,\n        relevance: 0,\n        contains: [HTML_COMMENT]\n      }\n    }, LASSO_NOPROCESS, LASSO_START, {\n      className: 'meta',\n      begin: '\\\\[no_square_brackets',\n      starts: {\n        end: '\\\\[/no_square_brackets\\\\]',\n        // not implemented in the language\n        keywords: LASSO_KEYWORDS,\n        contains: [{\n          className: 'meta',\n          begin: LASSO_CLOSE_RE,\n          relevance: 0,\n          starts: {\n            end: '\\\\[noprocess\\\\]|' + LASSO_ANGLE_RE,\n            returnEnd: true,\n            contains: [HTML_COMMENT]\n          }\n        }, LASSO_NOPROCESS, LASSO_START].concat(LASSO_CODE)\n      }\n    }, {\n      className: 'meta',\n      begin: '\\\\[',\n      relevance: 0\n    }, {\n      className: 'meta',\n      begin: '^#!',\n      end: 'lasso9$',\n      relevance: 10\n    }].concat(LASSO_CODE)\n  };\n}\nmodule.exports = lasso;", "map": {"version": 3, "names": ["lasso", "hljs", "LASSO_IDENT_RE", "LASSO_ANGLE_RE", "LASSO_CLOSE_RE", "LASSO_KEYWORDS", "$pattern", "literal", "built_in", "keyword", "HTML_COMMENT", "COMMENT", "relevance", "LASSO_NOPROCESS", "className", "begin", "starts", "end", "returnEnd", "contains", "LASSO_START", "LASSO_DATAMEMBER", "LASSO_CODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "inherit", "C_NUMBER_MODE", "C_NUMBER_RE", "APOS_STRING_MODE", "illegal", "QUOTE_STRING_MODE", "variants", "beginKeywords", "TITLE_MODE", "name", "aliases", "case_insensitive", "keywords", "concat", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/lasso.js"], "sourcesContent": ["/*\nLanguage: Lasso\nAuthor: <PERSON> <<EMAIL>>\nDescription: Lasso is a language and server platform for database-driven web applications. This definition handles Lasso 9 syntax and LassoScript for Lasso 8.6 and earlier.\nWebsite: http://www.lassosoft.com/What-Is-Lasso\n*/\n\nfunction lasso(hljs) {\n  const LASSO_IDENT_RE = '[a-zA-Z_][\\\\w.]*';\n  const LASSO_ANGLE_RE = '<\\\\?(lasso(script)?|=)';\n  const LASSO_CLOSE_RE = '\\\\]|\\\\?>';\n  const LASSO_KEYWORDS = {\n    $pattern: LASSO_IDENT_RE + '|&[lg]t;',\n    literal:\n      'true false none minimal full all void and or not ' +\n      'bw nbw ew new cn ncn lt lte gt gte eq neq rx nrx ft',\n    built_in:\n      'array date decimal duration integer map pair string tag xml null ' +\n      'boolean bytes keyword list locale queue set stack staticarray ' +\n      'local var variable global data self inherited currentcapture givenblock',\n    keyword:\n      'cache database_names database_schemanames database_tablenames ' +\n      'define_tag define_type email_batch encode_set html_comment handle ' +\n      'handle_error header if inline iterate ljax_target link ' +\n      'link_currentaction link_currentgroup link_currentrecord link_detail ' +\n      'link_firstgroup link_firstrecord link_lastgroup link_lastrecord ' +\n      'link_nextgroup link_nextrecord link_prevgroup link_prevrecord log ' +\n      'loop namespace_using output_none portal private protect records ' +\n      'referer referrer repeating resultset rows search_args ' +\n      'search_arguments select sort_args sort_arguments thread_atomic ' +\n      'value_list while abort case else fail_if fail_ifnot fail if_empty ' +\n      'if_false if_null if_true loop_abort loop_continue loop_count params ' +\n      'params_up return return_value run_children soap_definetag ' +\n      'soap_lastrequest soap_lastresponse tag_name ascending average by ' +\n      'define descending do equals frozen group handle_failure import in ' +\n      'into join let match max min on order parent protected provide public ' +\n      'require returnhome skip split_thread sum take thread to trait type ' +\n      'where with yield yieldhome'\n  };\n  const HTML_COMMENT = hljs.COMMENT(\n    '<!--',\n    '-->',\n    {\n      relevance: 0\n    }\n  );\n  const LASSO_NOPROCESS = {\n    className: 'meta',\n    begin: '\\\\[noprocess\\\\]',\n    starts: {\n      end: '\\\\[/noprocess\\\\]',\n      returnEnd: true,\n      contains: [HTML_COMMENT]\n    }\n  };\n  const LASSO_START = {\n    className: 'meta',\n    begin: '\\\\[/noprocess|' + LASSO_ANGLE_RE\n  };\n  const LASSO_DATAMEMBER = {\n    className: 'symbol',\n    begin: '\\'' + LASSO_IDENT_RE + '\\''\n  };\n  const LASSO_CODE = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.inherit(hljs.C_NUMBER_MODE, {\n      begin: hljs.C_NUMBER_RE + '|(-?infinity|NaN)\\\\b'\n    }),\n    hljs.inherit(hljs.APOS_STRING_MODE, {\n      illegal: null\n    }),\n    hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    }),\n    {\n      className: 'string',\n      begin: '`',\n      end: '`'\n    },\n    { // variables\n      variants: [\n        {\n          begin: '[#$]' + LASSO_IDENT_RE\n        },\n        {\n          begin: '#',\n          end: '\\\\d+',\n          illegal: '\\\\W'\n        }\n      ]\n    },\n    {\n      className: 'type',\n      begin: '::\\\\s*',\n      end: LASSO_IDENT_RE,\n      illegal: '\\\\W'\n    },\n    {\n      className: 'params',\n      variants: [\n        {\n          begin: '-(?!infinity)' + LASSO_IDENT_RE,\n          relevance: 0\n        },\n        {\n          begin: '(\\\\.\\\\.\\\\.)'\n        }\n      ]\n    },\n    {\n      begin: /(->|\\.)\\s*/,\n      relevance: 0,\n      contains: [LASSO_DATAMEMBER]\n    },\n    {\n      className: 'class',\n      beginKeywords: 'define',\n      returnEnd: true,\n      end: '\\\\(|=>',\n      contains: [\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: LASSO_IDENT_RE + '(=(?!>))?|[-+*/%](?!>)'\n        })\n      ]\n    }\n  ];\n  return {\n    name: 'Lasso',\n    aliases: [\n      'ls',\n      'lassoscript'\n    ],\n    case_insensitive: true,\n    keywords: LASSO_KEYWORDS,\n    contains: [\n      {\n        className: 'meta',\n        begin: LASSO_CLOSE_RE,\n        relevance: 0,\n        starts: { // markup\n          end: '\\\\[|' + LASSO_ANGLE_RE,\n          returnEnd: true,\n          relevance: 0,\n          contains: [HTML_COMMENT]\n        }\n      },\n      LASSO_NOPROCESS,\n      LASSO_START,\n      {\n        className: 'meta',\n        begin: '\\\\[no_square_brackets',\n        starts: {\n          end: '\\\\[/no_square_brackets\\\\]', // not implemented in the language\n          keywords: LASSO_KEYWORDS,\n          contains: [\n            {\n              className: 'meta',\n              begin: LASSO_CLOSE_RE,\n              relevance: 0,\n              starts: {\n                end: '\\\\[noprocess\\\\]|' + LASSO_ANGLE_RE,\n                returnEnd: true,\n                contains: [HTML_COMMENT]\n              }\n            },\n            LASSO_NOPROCESS,\n            LASSO_START\n          ].concat(LASSO_CODE)\n        }\n      },\n      {\n        className: 'meta',\n        begin: '\\\\[',\n        relevance: 0\n      },\n      {\n        className: 'meta',\n        begin: '^#!',\n        end: 'lasso9$',\n        relevance: 10\n      }\n    ].concat(LASSO_CODE)\n  };\n}\n\nmodule.exports = lasso;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,cAAc,GAAG,kBAAkB;EACzC,MAAMC,cAAc,GAAG,wBAAwB;EAC/C,MAAMC,cAAc,GAAG,UAAU;EACjC,MAAMC,cAAc,GAAG;IACrBC,QAAQ,EAAEJ,cAAc,GAAG,UAAU;IACrCK,OAAO,EACL,mDAAmD,GACnD,qDAAqD;IACvDC,QAAQ,EACN,mEAAmE,GACnE,gEAAgE,GAChE,yEAAyE;IAC3EC,OAAO,EACL,gEAAgE,GAChE,oEAAoE,GACpE,yDAAyD,GACzD,sEAAsE,GACtE,kEAAkE,GAClE,oEAAoE,GACpE,kEAAkE,GAClE,wDAAwD,GACxD,iEAAiE,GACjE,oEAAoE,GACpE,sEAAsE,GACtE,4DAA4D,GAC5D,mEAAmE,GACnE,oEAAoE,GACpE,uEAAuE,GACvE,qEAAqE,GACrE;EACJ,CAAC;EACD,MAAMC,YAAY,GAAGT,IAAI,CAACU,OAAO,CAC/B,MAAM,EACN,KAAK,EACL;IACEC,SAAS,EAAE;EACb,CACF,CAAC;EACD,MAAMC,eAAe,GAAG;IACtBC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,iBAAiB;IACxBC,MAAM,EAAE;MACNC,GAAG,EAAE,kBAAkB;MACvBC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,CAACT,YAAY;IACzB;EACF,CAAC;EACD,MAAMU,WAAW,GAAG;IAClBN,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,gBAAgB,GAAGZ;EAC5B,CAAC;EACD,MAAMkB,gBAAgB,GAAG;IACvBP,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI,GAAGb,cAAc,GAAG;EACjC,CAAC;EACD,MAAMoB,UAAU,GAAG,CACjBrB,IAAI,CAACsB,mBAAmB,EACxBtB,IAAI,CAACuB,oBAAoB,EACzBvB,IAAI,CAACwB,OAAO,CAACxB,IAAI,CAACyB,aAAa,EAAE;IAC/BX,KAAK,EAAEd,IAAI,CAAC0B,WAAW,GAAG;EAC5B,CAAC,CAAC,EACF1B,IAAI,CAACwB,OAAO,CAACxB,IAAI,CAAC2B,gBAAgB,EAAE;IAClCC,OAAO,EAAE;EACX,CAAC,CAAC,EACF5B,IAAI,CAACwB,OAAO,CAACxB,IAAI,CAAC6B,iBAAiB,EAAE;IACnCD,OAAO,EAAE;EACX,CAAC,CAAC,EACF;IACEf,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVE,GAAG,EAAE;EACP,CAAC,EACD;IAAE;IACAc,QAAQ,EAAE,CACR;MACEhB,KAAK,EAAE,MAAM,GAAGb;IAClB,CAAC,EACD;MACEa,KAAK,EAAE,GAAG;MACVE,GAAG,EAAE,MAAM;MACXY,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEf,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,QAAQ;IACfE,GAAG,EAAEf,cAAc;IACnB2B,OAAO,EAAE;EACX,CAAC,EACD;IACEf,SAAS,EAAE,QAAQ;IACnBiB,QAAQ,EAAE,CACR;MACEhB,KAAK,EAAE,eAAe,GAAGb,cAAc;MACvCU,SAAS,EAAE;IACb,CAAC,EACD;MACEG,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEA,KAAK,EAAE,YAAY;IACnBH,SAAS,EAAE,CAAC;IACZO,QAAQ,EAAE,CAACE,gBAAgB;EAC7B,CAAC,EACD;IACEP,SAAS,EAAE,OAAO;IAClBkB,aAAa,EAAE,QAAQ;IACvBd,SAAS,EAAE,IAAI;IACfD,GAAG,EAAE,QAAQ;IACbE,QAAQ,EAAE,CACRlB,IAAI,CAACwB,OAAO,CAACxB,IAAI,CAACgC,UAAU,EAAE;MAC5BlB,KAAK,EAAEb,cAAc,GAAG;IAC1B,CAAC,CAAC;EAEN,CAAC,CACF;EACD,OAAO;IACLgC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,CACP,IAAI,EACJ,aAAa,CACd;IACDC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAEhC,cAAc;IACxBc,QAAQ,EAAE,CACR;MACEL,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAEX,cAAc;MACrBQ,SAAS,EAAE,CAAC;MACZI,MAAM,EAAE;QAAE;QACRC,GAAG,EAAE,MAAM,GAAGd,cAAc;QAC5Be,SAAS,EAAE,IAAI;QACfN,SAAS,EAAE,CAAC;QACZO,QAAQ,EAAE,CAACT,YAAY;MACzB;IACF,CAAC,EACDG,eAAe,EACfO,WAAW,EACX;MACEN,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,uBAAuB;MAC9BC,MAAM,EAAE;QACNC,GAAG,EAAE,2BAA2B;QAAE;QAClCoB,QAAQ,EAAEhC,cAAc;QACxBc,QAAQ,EAAE,CACR;UACEL,SAAS,EAAE,MAAM;UACjBC,KAAK,EAAEX,cAAc;UACrBQ,SAAS,EAAE,CAAC;UACZI,MAAM,EAAE;YACNC,GAAG,EAAE,kBAAkB,GAAGd,cAAc;YACxCe,SAAS,EAAE,IAAI;YACfC,QAAQ,EAAE,CAACT,YAAY;UACzB;QACF,CAAC,EACDG,eAAe,EACfO,WAAW,CACZ,CAACkB,MAAM,CAAChB,UAAU;MACrB;IACF,CAAC,EACD;MACER,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,KAAK;MACZH,SAAS,EAAE;IACb,CAAC,EACD;MACEE,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,KAAK;MACZE,GAAG,EAAE,SAAS;MACdL,SAAS,EAAE;IACb,CAAC,CACF,CAAC0B,MAAM,CAAChB,UAAU;EACrB,CAAC;AACH;AAEAiB,MAAM,CAACC,OAAO,GAAGxC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}