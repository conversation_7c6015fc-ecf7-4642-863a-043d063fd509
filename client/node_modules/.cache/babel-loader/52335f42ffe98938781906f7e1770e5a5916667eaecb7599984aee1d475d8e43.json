{"ast": null, "code": "/*\nLanguage: Scheme\nDescription: Scheme is a programming language in the Lisp family.\n             (keywords based on http://community.schemewiki.org/?scheme-keywords)\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nOrigin: clojure.js\nWebsite: http://community.schemewiki.org/?what-is-scheme\nCategory: lisp\n*/\n\nfunction scheme(hljs) {\n  const SCHEME_IDENT_RE = '[^\\\\(\\\\)\\\\[\\\\]\\\\{\\\\}\",\\'`;#|\\\\\\\\\\\\s]+';\n  const SCHEME_SIMPLE_NUMBER_RE = '(-|\\\\+)?\\\\d+([./]\\\\d+)?';\n  const SCHEME_COMPLEX_NUMBER_RE = SCHEME_SIMPLE_NUMBER_RE + '[+\\\\-]' + SCHEME_SIMPLE_NUMBER_RE + 'i';\n  const KEYWORDS = {\n    $pattern: SCHEME_IDENT_RE,\n    'builtin-name': 'case-lambda call/cc class define-class exit-handler field import ' + 'inherit init-field interface let*-values let-values let/ec mixin ' + 'opt-lambda override protect provide public rename require ' + 'require-for-syntax syntax syntax-case syntax-error unit/sig unless ' + 'when with-syntax and begin call-with-current-continuation ' + 'call-with-input-file call-with-output-file case cond define ' + 'define-syntax delay do dynamic-wind else for-each if lambda let let* ' + 'let-syntax letrec letrec-syntax map or syntax-rules \\' * + , ,@ - ... / ' + '; < <= = => > >= ` abs acos angle append apply asin assoc assq assv atan ' + 'boolean? caar cadr call-with-input-file call-with-output-file ' + 'call-with-values car cdddar cddddr cdr ceiling char->integer ' + 'char-alphabetic? char-ci<=? char-ci<? char-ci=? char-ci>=? char-ci>? ' + 'char-downcase char-lower-case? char-numeric? char-ready? char-upcase ' + 'char-upper-case? char-whitespace? char<=? char<? char=? char>=? char>? ' + 'char? close-input-port close-output-port complex? cons cos ' + 'current-input-port current-output-port denominator display eof-object? ' + 'eq? equal? eqv? eval even? exact->inexact exact? exp expt floor ' + 'force gcd imag-part inexact->exact inexact? input-port? integer->char ' + 'integer? interaction-environment lcm length list list->string ' + 'list->vector list-ref list-tail list? load log magnitude make-polar ' + 'make-rectangular make-string make-vector max member memq memv min ' + 'modulo negative? newline not null-environment null? number->string ' + 'number? numerator odd? open-input-file open-output-file output-port? ' + 'pair? peek-char port? positive? procedure? quasiquote quote quotient ' + 'rational? rationalize read read-char real-part real? remainder reverse ' + 'round scheme-report-environment set! set-car! set-cdr! sin sqrt string ' + 'string->list string->number string->symbol string-append string-ci<=? ' + 'string-ci<? string-ci=? string-ci>=? string-ci>? string-copy ' + 'string-fill! string-length string-ref string-set! string<=? string<? ' + 'string=? string>=? string>? string? substring symbol->string symbol? ' + 'tan transcript-off transcript-on truncate values vector ' + 'vector->list vector-fill! vector-length vector-ref vector-set! ' + 'with-input-from-file with-output-to-file write write-char zero?'\n  };\n  const LITERAL = {\n    className: 'literal',\n    begin: '(#t|#f|#\\\\\\\\' + SCHEME_IDENT_RE + '|#\\\\\\\\.)'\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [{\n      begin: SCHEME_SIMPLE_NUMBER_RE,\n      relevance: 0\n    }, {\n      begin: SCHEME_COMPLEX_NUMBER_RE,\n      relevance: 0\n    }, {\n      begin: '#b[0-1]+(/[0-1]+)?'\n    }, {\n      begin: '#o[0-7]+(/[0-7]+)?'\n    }, {\n      begin: '#x[0-9a-f]+(/[0-9a-f]+)?'\n    }]\n  };\n  const STRING = hljs.QUOTE_STRING_MODE;\n  const COMMENT_MODES = [hljs.COMMENT(';', '$', {\n    relevance: 0\n  }), hljs.COMMENT('#\\\\|', '\\\\|#')];\n  const IDENT = {\n    begin: SCHEME_IDENT_RE,\n    relevance: 0\n  };\n  const QUOTED_IDENT = {\n    className: 'symbol',\n    begin: '\\'' + SCHEME_IDENT_RE\n  };\n  const BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  const QUOTED_LIST = {\n    variants: [{\n      begin: /'/\n    }, {\n      begin: '`'\n    }],\n    contains: [{\n      begin: '\\\\(',\n      end: '\\\\)',\n      contains: ['self', LITERAL, STRING, NUMBER, IDENT, QUOTED_IDENT]\n    }]\n  };\n  const NAME = {\n    className: 'name',\n    relevance: 0,\n    begin: SCHEME_IDENT_RE,\n    keywords: KEYWORDS\n  };\n  const LAMBDA = {\n    begin: /lambda/,\n    endsWithParent: true,\n    returnBegin: true,\n    contains: [NAME, {\n      endsParent: true,\n      variants: [{\n        begin: /\\(/,\n        end: /\\)/\n      }, {\n        begin: /\\[/,\n        end: /\\]/\n      }],\n      contains: [IDENT]\n    }]\n  };\n  const LIST = {\n    variants: [{\n      begin: '\\\\(',\n      end: '\\\\)'\n    }, {\n      begin: '\\\\[',\n      end: '\\\\]'\n    }],\n    contains: [LAMBDA, NAME, BODY]\n  };\n  BODY.contains = [LITERAL, NUMBER, STRING, IDENT, QUOTED_IDENT, QUOTED_LIST, LIST].concat(COMMENT_MODES);\n  return {\n    name: 'Scheme',\n    illegal: /\\S/,\n    contains: [hljs.SHEBANG(), NUMBER, STRING, QUOTED_IDENT, QUOTED_LIST, LIST].concat(COMMENT_MODES)\n  };\n}\nmodule.exports = scheme;", "map": {"version": 3, "names": ["scheme", "hljs", "SCHEME_IDENT_RE", "SCHEME_SIMPLE_NUMBER_RE", "SCHEME_COMPLEX_NUMBER_RE", "KEYWORDS", "$pattern", "LITERAL", "className", "begin", "NUMBER", "variants", "relevance", "STRING", "QUOTE_STRING_MODE", "COMMENT_MODES", "COMMENT", "IDENT", "QUOTED_IDENT", "BODY", "endsWithParent", "QUOTED_LIST", "contains", "end", "NAME", "keywords", "LAMBDA", "returnBegin", "endsParent", "LIST", "concat", "name", "illegal", "SHEBANG", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/scheme.js"], "sourcesContent": ["/*\nLanguage: Scheme\nDescription: Scheme is a programming language in the Lisp family.\n             (keywords based on http://community.schemewiki.org/?scheme-keywords)\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nOrigin: clojure.js\nWebsite: http://community.schemewiki.org/?what-is-scheme\nCategory: lisp\n*/\n\nfunction scheme(hljs) {\n  const SCHEME_IDENT_RE = '[^\\\\(\\\\)\\\\[\\\\]\\\\{\\\\}\",\\'`;#|\\\\\\\\\\\\s]+';\n  const SCHEME_SIMPLE_NUMBER_RE = '(-|\\\\+)?\\\\d+([./]\\\\d+)?';\n  const SCHEME_COMPLEX_NUMBER_RE = SCHEME_SIMPLE_NUMBER_RE + '[+\\\\-]' + SCHEME_SIMPLE_NUMBER_RE + 'i';\n  const KEYWORDS = {\n    $pattern: SCHEME_IDENT_RE,\n    'builtin-name':\n      'case-lambda call/cc class define-class exit-handler field import ' +\n      'inherit init-field interface let*-values let-values let/ec mixin ' +\n      'opt-lambda override protect provide public rename require ' +\n      'require-for-syntax syntax syntax-case syntax-error unit/sig unless ' +\n      'when with-syntax and begin call-with-current-continuation ' +\n      'call-with-input-file call-with-output-file case cond define ' +\n      'define-syntax delay do dynamic-wind else for-each if lambda let let* ' +\n      'let-syntax letrec letrec-syntax map or syntax-rules \\' * + , ,@ - ... / ' +\n      '; < <= = => > >= ` abs acos angle append apply asin assoc assq assv atan ' +\n      'boolean? caar cadr call-with-input-file call-with-output-file ' +\n      'call-with-values car cdddar cddddr cdr ceiling char->integer ' +\n      'char-alphabetic? char-ci<=? char-ci<? char-ci=? char-ci>=? char-ci>? ' +\n      'char-downcase char-lower-case? char-numeric? char-ready? char-upcase ' +\n      'char-upper-case? char-whitespace? char<=? char<? char=? char>=? char>? ' +\n      'char? close-input-port close-output-port complex? cons cos ' +\n      'current-input-port current-output-port denominator display eof-object? ' +\n      'eq? equal? eqv? eval even? exact->inexact exact? exp expt floor ' +\n      'force gcd imag-part inexact->exact inexact? input-port? integer->char ' +\n      'integer? interaction-environment lcm length list list->string ' +\n      'list->vector list-ref list-tail list? load log magnitude make-polar ' +\n      'make-rectangular make-string make-vector max member memq memv min ' +\n      'modulo negative? newline not null-environment null? number->string ' +\n      'number? numerator odd? open-input-file open-output-file output-port? ' +\n      'pair? peek-char port? positive? procedure? quasiquote quote quotient ' +\n      'rational? rationalize read read-char real-part real? remainder reverse ' +\n      'round scheme-report-environment set! set-car! set-cdr! sin sqrt string ' +\n      'string->list string->number string->symbol string-append string-ci<=? ' +\n      'string-ci<? string-ci=? string-ci>=? string-ci>? string-copy ' +\n      'string-fill! string-length string-ref string-set! string<=? string<? ' +\n      'string=? string>=? string>? string? substring symbol->string symbol? ' +\n      'tan transcript-off transcript-on truncate values vector ' +\n      'vector->list vector-fill! vector-length vector-ref vector-set! ' +\n      'with-input-from-file with-output-to-file write write-char zero?'\n  };\n\n  const LITERAL = {\n    className: 'literal',\n    begin: '(#t|#f|#\\\\\\\\' + SCHEME_IDENT_RE + '|#\\\\\\\\.)'\n  };\n\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      {\n        begin: SCHEME_SIMPLE_NUMBER_RE,\n        relevance: 0\n      },\n      {\n        begin: SCHEME_COMPLEX_NUMBER_RE,\n        relevance: 0\n      },\n      {\n        begin: '#b[0-1]+(/[0-1]+)?'\n      },\n      {\n        begin: '#o[0-7]+(/[0-7]+)?'\n      },\n      {\n        begin: '#x[0-9a-f]+(/[0-9a-f]+)?'\n      }\n    ]\n  };\n\n  const STRING = hljs.QUOTE_STRING_MODE;\n\n  const COMMENT_MODES = [\n    hljs.COMMENT(\n      ';',\n      '$',\n      {\n        relevance: 0\n      }\n    ),\n    hljs.COMMENT('#\\\\|', '\\\\|#')\n  ];\n\n  const IDENT = {\n    begin: SCHEME_IDENT_RE,\n    relevance: 0\n  };\n\n  const QUOTED_IDENT = {\n    className: 'symbol',\n    begin: '\\'' + SCHEME_IDENT_RE\n  };\n\n  const BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n\n  const QUOTED_LIST = {\n    variants: [\n      {\n        begin: /'/\n      },\n      {\n        begin: '`'\n      }\n    ],\n    contains: [\n      {\n        begin: '\\\\(',\n        end: '\\\\)',\n        contains: [\n          'self',\n          LITERAL,\n          STRING,\n          NUMBER,\n          IDENT,\n          QUOTED_IDENT\n        ]\n      }\n    ]\n  };\n\n  const NAME = {\n    className: 'name',\n    relevance: 0,\n    begin: SCHEME_IDENT_RE,\n    keywords: KEYWORDS\n  };\n\n  const LAMBDA = {\n    begin: /lambda/,\n    endsWithParent: true,\n    returnBegin: true,\n    contains: [\n      NAME,\n      {\n        endsParent: true,\n        variants: [\n          {\n            begin: /\\(/,\n            end: /\\)/\n          },\n          {\n            begin: /\\[/,\n            end: /\\]/\n          }\n        ],\n        contains: [ IDENT ]\n      }\n    ]\n  };\n\n  const LIST = {\n    variants: [\n      {\n        begin: '\\\\(',\n        end: '\\\\)'\n      },\n      {\n        begin: '\\\\[',\n        end: '\\\\]'\n      }\n    ],\n    contains: [\n      LAMBDA,\n      NAME,\n      BODY\n    ]\n  };\n\n  BODY.contains = [\n    LITERAL,\n    NUMBER,\n    STRING,\n    IDENT,\n    QUOTED_IDENT,\n    QUOTED_LIST,\n    LIST\n  ].concat(COMMENT_MODES);\n\n  return {\n    name: 'Scheme',\n    illegal: /\\S/,\n    contains: [\n      hljs.SHEBANG(),\n      NUMBER,\n      STRING,\n      QUOTED_IDENT,\n      QUOTED_LIST,\n      LIST\n    ].concat(COMMENT_MODES)\n  };\n}\n\nmodule.exports = scheme;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,eAAe,GAAG,uCAAuC;EAC/D,MAAMC,uBAAuB,GAAG,yBAAyB;EACzD,MAAMC,wBAAwB,GAAGD,uBAAuB,GAAG,QAAQ,GAAGA,uBAAuB,GAAG,GAAG;EACnG,MAAME,QAAQ,GAAG;IACfC,QAAQ,EAAEJ,eAAe;IACzB,cAAc,EACZ,mEAAmE,GACnE,mEAAmE,GACnE,4DAA4D,GAC5D,qEAAqE,GACrE,4DAA4D,GAC5D,8DAA8D,GAC9D,uEAAuE,GACvE,0EAA0E,GAC1E,2EAA2E,GAC3E,gEAAgE,GAChE,+DAA+D,GAC/D,uEAAuE,GACvE,uEAAuE,GACvE,yEAAyE,GACzE,6DAA6D,GAC7D,yEAAyE,GACzE,kEAAkE,GAClE,wEAAwE,GACxE,gEAAgE,GAChE,sEAAsE,GACtE,oEAAoE,GACpE,qEAAqE,GACrE,uEAAuE,GACvE,uEAAuE,GACvE,yEAAyE,GACzE,yEAAyE,GACzE,wEAAwE,GACxE,+DAA+D,GAC/D,uEAAuE,GACvE,uEAAuE,GACvE,0DAA0D,GAC1D,iEAAiE,GACjE;EACJ,CAAC;EAED,MAAMK,OAAO,GAAG;IACdC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,cAAc,GAAGP,eAAe,GAAG;EAC5C,CAAC;EAED,MAAMQ,MAAM,GAAG;IACbF,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MACEF,KAAK,EAAEN,uBAAuB;MAC9BS,SAAS,EAAE;IACb,CAAC,EACD;MACEH,KAAK,EAAEL,wBAAwB;MAC/BQ,SAAS,EAAE;IACb,CAAC,EACD;MACEH,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EAED,MAAMI,MAAM,GAAGZ,IAAI,CAACa,iBAAiB;EAErC,MAAMC,aAAa,GAAG,CACpBd,IAAI,CAACe,OAAO,CACV,GAAG,EACH,GAAG,EACH;IACEJ,SAAS,EAAE;EACb,CACF,CAAC,EACDX,IAAI,CAACe,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAC7B;EAED,MAAMC,KAAK,GAAG;IACZR,KAAK,EAAEP,eAAe;IACtBU,SAAS,EAAE;EACb,CAAC;EAED,MAAMM,YAAY,GAAG;IACnBV,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI,GAAGP;EAChB,CAAC;EAED,MAAMiB,IAAI,GAAG;IACXC,cAAc,EAAE,IAAI;IACpBR,SAAS,EAAE;EACb,CAAC;EAED,MAAMS,WAAW,GAAG;IAClBV,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,CACF;IACDa,QAAQ,EAAE,CACR;MACEb,KAAK,EAAE,KAAK;MACZc,GAAG,EAAE,KAAK;MACVD,QAAQ,EAAE,CACR,MAAM,EACNf,OAAO,EACPM,MAAM,EACNH,MAAM,EACNO,KAAK,EACLC,YAAY;IAEhB,CAAC;EAEL,CAAC;EAED,MAAMM,IAAI,GAAG;IACXhB,SAAS,EAAE,MAAM;IACjBI,SAAS,EAAE,CAAC;IACZH,KAAK,EAAEP,eAAe;IACtBuB,QAAQ,EAAEpB;EACZ,CAAC;EAED,MAAMqB,MAAM,GAAG;IACbjB,KAAK,EAAE,QAAQ;IACfW,cAAc,EAAE,IAAI;IACpBO,WAAW,EAAE,IAAI;IACjBL,QAAQ,EAAE,CACRE,IAAI,EACJ;MACEI,UAAU,EAAE,IAAI;MAChBjB,QAAQ,EAAE,CACR;QACEF,KAAK,EAAE,IAAI;QACXc,GAAG,EAAE;MACP,CAAC,EACD;QACEd,KAAK,EAAE,IAAI;QACXc,GAAG,EAAE;MACP,CAAC,CACF;MACDD,QAAQ,EAAE,CAAEL,KAAK;IACnB,CAAC;EAEL,CAAC;EAED,MAAMY,IAAI,GAAG;IACXlB,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE,KAAK;MACZc,GAAG,EAAE;IACP,CAAC,EACD;MACEd,KAAK,EAAE,KAAK;MACZc,GAAG,EAAE;IACP,CAAC,CACF;IACDD,QAAQ,EAAE,CACRI,MAAM,EACNF,IAAI,EACJL,IAAI;EAER,CAAC;EAEDA,IAAI,CAACG,QAAQ,GAAG,CACdf,OAAO,EACPG,MAAM,EACNG,MAAM,EACNI,KAAK,EACLC,YAAY,EACZG,WAAW,EACXQ,IAAI,CACL,CAACC,MAAM,CAACf,aAAa,CAAC;EAEvB,OAAO;IACLgB,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,IAAI;IACbV,QAAQ,EAAE,CACRrB,IAAI,CAACgC,OAAO,CAAC,CAAC,EACdvB,MAAM,EACNG,MAAM,EACNK,YAAY,EACZG,WAAW,EACXQ,IAAI,CACL,CAACC,MAAM,CAACf,aAAa;EACxB,CAAC;AACH;AAEAmB,MAAM,CAACC,OAAO,GAAGnC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}