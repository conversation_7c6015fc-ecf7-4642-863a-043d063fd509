{"ast": null, "code": "'use strict';\n\nmodule.exports = firestoreSecurityRules;\nfirestoreSecurityRules.displayName = 'firestoreSecurityRules';\nfirestoreSecurityRules.aliases = [];\nfunction firestoreSecurityRules(Prism) {\n  Prism.languages['firestore-security-rules'] = Prism.languages.extend('clike', {\n    comment: /\\/\\/.*/,\n    keyword: /\\b(?:allow|function|if|match|null|return|rules_version|service)\\b/,\n    operator: /&&|\\|\\||[<>!=]=?|[-+*/%]|\\b(?:in|is)\\b/\n  });\n  delete Prism.languages['firestore-security-rules']['class-name'];\n  Prism.languages.insertBefore('firestore-security-rules', 'keyword', {\n    path: {\n      pattern: /(^|[\\s(),])(?:\\/(?:[\\w\\xA0-\\uFFFF]+|\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)))+/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)/,\n          inside: {\n            operator: /=/,\n            keyword: /\\*\\*/,\n            punctuation: /[.$(){}]/\n          }\n        },\n        punctuation: /\\//\n      }\n    },\n    method: {\n      // to make the pattern shorter, the actual method names are omitted\n      pattern: /(\\ballow\\s+)[a-z]+(?:\\s*,\\s*[a-z]+)*(?=\\s*[:;])/,\n      lookbehind: true,\n      alias: 'builtin',\n      inside: {\n        punctuation: /,/\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "firestoreSecurityRules", "displayName", "aliases", "Prism", "languages", "extend", "comment", "keyword", "operator", "insertBefore", "path", "pattern", "lookbehind", "greedy", "inside", "variable", "punctuation", "method", "alias"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/firestore-security-rules.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = firestoreSecurityRules\nfirestoreSecurityRules.displayName = 'firestoreSecurityRules'\nfirestoreSecurityRules.aliases = []\nfunction firestoreSecurityRules(Prism) {\n  Prism.languages['firestore-security-rules'] = Prism.languages.extend(\n    'clike',\n    {\n      comment: /\\/\\/.*/,\n      keyword:\n        /\\b(?:allow|function|if|match|null|return|rules_version|service)\\b/,\n      operator: /&&|\\|\\||[<>!=]=?|[-+*/%]|\\b(?:in|is)\\b/\n    }\n  )\n  delete Prism.languages['firestore-security-rules']['class-name']\n  Prism.languages.insertBefore('firestore-security-rules', 'keyword', {\n    path: {\n      pattern:\n        /(^|[\\s(),])(?:\\/(?:[\\w\\xA0-\\uFFFF]+|\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)))+/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)/,\n          inside: {\n            operator: /=/,\n            keyword: /\\*\\*/,\n            punctuation: /[.$(){}]/\n          }\n        },\n        punctuation: /\\//\n      }\n    },\n    method: {\n      // to make the pattern shorter, the actual method names are omitted\n      pattern: /(\\ballow\\s+)[a-z]+(?:\\s*,\\s*[a-z]+)*(?=\\s*[:;])/,\n      lookbehind: true,\n      alias: 'builtin',\n      inside: {\n        punctuation: /,/\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,sBAAsB;AACvCA,sBAAsB,CAACC,WAAW,GAAG,wBAAwB;AAC7DD,sBAAsB,CAACE,OAAO,GAAG,EAAE;AACnC,SAASF,sBAAsBA,CAACG,KAAK,EAAE;EACrCA,KAAK,CAACC,SAAS,CAAC,0BAA0B,CAAC,GAAGD,KAAK,CAACC,SAAS,CAACC,MAAM,CAClE,OAAO,EACP;IACEC,OAAO,EAAE,QAAQ;IACjBC,OAAO,EACL,mEAAmE;IACrEC,QAAQ,EAAE;EACZ,CACF,CAAC;EACD,OAAOL,KAAK,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,YAAY,CAAC;EAChED,KAAK,CAACC,SAAS,CAACK,YAAY,CAAC,0BAA0B,EAAE,SAAS,EAAE;IAClEC,IAAI,EAAE;MACJC,OAAO,EACL,+FAA+F;MACjGC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNC,QAAQ,EAAE;UACRJ,OAAO,EAAE,wDAAwD;UACjEG,MAAM,EAAE;YACNN,QAAQ,EAAE,GAAG;YACbD,OAAO,EAAE,MAAM;YACfS,WAAW,EAAE;UACf;QACF,CAAC;QACDA,WAAW,EAAE;MACf;IACF,CAAC;IACDC,MAAM,EAAE;MACN;MACAN,OAAO,EAAE,iDAAiD;MAC1DC,UAAU,EAAE,IAAI;MAChBM,KAAK,EAAE,SAAS;MAChBJ,MAAM,EAAE;QACNE,WAAW,EAAE;MACf;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}