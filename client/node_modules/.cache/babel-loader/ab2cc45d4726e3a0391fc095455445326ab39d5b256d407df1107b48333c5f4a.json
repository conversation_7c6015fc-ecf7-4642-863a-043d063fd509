{"ast": null, "code": "'use strict';\n\nvar caseSensitiveTransform = require('./case-sensitive-transform');\nmodule.exports = caseInsensitiveTransform;\nfunction caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase());\n}", "map": {"version": 3, "names": ["caseSensitiveTransform", "require", "module", "exports", "caseInsensitiveTransform", "attributes", "property", "toLowerCase"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/lib/util/case-insensitive-transform.js"], "sourcesContent": ["'use strict'\n\nvar caseSensitiveTransform = require('./case-sensitive-transform')\n\nmodule.exports = caseInsensitiveTransform\n\nfunction caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAElEC,MAAM,CAACC,OAAO,GAAGC,wBAAwB;AAEzC,SAASA,wBAAwBA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EACtD,OAAON,sBAAsB,CAACK,UAAU,EAAEC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}