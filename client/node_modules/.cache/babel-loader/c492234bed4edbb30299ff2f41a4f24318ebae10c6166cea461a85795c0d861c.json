{"ast": null, "code": "'use strict';\n\nmodule.exports = nginx;\nnginx.displayName = 'nginx';\nnginx.aliases = [];\nfunction nginx(Prism) {\n  ;\n  (function (Prism) {\n    var variable = /\\$(?:\\w[a-z\\d]*(?:_[^\\x00-\\x1F\\s\"'\\\\()$]*)?|\\{[^}\\s\"'\\\\]+\\})/i;\n    Prism.languages.nginx = {\n      comment: {\n        pattern: /(^|[\\s{};])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      directive: {\n        pattern: /(^|\\s)\\w(?:[^;{}\"'\\\\\\s]|\\\\.|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*'|\\s+(?:#.*(?!.)|(?![#\\s])))*?(?=\\s*[;{])/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          string: {\n            pattern: /((?:^|[^\\\\])(?:\\\\\\\\)*)(?:\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*')/,\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              escape: {\n                pattern: /\\\\[\"'\\\\nrt]/,\n                alias: 'entity'\n              },\n              variable: variable\n            }\n          },\n          comment: {\n            pattern: /(\\s)#.*/,\n            lookbehind: true,\n            greedy: true\n          },\n          keyword: {\n            pattern: /^\\S+/,\n            greedy: true\n          },\n          // other patterns\n          boolean: {\n            pattern: /(\\s)(?:off|on)(?!\\S)/,\n            lookbehind: true\n          },\n          number: {\n            pattern: /(\\s)\\d+[a-z]*(?!\\S)/i,\n            lookbehind: true\n          },\n          variable: variable\n        }\n      },\n      punctuation: /[{};]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "nginx", "displayName", "aliases", "Prism", "variable", "languages", "comment", "pattern", "lookbehind", "greedy", "directive", "inside", "string", "escape", "alias", "keyword", "boolean", "number", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/nginx.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nginx\nnginx.displayName = 'nginx'\nnginx.aliases = []\nfunction nginx(Prism) {\n  ;(function (Prism) {\n    var variable =\n      /\\$(?:\\w[a-z\\d]*(?:_[^\\x00-\\x1F\\s\"'\\\\()$]*)?|\\{[^}\\s\"'\\\\]+\\})/i\n    Prism.languages.nginx = {\n      comment: {\n        pattern: /(^|[\\s{};])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      directive: {\n        pattern:\n          /(^|\\s)\\w(?:[^;{}\"'\\\\\\s]|\\\\.|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*'|\\s+(?:#.*(?!.)|(?![#\\s])))*?(?=\\s*[;{])/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          string: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\\\\\)*)(?:\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*')/,\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              escape: {\n                pattern: /\\\\[\"'\\\\nrt]/,\n                alias: 'entity'\n              },\n              variable: variable\n            }\n          },\n          comment: {\n            pattern: /(\\s)#.*/,\n            lookbehind: true,\n            greedy: true\n          },\n          keyword: {\n            pattern: /^\\S+/,\n            greedy: true\n          },\n          // other patterns\n          boolean: {\n            pattern: /(\\s)(?:off|on)(?!\\S)/,\n            lookbehind: true\n          },\n          number: {\n            pattern: /(\\s)\\d+[a-z]*(?!\\S)/i,\n            lookbehind: true\n          },\n          variable: variable\n        }\n      },\n      punctuation: /[{};]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GACV,+DAA+D;IACjED,KAAK,CAACE,SAAS,CAACL,KAAK,GAAG;MACtBM,OAAO,EAAE;QACPC,OAAO,EAAE,gBAAgB;QACzBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV,CAAC;MACDC,SAAS,EAAE;QACTH,OAAO,EACL,yGAAyG;QAC3GC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZE,MAAM,EAAE;UACNC,MAAM,EAAE;YACNL,OAAO,EACL,+DAA+D;YACjEC,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE,IAAI;YACZE,MAAM,EAAE;cACNE,MAAM,EAAE;gBACNN,OAAO,EAAE,aAAa;gBACtBO,KAAK,EAAE;cACT,CAAC;cACDV,QAAQ,EAAEA;YACZ;UACF,CAAC;UACDE,OAAO,EAAE;YACPC,OAAO,EAAE,SAAS;YAClBC,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;UACV,CAAC;UACDM,OAAO,EAAE;YACPR,OAAO,EAAE,MAAM;YACfE,MAAM,EAAE;UACV,CAAC;UACD;UACAO,OAAO,EAAE;YACPT,OAAO,EAAE,sBAAsB;YAC/BC,UAAU,EAAE;UACd,CAAC;UACDS,MAAM,EAAE;YACNV,OAAO,EAAE,sBAAsB;YAC/BC,UAAU,EAAE;UACd,CAAC;UACDJ,QAAQ,EAAEA;QACZ;MACF,CAAC;MACDc,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEf,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}