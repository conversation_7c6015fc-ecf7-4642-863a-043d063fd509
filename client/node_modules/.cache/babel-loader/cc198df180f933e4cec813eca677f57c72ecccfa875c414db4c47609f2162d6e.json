{"ast": null, "code": "'use strict';\n\nmodule.exports = brightscript;\nbrightscript.displayName = 'brightscript';\nbrightscript.aliases = [];\nfunction brightscript(Prism) {\n  Prism.languages.brightscript = {\n    comment: /(?:\\brem|').*/i,\n    'directive-statement': {\n      pattern: /(^[\\t ]*)#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if).*/im,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        'error-message': {\n          pattern: /(^#error).+/,\n          lookbehind: true\n        },\n        directive: {\n          pattern: /^#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if)/,\n          alias: 'keyword'\n        },\n        expression: {\n          pattern: /[\\s\\S]+/,\n          inside: null // see below\n        }\n      }\n    },\n    property: {\n      pattern: /([\\r\\n{,][\\t ]*)(?:(?!\\d)\\w+|\"(?:[^\"\\r\\n]|\"\")*\"(?!\"))(?=[ \\t]*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\bAs[\\t ]+)\\w+/i,\n      lookbehind: true\n    },\n    keyword: /\\b(?:As|Dim|Each|Else|Elseif|End|Exit|For|Function|Goto|If|In|Print|Return|Step|Stop|Sub|Then|To|While)\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    function: /\\b(?!\\d)\\w+(?=[\\t ]*\\()/,\n    number: /(?:\\b\\d+(?:\\.\\d+)?(?:[ed][+-]\\d+)?|&h[a-f\\d]+)\\b[%&!#]?/i,\n    operator: /--|\\+\\+|>>=?|<<=?|<>|[-+*/\\\\<>]=?|[:^=?]|\\b(?:and|mod|not|or)\\b/i,\n    punctuation: /[.,;()[\\]{}]/,\n    constant: /\\b(?:LINE_NUM)\\b/i\n  };\n  Prism.languages.brightscript['directive-statement'].inside.expression.inside = Prism.languages.brightscript;\n}", "map": {"version": 3, "names": ["module", "exports", "brightscript", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "alias", "inside", "directive", "expression", "property", "greedy", "string", "keyword", "boolean", "function", "number", "operator", "punctuation", "constant"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/brightscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = brightscript\nbrightscript.displayName = 'brightscript'\nbrightscript.aliases = []\nfunction brightscript(Prism) {\n  Prism.languages.brightscript = {\n    comment: /(?:\\brem|').*/i,\n    'directive-statement': {\n      pattern: /(^[\\t ]*)#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if).*/im,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        'error-message': {\n          pattern: /(^#error).+/,\n          lookbehind: true\n        },\n        directive: {\n          pattern: /^#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if)/,\n          alias: 'keyword'\n        },\n        expression: {\n          pattern: /[\\s\\S]+/,\n          inside: null // see below\n        }\n      }\n    },\n    property: {\n      pattern:\n        /([\\r\\n{,][\\t ]*)(?:(?!\\d)\\w+|\"(?:[^\"\\r\\n]|\"\")*\"(?!\"))(?=[ \\t]*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\bAs[\\t ]+)\\w+/i,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:As|Dim|Each|Else|Elseif|End|Exit|For|Function|Goto|If|In|Print|Return|Step|Stop|Sub|Then|To|While)\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    function: /\\b(?!\\d)\\w+(?=[\\t ]*\\()/,\n    number: /(?:\\b\\d+(?:\\.\\d+)?(?:[ed][+-]\\d+)?|&h[a-f\\d]+)\\b[%&!#]?/i,\n    operator:\n      /--|\\+\\+|>>=?|<<=?|<>|[-+*/\\\\<>]=?|[:^=?]|\\b(?:and|mod|not|or)\\b/i,\n    punctuation: /[.,;()[\\]{}]/,\n    constant: /\\b(?:LINE_NUM)\\b/i\n  }\n  Prism.languages.brightscript['directive-statement'].inside.expression.inside =\n    Prism.languages.brightscript\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;AAC7BA,YAAY,CAACC,WAAW,GAAG,cAAc;AACzCD,YAAY,CAACE,OAAO,GAAG,EAAE;AACzB,SAASF,YAAYA,CAACG,KAAK,EAAE;EAC3BA,KAAK,CAACC,SAAS,CAACJ,YAAY,GAAG;IAC7BK,OAAO,EAAE,gBAAgB;IACzB,qBAAqB,EAAE;MACrBC,OAAO,EAAE,gEAAgE;MACzEC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QACN,eAAe,EAAE;UACfH,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE;QACd,CAAC;QACDG,SAAS,EAAE;UACTJ,OAAO,EAAE,oDAAoD;UAC7DE,KAAK,EAAE;QACT,CAAC;QACDG,UAAU,EAAE;UACVL,OAAO,EAAE,SAAS;UAClBG,MAAM,EAAE,IAAI,CAAC;QACf;MACF;IACF,CAAC;IACDG,QAAQ,EAAE;MACRN,OAAO,EACL,kEAAkE;MACpEC,UAAU,EAAE,IAAI;MAChBM,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNR,OAAO,EAAE,yBAAyB;MAClCO,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZP,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE;IACd,CAAC;IACDQ,OAAO,EACL,4GAA4G;IAC9GC,OAAO,EAAE,qBAAqB;IAC9BC,QAAQ,EAAE,yBAAyB;IACnCC,MAAM,EAAE,0DAA0D;IAClEC,QAAQ,EACN,kEAAkE;IACpEC,WAAW,EAAE,cAAc;IAC3BC,QAAQ,EAAE;EACZ,CAAC;EACDlB,KAAK,CAACC,SAAS,CAACJ,YAAY,CAAC,qBAAqB,CAAC,CAACS,MAAM,CAACE,UAAU,CAACF,MAAM,GAC1EN,KAAK,CAACC,SAAS,CAACJ,YAAY;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}