{"ast": null, "code": "'use strict';\n\nvar refractorC = require('./c.js');\nmodule.exports = objectivec;\nobjectivec.displayName = 'objectivec';\nobjectivec.aliases = ['objc'];\nfunction objectivec(Prism) {\n  Prism.register(refractorC);\n  Prism.languages.objectivec = Prism.languages.extend('c', {\n    string: {\n      pattern: /@?\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    keyword: /\\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\\b/,\n    operator: /-[->]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|\\|?|[~^%?*\\/@]/\n  });\n  delete Prism.languages.objectivec['class-name'];\n  Prism.languages.objc = Prism.languages.objectivec;\n}", "map": {"version": 3, "names": ["refractorC", "require", "module", "exports", "objectivec", "displayName", "aliases", "Prism", "register", "languages", "extend", "string", "pattern", "greedy", "keyword", "operator", "objc"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/objectivec.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = objectivec\nobjectivec.displayName = 'objectivec'\nobjectivec.aliases = ['objc']\nfunction objectivec(Prism) {\n  Prism.register(refractorC)\n  Prism.languages.objectivec = Prism.languages.extend('c', {\n    string: {\n      pattern: /@?\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\\b/,\n    operator: /-[->]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|\\|?|[~^%?*\\/@]/\n  })\n  delete Prism.languages.objectivec['class-name']\n  Prism.languages.objc = Prism.languages.objectivec\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClCC,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,CAAC,MAAM,CAAC;AAC7B,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,QAAQ,CAACR,UAAU,CAAC;EAC1BO,KAAK,CAACE,SAAS,CAACL,UAAU,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,GAAG,EAAE;IACvDC,MAAM,EAAE;MACNC,OAAO,EAAE,uCAAuC;MAChDC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EACL,uYAAuY;IACzYC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,OAAOR,KAAK,CAACE,SAAS,CAACL,UAAU,CAAC,YAAY,CAAC;EAC/CG,KAAK,CAACE,SAAS,CAACO,IAAI,GAAGT,KAAK,CAACE,SAAS,CAACL,UAAU;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}