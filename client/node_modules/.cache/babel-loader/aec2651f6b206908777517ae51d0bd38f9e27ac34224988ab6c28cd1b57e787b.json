{"ast": null, "code": "'use strict';\n\nvar create = require('./util/create');\nmodule.exports = create({\n  space: 'xlink',\n  transform: xlinkTransform,\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n});\nfunction xlinkTransform(_, prop) {\n  return 'xlink:' + prop.slice(5).toLowerCase();\n}", "map": {"version": 3, "names": ["create", "require", "module", "exports", "space", "transform", "xlinkTransform", "properties", "xLinkActuate", "xLinkArcRole", "xLinkHref", "xLinkRole", "xLinkShow", "xLinkTitle", "xLinkType", "_", "prop", "slice", "toLowerCase"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/property-information/lib/xlink.js"], "sourcesContent": ["'use strict'\n\nvar create = require('./util/create')\n\nmodule.exports = create({\n  space: 'xlink',\n  transform: xlinkTransform,\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n\nfunction xlinkTransform(_, prop) {\n  return 'xlink:' + prop.slice(5).toLowerCase()\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,eAAe,CAAC;AAErCC,MAAM,CAACC,OAAO,GAAGH,MAAM,CAAC;EACtBI,KAAK,EAAE,OAAO;EACdC,SAAS,EAAEC,cAAc;EACzBC,UAAU,EAAE;IACVC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,SAASR,cAAcA,CAACS,CAAC,EAAEC,IAAI,EAAE;EAC/B,OAAO,QAAQ,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}