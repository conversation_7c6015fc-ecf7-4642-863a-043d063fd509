{"ast": null, "code": "'use strict';\n\nmodule.exports = d;\nd.displayName = 'd';\nd.aliases = [];\nfunction d(Prism) {\n  Prism.languages.d = Prism.languages.extend('clike', {\n    comment: [{\n      // Shebang\n      pattern: /^\\s*#!.+/,\n      greedy: true\n    }, {\n      pattern: RegExp(/(^|[^\\\\])/.source + '(?:' + [\n      // /+ comment +/\n      // Allow one level of nesting\n      /\\/\\+(?:\\/\\+(?:[^+]|\\+(?!\\/))*\\+\\/|(?!\\/\\+)[\\s\\S])*?\\+\\//.source,\n      // // comment\n      /\\/\\/.*/.source,\n      // /* comment */\n      /\\/\\*[\\s\\S]*?\\*\\//.source].join('|') + ')'),\n      lookbehind: true,\n      greedy: true\n    }],\n    string: [{\n      pattern: RegExp([\n      // r\"\", x\"\"\n      /\\b[rx]\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"[cwd]?/.source,\n      // q\"[]\", q\"()\", q\"<>\", q\"{}\"\n      /\\bq\"(?:\\[[\\s\\S]*?\\]|\\([\\s\\S]*?\\)|<[\\s\\S]*?>|\\{[\\s\\S]*?\\})\"/.source,\n      // q\"IDENT\n      // ...\n      // IDENT\"\n      /\\bq\"((?!\\d)\\w+)$[\\s\\S]*?^\\1\"/.source,\n      // q\"//\", q\"||\", etc.\n      // eslint-disable-next-line regexp/strict\n      /\\bq\"(.)[\\s\\S]*?\\2\"/.source,\n      // eslint-disable-next-line regexp/strict\n      /([\"`])(?:\\\\[\\s\\S]|(?!\\3)[^\\\\])*\\3[cwd]?/.source].join('|'), 'm'),\n      greedy: true\n    }, {\n      pattern: /\\bq\\{(?:\\{[^{}]*\\}|[^{}])*\\}/,\n      greedy: true,\n      alias: 'token-string'\n    }],\n    // In order: $, keywords and special tokens, globally defined symbols\n    keyword: /\\$|\\b(?:__(?:(?:DATE|EOF|FILE|FUNCTION|LINE|MODULE|PRETTY_FUNCTION|TIMESTAMP|TIME|VENDOR|VERSION)__|gshared|parameters|traits|vector)|abstract|alias|align|asm|assert|auto|body|bool|break|byte|case|cast|catch|cdouble|cent|cfloat|char|class|const|continue|creal|dchar|debug|default|delegate|delete|deprecated|do|double|dstring|else|enum|export|extern|false|final|finally|float|for|foreach|foreach_reverse|function|goto|idouble|if|ifloat|immutable|import|inout|int|interface|invariant|ireal|lazy|long|macro|mixin|module|new|nothrow|null|out|override|package|pragma|private|protected|ptrdiff_t|public|pure|real|ref|return|scope|shared|short|size_t|static|string|struct|super|switch|synchronized|template|this|throw|true|try|typedef|typeid|typeof|ubyte|ucent|uint|ulong|union|unittest|ushort|version|void|volatile|wchar|while|with|wstring)\\b/,\n    number: [\n    // The lookbehind and the negative look-ahead try to prevent bad highlighting of the .. operator\n    // Hexadecimal numbers must be handled separately to avoid problems with exponent \"e\"\n    /\\b0x\\.?[a-f\\d_]+(?:(?!\\.\\.)\\.[a-f\\d_]*)?(?:p[+-]?[a-f\\d_]+)?[ulfi]{0,4}/i, {\n      pattern: /((?:\\.\\.)?)(?:\\b0b\\.?|\\b|\\.)\\d[\\d_]*(?:(?!\\.\\.)\\.[\\d_]*)?(?:e[+-]?\\d[\\d_]*)?[ulfi]{0,4}/i,\n      lookbehind: true\n    }],\n    operator: /\\|[|=]?|&[&=]?|\\+[+=]?|-[-=]?|\\.?\\.\\.|=[>=]?|!(?:i[ns]\\b|<>?=?|>=?|=)?|\\bi[ns]\\b|(?:<[<>]?|>>?>?|\\^\\^|[*\\/%^~])=?/\n  });\n  Prism.languages.insertBefore('d', 'string', {\n    // Characters\n    // 'a', '\\\\', '\\n', '\\xFF', '\\377', '\\uFFFF', '\\U0010FFFF', '\\quot'\n    char: /'(?:\\\\(?:\\W|\\w+)|[^\\\\])'/\n  });\n  Prism.languages.insertBefore('d', 'keyword', {\n    property: /\\B@\\w*/\n  });\n  Prism.languages.insertBefore('d', 'function', {\n    register: {\n      // Iasm registers\n      pattern: /\\b(?:[ABCD][LHX]|E?(?:BP|DI|SI|SP)|[BS]PL|[ECSDGF]S|CR[0234]|[DS]IL|DR[012367]|E[ABCD]X|X?MM[0-7]|R(?:1[0-5]|[89])[BWD]?|R[ABCD]X|R[BS]P|R[DS]I|TR[3-7]|XMM(?:1[0-5]|[89])|YMM(?:1[0-5]|\\d))\\b|\\bST(?:\\([0-7]\\)|\\b)/,\n      alias: 'variable'\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "d", "displayName", "aliases", "Prism", "languages", "extend", "comment", "pattern", "greedy", "RegExp", "source", "join", "lookbehind", "string", "alias", "keyword", "number", "operator", "insertBefore", "char", "property", "register"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/d.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = d\nd.displayName = 'd'\nd.aliases = []\nfunction d(Prism) {\n  Prism.languages.d = Prism.languages.extend('clike', {\n    comment: [\n      {\n        // Shebang\n        pattern: /^\\s*#!.+/,\n        greedy: true\n      },\n      {\n        pattern: RegExp(\n          /(^|[^\\\\])/.source +\n            '(?:' +\n            [\n              // /+ comment +/\n              // Allow one level of nesting\n              /\\/\\+(?:\\/\\+(?:[^+]|\\+(?!\\/))*\\+\\/|(?!\\/\\+)[\\s\\S])*?\\+\\//.source, // // comment\n              /\\/\\/.*/.source, // /* comment */\n              /\\/\\*[\\s\\S]*?\\*\\//.source\n            ].join('|') +\n            ')'\n        ),\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: [\n      {\n        pattern: RegExp(\n          [\n            // r\"\", x\"\"\n            /\\b[rx]\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"[cwd]?/.source, // q\"[]\", q\"()\", q\"<>\", q\"{}\"\n            /\\bq\"(?:\\[[\\s\\S]*?\\]|\\([\\s\\S]*?\\)|<[\\s\\S]*?>|\\{[\\s\\S]*?\\})\"/.source, // q\"IDENT\n            // ...\n            // IDENT\"\n            /\\bq\"((?!\\d)\\w+)$[\\s\\S]*?^\\1\"/.source, // q\"//\", q\"||\", etc.\n            // eslint-disable-next-line regexp/strict\n            /\\bq\"(.)[\\s\\S]*?\\2\"/.source, // eslint-disable-next-line regexp/strict\n            /([\"`])(?:\\\\[\\s\\S]|(?!\\3)[^\\\\])*\\3[cwd]?/.source\n          ].join('|'),\n          'm'\n        ),\n        greedy: true\n      },\n      {\n        pattern: /\\bq\\{(?:\\{[^{}]*\\}|[^{}])*\\}/,\n        greedy: true,\n        alias: 'token-string'\n      }\n    ],\n    // In order: $, keywords and special tokens, globally defined symbols\n    keyword:\n      /\\$|\\b(?:__(?:(?:DATE|EOF|FILE|FUNCTION|LINE|MODULE|PRETTY_FUNCTION|TIMESTAMP|TIME|VENDOR|VERSION)__|gshared|parameters|traits|vector)|abstract|alias|align|asm|assert|auto|body|bool|break|byte|case|cast|catch|cdouble|cent|cfloat|char|class|const|continue|creal|dchar|debug|default|delegate|delete|deprecated|do|double|dstring|else|enum|export|extern|false|final|finally|float|for|foreach|foreach_reverse|function|goto|idouble|if|ifloat|immutable|import|inout|int|interface|invariant|ireal|lazy|long|macro|mixin|module|new|nothrow|null|out|override|package|pragma|private|protected|ptrdiff_t|public|pure|real|ref|return|scope|shared|short|size_t|static|string|struct|super|switch|synchronized|template|this|throw|true|try|typedef|typeid|typeof|ubyte|ucent|uint|ulong|union|unittest|ushort|version|void|volatile|wchar|while|with|wstring)\\b/,\n    number: [\n      // The lookbehind and the negative look-ahead try to prevent bad highlighting of the .. operator\n      // Hexadecimal numbers must be handled separately to avoid problems with exponent \"e\"\n      /\\b0x\\.?[a-f\\d_]+(?:(?!\\.\\.)\\.[a-f\\d_]*)?(?:p[+-]?[a-f\\d_]+)?[ulfi]{0,4}/i,\n      {\n        pattern:\n          /((?:\\.\\.)?)(?:\\b0b\\.?|\\b|\\.)\\d[\\d_]*(?:(?!\\.\\.)\\.[\\d_]*)?(?:e[+-]?\\d[\\d_]*)?[ulfi]{0,4}/i,\n        lookbehind: true\n      }\n    ],\n    operator:\n      /\\|[|=]?|&[&=]?|\\+[+=]?|-[-=]?|\\.?\\.\\.|=[>=]?|!(?:i[ns]\\b|<>?=?|>=?|=)?|\\bi[ns]\\b|(?:<[<>]?|>>?>?|\\^\\^|[*\\/%^~])=?/\n  })\n  Prism.languages.insertBefore('d', 'string', {\n    // Characters\n    // 'a', '\\\\', '\\n', '\\xFF', '\\377', '\\uFFFF', '\\U0010FFFF', '\\quot'\n    char: /'(?:\\\\(?:\\W|\\w+)|[^\\\\])'/\n  })\n  Prism.languages.insertBefore('d', 'keyword', {\n    property: /\\B@\\w*/\n  })\n  Prism.languages.insertBefore('d', 'function', {\n    register: {\n      // Iasm registers\n      pattern:\n        /\\b(?:[ABCD][LHX]|E?(?:BP|DI|SI|SP)|[BS]PL|[ECSDGF]S|CR[0234]|[DS]IL|DR[012367]|E[ABCD]X|X?MM[0-7]|R(?:1[0-5]|[89])[BWD]?|R[ABCD]X|R[BS]P|R[DS]I|TR[3-7]|XMM(?:1[0-5]|[89])|YMM(?:1[0-5]|\\d))\\b|\\bST(?:\\([0-7]\\)|\\b)/,\n      alias: 'variable'\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,CAAC;AAClBA,CAAC,CAACC,WAAW,GAAG,GAAG;AACnBD,CAAC,CAACE,OAAO,GAAG,EAAE;AACd,SAASF,CAACA,CAACG,KAAK,EAAE;EAChBA,KAAK,CAACC,SAAS,CAACJ,CAAC,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IAClDC,OAAO,EAAE,CACP;MACE;MACAC,OAAO,EAAE,UAAU;MACnBC,MAAM,EAAE;IACV,CAAC,EACD;MACED,OAAO,EAAEE,MAAM,CACb,WAAW,CAACC,MAAM,GAChB,KAAK,GACL;MACE;MACA;MACA,yDAAyD,CAACA,MAAM;MAAE;MAClE,QAAQ,CAACA,MAAM;MAAE;MACjB,kBAAkB,CAACA,MAAM,CAC1B,CAACC,IAAI,CAAC,GAAG,CAAC,GACX,GACJ,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBJ,MAAM,EAAE;IACV,CAAC,CACF;IACDK,MAAM,EAAE,CACN;MACEN,OAAO,EAAEE,MAAM,CACb;MACE;MACA,oCAAoC,CAACC,MAAM;MAAE;MAC7C,4DAA4D,CAACA,MAAM;MAAE;MACrE;MACA;MACA,8BAA8B,CAACA,MAAM;MAAE;MACvC;MACA,oBAAoB,CAACA,MAAM;MAAE;MAC7B,yCAAyC,CAACA,MAAM,CACjD,CAACC,IAAI,CAAC,GAAG,CAAC,EACX,GACF,CAAC;MACDH,MAAM,EAAE;IACV,CAAC,EACD;MACED,OAAO,EAAE,8BAA8B;MACvCC,MAAM,EAAE,IAAI;MACZM,KAAK,EAAE;IACT,CAAC,CACF;IACD;IACAC,OAAO,EACL,s0BAAs0B;IACx0BC,MAAM,EAAE;IACN;IACA;IACA,0EAA0E,EAC1E;MACET,OAAO,EACL,0FAA0F;MAC5FK,UAAU,EAAE;IACd,CAAC,CACF;IACDK,QAAQ,EACN;EACJ,CAAC,CAAC;EACFd,KAAK,CAACC,SAAS,CAACc,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE;IAC1C;IACA;IACAC,IAAI,EAAE;EACR,CAAC,CAAC;EACFhB,KAAK,CAACC,SAAS,CAACc,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE;IAC3CE,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFjB,KAAK,CAACC,SAAS,CAACc,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE;IAC5CG,QAAQ,EAAE;MACR;MACAd,OAAO,EACL,qNAAqN;MACvNO,KAAK,EAAE;IACT;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}