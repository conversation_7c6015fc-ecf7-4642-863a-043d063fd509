{"ast": null, "code": "'use strict';\n\nmodule.exports = parse;\nvar search = /[#.]/g;\n\n// Create a hast element from a simple CSS selector.\nfunction parse(selector, defaultTagName) {\n  var value = selector || '';\n  var name = defaultTagName || 'div';\n  var props = {};\n  var start = 0;\n  var subvalue;\n  var previous;\n  var match;\n  while (start < value.length) {\n    search.lastIndex = start;\n    match = search.exec(value);\n    subvalue = value.slice(start, match ? match.index : value.length);\n    if (subvalue) {\n      if (!previous) {\n        name = subvalue;\n      } else if (previous === '#') {\n        props.id = subvalue;\n      } else if (props.className) {\n        props.className.push(subvalue);\n      } else {\n        props.className = [subvalue];\n      }\n      start += subvalue.length;\n    }\n    if (match) {\n      previous = match[0];\n      start++;\n    }\n  }\n  return {\n    type: 'element',\n    tagName: name,\n    properties: props,\n    children: []\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "parse", "search", "selector", "defaultTagName", "value", "name", "props", "start", "subvalue", "previous", "match", "length", "lastIndex", "exec", "slice", "index", "id", "className", "push", "type", "tagName", "properties", "children"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/hast-util-parse-selector/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = parse\n\nvar search = /[#.]/g\n\n// Create a hast element from a simple CSS selector.\nfunction parse(selector, defaultTagName) {\n  var value = selector || ''\n  var name = defaultTagName || 'div'\n  var props = {}\n  var start = 0\n  var subvalue\n  var previous\n  var match\n\n  while (start < value.length) {\n    search.lastIndex = start\n    match = search.exec(value)\n    subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        name = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (props.className) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {type: 'element', tagName: name, properties: props, children: []}\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AAEtB,IAAIC,MAAM,GAAG,OAAO;;AAEpB;AACA,SAASD,KAAKA,CAACE,QAAQ,EAAEC,cAAc,EAAE;EACvC,IAAIC,KAAK,GAAGF,QAAQ,IAAI,EAAE;EAC1B,IAAIG,IAAI,GAAGF,cAAc,IAAI,KAAK;EAClC,IAAIG,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,QAAQ;EACZ,IAAIC,QAAQ;EACZ,IAAIC,KAAK;EAET,OAAOH,KAAK,GAAGH,KAAK,CAACO,MAAM,EAAE;IAC3BV,MAAM,CAACW,SAAS,GAAGL,KAAK;IACxBG,KAAK,GAAGT,MAAM,CAACY,IAAI,CAACT,KAAK,CAAC;IAC1BI,QAAQ,GAAGJ,KAAK,CAACU,KAAK,CAACP,KAAK,EAAEG,KAAK,GAAGA,KAAK,CAACK,KAAK,GAAGX,KAAK,CAACO,MAAM,CAAC;IAEjE,IAAIH,QAAQ,EAAE;MACZ,IAAI,CAACC,QAAQ,EAAE;QACbJ,IAAI,GAAGG,QAAQ;MACjB,CAAC,MAAM,IAAIC,QAAQ,KAAK,GAAG,EAAE;QAC3BH,KAAK,CAACU,EAAE,GAAGR,QAAQ;MACrB,CAAC,MAAM,IAAIF,KAAK,CAACW,SAAS,EAAE;QAC1BX,KAAK,CAACW,SAAS,CAACC,IAAI,CAACV,QAAQ,CAAC;MAChC,CAAC,MAAM;QACLF,KAAK,CAACW,SAAS,GAAG,CAACT,QAAQ,CAAC;MAC9B;MAEAD,KAAK,IAAIC,QAAQ,CAACG,MAAM;IAC1B;IAEA,IAAID,KAAK,EAAE;MACTD,QAAQ,GAAGC,KAAK,CAAC,CAAC,CAAC;MACnBH,KAAK,EAAE;IACT;EACF;EAEA,OAAO;IAACY,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAEf,IAAI;IAAEgB,UAAU,EAAEf,KAAK;IAAEgB,QAAQ,EAAE;EAAE,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}