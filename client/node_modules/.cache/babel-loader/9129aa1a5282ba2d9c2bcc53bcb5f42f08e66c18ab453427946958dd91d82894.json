{"ast": null, "code": "'use strict';\n\nmodule.exports = visualBasic;\nvisualBasic.displayName = 'visualBasic';\nvisualBasic.aliases = [];\nfunction visualBasic(Prism) {\n  Prism.languages['visual-basic'] = {\n    comment: {\n      pattern: /(?:['‘’]|REM\\b)(?:[^\\r\\n_]|_(?:\\r\\n?|\\n)?)*/i,\n      inside: {\n        keyword: /^REM/i\n      }\n    },\n    directive: {\n      pattern: /#(?:Const|Else|ElseIf|End|ExternalChecksum|ExternalSource|If|Region)(?:\\b_[ \\t]*(?:\\r\\n?|\\n)|.)+/i,\n      alias: 'property',\n      greedy: true\n    },\n    string: {\n      pattern: /\\$?[\"“”](?:[\"“”]{2}|[^\"“”])*[\"“”]C?/i,\n      greedy: true\n    },\n    date: {\n      pattern: /#[ \\t]*(?:\\d+([/-])\\d+\\1\\d+(?:[ \\t]+(?:\\d+[ \\t]*(?:AM|PM)|\\d+:\\d+(?::\\d+)?(?:[ \\t]*(?:AM|PM))?))?|\\d+[ \\t]*(?:AM|PM)|\\d+:\\d+(?::\\d+)?(?:[ \\t]*(?:AM|PM))?)[ \\t]*#/i,\n      alias: 'number'\n    },\n    number: /(?:(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)(?:E[+-]?\\d+)?|&[HO][\\dA-F]+)(?:[FRD]|U?[ILS])?/i,\n    boolean: /\\b(?:False|Nothing|True)\\b/i,\n    keyword: /\\b(?:AddHandler|AddressOf|Alias|And(?:Also)?|As|Boolean|ByRef|Byte|ByVal|Call|Case|Catch|C(?:Bool|Byte|Char|Date|Dbl|Dec|Int|Lng|Obj|SByte|Short|Sng|Str|Type|UInt|ULng|UShort)|Char|Class|Const|Continue|Currency|Date|Decimal|Declare|Default|Delegate|Dim|DirectCast|Do|Double|Each|Else(?:If)?|End(?:If)?|Enum|Erase|Error|Event|Exit|Finally|For|Friend|Function|Get(?:Type|XMLNamespace)?|Global|GoSub|GoTo|Handles|If|Implements|Imports|In|Inherits|Integer|Interface|Is|IsNot|Let|Lib|Like|Long|Loop|Me|Mod|Module|Must(?:Inherit|Override)|My(?:Base|Class)|Namespace|Narrowing|New|Next|Not(?:Inheritable|Overridable)?|Object|Of|On|Operator|Option(?:al)?|Or(?:Else)?|Out|Overloads|Overridable|Overrides|ParamArray|Partial|Private|Property|Protected|Public|RaiseEvent|ReadOnly|ReDim|RemoveHandler|Resume|Return|SByte|Select|Set|Shadows|Shared|short|Single|Static|Step|Stop|String|Structure|Sub|SyncLock|Then|Throw|To|Try|TryCast|Type|TypeOf|U(?:Integer|Long|Short)|Until|Using|Variant|Wend|When|While|Widening|With(?:Events)?|WriteOnly|Xor)\\b/i,\n    operator: /[+\\-*/\\\\^<=>&#@$%!]|\\b_(?=[ \\t]*[\\r\\n])/,\n    punctuation: /[{}().,:?]/\n  };\n  Prism.languages.vb = Prism.languages['visual-basic'];\n  Prism.languages.vba = Prism.languages['visual-basic'];\n}", "map": {"version": 3, "names": ["module", "exports", "visualBasic", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "inside", "keyword", "directive", "alias", "greedy", "string", "date", "number", "boolean", "operator", "punctuation", "vb", "vba"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/visual-basic.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = visualBasic\nvisualBasic.displayName = 'visualBasic'\nvisualBasic.aliases = []\nfunction visualBasic(Prism) {\n  Prism.languages['visual-basic'] = {\n    comment: {\n      pattern: /(?:['‘’]|REM\\b)(?:[^\\r\\n_]|_(?:\\r\\n?|\\n)?)*/i,\n      inside: {\n        keyword: /^REM/i\n      }\n    },\n    directive: {\n      pattern:\n        /#(?:Const|Else|ElseIf|End|ExternalChecksum|ExternalSource|If|Region)(?:\\b_[ \\t]*(?:\\r\\n?|\\n)|.)+/i,\n      alias: 'property',\n      greedy: true\n    },\n    string: {\n      pattern: /\\$?[\"“”](?:[\"“”]{2}|[^\"“”])*[\"“”]C?/i,\n      greedy: true\n    },\n    date: {\n      pattern:\n        /#[ \\t]*(?:\\d+([/-])\\d+\\1\\d+(?:[ \\t]+(?:\\d+[ \\t]*(?:AM|PM)|\\d+:\\d+(?::\\d+)?(?:[ \\t]*(?:AM|PM))?))?|\\d+[ \\t]*(?:AM|PM)|\\d+:\\d+(?::\\d+)?(?:[ \\t]*(?:AM|PM))?)[ \\t]*#/i,\n      alias: 'number'\n    },\n    number:\n      /(?:(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)(?:E[+-]?\\d+)?|&[HO][\\dA-F]+)(?:[FRD]|U?[ILS])?/i,\n    boolean: /\\b(?:False|Nothing|True)\\b/i,\n    keyword:\n      /\\b(?:AddHandler|AddressOf|Alias|And(?:Also)?|As|Boolean|ByRef|Byte|ByVal|Call|Case|Catch|C(?:Bool|Byte|Char|Date|Dbl|Dec|Int|Lng|Obj|SByte|Short|Sng|Str|Type|UInt|ULng|UShort)|Char|Class|Const|Continue|Currency|Date|Decimal|Declare|Default|Delegate|Dim|DirectCast|Do|Double|Each|Else(?:If)?|End(?:If)?|Enum|Erase|Error|Event|Exit|Finally|For|Friend|Function|Get(?:Type|XMLNamespace)?|Global|GoSub|GoTo|Handles|If|Implements|Imports|In|Inherits|Integer|Interface|Is|IsNot|Let|Lib|Like|Long|Loop|Me|Mod|Module|Must(?:Inherit|Override)|My(?:Base|Class)|Namespace|Narrowing|New|Next|Not(?:Inheritable|Overridable)?|Object|Of|On|Operator|Option(?:al)?|Or(?:Else)?|Out|Overloads|Overridable|Overrides|ParamArray|Partial|Private|Property|Protected|Public|RaiseEvent|ReadOnly|ReDim|RemoveHandler|Resume|Return|SByte|Select|Set|Shadows|Shared|short|Single|Static|Step|Stop|String|Structure|Sub|SyncLock|Then|Throw|To|Try|TryCast|Type|TypeOf|U(?:Integer|Long|Short)|Until|Using|Variant|Wend|When|While|Widening|With(?:Events)?|WriteOnly|Xor)\\b/i,\n    operator: /[+\\-*/\\\\^<=>&#@$%!]|\\b_(?=[ \\t]*[\\r\\n])/,\n    punctuation: /[{}().,:?]/\n  }\n  Prism.languages.vb = Prism.languages['visual-basic']\n  Prism.languages.vba = Prism.languages['visual-basic']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,WAAW;AAC5BA,WAAW,CAACC,WAAW,GAAG,aAAa;AACvCD,WAAW,CAACE,OAAO,GAAG,EAAE;AACxB,SAASF,WAAWA,CAACG,KAAK,EAAE;EAC1BA,KAAK,CAACC,SAAS,CAAC,cAAc,CAAC,GAAG;IAChCC,OAAO,EAAE;MACPC,OAAO,EAAE,8CAA8C;MACvDC,MAAM,EAAE;QACNC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,OAAO,EACL,mGAAmG;MACrGI,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNN,OAAO,EAAE,sCAAsC;MAC/CK,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJP,OAAO,EACL,oKAAoK;MACtKI,KAAK,EAAE;IACT,CAAC;IACDI,MAAM,EACJ,8EAA8E;IAChFC,OAAO,EAAE,6BAA6B;IACtCP,OAAO,EACL,4gCAA4gC;IAC9gCQ,QAAQ,EAAE,yCAAyC;IACnDC,WAAW,EAAE;EACf,CAAC;EACDd,KAAK,CAACC,SAAS,CAACc,EAAE,GAAGf,KAAK,CAACC,SAAS,CAAC,cAAc,CAAC;EACpDD,KAAK,CAACC,SAAS,CAACe,GAAG,GAAGhB,KAAK,CAACC,SAAS,CAAC,cAAc,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}