{"ast": null, "code": "/*\nLanguage: Backus–Naur Form\nWebsite: https://en.wikipedia.org/wiki/Backus–Naur_form\nAuthor: <PERSON><PERSON> <<EMAIL>>\n*/\n\n/** @type LanguageFn */\nfunction bnf(hljs) {\n  return {\n    name: 'Backus–Naur Form',\n    contains: [\n    // Attribute\n    {\n      className: 'attribute',\n      begin: /</,\n      end: />/\n    },\n    // Specific\n    {\n      begin: /::=/,\n      end: /$/,\n      contains: [{\n        begin: /</,\n        end: />/\n      },\n      // Common\n      hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n    }]\n  };\n}\nmodule.exports = bnf;", "map": {"version": 3, "names": ["bnf", "hljs", "name", "contains", "className", "begin", "end", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/bnf.js"], "sourcesContent": ["/*\nLanguage: Backus–Naur Form\nWebsite: https://en.wikipedia.org/wiki/Backus–Naur_form\nAuthor: <PERSON><PERSON> <<EMAIL>>\n*/\n\n/** @type LanguageFn */\nfunction bnf(hljs) {\n  return {\n    name: 'Backus–Naur Form',\n    contains: [\n      // Attribute\n      {\n        className: 'attribute',\n        begin: /</,\n        end: />/\n      },\n      // Specific\n      {\n        begin: /::=/,\n        end: /$/,\n        contains: [\n          {\n            begin: /</,\n            end: />/\n          },\n          // Common\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.APOS_STRING_MODE,\n          hljs.QUOTE_STRING_MODE\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = bnf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,kBAAkB;IACxBC,QAAQ,EAAE;IACR;IACA;MACEC,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE;IACP,CAAC;IACD;IACA;MACED,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,GAAG;MACRH,QAAQ,EAAE,CACR;QACEE,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE;MACP,CAAC;MACD;MACAL,IAAI,CAACM,mBAAmB,EACxBN,IAAI,CAACO,oBAAoB,EACzBP,IAAI,CAACQ,gBAAgB,EACrBR,IAAI,CAACS,iBAAiB;IAE1B,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGZ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}