{"ast": null, "code": "/*\nLanguage: Dockerfile\nRequires: bash.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: language definition for Dockerfile files\nWebsite: https://docs.docker.com/engine/reference/builder/\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction dockerfile(hljs) {\n  return {\n    name: 'Dockerfile',\n    aliases: ['docker'],\n    case_insensitive: true,\n    keywords: 'from maintainer expose env arg user onbuild stopsignal',\n    contains: [hljs.HASH_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.NUMBER_MODE, {\n      beginKeywords: 'run cmd entrypoint volume add copy workdir label healthcheck shell',\n      starts: {\n        end: /[^\\\\]$/,\n        subLanguage: 'bash'\n      }\n    }],\n    illegal: '</'\n  };\n}\nmodule.exports = dockerfile;", "map": {"version": 3, "names": ["dockerfile", "hljs", "name", "aliases", "case_insensitive", "keywords", "contains", "HASH_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "NUMBER_MODE", "beginKeywords", "starts", "end", "subLanguage", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/dockerfile.js"], "sourcesContent": ["/*\nLanguage: Dockerfile\nRequires: bash.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: language definition for Dockerfile files\nWebsite: https://docs.docker.com/engine/reference/builder/\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction dockerfile(hljs) {\n  return {\n    name: 'Dockerfile',\n    aliases: ['docker'],\n    case_insensitive: true,\n    keywords: 'from maintainer expose env arg user onbuild stopsignal',\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      {\n        beginKeywords: 'run cmd entrypoint volume add copy workdir label healthcheck shell',\n        starts: {\n          end: /[^\\\\]$/,\n          subLanguage: 'bash'\n        }\n      }\n    ],\n    illegal: '</'\n  };\n}\n\nmodule.exports = dockerfile;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO;IACLC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,wDAAwD;IAClEC,QAAQ,EAAE,CACRL,IAAI,CAACM,iBAAiB,EACtBN,IAAI,CAACO,gBAAgB,EACrBP,IAAI,CAACQ,iBAAiB,EACtBR,IAAI,CAACS,WAAW,EAChB;MACEC,aAAa,EAAE,oEAAoE;MACnFC,MAAM,EAAE;QACNC,GAAG,EAAE,QAAQ;QACbC,WAAW,EAAE;MACf;IACF,CAAC,CACF;IACDC,OAAO,EAAE;EACX,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGjB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}