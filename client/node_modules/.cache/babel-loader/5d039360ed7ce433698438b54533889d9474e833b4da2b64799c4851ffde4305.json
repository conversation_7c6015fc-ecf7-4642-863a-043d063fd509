{"ast": null, "code": "'use strict';\n\nmodule.exports = coq;\ncoq.displayName = 'coq';\ncoq.aliases = [];\nfunction coq(Prism) {\n  ;\n  (function (Prism) {\n    // https://github.com/coq/coq\n    var commentSource = /\\(\\*(?:[^(*]|\\((?!\\*)|\\*(?!\\))|<self>)*\\*\\)/.source;\n    for (var i = 0; i < 2; i++) {\n      commentSource = commentSource.replace(/<self>/g, function () {\n        return commentSource;\n      });\n    }\n    commentSource = commentSource.replace(/<self>/g, '[]');\n    Prism.languages.coq = {\n      comment: RegExp(commentSource),\n      string: {\n        pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n        greedy: true\n      },\n      attribute: [{\n        pattern: RegExp(/#\\[(?:[^\\[\\](\"]|\"(?:[^\"]|\"\")*\"(?!\")|\\((?!\\*)|<comment>)*\\]/.source.replace(/<comment>/g, function () {\n          return commentSource;\n        })),\n        greedy: true,\n        alias: 'attr-name',\n        inside: {\n          comment: RegExp(commentSource),\n          string: {\n            pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n            greedy: true\n          },\n          operator: /=/,\n          punctuation: /^#\\[|\\]$|[,()]/\n        }\n      }, {\n        pattern: /\\b(?:Cumulative|Global|Local|Monomorphic|NonCumulative|Polymorphic|Private|Program)\\b/,\n        alias: 'attr-name'\n      }],\n      keyword: /\\b(?:Abort|About|Add|Admit|Admitted|All|Arguments|As|Assumptions|Axiom|Axioms|Back|BackTo|Backtrace|BinOp|BinOpSpec|BinRel|Bind|Blacklist|Canonical|Case|Cd|Check|Class|Classes|Close|CoFixpoint|CoInductive|Coercion|Coercions|Collection|Combined|Compute|Conjecture|Conjectures|Constant|Constants|Constraint|Constructors|Context|Corollary|Create|CstOp|Custom|Cut|Debug|Declare|Defined|Definition|Delimit|Dependencies|Dependent|Derive|Diffs|Drop|Elimination|End|Entry|Equality|Eval|Example|Existential|Existentials|Existing|Export|Extern|Extraction|Fact|Fail|Field|File|Firstorder|Fixpoint|Flags|Focus|From|Funclass|Function|Functional|GC|Generalizable|Goal|Grab|Grammar|Graph|Guarded|Haskell|Heap|Hide|Hint|HintDb|Hints|Hypotheses|Hypothesis|IF|Identity|Immediate|Implicit|Implicits|Import|Include|Induction|Inductive|Infix|Info|Initial|InjTyp|Inline|Inspect|Instance|Instances|Intro|Intros|Inversion|Inversion_clear|JSON|Language|Left|Lemma|Let|Lia|Libraries|Library|Load|LoadPath|Locate|Ltac|Ltac2|ML|Match|Method|Minimality|Module|Modules|Morphism|Next|NoInline|Notation|Number|OCaml|Obligation|Obligations|Opaque|Open|Optimize|Parameter|Parameters|Parametric|Path|Paths|Prenex|Preterm|Primitive|Print|Profile|Projections|Proof|Prop|PropBinOp|PropOp|PropUOp|Property|Proposition|Pwd|Qed|Quit|Rec|Record|Recursive|Redirect|Reduction|Register|Relation|Remark|Remove|Require|Reserved|Reset|Resolve|Restart|Rewrite|Right|Ring|Rings|SProp|Saturate|Save|Scheme|Scope|Scopes|Search|SearchHead|SearchPattern|SearchRewrite|Section|Separate|Set|Setoid|Show|Signatures|Solve|Solver|Sort|Sortclass|Sorted|Spec|Step|Strategies|Strategy|String|Structure|SubClass|Subgraph|SuchThat|Tactic|Term|TestCompile|Theorem|Time|Timeout|To|Transparent|Type|Typeclasses|Types|Typing|UnOp|UnOpSpec|Undelimit|Undo|Unfocus|Unfocused|Unfold|Universe|Universes|Unshelve|Variable|Variables|Variant|Verbose|View|Visibility|Zify|_|apply|as|at|by|cofix|else|end|exists|exists2|fix|for|forall|fun|if|in|let|match|measure|move|removed|return|struct|then|using|wf|where|with)\\b/,\n      number: /\\b(?:0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]+)?(?:p[+-]?\\d[\\d_]*)?|\\d[\\d_]*(?:\\.[\\d_]+)?(?:e[+-]?\\d[\\d_]*)?)\\b/i,\n      punct: {\n        pattern: /@\\{|\\{\\||\\[=|:>/,\n        alias: 'punctuation'\n      },\n      operator: /\\/\\\\|\\\\\\/|\\.{2,3}|:{1,2}=|\\*\\*|[-=]>|<(?:->?|[+:=>]|<:)|>(?:=|->)|\\|[-|]?|[-!%&*+/<=>?@^~']/,\n      punctuation: /\\.\\(|`\\(|@\\{|`\\{|\\{\\||\\[=|:>|[:.,;(){}\\[\\]]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "coq", "displayName", "aliases", "Prism", "commentSource", "source", "i", "replace", "languages", "comment", "RegExp", "string", "pattern", "greedy", "attribute", "alias", "inside", "operator", "punctuation", "keyword", "number", "punct"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/coq.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = coq\ncoq.displayName = 'coq'\ncoq.aliases = []\nfunction coq(Prism) {\n  ;(function (Prism) {\n    // https://github.com/coq/coq\n    var commentSource = /\\(\\*(?:[^(*]|\\((?!\\*)|\\*(?!\\))|<self>)*\\*\\)/.source\n    for (var i = 0; i < 2; i++) {\n      commentSource = commentSource.replace(/<self>/g, function () {\n        return commentSource\n      })\n    }\n    commentSource = commentSource.replace(/<self>/g, '[]')\n    Prism.languages.coq = {\n      comment: RegExp(commentSource),\n      string: {\n        pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n        greedy: true\n      },\n      attribute: [\n        {\n          pattern: RegExp(\n            /#\\[(?:[^\\[\\](\"]|\"(?:[^\"]|\"\")*\"(?!\")|\\((?!\\*)|<comment>)*\\]/.source.replace(\n              /<comment>/g,\n              function () {\n                return commentSource\n              }\n            )\n          ),\n          greedy: true,\n          alias: 'attr-name',\n          inside: {\n            comment: RegExp(commentSource),\n            string: {\n              pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n              greedy: true\n            },\n            operator: /=/,\n            punctuation: /^#\\[|\\]$|[,()]/\n          }\n        },\n        {\n          pattern:\n            /\\b(?:Cumulative|Global|Local|Monomorphic|NonCumulative|Polymorphic|Private|Program)\\b/,\n          alias: 'attr-name'\n        }\n      ],\n      keyword:\n        /\\b(?:Abort|About|Add|Admit|Admitted|All|Arguments|As|Assumptions|Axiom|Axioms|Back|BackTo|Backtrace|BinOp|BinOpSpec|BinRel|Bind|Blacklist|Canonical|Case|Cd|Check|Class|Classes|Close|CoFixpoint|CoInductive|Coercion|Coercions|Collection|Combined|Compute|Conjecture|Conjectures|Constant|Constants|Constraint|Constructors|Context|Corollary|Create|CstOp|Custom|Cut|Debug|Declare|Defined|Definition|Delimit|Dependencies|Dependent|Derive|Diffs|Drop|Elimination|End|Entry|Equality|Eval|Example|Existential|Existentials|Existing|Export|Extern|Extraction|Fact|Fail|Field|File|Firstorder|Fixpoint|Flags|Focus|From|Funclass|Function|Functional|GC|Generalizable|Goal|Grab|Grammar|Graph|Guarded|Haskell|Heap|Hide|Hint|HintDb|Hints|Hypotheses|Hypothesis|IF|Identity|Immediate|Implicit|Implicits|Import|Include|Induction|Inductive|Infix|Info|Initial|InjTyp|Inline|Inspect|Instance|Instances|Intro|Intros|Inversion|Inversion_clear|JSON|Language|Left|Lemma|Let|Lia|Libraries|Library|Load|LoadPath|Locate|Ltac|Ltac2|ML|Match|Method|Minimality|Module|Modules|Morphism|Next|NoInline|Notation|Number|OCaml|Obligation|Obligations|Opaque|Open|Optimize|Parameter|Parameters|Parametric|Path|Paths|Prenex|Preterm|Primitive|Print|Profile|Projections|Proof|Prop|PropBinOp|PropOp|PropUOp|Property|Proposition|Pwd|Qed|Quit|Rec|Record|Recursive|Redirect|Reduction|Register|Relation|Remark|Remove|Require|Reserved|Reset|Resolve|Restart|Rewrite|Right|Ring|Rings|SProp|Saturate|Save|Scheme|Scope|Scopes|Search|SearchHead|SearchPattern|SearchRewrite|Section|Separate|Set|Setoid|Show|Signatures|Solve|Solver|Sort|Sortclass|Sorted|Spec|Step|Strategies|Strategy|String|Structure|SubClass|Subgraph|SuchThat|Tactic|Term|TestCompile|Theorem|Time|Timeout|To|Transparent|Type|Typeclasses|Types|Typing|UnOp|UnOpSpec|Undelimit|Undo|Unfocus|Unfocused|Unfold|Universe|Universes|Unshelve|Variable|Variables|Variant|Verbose|View|Visibility|Zify|_|apply|as|at|by|cofix|else|end|exists|exists2|fix|for|forall|fun|if|in|let|match|measure|move|removed|return|struct|then|using|wf|where|with)\\b/,\n      number:\n        /\\b(?:0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]+)?(?:p[+-]?\\d[\\d_]*)?|\\d[\\d_]*(?:\\.[\\d_]+)?(?:e[+-]?\\d[\\d_]*)?)\\b/i,\n      punct: {\n        pattern: /@\\{|\\{\\||\\[=|:>/,\n        alias: 'punctuation'\n      },\n      operator:\n        /\\/\\\\|\\\\\\/|\\.{2,3}|:{1,2}=|\\*\\*|[-=]>|<(?:->?|[+:=>]|<:)|>(?:=|->)|\\|[-|]?|[-!%&*+/<=>?@^~']/,\n      punctuation: /\\.\\(|`\\(|@\\{|`\\{|\\{\\||\\[=|:>|[:.,;(){}\\[\\]]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA,IAAIC,aAAa,GAAG,6CAA6C,CAACC,MAAM;IACxE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BF,aAAa,GAAGA,aAAa,CAACG,OAAO,CAAC,SAAS,EAAE,YAAY;QAC3D,OAAOH,aAAa;MACtB,CAAC,CAAC;IACJ;IACAA,aAAa,GAAGA,aAAa,CAACG,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;IACtDJ,KAAK,CAACK,SAAS,CAACR,GAAG,GAAG;MACpBS,OAAO,EAAEC,MAAM,CAACN,aAAa,CAAC;MAC9BO,MAAM,EAAE;QACNC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE;MACV,CAAC;MACDC,SAAS,EAAE,CACT;QACEF,OAAO,EAAEF,MAAM,CACb,4DAA4D,CAACL,MAAM,CAACE,OAAO,CACzE,YAAY,EACZ,YAAY;UACV,OAAOH,aAAa;QACtB,CACF,CACF,CAAC;QACDS,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE,WAAW;QAClBC,MAAM,EAAE;UACNP,OAAO,EAAEC,MAAM,CAACN,aAAa,CAAC;UAC9BO,MAAM,EAAE;YACNC,OAAO,EAAE,qBAAqB;YAC9BC,MAAM,EAAE;UACV,CAAC;UACDI,QAAQ,EAAE,GAAG;UACbC,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACEN,OAAO,EACL,uFAAuF;QACzFG,KAAK,EAAE;MACT,CAAC,CACF;MACDI,OAAO,EACL,y/DAAy/D;MAC3/DC,MAAM,EACJ,4GAA4G;MAC9GC,KAAK,EAAE;QACLT,OAAO,EAAE,iBAAiB;QAC1BG,KAAK,EAAE;MACT,CAAC;MACDE,QAAQ,EACN,6FAA6F;MAC/FC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEf,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}