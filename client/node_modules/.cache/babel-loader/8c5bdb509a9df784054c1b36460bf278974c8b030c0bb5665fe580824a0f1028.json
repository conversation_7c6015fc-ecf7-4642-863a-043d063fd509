{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: HTML, XML\nWebsite: https://www.w3.org/XML/\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xml(hljs) {\n  // Element names can contain letters, digits, hyphens, underscores, and periods\n  const TAG_NAME_RE = concat(/[A-Z_]/, optional(/[A-Z0-9_.-]*:/), /[A-Z0-9_.-]*/);\n  const XML_IDENT_RE = /[A-Za-z0-9._:-]+/;\n  const XML_ENTITIES = {\n    className: 'symbol',\n    begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\n  };\n  const XML_META_KEYWORDS = {\n    begin: /\\s/,\n    contains: [{\n      className: 'meta-keyword',\n      begin: /#?[a-z_][a-z1-9_-]+/,\n      illegal: /\\n/\n    }]\n  };\n  const XML_META_PAR_KEYWORDS = hljs.inherit(XML_META_KEYWORDS, {\n    begin: /\\(/,\n    end: /\\)/\n  });\n  const APOS_META_STRING_MODE = hljs.inherit(hljs.APOS_STRING_MODE, {\n    className: 'meta-string'\n  });\n  const QUOTE_META_STRING_MODE = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    className: 'meta-string'\n  });\n  const TAG_INTERNALS = {\n    endsWithParent: true,\n    illegal: /</,\n    relevance: 0,\n    contains: [{\n      className: 'attr',\n      begin: XML_IDENT_RE,\n      relevance: 0\n    }, {\n      begin: /=\\s*/,\n      relevance: 0,\n      contains: [{\n        className: 'string',\n        endsParent: true,\n        variants: [{\n          begin: /\"/,\n          end: /\"/,\n          contains: [XML_ENTITIES]\n        }, {\n          begin: /'/,\n          end: /'/,\n          contains: [XML_ENTITIES]\n        }, {\n          begin: /[^\\s\"'=<>`]+/\n        }]\n      }]\n    }]\n  };\n  return {\n    name: 'HTML, XML',\n    aliases: ['html', 'xhtml', 'rss', 'atom', 'xjb', 'xsd', 'xsl', 'plist', 'wsf', 'svg'],\n    case_insensitive: true,\n    contains: [{\n      className: 'meta',\n      begin: /<![a-z]/,\n      end: />/,\n      relevance: 10,\n      contains: [XML_META_KEYWORDS, QUOTE_META_STRING_MODE, APOS_META_STRING_MODE, XML_META_PAR_KEYWORDS, {\n        begin: /\\[/,\n        end: /\\]/,\n        contains: [{\n          className: 'meta',\n          begin: /<![a-z]/,\n          end: />/,\n          contains: [XML_META_KEYWORDS, XML_META_PAR_KEYWORDS, QUOTE_META_STRING_MODE, APOS_META_STRING_MODE]\n        }]\n      }]\n    }, hljs.COMMENT(/<!--/, /-->/, {\n      relevance: 10\n    }), {\n      begin: /<!\\[CDATA\\[/,\n      end: /\\]\\]>/,\n      relevance: 10\n    }, XML_ENTITIES, {\n      className: 'meta',\n      begin: /<\\?xml/,\n      end: /\\?>/,\n      relevance: 10\n    }, {\n      className: 'tag',\n      /*\n      The lookahead pattern (?=...) ensures that 'begin' only matches\n      '<style' as a single word, followed by a whitespace or an\n      ending braket. The '$' is needed for the lexeme to be recognized\n      by hljs.subMode() that tests lexemes outside the stream.\n      */\n      begin: /<style(?=\\s|>)/,\n      end: />/,\n      keywords: {\n        name: 'style'\n      },\n      contains: [TAG_INTERNALS],\n      starts: {\n        end: /<\\/style>/,\n        returnEnd: true,\n        subLanguage: ['css', 'xml']\n      }\n    }, {\n      className: 'tag',\n      // See the comment in the <style tag about the lookahead pattern\n      begin: /<script(?=\\s|>)/,\n      end: />/,\n      keywords: {\n        name: 'script'\n      },\n      contains: [TAG_INTERNALS],\n      starts: {\n        end: /<\\/script>/,\n        returnEnd: true,\n        subLanguage: ['javascript', 'handlebars', 'xml']\n      }\n    },\n    // we need this for now for jSX\n    {\n      className: 'tag',\n      begin: /<>|<\\/>/\n    },\n    // open tag\n    {\n      className: 'tag',\n      begin: concat(/</, lookahead(concat(TAG_NAME_RE,\n      // <tag/>\n      // <tag>\n      // <tag ...\n      either(/\\/>/, />/, /\\s/)))),\n      end: /\\/?>/,\n      contains: [{\n        className: 'name',\n        begin: TAG_NAME_RE,\n        relevance: 0,\n        starts: TAG_INTERNALS\n      }]\n    },\n    // close tag\n    {\n      className: 'tag',\n      begin: concat(/<\\//, lookahead(concat(TAG_NAME_RE, />/))),\n      contains: [{\n        className: 'name',\n        begin: TAG_NAME_RE,\n        relevance: 0\n      }, {\n        begin: />/,\n        relevance: 0,\n        endsParent: true\n      }]\n    }]\n  };\n}\nmodule.exports = xml;", "map": {"version": 3, "names": ["source", "re", "<PERSON><PERSON><PERSON>", "concat", "optional", "args", "joined", "map", "x", "join", "either", "xml", "hljs", "TAG_NAME_RE", "XML_IDENT_RE", "XML_ENTITIES", "className", "begin", "XML_META_KEYWORDS", "contains", "illegal", "XML_META_PAR_KEYWORDS", "inherit", "end", "APOS_META_STRING_MODE", "APOS_STRING_MODE", "QUOTE_META_STRING_MODE", "QUOTE_STRING_MODE", "TAG_INTERNALS", "endsWithParent", "relevance", "endsParent", "variants", "name", "aliases", "case_insensitive", "COMMENT", "keywords", "starts", "returnEnd", "subLanguage", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/xml.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: HTML, XML\nWebsite: https://www.w3.org/XML/\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xml(hljs) {\n  // Element names can contain letters, digits, hyphens, underscores, and periods\n  const TAG_NAME_RE = concat(/[A-Z_]/, optional(/[A-Z0-9_.-]*:/), /[A-Z0-9_.-]*/);\n  const XML_IDENT_RE = /[A-Za-z0-9._:-]+/;\n  const XML_ENTITIES = {\n    className: 'symbol',\n    begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\n  };\n  const XML_META_KEYWORDS = {\n    begin: /\\s/,\n    contains: [\n      {\n        className: 'meta-keyword',\n        begin: /#?[a-z_][a-z1-9_-]+/,\n        illegal: /\\n/\n      }\n    ]\n  };\n  const XML_META_PAR_KEYWORDS = hljs.inherit(XML_META_KEYWORDS, {\n    begin: /\\(/,\n    end: /\\)/\n  });\n  const APOS_META_STRING_MODE = hljs.inherit(hljs.APOS_STRING_MODE, {\n    className: 'meta-string'\n  });\n  const QUOTE_META_STRING_MODE = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    className: 'meta-string'\n  });\n  const TAG_INTERNALS = {\n    endsWithParent: true,\n    illegal: /</,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: XML_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /=\\s*/,\n        relevance: 0,\n        contains: [\n          {\n            className: 'string',\n            endsParent: true,\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [ XML_ENTITIES ]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [ XML_ENTITIES ]\n              },\n              {\n                begin: /[^\\s\"'=<>`]+/\n              }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n  return {\n    name: 'HTML, XML',\n    aliases: [\n      'html',\n      'xhtml',\n      'rss',\n      'atom',\n      'xjb',\n      'xsd',\n      'xsl',\n      'plist',\n      'wsf',\n      'svg'\n    ],\n    case_insensitive: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: /<![a-z]/,\n        end: />/,\n        relevance: 10,\n        contains: [\n          XML_META_KEYWORDS,\n          QUOTE_META_STRING_MODE,\n          APOS_META_STRING_MODE,\n          XML_META_PAR_KEYWORDS,\n          {\n            begin: /\\[/,\n            end: /\\]/,\n            contains: [\n              {\n                className: 'meta',\n                begin: /<![a-z]/,\n                end: />/,\n                contains: [\n                  XML_META_KEYWORDS,\n                  XML_META_PAR_KEYWORDS,\n                  QUOTE_META_STRING_MODE,\n                  APOS_META_STRING_MODE\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      hljs.COMMENT(\n        /<!--/,\n        /-->/,\n        {\n          relevance: 10\n        }\n      ),\n      {\n        begin: /<!\\[CDATA\\[/,\n        end: /\\]\\]>/,\n        relevance: 10\n      },\n      XML_ENTITIES,\n      {\n        className: 'meta',\n        begin: /<\\?xml/,\n        end: /\\?>/,\n        relevance: 10\n      },\n      {\n        className: 'tag',\n        /*\n        The lookahead pattern (?=...) ensures that 'begin' only matches\n        '<style' as a single word, followed by a whitespace or an\n        ending braket. The '$' is needed for the lexeme to be recognized\n        by hljs.subMode() that tests lexemes outside the stream.\n        */\n        begin: /<style(?=\\s|>)/,\n        end: />/,\n        keywords: {\n          name: 'style'\n        },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/style>/,\n          returnEnd: true,\n          subLanguage: [\n            'css',\n            'xml'\n          ]\n        }\n      },\n      {\n        className: 'tag',\n        // See the comment in the <style tag about the lookahead pattern\n        begin: /<script(?=\\s|>)/,\n        end: />/,\n        keywords: {\n          name: 'script'\n        },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/script>/,\n          returnEnd: true,\n          subLanguage: [\n            'javascript',\n            'handlebars',\n            'xml'\n          ]\n        }\n      },\n      // we need this for now for jSX\n      {\n        className: 'tag',\n        begin: /<>|<\\/>/\n      },\n      // open tag\n      {\n        className: 'tag',\n        begin: concat(\n          /</,\n          lookahead(concat(\n            TAG_NAME_RE,\n            // <tag/>\n            // <tag>\n            // <tag ...\n            either(/\\/>/, />/, /\\s/)\n          ))\n        ),\n        end: /\\/?>/,\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0,\n            starts: TAG_INTERNALS\n          }\n        ]\n      },\n      // close tag\n      {\n        className: 'tag',\n        begin: concat(\n          /<\\//,\n          lookahead(concat(\n            TAG_NAME_RE, />/\n          ))\n        ),\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0\n          },\n          {\n            begin: />/,\n            relevance: 0,\n            endsParent: true\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = xml;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOE,MAAM,CAAC,KAAK,EAAEF,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACH,EAAE,EAAE;EACpB,OAAOE,MAAM,CAAC,GAAG,EAAEF,EAAE,EAAE,IAAI,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGE,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKR,MAAM,CAACQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKR,MAAM,CAACQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASK,GAAGA,CAACC,IAAI,EAAE;EACjB;EACA,MAAMC,WAAW,GAAGV,MAAM,CAAC,QAAQ,EAAEC,QAAQ,CAAC,eAAe,CAAC,EAAE,cAAc,CAAC;EAC/E,MAAMU,YAAY,GAAG,kBAAkB;EACvC,MAAMC,YAAY,GAAG;IACnBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,iBAAiB,GAAG;IACxBD,KAAK,EAAE,IAAI;IACXE,QAAQ,EAAE,CACR;MACEH,SAAS,EAAE,cAAc;MACzBC,KAAK,EAAE,qBAAqB;MAC5BG,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;EACD,MAAMC,qBAAqB,GAAGT,IAAI,CAACU,OAAO,CAACJ,iBAAiB,EAAE;IAC5DD,KAAK,EAAE,IAAI;IACXM,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAMC,qBAAqB,GAAGZ,IAAI,CAACU,OAAO,CAACV,IAAI,CAACa,gBAAgB,EAAE;IAChET,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMU,sBAAsB,GAAGd,IAAI,CAACU,OAAO,CAACV,IAAI,CAACe,iBAAiB,EAAE;IAClEX,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMY,aAAa,GAAG;IACpBC,cAAc,EAAE,IAAI;IACpBT,OAAO,EAAE,GAAG;IACZU,SAAS,EAAE,CAAC;IACZX,QAAQ,EAAE,CACR;MACEH,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAEH,YAAY;MACnBgB,SAAS,EAAE;IACb,CAAC,EACD;MACEb,KAAK,EAAE,MAAM;MACba,SAAS,EAAE,CAAC;MACZX,QAAQ,EAAE,CACR;QACEH,SAAS,EAAE,QAAQ;QACnBe,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE,CACR;UACEf,KAAK,EAAE,GAAG;UACVM,GAAG,EAAE,GAAG;UACRJ,QAAQ,EAAE,CAAEJ,YAAY;QAC1B,CAAC,EACD;UACEE,KAAK,EAAE,GAAG;UACVM,GAAG,EAAE,GAAG;UACRJ,QAAQ,EAAE,CAAEJ,YAAY;QAC1B,CAAC,EACD;UACEE,KAAK,EAAE;QACT,CAAC;MAEL,CAAC;IAEL,CAAC;EAEL,CAAC;EACD,OAAO;IACLgB,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,CACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,CACN;IACDC,gBAAgB,EAAE,IAAI;IACtBhB,QAAQ,EAAE,CACR;MACEH,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,SAAS;MAChBM,GAAG,EAAE,GAAG;MACRO,SAAS,EAAE,EAAE;MACbX,QAAQ,EAAE,CACRD,iBAAiB,EACjBQ,sBAAsB,EACtBF,qBAAqB,EACrBH,qBAAqB,EACrB;QACEJ,KAAK,EAAE,IAAI;QACXM,GAAG,EAAE,IAAI;QACTJ,QAAQ,EAAE,CACR;UACEH,SAAS,EAAE,MAAM;UACjBC,KAAK,EAAE,SAAS;UAChBM,GAAG,EAAE,GAAG;UACRJ,QAAQ,EAAE,CACRD,iBAAiB,EACjBG,qBAAqB,EACrBK,sBAAsB,EACtBF,qBAAqB;QAEzB,CAAC;MAEL,CAAC;IAEL,CAAC,EACDZ,IAAI,CAACwB,OAAO,CACV,MAAM,EACN,KAAK,EACL;MACEN,SAAS,EAAE;IACb,CACF,CAAC,EACD;MACEb,KAAK,EAAE,aAAa;MACpBM,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE;IACb,CAAC,EACDf,YAAY,EACZ;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,QAAQ;MACfM,GAAG,EAAE,KAAK;MACVO,SAAS,EAAE;IACb,CAAC,EACD;MACEd,SAAS,EAAE,KAAK;MAChB;AACR;AACA;AACA;AACA;AACA;MACQC,KAAK,EAAE,gBAAgB;MACvBM,GAAG,EAAE,GAAG;MACRc,QAAQ,EAAE;QACRJ,IAAI,EAAE;MACR,CAAC;MACDd,QAAQ,EAAE,CAAES,aAAa,CAAE;MAC3BU,MAAM,EAAE;QACNf,GAAG,EAAE,WAAW;QAChBgB,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,CACX,KAAK,EACL,KAAK;MAET;IACF,CAAC,EACD;MACExB,SAAS,EAAE,KAAK;MAChB;MACAC,KAAK,EAAE,iBAAiB;MACxBM,GAAG,EAAE,GAAG;MACRc,QAAQ,EAAE;QACRJ,IAAI,EAAE;MACR,CAAC;MACDd,QAAQ,EAAE,CAAES,aAAa,CAAE;MAC3BU,MAAM,EAAE;QACNf,GAAG,EAAE,YAAY;QACjBgB,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,CACX,YAAY,EACZ,YAAY,EACZ,KAAK;MAET;IACF,CAAC;IACD;IACA;MACExB,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACED,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAEd,MAAM,CACX,GAAG,EACHD,SAAS,CAACC,MAAM,CACdU,WAAW;MACX;MACA;MACA;MACAH,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CACzB,CAAC,CACH,CAAC;MACDa,GAAG,EAAE,MAAM;MACXJ,QAAQ,EAAE,CACR;QACEH,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAEJ,WAAW;QAClBiB,SAAS,EAAE,CAAC;QACZQ,MAAM,EAAEV;MACV,CAAC;IAEL,CAAC;IACD;IACA;MACEZ,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAEd,MAAM,CACX,KAAK,EACLD,SAAS,CAACC,MAAM,CACdU,WAAW,EAAE,GACf,CAAC,CACH,CAAC;MACDM,QAAQ,EAAE,CACR;QACEH,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAEJ,WAAW;QAClBiB,SAAS,EAAE;MACb,CAAC,EACD;QACEb,KAAK,EAAE,GAAG;QACVa,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE;MACd,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAU,MAAM,CAACC,OAAO,GAAG/B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}