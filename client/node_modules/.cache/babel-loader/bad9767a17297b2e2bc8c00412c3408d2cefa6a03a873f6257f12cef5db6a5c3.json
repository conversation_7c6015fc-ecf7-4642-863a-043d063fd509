{"ast": null, "code": "'use strict';\n\nmodule.exports = flow;\nflow.displayName = 'flow';\nflow.aliases = [];\nfunction flow(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.flow = Prism.languages.extend('javascript', {});\n    Prism.languages.insertBefore('flow', 'keyword', {\n      type: [{\n        pattern: /\\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|any|mixed|null|void)\\b/,\n        alias: 'tag'\n      }]\n    });\n    Prism.languages.flow['function-variable'].pattern = /(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=\\s*(?:function\\b|(?:\\([^()]*\\)(?:\\s*:\\s*\\w+)?|(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/i;\n    delete Prism.languages.flow['parameter'];\n    Prism.languages.insertBefore('flow', 'operator', {\n      'flow-punctuation': {\n        pattern: /\\{\\||\\|\\}/,\n        alias: 'punctuation'\n      }\n    });\n    if (!Array.isArray(Prism.languages.flow.keyword)) {\n      Prism.languages.flow.keyword = [Prism.languages.flow.keyword];\n    }\n    Prism.languages.flow.keyword.unshift({\n      pattern: /(^|[^$]\\b)(?:Class|declare|opaque|type)\\b(?!\\$)/,\n      lookbehind: true\n    }, {\n      pattern: /(^|[^$]\\B)\\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\\b(?!\\$)/,\n      lookbehind: true\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "flow", "displayName", "aliases", "Prism", "languages", "extend", "insertBefore", "type", "pattern", "alias", "Array", "isArray", "keyword", "unshift", "lookbehind"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/flow.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = flow\nflow.displayName = 'flow'\nflow.aliases = []\nfunction flow(Prism) {\n  ;(function (Prism) {\n    Prism.languages.flow = Prism.languages.extend('javascript', {})\n    Prism.languages.insertBefore('flow', 'keyword', {\n      type: [\n        {\n          pattern:\n            /\\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|any|mixed|null|void)\\b/,\n          alias: 'tag'\n        }\n      ]\n    })\n    Prism.languages.flow['function-variable'].pattern =\n      /(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=\\s*(?:function\\b|(?:\\([^()]*\\)(?:\\s*:\\s*\\w+)?|(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/i\n    delete Prism.languages.flow['parameter']\n    Prism.languages.insertBefore('flow', 'operator', {\n      'flow-punctuation': {\n        pattern: /\\{\\||\\|\\}/,\n        alias: 'punctuation'\n      }\n    })\n    if (!Array.isArray(Prism.languages.flow.keyword)) {\n      Prism.languages.flow.keyword = [Prism.languages.flow.keyword]\n    }\n    Prism.languages.flow.keyword.unshift(\n      {\n        pattern: /(^|[^$]\\b)(?:Class|declare|opaque|type)\\b(?!\\$)/,\n        lookbehind: true\n      },\n      {\n        pattern:\n          /(^|[^$]\\B)\\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\\b(?!\\$)/,\n        lookbehind: true\n      }\n    )\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAC/DF,KAAK,CAACC,SAAS,CAACE,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE;MAC9CC,IAAI,EAAE,CACJ;QACEC,OAAO,EACL,qEAAqE;QACvEC,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,CAAC;IACFN,KAAK,CAACC,SAAS,CAACJ,IAAI,CAAC,mBAAmB,CAAC,CAACQ,OAAO,GAC/C,sKAAsK;IACxK,OAAOL,KAAK,CAACC,SAAS,CAACJ,IAAI,CAAC,WAAW,CAAC;IACxCG,KAAK,CAACC,SAAS,CAACE,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE;MAC/C,kBAAkB,EAAE;QAClBE,OAAO,EAAE,WAAW;QACpBC,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACF,IAAI,CAACC,KAAK,CAACC,OAAO,CAACR,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACY,OAAO,CAAC,EAAE;MAChDT,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACY,OAAO,GAAG,CAACT,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACY,OAAO,CAAC;IAC/D;IACAT,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACY,OAAO,CAACC,OAAO,CAClC;MACEL,OAAO,EAAE,iDAAiD;MAC1DM,UAAU,EAAE;IACd,CAAC,EACD;MACEN,OAAO,EACL,uGAAuG;MACzGM,UAAU,EAAE;IACd,CACF,CAAC;EACH,CAAC,EAAEX,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}