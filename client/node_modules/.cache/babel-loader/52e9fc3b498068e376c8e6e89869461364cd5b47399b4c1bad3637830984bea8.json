{"ast": null, "code": "/*\nLanguage: Stan\nDescription: The Stan probabilistic programming language\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://mc-stan.org/\nCategory: scientific\n*/\n\nfunction stan(hljs) {\n  // variable names cannot conflict with block identifiers\n  const BLOCKS = ['functions', 'model', 'data', 'parameters', 'quantities', 'transformed', 'generated'];\n  const STATEMENTS = ['for', 'in', 'if', 'else', 'while', 'break', 'continue', 'return'];\n  const SPECIAL_FUNCTIONS = ['print', 'reject', 'increment_log_prob|10', 'integrate_ode|10', 'integrate_ode_rk45|10', 'integrate_ode_bdf|10', 'algebra_solver'];\n  const VAR_TYPES = ['int', 'real', 'vector', 'ordered', 'positive_ordered', 'simplex', 'unit_vector', 'row_vector', 'matrix', 'cholesky_factor_corr|10', 'cholesky_factor_cov|10', 'corr_matrix|10', 'cov_matrix|10', 'void'];\n  const FUNCTIONS = ['Phi', 'Phi_approx', 'abs', 'acos', 'acosh', 'algebra_solver', 'append_array', 'append_col', 'append_row', 'asin', 'asinh', 'atan', 'atan2', 'atanh', 'bernoulli_cdf', 'bernoulli_lccdf', 'bernoulli_lcdf', 'bernoulli_logit_lpmf', 'bernoulli_logit_rng', 'bernoulli_lpmf', 'bernoulli_rng', 'bessel_first_kind', 'bessel_second_kind', 'beta_binomial_cdf', 'beta_binomial_lccdf', 'beta_binomial_lcdf', 'beta_binomial_lpmf', 'beta_binomial_rng', 'beta_cdf', 'beta_lccdf', 'beta_lcdf', 'beta_lpdf', 'beta_rng', 'binary_log_loss', 'binomial_cdf', 'binomial_coefficient_log', 'binomial_lccdf', 'binomial_lcdf', 'binomial_logit_lpmf', 'binomial_lpmf', 'binomial_rng', 'block', 'categorical_logit_lpmf', 'categorical_logit_rng', 'categorical_lpmf', 'categorical_rng', 'cauchy_cdf', 'cauchy_lccdf', 'cauchy_lcdf', 'cauchy_lpdf', 'cauchy_rng', 'cbrt', 'ceil', 'chi_square_cdf', 'chi_square_lccdf', 'chi_square_lcdf', 'chi_square_lpdf', 'chi_square_rng', 'cholesky_decompose', 'choose', 'col', 'cols', 'columns_dot_product', 'columns_dot_self', 'cos', 'cosh', 'cov_exp_quad', 'crossprod', 'csr_extract_u', 'csr_extract_v', 'csr_extract_w', 'csr_matrix_times_vector', 'csr_to_dense_matrix', 'cumulative_sum', 'determinant', 'diag_matrix', 'diag_post_multiply', 'diag_pre_multiply', 'diagonal', 'digamma', 'dims', 'dirichlet_lpdf', 'dirichlet_rng', 'distance', 'dot_product', 'dot_self', 'double_exponential_cdf', 'double_exponential_lccdf', 'double_exponential_lcdf', 'double_exponential_lpdf', 'double_exponential_rng', 'e', 'eigenvalues_sym', 'eigenvectors_sym', 'erf', 'erfc', 'exp', 'exp2', 'exp_mod_normal_cdf', 'exp_mod_normal_lccdf', 'exp_mod_normal_lcdf', 'exp_mod_normal_lpdf', 'exp_mod_normal_rng', 'expm1', 'exponential_cdf', 'exponential_lccdf', 'exponential_lcdf', 'exponential_lpdf', 'exponential_rng', 'fabs', 'falling_factorial', 'fdim', 'floor', 'fma', 'fmax', 'fmin', 'fmod', 'frechet_cdf', 'frechet_lccdf', 'frechet_lcdf', 'frechet_lpdf', 'frechet_rng', 'gamma_cdf', 'gamma_lccdf', 'gamma_lcdf', 'gamma_lpdf', 'gamma_p', 'gamma_q', 'gamma_rng', 'gaussian_dlm_obs_lpdf', 'get_lp', 'gumbel_cdf', 'gumbel_lccdf', 'gumbel_lcdf', 'gumbel_lpdf', 'gumbel_rng', 'head', 'hypergeometric_lpmf', 'hypergeometric_rng', 'hypot', 'inc_beta', 'int_step', 'integrate_ode', 'integrate_ode_bdf', 'integrate_ode_rk45', 'inv', 'inv_Phi', 'inv_chi_square_cdf', 'inv_chi_square_lccdf', 'inv_chi_square_lcdf', 'inv_chi_square_lpdf', 'inv_chi_square_rng', 'inv_cloglog', 'inv_gamma_cdf', 'inv_gamma_lccdf', 'inv_gamma_lcdf', 'inv_gamma_lpdf', 'inv_gamma_rng', 'inv_logit', 'inv_sqrt', 'inv_square', 'inv_wishart_lpdf', 'inv_wishart_rng', 'inverse', 'inverse_spd', 'is_inf', 'is_nan', 'lbeta', 'lchoose', 'lgamma', 'lkj_corr_cholesky_lpdf', 'lkj_corr_cholesky_rng', 'lkj_corr_lpdf', 'lkj_corr_rng', 'lmgamma', 'lmultiply', 'log', 'log10', 'log1m', 'log1m_exp', 'log1m_inv_logit', 'log1p', 'log1p_exp', 'log2', 'log_determinant', 'log_diff_exp', 'log_falling_factorial', 'log_inv_logit', 'log_mix', 'log_rising_factorial', 'log_softmax', 'log_sum_exp', 'logistic_cdf', 'logistic_lccdf', 'logistic_lcdf', 'logistic_lpdf', 'logistic_rng', 'logit', 'lognormal_cdf', 'lognormal_lccdf', 'lognormal_lcdf', 'lognormal_lpdf', 'lognormal_rng', 'machine_precision', 'matrix_exp', 'max', 'mdivide_left_spd', 'mdivide_left_tri_low', 'mdivide_right_spd', 'mdivide_right_tri_low', 'mean', 'min', 'modified_bessel_first_kind', 'modified_bessel_second_kind', 'multi_gp_cholesky_lpdf', 'multi_gp_lpdf', 'multi_normal_cholesky_lpdf', 'multi_normal_cholesky_rng', 'multi_normal_lpdf', 'multi_normal_prec_lpdf', 'multi_normal_rng', 'multi_student_t_lpdf', 'multi_student_t_rng', 'multinomial_lpmf', 'multinomial_rng', 'multiply_log', 'multiply_lower_tri_self_transpose', 'neg_binomial_2_cdf', 'neg_binomial_2_lccdf', 'neg_binomial_2_lcdf', 'neg_binomial_2_log_lpmf', 'neg_binomial_2_log_rng', 'neg_binomial_2_lpmf', 'neg_binomial_2_rng', 'neg_binomial_cdf', 'neg_binomial_lccdf', 'neg_binomial_lcdf', 'neg_binomial_lpmf', 'neg_binomial_rng', 'negative_infinity', 'normal_cdf', 'normal_lccdf', 'normal_lcdf', 'normal_lpdf', 'normal_rng', 'not_a_number', 'num_elements', 'ordered_logistic_lpmf', 'ordered_logistic_rng', 'owens_t', 'pareto_cdf', 'pareto_lccdf', 'pareto_lcdf', 'pareto_lpdf', 'pareto_rng', 'pareto_type_2_cdf', 'pareto_type_2_lccdf', 'pareto_type_2_lcdf', 'pareto_type_2_lpdf', 'pareto_type_2_rng', 'pi', 'poisson_cdf', 'poisson_lccdf', 'poisson_lcdf', 'poisson_log_lpmf', 'poisson_log_rng', 'poisson_lpmf', 'poisson_rng', 'positive_infinity', 'pow', 'print', 'prod', 'qr_Q', 'qr_R', 'quad_form', 'quad_form_diag', 'quad_form_sym', 'rank', 'rayleigh_cdf', 'rayleigh_lccdf', 'rayleigh_lcdf', 'rayleigh_lpdf', 'rayleigh_rng', 'reject', 'rep_array', 'rep_matrix', 'rep_row_vector', 'rep_vector', 'rising_factorial', 'round', 'row', 'rows', 'rows_dot_product', 'rows_dot_self', 'scaled_inv_chi_square_cdf', 'scaled_inv_chi_square_lccdf', 'scaled_inv_chi_square_lcdf', 'scaled_inv_chi_square_lpdf', 'scaled_inv_chi_square_rng', 'sd', 'segment', 'sin', 'singular_values', 'sinh', 'size', 'skew_normal_cdf', 'skew_normal_lccdf', 'skew_normal_lcdf', 'skew_normal_lpdf', 'skew_normal_rng', 'softmax', 'sort_asc', 'sort_desc', 'sort_indices_asc', 'sort_indices_desc', 'sqrt', 'sqrt2', 'square', 'squared_distance', 'step', 'student_t_cdf', 'student_t_lccdf', 'student_t_lcdf', 'student_t_lpdf', 'student_t_rng', 'sub_col', 'sub_row', 'sum', 'tail', 'tan', 'tanh', 'target', 'tcrossprod', 'tgamma', 'to_array_1d', 'to_array_2d', 'to_matrix', 'to_row_vector', 'to_vector', 'trace', 'trace_gen_quad_form', 'trace_quad_form', 'trigamma', 'trunc', 'uniform_cdf', 'uniform_lccdf', 'uniform_lcdf', 'uniform_lpdf', 'uniform_rng', 'variance', 'von_mises_lpdf', 'von_mises_rng', 'weibull_cdf', 'weibull_lccdf', 'weibull_lcdf', 'weibull_lpdf', 'weibull_rng', 'wiener_lpdf', 'wishart_lpdf', 'wishart_rng'];\n  const DISTRIBUTIONS = ['bernoulli', 'bernoulli_logit', 'beta', 'beta_binomial', 'binomial', 'binomial_logit', 'categorical', 'categorical_logit', 'cauchy', 'chi_square', 'dirichlet', 'double_exponential', 'exp_mod_normal', 'exponential', 'frechet', 'gamma', 'gaussian_dlm_obs', 'gumbel', 'hypergeometric', 'inv_chi_square', 'inv_gamma', 'inv_wishart', 'lkj_corr', 'lkj_corr_cholesky', 'logistic', 'lognormal', 'multi_gp', 'multi_gp_cholesky', 'multi_normal', 'multi_normal_cholesky', 'multi_normal_prec', 'multi_student_t', 'multinomial', 'neg_binomial', 'neg_binomial_2', 'neg_binomial_2_log', 'normal', 'ordered_logistic', 'pareto', 'pareto_type_2', 'poisson', 'poisson_log', 'rayleigh', 'scaled_inv_chi_square', 'skew_normal', 'student_t', 'uniform', 'von_mises', 'weibull', 'wiener', 'wishart'];\n  return {\n    name: 'Stan',\n    aliases: ['stanfuncs'],\n    keywords: {\n      $pattern: hljs.IDENT_RE,\n      title: BLOCKS,\n      keyword: STATEMENTS.concat(VAR_TYPES).concat(SPECIAL_FUNCTIONS),\n      built_in: FUNCTIONS\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.COMMENT(/#/, /$/, {\n      relevance: 0,\n      keywords: {\n        'meta-keyword': 'include'\n      }\n    }), hljs.COMMENT(/\\/\\*/, /\\*\\//, {\n      relevance: 0,\n      // highlight doc strings mentioned in Stan reference\n      contains: [{\n        className: 'doctag',\n        begin: /@(return|param)/\n      }]\n    }), {\n      // hack: in range constraints, lower must follow \"<\"\n      begin: /<\\s*lower\\s*=/,\n      keywords: 'lower'\n    }, {\n      // hack: in range constraints, upper must follow either , or <\n      // <lower = ..., upper = ...> or <upper = ...>\n      begin: /[<,]\\s*upper\\s*=/,\n      keywords: 'upper'\n    }, {\n      className: 'keyword',\n      begin: /\\btarget\\s*\\+=/,\n      relevance: 10\n    }, {\n      begin: '~\\\\s*(' + hljs.IDENT_RE + ')\\\\s*\\\\(',\n      keywords: DISTRIBUTIONS\n    }, {\n      className: 'number',\n      variants: [{\n        begin: /\\b\\d+(?:\\.\\d*)?(?:[eE][+-]?\\d+)?/\n      }, {\n        begin: /\\.\\d+(?:[eE][+-]?\\d+)?\\b/\n      }],\n      relevance: 0\n    }, {\n      className: 'string',\n      begin: '\"',\n      end: '\"',\n      relevance: 0\n    }]\n  };\n}\nmodule.exports = stan;", "map": {"version": 3, "names": ["stan", "hljs", "BLOCKS", "STATEMENTS", "SPECIAL_FUNCTIONS", "VAR_TYPES", "FUNCTIONS", "DISTRIBUTIONS", "name", "aliases", "keywords", "$pattern", "IDENT_RE", "title", "keyword", "concat", "built_in", "contains", "C_LINE_COMMENT_MODE", "COMMENT", "relevance", "className", "begin", "variants", "end", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/stan.js"], "sourcesContent": ["/*\nLanguage: Stan\nDescription: The Stan probabilistic programming language\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://mc-stan.org/\nCategory: scientific\n*/\n\nfunction stan(hljs) {\n  // variable names cannot conflict with block identifiers\n  const BLOCKS = [\n    'functions',\n    'model',\n    'data',\n    'parameters',\n    'quantities',\n    'transformed',\n    'generated'\n  ];\n  const STATEMENTS = [\n    'for',\n    'in',\n    'if',\n    'else',\n    'while',\n    'break',\n    'continue',\n    'return'\n  ];\n  const SPECIAL_FUNCTIONS = [\n    'print',\n    'reject',\n    'increment_log_prob|10',\n    'integrate_ode|10',\n    'integrate_ode_rk45|10',\n    'integrate_ode_bdf|10',\n    'algebra_solver'\n  ];\n  const VAR_TYPES = [\n    'int',\n    'real',\n    'vector',\n    'ordered',\n    'positive_ordered',\n    'simplex',\n    'unit_vector',\n    'row_vector',\n    'matrix',\n    'cholesky_factor_corr|10',\n    'cholesky_factor_cov|10',\n    'corr_matrix|10',\n    'cov_matrix|10',\n    'void'\n  ];\n  const FUNCTIONS = [\n    'Phi',\n    'Phi_approx',\n    'abs',\n    'acos',\n    'acosh',\n    'algebra_solver',\n    'append_array',\n    'append_col',\n    'append_row',\n    'asin',\n    'asinh',\n    'atan',\n    'atan2',\n    'atanh',\n    'bernoulli_cdf',\n    'bernoulli_lccdf',\n    'bernoulli_lcdf',\n    'bernoulli_logit_lpmf',\n    'bernoulli_logit_rng',\n    'bernoulli_lpmf',\n    'bernoulli_rng',\n    'bessel_first_kind',\n    'bessel_second_kind',\n    'beta_binomial_cdf',\n    'beta_binomial_lccdf',\n    'beta_binomial_lcdf',\n    'beta_binomial_lpmf',\n    'beta_binomial_rng',\n    'beta_cdf',\n    'beta_lccdf',\n    'beta_lcdf',\n    'beta_lpdf',\n    'beta_rng',\n    'binary_log_loss',\n    'binomial_cdf',\n    'binomial_coefficient_log',\n    'binomial_lccdf',\n    'binomial_lcdf',\n    'binomial_logit_lpmf',\n    'binomial_lpmf',\n    'binomial_rng',\n    'block',\n    'categorical_logit_lpmf',\n    'categorical_logit_rng',\n    'categorical_lpmf',\n    'categorical_rng',\n    'cauchy_cdf',\n    'cauchy_lccdf',\n    'cauchy_lcdf',\n    'cauchy_lpdf',\n    'cauchy_rng',\n    'cbrt',\n    'ceil',\n    'chi_square_cdf',\n    'chi_square_lccdf',\n    'chi_square_lcdf',\n    'chi_square_lpdf',\n    'chi_square_rng',\n    'cholesky_decompose',\n    'choose',\n    'col',\n    'cols',\n    'columns_dot_product',\n    'columns_dot_self',\n    'cos',\n    'cosh',\n    'cov_exp_quad',\n    'crossprod',\n    'csr_extract_u',\n    'csr_extract_v',\n    'csr_extract_w',\n    'csr_matrix_times_vector',\n    'csr_to_dense_matrix',\n    'cumulative_sum',\n    'determinant',\n    'diag_matrix',\n    'diag_post_multiply',\n    'diag_pre_multiply',\n    'diagonal',\n    'digamma',\n    'dims',\n    'dirichlet_lpdf',\n    'dirichlet_rng',\n    'distance',\n    'dot_product',\n    'dot_self',\n    'double_exponential_cdf',\n    'double_exponential_lccdf',\n    'double_exponential_lcdf',\n    'double_exponential_lpdf',\n    'double_exponential_rng',\n    'e',\n    'eigenvalues_sym',\n    'eigenvectors_sym',\n    'erf',\n    'erfc',\n    'exp',\n    'exp2',\n    'exp_mod_normal_cdf',\n    'exp_mod_normal_lccdf',\n    'exp_mod_normal_lcdf',\n    'exp_mod_normal_lpdf',\n    'exp_mod_normal_rng',\n    'expm1',\n    'exponential_cdf',\n    'exponential_lccdf',\n    'exponential_lcdf',\n    'exponential_lpdf',\n    'exponential_rng',\n    'fabs',\n    'falling_factorial',\n    'fdim',\n    'floor',\n    'fma',\n    'fmax',\n    'fmin',\n    'fmod',\n    'frechet_cdf',\n    'frechet_lccdf',\n    'frechet_lcdf',\n    'frechet_lpdf',\n    'frechet_rng',\n    'gamma_cdf',\n    'gamma_lccdf',\n    'gamma_lcdf',\n    'gamma_lpdf',\n    'gamma_p',\n    'gamma_q',\n    'gamma_rng',\n    'gaussian_dlm_obs_lpdf',\n    'get_lp',\n    'gumbel_cdf',\n    'gumbel_lccdf',\n    'gumbel_lcdf',\n    'gumbel_lpdf',\n    'gumbel_rng',\n    'head',\n    'hypergeometric_lpmf',\n    'hypergeometric_rng',\n    'hypot',\n    'inc_beta',\n    'int_step',\n    'integrate_ode',\n    'integrate_ode_bdf',\n    'integrate_ode_rk45',\n    'inv',\n    'inv_Phi',\n    'inv_chi_square_cdf',\n    'inv_chi_square_lccdf',\n    'inv_chi_square_lcdf',\n    'inv_chi_square_lpdf',\n    'inv_chi_square_rng',\n    'inv_cloglog',\n    'inv_gamma_cdf',\n    'inv_gamma_lccdf',\n    'inv_gamma_lcdf',\n    'inv_gamma_lpdf',\n    'inv_gamma_rng',\n    'inv_logit',\n    'inv_sqrt',\n    'inv_square',\n    'inv_wishart_lpdf',\n    'inv_wishart_rng',\n    'inverse',\n    'inverse_spd',\n    'is_inf',\n    'is_nan',\n    'lbeta',\n    'lchoose',\n    'lgamma',\n    'lkj_corr_cholesky_lpdf',\n    'lkj_corr_cholesky_rng',\n    'lkj_corr_lpdf',\n    'lkj_corr_rng',\n    'lmgamma',\n    'lmultiply',\n    'log',\n    'log10',\n    'log1m',\n    'log1m_exp',\n    'log1m_inv_logit',\n    'log1p',\n    'log1p_exp',\n    'log2',\n    'log_determinant',\n    'log_diff_exp',\n    'log_falling_factorial',\n    'log_inv_logit',\n    'log_mix',\n    'log_rising_factorial',\n    'log_softmax',\n    'log_sum_exp',\n    'logistic_cdf',\n    'logistic_lccdf',\n    'logistic_lcdf',\n    'logistic_lpdf',\n    'logistic_rng',\n    'logit',\n    'lognormal_cdf',\n    'lognormal_lccdf',\n    'lognormal_lcdf',\n    'lognormal_lpdf',\n    'lognormal_rng',\n    'machine_precision',\n    'matrix_exp',\n    'max',\n    'mdivide_left_spd',\n    'mdivide_left_tri_low',\n    'mdivide_right_spd',\n    'mdivide_right_tri_low',\n    'mean',\n    'min',\n    'modified_bessel_first_kind',\n    'modified_bessel_second_kind',\n    'multi_gp_cholesky_lpdf',\n    'multi_gp_lpdf',\n    'multi_normal_cholesky_lpdf',\n    'multi_normal_cholesky_rng',\n    'multi_normal_lpdf',\n    'multi_normal_prec_lpdf',\n    'multi_normal_rng',\n    'multi_student_t_lpdf',\n    'multi_student_t_rng',\n    'multinomial_lpmf',\n    'multinomial_rng',\n    'multiply_log',\n    'multiply_lower_tri_self_transpose',\n    'neg_binomial_2_cdf',\n    'neg_binomial_2_lccdf',\n    'neg_binomial_2_lcdf',\n    'neg_binomial_2_log_lpmf',\n    'neg_binomial_2_log_rng',\n    'neg_binomial_2_lpmf',\n    'neg_binomial_2_rng',\n    'neg_binomial_cdf',\n    'neg_binomial_lccdf',\n    'neg_binomial_lcdf',\n    'neg_binomial_lpmf',\n    'neg_binomial_rng',\n    'negative_infinity',\n    'normal_cdf',\n    'normal_lccdf',\n    'normal_lcdf',\n    'normal_lpdf',\n    'normal_rng',\n    'not_a_number',\n    'num_elements',\n    'ordered_logistic_lpmf',\n    'ordered_logistic_rng',\n    'owens_t',\n    'pareto_cdf',\n    'pareto_lccdf',\n    'pareto_lcdf',\n    'pareto_lpdf',\n    'pareto_rng',\n    'pareto_type_2_cdf',\n    'pareto_type_2_lccdf',\n    'pareto_type_2_lcdf',\n    'pareto_type_2_lpdf',\n    'pareto_type_2_rng',\n    'pi',\n    'poisson_cdf',\n    'poisson_lccdf',\n    'poisson_lcdf',\n    'poisson_log_lpmf',\n    'poisson_log_rng',\n    'poisson_lpmf',\n    'poisson_rng',\n    'positive_infinity',\n    'pow',\n    'print',\n    'prod',\n    'qr_Q',\n    'qr_R',\n    'quad_form',\n    'quad_form_diag',\n    'quad_form_sym',\n    'rank',\n    'rayleigh_cdf',\n    'rayleigh_lccdf',\n    'rayleigh_lcdf',\n    'rayleigh_lpdf',\n    'rayleigh_rng',\n    'reject',\n    'rep_array',\n    'rep_matrix',\n    'rep_row_vector',\n    'rep_vector',\n    'rising_factorial',\n    'round',\n    'row',\n    'rows',\n    'rows_dot_product',\n    'rows_dot_self',\n    'scaled_inv_chi_square_cdf',\n    'scaled_inv_chi_square_lccdf',\n    'scaled_inv_chi_square_lcdf',\n    'scaled_inv_chi_square_lpdf',\n    'scaled_inv_chi_square_rng',\n    'sd',\n    'segment',\n    'sin',\n    'singular_values',\n    'sinh',\n    'size',\n    'skew_normal_cdf',\n    'skew_normal_lccdf',\n    'skew_normal_lcdf',\n    'skew_normal_lpdf',\n    'skew_normal_rng',\n    'softmax',\n    'sort_asc',\n    'sort_desc',\n    'sort_indices_asc',\n    'sort_indices_desc',\n    'sqrt',\n    'sqrt2',\n    'square',\n    'squared_distance',\n    'step',\n    'student_t_cdf',\n    'student_t_lccdf',\n    'student_t_lcdf',\n    'student_t_lpdf',\n    'student_t_rng',\n    'sub_col',\n    'sub_row',\n    'sum',\n    'tail',\n    'tan',\n    'tanh',\n    'target',\n    'tcrossprod',\n    'tgamma',\n    'to_array_1d',\n    'to_array_2d',\n    'to_matrix',\n    'to_row_vector',\n    'to_vector',\n    'trace',\n    'trace_gen_quad_form',\n    'trace_quad_form',\n    'trigamma',\n    'trunc',\n    'uniform_cdf',\n    'uniform_lccdf',\n    'uniform_lcdf',\n    'uniform_lpdf',\n    'uniform_rng',\n    'variance',\n    'von_mises_lpdf',\n    'von_mises_rng',\n    'weibull_cdf',\n    'weibull_lccdf',\n    'weibull_lcdf',\n    'weibull_lpdf',\n    'weibull_rng',\n    'wiener_lpdf',\n    'wishart_lpdf',\n    'wishart_rng'\n  ];\n  const DISTRIBUTIONS = [\n    'bernoulli',\n    'bernoulli_logit',\n    'beta',\n    'beta_binomial',\n    'binomial',\n    'binomial_logit',\n    'categorical',\n    'categorical_logit',\n    'cauchy',\n    'chi_square',\n    'dirichlet',\n    'double_exponential',\n    'exp_mod_normal',\n    'exponential',\n    'frechet',\n    'gamma',\n    'gaussian_dlm_obs',\n    'gumbel',\n    'hypergeometric',\n    'inv_chi_square',\n    'inv_gamma',\n    'inv_wishart',\n    'lkj_corr',\n    'lkj_corr_cholesky',\n    'logistic',\n    'lognormal',\n    'multi_gp',\n    'multi_gp_cholesky',\n    'multi_normal',\n    'multi_normal_cholesky',\n    'multi_normal_prec',\n    'multi_student_t',\n    'multinomial',\n    'neg_binomial',\n    'neg_binomial_2',\n    'neg_binomial_2_log',\n    'normal',\n    'ordered_logistic',\n    'pareto',\n    'pareto_type_2',\n    'poisson',\n    'poisson_log',\n    'rayleigh',\n    'scaled_inv_chi_square',\n    'skew_normal',\n    'student_t',\n    'uniform',\n    'von_mises',\n    'weibull',\n    'wiener',\n    'wishart'\n  ];\n\n  return {\n    name: 'Stan',\n    aliases: [ 'stanfuncs' ],\n    keywords: {\n      $pattern: hljs.IDENT_RE,\n      title: BLOCKS,\n      keyword: STATEMENTS.concat(VAR_TYPES).concat(SPECIAL_FUNCTIONS),\n      built_in: FUNCTIONS\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT(\n        /#/,\n        /$/,\n        {\n          relevance: 0,\n          keywords: {\n            'meta-keyword': 'include'\n          }\n        }\n      ),\n      hljs.COMMENT(\n        /\\/\\*/,\n        /\\*\\//,\n        {\n          relevance: 0,\n          // highlight doc strings mentioned in Stan reference\n          contains: [\n            {\n              className: 'doctag',\n              begin: /@(return|param)/\n            }\n          ]\n        }\n      ),\n      {\n        // hack: in range constraints, lower must follow \"<\"\n        begin: /<\\s*lower\\s*=/,\n        keywords: 'lower'\n      },\n      {\n        // hack: in range constraints, upper must follow either , or <\n        // <lower = ..., upper = ...> or <upper = ...>\n        begin: /[<,]\\s*upper\\s*=/,\n        keywords: 'upper'\n      },\n      {\n        className: 'keyword',\n        begin: /\\btarget\\s*\\+=/,\n        relevance: 10\n      },\n      {\n        begin: '~\\\\s*(' + hljs.IDENT_RE + ')\\\\s*\\\\(',\n        keywords: DISTRIBUTIONS\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: /\\b\\d+(?:\\.\\d*)?(?:[eE][+-]?\\d+)?/\n          },\n          {\n            begin: /\\.\\d+(?:[eE][+-]?\\d+)?\\b/\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = stan;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB;EACA,MAAMC,MAAM,GAAG,CACb,WAAW,EACX,OAAO,EACP,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,WAAW,CACZ;EACD,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,CACT;EACD,MAAMC,iBAAiB,GAAG,CACxB,OAAO,EACP,QAAQ,EACR,uBAAuB,EACvB,kBAAkB,EAClB,uBAAuB,EACvB,sBAAsB,EACtB,gBAAgB,CACjB;EACD,MAAMC,SAAS,GAAG,CAChB,KAAK,EACL,MAAM,EACN,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,SAAS,EACT,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,yBAAyB,EACzB,wBAAwB,EACxB,gBAAgB,EAChB,eAAe,EACf,MAAM,CACP;EACD,MAAMC,SAAS,GAAG,CAChB,KAAK,EACL,YAAY,EACZ,KAAK,EACL,MAAM,EACN,OAAO,EACP,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,sBAAsB,EACtB,qBAAqB,EACrB,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,qBAAqB,EACrB,oBAAoB,EACpB,oBAAoB,EACpB,mBAAmB,EACnB,UAAU,EACV,YAAY,EACZ,WAAW,EACX,WAAW,EACX,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,0BAA0B,EAC1B,gBAAgB,EAChB,eAAe,EACf,qBAAqB,EACrB,eAAe,EACf,cAAc,EACd,OAAO,EACP,wBAAwB,EACxB,uBAAuB,EACvB,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,cAAc,EACd,aAAa,EACb,aAAa,EACb,YAAY,EACZ,MAAM,EACN,MAAM,EACN,gBAAgB,EAChB,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,EACjB,gBAAgB,EAChB,oBAAoB,EACpB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,qBAAqB,EACrB,kBAAkB,EAClB,KAAK,EACL,MAAM,EACN,cAAc,EACd,WAAW,EACX,eAAe,EACf,eAAe,EACf,eAAe,EACf,yBAAyB,EACzB,qBAAqB,EACrB,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,oBAAoB,EACpB,mBAAmB,EACnB,UAAU,EACV,SAAS,EACT,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,UAAU,EACV,aAAa,EACb,UAAU,EACV,wBAAwB,EACxB,0BAA0B,EAC1B,yBAAyB,EACzB,yBAAyB,EACzB,wBAAwB,EACxB,GAAG,EACH,iBAAiB,EACjB,kBAAkB,EAClB,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,oBAAoB,EACpB,sBAAsB,EACtB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,OAAO,EACP,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,iBAAiB,EACjB,MAAM,EACN,mBAAmB,EACnB,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,aAAa,EACb,eAAe,EACf,cAAc,EACd,cAAc,EACd,aAAa,EACb,WAAW,EACX,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,SAAS,EACT,WAAW,EACX,uBAAuB,EACvB,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,aAAa,EACb,aAAa,EACb,YAAY,EACZ,MAAM,EACN,qBAAqB,EACrB,oBAAoB,EACpB,OAAO,EACP,UAAU,EACV,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACpB,KAAK,EACL,SAAS,EACT,oBAAoB,EACpB,sBAAsB,EACtB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,aAAa,EACb,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,UAAU,EACV,YAAY,EACZ,kBAAkB,EAClB,iBAAiB,EACjB,SAAS,EACT,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,SAAS,EACT,QAAQ,EACR,wBAAwB,EACxB,uBAAuB,EACvB,eAAe,EACf,cAAc,EACd,SAAS,EACT,WAAW,EACX,KAAK,EACL,OAAO,EACP,OAAO,EACP,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,WAAW,EACX,MAAM,EACN,iBAAiB,EACjB,cAAc,EACd,uBAAuB,EACvB,eAAe,EACf,SAAS,EACT,sBAAsB,EACtB,aAAa,EACb,aAAa,EACb,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,cAAc,EACd,OAAO,EACP,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,YAAY,EACZ,KAAK,EACL,kBAAkB,EAClB,sBAAsB,EACtB,mBAAmB,EACnB,uBAAuB,EACvB,MAAM,EACN,KAAK,EACL,4BAA4B,EAC5B,6BAA6B,EAC7B,wBAAwB,EACxB,eAAe,EACf,4BAA4B,EAC5B,2BAA2B,EAC3B,mBAAmB,EACnB,wBAAwB,EACxB,kBAAkB,EAClB,sBAAsB,EACtB,qBAAqB,EACrB,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACd,mCAAmC,EACnC,oBAAoB,EACpB,sBAAsB,EACtB,qBAAqB,EACrB,yBAAyB,EACzB,wBAAwB,EACxB,qBAAqB,EACrB,oBAAoB,EACpB,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,YAAY,EACZ,cAAc,EACd,aAAa,EACb,aAAa,EACb,YAAY,EACZ,cAAc,EACd,cAAc,EACd,uBAAuB,EACvB,sBAAsB,EACtB,SAAS,EACT,YAAY,EACZ,cAAc,EACd,aAAa,EACb,aAAa,EACb,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,EACrB,oBAAoB,EACpB,oBAAoB,EACpB,mBAAmB,EACnB,IAAI,EACJ,aAAa,EACb,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACd,aAAa,EACb,mBAAmB,EACnB,KAAK,EACL,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,WAAW,EACX,gBAAgB,EAChB,eAAe,EACf,MAAM,EACN,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,cAAc,EACd,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,kBAAkB,EAClB,OAAO,EACP,KAAK,EACL,MAAM,EACN,kBAAkB,EAClB,eAAe,EACf,2BAA2B,EAC3B,6BAA6B,EAC7B,4BAA4B,EAC5B,4BAA4B,EAC5B,2BAA2B,EAC3B,IAAI,EACJ,SAAS,EACT,KAAK,EACL,iBAAiB,EACjB,MAAM,EACN,MAAM,EACN,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,iBAAiB,EACjB,SAAS,EACT,UAAU,EACV,WAAW,EACX,kBAAkB,EAClB,mBAAmB,EACnB,MAAM,EACN,OAAO,EACP,QAAQ,EACR,kBAAkB,EAClB,MAAM,EACN,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,SAAS,EACT,SAAS,EACT,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,aAAa,EACb,WAAW,EACX,eAAe,EACf,WAAW,EACX,OAAO,EACP,qBAAqB,EACrB,iBAAiB,EACjB,UAAU,EACV,OAAO,EACP,aAAa,EACb,eAAe,EACf,cAAc,EACd,cAAc,EACd,aAAa,EACb,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,eAAe,EACf,cAAc,EACd,cAAc,EACd,aAAa,EACb,aAAa,EACb,cAAc,EACd,aAAa,CACd;EACD,MAAMC,aAAa,GAAG,CACpB,WAAW,EACX,iBAAiB,EACjB,MAAM,EACN,eAAe,EACf,UAAU,EACV,gBAAgB,EAChB,aAAa,EACb,mBAAmB,EACnB,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,oBAAoB,EACpB,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,OAAO,EACP,kBAAkB,EAClB,QAAQ,EACR,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,aAAa,EACb,UAAU,EACV,mBAAmB,EACnB,UAAU,EACV,WAAW,EACX,UAAU,EACV,mBAAmB,EACnB,cAAc,EACd,uBAAuB,EACvB,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,EACb,cAAc,EACd,gBAAgB,EAChB,oBAAoB,EACpB,QAAQ,EACR,kBAAkB,EAClB,QAAQ,EACR,eAAe,EACf,SAAS,EACT,aAAa,EACb,UAAU,EACV,uBAAuB,EACvB,aAAa,EACb,WAAW,EACX,SAAS,EACT,WAAW,EACX,SAAS,EACT,QAAQ,EACR,SAAS,CACV;EAED,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAE,WAAW,CAAE;IACxBC,QAAQ,EAAE;MACRC,QAAQ,EAAEV,IAAI,CAACW,QAAQ;MACvBC,KAAK,EAAEX,MAAM;MACbY,OAAO,EAAEX,UAAU,CAACY,MAAM,CAACV,SAAS,CAAC,CAACU,MAAM,CAACX,iBAAiB,CAAC;MAC/DY,QAAQ,EAAEV;IACZ,CAAC;IACDW,QAAQ,EAAE,CACRhB,IAAI,CAACiB,mBAAmB,EACxBjB,IAAI,CAACkB,OAAO,CACV,GAAG,EACH,GAAG,EACH;MACEC,SAAS,EAAE,CAAC;MACZV,QAAQ,EAAE;QACR,cAAc,EAAE;MAClB;IACF,CACF,CAAC,EACDT,IAAI,CAACkB,OAAO,CACV,MAAM,EACN,MAAM,EACN;MACEC,SAAS,EAAE,CAAC;MACZ;MACAH,QAAQ,EAAE,CACR;QACEI,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAC;IAEL,CACF,CAAC,EACD;MACE;MACAA,KAAK,EAAE,eAAe;MACtBZ,QAAQ,EAAE;IACZ,CAAC,EACD;MACE;MACA;MACAY,KAAK,EAAE,kBAAkB;MACzBZ,QAAQ,EAAE;IACZ,CAAC,EACD;MACEW,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,gBAAgB;MACvBF,SAAS,EAAE;IACb,CAAC,EACD;MACEE,KAAK,EAAE,QAAQ,GAAGrB,IAAI,CAACW,QAAQ,GAAG,UAAU;MAC5CF,QAAQ,EAAEH;IACZ,CAAC,EACD;MACEc,SAAS,EAAE,QAAQ;MACnBE,QAAQ,EAAE,CACR;QACED,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC,CACF;MACDF,SAAS,EAAE;IACb,CAAC,EACD;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MACVE,GAAG,EAAE,GAAG;MACRJ,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAG1B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}