{"ast": null, "code": "'use strict';\n\nvar refractorCsharp = require('./csharp.js');\nmodule.exports = aspnet;\naspnet.displayName = 'aspnet';\naspnet.aliases = [];\nfunction aspnet(Prism) {\n  Prism.register(refractorCsharp);\n  Prism.languages.aspnet = Prism.languages.extend('markup', {\n    'page-directive': {\n      pattern: /<%\\s*@.*%>/,\n      alias: 'tag',\n      inside: {\n        'page-directive': {\n          pattern: /<%\\s*@\\s*(?:Assembly|Control|Implements|Import|Master(?:Type)?|OutputCache|Page|PreviousPageType|Reference|Register)?|%>/i,\n          alias: 'tag'\n        },\n        rest: Prism.languages.markup.tag.inside\n      }\n    },\n    directive: {\n      pattern: /<%.*%>/,\n      alias: 'tag',\n      inside: {\n        directive: {\n          pattern: /<%\\s*?[$=%#:]{0,2}|%>/,\n          alias: 'tag'\n        },\n        rest: Prism.languages.csharp\n      }\n    }\n  }); // Regexp copied from prism-markup, with a negative look-ahead added\n  Prism.languages.aspnet.tag.pattern = /<(?!%)\\/?[^\\s>\\/]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/; // match directives of attribute value foo=\"<% Bar %>\"\n  Prism.languages.insertBefore('inside', 'punctuation', {\n    directive: Prism.languages.aspnet['directive']\n  }, Prism.languages.aspnet.tag.inside['attr-value']);\n  Prism.languages.insertBefore('aspnet', 'comment', {\n    'asp-comment': {\n      pattern: /<%--[\\s\\S]*?--%>/,\n      alias: ['asp', 'comment']\n    }\n  }); // script runat=\"server\" contains csharp, not javascript\n  Prism.languages.insertBefore('aspnet', Prism.languages.javascript ? 'script' : 'tag', {\n    'asp-script': {\n      pattern: /(<script(?=.*runat=['\"]?server\\b)[^>]*>)[\\s\\S]*?(?=<\\/script>)/i,\n      lookbehind: true,\n      alias: ['asp', 'script'],\n      inside: Prism.languages.csharp || {}\n    }\n  });\n}", "map": {"version": 3, "names": ["refractorCsharp", "require", "module", "exports", "aspnet", "displayName", "aliases", "Prism", "register", "languages", "extend", "pattern", "alias", "inside", "rest", "markup", "tag", "directive", "csharp", "insertBefore", "javascript", "lookbehind"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/aspnet.js"], "sourcesContent": ["'use strict'\nvar refractorCsharp = require('./csharp.js')\nmodule.exports = aspnet\naspnet.displayName = 'aspnet'\naspnet.aliases = []\nfunction aspnet(Prism) {\n  Prism.register(refractorCsharp)\n  Prism.languages.aspnet = Prism.languages.extend('markup', {\n    'page-directive': {\n      pattern: /<%\\s*@.*%>/,\n      alias: 'tag',\n      inside: {\n        'page-directive': {\n          pattern:\n            /<%\\s*@\\s*(?:Assembly|Control|Implements|Import|Master(?:Type)?|OutputCache|Page|PreviousPageType|Reference|Register)?|%>/i,\n          alias: 'tag'\n        },\n        rest: Prism.languages.markup.tag.inside\n      }\n    },\n    directive: {\n      pattern: /<%.*%>/,\n      alias: 'tag',\n      inside: {\n        directive: {\n          pattern: /<%\\s*?[$=%#:]{0,2}|%>/,\n          alias: 'tag'\n        },\n        rest: Prism.languages.csharp\n      }\n    }\n  }) // Regexp copied from prism-markup, with a negative look-ahead added\n  Prism.languages.aspnet.tag.pattern =\n    /<(?!%)\\/?[^\\s>\\/]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/ // match directives of attribute value foo=\"<% Bar %>\"\n  Prism.languages.insertBefore(\n    'inside',\n    'punctuation',\n    {\n      directive: Prism.languages.aspnet['directive']\n    },\n    Prism.languages.aspnet.tag.inside['attr-value']\n  )\n  Prism.languages.insertBefore('aspnet', 'comment', {\n    'asp-comment': {\n      pattern: /<%--[\\s\\S]*?--%>/,\n      alias: ['asp', 'comment']\n    }\n  }) // script runat=\"server\" contains csharp, not javascript\n  Prism.languages.insertBefore(\n    'aspnet',\n    Prism.languages.javascript ? 'script' : 'tag',\n    {\n      'asp-script': {\n        pattern:\n          /(<script(?=.*runat=['\"]?server\\b)[^>]*>)[\\s\\S]*?(?=<\\/script>)/i,\n        lookbehind: true,\n        alias: ['asp', 'script'],\n        inside: Prism.languages.csharp || {}\n      }\n    }\n  )\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,aAAa,CAAC;AAC5CC,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACR,eAAe,CAAC;EAC/BO,KAAK,CAACE,SAAS,CAACL,MAAM,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;IACxD,gBAAgB,EAAE;MAChBC,OAAO,EAAE,YAAY;MACrBC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE;QACN,gBAAgB,EAAE;UAChBF,OAAO,EACL,2HAA2H;UAC7HC,KAAK,EAAE;QACT,CAAC;QACDE,IAAI,EAAEP,KAAK,CAACE,SAAS,CAACM,MAAM,CAACC,GAAG,CAACH;MACnC;IACF,CAAC;IACDI,SAAS,EAAE;MACTN,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE;QACNI,SAAS,EAAE;UACTN,OAAO,EAAE,uBAAuB;UAChCC,KAAK,EAAE;QACT,CAAC;QACDE,IAAI,EAAEP,KAAK,CAACE,SAAS,CAACS;MACxB;IACF;EACF,CAAC,CAAC,EAAC;EACHX,KAAK,CAACE,SAAS,CAACL,MAAM,CAACY,GAAG,CAACL,OAAO,GAChC,kGAAkG,EAAC;EACrGJ,KAAK,CAACE,SAAS,CAACU,YAAY,CAC1B,QAAQ,EACR,aAAa,EACb;IACEF,SAAS,EAAEV,KAAK,CAACE,SAAS,CAACL,MAAM,CAAC,WAAW;EAC/C,CAAC,EACDG,KAAK,CAACE,SAAS,CAACL,MAAM,CAACY,GAAG,CAACH,MAAM,CAAC,YAAY,CAChD,CAAC;EACDN,KAAK,CAACE,SAAS,CAACU,YAAY,CAAC,QAAQ,EAAE,SAAS,EAAE;IAChD,aAAa,EAAE;MACbR,OAAO,EAAE,kBAAkB;MAC3BC,KAAK,EAAE,CAAC,KAAK,EAAE,SAAS;IAC1B;EACF,CAAC,CAAC,EAAC;EACHL,KAAK,CAACE,SAAS,CAACU,YAAY,CAC1B,QAAQ,EACRZ,KAAK,CAACE,SAAS,CAACW,UAAU,GAAG,QAAQ,GAAG,KAAK,EAC7C;IACE,YAAY,EAAE;MACZT,OAAO,EACL,iEAAiE;MACnEU,UAAU,EAAE,IAAI;MAChBT,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;MACxBC,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACS,MAAM,IAAI,CAAC;IACrC;EACF,CACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}