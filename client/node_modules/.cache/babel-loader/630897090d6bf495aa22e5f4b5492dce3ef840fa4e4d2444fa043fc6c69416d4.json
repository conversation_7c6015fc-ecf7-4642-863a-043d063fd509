{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\n Language: Apache Access Log\n Author: Oleg Efimov <<EMAIL>>\n Description: Apache/Nginx Access Logs\n Website: https://httpd.apache.org/docs/2.4/logs.html#accesslog\n Audit: 2020\n */\n\n/** @type LanguageFn */\nfunction accesslog(_hljs) {\n  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods\n  const HTTP_VERBS = [\"GET\", \"POST\", \"HEAD\", \"PUT\", \"DELETE\", \"CONNECT\", \"OPTIONS\", \"PATCH\", \"TRACE\"];\n  return {\n    name: 'Apache Access Log',\n    contains: [\n    // IP\n    {\n      className: 'number',\n      begin: /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?\\b/,\n      relevance: 5\n    },\n    // Other numbers\n    {\n      className: 'number',\n      begin: /\\b\\d+\\b/,\n      relevance: 0\n    },\n    // Requests\n    {\n      className: 'string',\n      begin: concat(/\"/, either(...HTTP_VERBS)),\n      end: /\"/,\n      keywords: HTTP_VERBS,\n      illegal: /\\n/,\n      relevance: 5,\n      contains: [{\n        begin: /HTTP\\/[12]\\.\\d'/,\n        relevance: 5\n      }]\n    },\n    // Dates\n    {\n      className: 'string',\n      // dates must have a certain length, this prevents matching\n      // simple array accesses a[123] and [] and other common patterns\n      // found in other languages\n      begin: /\\[\\d[^\\]\\n]{8,}\\]/,\n      illegal: /\\n/,\n      relevance: 1\n    }, {\n      className: 'string',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: /\\n/,\n      relevance: 0\n    },\n    // User agent / relevance boost\n    {\n      className: 'string',\n      begin: /\"Mozilla\\/\\d\\.\\d \\(/,\n      end: /\"/,\n      illegal: /\\n/,\n      relevance: 3\n    },\n    // Strings\n    {\n      className: 'string',\n      begin: /\"/,\n      end: /\"/,\n      illegal: /\\n/,\n      relevance: 0\n    }]\n  };\n}\nmodule.exports = accesslog;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "either", "accesslog", "_hljs", "HTTP_VERBS", "name", "contains", "className", "begin", "relevance", "end", "keywords", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/accesslog.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\n Language: Apache Access Log\n Author: Oleg Efimov <<EMAIL>>\n Description: Apache/Nginx Access Logs\n Website: https://httpd.apache.org/docs/2.4/logs.html#accesslog\n Audit: 2020\n */\n\n/** @type LanguageFn */\nfunction accesslog(_hljs) {\n  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods\n  const HTTP_VERBS = [\n    \"GET\",\n    \"POST\",\n    \"HEAD\",\n    \"PUT\",\n    \"DELETE\",\n    \"CONNECT\",\n    \"OPTIONS\",\n    \"PATCH\",\n    \"TRACE\"\n  ];\n  return {\n    name: 'Apache Access Log',\n    contains: [\n      // IP\n      {\n        className: 'number',\n        begin: /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?\\b/,\n        relevance: 5\n      },\n      // Other numbers\n      {\n        className: 'number',\n        begin: /\\b\\d+\\b/,\n        relevance: 0\n      },\n      // Requests\n      {\n        className: 'string',\n        begin: concat(/\"/, either(...HTTP_VERBS)),\n        end: /\"/,\n        keywords: HTTP_VERBS,\n        illegal: /\\n/,\n        relevance: 5,\n        contains: [\n          {\n            begin: /HTTP\\/[12]\\.\\d'/,\n            relevance: 5\n          }\n        ]\n      },\n      // Dates\n      {\n        className: 'string',\n        // dates must have a certain length, this prevents matching\n        // simple array accesses a[123] and [] and other common patterns\n        // found in other languages\n        begin: /\\[\\d[^\\]\\n]{8,}\\]/,\n        illegal: /\\n/,\n        relevance: 1\n      },\n      {\n        className: 'string',\n        begin: /\\[/,\n        end: /\\]/,\n        illegal: /\\n/,\n        relevance: 0\n      },\n      // User agent / relevance boost\n      {\n        className: 'string',\n        begin: /\"Mozilla\\/\\d\\.\\d \\(/,\n        end: /\"/,\n        illegal: /\\n/,\n        relevance: 3\n      },\n      // Strings\n      {\n        className: 'string',\n        begin: /\"/,\n        end: /\"/,\n        illegal: /\\n/,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = accesslog;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASK,SAASA,CAACC,KAAK,EAAE;EACxB;EACA,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,SAAS,EACT,SAAS,EACT,OAAO,EACP,OAAO,CACR;EACD,OAAO;IACLC,IAAI,EAAE,mBAAmB;IACzBC,QAAQ,EAAE;IACR;IACA;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,kDAAkD;MACzDC,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,SAAS;MAChBC,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAEb,MAAM,CAAC,GAAG,EAAEM,MAAM,CAAC,GAAGG,UAAU,CAAC,CAAC;MACzCM,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAEP,UAAU;MACpBQ,OAAO,EAAE,IAAI;MACbH,SAAS,EAAE,CAAC;MACZH,QAAQ,EAAE,CACR;QACEE,KAAK,EAAE,iBAAiB;QACxBC,SAAS,EAAE;MACb,CAAC;IAEL,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnB;MACA;MACA;MACAC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAE,IAAI;MACbH,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,IAAI;MACXE,GAAG,EAAE,IAAI;MACTE,OAAO,EAAE,IAAI;MACbH,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,qBAAqB;MAC5BE,GAAG,EAAE,GAAG;MACRE,OAAO,EAAE,IAAI;MACbH,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MACVE,GAAG,EAAE,GAAG;MACRE,OAAO,EAAE,IAAI;MACbH,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAI,MAAM,CAACC,OAAO,GAAGZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}