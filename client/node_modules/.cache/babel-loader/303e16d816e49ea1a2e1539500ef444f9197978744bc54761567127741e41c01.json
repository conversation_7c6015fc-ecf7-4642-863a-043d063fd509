{"ast": null, "code": "'use strict';\n\nmodule.exports = bbcode;\nbbcode.displayName = 'bbcode';\nbbcode.aliases = ['shortcode'];\nfunction bbcode(Prism) {\n  Prism.languages.bbcode = {\n    tag: {\n      pattern: /\\[\\/?[^\\s=\\]]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\"\\]=]+))?(?:\\s+[^\\s=\\]]+\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\"\\]=]+))*\\s*\\]/,\n      inside: {\n        tag: {\n          pattern: /^\\[\\/?[^\\s=\\]]+/,\n          inside: {\n            punctuation: /^\\[\\/?/\n          }\n        },\n        'attr-value': {\n          pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\"\\]=]+)/,\n          inside: {\n            punctuation: [/^=/, {\n              pattern: /^(\\s*)[\"']|[\"']$/,\n              lookbehind: true\n            }]\n          }\n        },\n        punctuation: /\\]/,\n        'attr-name': /[^\\s=\\]]+/\n      }\n    }\n  };\n  Prism.languages.shortcode = Prism.languages.bbcode;\n}", "map": {"version": 3, "names": ["module", "exports", "bbcode", "displayName", "aliases", "Prism", "languages", "tag", "pattern", "inside", "punctuation", "lookbehind", "shortcode"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/bbcode.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bbcode\nbbcode.displayName = 'bbcode'\nbbcode.aliases = ['shortcode']\nfunction bbcode(Prism) {\n  Prism.languages.bbcode = {\n    tag: {\n      pattern:\n        /\\[\\/?[^\\s=\\]]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\"\\]=]+))?(?:\\s+[^\\s=\\]]+\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\"\\]=]+))*\\s*\\]/,\n      inside: {\n        tag: {\n          pattern: /^\\[\\/?[^\\s=\\]]+/,\n          inside: {\n            punctuation: /^\\[\\/?/\n          }\n        },\n        'attr-value': {\n          pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\"\\]=]+)/,\n          inside: {\n            punctuation: [\n              /^=/,\n              {\n                pattern: /^(\\s*)[\"']|[\"']$/,\n                lookbehind: true\n              }\n            ]\n          }\n        },\n        punctuation: /\\]/,\n        'attr-name': /[^\\s=\\]]+/\n      }\n    }\n  }\n  Prism.languages.shortcode = Prism.languages.bbcode\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,WAAW,CAAC;AAC9B,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,GAAG,EAAE;MACHC,OAAO,EACL,uHAAuH;MACzHC,MAAM,EAAE;QACNF,GAAG,EAAE;UACHC,OAAO,EAAE,iBAAiB;UAC1BC,MAAM,EAAE;YACNC,WAAW,EAAE;UACf;QACF,CAAC;QACD,YAAY,EAAE;UACZF,OAAO,EAAE,qCAAqC;UAC9CC,MAAM,EAAE;YACNC,WAAW,EAAE,CACX,IAAI,EACJ;cACEF,OAAO,EAAE,kBAAkB;cAC3BG,UAAU,EAAE;YACd,CAAC;UAEL;QACF,CAAC;QACDD,WAAW,EAAE,IAAI;QACjB,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACDL,KAAK,CAACC,SAAS,CAACM,SAAS,GAAGP,KAAK,CAACC,SAAS,CAACJ,MAAM;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}