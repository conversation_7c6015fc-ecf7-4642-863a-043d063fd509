{"ast": null, "code": "'use strict';\n\nmodule.exports = wren;\nwren.displayName = 'wren';\nwren.aliases = [];\nfunction wren(Prism) {\n  // https://wren.io/\n  Prism.languages.wren = {\n    // Multiline comments in Wren can have nested multiline comments\n    // Comments: // and /* */\n    comment: [{\n      // support 3 levels of nesting\n      // regex: \\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\/\n      pattern: /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*))*\\*\\/)*\\*\\/)*\\*\\//,\n      greedy: true\n    }, {\n      pattern: /(^|[^\\\\:])\\/\\/.*/,\n      lookbehind: true,\n      greedy: true\n    }],\n    // Triple quoted strings are multiline but cannot have interpolation (raw strings)\n    // Based on prism-python.js\n    'triple-quoted-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    // see below\n    'string-literal': null,\n    // #!/usr/bin/env wren on the first line\n    hashbang: {\n      pattern: /^#!\\/.+/,\n      greedy: true,\n      alias: 'comment'\n    },\n    // Attributes are special keywords to add meta data to classes\n    attribute: {\n      // #! attributes are stored in class properties\n      // #!myvar = true\n      // #attributes are not stored and dismissed at compilation\n      pattern: /#!?[ \\t\\u3000]*\\w+/,\n      alias: 'keyword'\n    },\n    'class-name': [{\n      // class definition\n      // class Meta {}\n      pattern: /(\\bclass\\s+)\\w+/,\n      lookbehind: true\n    },\n    // A class must always start with an uppercase.\n    // File.read\n    /\\b[A-Z][a-z\\d_]*\\b/],\n    // A constant can be a variable, class, property or method. Just named in all uppercase letters\n    constant: /\\b[A-Z][A-Z\\d_]*\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    keyword: /\\b(?:as|break|class|construct|continue|else|for|foreign|if|import|in|is|return|static|super|this|var|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    // Functions can be Class.method()\n    function: /\\b[a-z_]\\w*(?=\\s*[({])/i,\n    operator: /<<|>>|[=!<>]=?|&&|\\|\\||[-+*/%~^&|?:]|\\.{2,3}/,\n    punctuation: /[\\[\\](){}.,;]/\n  };\n  Prism.languages.wren['string-literal'] = {\n    // A single quote string is multiline and can have interpolation (similar to JS backticks ``)\n    pattern: /(^|[^\\\\\"])\"(?:[^\\\\\"%]|\\\\[\\s\\S]|%(?!\\()|%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\))*\"/,\n    lookbehind: true,\n    greedy: true,\n    inside: {\n      interpolation: {\n        // \"%(interpolation)\"\n        pattern: /((?:^|[^\\\\])(?:\\\\{2})*)%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\)/,\n        lookbehind: true,\n        inside: {\n          expression: {\n            pattern: /^(%\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: Prism.languages.wren\n          },\n          'interpolation-punctuation': {\n            pattern: /^%\\(|\\)$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: /[\\s\\S]+/\n    }\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "wren", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "lookbehind", "alias", "hashbang", "attribute", "constant", "null", "keyword", "boolean", "number", "function", "operator", "punctuation", "inside", "interpolation", "expression", "string"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/wren.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wren\nwren.displayName = 'wren'\nwren.aliases = []\nfunction wren(Prism) {\n  // https://wren.io/\n  Prism.languages.wren = {\n    // Multiline comments in Wren can have nested multiline comments\n    // Comments: // and /* */\n    comment: [\n      {\n        // support 3 levels of nesting\n        // regex: \\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\/\n        pattern:\n          /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*))*\\*\\/)*\\*\\/)*\\*\\//,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    // Triple quoted strings are multiline but cannot have interpolation (raw strings)\n    // Based on prism-python.js\n    'triple-quoted-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    // see below\n    'string-literal': null,\n    // #!/usr/bin/env wren on the first line\n    hashbang: {\n      pattern: /^#!\\/.+/,\n      greedy: true,\n      alias: 'comment'\n    },\n    // Attributes are special keywords to add meta data to classes\n    attribute: {\n      // #! attributes are stored in class properties\n      // #!myvar = true\n      // #attributes are not stored and dismissed at compilation\n      pattern: /#!?[ \\t\\u3000]*\\w+/,\n      alias: 'keyword'\n    },\n    'class-name': [\n      {\n        // class definition\n        // class Meta {}\n        pattern: /(\\bclass\\s+)\\w+/,\n        lookbehind: true\n      }, // A class must always start with an uppercase.\n      // File.read\n      /\\b[A-Z][a-z\\d_]*\\b/\n    ],\n    // A constant can be a variable, class, property or method. Just named in all uppercase letters\n    constant: /\\b[A-Z][A-Z\\d_]*\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    keyword:\n      /\\b(?:as|break|class|construct|continue|else|for|foreign|if|import|in|is|return|static|super|this|var|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    // Functions can be Class.method()\n    function: /\\b[a-z_]\\w*(?=\\s*[({])/i,\n    operator: /<<|>>|[=!<>]=?|&&|\\|\\||[-+*/%~^&|?:]|\\.{2,3}/,\n    punctuation: /[\\[\\](){}.,;]/\n  }\n  Prism.languages.wren['string-literal'] = {\n    // A single quote string is multiline and can have interpolation (similar to JS backticks ``)\n    pattern:\n      /(^|[^\\\\\"])\"(?:[^\\\\\"%]|\\\\[\\s\\S]|%(?!\\()|%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\))*\"/,\n    lookbehind: true,\n    greedy: true,\n    inside: {\n      interpolation: {\n        // \"%(interpolation)\"\n        pattern:\n          /((?:^|[^\\\\])(?:\\\\{2})*)%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\)/,\n        lookbehind: true,\n        inside: {\n          expression: {\n            pattern: /^(%\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: Prism.languages.wren\n          },\n          'interpolation-punctuation': {\n            pattern: /^%\\(|\\)$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: /[\\s\\S]+/\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EACAA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrB;IACA;IACAK,OAAO,EAAE,CACP;MACE;MACA;MACAC,OAAO,EACL,gHAAgH;MAClHC,MAAM,EAAE;IACV,CAAC,EACD;MACED,OAAO,EAAE,kBAAkB;MAC3BE,UAAU,EAAE,IAAI;MAChBD,MAAM,EAAE;IACV,CAAC,CACF;IACD;IACA;IACA,sBAAsB,EAAE;MACtBD,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE,IAAI;MACZE,KAAK,EAAE;IACT,CAAC;IACD;IACA,gBAAgB,EAAE,IAAI;IACtB;IACAC,QAAQ,EAAE;MACRJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,IAAI;MACZE,KAAK,EAAE;IACT,CAAC;IACD;IACAE,SAAS,EAAE;MACT;MACA;MACA;MACAL,OAAO,EAAE,oBAAoB;MAC7BG,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE,CACZ;MACE;MACA;MACAH,OAAO,EAAE,iBAAiB;MAC1BE,UAAU,EAAE;IACd,CAAC;IAAE;IACH;IACA,oBAAoB,CACrB;IACD;IACAI,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE;MACJP,OAAO,EAAE,UAAU;MACnBG,KAAK,EAAE;IACT,CAAC;IACDK,OAAO,EACL,+GAA+G;IACjHC,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EAAE,iDAAiD;IACzD;IACAC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,8CAA8C;IACxDC,WAAW,EAAE;EACf,CAAC;EACDhB,KAAK,CAACC,SAAS,CAACJ,IAAI,CAAC,gBAAgB,CAAC,GAAG;IACvC;IACAM,OAAO,EACL,oFAAoF;IACtFE,UAAU,EAAE,IAAI;IAChBD,MAAM,EAAE,IAAI;IACZa,MAAM,EAAE;MACNC,aAAa,EAAE;QACb;QACAf,OAAO,EACL,iEAAiE;QACnEE,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAE;UACNE,UAAU,EAAE;YACVhB,OAAO,EAAE,sBAAsB;YAC/BE,UAAU,EAAE,IAAI;YAChBY,MAAM,EAAEjB,KAAK,CAACC,SAAS,CAACJ;UAC1B,CAAC;UACD,2BAA2B,EAAE;YAC3BM,OAAO,EAAE,UAAU;YACnBG,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDc,MAAM,EAAE;IACV;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}