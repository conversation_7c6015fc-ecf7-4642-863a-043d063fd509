{"ast": null, "code": "'use strict';\n\nmodule.exports = kotlin;\nkotlin.displayName = 'kotlin';\nkotlin.aliases = ['kt', 'kts'];\nfunction kotlin(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.kotlin = Prism.languages.extend('clike', {\n      keyword: {\n        // The lookbehind prevents wrong highlighting of e.g. kotlin.properties.get\n        pattern: /(^|[^.])\\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\\b/,\n        lookbehind: true\n      },\n      function: [{\n        pattern: /(?:`[^\\r\\n`]+`|\\b\\w+)(?=\\s*\\()/,\n        greedy: true\n      }, {\n        pattern: /(\\.)(?:`[^\\r\\n`]+`|\\w+)(?=\\s*\\{)/,\n        lookbehind: true,\n        greedy: true\n      }],\n      number: /\\b(?:0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?[fFL]?)\\b/,\n      operator: /\\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\\/*%<>]=?|[?:]:?|\\.\\.|&&|\\|\\||\\b(?:and|inv|or|shl|shr|ushr|xor)\\b/\n    });\n    delete Prism.languages.kotlin['class-name'];\n    var interpolationInside = {\n      'interpolation-punctuation': {\n        pattern: /^\\$\\{?|\\}$/,\n        alias: 'punctuation'\n      },\n      expression: {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.kotlin\n      }\n    };\n    Prism.languages.insertBefore('kotlin', 'string', {\n      // https://kotlinlang.org/spec/expressions.html#string-interpolation-expressions\n      'string-literal': [{\n        pattern: /\"\"\"(?:[^$]|\\$(?:(?!\\{)|\\{[^{}]*\\}))*?\"\"\"/,\n        alias: 'multiline',\n        inside: {\n          interpolation: {\n            pattern: /\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n            inside: interpolationInside\n          },\n          string: /[\\s\\S]+/\n        }\n      }, {\n        pattern: /\"(?:[^\"\\\\\\r\\n$]|\\\\.|\\$(?:(?!\\{)|\\{[^{}]*\\}))*\"/,\n        alias: 'singleline',\n        inside: {\n          interpolation: {\n            pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n            lookbehind: true,\n            inside: interpolationInside\n          },\n          string: /[\\s\\S]+/\n        }\n      }],\n      char: {\n        // https://kotlinlang.org/spec/expressions.html#character-literals\n        pattern: /'(?:[^'\\\\\\r\\n]|\\\\(?:.|u[a-fA-F0-9]{0,4}))'/,\n        greedy: true\n      }\n    });\n    delete Prism.languages.kotlin['string'];\n    Prism.languages.insertBefore('kotlin', 'keyword', {\n      annotation: {\n        pattern: /\\B@(?:\\w+:)?(?:[A-Z]\\w*|\\[[^\\]]+\\])/,\n        alias: 'builtin'\n      }\n    });\n    Prism.languages.insertBefore('kotlin', 'function', {\n      label: {\n        pattern: /\\b\\w+@|@\\w+\\b/,\n        alias: 'symbol'\n      }\n    });\n    Prism.languages.kt = Prism.languages.kotlin;\n    Prism.languages.kts = Prism.languages.kotlin;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "kotlin", "displayName", "aliases", "Prism", "languages", "extend", "keyword", "pattern", "lookbehind", "function", "greedy", "number", "operator", "interpolationInside", "alias", "expression", "inside", "insertBefore", "interpolation", "string", "char", "annotation", "label", "kt", "kts"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/kotlin.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = kotlin\nkotlin.displayName = 'kotlin'\nkotlin.aliases = ['kt', 'kts']\nfunction kotlin(Prism) {\n  ;(function (Prism) {\n    Prism.languages.kotlin = Prism.languages.extend('clike', {\n      keyword: {\n        // The lookbehind prevents wrong highlighting of e.g. kotlin.properties.get\n        pattern:\n          /(^|[^.])\\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\\b/,\n        lookbehind: true\n      },\n      function: [\n        {\n          pattern: /(?:`[^\\r\\n`]+`|\\b\\w+)(?=\\s*\\()/,\n          greedy: true\n        },\n        {\n          pattern: /(\\.)(?:`[^\\r\\n`]+`|\\w+)(?=\\s*\\{)/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      number:\n        /\\b(?:0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?[fFL]?)\\b/,\n      operator:\n        /\\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\\/*%<>]=?|[?:]:?|\\.\\.|&&|\\|\\||\\b(?:and|inv|or|shl|shr|ushr|xor)\\b/\n    })\n    delete Prism.languages.kotlin['class-name']\n    var interpolationInside = {\n      'interpolation-punctuation': {\n        pattern: /^\\$\\{?|\\}$/,\n        alias: 'punctuation'\n      },\n      expression: {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.kotlin\n      }\n    }\n    Prism.languages.insertBefore('kotlin', 'string', {\n      // https://kotlinlang.org/spec/expressions.html#string-interpolation-expressions\n      'string-literal': [\n        {\n          pattern: /\"\"\"(?:[^$]|\\$(?:(?!\\{)|\\{[^{}]*\\}))*?\"\"\"/,\n          alias: 'multiline',\n          inside: {\n            interpolation: {\n              pattern: /\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n              inside: interpolationInside\n            },\n            string: /[\\s\\S]+/\n          }\n        },\n        {\n          pattern: /\"(?:[^\"\\\\\\r\\n$]|\\\\.|\\$(?:(?!\\{)|\\{[^{}]*\\}))*\"/,\n          alias: 'singleline',\n          inside: {\n            interpolation: {\n              pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n              lookbehind: true,\n              inside: interpolationInside\n            },\n            string: /[\\s\\S]+/\n          }\n        }\n      ],\n      char: {\n        // https://kotlinlang.org/spec/expressions.html#character-literals\n        pattern: /'(?:[^'\\\\\\r\\n]|\\\\(?:.|u[a-fA-F0-9]{0,4}))'/,\n        greedy: true\n      }\n    })\n    delete Prism.languages.kotlin['string']\n    Prism.languages.insertBefore('kotlin', 'keyword', {\n      annotation: {\n        pattern: /\\B@(?:\\w+:)?(?:[A-Z]\\w*|\\[[^\\]]+\\])/,\n        alias: 'builtin'\n      }\n    })\n    Prism.languages.insertBefore('kotlin', 'function', {\n      label: {\n        pattern: /\\b\\w+@|@\\w+\\b/,\n        alias: 'symbol'\n      }\n    })\n    Prism.languages.kt = Prism.languages.kotlin\n    Prism.languages.kts = Prism.languages.kotlin\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;AAC9B,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MACvDC,OAAO,EAAE;QACP;QACAC,OAAO,EACL,0bAA0b;QAC5bC,UAAU,EAAE;MACd,CAAC;MACDC,QAAQ,EAAE,CACR;QACEF,OAAO,EAAE,gCAAgC;QACzCG,MAAM,EAAE;MACV,CAAC,EACD;QACEH,OAAO,EAAE,kCAAkC;QAC3CC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;MACV,CAAC,CACF;MACDC,MAAM,EACJ,iIAAiI;MACnIC,QAAQ,EACN;IACJ,CAAC,CAAC;IACF,OAAOT,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,YAAY,CAAC;IAC3C,IAAIa,mBAAmB,GAAG;MACxB,2BAA2B,EAAE;QAC3BN,OAAO,EAAE,YAAY;QACrBO,KAAK,EAAE;MACT,CAAC;MACDC,UAAU,EAAE;QACVR,OAAO,EAAE,SAAS;QAClBS,MAAM,EAAEb,KAAK,CAACC,SAAS,CAACJ;MAC1B;IACF,CAAC;IACDG,KAAK,CAACC,SAAS,CAACa,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;MAC/C;MACA,gBAAgB,EAAE,CAChB;QACEV,OAAO,EAAE,0CAA0C;QACnDO,KAAK,EAAE,WAAW;QAClBE,MAAM,EAAE;UACNE,aAAa,EAAE;YACbX,OAAO,EAAE,6BAA6B;YACtCS,MAAM,EAAEH;UACV,CAAC;UACDM,MAAM,EAAE;QACV;MACF,CAAC,EACD;QACEZ,OAAO,EAAE,gDAAgD;QACzDO,KAAK,EAAE,YAAY;QACnBE,MAAM,EAAE;UACNE,aAAa,EAAE;YACbX,OAAO,EAAE,oDAAoD;YAC7DC,UAAU,EAAE,IAAI;YAChBQ,MAAM,EAAEH;UACV,CAAC;UACDM,MAAM,EAAE;QACV;MACF,CAAC,CACF;MACDC,IAAI,EAAE;QACJ;QACAb,OAAO,EAAE,4CAA4C;QACrDG,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IACF,OAAOP,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,QAAQ,CAAC;IACvCG,KAAK,CAACC,SAAS,CAACa,YAAY,CAAC,QAAQ,EAAE,SAAS,EAAE;MAChDI,UAAU,EAAE;QACVd,OAAO,EAAE,qCAAqC;QAC9CO,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFX,KAAK,CAACC,SAAS,CAACa,YAAY,CAAC,QAAQ,EAAE,UAAU,EAAE;MACjDK,KAAK,EAAE;QACLf,OAAO,EAAE,eAAe;QACxBO,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFX,KAAK,CAACC,SAAS,CAACmB,EAAE,GAAGpB,KAAK,CAACC,SAAS,CAACJ,MAAM;IAC3CG,KAAK,CAACC,SAAS,CAACoB,GAAG,GAAGrB,KAAK,CAACC,SAAS,CAACJ,MAAM;EAC9C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}