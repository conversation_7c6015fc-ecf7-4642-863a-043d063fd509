{"ast": null, "code": "'use strict';\n\nmodule.exports = xquery;\nxquery.displayName = 'xquery';\nxquery.aliases = [];\nfunction xquery(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.xquery = Prism.languages.extend('markup', {\n      'xquery-comment': {\n        pattern: /\\(:[\\s\\S]*?:\\)/,\n        greedy: true,\n        alias: 'comment'\n      },\n      string: {\n        pattern: /([\"'])(?:\\1\\1|(?!\\1)[\\s\\S])*\\1/,\n        greedy: true\n      },\n      extension: {\n        pattern: /\\(#.+?#\\)/,\n        alias: 'symbol'\n      },\n      variable: /\\$[-\\w:]+/,\n      axis: {\n        pattern: /(^|[^-])(?:ancestor(?:-or-self)?|attribute|child|descendant(?:-or-self)?|following(?:-sibling)?|parent|preceding(?:-sibling)?|self)(?=::)/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      'keyword-operator': {\n        pattern: /(^|[^:-])\\b(?:and|castable as|div|eq|except|ge|gt|idiv|instance of|intersect|is|le|lt|mod|ne|or|union)\\b(?=$|[^:-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      keyword: {\n        pattern: /(^|[^:-])\\b(?:as|ascending|at|base-uri|boundary-space|case|cast as|collation|construction|copy-namespaces|declare|default|descending|else|empty (?:greatest|least)|encoding|every|external|for|function|if|import|in|inherit|lax|let|map|module|namespace|no-inherit|no-preserve|option|order(?: by|ed|ing)?|preserve|return|satisfies|schema|some|stable|strict|strip|then|to|treat as|typeswitch|unordered|validate|variable|version|where|xquery)\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      function: /[\\w-]+(?::[\\w-]+)*(?=\\s*\\()/,\n      'xquery-element': {\n        pattern: /(element\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'tag'\n      },\n      'xquery-attribute': {\n        pattern: /(attribute\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'attr-name'\n      },\n      builtin: {\n        pattern: /(^|[^:-])\\b(?:attribute|comment|document|element|processing-instruction|text|xs:(?:ENTITIES|ENTITY|ID|IDREFS?|NCName|NMTOKENS?|NOTATION|Name|QName|anyAtomicType|anyType|anyURI|base64Binary|boolean|byte|date|dateTime|dayTimeDuration|decimal|double|duration|float|gDay|gMonth|gMonthDay|gYear|gYearMonth|hexBinary|int|integer|language|long|negativeInteger|nonNegativeInteger|nonPositiveInteger|normalizedString|positiveInteger|short|string|time|token|unsigned(?:Byte|Int|Long|Short)|untyped(?:Atomic)?|yearMonthDuration))\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      number: /\\b\\d+(?:\\.\\d+)?(?:E[+-]?\\d+)?/,\n      operator: [/[+*=?|@]|\\.\\.?|:=|!=|<[=<]?|>[=>]?/, {\n        pattern: /(\\s)-(?=\\s)/,\n        lookbehind: true\n      }],\n      punctuation: /[[\\](){},;:/]/\n    });\n    Prism.languages.xquery.tag.pattern = /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/;\n    Prism.languages.xquery['tag'].inside['attr-value'].pattern = /=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/;\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['punctuation'] = /^=\"|\"$/;\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['expression'] = {\n      // Allow for two levels of nesting\n      pattern: /\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}/,\n      inside: Prism.languages.xquery,\n      alias: 'language-xquery'\n    }; // The following will handle plain text inside tags\n    var stringifyToken = function (token) {\n      if (typeof token === 'string') {\n        return token;\n      }\n      if (typeof token.content === 'string') {\n        return token.content;\n      }\n      return token.content.map(stringifyToken).join('');\n    };\n    var walkTokens = function (tokens) {\n      var openedTags = [];\n      for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i];\n        var notTagNorBrace = false;\n        if (typeof token !== 'string') {\n          if (token.type === 'tag' && token.content[0] && token.content[0].type === 'tag') {\n            // We found a tag, now find its kind\n            if (token.content[0].content[0].content === '</') {\n              // Closing tag\n              if (openedTags.length > 0 && openedTags[openedTags.length - 1].tagName === stringifyToken(token.content[0].content[1])) {\n                // Pop matching opening tag\n                openedTags.pop();\n              }\n            } else {\n              if (token.content[token.content.length - 1].content === '/>') {\n                // Autoclosed tag, ignore\n              } else {\n                // Opening tag\n                openedTags.push({\n                  tagName: stringifyToken(token.content[0].content[1]),\n                  openedBraces: 0\n                });\n              }\n            }\n          } else if (openedTags.length > 0 && token.type === 'punctuation' && token.content === '{' && (\n          // Ignore `{{`\n          !tokens[i + 1] || tokens[i + 1].type !== 'punctuation' || tokens[i + 1].content !== '{') && (!tokens[i - 1] || tokens[i - 1].type !== 'plain-text' || tokens[i - 1].content !== '{')) {\n            // Here we might have entered an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces++;\n          } else if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces > 0 && token.type === 'punctuation' && token.content === '}') {\n            // Here we might have left an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces--;\n          } else if (token.type !== 'comment') {\n            notTagNorBrace = true;\n          }\n        }\n        if (notTagNorBrace || typeof token === 'string') {\n          if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces === 0) {\n            // Here we are inside a tag, and not inside an XQuery expression.\n            // That's plain text: drop any tokens matched.\n            var plainText = stringifyToken(token); // And merge text with adjacent text\n            if (i < tokens.length - 1 && (typeof tokens[i + 1] === 'string' || tokens[i + 1].type === 'plain-text')) {\n              plainText += stringifyToken(tokens[i + 1]);\n              tokens.splice(i + 1, 1);\n            }\n            if (i > 0 && (typeof tokens[i - 1] === 'string' || tokens[i - 1].type === 'plain-text')) {\n              plainText = stringifyToken(tokens[i - 1]) + plainText;\n              tokens.splice(i - 1, 1);\n              i--;\n            }\n            if (/^\\s+$/.test(plainText)) {\n              tokens[i] = plainText;\n            } else {\n              tokens[i] = new Prism.Token('plain-text', plainText, null, plainText);\n            }\n          }\n        }\n        if (token.content && typeof token.content !== 'string') {\n          walkTokens(token.content);\n        }\n      }\n    };\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'xquery') {\n        return;\n      }\n      walkTokens(env.tokens);\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "xquery", "displayName", "aliases", "Prism", "languages", "extend", "pattern", "greedy", "alias", "string", "extension", "variable", "axis", "lookbehind", "keyword", "function", "builtin", "number", "operator", "punctuation", "tag", "inside", "stringifyToken", "token", "content", "map", "join", "walkTokens", "tokens", "openedTags", "i", "length", "notTagNorBrace", "type", "tagName", "pop", "push", "openedBraces", "plainText", "splice", "test", "Token", "hooks", "add", "env", "language"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/xquery.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = xquery\nxquery.displayName = 'xquery'\nxquery.aliases = []\nfunction xquery(Prism) {\n  ;(function (Prism) {\n    Prism.languages.xquery = Prism.languages.extend('markup', {\n      'xquery-comment': {\n        pattern: /\\(:[\\s\\S]*?:\\)/,\n        greedy: true,\n        alias: 'comment'\n      },\n      string: {\n        pattern: /([\"'])(?:\\1\\1|(?!\\1)[\\s\\S])*\\1/,\n        greedy: true\n      },\n      extension: {\n        pattern: /\\(#.+?#\\)/,\n        alias: 'symbol'\n      },\n      variable: /\\$[-\\w:]+/,\n      axis: {\n        pattern:\n          /(^|[^-])(?:ancestor(?:-or-self)?|attribute|child|descendant(?:-or-self)?|following(?:-sibling)?|parent|preceding(?:-sibling)?|self)(?=::)/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      'keyword-operator': {\n        pattern:\n          /(^|[^:-])\\b(?:and|castable as|div|eq|except|ge|gt|idiv|instance of|intersect|is|le|lt|mod|ne|or|union)\\b(?=$|[^:-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      keyword: {\n        pattern:\n          /(^|[^:-])\\b(?:as|ascending|at|base-uri|boundary-space|case|cast as|collation|construction|copy-namespaces|declare|default|descending|else|empty (?:greatest|least)|encoding|every|external|for|function|if|import|in|inherit|lax|let|map|module|namespace|no-inherit|no-preserve|option|order(?: by|ed|ing)?|preserve|return|satisfies|schema|some|stable|strict|strip|then|to|treat as|typeswitch|unordered|validate|variable|version|where|xquery)\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      function: /[\\w-]+(?::[\\w-]+)*(?=\\s*\\()/,\n      'xquery-element': {\n        pattern: /(element\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'tag'\n      },\n      'xquery-attribute': {\n        pattern: /(attribute\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'attr-name'\n      },\n      builtin: {\n        pattern:\n          /(^|[^:-])\\b(?:attribute|comment|document|element|processing-instruction|text|xs:(?:ENTITIES|ENTITY|ID|IDREFS?|NCName|NMTOKENS?|NOTATION|Name|QName|anyAtomicType|anyType|anyURI|base64Binary|boolean|byte|date|dateTime|dayTimeDuration|decimal|double|duration|float|gDay|gMonth|gMonthDay|gYear|gYearMonth|hexBinary|int|integer|language|long|negativeInteger|nonNegativeInteger|nonPositiveInteger|normalizedString|positiveInteger|short|string|time|token|unsigned(?:Byte|Int|Long|Short)|untyped(?:Atomic)?|yearMonthDuration))\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      number: /\\b\\d+(?:\\.\\d+)?(?:E[+-]?\\d+)?/,\n      operator: [\n        /[+*=?|@]|\\.\\.?|:=|!=|<[=<]?|>[=>]?/,\n        {\n          pattern: /(\\s)-(?=\\s)/,\n          lookbehind: true\n        }\n      ],\n      punctuation: /[[\\](){},;:/]/\n    })\n    Prism.languages.xquery.tag.pattern =\n      /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/\n    Prism.languages.xquery['tag'].inside['attr-value'].pattern =\n      /=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['punctuation'] =\n      /^=\"|\"$/\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['expression'] = {\n      // Allow for two levels of nesting\n      pattern: /\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}/,\n      inside: Prism.languages.xquery,\n      alias: 'language-xquery'\n    } // The following will handle plain text inside tags\n    var stringifyToken = function (token) {\n      if (typeof token === 'string') {\n        return token\n      }\n      if (typeof token.content === 'string') {\n        return token.content\n      }\n      return token.content.map(stringifyToken).join('')\n    }\n    var walkTokens = function (tokens) {\n      var openedTags = []\n      for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i]\n        var notTagNorBrace = false\n        if (typeof token !== 'string') {\n          if (\n            token.type === 'tag' &&\n            token.content[0] &&\n            token.content[0].type === 'tag'\n          ) {\n            // We found a tag, now find its kind\n            if (token.content[0].content[0].content === '</') {\n              // Closing tag\n              if (\n                openedTags.length > 0 &&\n                openedTags[openedTags.length - 1].tagName ===\n                  stringifyToken(token.content[0].content[1])\n              ) {\n                // Pop matching opening tag\n                openedTags.pop()\n              }\n            } else {\n              if (token.content[token.content.length - 1].content === '/>') {\n                // Autoclosed tag, ignore\n              } else {\n                // Opening tag\n                openedTags.push({\n                  tagName: stringifyToken(token.content[0].content[1]),\n                  openedBraces: 0\n                })\n              }\n            }\n          } else if (\n            openedTags.length > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '{' && // Ignore `{{`\n            (!tokens[i + 1] ||\n              tokens[i + 1].type !== 'punctuation' ||\n              tokens[i + 1].content !== '{') &&\n            (!tokens[i - 1] ||\n              tokens[i - 1].type !== 'plain-text' ||\n              tokens[i - 1].content !== '{')\n          ) {\n            // Here we might have entered an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces++\n          } else if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '}'\n          ) {\n            // Here we might have left an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces--\n          } else if (token.type !== 'comment') {\n            notTagNorBrace = true\n          }\n        }\n        if (notTagNorBrace || typeof token === 'string') {\n          if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces === 0\n          ) {\n            // Here we are inside a tag, and not inside an XQuery expression.\n            // That's plain text: drop any tokens matched.\n            var plainText = stringifyToken(token) // And merge text with adjacent text\n            if (\n              i < tokens.length - 1 &&\n              (typeof tokens[i + 1] === 'string' ||\n                tokens[i + 1].type === 'plain-text')\n            ) {\n              plainText += stringifyToken(tokens[i + 1])\n              tokens.splice(i + 1, 1)\n            }\n            if (\n              i > 0 &&\n              (typeof tokens[i - 1] === 'string' ||\n                tokens[i - 1].type === 'plain-text')\n            ) {\n              plainText = stringifyToken(tokens[i - 1]) + plainText\n              tokens.splice(i - 1, 1)\n              i--\n            }\n            if (/^\\s+$/.test(plainText)) {\n              tokens[i] = plainText\n            } else {\n              tokens[i] = new Prism.Token(\n                'plain-text',\n                plainText,\n                null,\n                plainText\n              )\n            }\n          }\n        }\n        if (token.content && typeof token.content !== 'string') {\n          walkTokens(token.content)\n        }\n      }\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'xquery') {\n        return\n      }\n      walkTokens(env.tokens)\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;MACxD,gBAAgB,EAAE;QAChBC,OAAO,EAAE,gBAAgB;QACzBC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAE;QACNH,OAAO,EAAE,gCAAgC;QACzCC,MAAM,EAAE;MACV,CAAC;MACDG,SAAS,EAAE;QACTJ,OAAO,EAAE,WAAW;QACpBE,KAAK,EAAE;MACT,CAAC;MACDG,QAAQ,EAAE,WAAW;MACrBC,IAAI,EAAE;QACJN,OAAO,EACL,2IAA2I;QAC7IO,UAAU,EAAE,IAAI;QAChBL,KAAK,EAAE;MACT,CAAC;MACD,kBAAkB,EAAE;QAClBF,OAAO,EACL,qHAAqH;QACvHO,UAAU,EAAE,IAAI;QAChBL,KAAK,EAAE;MACT,CAAC;MACDM,OAAO,EAAE;QACPR,OAAO,EACL,mcAAmc;QACrcO,UAAU,EAAE;MACd,CAAC;MACDE,QAAQ,EAAE,6BAA6B;MACvC,gBAAgB,EAAE;QAChBT,OAAO,EAAE,gCAAgC;QACzCO,UAAU,EAAE,IAAI;QAChBL,KAAK,EAAE;MACT,CAAC;MACD,kBAAkB,EAAE;QAClBF,OAAO,EAAE,kCAAkC;QAC3CO,UAAU,EAAE,IAAI;QAChBL,KAAK,EAAE;MACT,CAAC;MACDQ,OAAO,EAAE;QACPV,OAAO,EACL,qhBAAqhB;QACvhBO,UAAU,EAAE;MACd,CAAC;MACDI,MAAM,EAAE,+BAA+B;MACvCC,QAAQ,EAAE,CACR,oCAAoC,EACpC;QACEZ,OAAO,EAAE,aAAa;QACtBO,UAAU,EAAE;MACd,CAAC,CACF;MACDM,WAAW,EAAE;IACf,CAAC,CAAC;IACFhB,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACoB,GAAG,CAACd,OAAO,GAChC,sJAAsJ;IACxJH,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,KAAK,CAAC,CAACqB,MAAM,CAAC,YAAY,CAAC,CAACf,OAAO,GACxD,iGAAiG;IACnGH,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,KAAK,CAAC,CAACqB,MAAM,CAAC,YAAY,CAAC,CAACA,MAAM,CAAC,aAAa,CAAC,GACtE,QAAQ;IACVlB,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,KAAK,CAAC,CAACqB,MAAM,CAAC,YAAY,CAAC,CAACA,MAAM,CAAC,YAAY,CAAC,GAAG;MACxE;MACAf,OAAO,EAAE,gDAAgD;MACzDe,MAAM,EAAElB,KAAK,CAACC,SAAS,CAACJ,MAAM;MAC9BQ,KAAK,EAAE;IACT,CAAC,EAAC;IACF,IAAIc,cAAc,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACpC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOA,KAAK;MACd;MACA,IAAI,OAAOA,KAAK,CAACC,OAAO,KAAK,QAAQ,EAAE;QACrC,OAAOD,KAAK,CAACC,OAAO;MACtB;MACA,OAAOD,KAAK,CAACC,OAAO,CAACC,GAAG,CAACH,cAAc,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;IACnD,CAAC;IACD,IAAIC,UAAU,GAAG,SAAAA,CAAUC,MAAM,EAAE;MACjC,IAAIC,UAAU,GAAG,EAAE;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIP,KAAK,GAAGK,MAAM,CAACE,CAAC,CAAC;QACrB,IAAIE,cAAc,GAAG,KAAK;QAC1B,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;UAC7B,IACEA,KAAK,CAACU,IAAI,KAAK,KAAK,IACpBV,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,IAChBD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACS,IAAI,KAAK,KAAK,EAC/B;YACA;YACA,IAAIV,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,KAAK,IAAI,EAAE;cAChD;cACA,IACEK,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBF,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACG,OAAO,KACvCZ,cAAc,CAACC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC,CAAC,EAC7C;gBACA;gBACAK,UAAU,CAACM,GAAG,CAAC,CAAC;cAClB;YACF,CAAC,MAAM;cACL,IAAIZ,KAAK,CAACC,OAAO,CAACD,KAAK,CAACC,OAAO,CAACO,MAAM,GAAG,CAAC,CAAC,CAACP,OAAO,KAAK,IAAI,EAAE;gBAC5D;cAAA,CACD,MAAM;gBACL;gBACAK,UAAU,CAACO,IAAI,CAAC;kBACdF,OAAO,EAAEZ,cAAc,CAACC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC,CAAC;kBACpDa,YAAY,EAAE;gBAChB,CAAC,CAAC;cACJ;YACF;UACF,CAAC,MAAM,IACLR,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBR,KAAK,CAACU,IAAI,KAAK,aAAa,IAC5BV,KAAK,CAACC,OAAO,KAAK,GAAG;UAAI;UACxB,CAACI,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,IACbF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,KAAK,aAAa,IACpCL,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACN,OAAO,KAAK,GAAG,CAAC,KAC/B,CAACI,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,IACbF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,KAAK,YAAY,IACnCL,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACN,OAAO,KAAK,GAAG,CAAC,EAChC;YACA;YACAK,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,EAAE;UAClD,CAAC,MAAM,IACLR,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBF,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,GAAG,CAAC,IAClDd,KAAK,CAACU,IAAI,KAAK,aAAa,IAC5BV,KAAK,CAACC,OAAO,KAAK,GAAG,EACrB;YACA;YACAK,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,EAAE;UAClD,CAAC,MAAM,IAAId,KAAK,CAACU,IAAI,KAAK,SAAS,EAAE;YACnCD,cAAc,GAAG,IAAI;UACvB;QACF;QACA,IAAIA,cAAc,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;UAC/C,IACEM,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBF,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,KAAK,CAAC,EACpD;YACA;YACA;YACA,IAAIC,SAAS,GAAGhB,cAAc,CAACC,KAAK,CAAC,EAAC;YACtC,IACEO,CAAC,GAAGF,MAAM,CAACG,MAAM,GAAG,CAAC,KACpB,OAAOH,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,IAChCF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,KAAK,YAAY,CAAC,EACtC;cACAK,SAAS,IAAIhB,cAAc,CAACM,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1CF,MAAM,CAACW,MAAM,CAACT,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACzB;YACA,IACEA,CAAC,GAAG,CAAC,KACJ,OAAOF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,IAChCF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,KAAK,YAAY,CAAC,EACtC;cACAK,SAAS,GAAGhB,cAAc,CAACM,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGQ,SAAS;cACrDV,MAAM,CAACW,MAAM,CAACT,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cACvBA,CAAC,EAAE;YACL;YACA,IAAI,OAAO,CAACU,IAAI,CAACF,SAAS,CAAC,EAAE;cAC3BV,MAAM,CAACE,CAAC,CAAC,GAAGQ,SAAS;YACvB,CAAC,MAAM;cACLV,MAAM,CAACE,CAAC,CAAC,GAAG,IAAI3B,KAAK,CAACsC,KAAK,CACzB,YAAY,EACZH,SAAS,EACT,IAAI,EACJA,SACF,CAAC;YACH;UACF;QACF;QACA,IAAIf,KAAK,CAACC,OAAO,IAAI,OAAOD,KAAK,CAACC,OAAO,KAAK,QAAQ,EAAE;UACtDG,UAAU,CAACJ,KAAK,CAACC,OAAO,CAAC;QAC3B;MACF;IACF,CAAC;IACDrB,KAAK,CAACuC,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/C,IAAIA,GAAG,CAACC,QAAQ,KAAK,QAAQ,EAAE;QAC7B;MACF;MACAlB,UAAU,CAACiB,GAAG,CAAChB,MAAM,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,EAAEzB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}