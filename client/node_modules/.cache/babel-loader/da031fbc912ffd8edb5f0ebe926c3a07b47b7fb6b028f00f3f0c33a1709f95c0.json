{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: VBScript\nDescription: VBScript (\"Microsoft Visual Basic Scripting Edition\") is an Active Scripting language developed by Microsoft that is modeled on Visual Basic.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction vbscript(hljs) {\n  const BUILT_IN_FUNCTIONS = ('lcase month vartype instrrev ubound setlocale getobject rgb getref string ' + 'weekdayname rnd dateadd monthname now day minute isarray cbool round formatcurrency ' + 'conversions csng timevalue second year space abs clng timeserial fixs len asc ' + 'isempty maths dateserial atn timer isobject filter weekday datevalue ccur isdate ' + 'instr datediff formatdatetime replace isnull right sgn array snumeric log cdbl hex ' + 'chr lbound msgbox ucase getlocale cos cdate cbyte rtrim join hour oct typename trim ' + 'strcomp int createobject loadpicture tan formatnumber mid ' + 'split  cint sin datepart ltrim sqr ' + 'time derived eval date formatpercent exp inputbox left ascw ' + 'chrw regexp cstr err').split(\" \");\n  const BUILT_IN_OBJECTS = [\"server\", \"response\", \"request\",\n  // take no arguments so can be called without ()\n  \"scriptengine\", \"scriptenginebuildversion\", \"scriptengineminorversion\", \"scriptenginemajorversion\"];\n  const BUILT_IN_CALL = {\n    begin: concat(either(...BUILT_IN_FUNCTIONS), \"\\\\s*\\\\(\"),\n    // relevance 0 because this is acting as a beginKeywords really\n    relevance: 0,\n    keywords: {\n      built_in: BUILT_IN_FUNCTIONS\n    }\n  };\n  return {\n    name: 'VBScript',\n    aliases: ['vbs'],\n    case_insensitive: true,\n    keywords: {\n      keyword: 'call class const dim do loop erase execute executeglobal exit for each next function ' + 'if then else on error option explicit new private property let get public randomize ' + 'redim rem select case set stop sub while wend with end to elseif is or xor and not ' + 'class_initialize class_terminate default preserve in me byval byref step resume goto',\n      built_in: BUILT_IN_OBJECTS,\n      literal: 'true false null nothing empty'\n    },\n    illegal: '//',\n    contains: [BUILT_IN_CALL, hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      contains: [{\n        begin: '\"\"'\n      }]\n    }), hljs.COMMENT(/'/, /$/, {\n      relevance: 0\n    }), hljs.C_NUMBER_MODE]\n  };\n}\nmodule.exports = vbscript;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "either", "vbscript", "hljs", "BUILT_IN_FUNCTIONS", "split", "BUILT_IN_OBJECTS", "BUILT_IN_CALL", "begin", "relevance", "keywords", "built_in", "name", "aliases", "case_insensitive", "keyword", "literal", "illegal", "contains", "inherit", "QUOTE_STRING_MODE", "COMMENT", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/vbscript.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: VBScript\nDescription: VBScript (\"Microsoft Visual Basic Scripting Edition\") is an Active Scripting language developed by Microsoft that is modeled on Visual Basic.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction vbscript(hljs) {\n  const BUILT_IN_FUNCTIONS = ('lcase month vartype instrrev ubound setlocale getobject rgb getref string ' +\n  'weekdayname rnd dateadd monthname now day minute isarray cbool round formatcurrency ' +\n  'conversions csng timevalue second year space abs clng timeserial fixs len asc ' +\n  'isempty maths dateserial atn timer isobject filter weekday datevalue ccur isdate ' +\n  'instr datediff formatdatetime replace isnull right sgn array snumeric log cdbl hex ' +\n  'chr lbound msgbox ucase getlocale cos cdate cbyte rtrim join hour oct typename trim ' +\n  'strcomp int createobject loadpicture tan formatnumber mid ' +\n  'split  cint sin datepart ltrim sqr ' +\n  'time derived eval date formatpercent exp inputbox left ascw ' +\n  'chrw regexp cstr err').split(\" \");\n  const BUILT_IN_OBJECTS = [\n    \"server\",\n    \"response\",\n    \"request\",\n    // take no arguments so can be called without ()\n    \"scriptengine\",\n    \"scriptenginebuildversion\",\n    \"scriptengineminorversion\",\n    \"scriptenginemajorversion\"\n  ];\n\n  const BUILT_IN_CALL = {\n    begin: concat(either(...BUILT_IN_FUNCTIONS), \"\\\\s*\\\\(\"),\n    // relevance 0 because this is acting as a beginKeywords really\n    relevance:0,\n    keywords: {\n      built_in: BUILT_IN_FUNCTIONS\n    }\n  };\n\n  return {\n    name: 'VBScript',\n    aliases: ['vbs'],\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        'call class const dim do loop erase execute executeglobal exit for each next function ' +\n        'if then else on error option explicit new private property let get public randomize ' +\n        'redim rem select case set stop sub while wend with end to elseif is or xor and not ' +\n        'class_initialize class_terminate default preserve in me byval byref step resume goto',\n      built_in: BUILT_IN_OBJECTS,\n      literal:\n        'true false null nothing empty'\n    },\n    illegal: '//',\n    contains: [\n      BUILT_IN_CALL,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {contains: [{begin: '\"\"'}]}),\n      hljs.COMMENT(\n        /'/,\n        /$/,\n        {\n          relevance: 0\n        }\n      ),\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = vbscript;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASK,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,kBAAkB,GAAG,CAAC,4EAA4E,GACxG,sFAAsF,GACtF,gFAAgF,GAChF,mFAAmF,GACnF,qFAAqF,GACrF,sFAAsF,GACtF,4DAA4D,GAC5D,qCAAqC,GACrC,8DAA8D,GAC9D,sBAAsB,EAAEC,KAAK,CAAC,GAAG,CAAC;EAClC,MAAMC,gBAAgB,GAAG,CACvB,QAAQ,EACR,UAAU,EACV,SAAS;EACT;EACA,cAAc,EACd,0BAA0B,EAC1B,0BAA0B,EAC1B,0BAA0B,CAC3B;EAED,MAAMC,aAAa,GAAG;IACpBC,KAAK,EAAEb,MAAM,CAACM,MAAM,CAAC,GAAGG,kBAAkB,CAAC,EAAE,SAAS,CAAC;IACvD;IACAK,SAAS,EAAC,CAAC;IACXC,QAAQ,EAAE;MACRC,QAAQ,EAAEP;IACZ;EACF,CAAC;EAED,OAAO;IACLQ,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,gBAAgB,EAAE,IAAI;IACtBJ,QAAQ,EAAE;MACRK,OAAO,EACL,uFAAuF,GACvF,sFAAsF,GACtF,qFAAqF,GACrF,sFAAsF;MACxFJ,QAAQ,EAAEL,gBAAgB;MAC1BU,OAAO,EACL;IACJ,CAAC;IACDC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRX,aAAa,EACbJ,IAAI,CAACgB,OAAO,CAAChB,IAAI,CAACiB,iBAAiB,EAAE;MAACF,QAAQ,EAAE,CAAC;QAACV,KAAK,EAAE;MAAI,CAAC;IAAC,CAAC,CAAC,EACjEL,IAAI,CAACkB,OAAO,CACV,GAAG,EACH,GAAG,EACH;MACEZ,SAAS,EAAE;IACb,CACF,CAAC,EACDN,IAAI,CAACmB,aAAa;EAEtB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGtB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}