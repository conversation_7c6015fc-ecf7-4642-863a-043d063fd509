{"ast": null, "code": "/*\nLanguage: HSP\nAuthor: prince <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Hot_Soup_Processor\nCategory: scripting\n*/\n\nfunction hsp(hljs) {\n  return {\n    name: 'HSP',\n    case_insensitive: true,\n    keywords: {\n      $pattern: /[\\w._]+/,\n      keyword: 'goto gosub return break repeat loop continue wait await dim sdim foreach dimtype dup dupptr end stop newmod delmod mref run exgoto on mcall assert logmes newlab resume yield onexit onerror onkey onclick oncmd exist delete mkdir chdir dirlist bload bsave bcopy memfile if else poke wpoke lpoke getstr chdpm memexpand memcpy memset notesel noteadd notedel noteload notesave randomize noteunsel noteget split strrep setease button chgdisp exec dialog mmload mmplay mmstop mci pset pget syscolor mes print title pos circle cls font sysfont objsize picload color palcolor palette redraw width gsel gcopy gzoom gmode bmpsave hsvcolor getkey listbox chkbox combox input mesbox buffer screen bgscr mouse objsel groll line clrobj boxf objprm objmode stick grect grotate gsquare gradf objimage objskip objenable celload celdiv celput newcom querycom delcom cnvstow comres axobj winobj sendmsg comevent comevarg sarrayconv callfunc cnvwtos comevdisp libptr system hspstat hspver stat cnt err strsize looplev sublev iparam wparam lparam refstr refdval int rnd strlen length length2 length3 length4 vartype gettime peek wpeek lpeek varptr varuse noteinfo instr abs limit getease str strmid strf getpath strtrim sin cos tan atan sqrt double absf expf logf limitf powf geteasef mousex mousey mousew hwnd hinstance hdc ginfo objinfo dirinfo sysinfo thismod __hspver__ __hsp30__ __date__ __time__ __line__ __file__ _debug __hspdef__ and or xor not screen_normal screen_palette screen_hide screen_fixedsize screen_tool screen_frame gmode_gdi gmode_mem gmode_rgb0 gmode_alpha gmode_rgb0alpha gmode_add gmode_sub gmode_pixela ginfo_mx ginfo_my ginfo_act ginfo_sel ginfo_wx1 ginfo_wy1 ginfo_wx2 ginfo_wy2 ginfo_vx ginfo_vy ginfo_sizex ginfo_sizey ginfo_winx ginfo_winy ginfo_mesx ginfo_mesy ginfo_r ginfo_g ginfo_b ginfo_paluse ginfo_dispx ginfo_dispy ginfo_cx ginfo_cy ginfo_intid ginfo_newid ginfo_sx ginfo_sy objinfo_mode objinfo_bmscr objinfo_hwnd notemax notesize dir_cur dir_exe dir_win dir_sys dir_cmdline dir_desktop dir_mydoc dir_tv font_normal font_bold font_italic font_underline font_strikeout font_antialias objmode_normal objmode_guifont objmode_usefont gsquare_grad msgothic msmincho do until while wend for next _break _continue switch case default swbreak swend ddim ldim alloc m_pi rad2deg deg2rad ease_linear ease_quad_in ease_quad_out ease_quad_inout ease_cubic_in ease_cubic_out ease_cubic_inout ease_quartic_in ease_quartic_out ease_quartic_inout ease_bounce_in ease_bounce_out ease_bounce_inout ease_shake_in ease_shake_out ease_shake_inout ease_loop'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, {\n      // multi-line string\n      className: 'string',\n      begin: /\\{\"/,\n      end: /\"\\}/,\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, hljs.COMMENT(';', '$', {\n      relevance: 0\n    }), {\n      // pre-processor\n      className: 'meta',\n      begin: '#',\n      end: '$',\n      keywords: {\n        'meta-keyword': 'addion cfunc cmd cmpopt comfunc const defcfunc deffunc define else endif enum epack func global if ifdef ifndef include modcfunc modfunc modinit modterm module pack packopt regcmd runtime undef usecom uselib'\n      },\n      contains: [hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        className: 'meta-string'\n      }), hljs.NUMBER_MODE, hljs.C_NUMBER_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      // label\n      className: 'symbol',\n      begin: '^\\\\*(\\\\w+|@)'\n    }, hljs.NUMBER_MODE, hljs.C_NUMBER_MODE]\n  };\n}\nmodule.exports = hsp;", "map": {"version": 3, "names": ["hsp", "hljs", "name", "case_insensitive", "keywords", "$pattern", "keyword", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "className", "begin", "end", "BACKSLASH_ESCAPE", "COMMENT", "relevance", "inherit", "NUMBER_MODE", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/hsp.js"], "sourcesContent": ["/*\nLanguage: HSP\nAuthor: prince <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Hot_Soup_Processor\nCategory: scripting\n*/\n\nfunction hsp(hljs) {\n  return {\n    name: 'HSP',\n    case_insensitive: true,\n    keywords: {\n      $pattern: /[\\w._]+/,\n      keyword: 'goto gosub return break repeat loop continue wait await dim sdim foreach dimtype dup dupptr end stop newmod delmod mref run exgoto on mcall assert logmes newlab resume yield onexit onerror onkey onclick oncmd exist delete mkdir chdir dirlist bload bsave bcopy memfile if else poke wpoke lpoke getstr chdpm memexpand memcpy memset notesel noteadd notedel noteload notesave randomize noteunsel noteget split strrep setease button chgdisp exec dialog mmload mmplay mmstop mci pset pget syscolor mes print title pos circle cls font sysfont objsize picload color palcolor palette redraw width gsel gcopy gzoom gmode bmpsave hsvcolor getkey listbox chkbox combox input mesbox buffer screen bgscr mouse objsel groll line clrobj boxf objprm objmode stick grect grotate gsquare gradf objimage objskip objenable celload celdiv celput newcom querycom delcom cnvstow comres axobj winobj sendmsg comevent comevarg sarrayconv callfunc cnvwtos comevdisp libptr system hspstat hspver stat cnt err strsize looplev sublev iparam wparam lparam refstr refdval int rnd strlen length length2 length3 length4 vartype gettime peek wpeek lpeek varptr varuse noteinfo instr abs limit getease str strmid strf getpath strtrim sin cos tan atan sqrt double absf expf logf limitf powf geteasef mousex mousey mousew hwnd hinstance hdc ginfo objinfo dirinfo sysinfo thismod __hspver__ __hsp30__ __date__ __time__ __line__ __file__ _debug __hspdef__ and or xor not screen_normal screen_palette screen_hide screen_fixedsize screen_tool screen_frame gmode_gdi gmode_mem gmode_rgb0 gmode_alpha gmode_rgb0alpha gmode_add gmode_sub gmode_pixela ginfo_mx ginfo_my ginfo_act ginfo_sel ginfo_wx1 ginfo_wy1 ginfo_wx2 ginfo_wy2 ginfo_vx ginfo_vy ginfo_sizex ginfo_sizey ginfo_winx ginfo_winy ginfo_mesx ginfo_mesy ginfo_r ginfo_g ginfo_b ginfo_paluse ginfo_dispx ginfo_dispy ginfo_cx ginfo_cy ginfo_intid ginfo_newid ginfo_sx ginfo_sy objinfo_mode objinfo_bmscr objinfo_hwnd notemax notesize dir_cur dir_exe dir_win dir_sys dir_cmdline dir_desktop dir_mydoc dir_tv font_normal font_bold font_italic font_underline font_strikeout font_antialias objmode_normal objmode_guifont objmode_usefont gsquare_grad msgothic msmincho do until while wend for next _break _continue switch case default swbreak swend ddim ldim alloc m_pi rad2deg deg2rad ease_linear ease_quad_in ease_quad_out ease_quad_inout ease_cubic_in ease_cubic_out ease_cubic_inout ease_quartic_in ease_quartic_out ease_quartic_inout ease_bounce_in ease_bounce_out ease_bounce_inout ease_shake_in ease_shake_out ease_shake_inout ease_loop'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n\n      {\n        // multi-line string\n        className: 'string',\n        begin: /\\{\"/,\n        end: /\"\\}/,\n        contains: [hljs.BACKSLASH_ESCAPE]\n      },\n\n      hljs.COMMENT(';', '$', {\n        relevance: 0\n      }),\n\n      {\n        // pre-processor\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'addion cfunc cmd cmpopt comfunc const defcfunc deffunc define else endif enum epack func global if ifdef ifndef include modcfunc modfunc modinit modterm module pack packopt regcmd runtime undef usecom uselib'\n        },\n        contains: [\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {\n            className: 'meta-string'\n          }),\n          hljs.NUMBER_MODE,\n          hljs.C_NUMBER_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n\n      {\n        // label\n        className: 'symbol',\n        begin: '^\\\\*(\\\\w+|@)'\n      },\n\n      hljs.NUMBER_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = hsp;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE,CACRN,IAAI,CAACO,mBAAmB,EACxBP,IAAI,CAACQ,oBAAoB,EACzBR,IAAI,CAACS,iBAAiB,EACtBT,IAAI,CAACU,gBAAgB,EAErB;MACE;MACAC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,KAAK;MACVP,QAAQ,EAAE,CAACN,IAAI,CAACc,gBAAgB;IAClC,CAAC,EAEDd,IAAI,CAACe,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;MACrBC,SAAS,EAAE;IACb,CAAC,CAAC,EAEF;MACE;MACAL,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRV,QAAQ,EAAE;QACR,cAAc,EAAE;MAClB,CAAC;MACDG,QAAQ,EAAE,CACRN,IAAI,CAACiB,OAAO,CAACjB,IAAI,CAACS,iBAAiB,EAAE;QACnCE,SAAS,EAAE;MACb,CAAC,CAAC,EACFX,IAAI,CAACkB,WAAW,EAChBlB,IAAI,CAACmB,aAAa,EAClBnB,IAAI,CAACO,mBAAmB,EACxBP,IAAI,CAACQ,oBAAoB;IAE7B,CAAC,EAED;MACE;MACAG,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC,EAEDZ,IAAI,CAACkB,WAAW,EAChBlB,IAAI,CAACmB,aAAa;EAEtB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGtB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}