{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: TOML, also INI\nDescription: TOML aims to be a minimal configuration file format that's easy to read due to obvious semantics.\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://github.com/toml-lang/toml\n*/\n\nfunction ini(hljs) {\n  const NUMBERS = {\n    className: 'number',\n    relevance: 0,\n    variants: [{\n      begin: /([+-]+)?[\\d]+_[\\d_]+/\n    }, {\n      begin: hljs.NUMBER_RE\n    }]\n  };\n  const COMMENTS = hljs.COMMENT();\n  COMMENTS.variants = [{\n    begin: /;/,\n    end: /$/\n  }, {\n    begin: /#/,\n    end: /$/\n  }];\n  const VARIABLES = {\n    className: 'variable',\n    variants: [{\n      begin: /\\$[\\w\\d\"][\\w\\d_]*/\n    }, {\n      begin: /\\$\\{(.*?)\\}/\n    }]\n  };\n  const LITERALS = {\n    className: 'literal',\n    begin: /\\bon|off|true|false|yes|no\\b/\n  };\n  const STRINGS = {\n    className: \"string\",\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [{\n      begin: \"'''\",\n      end: \"'''\",\n      relevance: 10\n    }, {\n      begin: '\"\"\"',\n      end: '\"\"\"',\n      relevance: 10\n    }, {\n      begin: '\"',\n      end: '\"'\n    }, {\n      begin: \"'\",\n      end: \"'\"\n    }]\n  };\n  const ARRAY = {\n    begin: /\\[/,\n    end: /\\]/,\n    contains: [COMMENTS, LITERALS, VARIABLES, STRINGS, NUMBERS, 'self'],\n    relevance: 0\n  };\n  const BARE_KEY = /[A-Za-z0-9_-]+/;\n  const QUOTED_KEY_DOUBLE_QUOTE = /\"(\\\\\"|[^\"])*\"/;\n  const QUOTED_KEY_SINGLE_QUOTE = /'[^']*'/;\n  const ANY_KEY = either(BARE_KEY, QUOTED_KEY_DOUBLE_QUOTE, QUOTED_KEY_SINGLE_QUOTE);\n  const DOTTED_KEY = concat(ANY_KEY, '(\\\\s*\\\\.\\\\s*', ANY_KEY, ')*', lookahead(/\\s*=\\s*[^#\\s]/));\n  return {\n    name: 'TOML, also INI',\n    aliases: ['toml'],\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [COMMENTS, {\n      className: 'section',\n      begin: /\\[+/,\n      end: /\\]+/\n    }, {\n      begin: DOTTED_KEY,\n      className: 'attr',\n      starts: {\n        end: /$/,\n        contains: [COMMENTS, ARRAY, LITERALS, VARIABLES, STRINGS, NUMBERS]\n      }\n    }]\n  };\n}\nmodule.exports = ini;", "map": {"version": 3, "names": ["source", "re", "<PERSON><PERSON><PERSON>", "concat", "args", "joined", "map", "x", "join", "either", "ini", "hljs", "NUMBERS", "className", "relevance", "variants", "begin", "NUMBER_RE", "COMMENTS", "COMMENT", "end", "VARIABLES", "LITERALS", "STRINGS", "contains", "BACKSLASH_ESCAPE", "ARRAY", "BARE_KEY", "QUOTED_KEY_DOUBLE_QUOTE", "QUOTED_KEY_SINGLE_QUOTE", "ANY_KEY", "DOTTED_KEY", "name", "aliases", "case_insensitive", "illegal", "starts", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/ini.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: TOML, also INI\nDescription: TOML aims to be a minimal configuration file format that's easy to read due to obvious semantics.\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://github.com/toml-lang/toml\n*/\n\nfunction ini(hljs) {\n  const NUMBERS = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        begin: /([+-]+)?[\\d]+_[\\d_]+/\n      },\n      {\n        begin: hljs.NUMBER_RE\n      }\n    ]\n  };\n  const COMMENTS = hljs.COMMENT();\n  COMMENTS.variants = [\n    {\n      begin: /;/,\n      end: /$/\n    },\n    {\n      begin: /#/,\n      end: /$/\n    }\n  ];\n  const VARIABLES = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$[\\w\\d\"][\\w\\d_]*/\n      },\n      {\n        begin: /\\$\\{(.*?)\\}/\n      }\n    ]\n  };\n  const LITERALS = {\n    className: 'literal',\n    begin: /\\bon|off|true|false|yes|no\\b/\n  };\n  const STRINGS = {\n    className: \"string\",\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [\n      {\n        begin: \"'''\",\n        end: \"'''\",\n        relevance: 10\n      },\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"',\n        relevance: 10\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: \"'\",\n        end: \"'\"\n      }\n    ]\n  };\n  const ARRAY = {\n    begin: /\\[/,\n    end: /\\]/,\n    contains: [\n      COMMENTS,\n      LITERALS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      'self'\n    ],\n    relevance: 0\n  };\n\n  const BARE_KEY = /[A-Za-z0-9_-]+/;\n  const QUOTED_KEY_DOUBLE_QUOTE = /\"(\\\\\"|[^\"])*\"/;\n  const QUOTED_KEY_SINGLE_QUOTE = /'[^']*'/;\n  const ANY_KEY = either(\n    BARE_KEY, QUOTED_KEY_DOUBLE_QUOTE, QUOTED_KEY_SINGLE_QUOTE\n  );\n  const DOTTED_KEY = concat(\n    ANY_KEY, '(\\\\s*\\\\.\\\\s*', ANY_KEY, ')*',\n    lookahead(/\\s*=\\s*[^#\\s]/)\n  );\n\n  return {\n    name: 'TOML, also INI',\n    aliases: ['toml'],\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [\n      COMMENTS,\n      {\n        className: 'section',\n        begin: /\\[+/,\n        end: /\\]+/\n      },\n      {\n        begin: DOTTED_KEY,\n        className: 'attr',\n        starts: {\n          end: /$/,\n          contains: [\n            COMMENTS,\n            ARRAY,\n            LITERALS,\n            VARIABLES,\n            STRINGS,\n            NUMBERS\n          ]\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = ini;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOE,MAAM,CAAC,KAAK,EAAEF,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAC,GAAGL,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASK,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAEL,IAAI,CAACM;IACd,CAAC;EAEL,CAAC;EACD,MAAMC,QAAQ,GAAGP,IAAI,CAACQ,OAAO,CAAC,CAAC;EAC/BD,QAAQ,CAACH,QAAQ,GAAG,CAClB;IACEC,KAAK,EAAE,GAAG;IACVI,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,KAAK,EAAE,GAAG;IACVI,GAAG,EAAE;EACP,CAAC,CACF;EACD,MAAMC,SAAS,GAAG;IAChBR,SAAS,EAAE,UAAU;IACrBE,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMM,QAAQ,GAAG;IACfT,SAAS,EAAE,SAAS;IACpBG,KAAK,EAAE;EACT,CAAC;EACD,MAAMO,OAAO,GAAG;IACdV,SAAS,EAAE,QAAQ;IACnBW,QAAQ,EAAE,CAACb,IAAI,CAACc,gBAAgB,CAAC;IACjCV,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,KAAK;MACZI,GAAG,EAAE,KAAK;MACVN,SAAS,EAAE;IACb,CAAC,EACD;MACEE,KAAK,EAAE,KAAK;MACZI,GAAG,EAAE,KAAK;MACVN,SAAS,EAAE;IACb,CAAC,EACD;MACEE,KAAK,EAAE,GAAG;MACVI,GAAG,EAAE;IACP,CAAC,EACD;MACEJ,KAAK,EAAE,GAAG;MACVI,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EACD,MAAMM,KAAK,GAAG;IACZV,KAAK,EAAE,IAAI;IACXI,GAAG,EAAE,IAAI;IACTI,QAAQ,EAAE,CACRN,QAAQ,EACRI,QAAQ,EACRD,SAAS,EACTE,OAAO,EACPX,OAAO,EACP,MAAM,CACP;IACDE,SAAS,EAAE;EACb,CAAC;EAED,MAAMa,QAAQ,GAAG,gBAAgB;EACjC,MAAMC,uBAAuB,GAAG,eAAe;EAC/C,MAAMC,uBAAuB,GAAG,SAAS;EACzC,MAAMC,OAAO,GAAGrB,MAAM,CACpBkB,QAAQ,EAAEC,uBAAuB,EAAEC,uBACrC,CAAC;EACD,MAAME,UAAU,GAAG5B,MAAM,CACvB2B,OAAO,EAAE,cAAc,EAAEA,OAAO,EAAE,IAAI,EACtC5B,SAAS,CAAC,eAAe,CAC3B,CAAC;EAED,OAAO;IACL8B,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,CAAC,MAAM,CAAC;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,IAAI;IACbX,QAAQ,EAAE,CACRN,QAAQ,EACR;MACEL,SAAS,EAAE,SAAS;MACpBG,KAAK,EAAE,KAAK;MACZI,GAAG,EAAE;IACP,CAAC,EACD;MACEJ,KAAK,EAAEe,UAAU;MACjBlB,SAAS,EAAE,MAAM;MACjBuB,MAAM,EAAE;QACNhB,GAAG,EAAE,GAAG;QACRI,QAAQ,EAAE,CACRN,QAAQ,EACRQ,KAAK,EACLJ,QAAQ,EACRD,SAAS,EACTE,OAAO,EACPX,OAAO;MAEX;IACF,CAAC;EAEL,CAAC;AACH;AAEAyB,MAAM,CAACC,OAAO,GAAG5B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}