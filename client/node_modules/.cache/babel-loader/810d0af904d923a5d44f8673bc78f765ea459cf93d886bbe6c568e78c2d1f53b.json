{"ast": null, "code": "/*\nLanguage: TP\nAuthor: <PERSON> <<EMAIL>>\nDescription: FANUC TP programming language (TPP).\n*/\n\nfunction tp(hljs) {\n  const TPID = {\n    className: 'number',\n    begin: '[1-9][0-9]*',\n    /* no leading zeros */\n    relevance: 0\n  };\n  const TPLABEL = {\n    className: 'symbol',\n    begin: ':[^\\\\]]+'\n  };\n  const TPDATA = {\n    className: 'built_in',\n    begin: '(AR|P|PAYLOAD|PR|R|SR|RSR|LBL|VR|UALM|MESSAGE|UTOOL|UFRAME|TIMER|' + 'TIMER_OVERFLOW|JOINT_MAX_SPEED|RESUME_PROG|DIAG_REC)\\\\[',\n    end: '\\\\]',\n    contains: ['self', TPID, TPLABEL]\n  };\n  const TPIO = {\n    className: 'built_in',\n    begin: '(AI|AO|DI|DO|F|RI|RO|UI|UO|GI|GO|SI|SO)\\\\[',\n    end: '\\\\]',\n    contains: ['self', TPID, hljs.QUOTE_STRING_MODE, /* for pos section at bottom */\n    TPLABEL]\n  };\n  return {\n    name: 'TP',\n    keywords: {\n      keyword: 'ABORT ACC ADJUST AND AP_LD BREAK CALL CNT COL CONDITION CONFIG DA DB ' + 'DIV DETECT ELSE END ENDFOR ERR_NUM ERROR_PROG FINE FOR GP GUARD INC ' + 'IF JMP LINEAR_MAX_SPEED LOCK MOD MONITOR OFFSET Offset OR OVERRIDE ' + 'PAUSE PREG PTH RT_LD RUN SELECT SKIP Skip TA TB TO TOOL_OFFSET ' + 'Tool_Offset UF UT UFRAME_NUM UTOOL_NUM UNLOCK WAIT X Y Z W P R STRLEN ' + 'SUBSTR FINDSTR VOFFSET PROG ATTR MN POS',\n      literal: 'ON OFF max_speed LPOS JPOS ENABLE DISABLE START STOP RESET'\n    },\n    contains: [TPDATA, TPIO, {\n      className: 'keyword',\n      begin: '/(PROG|ATTR|MN|POS|END)\\\\b'\n    }, {\n      /* this is for cases like ,CALL */\n      className: 'keyword',\n      begin: '(CALL|RUN|POINT_LOGIC|LBL)\\\\b'\n    }, {\n      /* this is for cases like CNT100 where the default lexemes do not\n       * separate the keyword and the number */\n      className: 'keyword',\n      begin: '\\\\b(ACC|CNT|Skip|Offset|PSPD|RT_LD|AP_LD|Tool_Offset)'\n    }, {\n      /* to catch numbers that do not have a word boundary on the left */\n      className: 'number',\n      begin: '\\\\d+(sec|msec|mm/sec|cm/min|inch/min|deg/sec|mm|in|cm)?\\\\b',\n      relevance: 0\n    }, hljs.COMMENT('//', '[;$]'), hljs.COMMENT('!', '[;$]'), hljs.COMMENT('--eg:', '$'), hljs.QUOTE_STRING_MODE, {\n      className: 'string',\n      begin: '\\'',\n      end: '\\''\n    }, hljs.C_NUMBER_MODE, {\n      className: 'variable',\n      begin: '\\\\$[A-Za-z0-9_]+'\n    }]\n  };\n}\nmodule.exports = tp;", "map": {"version": 3, "names": ["tp", "hljs", "TPID", "className", "begin", "relevance", "TPLABEL", "TPDATA", "end", "contains", "TPIO", "QUOTE_STRING_MODE", "name", "keywords", "keyword", "literal", "COMMENT", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/tp.js"], "sourcesContent": ["/*\nLanguage: TP\nAuthor: <PERSON> <<EMAIL>>\nDescription: FANUC TP programming language (TPP).\n*/\n\nfunction tp(hljs) {\n  const TPID = {\n    className: 'number',\n    begin: '[1-9][0-9]*', /* no leading zeros */\n    relevance: 0\n  };\n  const TPLABEL = {\n    className: 'symbol',\n    begin: ':[^\\\\]]+'\n  };\n  const TPDATA = {\n    className: 'built_in',\n    begin: '(AR|P|PAYLOAD|PR|R|SR|RSR|LBL|VR|UALM|MESSAGE|UTOOL|UFRAME|TIMER|' +\n    'TIMER_OVERFLOW|JOINT_MAX_SPEED|RESUME_PROG|DIAG_REC)\\\\[',\n    end: '\\\\]',\n    contains: [\n      'self',\n      TPID,\n      TPLABEL\n    ]\n  };\n  const TPIO = {\n    className: 'built_in',\n    begin: '(AI|AO|DI|DO|F|RI|RO|UI|UO|GI|GO|SI|SO)\\\\[',\n    end: '\\\\]',\n    contains: [\n      'self',\n      TPID,\n      hljs.QUOTE_STRING_MODE, /* for pos section at bottom */\n      TPLABEL\n    ]\n  };\n\n  return {\n    name: 'TP',\n    keywords: {\n      keyword:\n        'ABORT ACC ADJUST AND AP_LD BREAK CALL CNT COL CONDITION CONFIG DA DB ' +\n        'DIV DETECT ELSE END ENDFOR ERR_NUM ERROR_PROG FINE FOR GP GUARD INC ' +\n        'IF JMP LINEAR_MAX_SPEED LOCK MOD MONITOR OFFSET Offset OR OVERRIDE ' +\n        'PAUSE PREG PTH RT_LD RUN SELECT SKIP Skip TA TB TO TOOL_OFFSET ' +\n        'Tool_Offset UF UT UFRAME_NUM UTOOL_NUM UNLOCK WAIT X Y Z W P R STRLEN ' +\n        'SUBSTR FINDSTR VOFFSET PROG ATTR MN POS',\n      literal:\n        'ON OFF max_speed LPOS JPOS ENABLE DISABLE START STOP RESET'\n    },\n    contains: [\n      TPDATA,\n      TPIO,\n      {\n        className: 'keyword',\n        begin: '/(PROG|ATTR|MN|POS|END)\\\\b'\n      },\n      {\n        /* this is for cases like ,CALL */\n        className: 'keyword',\n        begin: '(CALL|RUN|POINT_LOGIC|LBL)\\\\b'\n      },\n      {\n        /* this is for cases like CNT100 where the default lexemes do not\n         * separate the keyword and the number */\n        className: 'keyword',\n        begin: '\\\\b(ACC|CNT|Skip|Offset|PSPD|RT_LD|AP_LD|Tool_Offset)'\n      },\n      {\n        /* to catch numbers that do not have a word boundary on the left */\n        className: 'number',\n        begin: '\\\\d+(sec|msec|mm/sec|cm/min|inch/min|deg/sec|mm|in|cm)?\\\\b',\n        relevance: 0\n      },\n      hljs.COMMENT('//', '[;$]'),\n      hljs.COMMENT('!', '[;$]'),\n      hljs.COMMENT('--eg:', '$'),\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '\\''\n      },\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'variable',\n        begin: '\\\\$[A-Za-z0-9_]+'\n      }\n    ]\n  };\n}\n\nmodule.exports = tp;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,EAAEA,CAACC,IAAI,EAAE;EAChB,MAAMC,IAAI,GAAG;IACXC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,aAAa;IAAE;IACtBC,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,OAAO,GAAG;IACdH,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMG,MAAM,GAAG;IACbJ,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,mEAAmE,GAC1E,yDAAyD;IACzDI,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE,CACR,MAAM,EACNP,IAAI,EACJI,OAAO;EAEX,CAAC;EACD,MAAMI,IAAI,GAAG;IACXP,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,4CAA4C;IACnDI,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE,CACR,MAAM,EACNP,IAAI,EACJD,IAAI,CAACU,iBAAiB,EAAE;IACxBL,OAAO;EAEX,CAAC;EAED,OAAO;IACLM,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE;MACRC,OAAO,EACL,uEAAuE,GACvE,sEAAsE,GACtE,qEAAqE,GACrE,iEAAiE,GACjE,wEAAwE,GACxE,yCAAyC;MAC3CC,OAAO,EACL;IACJ,CAAC;IACDN,QAAQ,EAAE,CACRF,MAAM,EACNG,IAAI,EACJ;MACEP,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAD,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACD;MACE;AACR;MACQD,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAD,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,4DAA4D;MACnEC,SAAS,EAAE;IACb,CAAC,EACDJ,IAAI,CAACe,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAC1Bf,IAAI,CAACe,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EACzBf,IAAI,CAACe,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAC1Bf,IAAI,CAACU,iBAAiB,EACtB;MACER,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,IAAI;MACXI,GAAG,EAAE;IACP,CAAC,EACDP,IAAI,CAACgB,aAAa,EAClB;MACEd,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAc,MAAM,CAACC,OAAO,GAAGnB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}