{"ast": null, "code": "'use strict';\n\nvar find = require('property-information/find');\nvar normalize = require('property-information/normalize');\nvar parseSelector = require('hast-util-parse-selector');\nvar spaces = require('space-separated-tokens').parse;\nvar commas = require('comma-separated-tokens').parse;\nmodule.exports = factory;\nvar own = {}.hasOwnProperty;\nfunction factory(schema, defaultTagName, caseSensitive) {\n  var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null;\n  return h;\n\n  // Hyperscript compatible DSL for creating virtual hast trees.\n  function h(selector, properties) {\n    var node = parseSelector(selector, defaultTagName);\n    var children = Array.prototype.slice.call(arguments, 2);\n    var name = node.tagName.toLowerCase();\n    var property;\n    node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name;\n    if (properties && isChildren(properties, node)) {\n      children.unshift(properties);\n      properties = null;\n    }\n    if (properties) {\n      for (property in properties) {\n        addProperty(node.properties, property, properties[property]);\n      }\n    }\n    addChild(node.children, children);\n    if (node.tagName === 'template') {\n      node.content = {\n        type: 'root',\n        children: node.children\n      };\n      node.children = [];\n    }\n    return node;\n  }\n  function addProperty(properties, key, value) {\n    var info;\n    var property;\n    var result;\n\n    // Ignore nullish and NaN values.\n    if (value === null || value === undefined || value !== value) {\n      return;\n    }\n    info = find(schema, key);\n    property = info.property;\n    result = value;\n\n    // Handle list values.\n    if (typeof result === 'string') {\n      if (info.spaceSeparated) {\n        result = spaces(result);\n      } else if (info.commaSeparated) {\n        result = commas(result);\n      } else if (info.commaOrSpaceSeparated) {\n        result = spaces(commas(result).join(' '));\n      }\n    }\n\n    // Accept `object` on style.\n    if (property === 'style' && typeof value !== 'string') {\n      result = style(result);\n    }\n\n    // Class-names (which can be added both on the `selector` and here).\n    if (property === 'className' && properties.className) {\n      result = properties.className.concat(result);\n    }\n    properties[property] = parsePrimitives(info, property, result);\n  }\n}\nfunction isChildren(value, node) {\n  return typeof value === 'string' || 'length' in value || isNode(node.tagName, value);\n}\nfunction isNode(tagName, value) {\n  var type = value.type;\n  if (tagName === 'input' || !type || typeof type !== 'string') {\n    return false;\n  }\n  if (typeof value.children === 'object' && 'length' in value.children) {\n    return true;\n  }\n  type = type.toLowerCase();\n  if (tagName === 'button') {\n    return type !== 'menu' && type !== 'submit' && type !== 'reset' && type !== 'button';\n  }\n  return 'value' in value;\n}\nfunction addChild(nodes, value) {\n  var index;\n  var length;\n  if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({\n      type: 'text',\n      value: String(value)\n    });\n    return;\n  }\n  if (typeof value === 'object' && 'length' in value) {\n    index = -1;\n    length = value.length;\n    while (++index < length) {\n      addChild(nodes, value[index]);\n    }\n    return;\n  }\n  if (typeof value !== 'object' || !('type' in value)) {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`');\n  }\n  nodes.push(value);\n}\n\n// Parse a (list of) primitives.\nfunction parsePrimitives(info, name, value) {\n  var index;\n  var length;\n  var result;\n  if (typeof value !== 'object' || !('length' in value)) {\n    return parsePrimitive(info, name, value);\n  }\n  length = value.length;\n  index = -1;\n  result = [];\n  while (++index < length) {\n    result[index] = parsePrimitive(info, name, value[index]);\n  }\n  return result;\n}\n\n// Parse a single primitives.\nfunction parsePrimitive(info, name, value) {\n  var result = value;\n  if (info.number || info.positiveNumber) {\n    if (!isNaN(result) && result !== '') {\n      result = Number(result);\n    }\n  } else if (info.boolean || info.overloadedBoolean) {\n    // Accept `boolean` and `string`.\n    if (typeof result === 'string' && (result === '' || normalize(value) === normalize(name))) {\n      result = true;\n    }\n  }\n  return result;\n}\nfunction style(value) {\n  var result = [];\n  var key;\n  for (key in value) {\n    result.push([key, value[key]].join(': '));\n  }\n  return result.join('; ');\n}\nfunction createAdjustMap(values) {\n  var length = values.length;\n  var index = -1;\n  var result = {};\n  var value;\n  while (++index < length) {\n    value = values[index];\n    result[value.toLowerCase()] = value;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["find", "require", "normalize", "parseSelector", "spaces", "parse", "commas", "module", "exports", "factory", "own", "hasOwnProperty", "schema", "defaultTagName", "caseSensitive", "adjust", "createAdjustMap", "h", "selector", "properties", "node", "children", "Array", "prototype", "slice", "call", "arguments", "name", "tagName", "toLowerCase", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unshift", "addProperty", "<PERSON><PERSON><PERSON><PERSON>", "content", "type", "key", "value", "info", "result", "undefined", "spaceSeparated", "commaSeparated", "commaOrSpaceSeparated", "join", "style", "className", "concat", "parsePrimitives", "isNode", "nodes", "index", "length", "push", "String", "Error", "parsePrimitive", "number", "positiveNumber", "isNaN", "Number", "boolean", "overloadedBoolean", "values"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/hastscript/factory.js"], "sourcesContent": ["'use strict'\n\nvar find = require('property-information/find')\nvar normalize = require('property-information/normalize')\nvar parseSelector = require('hast-util-parse-selector')\nvar spaces = require('space-separated-tokens').parse\nvar commas = require('comma-separated-tokens').parse\n\nmodule.exports = factory\n\nvar own = {}.hasOwnProperty\n\nfunction factory(schema, defaultTagName, caseSensitive) {\n  var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null\n\n  return h\n\n  // Hyperscript compatible DSL for creating virtual hast trees.\n  function h(selector, properties) {\n    var node = parseSelector(selector, defaultTagName)\n    var children = Array.prototype.slice.call(arguments, 2)\n    var name = node.tagName.toLowerCase()\n    var property\n\n    node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name\n\n    if (properties && isChildren(properties, node)) {\n      children.unshift(properties)\n      properties = null\n    }\n\n    if (properties) {\n      for (property in properties) {\n        addProperty(node.properties, property, properties[property])\n      }\n    }\n\n    addChild(node.children, children)\n\n    if (node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  function addProperty(properties, key, value) {\n    var info\n    var property\n    var result\n\n    // Ignore nullish and NaN values.\n    if (value === null || value === undefined || value !== value) {\n      return\n    }\n\n    info = find(schema, key)\n    property = info.property\n    result = value\n\n    // Handle list values.\n    if (typeof result === 'string') {\n      if (info.spaceSeparated) {\n        result = spaces(result)\n      } else if (info.commaSeparated) {\n        result = commas(result)\n      } else if (info.commaOrSpaceSeparated) {\n        result = spaces(commas(result).join(' '))\n      }\n    }\n\n    // Accept `object` on style.\n    if (property === 'style' && typeof value !== 'string') {\n      result = style(result)\n    }\n\n    // Class-names (which can be added both on the `selector` and here).\n    if (property === 'className' && properties.className) {\n      result = properties.className.concat(result)\n    }\n\n    properties[property] = parsePrimitives(info, property, result)\n  }\n}\n\nfunction isChildren(value, node) {\n  return (\n    typeof value === 'string' ||\n    'length' in value ||\n    isNode(node.tagName, value)\n  )\n}\n\nfunction isNode(tagName, value) {\n  var type = value.type\n\n  if (tagName === 'input' || !type || typeof type !== 'string') {\n    return false\n  }\n\n  if (typeof value.children === 'object' && 'length' in value.children) {\n    return true\n  }\n\n  type = type.toLowerCase()\n\n  if (tagName === 'button') {\n    return (\n      type !== 'menu' &&\n      type !== 'submit' &&\n      type !== 'reset' &&\n      type !== 'button'\n    )\n  }\n\n  return 'value' in value\n}\n\nfunction addChild(nodes, value) {\n  var index\n  var length\n\n  if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n    return\n  }\n\n  if (typeof value === 'object' && 'length' in value) {\n    index = -1\n    length = value.length\n\n    while (++index < length) {\n      addChild(nodes, value[index])\n    }\n\n    return\n  }\n\n  if (typeof value !== 'object' || !('type' in value)) {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n\n  nodes.push(value)\n}\n\n// Parse a (list of) primitives.\nfunction parsePrimitives(info, name, value) {\n  var index\n  var length\n  var result\n\n  if (typeof value !== 'object' || !('length' in value)) {\n    return parsePrimitive(info, name, value)\n  }\n\n  length = value.length\n  index = -1\n  result = []\n\n  while (++index < length) {\n    result[index] = parsePrimitive(info, name, value[index])\n  }\n\n  return result\n}\n\n// Parse a single primitives.\nfunction parsePrimitive(info, name, value) {\n  var result = value\n\n  if (info.number || info.positiveNumber) {\n    if (!isNaN(result) && result !== '') {\n      result = Number(result)\n    }\n  } else if (info.boolean || info.overloadedBoolean) {\n    // Accept `boolean` and `string`.\n    if (\n      typeof result === 'string' &&\n      (result === '' || normalize(value) === normalize(name))\n    ) {\n      result = true\n    }\n  }\n\n  return result\n}\n\nfunction style(value) {\n  var result = []\n  var key\n\n  for (key in value) {\n    result.push([key, value[key]].join(': '))\n  }\n\n  return result.join('; ')\n}\n\nfunction createAdjustMap(values) {\n  var length = values.length\n  var index = -1\n  var result = {}\n  var value\n\n  while (++index < length) {\n    value = values[index]\n    result[value.toLowerCase()] = value\n  }\n\n  return result\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC/C,IAAIC,SAAS,GAAGD,OAAO,CAAC,gCAAgC,CAAC;AACzD,IAAIE,aAAa,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACvD,IAAIG,MAAM,GAAGH,OAAO,CAAC,wBAAwB,CAAC,CAACI,KAAK;AACpD,IAAIC,MAAM,GAAGL,OAAO,CAAC,wBAAwB,CAAC,CAACI,KAAK;AAEpDE,MAAM,CAACC,OAAO,GAAGC,OAAO;AAExB,IAAIC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;AAE3B,SAASF,OAAOA,CAACG,MAAM,EAAEC,cAAc,EAAEC,aAAa,EAAE;EACtD,IAAIC,MAAM,GAAGD,aAAa,GAAGE,eAAe,CAACF,aAAa,CAAC,GAAG,IAAI;EAElE,OAAOG,CAAC;;EAER;EACA,SAASA,CAACA,CAACC,QAAQ,EAAEC,UAAU,EAAE;IAC/B,IAAIC,IAAI,GAAGjB,aAAa,CAACe,QAAQ,EAAEL,cAAc,CAAC;IAClD,IAAIQ,QAAQ,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC;IACvD,IAAIC,IAAI,GAAGP,IAAI,CAACQ,OAAO,CAACC,WAAW,CAAC,CAAC;IACrC,IAAIC,QAAQ;IAEZV,IAAI,CAACQ,OAAO,GAAGb,MAAM,IAAIL,GAAG,CAACe,IAAI,CAACV,MAAM,EAAEY,IAAI,CAAC,GAAGZ,MAAM,CAACY,IAAI,CAAC,GAAGA,IAAI;IAErE,IAAIR,UAAU,IAAIY,UAAU,CAACZ,UAAU,EAAEC,IAAI,CAAC,EAAE;MAC9CC,QAAQ,CAACW,OAAO,CAACb,UAAU,CAAC;MAC5BA,UAAU,GAAG,IAAI;IACnB;IAEA,IAAIA,UAAU,EAAE;MACd,KAAKW,QAAQ,IAAIX,UAAU,EAAE;QAC3Bc,WAAW,CAACb,IAAI,CAACD,UAAU,EAAEW,QAAQ,EAAEX,UAAU,CAACW,QAAQ,CAAC,CAAC;MAC9D;IACF;IAEAI,QAAQ,CAACd,IAAI,CAACC,QAAQ,EAAEA,QAAQ,CAAC;IAEjC,IAAID,IAAI,CAACQ,OAAO,KAAK,UAAU,EAAE;MAC/BR,IAAI,CAACe,OAAO,GAAG;QAACC,IAAI,EAAE,MAAM;QAAEf,QAAQ,EAAED,IAAI,CAACC;MAAQ,CAAC;MACtDD,IAAI,CAACC,QAAQ,GAAG,EAAE;IACpB;IAEA,OAAOD,IAAI;EACb;EAEA,SAASa,WAAWA,CAACd,UAAU,EAAEkB,GAAG,EAAEC,KAAK,EAAE;IAC3C,IAAIC,IAAI;IACR,IAAIT,QAAQ;IACZ,IAAIU,MAAM;;IAEV;IACA,IAAIF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAKA,KAAK,EAAE;MAC5D;IACF;IAEAC,IAAI,GAAGvC,IAAI,CAACY,MAAM,EAAEyB,GAAG,CAAC;IACxBP,QAAQ,GAAGS,IAAI,CAACT,QAAQ;IACxBU,MAAM,GAAGF,KAAK;;IAEd;IACA,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAID,IAAI,CAACG,cAAc,EAAE;QACvBF,MAAM,GAAGpC,MAAM,CAACoC,MAAM,CAAC;MACzB,CAAC,MAAM,IAAID,IAAI,CAACI,cAAc,EAAE;QAC9BH,MAAM,GAAGlC,MAAM,CAACkC,MAAM,CAAC;MACzB,CAAC,MAAM,IAAID,IAAI,CAACK,qBAAqB,EAAE;QACrCJ,MAAM,GAAGpC,MAAM,CAACE,MAAM,CAACkC,MAAM,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3C;IACF;;IAEA;IACA,IAAIf,QAAQ,KAAK,OAAO,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EAAE;MACrDE,MAAM,GAAGM,KAAK,CAACN,MAAM,CAAC;IACxB;;IAEA;IACA,IAAIV,QAAQ,KAAK,WAAW,IAAIX,UAAU,CAAC4B,SAAS,EAAE;MACpDP,MAAM,GAAGrB,UAAU,CAAC4B,SAAS,CAACC,MAAM,CAACR,MAAM,CAAC;IAC9C;IAEArB,UAAU,CAACW,QAAQ,CAAC,GAAGmB,eAAe,CAACV,IAAI,EAAET,QAAQ,EAAEU,MAAM,CAAC;EAChE;AACF;AAEA,SAAST,UAAUA,CAACO,KAAK,EAAElB,IAAI,EAAE;EAC/B,OACE,OAAOkB,KAAK,KAAK,QAAQ,IACzB,QAAQ,IAAIA,KAAK,IACjBY,MAAM,CAAC9B,IAAI,CAACQ,OAAO,EAAEU,KAAK,CAAC;AAE/B;AAEA,SAASY,MAAMA,CAACtB,OAAO,EAAEU,KAAK,EAAE;EAC9B,IAAIF,IAAI,GAAGE,KAAK,CAACF,IAAI;EAErB,IAAIR,OAAO,KAAK,OAAO,IAAI,CAACQ,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5D,OAAO,KAAK;EACd;EAEA,IAAI,OAAOE,KAAK,CAACjB,QAAQ,KAAK,QAAQ,IAAI,QAAQ,IAAIiB,KAAK,CAACjB,QAAQ,EAAE;IACpE,OAAO,IAAI;EACb;EAEAe,IAAI,GAAGA,IAAI,CAACP,WAAW,CAAC,CAAC;EAEzB,IAAID,OAAO,KAAK,QAAQ,EAAE;IACxB,OACEQ,IAAI,KAAK,MAAM,IACfA,IAAI,KAAK,QAAQ,IACjBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,QAAQ;EAErB;EAEA,OAAO,OAAO,IAAIE,KAAK;AACzB;AAEA,SAASJ,QAAQA,CAACiB,KAAK,EAAEb,KAAK,EAAE;EAC9B,IAAIc,KAAK;EACT,IAAIC,MAAM;EAEV,IAAI,OAAOf,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC1Da,KAAK,CAACG,IAAI,CAAC;MAAClB,IAAI,EAAE,MAAM;MAAEE,KAAK,EAAEiB,MAAM,CAACjB,KAAK;IAAC,CAAC,CAAC;IAChD;EACF;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIA,KAAK,EAAE;IAClDc,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGf,KAAK,CAACe,MAAM;IAErB,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;MACvBnB,QAAQ,CAACiB,KAAK,EAAEb,KAAK,CAACc,KAAK,CAAC,CAAC;IAC/B;IAEA;EACF;EAEA,IAAI,OAAOd,KAAK,KAAK,QAAQ,IAAI,EAAE,MAAM,IAAIA,KAAK,CAAC,EAAE;IACnD,MAAM,IAAIkB,KAAK,CAAC,wCAAwC,GAAGlB,KAAK,GAAG,GAAG,CAAC;EACzE;EAEAa,KAAK,CAACG,IAAI,CAAChB,KAAK,CAAC;AACnB;;AAEA;AACA,SAASW,eAAeA,CAACV,IAAI,EAAEZ,IAAI,EAAEW,KAAK,EAAE;EAC1C,IAAIc,KAAK;EACT,IAAIC,MAAM;EACV,IAAIb,MAAM;EAEV,IAAI,OAAOF,KAAK,KAAK,QAAQ,IAAI,EAAE,QAAQ,IAAIA,KAAK,CAAC,EAAE;IACrD,OAAOmB,cAAc,CAAClB,IAAI,EAAEZ,IAAI,EAAEW,KAAK,CAAC;EAC1C;EAEAe,MAAM,GAAGf,KAAK,CAACe,MAAM;EACrBD,KAAK,GAAG,CAAC,CAAC;EACVZ,MAAM,GAAG,EAAE;EAEX,OAAO,EAAEY,KAAK,GAAGC,MAAM,EAAE;IACvBb,MAAM,CAACY,KAAK,CAAC,GAAGK,cAAc,CAAClB,IAAI,EAAEZ,IAAI,EAAEW,KAAK,CAACc,KAAK,CAAC,CAAC;EAC1D;EAEA,OAAOZ,MAAM;AACf;;AAEA;AACA,SAASiB,cAAcA,CAAClB,IAAI,EAAEZ,IAAI,EAAEW,KAAK,EAAE;EACzC,IAAIE,MAAM,GAAGF,KAAK;EAElB,IAAIC,IAAI,CAACmB,MAAM,IAAInB,IAAI,CAACoB,cAAc,EAAE;IACtC,IAAI,CAACC,KAAK,CAACpB,MAAM,CAAC,IAAIA,MAAM,KAAK,EAAE,EAAE;MACnCA,MAAM,GAAGqB,MAAM,CAACrB,MAAM,CAAC;IACzB;EACF,CAAC,MAAM,IAAID,IAAI,CAACuB,OAAO,IAAIvB,IAAI,CAACwB,iBAAiB,EAAE;IACjD;IACA,IACE,OAAOvB,MAAM,KAAK,QAAQ,KACzBA,MAAM,KAAK,EAAE,IAAItC,SAAS,CAACoC,KAAK,CAAC,KAAKpC,SAAS,CAACyB,IAAI,CAAC,CAAC,EACvD;MACAa,MAAM,GAAG,IAAI;IACf;EACF;EAEA,OAAOA,MAAM;AACf;AAEA,SAASM,KAAKA,CAACR,KAAK,EAAE;EACpB,IAAIE,MAAM,GAAG,EAAE;EACf,IAAIH,GAAG;EAEP,KAAKA,GAAG,IAAIC,KAAK,EAAE;IACjBE,MAAM,CAACc,IAAI,CAAC,CAACjB,GAAG,EAAEC,KAAK,CAACD,GAAG,CAAC,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3C;EAEA,OAAOL,MAAM,CAACK,IAAI,CAAC,IAAI,CAAC;AAC1B;AAEA,SAAS7B,eAAeA,CAACgD,MAAM,EAAE;EAC/B,IAAIX,MAAM,GAAGW,MAAM,CAACX,MAAM;EAC1B,IAAID,KAAK,GAAG,CAAC,CAAC;EACd,IAAIZ,MAAM,GAAG,CAAC,CAAC;EACf,IAAIF,KAAK;EAET,OAAO,EAAEc,KAAK,GAAGC,MAAM,EAAE;IACvBf,KAAK,GAAG0B,MAAM,CAACZ,KAAK,CAAC;IACrBZ,MAAM,CAACF,KAAK,CAACT,WAAW,CAAC,CAAC,CAAC,GAAGS,KAAK;EACrC;EAEA,OAAOE,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}