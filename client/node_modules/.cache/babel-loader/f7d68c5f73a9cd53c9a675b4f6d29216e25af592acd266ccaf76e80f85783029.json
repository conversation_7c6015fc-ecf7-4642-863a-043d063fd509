{"ast": null, "code": "'use strict';\n\nvar refractorLua = require('./lua.js');\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = etlua;\netlua.displayName = 'etlua';\netlua.aliases = [];\nfunction etlua(Prism) {\n  Prism.register(refractorLua);\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    Prism.languages.etlua = {\n      delimiter: {\n        pattern: /^<%[-=]?|-?%>$/,\n        alias: 'punctuation'\n      },\n      'language-lua': {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.lua\n      }\n    };\n    Prism.hooks.add('before-tokenize', function (env) {\n      var pattern = /<%[\\s\\S]+?%>/g;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'etlua', pattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'etlua');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorLua", "require", "refractorMarkupTemplating", "module", "exports", "etlua", "displayName", "aliases", "Prism", "register", "languages", "delimiter", "pattern", "alias", "inside", "lua", "hooks", "add", "env", "buildPlaceholders", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/etlua.js"], "sourcesContent": ["'use strict'\nvar refractorLua = require('./lua.js')\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = etlua\netlua.displayName = 'etlua'\netlua.aliases = []\nfunction etlua(Prism) {\n  Prism.register(refractorLua)\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.etlua = {\n      delimiter: {\n        pattern: /^<%[-=]?|-?%>$/,\n        alias: 'punctuation'\n      },\n      'language-lua': {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.lua\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var pattern = /<%[\\s\\S]+?%>/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'etlua',\n        pattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'etlua')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,UAAU,CAAC;AACtC,IAAIC,yBAAyB,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACjEE,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACT,YAAY,CAAC;EAC5BQ,KAAK,CAACC,QAAQ,CAACP,yBAAyB,CAAC;EACxC,CAAC,UAAUM,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,KAAK,GAAG;MACtBM,SAAS,EAAE;QACTC,OAAO,EAAE,gBAAgB;QACzBC,KAAK,EAAE;MACT,CAAC;MACD,cAAc,EAAE;QACdD,OAAO,EAAE,SAAS;QAClBE,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;MAC1B;IACF,CAAC;IACDP,KAAK,CAACQ,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIN,OAAO,GAAG,eAAe;MAC7BJ,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACS,iBAAiB,CACpDD,GAAG,EACH,OAAO,EACPN,OACF,CAAC;IACH,CAAC,CAAC;IACFJ,KAAK,CAACQ,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/CV,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACU,oBAAoB,CAACF,GAAG,EAAE,OAAO,CAAC;IACzE,CAAC,CAAC;EACJ,CAAC,EAAEV,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}