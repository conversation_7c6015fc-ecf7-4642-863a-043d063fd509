{"ast": null, "code": "'use strict';\n\nmodule.exports = stan;\nstan.displayName = 'stan';\nstan.aliases = [];\nfunction stan(Prism) {\n  ;\n  (function (Prism) {\n    // https://mc-stan.org/docs/2_28/reference-manual/bnf-grammars.html\n    var higherOrderFunctions = /\\b(?:algebra_solver|algebra_solver_newton|integrate_1d|integrate_ode|integrate_ode_bdf|integrate_ode_rk45|map_rect|ode_(?:adams|bdf|ckrk|rk45)(?:_tol)?|ode_adjoint_tol_ctl|reduce_sum|reduce_sum_static)\\b/;\n    Prism.languages.stan = {\n      comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\/|#(?!include).*/,\n      string: {\n        // String literals can contain spaces and any printable ASCII characters except for \" and \\\n        // https://mc-stan.org/docs/2_24/reference-manual/print-statements-section.html#string-literals\n        pattern: /\"[\\x20\\x21\\x23-\\x5B\\x5D-\\x7E]*\"/,\n        greedy: true\n      },\n      directive: {\n        pattern: /^([ \\t]*)#include\\b.*/m,\n        lookbehind: true,\n        alias: 'property'\n      },\n      'function-arg': {\n        pattern: RegExp('(' + higherOrderFunctions.source + /\\s*\\(\\s*/.source + ')' + /[a-zA-Z]\\w*/.source),\n        lookbehind: true,\n        alias: 'function'\n      },\n      constraint: {\n        pattern: /(\\b(?:int|matrix|real|row_vector|vector)\\s*)<[^<>]*>/,\n        lookbehind: true,\n        inside: {\n          expression: {\n            pattern: /(=\\s*)\\S(?:\\S|\\s+(?!\\s))*?(?=\\s*(?:>$|,\\s*\\w+\\s*=))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          property: /\\b[a-z]\\w*(?=\\s*=)/i,\n          operator: /=/,\n          punctuation: /^<|>$|,/\n        }\n      },\n      keyword: [{\n        pattern: /\\bdata(?=\\s*\\{)|\\b(?:functions|generated|model|parameters|quantities|transformed)\\b/,\n        alias: 'program-block'\n      }, /\\b(?:array|break|cholesky_factor_corr|cholesky_factor_cov|complex|continue|corr_matrix|cov_matrix|data|else|for|if|in|increment_log_prob|int|matrix|ordered|positive_ordered|print|real|reject|return|row_vector|simplex|target|unit_vector|vector|void|while)\\b/,\n      // these are functions that are known to take another function as their first argument.\n      higherOrderFunctions],\n      function: /\\b[a-z]\\w*(?=\\s*\\()/i,\n      number: /(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:E[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: /<-|\\.[*/]=?|\\|\\|?|&&|[!=<>+\\-*/]=?|['^%~?:]/,\n      punctuation: /[()\\[\\]{},;]/\n    };\n    Prism.languages.stan.constraint.inside.expression.inside = Prism.languages.stan;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "stan", "displayName", "aliases", "Prism", "higherOrderFunctions", "languages", "comment", "string", "pattern", "greedy", "directive", "lookbehind", "alias", "RegExp", "source", "constraint", "inside", "expression", "property", "operator", "punctuation", "keyword", "function", "number", "boolean"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/stan.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = stan\nstan.displayName = 'stan'\nstan.aliases = []\nfunction stan(Prism) {\n  ;(function (Prism) {\n    // https://mc-stan.org/docs/2_28/reference-manual/bnf-grammars.html\n    var higherOrderFunctions =\n      /\\b(?:algebra_solver|algebra_solver_newton|integrate_1d|integrate_ode|integrate_ode_bdf|integrate_ode_rk45|map_rect|ode_(?:adams|bdf|ckrk|rk45)(?:_tol)?|ode_adjoint_tol_ctl|reduce_sum|reduce_sum_static)\\b/\n    Prism.languages.stan = {\n      comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\/|#(?!include).*/,\n      string: {\n        // String literals can contain spaces and any printable ASCII characters except for \" and \\\n        // https://mc-stan.org/docs/2_24/reference-manual/print-statements-section.html#string-literals\n        pattern: /\"[\\x20\\x21\\x23-\\x5B\\x5D-\\x7E]*\"/,\n        greedy: true\n      },\n      directive: {\n        pattern: /^([ \\t]*)#include\\b.*/m,\n        lookbehind: true,\n        alias: 'property'\n      },\n      'function-arg': {\n        pattern: RegExp(\n          '(' +\n            higherOrderFunctions.source +\n            /\\s*\\(\\s*/.source +\n            ')' +\n            /[a-zA-Z]\\w*/.source\n        ),\n        lookbehind: true,\n        alias: 'function'\n      },\n      constraint: {\n        pattern: /(\\b(?:int|matrix|real|row_vector|vector)\\s*)<[^<>]*>/,\n        lookbehind: true,\n        inside: {\n          expression: {\n            pattern: /(=\\s*)\\S(?:\\S|\\s+(?!\\s))*?(?=\\s*(?:>$|,\\s*\\w+\\s*=))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          property: /\\b[a-z]\\w*(?=\\s*=)/i,\n          operator: /=/,\n          punctuation: /^<|>$|,/\n        }\n      },\n      keyword: [\n        {\n          pattern:\n            /\\bdata(?=\\s*\\{)|\\b(?:functions|generated|model|parameters|quantities|transformed)\\b/,\n          alias: 'program-block'\n        },\n        /\\b(?:array|break|cholesky_factor_corr|cholesky_factor_cov|complex|continue|corr_matrix|cov_matrix|data|else|for|if|in|increment_log_prob|int|matrix|ordered|positive_ordered|print|real|reject|return|row_vector|simplex|target|unit_vector|vector|void|while)\\b/, // these are functions that are known to take another function as their first argument.\n        higherOrderFunctions\n      ],\n      function: /\\b[a-z]\\w*(?=\\s*\\()/i,\n      number:\n        /(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:E[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: /<-|\\.[*/]=?|\\|\\|?|&&|[!=<>+\\-*/]=?|['^%~?:]/,\n      punctuation: /[()\\[\\]{},;]/\n    }\n    Prism.languages.stan.constraint.inside.expression.inside =\n      Prism.languages.stan\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA,IAAIC,oBAAoB,GACtB,6MAA6M;IAC/MD,KAAK,CAACE,SAAS,CAACL,IAAI,GAAG;MACrBM,OAAO,EAAE,wCAAwC;MACjDC,MAAM,EAAE;QACN;QACA;QACAC,OAAO,EAAE,iCAAiC;QAC1CC,MAAM,EAAE;MACV,CAAC;MACDC,SAAS,EAAE;QACTF,OAAO,EAAE,wBAAwB;QACjCG,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACD,cAAc,EAAE;QACdJ,OAAO,EAAEK,MAAM,CACb,GAAG,GACDT,oBAAoB,CAACU,MAAM,GAC3B,UAAU,CAACA,MAAM,GACjB,GAAG,GACH,aAAa,CAACA,MAClB,CAAC;QACDH,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDG,UAAU,EAAE;QACVP,OAAO,EAAE,sDAAsD;QAC/DG,UAAU,EAAE,IAAI;QAChBK,MAAM,EAAE;UACNC,UAAU,EAAE;YACVT,OAAO,EAAE,qDAAqD;YAC9DG,UAAU,EAAE,IAAI;YAChBK,MAAM,EAAE,IAAI,CAAC;UACf,CAAC;UACDE,QAAQ,EAAE,qBAAqB;UAC/BC,QAAQ,EAAE,GAAG;UACbC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,OAAO,EAAE,CACP;QACEb,OAAO,EACL,qFAAqF;QACvFI,KAAK,EAAE;MACT,CAAC,EACD,kQAAkQ;MAAE;MACpQR,oBAAoB,CACrB;MACDkB,QAAQ,EAAE,sBAAsB;MAChCC,MAAM,EACJ,6FAA6F;MAC/FC,OAAO,EAAE,oBAAoB;MAC7BL,QAAQ,EAAE,6CAA6C;MACvDC,WAAW,EAAE;IACf,CAAC;IACDjB,KAAK,CAACE,SAAS,CAACL,IAAI,CAACe,UAAU,CAACC,MAAM,CAACC,UAAU,CAACD,MAAM,GACtDb,KAAK,CAACE,SAAS,CAACL,IAAI;EACxB,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}