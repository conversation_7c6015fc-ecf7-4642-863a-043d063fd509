{"ast": null, "code": "'use strict';\n\nmodule.exports = require('./html');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/hastscript/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = require('./html')\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}