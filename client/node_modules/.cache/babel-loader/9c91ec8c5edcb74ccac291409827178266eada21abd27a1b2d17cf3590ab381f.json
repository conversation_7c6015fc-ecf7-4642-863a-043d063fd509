{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = django;\ndjango.displayName = 'django';\ndjango.aliases = ['jinja2'];\nfunction django(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  // Django/Jinja2 syntax definition for Prism.js <http://prismjs.com> syntax highlighter.\n  // Mostly it works OK but can paint code incorrectly on complex html/template tag combinations.\n  ;\n  (function (Prism) {\n    Prism.languages.django = {\n      comment: /^\\{#[\\s\\S]*?#\\}$/,\n      tag: {\n        pattern: /(^\\{%[+-]?\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      delimiter: {\n        pattern: /^\\{[{%][+-]?|[+-]?[}%]\\}$/,\n        alias: 'punctuation'\n      },\n      string: {\n        pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true\n      },\n      filter: {\n        pattern: /(\\|)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      test: {\n        pattern: /(\\bis\\s+(?:not\\s+)?)(?!not\\b)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      function: /\\b[a-z_]\\w+(?=\\s*\\()/i,\n      keyword: /\\b(?:and|as|by|else|for|if|import|in|is|loop|not|or|recursive|with|without)\\b/,\n      operator: /[-+%=]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n      number: /\\b\\d+(?:\\.\\d+)?\\b/,\n      boolean: /[Ff]alse|[Nn]one|[Tt]rue/,\n      variable: /\\b\\w+\\b/,\n      punctuation: /[{}[\\](),.:;]/\n    };\n    var pattern = /\\{\\{[\\s\\S]*?\\}\\}|\\{%[\\s\\S]*?%\\}|\\{#[\\s\\S]*?#\\}/g;\n    var markupTemplating = Prism.languages['markup-templating'];\n    Prism.hooks.add('before-tokenize', function (env) {\n      markupTemplating.buildPlaceholders(env, 'django', pattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      markupTemplating.tokenizePlaceholders(env, 'django');\n    }); // Add an Jinja2 alias\n    Prism.languages.jinja2 = Prism.languages.django;\n    Prism.hooks.add('before-tokenize', function (env) {\n      markupTemplating.buildPlaceholders(env, 'jinja2', pattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      markupTemplating.tokenizePlaceholders(env, 'jinja2');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "django", "displayName", "aliases", "Prism", "register", "languages", "comment", "tag", "pattern", "lookbehind", "alias", "delimiter", "string", "greedy", "filter", "test", "function", "keyword", "operator", "number", "boolean", "variable", "punctuation", "markupTemplating", "hooks", "add", "env", "buildPlaceholders", "tokenizePlaceholders", "jinja2"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/django.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = django\ndjango.displayName = 'django'\ndjango.aliases = ['jinja2']\nfunction django(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  // Django/Jinja2 syntax definition for Prism.js <http://prismjs.com> syntax highlighter.\n  // Mostly it works OK but can paint code incorrectly on complex html/template tag combinations.\n  ;(function (Prism) {\n    Prism.languages.django = {\n      comment: /^\\{#[\\s\\S]*?#\\}$/,\n      tag: {\n        pattern: /(^\\{%[+-]?\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      delimiter: {\n        pattern: /^\\{[{%][+-]?|[+-]?[}%]\\}$/,\n        alias: 'punctuation'\n      },\n      string: {\n        pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true\n      },\n      filter: {\n        pattern: /(\\|)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      test: {\n        pattern: /(\\bis\\s+(?:not\\s+)?)(?!not\\b)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      function: /\\b[a-z_]\\w+(?=\\s*\\()/i,\n      keyword:\n        /\\b(?:and|as|by|else|for|if|import|in|is|loop|not|or|recursive|with|without)\\b/,\n      operator: /[-+%=]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n      number: /\\b\\d+(?:\\.\\d+)?\\b/,\n      boolean: /[Ff]alse|[Nn]one|[Tt]rue/,\n      variable: /\\b\\w+\\b/,\n      punctuation: /[{}[\\](),.:;]/\n    }\n    var pattern = /\\{\\{[\\s\\S]*?\\}\\}|\\{%[\\s\\S]*?%\\}|\\{#[\\s\\S]*?#\\}/g\n    var markupTemplating = Prism.languages['markup-templating']\n    Prism.hooks.add('before-tokenize', function (env) {\n      markupTemplating.buildPlaceholders(env, 'django', pattern)\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      markupTemplating.tokenizePlaceholders(env, 'django')\n    }) // Add an Jinja2 alias\n    Prism.languages.jinja2 = Prism.languages.django\n    Prism.hooks.add('before-tokenize', function (env) {\n      markupTemplating.buildPlaceholders(env, 'jinja2', pattern)\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      markupTemplating.tokenizePlaceholders(env, 'jinja2')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,QAAQ,CAAC;AAC3B,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB;EACxC;EACA;EAAA;EACC,CAAC,UAAUO,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,MAAM,GAAG;MACvBM,OAAO,EAAE,kBAAkB;MAC3BC,GAAG,EAAE;QACHC,OAAO,EAAE,mBAAmB;QAC5BC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,SAAS,EAAE;QACTH,OAAO,EAAE,2BAA2B;QACpCE,KAAK,EAAE;MACT,CAAC;MACDE,MAAM,EAAE;QACNJ,OAAO,EAAE,iCAAiC;QAC1CK,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNN,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDK,IAAI,EAAE;QACJP,OAAO,EAAE,kCAAkC;QAC3CC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDM,QAAQ,EAAE,uBAAuB;MACjCC,OAAO,EACL,+EAA+E;MACjFC,QAAQ,EAAE,mDAAmD;MAC7DC,MAAM,EAAE,mBAAmB;MAC3BC,OAAO,EAAE,0BAA0B;MACnCC,QAAQ,EAAE,SAAS;MACnBC,WAAW,EAAE;IACf,CAAC;IACD,IAAId,OAAO,GAAG,iDAAiD;IAC/D,IAAIe,gBAAgB,GAAGpB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC;IAC3DF,KAAK,CAACqB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChDH,gBAAgB,CAACI,iBAAiB,CAACD,GAAG,EAAE,QAAQ,EAAElB,OAAO,CAAC;IAC5D,CAAC,CAAC;IACFL,KAAK,CAACqB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/CH,gBAAgB,CAACK,oBAAoB,CAACF,GAAG,EAAE,QAAQ,CAAC;IACtD,CAAC,CAAC,EAAC;IACHvB,KAAK,CAACE,SAAS,CAACwB,MAAM,GAAG1B,KAAK,CAACE,SAAS,CAACL,MAAM;IAC/CG,KAAK,CAACqB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChDH,gBAAgB,CAACI,iBAAiB,CAACD,GAAG,EAAE,QAAQ,EAAElB,OAAO,CAAC;IAC5D,CAAC,CAAC;IACFL,KAAK,CAACqB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/CH,gBAAgB,CAACK,oBAAoB,CAACF,GAAG,EAAE,QAAQ,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,EAAEvB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}