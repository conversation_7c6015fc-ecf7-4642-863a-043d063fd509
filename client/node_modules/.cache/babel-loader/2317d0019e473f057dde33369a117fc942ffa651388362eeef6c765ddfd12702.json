{"ast": null, "code": "'use strict';\n\nmodule.exports = concurnas;\nconcurnas.displayName = 'concurnas';\nconcurnas.aliases = ['conc'];\nfunction concurnas(Prism) {\n  Prism.languages.concurnas = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\/\\/.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    langext: {\n      pattern: /\\b\\w+\\s*\\|\\|[\\s\\S]+?\\|\\|/,\n      greedy: true,\n      inside: {\n        'class-name': /^\\w+/,\n        string: {\n          pattern: /(^\\s*\\|\\|)[\\s\\S]+(?=\\|\\|$)/,\n          lookbehind: true\n        },\n        punctuation: /\\|\\|/\n      }\n    },\n    function: {\n      pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/,\n      lookbehind: true\n    },\n    keyword: /\\b(?:abstract|actor|also|annotation|assert|async|await|bool|boolean|break|byte|case|catch|changed|char|class|closed|constant|continue|def|default|del|double|elif|else|enum|every|extends|false|finally|float|for|from|global|gpudef|gpukernel|if|import|in|init|inject|int|lambda|local|long|loop|match|new|nodefault|null|of|onchange|open|out|override|package|parfor|parforsync|post|pre|private|protected|provide|provider|public|return|shared|short|single|size_t|sizeof|super|sync|this|throw|trait|trans|transient|true|try|typedef|unchecked|using|val|var|void|while|with)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b0b[01][01_]*L?\\b|\\b0x(?:[\\da-f_]*\\.)?[\\da-f_p+-]+\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfls]?/i,\n    punctuation: /[{}[\\];(),.:]/,\n    operator: /<==|>==|=>|->|<-|<>|&==|&<>|\\?:?|\\.\\?|\\+\\+|--|[-+*/=<>]=?|[!^~]|\\b(?:and|as|band|bor|bxor|comp|is|isnot|mod|or)\\b=?/,\n    annotation: {\n      pattern: /@(?:\\w+:)?(?:\\w+|\\[[^\\]]+\\])?/,\n      alias: 'builtin'\n    }\n  };\n  Prism.languages.insertBefore('concurnas', 'langext', {\n    'regex-literal': {\n      pattern: /\\br(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: Prism.languages.concurnas\n        },\n        regex: /[\\s\\S]+/\n      }\n    },\n    'string-literal': {\n      pattern: /(?:\\B|\\bs)(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: Prism.languages.concurnas\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  });\n  Prism.languages.conc = Prism.languages.concurnas;\n}", "map": {"version": 3, "names": ["module", "exports", "concurnas", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "langext", "inside", "string", "punctuation", "function", "keyword", "boolean", "number", "operator", "annotation", "alias", "insertBefore", "interpolation", "regex", "conc"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/concurnas.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = concurnas\nconcurnas.displayName = 'concurnas'\nconcurnas.aliases = ['conc']\nfunction concurnas(Prism) {\n  Prism.languages.concurnas = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\/\\/.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    langext: {\n      pattern: /\\b\\w+\\s*\\|\\|[\\s\\S]+?\\|\\|/,\n      greedy: true,\n      inside: {\n        'class-name': /^\\w+/,\n        string: {\n          pattern: /(^\\s*\\|\\|)[\\s\\S]+(?=\\|\\|$)/,\n          lookbehind: true\n        },\n        punctuation: /\\|\\|/\n      }\n    },\n    function: {\n      pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:abstract|actor|also|annotation|assert|async|await|bool|boolean|break|byte|case|catch|changed|char|class|closed|constant|continue|def|default|del|double|elif|else|enum|every|extends|false|finally|float|for|from|global|gpudef|gpukernel|if|import|in|init|inject|int|lambda|local|long|loop|match|new|nodefault|null|of|onchange|open|out|override|package|parfor|parforsync|post|pre|private|protected|provide|provider|public|return|shared|short|single|size_t|sizeof|super|sync|this|throw|trait|trans|transient|true|try|typedef|unchecked|using|val|var|void|while|with)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number:\n      /\\b0b[01][01_]*L?\\b|\\b0x(?:[\\da-f_]*\\.)?[\\da-f_p+-]+\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfls]?/i,\n    punctuation: /[{}[\\];(),.:]/,\n    operator:\n      /<==|>==|=>|->|<-|<>|&==|&<>|\\?:?|\\.\\?|\\+\\+|--|[-+*/=<>]=?|[!^~]|\\b(?:and|as|band|bor|bxor|comp|is|isnot|mod|or)\\b=?/,\n    annotation: {\n      pattern: /@(?:\\w+:)?(?:\\w+|\\[[^\\]]+\\])?/,\n      alias: 'builtin'\n    }\n  }\n  Prism.languages.insertBefore('concurnas', 'langext', {\n    'regex-literal': {\n      pattern: /\\br(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: Prism.languages.concurnas\n        },\n        regex: /[\\s\\S]+/\n      }\n    },\n    'string-literal': {\n      pattern: /(?:\\B|\\bs)(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: Prism.languages.concurnas\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.conc = Prism.languages.concurnas\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,CAAC,MAAM,CAAC;AAC5B,SAASF,SAASA,CAACG,KAAK,EAAE;EACxBA,KAAK,CAACC,SAAS,CAACJ,SAAS,GAAG;IAC1BK,OAAO,EAAE;MACPC,OAAO,EAAE,4CAA4C;MACrDC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPH,OAAO,EAAE,0BAA0B;MACnCE,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE;UACNL,OAAO,EAAE,4BAA4B;UACrCC,UAAU,EAAE;QACd,CAAC;QACDK,WAAW,EAAE;MACf;IACF,CAAC;IACDC,QAAQ,EAAE;MACRP,OAAO,EAAE,0CAA0C;MACnDC,UAAU,EAAE;IACd,CAAC;IACDO,OAAO,EACL,yjBAAyjB;IAC3jBC,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EACJ,2HAA2H;IAC7HJ,WAAW,EAAE,eAAe;IAC5BK,QAAQ,EACN,qHAAqH;IACvHC,UAAU,EAAE;MACVZ,OAAO,EAAE,+BAA+B;MACxCa,KAAK,EAAE;IACT;EACF,CAAC;EACDhB,KAAK,CAACC,SAAS,CAACgB,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE;IACnD,eAAe,EAAE;MACfd,OAAO,EAAE,oCAAoC;MAC7CE,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACNW,aAAa,EAAE;UACbf,OAAO,EACL,gEAAgE;UAClEC,UAAU,EAAE,IAAI;UAChBG,MAAM,EAAEP,KAAK,CAACC,SAAS,CAACJ;QAC1B,CAAC;QACDsB,KAAK,EAAE;MACT;IACF,CAAC;IACD,gBAAgB,EAAE;MAChBhB,OAAO,EAAE,2CAA2C;MACpDE,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;QACNW,aAAa,EAAE;UACbf,OAAO,EACL,gEAAgE;UAClEC,UAAU,EAAE,IAAI;UAChBG,MAAM,EAAEP,KAAK,CAACC,SAAS,CAACJ;QAC1B,CAAC;QACDW,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;EACFR,KAAK,CAACC,SAAS,CAACmB,IAAI,GAAGpB,KAAK,CAACC,SAAS,CAACJ,SAAS;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}