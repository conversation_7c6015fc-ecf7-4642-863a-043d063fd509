{"ast": null, "code": "'use strict';\n\nmodule.exports = c;\nc.displayName = 'c';\nc.aliases = [];\nfunction c(Prism) {\n  Prism.languages.c = Prism.languages.extend('clike', {\n    comment: {\n      pattern: /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      greedy: true\n    },\n    string: {\n      // https://en.cppreference.com/w/c/language/string_literal\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n      lookbehind: true\n    },\n    keyword: /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n    operator: />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n  });\n  Prism.languages.insertBefore('c', 'string', {\n    char: {\n      // https://en.cppreference.com/w/c/language/character_constant\n      pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n      greedy: true\n    }\n  });\n  Prism.languages.insertBefore('c', 'string', {\n    macro: {\n      // allow for multiline macro definitions\n      // spaces after the # character compile fine with gcc\n      pattern: /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property',\n      inside: {\n        string: [{\n          // highlight the path of the include statement as a string\n          pattern: /^(#\\s*include\\s*)<[^>]+>/,\n          lookbehind: true\n        }, Prism.languages.c['string']],\n        char: Prism.languages.c['char'],\n        comment: Prism.languages.c['comment'],\n        'macro-name': [{\n          pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n          lookbehind: true\n        }, {\n          pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n          lookbehind: true,\n          alias: 'function'\n        }],\n        // highlight macro directives as keywords\n        directive: {\n          pattern: /^(#\\s*)[a-z]+/,\n          lookbehind: true,\n          alias: 'keyword'\n        },\n        'directive-hash': /^#/,\n        punctuation: /##|\\\\(?=[\\r\\n])/,\n        expression: {\n          pattern: /\\S[\\s\\S]*/,\n          inside: Prism.languages.c\n        }\n      }\n    }\n  });\n  Prism.languages.insertBefore('c', 'function', {\n    // highlight predefined macros as constants\n    constant: /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/\n  });\n  delete Prism.languages.c['boolean'];\n}", "map": {"version": 3, "names": ["module", "exports", "c", "displayName", "aliases", "Prism", "languages", "extend", "comment", "pattern", "greedy", "string", "lookbehind", "keyword", "function", "number", "operator", "insertBefore", "char", "macro", "alias", "inside", "directive", "punctuation", "expression", "constant"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/c.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = c\nc.displayName = 'c'\nc.aliases = []\nfunction c(Prism) {\n  Prism.languages.c = Prism.languages.extend('clike', {\n    comment: {\n      pattern:\n        /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      greedy: true\n    },\n    string: {\n      // https://en.cppreference.com/w/c/language/string_literal\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number:\n      /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n    operator: />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n  })\n  Prism.languages.insertBefore('c', 'string', {\n    char: {\n      // https://en.cppreference.com/w/c/language/character_constant\n      pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('c', 'string', {\n    macro: {\n      // allow for multiline macro definitions\n      // spaces after the # character compile fine with gcc\n      pattern:\n        /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property',\n      inside: {\n        string: [\n          {\n            // highlight the path of the include statement as a string\n            pattern: /^(#\\s*include\\s*)<[^>]+>/,\n            lookbehind: true\n          },\n          Prism.languages.c['string']\n        ],\n        char: Prism.languages.c['char'],\n        comment: Prism.languages.c['comment'],\n        'macro-name': [\n          {\n            pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n            lookbehind: true\n          },\n          {\n            pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n            lookbehind: true,\n            alias: 'function'\n          }\n        ],\n        // highlight macro directives as keywords\n        directive: {\n          pattern: /^(#\\s*)[a-z]+/,\n          lookbehind: true,\n          alias: 'keyword'\n        },\n        'directive-hash': /^#/,\n        punctuation: /##|\\\\(?=[\\r\\n])/,\n        expression: {\n          pattern: /\\S[\\s\\S]*/,\n          inside: Prism.languages.c\n        }\n      }\n    }\n  })\n  Prism.languages.insertBefore('c', 'function', {\n    // highlight predefined macros as constants\n    constant:\n      /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/\n  })\n  delete Prism.languages.c['boolean']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,CAAC;AAClBA,CAAC,CAACC,WAAW,GAAG,GAAG;AACnBD,CAAC,CAACE,OAAO,GAAG,EAAE;AACd,SAASF,CAACA,CAACG,KAAK,EAAE;EAChBA,KAAK,CAACC,SAAS,CAACJ,CAAC,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IAClDC,OAAO,EAAE;MACPC,OAAO,EACL,qEAAqE;MACvEC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACN;MACAF,OAAO,EAAE,qCAAqC;MAC9CC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE;MACZD,OAAO,EACL,kFAAkF;MACpFG,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EACL,mVAAmV;IACrVC,QAAQ,EAAE,uBAAuB;IACjCC,MAAM,EACJ,mHAAmH;IACrHC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFX,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE;IAC1CC,IAAI,EAAE;MACJ;MACAT,OAAO,EAAE,0CAA0C;MACnDC,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACFL,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE;IAC1CE,KAAK,EAAE;MACL;MACA;MACAV,OAAO,EACL,2FAA2F;MAC7FG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZU,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE;QACNV,MAAM,EAAE,CACN;UACE;UACAF,OAAO,EAAE,0BAA0B;UACnCG,UAAU,EAAE;QACd,CAAC,EACDP,KAAK,CAACC,SAAS,CAACJ,CAAC,CAAC,QAAQ,CAAC,CAC5B;QACDgB,IAAI,EAAEb,KAAK,CAACC,SAAS,CAACJ,CAAC,CAAC,MAAM,CAAC;QAC/BM,OAAO,EAAEH,KAAK,CAACC,SAAS,CAACJ,CAAC,CAAC,SAAS,CAAC;QACrC,YAAY,EAAE,CACZ;UACEO,OAAO,EAAE,8BAA8B;UACvCG,UAAU,EAAE;QACd,CAAC,EACD;UACEH,OAAO,EAAE,8BAA8B;UACvCG,UAAU,EAAE,IAAI;UAChBQ,KAAK,EAAE;QACT,CAAC,CACF;QACD;QACAE,SAAS,EAAE;UACTb,OAAO,EAAE,eAAe;UACxBG,UAAU,EAAE,IAAI;UAChBQ,KAAK,EAAE;QACT,CAAC;QACD,gBAAgB,EAAE,IAAI;QACtBG,WAAW,EAAE,iBAAiB;QAC9BC,UAAU,EAAE;UACVf,OAAO,EAAE,WAAW;UACpBY,MAAM,EAAEhB,KAAK,CAACC,SAAS,CAACJ;QAC1B;MACF;IACF;EACF,CAAC,CAAC;EACFG,KAAK,CAACC,SAAS,CAACW,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE;IAC5C;IACAQ,QAAQ,EACN;EACJ,CAAC,CAAC;EACF,OAAOpB,KAAK,CAACC,SAAS,CAACJ,CAAC,CAAC,SAAS,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}