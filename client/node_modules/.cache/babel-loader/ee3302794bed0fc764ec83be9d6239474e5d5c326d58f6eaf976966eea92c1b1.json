{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Tcl\nDescription: Tcl is a very simple programming language.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.tcl.tk/about/language.html\n*/\n\nfunction tcl(hljs) {\n  const TCL_IDENT = /[a-zA-Z_][a-zA-Z0-9_]*/;\n  const NUMBER = {\n    className: 'number',\n    variants: [hljs.BINARY_NUMBER_MODE, hljs.C_NUMBER_MODE]\n  };\n  return {\n    name: 'Tcl',\n    aliases: ['tk'],\n    keywords: 'after append apply array auto_execok auto_import auto_load auto_mkindex ' + 'auto_mkindex_old auto_qualify auto_reset bgerror binary break catch cd chan clock ' + 'close concat continue dde dict encoding eof error eval exec exit expr fblocked ' + 'fconfigure fcopy file fileevent filename flush for foreach format gets glob global ' + 'history http if incr info interp join lappend|10 lassign|10 lindex|10 linsert|10 list ' + 'llength|10 load lrange|10 lrepeat|10 lreplace|10 lreverse|10 lsearch|10 lset|10 lsort|10 ' + 'mathfunc mathop memory msgcat namespace open package parray pid pkg::create pkg_mkIndex ' + 'platform platform::shell proc puts pwd read refchan regexp registry regsub|10 rename ' + 'return safe scan seek set socket source split string subst switch tcl_endOfWord ' + 'tcl_findLibrary tcl_startOfNextWord tcl_startOfPreviousWord tcl_wordBreakAfter ' + 'tcl_wordBreakBefore tcltest tclvars tell time tm trace unknown unload unset update ' + 'uplevel upvar variable vwait while',\n    contains: [hljs.COMMENT(';[ \\\\t]*#', '$'), hljs.COMMENT('^[ \\\\t]*#', '$'), {\n      beginKeywords: 'proc',\n      end: '[\\\\{]',\n      excludeEnd: true,\n      contains: [{\n        className: 'title',\n        begin: '[ \\\\t\\\\n\\\\r]+(::)?[a-zA-Z_]((::)?[a-zA-Z0-9_])*',\n        end: '[ \\\\t\\\\n\\\\r]',\n        endsWithParent: true,\n        excludeEnd: true\n      }]\n    }, {\n      className: \"variable\",\n      variants: [{\n        begin: concat(/\\$/, optional(/::/), TCL_IDENT, '(::', TCL_IDENT, ')*')\n      }, {\n        begin: '\\\\$\\\\{(::)?[a-zA-Z_]((::)?[a-zA-Z0-9_])*',\n        end: '\\\\}',\n        contains: [NUMBER]\n      }]\n    }, {\n      className: 'string',\n      contains: [hljs.BACKSLASH_ESCAPE],\n      variants: [hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      })]\n    }, NUMBER]\n  };\n}\nmodule.exports = tcl;", "map": {"version": 3, "names": ["source", "re", "optional", "concat", "args", "joined", "map", "x", "join", "tcl", "hljs", "TCL_IDENT", "NUMBER", "className", "variants", "BINARY_NUMBER_MODE", "C_NUMBER_MODE", "name", "aliases", "keywords", "contains", "COMMENT", "beginKeywords", "end", "excludeEnd", "begin", "endsWithParent", "BACKSLASH_ESCAPE", "inherit", "QUOTE_STRING_MODE", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/tcl.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Tcl\nDescription: Tcl is a very simple programming language.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.tcl.tk/about/language.html\n*/\n\nfunction tcl(hljs) {\n  const TCL_IDENT = /[a-zA-Z_][a-zA-Z0-9_]*/;\n\n  const NUMBER = {\n    className: 'number',\n    variants: [hljs.BINARY_NUMBER_MODE, hljs.C_NUMBER_MODE]\n  };\n\n  return {\n    name: 'Tcl',\n    aliases: ['tk'],\n    keywords: 'after append apply array auto_execok auto_import auto_load auto_mkindex ' +\n      'auto_mkindex_old auto_qualify auto_reset bgerror binary break catch cd chan clock ' +\n      'close concat continue dde dict encoding eof error eval exec exit expr fblocked ' +\n      'fconfigure fcopy file fileevent filename flush for foreach format gets glob global ' +\n      'history http if incr info interp join lappend|10 lassign|10 lindex|10 linsert|10 list ' +\n      'llength|10 load lrange|10 lrepeat|10 lreplace|10 lreverse|10 lsearch|10 lset|10 lsort|10 '+\n      'mathfunc mathop memory msgcat namespace open package parray pid pkg::create pkg_mkIndex '+\n      'platform platform::shell proc puts pwd read refchan regexp registry regsub|10 rename '+\n      'return safe scan seek set socket source split string subst switch tcl_endOfWord '+\n      'tcl_findLibrary tcl_startOfNextWord tcl_startOfPreviousWord tcl_wordBreakAfter '+\n      'tcl_wordBreakBefore tcltest tclvars tell time tm trace unknown unload unset update '+\n      'uplevel upvar variable vwait while',\n    contains: [\n      hljs.COMMENT(';[ \\\\t]*#', '$'),\n      hljs.COMMENT('^[ \\\\t]*#', '$'),\n      {\n        beginKeywords: 'proc',\n        end: '[\\\\{]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'title',\n            begin: '[ \\\\t\\\\n\\\\r]+(::)?[a-zA-Z_]((::)?[a-zA-Z0-9_])*',\n            end: '[ \\\\t\\\\n\\\\r]',\n            endsWithParent: true,\n            excludeEnd: true\n          }\n        ]\n      },\n      {\n        className: \"variable\",\n        variants: [\n          {\n            begin: concat(\n              /\\$/,\n              optional(/::/),\n              TCL_IDENT,\n              '(::',\n              TCL_IDENT,\n              ')*'\n            )\n          },\n          {\n            begin: '\\\\$\\\\{(::)?[a-zA-Z_]((::)?[a-zA-Z0-9_])*',\n            end: '\\\\}',\n            contains: [\n              NUMBER\n            ]\n          }\n        ]\n      },\n      {\n        className: 'string',\n        contains: [hljs.BACKSLASH_ESCAPE],\n        variants: [\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {illegal: null})\n        ]\n      },\n      NUMBER\n    ]\n  }\n}\n\nmodule.exports = tcl;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAACD,EAAE,EAAE;EACpB,OAAOE,MAAM,CAAC,GAAG,EAAEF,EAAE,EAAE,IAAI,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,SAAS,GAAG,wBAAwB;EAE1C,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CAACJ,IAAI,CAACK,kBAAkB,EAAEL,IAAI,CAACM,aAAa;EACxD,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,CAAC,IAAI,CAAC;IACfC,QAAQ,EAAE,0EAA0E,GAClF,oFAAoF,GACpF,iFAAiF,GACjF,qFAAqF,GACrF,wFAAwF,GACxF,2FAA2F,GAC3F,0FAA0F,GAC1F,uFAAuF,GACvF,kFAAkF,GAClF,iFAAiF,GACjF,qFAAqF,GACrF,oCAAoC;IACtCC,QAAQ,EAAE,CACRV,IAAI,CAACW,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,EAC9BX,IAAI,CAACW,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,EAC9B;MACEC,aAAa,EAAE,MAAM;MACrBC,GAAG,EAAE,OAAO;MACZC,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,CACR;QACEP,SAAS,EAAE,OAAO;QAClBY,KAAK,EAAE,iDAAiD;QACxDF,GAAG,EAAE,cAAc;QACnBG,cAAc,EAAE,IAAI;QACpBF,UAAU,EAAE;MACd,CAAC;IAEL,CAAC,EACD;MACEX,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,CACR;QACEW,KAAK,EAAEtB,MAAM,CACX,IAAI,EACJD,QAAQ,CAAC,IAAI,CAAC,EACdS,SAAS,EACT,KAAK,EACLA,SAAS,EACT,IACF;MACF,CAAC,EACD;QACEc,KAAK,EAAE,0CAA0C;QACjDF,GAAG,EAAE,KAAK;QACVH,QAAQ,EAAE,CACRR,MAAM;MAEV,CAAC;IAEL,CAAC,EACD;MACEC,SAAS,EAAE,QAAQ;MACnBO,QAAQ,EAAE,CAACV,IAAI,CAACiB,gBAAgB,CAAC;MACjCb,QAAQ,EAAE,CACRJ,IAAI,CAACkB,OAAO,CAAClB,IAAI,CAACmB,iBAAiB,EAAE;QAACC,OAAO,EAAE;MAAI,CAAC,CAAC;IAEzD,CAAC,EACDlB,MAAM;EAEV,CAAC;AACH;AAEAmB,MAAM,CAACC,OAAO,GAAGvB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}