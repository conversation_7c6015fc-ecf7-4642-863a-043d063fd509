{"ast": null, "code": "/*\n Language: <PERSON><PERSON><PERSON> CLI\n Author: <PERSON><PERSON><PERSON> <<EMAIL>>\n Description: language definition jboss cli\n Website: https://docs.jboss.org/author/display/WFLY/Command+Line+Interface\n Category: config\n */\n\nfunction jbossCli(hljs) {\n  const PARAM = {\n    begin: /[\\w-]+ *=/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [{\n      className: 'attr',\n      begin: /[\\w-]+/\n    }]\n  };\n  const PARAMSBLOCK = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    contains: [PARAM],\n    relevance: 0\n  };\n  const OPERATION = {\n    className: 'function',\n    begin: /:[\\w\\-.]+/,\n    relevance: 0\n  };\n  const PATH = {\n    className: 'string',\n    begin: /\\B([\\/.])[\\w\\-.\\/=]+/\n  };\n  const COMMAND_PARAMS = {\n    className: 'params',\n    begin: /--[\\w\\-=\\/]+/\n  };\n  return {\n    name: '<PERSON><PERSON><PERSON> CLI',\n    aliases: ['wildfly-cli'],\n    keywords: {\n      $pattern: '[a-z\\-]+',\n      keyword: 'alias batch cd clear command connect connection-factory connection-info data-source deploy ' + 'deployment-info deployment-overlay echo echo-dmr help history if jdbc-driver-info jms-queue|20 jms-topic|20 ls ' + 'patch pwd quit read-attribute read-operation reload rollout-plan run-batch set shutdown try unalias ' + 'undeploy unset version xa-data-source',\n      // module\n      literal: 'true false'\n    },\n    contains: [hljs.HASH_COMMENT_MODE, hljs.QUOTE_STRING_MODE, COMMAND_PARAMS, OPERATION, PATH, PARAMSBLOCK]\n  };\n}\nmodule.exports = jbossCli;", "map": {"version": 3, "names": ["jboss<PERSON>li", "hljs", "PARAM", "begin", "returnBegin", "relevance", "contains", "className", "PARAMSBLOCK", "end", "OPERATION", "PATH", "COMMAND_PARAMS", "name", "aliases", "keywords", "$pattern", "keyword", "literal", "HASH_COMMENT_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/jboss-cli.js"], "sourcesContent": ["/*\n Language: <PERSON><PERSON><PERSON> CLI\n Author: <PERSON><PERSON><PERSON> <<EMAIL>>\n Description: language definition jboss cli\n Website: https://docs.jboss.org/author/display/WFLY/Command+Line+Interface\n Category: config\n */\n\nfunction jbossCli(hljs) {\n  const PARAM = {\n    begin: /[\\w-]+ *=/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: /[\\w-]+/\n      }\n    ]\n  };\n  const PARAMSBLOCK = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    contains: [PARAM],\n    relevance: 0\n  };\n  const OPERATION = {\n    className: 'function',\n    begin: /:[\\w\\-.]+/,\n    relevance: 0\n  };\n  const PATH = {\n    className: 'string',\n    begin: /\\B([\\/.])[\\w\\-.\\/=]+/\n  };\n  const COMMAND_PARAMS = {\n    className: 'params',\n    begin: /--[\\w\\-=\\/]+/\n  };\n  return {\n    name: '<PERSON><PERSON><PERSON> CLI',\n    aliases: ['wildfly-cli'],\n    keywords: {\n      $pattern: '[a-z\\-]+',\n      keyword: 'alias batch cd clear command connect connection-factory connection-info data-source deploy ' +\n      'deployment-info deployment-overlay echo echo-dmr help history if jdbc-driver-info jms-queue|20 jms-topic|20 ls ' +\n      'patch pwd quit read-attribute read-operation reload rollout-plan run-batch set shutdown try unalias ' +\n      'undeploy unset version xa-data-source', // module\n      literal: 'true false'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      COMMAND_PARAMS,\n      OPERATION,\n      PATH,\n      PARAMSBLOCK\n    ]\n  };\n}\n\nmodule.exports = jbossCli;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,KAAK,GAAG;IACZC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBJ,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMK,WAAW,GAAG;IAClBD,SAAS,EAAE,QAAQ;IACnBJ,KAAK,EAAE,IAAI;IACXM,GAAG,EAAE,IAAI;IACTH,QAAQ,EAAE,CAACJ,KAAK,CAAC;IACjBG,SAAS,EAAE;EACb,CAAC;EACD,MAAMK,SAAS,GAAG;IAChBH,SAAS,EAAE,UAAU;IACrBJ,KAAK,EAAE,WAAW;IAClBE,SAAS,EAAE;EACb,CAAC;EACD,MAAMM,IAAI,GAAG;IACXJ,SAAS,EAAE,QAAQ;IACnBJ,KAAK,EAAE;EACT,CAAC;EACD,MAAMS,cAAc,GAAG;IACrBL,SAAS,EAAE,QAAQ;IACnBJ,KAAK,EAAE;EACT,CAAC;EACD,OAAO;IACLU,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,CAAC,aAAa,CAAC;IACxBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,6FAA6F,GACtG,iHAAiH,GACjH,sGAAsG,GACtG,uCAAuC;MAAE;MACzCC,OAAO,EAAE;IACX,CAAC;IACDZ,QAAQ,EAAE,CACRL,IAAI,CAACkB,iBAAiB,EACtBlB,IAAI,CAACmB,iBAAiB,EACtBR,cAAc,EACdF,SAAS,EACTC,IAAI,EACJH,WAAW;EAEf,CAAC;AACH;AAEAa,MAAM,CAACC,OAAO,GAAGtB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}