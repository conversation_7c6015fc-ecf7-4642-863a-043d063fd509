{"ast": null, "code": "'use strict';\n\nmodule.exports = sass;\nsass.displayName = 'sass';\nsass.aliases = [];\nfunction sass(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.sass = Prism.languages.extend('css', {\n      // Sass comments don't need to be closed, only indented\n      comment: {\n        pattern: /^([ \\t]*)\\/[\\/*].*(?:(?:\\r?\\n|\\r)\\1[ \\t].+)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    });\n    Prism.languages.insertBefore('sass', 'atrule', {\n      // We want to consume the whole line\n      'atrule-line': {\n        // Includes support for = and + shortcuts\n        pattern: /^(?:[ \\t]*)[@+=].+/m,\n        greedy: true,\n        inside: {\n          atrule: /(?:@[\\w-]+|[+=])/\n        }\n      }\n    });\n    delete Prism.languages.sass.atrule;\n    var variable = /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/;\n    var operator = [/[+*\\/%]|[=!]=|<=?|>=?|\\b(?:and|not|or)\\b/, {\n      pattern: /(\\s)-(?=\\s)/,\n      lookbehind: true\n    }];\n    Prism.languages.insertBefore('sass', 'property', {\n      // We want to consume the whole line\n      'variable-line': {\n        pattern: /^[ \\t]*\\$.+/m,\n        greedy: true,\n        inside: {\n          punctuation: /:/,\n          variable: variable,\n          operator: operator\n        }\n      },\n      // We want to consume the whole line\n      'property-line': {\n        pattern: /^[ \\t]*(?:[^:\\s]+ *:.*|:[^:\\s].*)/m,\n        greedy: true,\n        inside: {\n          property: [/[^:\\s]+(?=\\s*:)/, {\n            pattern: /(:)[^:\\s]+/,\n            lookbehind: true\n          }],\n          punctuation: /:/,\n          variable: variable,\n          operator: operator,\n          important: Prism.languages.sass.important\n        }\n      }\n    });\n    delete Prism.languages.sass.property;\n    delete Prism.languages.sass.important; // Now that whole lines for other patterns are consumed,\n    // what's left should be selectors\n    Prism.languages.insertBefore('sass', 'punctuation', {\n      selector: {\n        pattern: /^([ \\t]*)\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*(?:,(?:\\r?\\n|\\r)\\1[ \\t]+\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "sass", "displayName", "aliases", "Prism", "languages", "extend", "comment", "pattern", "lookbehind", "greedy", "insertBefore", "inside", "at<PERSON>le", "variable", "operator", "punctuation", "property", "important", "selector"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/sass.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = sass\nsass.displayName = 'sass'\nsass.aliases = []\nfunction sass(Prism) {\n  ;(function (Prism) {\n    Prism.languages.sass = Prism.languages.extend('css', {\n      // Sass comments don't need to be closed, only indented\n      comment: {\n        pattern: /^([ \\t]*)\\/[\\/*].*(?:(?:\\r?\\n|\\r)\\1[ \\t].+)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('sass', 'atrule', {\n      // We want to consume the whole line\n      'atrule-line': {\n        // Includes support for = and + shortcuts\n        pattern: /^(?:[ \\t]*)[@+=].+/m,\n        greedy: true,\n        inside: {\n          atrule: /(?:@[\\w-]+|[+=])/\n        }\n      }\n    })\n    delete Prism.languages.sass.atrule\n    var variable = /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n    var operator = [\n      /[+*\\/%]|[=!]=|<=?|>=?|\\b(?:and|not|or)\\b/,\n      {\n        pattern: /(\\s)-(?=\\s)/,\n        lookbehind: true\n      }\n    ]\n    Prism.languages.insertBefore('sass', 'property', {\n      // We want to consume the whole line\n      'variable-line': {\n        pattern: /^[ \\t]*\\$.+/m,\n        greedy: true,\n        inside: {\n          punctuation: /:/,\n          variable: variable,\n          operator: operator\n        }\n      },\n      // We want to consume the whole line\n      'property-line': {\n        pattern: /^[ \\t]*(?:[^:\\s]+ *:.*|:[^:\\s].*)/m,\n        greedy: true,\n        inside: {\n          property: [\n            /[^:\\s]+(?=\\s*:)/,\n            {\n              pattern: /(:)[^:\\s]+/,\n              lookbehind: true\n            }\n          ],\n          punctuation: /:/,\n          variable: variable,\n          operator: operator,\n          important: Prism.languages.sass.important\n        }\n      }\n    })\n    delete Prism.languages.sass.property\n    delete Prism.languages.sass.important // Now that whole lines for other patterns are consumed,\n    // what's left should be selectors\n    Prism.languages.insertBefore('sass', 'punctuation', {\n      selector: {\n        pattern:\n          /^([ \\t]*)\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*(?:,(?:\\r?\\n|\\r)\\1[ \\t]+\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,KAAK,EAAE;MACnD;MACAC,OAAO,EAAE;QACPC,OAAO,EAAE,+CAA+C;QACxDC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IACFN,KAAK,CAACC,SAAS,CAACM,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE;MAC7C;MACA,aAAa,EAAE;QACb;QACAH,OAAO,EAAE,qBAAqB;QAC9BE,MAAM,EAAE,IAAI;QACZE,MAAM,EAAE;UACNC,MAAM,EAAE;QACV;MACF;IACF,CAAC,CAAC;IACF,OAAOT,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACY,MAAM;IAClC,IAAIC,QAAQ,GAAG,wBAAwB;IACvC,IAAIC,QAAQ,GAAG,CACb,0CAA0C,EAC1C;MACEP,OAAO,EAAE,aAAa;MACtBC,UAAU,EAAE;IACd,CAAC,CACF;IACDL,KAAK,CAACC,SAAS,CAACM,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE;MAC/C;MACA,eAAe,EAAE;QACfH,OAAO,EAAE,cAAc;QACvBE,MAAM,EAAE,IAAI;QACZE,MAAM,EAAE;UACNI,WAAW,EAAE,GAAG;UAChBF,QAAQ,EAAEA,QAAQ;UAClBC,QAAQ,EAAEA;QACZ;MACF,CAAC;MACD;MACA,eAAe,EAAE;QACfP,OAAO,EAAE,oCAAoC;QAC7CE,MAAM,EAAE,IAAI;QACZE,MAAM,EAAE;UACNK,QAAQ,EAAE,CACR,iBAAiB,EACjB;YACET,OAAO,EAAE,YAAY;YACrBC,UAAU,EAAE;UACd,CAAC,CACF;UACDO,WAAW,EAAE,GAAG;UAChBF,QAAQ,EAAEA,QAAQ;UAClBC,QAAQ,EAAEA,QAAQ;UAClBG,SAAS,EAAEd,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACiB;QAClC;MACF;IACF,CAAC,CAAC;IACF,OAAOd,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACgB,QAAQ;IACpC,OAAOb,KAAK,CAACC,SAAS,CAACJ,IAAI,CAACiB,SAAS,EAAC;IACtC;IACAd,KAAK,CAACC,SAAS,CAACM,YAAY,CAAC,MAAM,EAAE,aAAa,EAAE;MAClDQ,QAAQ,EAAE;QACRX,OAAO,EACL,wHAAwH;QAC1HC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ,CAAC,EAAEN,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}