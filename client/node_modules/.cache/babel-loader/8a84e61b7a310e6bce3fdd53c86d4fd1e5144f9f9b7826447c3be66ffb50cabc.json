{"ast": null, "code": "'use strict';\n\nmodule.exports = gcode;\ngcode.displayName = 'gcode';\ngcode.aliases = [];\nfunction gcode(Prism) {\n  Prism.languages.gcode = {\n    comment: /;.*|\\B\\(.*?\\)\\B/,\n    string: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    keyword: /\\b[GM]\\d+(?:\\.\\d+)?\\b/,\n    property: /\\b[A-Z]/,\n    checksum: {\n      pattern: /(\\*)\\d+/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    // T0:0:0\n    punctuation: /[:*]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "gcode", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "keyword", "property", "checksum", "lookbehind", "alias", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/gcode.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = gcode\ngcode.displayName = 'gcode'\ngcode.aliases = []\nfunction gcode(Prism) {\n  Prism.languages.gcode = {\n    comment: /;.*|\\B\\(.*?\\)\\B/,\n    string: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    keyword: /\\b[GM]\\d+(?:\\.\\d+)?\\b/,\n    property: /\\b[A-Z]/,\n    checksum: {\n      pattern: /(\\*)\\d+/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    // T0:0:0\n    punctuation: /[:*]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,uBAAuB;IAChCC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE;MACRJ,OAAO,EAAE,SAAS;MAClBK,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACD;IACAC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}