{"ast": null, "code": "'use strict';\n\nmodule.exports = naniscript;\nnaniscript.displayName = 'naniscript';\nnaniscript.aliases = [];\nfunction naniscript(Prism) {\n  ;\n  (function (Prism) {\n    var expressionDef = /\\{[^\\r\\n\\[\\]{}]*\\}/;\n    var params = {\n      'quoted-string': {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        alias: 'operator'\n      },\n      'command-param-id': {\n        pattern: /(\\s)\\w+:/,\n        lookbehind: true,\n        alias: 'property'\n      },\n      'command-param-value': [{\n        pattern: expressionDef,\n        alias: 'selector'\n      }, {\n        pattern: /([\\t ])\\S+/,\n        lookbehind: true,\n        greedy: true,\n        alias: 'operator'\n      }, {\n        pattern: /\\S(?:.*\\S)?/,\n        alias: 'operator'\n      }]\n    };\n    Prism.languages.naniscript = {\n      // ; ...\n      comment: {\n        pattern: /^([\\t ]*);.*/m,\n        lookbehind: true\n      },\n      // > ...\n      // Define is a control line starting with '>' followed by a word, a space and a text.\n      define: {\n        pattern: /^>.+/m,\n        alias: 'tag',\n        inside: {\n          value: {\n            pattern: /(^>\\w+[\\t ]+)(?!\\s)[^{}\\r\\n]+/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          key: {\n            pattern: /(^>)\\w+/,\n            lookbehind: true\n          }\n        }\n      },\n      // # ...\n      label: {\n        pattern: /^([\\t ]*)#[\\t ]*\\w+[\\t ]*$/m,\n        lookbehind: true,\n        alias: 'regex'\n      },\n      command: {\n        pattern: /^([\\t ]*)@\\w+(?=[\\t ]|$).*/m,\n        lookbehind: true,\n        alias: 'function',\n        inside: {\n          'command-name': /^@\\w+/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'command-params': {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            inside: params\n          }\n        }\n      },\n      // Generic is any line that doesn't start with operators: ;>#@\n      'generic-text': {\n        pattern: /(^[ \\t]*)[^#@>;\\s].*/m,\n        lookbehind: true,\n        alias: 'punctuation',\n        inside: {\n          // \\{ ... \\} ... \\[ ... \\] ... \\\"\n          'escaped-char': /\\\\[{}\\[\\]\"]/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'inline-command': {\n            pattern: /\\[[\\t ]*\\w[^\\r\\n\\[\\]]*\\]/,\n            greedy: true,\n            alias: 'function',\n            inside: {\n              'command-params': {\n                pattern: /(^\\[[\\t ]*\\w+\\b)[\\s\\S]+(?=\\]$)/,\n                lookbehind: true,\n                inside: params\n              },\n              'command-param-name': {\n                pattern: /^(\\[[\\t ]*)\\w+/,\n                lookbehind: true,\n                alias: 'name'\n              },\n              'start-stop-char': /[\\[\\]]/\n            }\n          }\n        }\n      }\n    };\n    Prism.languages.nani = Prism.languages['naniscript'];\n    /** @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token */\n    /**\n     * This hook is used to validate generic-text tokens for balanced brackets.\n     * Mark token as bad-line when contains not balanced brackets: {},[]\n     */\n    Prism.hooks.add('after-tokenize', function (env) {\n      /** @type {(Token | string)[]} */\n      var tokens = env.tokens;\n      tokens.forEach(function (token) {\n        if (typeof token !== 'string' && token.type === 'generic-text') {\n          var content = getTextContent(token);\n          if (!isBracketsBalanced(content)) {\n            token.type = 'bad-line';\n            token.content = content;\n          }\n        }\n      });\n    });\n    /**\n     * @param {string} input\n     * @returns {boolean}\n     */\n    function isBracketsBalanced(input) {\n      var brackets = '[]{}';\n      var stack = [];\n      for (var i = 0; i < input.length; i++) {\n        var bracket = input[i];\n        var bracketsIndex = brackets.indexOf(bracket);\n        if (bracketsIndex !== -1) {\n          if (bracketsIndex % 2 === 0) {\n            stack.push(bracketsIndex + 1);\n          } else if (stack.pop() !== bracketsIndex) {\n            return false;\n          }\n        }\n      }\n      return stack.length === 0;\n    }\n    /**\n     * @param {string | Token | (string | Token)[]} token\n     * @returns {string}\n     */\n    function getTextContent(token) {\n      if (typeof token === 'string') {\n        return token;\n      } else if (Array.isArray(token)) {\n        return token.map(getTextContent).join('');\n      } else {\n        return getTextContent(token.content);\n      }\n    }\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "naniscript", "displayName", "aliases", "Prism", "expressionDef", "params", "pattern", "alias", "lookbehind", "greedy", "languages", "comment", "define", "inside", "value", "key", "label", "command", "expression", "nani", "hooks", "add", "env", "tokens", "for<PERSON>ach", "token", "type", "content", "getTextContent", "isBracketsBalanced", "input", "brackets", "stack", "i", "length", "bracket", "bracketsIndex", "indexOf", "push", "pop", "Array", "isArray", "map", "join"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/naniscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = naniscript\nnaniscript.displayName = 'naniscript'\nnaniscript.aliases = []\nfunction naniscript(Prism) {\n  ;(function (Prism) {\n    var expressionDef = /\\{[^\\r\\n\\[\\]{}]*\\}/\n    var params = {\n      'quoted-string': {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        alias: 'operator'\n      },\n      'command-param-id': {\n        pattern: /(\\s)\\w+:/,\n        lookbehind: true,\n        alias: 'property'\n      },\n      'command-param-value': [\n        {\n          pattern: expressionDef,\n          alias: 'selector'\n        },\n        {\n          pattern: /([\\t ])\\S+/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'operator'\n        },\n        {\n          pattern: /\\S(?:.*\\S)?/,\n          alias: 'operator'\n        }\n      ]\n    }\n    Prism.languages.naniscript = {\n      // ; ...\n      comment: {\n        pattern: /^([\\t ]*);.*/m,\n        lookbehind: true\n      },\n      // > ...\n      // Define is a control line starting with '>' followed by a word, a space and a text.\n      define: {\n        pattern: /^>.+/m,\n        alias: 'tag',\n        inside: {\n          value: {\n            pattern: /(^>\\w+[\\t ]+)(?!\\s)[^{}\\r\\n]+/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          key: {\n            pattern: /(^>)\\w+/,\n            lookbehind: true\n          }\n        }\n      },\n      // # ...\n      label: {\n        pattern: /^([\\t ]*)#[\\t ]*\\w+[\\t ]*$/m,\n        lookbehind: true,\n        alias: 'regex'\n      },\n      command: {\n        pattern: /^([\\t ]*)@\\w+(?=[\\t ]|$).*/m,\n        lookbehind: true,\n        alias: 'function',\n        inside: {\n          'command-name': /^@\\w+/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'command-params': {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            inside: params\n          }\n        }\n      },\n      // Generic is any line that doesn't start with operators: ;>#@\n      'generic-text': {\n        pattern: /(^[ \\t]*)[^#@>;\\s].*/m,\n        lookbehind: true,\n        alias: 'punctuation',\n        inside: {\n          // \\{ ... \\} ... \\[ ... \\] ... \\\"\n          'escaped-char': /\\\\[{}\\[\\]\"]/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'inline-command': {\n            pattern: /\\[[\\t ]*\\w[^\\r\\n\\[\\]]*\\]/,\n            greedy: true,\n            alias: 'function',\n            inside: {\n              'command-params': {\n                pattern: /(^\\[[\\t ]*\\w+\\b)[\\s\\S]+(?=\\]$)/,\n                lookbehind: true,\n                inside: params\n              },\n              'command-param-name': {\n                pattern: /^(\\[[\\t ]*)\\w+/,\n                lookbehind: true,\n                alias: 'name'\n              },\n              'start-stop-char': /[\\[\\]]/\n            }\n          }\n        }\n      }\n    }\n    Prism.languages.nani = Prism.languages['naniscript']\n    /** @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token */\n    /**\n     * This hook is used to validate generic-text tokens for balanced brackets.\n     * Mark token as bad-line when contains not balanced brackets: {},[]\n     */\n    Prism.hooks.add('after-tokenize', function (env) {\n      /** @type {(Token | string)[]} */\n      var tokens = env.tokens\n      tokens.forEach(function (token) {\n        if (typeof token !== 'string' && token.type === 'generic-text') {\n          var content = getTextContent(token)\n          if (!isBracketsBalanced(content)) {\n            token.type = 'bad-line'\n            token.content = content\n          }\n        }\n      })\n    })\n    /**\n     * @param {string} input\n     * @returns {boolean}\n     */\n    function isBracketsBalanced(input) {\n      var brackets = '[]{}'\n      var stack = []\n      for (var i = 0; i < input.length; i++) {\n        var bracket = input[i]\n        var bracketsIndex = brackets.indexOf(bracket)\n        if (bracketsIndex !== -1) {\n          if (bracketsIndex % 2 === 0) {\n            stack.push(bracketsIndex + 1)\n          } else if (stack.pop() !== bracketsIndex) {\n            return false\n          }\n        }\n      }\n      return stack.length === 0\n    }\n    /**\n     * @param {string | Token | (string | Token)[]} token\n     * @returns {string}\n     */\n    function getTextContent(token) {\n      if (typeof token === 'string') {\n        return token\n      } else if (Array.isArray(token)) {\n        return token.map(getTextContent).join('')\n      } else {\n        return getTextContent(token.content)\n      }\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,EAAE;AACvB,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,aAAa,GAAG,oBAAoB;IACxC,IAAIC,MAAM,GAAG;MACX,eAAe,EAAE;QACfC,OAAO,EAAE,mBAAmB;QAC5BC,KAAK,EAAE;MACT,CAAC;MACD,kBAAkB,EAAE;QAClBD,OAAO,EAAE,UAAU;QACnBE,UAAU,EAAE,IAAI;QAChBD,KAAK,EAAE;MACT,CAAC;MACD,qBAAqB,EAAE,CACrB;QACED,OAAO,EAAEF,aAAa;QACtBG,KAAK,EAAE;MACT,CAAC,EACD;QACED,OAAO,EAAE,YAAY;QACrBE,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZF,KAAK,EAAE;MACT,CAAC,EACD;QACED,OAAO,EAAE,aAAa;QACtBC,KAAK,EAAE;MACT,CAAC;IAEL,CAAC;IACDJ,KAAK,CAACO,SAAS,CAACV,UAAU,GAAG;MAC3B;MACAW,OAAO,EAAE;QACPL,OAAO,EAAE,eAAe;QACxBE,UAAU,EAAE;MACd,CAAC;MACD;MACA;MACAI,MAAM,EAAE;QACNN,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE,KAAK;QACZM,MAAM,EAAE;UACNC,KAAK,EAAE;YACLR,OAAO,EAAE,+BAA+B;YACxCE,UAAU,EAAE,IAAI;YAChBD,KAAK,EAAE;UACT,CAAC;UACDQ,GAAG,EAAE;YACHT,OAAO,EAAE,SAAS;YAClBE,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACD;MACAQ,KAAK,EAAE;QACLV,OAAO,EAAE,6BAA6B;QACtCE,UAAU,EAAE,IAAI;QAChBD,KAAK,EAAE;MACT,CAAC;MACDU,OAAO,EAAE;QACPX,OAAO,EAAE,6BAA6B;QACtCE,UAAU,EAAE,IAAI;QAChBD,KAAK,EAAE,UAAU;QACjBM,MAAM,EAAE;UACN,cAAc,EAAE,OAAO;UACvBK,UAAU,EAAE;YACVZ,OAAO,EAAEF,aAAa;YACtBK,MAAM,EAAE,IAAI;YACZF,KAAK,EAAE;UACT,CAAC;UACD,gBAAgB,EAAE;YAChBD,OAAO,EAAE,cAAc;YACvBO,MAAM,EAAER;UACV;QACF;MACF,CAAC;MACD;MACA,cAAc,EAAE;QACdC,OAAO,EAAE,uBAAuB;QAChCE,UAAU,EAAE,IAAI;QAChBD,KAAK,EAAE,aAAa;QACpBM,MAAM,EAAE;UACN;UACA,cAAc,EAAE,aAAa;UAC7BK,UAAU,EAAE;YACVZ,OAAO,EAAEF,aAAa;YACtBK,MAAM,EAAE,IAAI;YACZF,KAAK,EAAE;UACT,CAAC;UACD,gBAAgB,EAAE;YAChBD,OAAO,EAAE,0BAA0B;YACnCG,MAAM,EAAE,IAAI;YACZF,KAAK,EAAE,UAAU;YACjBM,MAAM,EAAE;cACN,gBAAgB,EAAE;gBAChBP,OAAO,EAAE,gCAAgC;gBACzCE,UAAU,EAAE,IAAI;gBAChBK,MAAM,EAAER;cACV,CAAC;cACD,oBAAoB,EAAE;gBACpBC,OAAO,EAAE,gBAAgB;gBACzBE,UAAU,EAAE,IAAI;gBAChBD,KAAK,EAAE;cACT,CAAC;cACD,iBAAiB,EAAE;YACrB;UACF;QACF;MACF;IACF,CAAC;IACDJ,KAAK,CAACO,SAAS,CAACS,IAAI,GAAGhB,KAAK,CAACO,SAAS,CAAC,YAAY,CAAC;IACpD;IACA;AACJ;AACA;AACA;IACIP,KAAK,CAACiB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/C;MACA,IAAIC,MAAM,GAAGD,GAAG,CAACC,MAAM;MACvBA,MAAM,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC9B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;UAC9D,IAAIC,OAAO,GAAGC,cAAc,CAACH,KAAK,CAAC;UACnC,IAAI,CAACI,kBAAkB,CAACF,OAAO,CAAC,EAAE;YAChCF,KAAK,CAACC,IAAI,GAAG,UAAU;YACvBD,KAAK,CAACE,OAAO,GAAGA,OAAO;UACzB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;AACJ;AACA;AACA;IACI,SAASE,kBAAkBA,CAACC,KAAK,EAAE;MACjC,IAAIC,QAAQ,GAAG,MAAM;MACrB,IAAIC,KAAK,GAAG,EAAE;MACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,IAAIE,OAAO,GAAGL,KAAK,CAACG,CAAC,CAAC;QACtB,IAAIG,aAAa,GAAGL,QAAQ,CAACM,OAAO,CAACF,OAAO,CAAC;QAC7C,IAAIC,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAIA,aAAa,GAAG,CAAC,KAAK,CAAC,EAAE;YAC3BJ,KAAK,CAACM,IAAI,CAACF,aAAa,GAAG,CAAC,CAAC;UAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACO,GAAG,CAAC,CAAC,KAAKH,aAAa,EAAE;YACxC,OAAO,KAAK;UACd;QACF;MACF;MACA,OAAOJ,KAAK,CAACE,MAAM,KAAK,CAAC;IAC3B;IACA;AACJ;AACA;AACA;IACI,SAASN,cAAcA,CAACH,KAAK,EAAE;MAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOA,KAAK;MACd,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,EAAE;QAC/B,OAAOA,KAAK,CAACiB,GAAG,CAACd,cAAc,CAAC,CAACe,IAAI,CAAC,EAAE,CAAC;MAC3C,CAAC,MAAM;QACL,OAAOf,cAAc,CAACH,KAAK,CAACE,OAAO,CAAC;MACtC;IACF;EACF,CAAC,EAAExB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}