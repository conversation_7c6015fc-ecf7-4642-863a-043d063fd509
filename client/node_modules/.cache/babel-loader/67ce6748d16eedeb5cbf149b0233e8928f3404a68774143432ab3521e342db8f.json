{"ast": null, "code": "/*\nLanguage: Makefile\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/make/manual/html_node/Introduction.html\nCategory: common\n*/\n\nfunction makefile(hljs) {\n  /* Variables: simple (eg $(var)) and special (eg $@) */\n  const VARIABLE = {\n    className: 'variable',\n    variants: [{\n      begin: '\\\\$\\\\(' + hljs.UNDERSCORE_IDENT_RE + '\\\\)',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: /\\$[@%<?\\^\\+\\*]/\n    }]\n  };\n  /* Quoted string with variables inside */\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [hljs.BACKSLASH_ESCAPE, VARIABLE]\n  };\n  /* Function: $(func arg,...) */\n  const FUNC = {\n    className: 'variable',\n    begin: /\\$\\([\\w-]+\\s/,\n    end: /\\)/,\n    keywords: {\n      built_in: 'subst patsubst strip findstring filter filter-out sort ' + 'word wordlist firstword lastword dir notdir suffix basename ' + 'addsuffix addprefix join wildcard realpath abspath error warning ' + 'shell origin flavor foreach if or and call eval file value'\n    },\n    contains: [VARIABLE]\n  };\n  /* Variable assignment */\n  const ASSIGNMENT = {\n    begin: '^' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*(?=[:+?]?=)'\n  };\n  /* Meta targets (.PHONY) */\n  const META = {\n    className: 'meta',\n    begin: /^\\.PHONY:/,\n    end: /$/,\n    keywords: {\n      $pattern: /[\\.\\w]+/,\n      'meta-keyword': '.PHONY'\n    }\n  };\n  /* Targets */\n  const TARGET = {\n    className: 'section',\n    begin: /^[^\\s]+:/,\n    end: /$/,\n    contains: [VARIABLE]\n  };\n  return {\n    name: 'Makefile',\n    aliases: ['mk', 'mak', 'make'],\n    keywords: {\n      $pattern: /[\\w-]+/,\n      keyword: 'define endef undefine ifdef ifndef ifeq ifneq else endif ' + 'include -include sinclude override export unexport private vpath'\n    },\n    contains: [hljs.HASH_COMMENT_MODE, VARIABLE, QUOTE_STRING, FUNC, ASSIGNMENT, META, TARGET]\n  };\n}\nmodule.exports = makefile;", "map": {"version": 3, "names": ["makefile", "hljs", "VARIABLE", "className", "variants", "begin", "UNDERSCORE_IDENT_RE", "contains", "BACKSLASH_ESCAPE", "QUOTE_STRING", "end", "FUNC", "keywords", "built_in", "ASSIGNMENT", "META", "$pattern", "TARGET", "name", "aliases", "keyword", "HASH_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/makefile.js"], "sourcesContent": ["/*\nLanguage: Makefile\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/make/manual/html_node/Introduction.html\nCategory: common\n*/\n\nfunction makefile(hljs) {\n  /* Variables: simple (eg $(var)) and special (eg $@) */\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      {\n        begin: '\\\\$\\\\(' + hljs.UNDERSCORE_IDENT_RE + '\\\\)',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: /\\$[@%<?\\^\\+\\*]/\n      }\n    ]\n  };\n  /* Quoted string with variables inside */\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VARIABLE\n    ]\n  };\n  /* Function: $(func arg,...) */\n  const FUNC = {\n    className: 'variable',\n    begin: /\\$\\([\\w-]+\\s/,\n    end: /\\)/,\n    keywords: {\n      built_in:\n        'subst patsubst strip findstring filter filter-out sort ' +\n        'word wordlist firstword lastword dir notdir suffix basename ' +\n        'addsuffix addprefix join wildcard realpath abspath error warning ' +\n        'shell origin flavor foreach if or and call eval file value'\n    },\n    contains: [ VARIABLE ]\n  };\n  /* Variable assignment */\n  const ASSIGNMENT = {\n    begin: '^' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*(?=[:+?]?=)'\n  };\n  /* Meta targets (.PHONY) */\n  const META = {\n    className: 'meta',\n    begin: /^\\.PHONY:/,\n    end: /$/,\n    keywords: {\n      $pattern: /[\\.\\w]+/,\n      'meta-keyword': '.PHONY'\n    }\n  };\n  /* Targets */\n  const TARGET = {\n    className: 'section',\n    begin: /^[^\\s]+:/,\n    end: /$/,\n    contains: [ VARIABLE ]\n  };\n  return {\n    name: 'Makefile',\n    aliases: [\n      'mk',\n      'mak',\n      'make',\n    ],\n    keywords: {\n      $pattern: /[\\w-]+/,\n      keyword: 'define endef undefine ifdef ifndef ifeq ifneq else endif ' +\n      'include -include sinclude override export unexport private vpath'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      VARIABLE,\n      QUOTE_STRING,\n      FUNC,\n      ASSIGNMENT,\n      META,\n      TARGET\n    ]\n  };\n}\n\nmodule.exports = makefile;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB;EACA,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,QAAQ,GAAGJ,IAAI,CAACK,mBAAmB,GAAG,KAAK;MAClDC,QAAQ,EAAE,CAAEN,IAAI,CAACO,gBAAgB;IACnC,CAAC,EACD;MACEH,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD;EACA,MAAMI,YAAY,GAAG;IACnBN,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE,GAAG;IACVK,GAAG,EAAE,GAAG;IACRH,QAAQ,EAAE,CACRN,IAAI,CAACO,gBAAgB,EACrBN,QAAQ;EAEZ,CAAC;EACD;EACA,MAAMS,IAAI,GAAG;IACXR,SAAS,EAAE,UAAU;IACrBE,KAAK,EAAE,cAAc;IACrBK,GAAG,EAAE,IAAI;IACTE,QAAQ,EAAE;MACRC,QAAQ,EACN,yDAAyD,GACzD,8DAA8D,GAC9D,mEAAmE,GACnE;IACJ,CAAC;IACDN,QAAQ,EAAE,CAAEL,QAAQ;EACtB,CAAC;EACD;EACA,MAAMY,UAAU,GAAG;IACjBT,KAAK,EAAE,GAAG,GAAGJ,IAAI,CAACK,mBAAmB,GAAG;EAC1C,CAAC;EACD;EACA,MAAMS,IAAI,GAAG;IACXZ,SAAS,EAAE,MAAM;IACjBE,KAAK,EAAE,WAAW;IAClBK,GAAG,EAAE,GAAG;IACRE,QAAQ,EAAE;MACRI,QAAQ,EAAE,SAAS;MACnB,cAAc,EAAE;IAClB;EACF,CAAC;EACD;EACA,MAAMC,MAAM,GAAG;IACbd,SAAS,EAAE,SAAS;IACpBE,KAAK,EAAE,UAAU;IACjBK,GAAG,EAAE,GAAG;IACRH,QAAQ,EAAE,CAAEL,QAAQ;EACtB,CAAC;EACD,OAAO;IACLgB,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CACP,IAAI,EACJ,KAAK,EACL,MAAM,CACP;IACDP,QAAQ,EAAE;MACRI,QAAQ,EAAE,QAAQ;MAClBI,OAAO,EAAE,2DAA2D,GACpE;IACF,CAAC;IACDb,QAAQ,EAAE,CACRN,IAAI,CAACoB,iBAAiB,EACtBnB,QAAQ,EACRO,YAAY,EACZE,IAAI,EACJG,UAAU,EACVC,IAAI,EACJE,MAAM;EAEV,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGvB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}