{"ast": null, "code": "/*\nLanguage: Test Anything Protocol\nDescription: TAP, the Test Anything Protocol, is a simple text-based interface between testing modules in a test harness.\nRequires: yaml.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://testanything.org\n*/\n\nfunction tap(hljs) {\n  return {\n    name: 'Test Anything Protocol',\n    case_insensitive: true,\n    contains: [hljs.HASH_COMMENT_MODE,\n    // version of format and total amount of testcases\n    {\n      className: 'meta',\n      variants: [{\n        begin: '^TAP version (\\\\d+)$'\n      }, {\n        begin: '^1\\\\.\\\\.(\\\\d+)$'\n      }]\n    },\n    // YAML block\n    {\n      begin: /---$/,\n      end: '\\\\.\\\\.\\\\.$',\n      subLanguage: 'yaml',\n      relevance: 0\n    },\n    // testcase number\n    {\n      className: 'number',\n      begin: ' (\\\\d+) '\n    },\n    // testcase status and description\n    {\n      className: 'symbol',\n      variants: [{\n        begin: '^ok'\n      }, {\n        begin: '^not ok'\n      }]\n    }]\n  };\n}\nmodule.exports = tap;", "map": {"version": 3, "names": ["tap", "hljs", "name", "case_insensitive", "contains", "HASH_COMMENT_MODE", "className", "variants", "begin", "end", "subLanguage", "relevance", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/tap.js"], "sourcesContent": ["/*\nLanguage: Test Anything Protocol\nDescription: TAP, the Test Anything Protocol, is a simple text-based interface between testing modules in a test harness.\nRequires: yaml.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://testanything.org\n*/\n\nfunction tap(hljs) {\n  return {\n    name: 'Test Anything Protocol',\n    case_insensitive: true,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      // version of format and total amount of testcases\n      {\n        className: 'meta',\n        variants: [\n          {\n            begin: '^TAP version (\\\\d+)$'\n          },\n          {\n            begin: '^1\\\\.\\\\.(\\\\d+)$'\n          }\n        ]\n      },\n      // YAML block\n      {\n        begin: /---$/,\n        end: '\\\\.\\\\.\\\\.$',\n        subLanguage: 'yaml',\n        relevance: 0\n      },\n      // testcase number\n      {\n        className: 'number',\n        begin: ' (\\\\d+) '\n      },\n      // testcase status and description\n      {\n        className: 'symbol',\n        variants: [\n          {\n            begin: '^ok'\n          },\n          {\n            begin: '^not ok'\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = tap;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,wBAAwB;IAC9BC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,CACRH,IAAI,CAACI,iBAAiB;IACtB;IACA;MACEC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC;IACD;IACA;MACEA,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,YAAY;MACjBC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEL,SAAS,EAAE,QAAQ;MACnBE,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACEF,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAI,MAAM,CAACC,OAAO,GAAGb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}