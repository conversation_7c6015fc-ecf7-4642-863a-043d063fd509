{"ast": null, "code": "'use strict';\n\nmodule.exports = purebasic;\npurebasic.displayName = 'purebasic';\npurebasic.aliases = [];\nfunction purebasic(Prism) {\n  /*\n  Original Code by <PERSON><PERSON>\n  !!MANY THANKS!! I never would have made this, rege<PERSON> and me will never be best friends ;)\n  ==> https://codepen.io/ImagineProgramming/details/JYydBy/\n  slightly changed to pass all tests\n  */\n  // PureBasic support, steal stuff from ansi-c\n  Prism.languages.purebasic = Prism.languages.extend('clike', {\n    comment: /;.*/,\n    keyword: /\\b(?:align|and|as|break|calldebugger|case|compilercase|compilerdefault|compilerelse|compilerelseif|compilerendif|compilerendselect|compilererror|compilerif|compilerselect|continue|data|datasection|debug|debuglevel|declare|declarec|declarecdll|declaredll|declaremodule|default|define|dim|disableasm|disabledebugger|disableexplicit|else|elseif|enableasm|enabledebugger|enableexplicit|end|enddatasection|enddeclaremodule|endenumeration|endif|endimport|endinterface|endmacro|endmodule|endprocedure|endselect|endstructure|endstructureunion|endwith|enumeration|extends|fakereturn|for|foreach|forever|global|gosub|goto|if|import|importc|includebinary|includefile|includepath|interface|macro|module|newlist|newmap|next|not|or|procedure|procedurec|procedurecdll|proceduredll|procedurereturn|protected|prototype|prototypec|read|redim|repeat|restore|return|runtime|select|shared|static|step|structure|structureunion|swap|threaded|to|until|wend|while|with|xincludefile|xor)\\b/i,\n    function: /\\b\\w+(?:\\.\\w+)?\\s*(?=\\()/,\n    number: /(?:\\$[\\da-f]+|\\b-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:e[+-]?\\d+)?)\\b/i,\n    operator: /(?:@\\*?|\\?|\\*)\\w+|-[>-]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|?\\||[~^%?*/@]/\n  });\n  Prism.languages.insertBefore('purebasic', 'keyword', {\n    tag: /#\\w+\\$?/,\n    asm: {\n      pattern: /(^[\\t ]*)!.*/m,\n      lookbehind: true,\n      alias: 'tag',\n      inside: {\n        comment: /;.*/,\n        string: {\n          pattern: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n          greedy: true\n        },\n        // Anonymous label references, i.e.: jmp @b\n        'label-reference-anonymous': {\n          pattern: /(!\\s*j[a-z]+\\s+)@[fb]/i,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        // Named label reference, i.e.: jne label1\n        'label-reference-addressed': {\n          pattern: /(!\\s*j[a-z]+\\s+)[A-Z._?$@][\\w.?$@~#]*/i,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        keyword: [/\\b(?:extern|global)\\b[^;\\r\\n]*/i, /\\b(?:CPU|DEFAULT|FLOAT)\\b.*/],\n        function: {\n          pattern: /^([\\t ]*!\\s*)[\\da-z]+(?=\\s|$)/im,\n          lookbehind: true\n        },\n        'function-inline': {\n          pattern: /(:\\s*)[\\da-z]+(?=\\s)/i,\n          lookbehind: true,\n          alias: 'function'\n        },\n        label: {\n          pattern: /^([\\t ]*!\\s*)[A-Za-z._?$@][\\w.?$@~#]*(?=:)/m,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        register: /\\b(?:st\\d|[xyz]mm\\d\\d?|[cdt]r\\d|r\\d\\d?[bwd]?|[er]?[abcd]x|[abcd][hl]|[er]?(?:bp|di|si|sp)|[cdefgs]s|mm\\d+)\\b/i,\n        number: /(?:\\b|-|(?=\\$))(?:0[hx](?:[\\da-f]*\\.)?[\\da-f]+(?:p[+-]?\\d+)?|\\d[\\da-f]+[hx]|\\$\\d[\\da-f]*|0[oq][0-7]+|[0-7]+[oq]|0[by][01]+|[01]+[by]|0[dt]\\d+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:\\.?e[+-]?\\d+)?[dt]?)\\b/i,\n        operator: /[\\[\\]*+\\-/%<>=&|$!,.:]/\n      }\n    }\n  });\n  delete Prism.languages.purebasic['class-name'];\n  delete Prism.languages.purebasic['boolean'];\n  Prism.languages.pbfasm = Prism.languages['purebasic'];\n}", "map": {"version": 3, "names": ["module", "exports", "purebasic", "displayName", "aliases", "Prism", "languages", "extend", "comment", "keyword", "function", "number", "operator", "insertBefore", "tag", "asm", "pattern", "lookbehind", "alias", "inside", "string", "greedy", "label", "register", "pbfasm"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/purebasic.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = purebasic\npurebasic.displayName = 'purebasic'\npurebasic.aliases = []\nfunction purebasic(Prism) {\n  /*\nOriginal Code by <PERSON><PERSON>\n!!MANY THANKS!! I never would have made this, rege<PERSON> and me will never be best friends ;)\n==> https://codepen.io/ImagineProgramming/details/JYydBy/\nslightly changed to pass all tests\n*/\n  // PureBasic support, steal stuff from ansi-c\n  Prism.languages.purebasic = Prism.languages.extend('clike', {\n    comment: /;.*/,\n    keyword:\n      /\\b(?:align|and|as|break|calldebugger|case|compilercase|compilerdefault|compilerelse|compilerelseif|compilerendif|compilerendselect|compilererror|compilerif|compilerselect|continue|data|datasection|debug|debuglevel|declare|declarec|declarecdll|declaredll|declaremodule|default|define|dim|disableasm|disabledebugger|disableexplicit|else|elseif|enableasm|enabledebugger|enableexplicit|end|enddatasection|enddeclaremodule|endenumeration|endif|endimport|endinterface|endmacro|endmodule|endprocedure|endselect|endstructure|endstructureunion|endwith|enumeration|extends|fakereturn|for|foreach|forever|global|gosub|goto|if|import|importc|includebinary|includefile|includepath|interface|macro|module|newlist|newmap|next|not|or|procedure|procedurec|procedurecdll|proceduredll|procedurereturn|protected|prototype|prototypec|read|redim|repeat|restore|return|runtime|select|shared|static|step|structure|structureunion|swap|threaded|to|until|wend|while|with|xincludefile|xor)\\b/i,\n    function: /\\b\\w+(?:\\.\\w+)?\\s*(?=\\()/,\n    number: /(?:\\$[\\da-f]+|\\b-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:e[+-]?\\d+)?)\\b/i,\n    operator:\n      /(?:@\\*?|\\?|\\*)\\w+|-[>-]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|?\\||[~^%?*/@]/\n  })\n  Prism.languages.insertBefore('purebasic', 'keyword', {\n    tag: /#\\w+\\$?/,\n    asm: {\n      pattern: /(^[\\t ]*)!.*/m,\n      lookbehind: true,\n      alias: 'tag',\n      inside: {\n        comment: /;.*/,\n        string: {\n          pattern: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n          greedy: true\n        },\n        // Anonymous label references, i.e.: jmp @b\n        'label-reference-anonymous': {\n          pattern: /(!\\s*j[a-z]+\\s+)@[fb]/i,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        // Named label reference, i.e.: jne label1\n        'label-reference-addressed': {\n          pattern: /(!\\s*j[a-z]+\\s+)[A-Z._?$@][\\w.?$@~#]*/i,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        keyword: [\n          /\\b(?:extern|global)\\b[^;\\r\\n]*/i,\n          /\\b(?:CPU|DEFAULT|FLOAT)\\b.*/\n        ],\n        function: {\n          pattern: /^([\\t ]*!\\s*)[\\da-z]+(?=\\s|$)/im,\n          lookbehind: true\n        },\n        'function-inline': {\n          pattern: /(:\\s*)[\\da-z]+(?=\\s)/i,\n          lookbehind: true,\n          alias: 'function'\n        },\n        label: {\n          pattern: /^([\\t ]*!\\s*)[A-Za-z._?$@][\\w.?$@~#]*(?=:)/m,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        register:\n          /\\b(?:st\\d|[xyz]mm\\d\\d?|[cdt]r\\d|r\\d\\d?[bwd]?|[er]?[abcd]x|[abcd][hl]|[er]?(?:bp|di|si|sp)|[cdefgs]s|mm\\d+)\\b/i,\n        number:\n          /(?:\\b|-|(?=\\$))(?:0[hx](?:[\\da-f]*\\.)?[\\da-f]+(?:p[+-]?\\d+)?|\\d[\\da-f]+[hx]|\\$\\d[\\da-f]*|0[oq][0-7]+|[0-7]+[oq]|0[by][01]+|[01]+[by]|0[dt]\\d+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:\\.?e[+-]?\\d+)?[dt]?)\\b/i,\n        operator: /[\\[\\]*+\\-/%<>=&|$!,.:]/\n      }\n    }\n  })\n  delete Prism.languages.purebasic['class-name']\n  delete Prism.languages.purebasic['boolean']\n  Prism.languages.pbfasm = Prism.languages['purebasic']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxB;AACF;AACA;AACA;AACA;AACA;EACE;EACAA,KAAK,CAACC,SAAS,CAACJ,SAAS,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IAC1DC,OAAO,EAAE,KAAK;IACdC,OAAO,EACL,s8BAAs8B;IACx8BC,QAAQ,EAAE,0BAA0B;IACpCC,MAAM,EAAE,6DAA6D;IACrEC,QAAQ,EACN;EACJ,CAAC,CAAC;EACFP,KAAK,CAACC,SAAS,CAACO,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE;IACnDC,GAAG,EAAE,SAAS;IACdC,GAAG,EAAE;MACHC,OAAO,EAAE,eAAe;MACxBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE;QACNX,OAAO,EAAE,KAAK;QACdY,MAAM,EAAE;UACNJ,OAAO,EAAE,mCAAmC;UAC5CK,MAAM,EAAE;QACV,CAAC;QACD;QACA,2BAA2B,EAAE;UAC3BL,OAAO,EAAE,wBAAwB;UACjCC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE;QACT,CAAC;QACD;QACA,2BAA2B,EAAE;UAC3BF,OAAO,EAAE,wCAAwC;UACjDC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE;QACT,CAAC;QACDT,OAAO,EAAE,CACP,iCAAiC,EACjC,6BAA6B,CAC9B;QACDC,QAAQ,EAAE;UACRM,OAAO,EAAE,iCAAiC;UAC1CC,UAAU,EAAE;QACd,CAAC;QACD,iBAAiB,EAAE;UACjBD,OAAO,EAAE,uBAAuB;UAChCC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE;QACT,CAAC;QACDI,KAAK,EAAE;UACLN,OAAO,EAAE,6CAA6C;UACtDC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE;QACT,CAAC;QACDK,QAAQ,EACN,+GAA+G;QACjHZ,MAAM,EACJ,iMAAiM;QACnMC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC,CAAC;EACF,OAAOP,KAAK,CAACC,SAAS,CAACJ,SAAS,CAAC,YAAY,CAAC;EAC9C,OAAOG,KAAK,CAACC,SAAS,CAACJ,SAAS,CAAC,SAAS,CAAC;EAC3CG,KAAK,CAACC,SAAS,CAACkB,MAAM,GAAGnB,KAAK,CAACC,SAAS,CAAC,WAAW,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}