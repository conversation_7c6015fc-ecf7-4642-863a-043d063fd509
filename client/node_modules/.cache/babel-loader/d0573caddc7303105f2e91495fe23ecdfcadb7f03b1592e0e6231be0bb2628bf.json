{"ast": null, "code": "/*\nLanguage: C/AL\nAuthor: <PERSON> <<EMAIL>>\nDescription: Provides highlighting of Microsoft Dynamics NAV C/AL code files\nWebsite: https://docs.microsoft.com/en-us/dynamics-nav/programming-in-c-al\n*/\n\n/** @type LanguageFn */\nfunction cal(hljs) {\n  const KEYWORDS = 'div mod in and or not xor asserterror begin case do downto else end exit for if of repeat then to ' + 'until while with var';\n  const LITERALS = 'false true';\n  const COMMENT_MODES = [hljs.C_LINE_COMMENT_MODE, hljs.COMMENT(/\\{/, /\\}/, {\n    relevance: 0\n  }), hljs.COMMENT(/\\(\\*/, /\\*\\)/, {\n    relevance: 10\n  })];\n  const STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/,\n    contains: [{\n      begin: /''/\n    }]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: /(#\\d+)+/\n  };\n  const DATE = {\n    className: 'number',\n    begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(DT|D|T)',\n    relevance: 0\n  };\n  const DBL_QUOTED_VARIABLE = {\n    className: 'string',\n    // not a string technically but makes sense to be highlighted in the same style\n    begin: '\"',\n    end: '\"'\n  };\n  const PROCEDURE = {\n    className: 'function',\n    beginKeywords: 'procedure',\n    end: /[:;]/,\n    keywords: 'procedure|10',\n    contains: [hljs.TITLE_MODE, {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS,\n      contains: [STRING, CHAR_STRING]\n    }].concat(COMMENT_MODES)\n  };\n  const OBJECT = {\n    className: 'class',\n    begin: 'OBJECT (Table|Form|Report|Dataport|Codeunit|XMLport|MenuSuite|Page|Query) (\\\\d+) ([^\\\\r\\\\n]+)',\n    returnBegin: true,\n    contains: [hljs.TITLE_MODE, PROCEDURE]\n  };\n  return {\n    name: 'C/AL',\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      literal: LITERALS\n    },\n    illegal: /\\/\\*/,\n    contains: [STRING, CHAR_STRING, DATE, DBL_QUOTED_VARIABLE, hljs.NUMBER_MODE, OBJECT, PROCEDURE]\n  };\n}\nmodule.exports = cal;", "map": {"version": 3, "names": ["cal", "hljs", "KEYWORDS", "LITERALS", "COMMENT_MODES", "C_LINE_COMMENT_MODE", "COMMENT", "relevance", "STRING", "className", "begin", "end", "contains", "CHAR_STRING", "DATE", "DBL_QUOTED_VARIABLE", "PROCEDURE", "beginKeywords", "keywords", "TITLE_MODE", "concat", "OBJECT", "returnBegin", "name", "case_insensitive", "keyword", "literal", "illegal", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/cal.js"], "sourcesContent": ["/*\nLanguage: C/AL\nAuthor: <PERSON> <<EMAIL>>\nDescription: Provides highlighting of Microsoft Dynamics NAV C/AL code files\nWebsite: https://docs.microsoft.com/en-us/dynamics-nav/programming-in-c-al\n*/\n\n/** @type LanguageFn */\nfunction cal(hljs) {\n  const KEYWORDS =\n    'div mod in and or not xor asserterror begin case do downto else end exit for if of repeat then to ' +\n    'until while with var';\n  const LITERALS = 'false true';\n  const COMMENT_MODES = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.COMMENT(\n      /\\{/,\n      /\\}/,\n      {\n        relevance: 0\n      }\n    ),\n    hljs.COMMENT(\n      /\\(\\*/,\n      /\\*\\)/,\n      {\n        relevance: 10\n      }\n    )\n  ];\n  const STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/,\n    contains: [{\n      begin: /''/\n    }]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: /(#\\d+)+/\n  };\n  const DATE = {\n    className: 'number',\n    begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(DT|D|T)',\n    relevance: 0\n  };\n  const DBL_QUOTED_VARIABLE = {\n    className: 'string', // not a string technically but makes sense to be highlighted in the same style\n    begin: '\"',\n    end: '\"'\n  };\n\n  const PROCEDURE = {\n    className: 'function',\n    beginKeywords: 'procedure',\n    end: /[:;]/,\n    keywords: 'procedure|10',\n    contains: [\n      hljs.TITLE_MODE,\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING\n        ]\n      }\n    ].concat(COMMENT_MODES)\n  };\n\n  const OBJECT = {\n    className: 'class',\n    begin: 'OBJECT (Table|Form|Report|Dataport|Codeunit|XMLport|MenuSuite|Page|Query) (\\\\d+) ([^\\\\r\\\\n]+)',\n    returnBegin: true,\n    contains: [\n      hljs.TITLE_MODE,\n      PROCEDURE\n    ]\n  };\n\n  return {\n    name: 'C/AL',\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      literal: LITERALS\n    },\n    illegal: /\\/\\*/,\n    contains: [\n      STRING,\n      CHAR_STRING,\n      DATE,\n      DBL_QUOTED_VARIABLE,\n      hljs.NUMBER_MODE,\n      OBJECT,\n      PROCEDURE\n    ]\n  };\n}\n\nmodule.exports = cal;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,QAAQ,GACZ,oGAAoG,GACpG,sBAAsB;EACxB,MAAMC,QAAQ,GAAG,YAAY;EAC7B,MAAMC,aAAa,GAAG,CACpBH,IAAI,CAACI,mBAAmB,EACxBJ,IAAI,CAACK,OAAO,CACV,IAAI,EACJ,IAAI,EACJ;IACEC,SAAS,EAAE;EACb,CACF,CAAC,EACDN,IAAI,CAACK,OAAO,CACV,MAAM,EACN,MAAM,EACN;IACEC,SAAS,EAAE;EACb,CACF,CAAC,CACF;EACD,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CAAC;MACTF,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,MAAMG,WAAW,GAAG;IAClBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMI,IAAI,GAAG;IACXL,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,2BAA2B;IAClCH,SAAS,EAAE;EACb,CAAC;EACD,MAAMQ,mBAAmB,GAAG;IAC1BN,SAAS,EAAE,QAAQ;IAAE;IACrBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE;EACP,CAAC;EAED,MAAMK,SAAS,GAAG;IAChBP,SAAS,EAAE,UAAU;IACrBQ,aAAa,EAAE,WAAW;IAC1BN,GAAG,EAAE,MAAM;IACXO,QAAQ,EAAE,cAAc;IACxBN,QAAQ,EAAE,CACRX,IAAI,CAACkB,UAAU,EACf;MACEV,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAEhB,QAAQ;MAClBU,QAAQ,EAAE,CACRJ,MAAM,EACNK,WAAW;IAEf,CAAC,CACF,CAACO,MAAM,CAAChB,aAAa;EACxB,CAAC;EAED,MAAMiB,MAAM,GAAG;IACbZ,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,+FAA+F;IACtGY,WAAW,EAAE,IAAI;IACjBV,QAAQ,EAAE,CACRX,IAAI,CAACkB,UAAU,EACfH,SAAS;EAEb,CAAC;EAED,OAAO;IACLO,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBN,QAAQ,EAAE;MACRO,OAAO,EAAEvB,QAAQ;MACjBwB,OAAO,EAAEvB;IACX,CAAC;IACDwB,OAAO,EAAE,MAAM;IACff,QAAQ,EAAE,CACRJ,MAAM,EACNK,WAAW,EACXC,IAAI,EACJC,mBAAmB,EACnBd,IAAI,CAAC2B,WAAW,EAChBP,MAAM,EACNL,SAAS;EAEb,CAAC;AACH;AAEAa,MAAM,CAACC,OAAO,GAAG9B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}