{"ast": null, "code": "/*\nLanguage: Ada\nAuthor: <PERSON> <<EMAIL>>\nDescription: Ada is a general-purpose programming language that has great support for saftey critical and real-time applications.\n             It has been developed by the DoD and thus has been used in military and safety-critical applications (like civil aviation).\n             The first version appeared in the 80s, but it's still actively developed today with\n             the newest standard being Ada2012.\n*/\n\n// We try to support full Ada2012\n//\n// We highlight all appearances of types, keywords, literals (string, char, number, bool)\n// and titles (user defined function/procedure/package)\n// CSS classes are set accordingly\n//\n// Languages causing problems for language detection:\n// xml (broken by Foo : Bar type), elm (broken by Foo : Bar type), vbscript-html (broken by body keyword)\n// sql (ada default.txt has a lot of sql keywords)\n\n/** @type LanguageFn */\nfunction ada(hljs) {\n  // Regular expression for Ada numeric literals.\n  // stolen form the VHDL highlighter\n\n  // Decimal literal:\n  const INTEGER_RE = '\\\\d(_|\\\\d)*';\n  const EXPONENT_RE = '[eE][-+]?' + INTEGER_RE;\n  const DECIMAL_LITERAL_RE = INTEGER_RE + '(\\\\.' + INTEGER_RE + ')?' + '(' + EXPONENT_RE + ')?';\n\n  // Based literal:\n  const BASED_INTEGER_RE = '\\\\w+';\n  const BASED_LITERAL_RE = INTEGER_RE + '#' + BASED_INTEGER_RE + '(\\\\.' + BASED_INTEGER_RE + ')?' + '#' + '(' + EXPONENT_RE + ')?';\n  const NUMBER_RE = '\\\\b(' + BASED_LITERAL_RE + '|' + DECIMAL_LITERAL_RE + ')';\n\n  // Identifier regex\n  const ID_REGEX = '[A-Za-z](_?[A-Za-z0-9.])*';\n\n  // bad chars, only allowed in literals\n  const BAD_CHARS = `[]\\\\{\\\\}%#'\"`;\n\n  // Ada doesn't have block comments, only line comments\n  const COMMENTS = hljs.COMMENT('--', '$');\n\n  // variable declarations of the form\n  // Foo : Bar := Baz;\n  // where only Bar will be highlighted\n  const VAR_DECLS = {\n    // TODO: These spaces are not required by the Ada syntax\n    // however, I have yet to see handwritten Ada code where\n    // someone does not put spaces around :\n    begin: '\\\\s+:\\\\s+',\n    end: '\\\\s*(:=|;|\\\\)|=>|$)',\n    // endsWithParent: true,\n    // returnBegin: true,\n    illegal: BAD_CHARS,\n    contains: [{\n      // workaround to avoid highlighting\n      // named loops and declare blocks\n      beginKeywords: 'loop for declare others',\n      endsParent: true\n    }, {\n      // properly highlight all modifiers\n      className: 'keyword',\n      beginKeywords: 'not null constant access function procedure in out aliased exception'\n    }, {\n      className: 'type',\n      begin: ID_REGEX,\n      endsParent: true,\n      relevance: 0\n    }]\n  };\n  return {\n    name: 'Ada',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'abort else new return abs elsif not reverse abstract end ' + 'accept entry select access exception of separate aliased exit or some ' + 'all others subtype and for out synchronized array function overriding ' + 'at tagged generic package task begin goto pragma terminate ' + 'body private then if procedure type case in protected constant interface ' + 'is raise use declare range delay limited record when delta loop rem while ' + 'digits renames with do mod requeue xor',\n      literal: 'True False'\n    },\n    contains: [COMMENTS,\n    // strings \"foobar\"\n    {\n      className: 'string',\n      begin: /\"/,\n      end: /\"/,\n      contains: [{\n        begin: /\"\"/,\n        relevance: 0\n      }]\n    },\n    // characters ''\n    {\n      // character literals always contain one char\n      className: 'string',\n      begin: /'.'/\n    }, {\n      // number literals\n      className: 'number',\n      begin: NUMBER_RE,\n      relevance: 0\n    }, {\n      // Attributes\n      className: 'symbol',\n      begin: \"'\" + ID_REGEX\n    }, {\n      // package definition, maybe inside generic\n      className: 'title',\n      begin: '(\\\\bwith\\\\s+)?(\\\\bprivate\\\\s+)?\\\\bpackage\\\\s+(\\\\bbody\\\\s+)?',\n      end: '(is|$)',\n      keywords: 'package body',\n      excludeBegin: true,\n      excludeEnd: true,\n      illegal: BAD_CHARS\n    }, {\n      // function/procedure declaration/definition\n      // maybe inside generic\n      begin: '(\\\\b(with|overriding)\\\\s+)?\\\\b(function|procedure)\\\\s+',\n      end: '(\\\\bis|\\\\bwith|\\\\brenames|\\\\)\\\\s*;)',\n      keywords: 'overriding function procedure with is renames return',\n      // we need to re-match the 'function' keyword, so that\n      // the title mode below matches only exactly once\n      returnBegin: true,\n      contains: [COMMENTS, {\n        // name of the function/procedure\n        className: 'title',\n        begin: '(\\\\bwith\\\\s+)?\\\\b(function|procedure)\\\\s+',\n        end: '(\\\\(|\\\\s+|$)',\n        excludeBegin: true,\n        excludeEnd: true,\n        illegal: BAD_CHARS\n      },\n      // 'self'\n      // // parameter types\n      VAR_DECLS, {\n        // return type\n        className: 'type',\n        begin: '\\\\breturn\\\\s+',\n        end: '(\\\\s+|;|$)',\n        keywords: 'return',\n        excludeBegin: true,\n        excludeEnd: true,\n        // we are done with functions\n        endsParent: true,\n        illegal: BAD_CHARS\n      }]\n    }, {\n      // new type declarations\n      // maybe inside generic\n      className: 'type',\n      begin: '\\\\b(sub)?type\\\\s+',\n      end: '\\\\s+',\n      keywords: 'type',\n      excludeBegin: true,\n      illegal: BAD_CHARS\n    },\n    // see comment above the definition\n    VAR_DECLS\n\n    // no markup\n    // relevance boosters for small snippets\n    // {begin: '\\\\s*=>\\\\s*'},\n    // {begin: '\\\\s*:=\\\\s*'},\n    // {begin: '\\\\s+:=\\\\s+'},\n    ]\n  };\n}\nmodule.exports = ada;", "map": {"version": 3, "names": ["ada", "hljs", "INTEGER_RE", "EXPONENT_RE", "DECIMAL_LITERAL_RE", "BASED_INTEGER_RE", "BASED_LITERAL_RE", "NUMBER_RE", "ID_REGEX", "BAD_CHARS", "COMMENTS", "COMMENT", "VAR_DECLS", "begin", "end", "illegal", "contains", "beginKeywords", "endsParent", "className", "relevance", "name", "case_insensitive", "keywords", "keyword", "literal", "excludeBegin", "excludeEnd", "returnBegin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/ada.js"], "sourcesContent": ["/*\nLanguage: Ada\nAuthor: <PERSON> <<EMAIL>>\nDescription: Ada is a general-purpose programming language that has great support for saftey critical and real-time applications.\n             It has been developed by the DoD and thus has been used in military and safety-critical applications (like civil aviation).\n             The first version appeared in the 80s, but it's still actively developed today with\n             the newest standard being Ada2012.\n*/\n\n// We try to support full Ada2012\n//\n// We highlight all appearances of types, keywords, literals (string, char, number, bool)\n// and titles (user defined function/procedure/package)\n// CSS classes are set accordingly\n//\n// Languages causing problems for language detection:\n// xml (broken by Foo : Bar type), elm (broken by Foo : Bar type), vbscript-html (broken by body keyword)\n// sql (ada default.txt has a lot of sql keywords)\n\n/** @type LanguageFn */\nfunction ada(hljs) {\n  // Regular expression for Ada numeric literals.\n  // stolen form the VHDL highlighter\n\n  // Decimal literal:\n  const INTEGER_RE = '\\\\d(_|\\\\d)*';\n  const EXPONENT_RE = '[eE][-+]?' + INTEGER_RE;\n  const DECIMAL_LITERAL_RE = INTEGER_RE + '(\\\\.' + INTEGER_RE + ')?' + '(' + EXPONENT_RE + ')?';\n\n  // Based literal:\n  const BASED_INTEGER_RE = '\\\\w+';\n  const BASED_LITERAL_RE = INTEGER_RE + '#' + BASED_INTEGER_RE + '(\\\\.' + BASED_INTEGER_RE + ')?' + '#' + '(' + EXPONENT_RE + ')?';\n\n  const NUMBER_RE = '\\\\b(' + BASED_LITERAL_RE + '|' + DECIMAL_LITERAL_RE + ')';\n\n  // Identifier regex\n  const ID_REGEX = '[A-Za-z](_?[A-Za-z0-9.])*';\n\n  // bad chars, only allowed in literals\n  const BAD_CHARS = `[]\\\\{\\\\}%#'\"`;\n\n  // Ada doesn't have block comments, only line comments\n  const COMMENTS = hljs.COMMENT('--', '$');\n\n  // variable declarations of the form\n  // Foo : Bar := Baz;\n  // where only Bar will be highlighted\n  const VAR_DECLS = {\n    // TODO: These spaces are not required by the Ada syntax\n    // however, I have yet to see handwritten Ada code where\n    // someone does not put spaces around :\n    begin: '\\\\s+:\\\\s+',\n    end: '\\\\s*(:=|;|\\\\)|=>|$)',\n    // endsWithParent: true,\n    // returnBegin: true,\n    illegal: BAD_CHARS,\n    contains: [\n      {\n        // workaround to avoid highlighting\n        // named loops and declare blocks\n        beginKeywords: 'loop for declare others',\n        endsParent: true\n      },\n      {\n        // properly highlight all modifiers\n        className: 'keyword',\n        beginKeywords: 'not null constant access function procedure in out aliased exception'\n      },\n      {\n        className: 'type',\n        begin: ID_REGEX,\n        endsParent: true,\n        relevance: 0\n      }\n    ]\n  };\n\n  return {\n    name: 'Ada',\n    case_insensitive: true,\n    keywords: {\n      keyword:\n                'abort else new return abs elsif not reverse abstract end ' +\n                'accept entry select access exception of separate aliased exit or some ' +\n                'all others subtype and for out synchronized array function overriding ' +\n                'at tagged generic package task begin goto pragma terminate ' +\n                'body private then if procedure type case in protected constant interface ' +\n                'is raise use declare range delay limited record when delta loop rem while ' +\n                'digits renames with do mod requeue xor',\n      literal:\n                'True False'\n    },\n    contains: [\n      COMMENTS,\n      // strings \"foobar\"\n      {\n        className: 'string',\n        begin: /\"/,\n        end: /\"/,\n        contains: [{\n          begin: /\"\"/,\n          relevance: 0\n        }]\n      },\n      // characters ''\n      {\n        // character literals always contain one char\n        className: 'string',\n        begin: /'.'/\n      },\n      {\n        // number literals\n        className: 'number',\n        begin: NUMBER_RE,\n        relevance: 0\n      },\n      {\n        // Attributes\n        className: 'symbol',\n        begin: \"'\" + ID_REGEX\n      },\n      {\n        // package definition, maybe inside generic\n        className: 'title',\n        begin: '(\\\\bwith\\\\s+)?(\\\\bprivate\\\\s+)?\\\\bpackage\\\\s+(\\\\bbody\\\\s+)?',\n        end: '(is|$)',\n        keywords: 'package body',\n        excludeBegin: true,\n        excludeEnd: true,\n        illegal: BAD_CHARS\n      },\n      {\n        // function/procedure declaration/definition\n        // maybe inside generic\n        begin: '(\\\\b(with|overriding)\\\\s+)?\\\\b(function|procedure)\\\\s+',\n        end: '(\\\\bis|\\\\bwith|\\\\brenames|\\\\)\\\\s*;)',\n        keywords: 'overriding function procedure with is renames return',\n        // we need to re-match the 'function' keyword, so that\n        // the title mode below matches only exactly once\n        returnBegin: true,\n        contains:\n                [\n                  COMMENTS,\n                  {\n                    // name of the function/procedure\n                    className: 'title',\n                    begin: '(\\\\bwith\\\\s+)?\\\\b(function|procedure)\\\\s+',\n                    end: '(\\\\(|\\\\s+|$)',\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    illegal: BAD_CHARS\n                  },\n                  // 'self'\n                  // // parameter types\n                  VAR_DECLS,\n                  {\n                    // return type\n                    className: 'type',\n                    begin: '\\\\breturn\\\\s+',\n                    end: '(\\\\s+|;|$)',\n                    keywords: 'return',\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    // we are done with functions\n                    endsParent: true,\n                    illegal: BAD_CHARS\n\n                  }\n                ]\n      },\n      {\n        // new type declarations\n        // maybe inside generic\n        className: 'type',\n        begin: '\\\\b(sub)?type\\\\s+',\n        end: '\\\\s+',\n        keywords: 'type',\n        excludeBegin: true,\n        illegal: BAD_CHARS\n      },\n\n      // see comment above the definition\n      VAR_DECLS\n\n      // no markup\n      // relevance boosters for small snippets\n      // {begin: '\\\\s*=>\\\\s*'},\n      // {begin: '\\\\s*:=\\\\s*'},\n      // {begin: '\\\\s+:=\\\\s+'},\n    ]\n  };\n}\n\nmodule.exports = ada;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB;EACA;;EAEA;EACA,MAAMC,UAAU,GAAG,aAAa;EAChC,MAAMC,WAAW,GAAG,WAAW,GAAGD,UAAU;EAC5C,MAAME,kBAAkB,GAAGF,UAAU,GAAG,MAAM,GAAGA,UAAU,GAAG,IAAI,GAAG,GAAG,GAAGC,WAAW,GAAG,IAAI;;EAE7F;EACA,MAAME,gBAAgB,GAAG,MAAM;EAC/B,MAAMC,gBAAgB,GAAGJ,UAAU,GAAG,GAAG,GAAGG,gBAAgB,GAAG,MAAM,GAAGA,gBAAgB,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAGF,WAAW,GAAG,IAAI;EAEhI,MAAMI,SAAS,GAAG,MAAM,GAAGD,gBAAgB,GAAG,GAAG,GAAGF,kBAAkB,GAAG,GAAG;;EAE5E;EACA,MAAMI,QAAQ,GAAG,2BAA2B;;EAE5C;EACA,MAAMC,SAAS,GAAG,cAAc;;EAEhC;EACA,MAAMC,QAAQ,GAAGT,IAAI,CAACU,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;;EAExC;EACA;EACA;EACA,MAAMC,SAAS,GAAG;IAChB;IACA;IACA;IACAC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,qBAAqB;IAC1B;IACA;IACAC,OAAO,EAAEN,SAAS;IAClBO,QAAQ,EAAE,CACR;MACE;MACA;MACAC,aAAa,EAAE,yBAAyB;MACxCC,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAC,SAAS,EAAE,SAAS;MACpBF,aAAa,EAAE;IACjB,CAAC,EACD;MACEE,SAAS,EAAE,MAAM;MACjBN,KAAK,EAAEL,QAAQ;MACfU,UAAU,EAAE,IAAI;MAChBE,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO,EACG,2DAA2D,GAC3D,wEAAwE,GACxE,wEAAwE,GACxE,6DAA6D,GAC7D,2EAA2E,GAC3E,4EAA4E,GAC5E,wCAAwC;MAClDC,OAAO,EACG;IACZ,CAAC;IACDT,QAAQ,EAAE,CACRN,QAAQ;IACR;IACA;MACES,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRE,QAAQ,EAAE,CAAC;QACTH,KAAK,EAAE,IAAI;QACXO,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IACD;IACA;MACE;MACAD,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAM,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAEN,SAAS;MAChBa,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACAD,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAE,GAAG,GAAGL;IACf,CAAC,EACD;MACE;MACAW,SAAS,EAAE,OAAO;MAClBN,KAAK,EAAE,6DAA6D;MACpEC,GAAG,EAAE,QAAQ;MACbS,QAAQ,EAAE,cAAc;MACxBG,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBZ,OAAO,EAAEN;IACX,CAAC,EACD;MACE;MACA;MACAI,KAAK,EAAE,wDAAwD;MAC/DC,GAAG,EAAE,qCAAqC;MAC1CS,QAAQ,EAAE,sDAAsD;MAChE;MACA;MACAK,WAAW,EAAE,IAAI;MACjBZ,QAAQ,EACA,CACEN,QAAQ,EACR;QACE;QACAS,SAAS,EAAE,OAAO;QAClBN,KAAK,EAAE,2CAA2C;QAClDC,GAAG,EAAE,cAAc;QACnBY,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBZ,OAAO,EAAEN;MACX,CAAC;MACD;MACA;MACAG,SAAS,EACT;QACE;QACAO,SAAS,EAAE,MAAM;QACjBN,KAAK,EAAE,eAAe;QACtBC,GAAG,EAAE,YAAY;QACjBS,QAAQ,EAAE,QAAQ;QAClBG,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChB;QACAT,UAAU,EAAE,IAAI;QAChBH,OAAO,EAAEN;MAEX,CAAC;IAEb,CAAC,EACD;MACE;MACA;MACAU,SAAS,EAAE,MAAM;MACjBN,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE,MAAM;MACXS,QAAQ,EAAE,MAAM;MAChBG,YAAY,EAAE,IAAI;MAClBX,OAAO,EAAEN;IACX,CAAC;IAED;IACAG;;IAEA;IACA;IACA;IACA;IACA;IAAA;EAEJ,CAAC;AACH;AAEAiB,MAAM,CAACC,OAAO,GAAG9B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}