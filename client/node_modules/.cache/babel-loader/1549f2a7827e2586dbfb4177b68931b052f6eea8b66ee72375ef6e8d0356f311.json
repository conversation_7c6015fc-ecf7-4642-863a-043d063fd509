{"ast": null, "code": "'use strict';\n\nmodule.exports = dart;\ndart.displayName = 'dart';\ndart.aliases = [];\nfunction dart(Prism) {\n  ;\n  (function (Prism) {\n    var keywords = [/\\b(?:async|sync|yield)\\*/, /\\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\\b/]; // Handles named imports, such as http.Client\n    var packagePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/.source; // based on the dart naming conventions\n    var className = {\n      pattern: RegExp(packagePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        }\n      }\n    };\n    Prism.languages.dart = Prism.languages.extend('clike', {\n      'class-name': [className, {\n        // variables and parameters\n        // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n        pattern: RegExp(packagePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source),\n        lookbehind: true,\n        inside: className.inside\n      }],\n      keyword: keywords,\n      operator: /\\bis!|\\b(?:as|is)\\b|\\+\\+|--|&&|\\|\\||<<=?|>>=?|~(?:\\/=?)?|[+\\-*\\/%&^|=!<>]=?|\\?/\n    });\n    Prism.languages.insertBefore('dart', 'string', {\n      'string-literal': {\n        pattern: /r?(?:(\"\"\"|''')[\\s\\S]*?\\1|([\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2(?!\\2))/,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\w+|\\{(?:[^{}]|\\{[^{}]*\\})*\\})/,\n            lookbehind: true,\n            inside: {\n              punctuation: /^\\$\\{?|\\}$/,\n              expression: {\n                pattern: /[\\s\\S]+/,\n                inside: Prism.languages.dart\n              }\n            }\n          },\n          string: /[\\s\\S]+/\n        }\n      },\n      string: undefined\n    });\n    Prism.languages.insertBefore('dart', 'class-name', {\n      metadata: {\n        pattern: /@\\w+/,\n        alias: 'function'\n      }\n    });\n    Prism.languages.insertBefore('dart', 'class-name', {\n      generics: {\n        pattern: /<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<[\\w\\s,.&?]*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "dart", "displayName", "aliases", "Prism", "keywords", "packagePrefix", "source", "className", "pattern", "RegExp", "lookbehind", "inside", "namespace", "punctuation", "languages", "extend", "keyword", "operator", "insertBefore", "greedy", "interpolation", "expression", "string", "undefined", "metadata", "alias", "generics"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/dart.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dart\ndart.displayName = 'dart'\ndart.aliases = []\nfunction dart(Prism) {\n  ;(function (Prism) {\n    var keywords = [\n      /\\b(?:async|sync|yield)\\*/,\n      /\\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\\b/\n    ] // Handles named imports, such as http.Client\n    var packagePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/\n      .source // based on the dart naming conventions\n    var className = {\n      pattern: RegExp(packagePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        }\n      }\n    }\n    Prism.languages.dart = Prism.languages.extend('clike', {\n      'class-name': [\n        className,\n        {\n          // variables and parameters\n          // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n          pattern: RegExp(\n            packagePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source\n          ),\n          lookbehind: true,\n          inside: className.inside\n        }\n      ],\n      keyword: keywords,\n      operator:\n        /\\bis!|\\b(?:as|is)\\b|\\+\\+|--|&&|\\|\\||<<=?|>>=?|~(?:\\/=?)?|[+\\-*\\/%&^|=!<>]=?|\\?/\n    })\n    Prism.languages.insertBefore('dart', 'string', {\n      'string-literal': {\n        pattern:\n          /r?(?:(\"\"\"|''')[\\s\\S]*?\\1|([\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2(?!\\2))/,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\w+|\\{(?:[^{}]|\\{[^{}]*\\})*\\})/,\n            lookbehind: true,\n            inside: {\n              punctuation: /^\\$\\{?|\\}$/,\n              expression: {\n                pattern: /[\\s\\S]+/,\n                inside: Prism.languages.dart\n              }\n            }\n          },\n          string: /[\\s\\S]+/\n        }\n      },\n      string: undefined\n    })\n    Prism.languages.insertBefore('dart', 'class-name', {\n      metadata: {\n        pattern: /@\\w+/,\n        alias: 'function'\n      }\n    })\n    Prism.languages.insertBefore('dart', 'class-name', {\n      generics: {\n        pattern:\n          /<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<[\\w\\s,.&?]*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GAAG,CACb,0BAA0B,EAC1B,4WAA4W,CAC7W,EAAC;IACF,IAAIC,aAAa,GAAG,sDAAsD,CACvEC,MAAM,EAAC;IACV,IAAIC,SAAS,GAAG;MACdC,OAAO,EAAEC,MAAM,CAACJ,aAAa,GAAG,+BAA+B,CAACC,MAAM,CAAC;MACvEI,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,SAAS,EAAE;UACTJ,OAAO,EAAE,0CAA0C;UACnDG,MAAM,EAAE;YACNE,WAAW,EAAE;UACf;QACF;MACF;IACF,CAAC;IACDV,KAAK,CAACW,SAAS,CAACd,IAAI,GAAGG,KAAK,CAACW,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;MACrD,YAAY,EAAE,CACZR,SAAS,EACT;QACE;QACA;QACAC,OAAO,EAAEC,MAAM,CACbJ,aAAa,GAAG,8BAA8B,CAACC,MACjD,CAAC;QACDI,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEJ,SAAS,CAACI;MACpB,CAAC,CACF;MACDK,OAAO,EAAEZ,QAAQ;MACjBa,QAAQ,EACN;IACJ,CAAC,CAAC;IACFd,KAAK,CAACW,SAAS,CAACI,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE;MAC7C,gBAAgB,EAAE;QAChBV,OAAO,EACL,kEAAkE;QACpEW,MAAM,EAAE,IAAI;QACZR,MAAM,EAAE;UACNS,aAAa,EAAE;YACbZ,OAAO,EACL,4DAA4D;YAC9DE,UAAU,EAAE,IAAI;YAChBC,MAAM,EAAE;cACNE,WAAW,EAAE,YAAY;cACzBQ,UAAU,EAAE;gBACVb,OAAO,EAAE,SAAS;gBAClBG,MAAM,EAAER,KAAK,CAACW,SAAS,CAACd;cAC1B;YACF;UACF,CAAC;UACDsB,MAAM,EAAE;QACV;MACF,CAAC;MACDA,MAAM,EAAEC;IACV,CAAC,CAAC;IACFpB,KAAK,CAACW,SAAS,CAACI,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE;MACjDM,QAAQ,EAAE;QACRhB,OAAO,EAAE,MAAM;QACfiB,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACFtB,KAAK,CAACW,SAAS,CAACI,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE;MACjDQ,QAAQ,EAAE;QACRlB,OAAO,EACL,qEAAqE;QACvEG,MAAM,EAAE;UACN,YAAY,EAAEJ,SAAS;UACvBS,OAAO,EAAEZ,QAAQ;UACjBS,WAAW,EAAE,WAAW;UACxBI,QAAQ,EAAE;QACZ;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAEd,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}