{"ast": null, "code": "'use strict';\n\nmodule.exports = clojure;\nclojure.displayName = 'clojure';\nclojure.aliases = [];\nfunction clojure(Prism) {\n  // Copied from https://github.com/jeluard/prism-clojure\n  Prism.languages.clojure = {\n    comment: {\n      pattern: /;.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n      greedy: true\n    },\n    char: /\\\\\\w+/,\n    symbol: {\n      pattern: /(^|[\\s()\\[\\]{},])::?[\\w*+!?'<>=/.-]+/,\n      lookbehind: true\n    },\n    keyword: {\n      pattern: /(\\()(?:-|->|->>|\\.|\\.\\.|\\*|\\/|\\+|<|<=|=|==|>|>=|accessor|agent|agent-errors|aget|alength|all-ns|alter|and|append-child|apply|array-map|aset|aset-boolean|aset-byte|aset-char|aset-double|aset-float|aset-int|aset-long|aset-short|assert|assoc|await|await-for|bean|binding|bit-and|bit-not|bit-or|bit-shift-left|bit-shift-right|bit-xor|boolean|branch\\?|butlast|byte|cast|char|children|class|clear-agent-errors|comment|commute|comp|comparator|complement|concat|cond|conj|cons|constantly|construct-proxy|contains\\?|count|create-ns|create-struct|cycle|dec|declare|def|def-|definline|definterface|defmacro|defmethod|defmulti|defn|defn-|defonce|defproject|defprotocol|defrecord|defstruct|deftype|deref|difference|disj|dissoc|distinct|do|doall|doc|dorun|doseq|dosync|dotimes|doto|double|down|drop|drop-while|edit|end\\?|ensure|eval|every\\?|false\\?|ffirst|file-seq|filter|find|find-doc|find-ns|find-var|first|float|flush|fn|fnseq|for|frest|gensym|get|get-proxy-class|hash-map|hash-set|identical\\?|identity|if|if-let|if-not|import|in-ns|inc|index|insert-child|insert-left|insert-right|inspect-table|inspect-tree|instance\\?|int|interleave|intersection|into|into-array|iterate|join|key|keys|keyword|keyword\\?|last|lazy-cat|lazy-cons|left|lefts|let|line-seq|list|list\\*|load|load-file|locking|long|loop|macroexpand|macroexpand-1|make-array|make-node|map|map-invert|map\\?|mapcat|max|max-key|memfn|merge|merge-with|meta|min|min-key|monitor-enter|name|namespace|neg\\?|new|newline|next|nil\\?|node|not|not-any\\?|not-every\\?|not=|ns|ns-imports|ns-interns|ns-map|ns-name|ns-publics|ns-refers|ns-resolve|ns-unmap|nth|nthrest|or|parse|partial|path|peek|pop|pos\\?|pr|pr-str|print|print-str|println|println-str|prn|prn-str|project|proxy|proxy-mappings|quot|quote|rand|rand-int|range|re-find|re-groups|re-matcher|re-matches|re-pattern|re-seq|read|read-line|recur|reduce|ref|ref-set|refer|rem|remove|remove-method|remove-ns|rename|rename-keys|repeat|replace|replicate|resolve|rest|resultset-seq|reverse|rfirst|right|rights|root|rrest|rseq|second|select|select-keys|send|send-off|seq|seq-zip|seq\\?|set|set!|short|slurp|some|sort|sort-by|sorted-map|sorted-map-by|sorted-set|special-symbol\\?|split-at|split-with|str|string\\?|struct|struct-map|subs|subvec|symbol|symbol\\?|sync|take|take-nth|take-while|test|throw|time|to-array|to-array-2d|tree-seq|true\\?|try|union|up|update-proxy|val|vals|var|var-get|var-set|var\\?|vector|vector-zip|vector\\?|when|when-first|when-let|when-not|with-local-vars|with-meta|with-open|with-out-str|xml-seq|xml-zip|zero\\?|zipmap|zipper)(?=[\\s)]|$)/,\n      lookbehind: true\n    },\n    boolean: /\\b(?:false|nil|true)\\b/,\n    number: {\n      pattern: /(^|[^\\w$@])(?:\\d+(?:[/.]\\d+)?(?:e[+-]?\\d+)?|0x[a-f0-9]+|[1-9]\\d?r[a-z0-9]+)[lmn]?(?![\\w$@])/i,\n      lookbehind: true\n    },\n    function: {\n      pattern: /((?:^|[^'])\\()[\\w*+!?'<>=/.-]+(?=[\\s)]|$)/,\n      lookbehind: true\n    },\n    operator: /[#@^`~]/,\n    punctuation: /[{}\\[\\](),]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "clojure", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "char", "symbol", "lookbehind", "keyword", "boolean", "number", "function", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/clojure.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = clojure\nclojure.displayName = 'clojure'\nclojure.aliases = []\nfunction clojure(Prism) {\n  // Copied from https://github.com/jeluard/prism-clojure\n  Prism.languages.clojure = {\n    comment: {\n      pattern: /;.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n      greedy: true\n    },\n    char: /\\\\\\w+/,\n    symbol: {\n      pattern: /(^|[\\s()\\[\\]{},])::?[\\w*+!?'<>=/.-]+/,\n      lookbehind: true\n    },\n    keyword: {\n      pattern:\n        /(\\()(?:-|->|->>|\\.|\\.\\.|\\*|\\/|\\+|<|<=|=|==|>|>=|accessor|agent|agent-errors|aget|alength|all-ns|alter|and|append-child|apply|array-map|aset|aset-boolean|aset-byte|aset-char|aset-double|aset-float|aset-int|aset-long|aset-short|assert|assoc|await|await-for|bean|binding|bit-and|bit-not|bit-or|bit-shift-left|bit-shift-right|bit-xor|boolean|branch\\?|butlast|byte|cast|char|children|class|clear-agent-errors|comment|commute|comp|comparator|complement|concat|cond|conj|cons|constantly|construct-proxy|contains\\?|count|create-ns|create-struct|cycle|dec|declare|def|def-|definline|definterface|defmacro|defmethod|defmulti|defn|defn-|defonce|defproject|defprotocol|defrecord|defstruct|deftype|deref|difference|disj|dissoc|distinct|do|doall|doc|dorun|doseq|dosync|dotimes|doto|double|down|drop|drop-while|edit|end\\?|ensure|eval|every\\?|false\\?|ffirst|file-seq|filter|find|find-doc|find-ns|find-var|first|float|flush|fn|fnseq|for|frest|gensym|get|get-proxy-class|hash-map|hash-set|identical\\?|identity|if|if-let|if-not|import|in-ns|inc|index|insert-child|insert-left|insert-right|inspect-table|inspect-tree|instance\\?|int|interleave|intersection|into|into-array|iterate|join|key|keys|keyword|keyword\\?|last|lazy-cat|lazy-cons|left|lefts|let|line-seq|list|list\\*|load|load-file|locking|long|loop|macroexpand|macroexpand-1|make-array|make-node|map|map-invert|map\\?|mapcat|max|max-key|memfn|merge|merge-with|meta|min|min-key|monitor-enter|name|namespace|neg\\?|new|newline|next|nil\\?|node|not|not-any\\?|not-every\\?|not=|ns|ns-imports|ns-interns|ns-map|ns-name|ns-publics|ns-refers|ns-resolve|ns-unmap|nth|nthrest|or|parse|partial|path|peek|pop|pos\\?|pr|pr-str|print|print-str|println|println-str|prn|prn-str|project|proxy|proxy-mappings|quot|quote|rand|rand-int|range|re-find|re-groups|re-matcher|re-matches|re-pattern|re-seq|read|read-line|recur|reduce|ref|ref-set|refer|rem|remove|remove-method|remove-ns|rename|rename-keys|repeat|replace|replicate|resolve|rest|resultset-seq|reverse|rfirst|right|rights|root|rrest|rseq|second|select|select-keys|send|send-off|seq|seq-zip|seq\\?|set|set!|short|slurp|some|sort|sort-by|sorted-map|sorted-map-by|sorted-set|special-symbol\\?|split-at|split-with|str|string\\?|struct|struct-map|subs|subvec|symbol|symbol\\?|sync|take|take-nth|take-while|test|throw|time|to-array|to-array-2d|tree-seq|true\\?|try|union|up|update-proxy|val|vals|var|var-get|var-set|var\\?|vector|vector-zip|vector\\?|when|when-first|when-let|when-not|with-local-vars|with-meta|with-open|with-out-str|xml-seq|xml-zip|zero\\?|zipmap|zipper)(?=[\\s)]|$)/,\n      lookbehind: true\n    },\n    boolean: /\\b(?:false|nil|true)\\b/,\n    number: {\n      pattern:\n        /(^|[^\\w$@])(?:\\d+(?:[/.]\\d+)?(?:e[+-]?\\d+)?|0x[a-f0-9]+|[1-9]\\d?r[a-z0-9]+)[lmn]?(?![\\w$@])/i,\n      lookbehind: true\n    },\n    function: {\n      pattern: /((?:^|[^'])\\()[\\w*+!?'<>=/.-]+(?=[\\s)]|$)/,\n      lookbehind: true\n    },\n    operator: /[#@^`~]/,\n    punctuation: /[{}\\[\\](),]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtB;EACAA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxBK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,mBAAmB;MAC5BC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;MACNJ,OAAO,EAAE,sCAAsC;MAC/CK,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EAAE;MACPN,OAAO,EACL,6+EAA6+E;MAC/+EK,UAAU,EAAE;IACd,CAAC;IACDE,OAAO,EAAE,wBAAwB;IACjCC,MAAM,EAAE;MACNR,OAAO,EACL,8FAA8F;MAChGK,UAAU,EAAE;IACd,CAAC;IACDI,QAAQ,EAAE;MACRT,OAAO,EAAE,2CAA2C;MACpDK,UAAU,EAAE;IACd,CAAC;IACDK,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}