{"ast": null, "code": "'use strict';\n\nvar refractorRuby = require('./ruby.js');\nmodule.exports = haml;\nhaml.displayName = 'haml';\nhaml.aliases = [];\nfunction haml(Prism) {\n  Prism.register(refractorRuby)\n  /* TODO\n  Handle multiline code after tag\n  %foo= some |\n  multiline |\n  code |\n  */;\n  (function (Prism) {\n    Prism.languages.haml = {\n      // Multiline stuff should appear before the rest\n      'multiline-comment': {\n        pattern: /((?:^|\\r?\\n|\\r)([\\t ]*))(?:\\/|-#).*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)*/,\n        lookbehind: true,\n        alias: 'comment'\n      },\n      'multiline-code': [{\n        pattern: /((?:^|\\r?\\n|\\r)([\\t ]*)(?:[~-]|[&!]?=)).*,[\\t ]*(?:(?:\\r?\\n|\\r)\\2[\\t ].*,[\\t ]*)*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)/,\n        lookbehind: true,\n        inside: Prism.languages.ruby\n      }, {\n        pattern: /((?:^|\\r?\\n|\\r)([\\t ]*)(?:[~-]|[&!]?=)).*\\|[\\t ]*(?:(?:\\r?\\n|\\r)\\2[\\t ].*\\|[\\t ]*)*/,\n        lookbehind: true,\n        inside: Prism.languages.ruby\n      }],\n      // See at the end of the file for known filters\n      filter: {\n        pattern: /((?:^|\\r?\\n|\\r)([\\t ]*)):[\\w-]+(?:(?:\\r?\\n|\\r)(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/,\n        lookbehind: true,\n        inside: {\n          'filter-name': {\n            pattern: /^:[\\w-]+/,\n            alias: 'symbol'\n          }\n        }\n      },\n      markup: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)<.+/,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      },\n      doctype: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)!!!(?: .+)?/,\n        lookbehind: true\n      },\n      tag: {\n        // Allows for one nested group of braces\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)[%.#][\\w\\-#.]*[\\w\\-](?:\\([^)]+\\)|\\{(?:\\{[^}]+\\}|[^{}])+\\}|\\[[^\\]]+\\])*[\\/<>]*/,\n        lookbehind: true,\n        inside: {\n          attributes: [{\n            // Lookbehind tries to prevent interpolations from breaking it all\n            // Allows for one nested group of braces\n            pattern: /(^|[^#])\\{(?:\\{[^}]+\\}|[^{}])+\\}/,\n            lookbehind: true,\n            inside: Prism.languages.ruby\n          }, {\n            pattern: /\\([^)]+\\)/,\n            inside: {\n              'attr-value': {\n                pattern: /(=\\s*)(?:\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|[^)\\s]+)/,\n                lookbehind: true\n              },\n              'attr-name': /[\\w:-]+(?=\\s*!?=|\\s*[,)])/,\n              punctuation: /[=(),]/\n            }\n          }, {\n            pattern: /\\[[^\\]]+\\]/,\n            inside: Prism.languages.ruby\n          }],\n          punctuation: /[<>]/\n        }\n      },\n      code: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*(?:[~-]|[&!]?=)).+/,\n        lookbehind: true,\n        inside: Prism.languages.ruby\n      },\n      // Interpolations in plain text\n      interpolation: {\n        pattern: /#\\{[^}]+\\}/,\n        inside: {\n          delimiter: {\n            pattern: /^#\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          ruby: {\n            pattern: /[\\s\\S]+/,\n            inside: Prism.languages.ruby\n          }\n        }\n      },\n      punctuation: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)[~=\\-&!]+/,\n        lookbehind: true\n      }\n    };\n    var filter_pattern = '((?:^|\\\\r?\\\\n|\\\\r)([\\\\t ]*)):{{filter_name}}(?:(?:\\\\r?\\\\n|\\\\r)(?:\\\\2[\\\\t ].+|\\\\s*?(?=\\\\r?\\\\n|\\\\r)))+'; // Non exhaustive list of available filters and associated languages\n    var filters = ['css', {\n      filter: 'coffee',\n      language: 'coffeescript'\n    }, 'erb', 'javascript', 'less', 'markdown', 'ruby', 'scss', 'textile'];\n    var all_filters = {};\n    for (var i = 0, l = filters.length; i < l; i++) {\n      var filter = filters[i];\n      filter = typeof filter === 'string' ? {\n        filter: filter,\n        language: filter\n      } : filter;\n      if (Prism.languages[filter.language]) {\n        all_filters['filter-' + filter.filter] = {\n          pattern: RegExp(filter_pattern.replace('{{filter_name}}', function () {\n            return filter.filter;\n          })),\n          lookbehind: true,\n          inside: {\n            'filter-name': {\n              pattern: /^:[\\w-]+/,\n              alias: 'symbol'\n            },\n            text: {\n              pattern: /[\\s\\S]+/,\n              alias: [filter.language, 'language-' + filter.language],\n              inside: Prism.languages[filter.language]\n            }\n          }\n        };\n      }\n    }\n    Prism.languages.insertBefore('haml', 'filter', all_filters);\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractor<PERSON>uby", "require", "module", "exports", "haml", "displayName", "aliases", "Prism", "register", "languages", "pattern", "lookbehind", "alias", "inside", "ruby", "filter", "markup", "doctype", "tag", "attributes", "punctuation", "code", "interpolation", "delimiter", "filter_pattern", "filters", "language", "all_filters", "i", "l", "length", "RegExp", "replace", "text", "insertBefore"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/haml.js"], "sourcesContent": ["'use strict'\nvar refractorRuby = require('./ruby.js')\nmodule.exports = haml\nhaml.displayName = 'haml'\nhaml.aliases = []\nfunction haml(Prism) {\n  Prism.register(refractorRuby)\n  /* TODO\nHandle multiline code after tag\n%foo= some |\nmultiline |\ncode |\n*/\n  ;(function (Prism) {\n    Prism.languages.haml = {\n      // Multiline stuff should appear before the rest\n      'multiline-comment': {\n        pattern:\n          /((?:^|\\r?\\n|\\r)([\\t ]*))(?:\\/|-#).*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)*/,\n        lookbehind: true,\n        alias: 'comment'\n      },\n      'multiline-code': [\n        {\n          pattern:\n            /((?:^|\\r?\\n|\\r)([\\t ]*)(?:[~-]|[&!]?=)).*,[\\t ]*(?:(?:\\r?\\n|\\r)\\2[\\t ].*,[\\t ]*)*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)/,\n          lookbehind: true,\n          inside: Prism.languages.ruby\n        },\n        {\n          pattern:\n            /((?:^|\\r?\\n|\\r)([\\t ]*)(?:[~-]|[&!]?=)).*\\|[\\t ]*(?:(?:\\r?\\n|\\r)\\2[\\t ].*\\|[\\t ]*)*/,\n          lookbehind: true,\n          inside: Prism.languages.ruby\n        }\n      ],\n      // See at the end of the file for known filters\n      filter: {\n        pattern:\n          /((?:^|\\r?\\n|\\r)([\\t ]*)):[\\w-]+(?:(?:\\r?\\n|\\r)(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/,\n        lookbehind: true,\n        inside: {\n          'filter-name': {\n            pattern: /^:[\\w-]+/,\n            alias: 'symbol'\n          }\n        }\n      },\n      markup: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)<.+/,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      },\n      doctype: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)!!!(?: .+)?/,\n        lookbehind: true\n      },\n      tag: {\n        // Allows for one nested group of braces\n        pattern:\n          /((?:^|\\r?\\n|\\r)[\\t ]*)[%.#][\\w\\-#.]*[\\w\\-](?:\\([^)]+\\)|\\{(?:\\{[^}]+\\}|[^{}])+\\}|\\[[^\\]]+\\])*[\\/<>]*/,\n        lookbehind: true,\n        inside: {\n          attributes: [\n            {\n              // Lookbehind tries to prevent interpolations from breaking it all\n              // Allows for one nested group of braces\n              pattern: /(^|[^#])\\{(?:\\{[^}]+\\}|[^{}])+\\}/,\n              lookbehind: true,\n              inside: Prism.languages.ruby\n            },\n            {\n              pattern: /\\([^)]+\\)/,\n              inside: {\n                'attr-value': {\n                  pattern: /(=\\s*)(?:\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|[^)\\s]+)/,\n                  lookbehind: true\n                },\n                'attr-name': /[\\w:-]+(?=\\s*!?=|\\s*[,)])/,\n                punctuation: /[=(),]/\n              }\n            },\n            {\n              pattern: /\\[[^\\]]+\\]/,\n              inside: Prism.languages.ruby\n            }\n          ],\n          punctuation: /[<>]/\n        }\n      },\n      code: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*(?:[~-]|[&!]?=)).+/,\n        lookbehind: true,\n        inside: Prism.languages.ruby\n      },\n      // Interpolations in plain text\n      interpolation: {\n        pattern: /#\\{[^}]+\\}/,\n        inside: {\n          delimiter: {\n            pattern: /^#\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          ruby: {\n            pattern: /[\\s\\S]+/,\n            inside: Prism.languages.ruby\n          }\n        }\n      },\n      punctuation: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)[~=\\-&!]+/,\n        lookbehind: true\n      }\n    }\n    var filter_pattern =\n      '((?:^|\\\\r?\\\\n|\\\\r)([\\\\t ]*)):{{filter_name}}(?:(?:\\\\r?\\\\n|\\\\r)(?:\\\\2[\\\\t ].+|\\\\s*?(?=\\\\r?\\\\n|\\\\r)))+' // Non exhaustive list of available filters and associated languages\n    var filters = [\n      'css',\n      {\n        filter: 'coffee',\n        language: 'coffeescript'\n      },\n      'erb',\n      'javascript',\n      'less',\n      'markdown',\n      'ruby',\n      'scss',\n      'textile'\n    ]\n    var all_filters = {}\n    for (var i = 0, l = filters.length; i < l; i++) {\n      var filter = filters[i]\n      filter =\n        typeof filter === 'string'\n          ? {\n              filter: filter,\n              language: filter\n            }\n          : filter\n      if (Prism.languages[filter.language]) {\n        all_filters['filter-' + filter.filter] = {\n          pattern: RegExp(\n            filter_pattern.replace('{{filter_name}}', function () {\n              return filter.filter\n            })\n          ),\n          lookbehind: true,\n          inside: {\n            'filter-name': {\n              pattern: /^:[\\w-]+/,\n              alias: 'symbol'\n            },\n            text: {\n              pattern: /[\\s\\S]+/,\n              alias: [filter.language, 'language-' + filter.language],\n              inside: Prism.languages[filter.language]\n            }\n          }\n        }\n      }\n    }\n    Prism.languages.insertBefore('haml', 'filter', all_filters)\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxCC,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,QAAQ,CAACR,aAAa;EAC5B;AACF;AACA;AACA;AACA;AACA,IALE;EAMC,CAAC,UAAUO,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,IAAI,GAAG;MACrB;MACA,mBAAmB,EAAE;QACnBM,OAAO,EACL,+DAA+D;QACjEC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACD,gBAAgB,EAAE,CAChB;QACEF,OAAO,EACL,4GAA4G;QAC9GC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;MAC1B,CAAC,EACD;QACEJ,OAAO,EACL,qFAAqF;QACvFC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;MAC1B,CAAC,CACF;MACD;MACAC,MAAM,EAAE;QACNL,OAAO,EACL,gFAAgF;QAClFC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACN,aAAa,EAAE;YACbH,OAAO,EAAE,UAAU;YACnBE,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDI,MAAM,EAAE;QACNN,OAAO,EAAE,2BAA2B;QACpCC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACO;MAC1B,CAAC;MACDC,OAAO,EAAE;QACPP,OAAO,EAAE,mCAAmC;QAC5CC,UAAU,EAAE;MACd,CAAC;MACDO,GAAG,EAAE;QACH;QACAR,OAAO,EACL,qGAAqG;QACvGC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAE;UACNM,UAAU,EAAE,CACV;YACE;YACA;YACAT,OAAO,EAAE,kCAAkC;YAC3CC,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;UAC1B,CAAC,EACD;YACEJ,OAAO,EAAE,WAAW;YACpBG,MAAM,EAAE;cACN,YAAY,EAAE;gBACZH,OAAO,EAAE,yCAAyC;gBAClDC,UAAU,EAAE;cACd,CAAC;cACD,WAAW,EAAE,2BAA2B;cACxCS,WAAW,EAAE;YACf;UACF,CAAC,EACD;YACEV,OAAO,EAAE,YAAY;YACrBG,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;UAC1B,CAAC,CACF;UACDM,WAAW,EAAE;QACf;MACF,CAAC;MACDC,IAAI,EAAE;QACJX,OAAO,EAAE,yCAAyC;QAClDC,UAAU,EAAE,IAAI;QAChBE,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;MAC1B,CAAC;MACD;MACAQ,aAAa,EAAE;QACbZ,OAAO,EAAE,YAAY;QACrBG,MAAM,EAAE;UACNU,SAAS,EAAE;YACTb,OAAO,EAAE,UAAU;YACnBE,KAAK,EAAE;UACT,CAAC;UACDE,IAAI,EAAE;YACJJ,OAAO,EAAE,SAAS;YAClBG,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;UAC1B;QACF;MACF,CAAC;MACDM,WAAW,EAAE;QACXV,OAAO,EAAE,iCAAiC;QAC1CC,UAAU,EAAE;MACd;IACF,CAAC;IACD,IAAIa,cAAc,GAChB,sGAAsG,EAAC;IACzG,IAAIC,OAAO,GAAG,CACZ,KAAK,EACL;MACEV,MAAM,EAAE,QAAQ;MAChBW,QAAQ,EAAE;IACZ,CAAC,EACD,KAAK,EACL,YAAY,EACZ,MAAM,EACN,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,CACV;IACD,IAAIC,WAAW,GAAG,CAAC,CAAC;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,OAAO,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAIb,MAAM,GAAGU,OAAO,CAACG,CAAC,CAAC;MACvBb,MAAM,GACJ,OAAOA,MAAM,KAAK,QAAQ,GACtB;QACEA,MAAM,EAAEA,MAAM;QACdW,QAAQ,EAAEX;MACZ,CAAC,GACDA,MAAM;MACZ,IAAIR,KAAK,CAACE,SAAS,CAACM,MAAM,CAACW,QAAQ,CAAC,EAAE;QACpCC,WAAW,CAAC,SAAS,GAAGZ,MAAM,CAACA,MAAM,CAAC,GAAG;UACvCL,OAAO,EAAEqB,MAAM,CACbP,cAAc,CAACQ,OAAO,CAAC,iBAAiB,EAAE,YAAY;YACpD,OAAOjB,MAAM,CAACA,MAAM;UACtB,CAAC,CACH,CAAC;UACDJ,UAAU,EAAE,IAAI;UAChBE,MAAM,EAAE;YACN,aAAa,EAAE;cACbH,OAAO,EAAE,UAAU;cACnBE,KAAK,EAAE;YACT,CAAC;YACDqB,IAAI,EAAE;cACJvB,OAAO,EAAE,SAAS;cAClBE,KAAK,EAAE,CAACG,MAAM,CAACW,QAAQ,EAAE,WAAW,GAAGX,MAAM,CAACW,QAAQ,CAAC;cACvDb,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACM,MAAM,CAACW,QAAQ;YACzC;UACF;QACF,CAAC;MACH;IACF;IACAnB,KAAK,CAACE,SAAS,CAACyB,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAEP,WAAW,CAAC;EAC7D,CAAC,EAAEpB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}