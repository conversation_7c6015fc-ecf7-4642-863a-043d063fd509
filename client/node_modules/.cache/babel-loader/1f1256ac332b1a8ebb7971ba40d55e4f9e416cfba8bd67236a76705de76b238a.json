{"ast": null, "code": "/*\nLanguage: D\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: D is a language with C-like syntax and static typing. It pragmatically combines efficiency, control, and modeling power, with safety and programmer productivity.\nVersion: 1.0a\nWebsite: https://dlang.org\nDate: 2012-04-08\n*/\n\n/**\n * Known issues:\n *\n * - invalid hex string literals will be recognized as a double quoted strings\n *   but 'x' at the beginning of string will not be matched\n *\n * - delimited string literals are not checked for matching end delimiter\n *   (not possible to do with js regexp)\n *\n * - content of token string is colored as a string (i.e. no keyword coloring inside a token string)\n *   also, content of token string is not validated to contain only valid D tokens\n *\n * - special token sequence rule is not strictly following D grammar (anything following #line\n *   up to the end of line is matched as special token sequence)\n */\n\n/** @type LanguageFn */\nfunction d(hljs) {\n  /**\n   * Language keywords\n   *\n   * @type {Object}\n   */\n  const D_KEYWORDS = {\n    $pattern: hljs.UNDERSCORE_IDENT_RE,\n    keyword: 'abstract alias align asm assert auto body break byte case cast catch class ' + 'const continue debug default delete deprecated do else enum export extern final ' + 'finally for foreach foreach_reverse|10 goto if immutable import in inout int ' + 'interface invariant is lazy macro mixin module new nothrow out override package ' + 'pragma private protected public pure ref return scope shared static struct ' + 'super switch synchronized template this throw try typedef typeid typeof union ' + 'unittest version void volatile while with __FILE__ __LINE__ __gshared|10 ' + '__thread __traits __DATE__ __EOF__ __TIME__ __TIMESTAMP__ __VENDOR__ __VERSION__',\n    built_in: 'bool cdouble cent cfloat char creal dchar delegate double dstring float function ' + 'idouble ifloat ireal long real short string ubyte ucent uint ulong ushort wchar ' + 'wstring',\n    literal: 'false null true'\n  };\n\n  /**\n   * Number literal regexps\n   *\n   * @type {String}\n   */\n  const decimal_integer_re = '(0|[1-9][\\\\d_]*)';\n  const decimal_integer_nosus_re = '(0|[1-9][\\\\d_]*|\\\\d[\\\\d_]*|[\\\\d_]+?\\\\d)';\n  const binary_integer_re = '0[bB][01_]+';\n  const hexadecimal_digits_re = '([\\\\da-fA-F][\\\\da-fA-F_]*|_[\\\\da-fA-F][\\\\da-fA-F_]*)';\n  const hexadecimal_integer_re = '0[xX]' + hexadecimal_digits_re;\n  const decimal_exponent_re = '([eE][+-]?' + decimal_integer_nosus_re + ')';\n  const decimal_float_re = '(' + decimal_integer_nosus_re + '(\\\\.\\\\d*|' + decimal_exponent_re + ')|' + '\\\\d+\\\\.' + decimal_integer_nosus_re + '|' + '\\\\.' + decimal_integer_re + decimal_exponent_re + '?' + ')';\n  const hexadecimal_float_re = '(0[xX](' + hexadecimal_digits_re + '\\\\.' + hexadecimal_digits_re + '|' + '\\\\.?' + hexadecimal_digits_re + ')[pP][+-]?' + decimal_integer_nosus_re + ')';\n  const integer_re = '(' + decimal_integer_re + '|' + binary_integer_re + '|' + hexadecimal_integer_re + ')';\n  const float_re = '(' + hexadecimal_float_re + '|' + decimal_float_re + ')';\n\n  /**\n   * Escape sequence supported in D string and character literals\n   *\n   * @type {String}\n   */\n  const escape_sequence_re = '\\\\\\\\(' + '[\\'\"\\\\?\\\\\\\\abfnrtv]|' +\n  // common escapes\n  'u[\\\\dA-Fa-f]{4}|' +\n  // four hex digit unicode codepoint\n  '[0-7]{1,3}|' +\n  // one to three octal digit ascii char code\n  'x[\\\\dA-Fa-f]{2}|' +\n  // two hex digit ascii char code\n  'U[\\\\dA-Fa-f]{8}' +\n  // eight hex digit unicode codepoint\n  ')|' + '&[a-zA-Z\\\\d]{2,};'; // named character entity\n\n  /**\n   * D integer number literals\n   *\n   * @type {Object}\n   */\n  const D_INTEGER_MODE = {\n    className: 'number',\n    begin: '\\\\b' + integer_re + '(L|u|U|Lu|LU|uL|UL)?',\n    relevance: 0\n  };\n\n  /**\n   * [D_FLOAT_MODE description]\n   * @type {Object}\n   */\n  const D_FLOAT_MODE = {\n    className: 'number',\n    begin: '\\\\b(' + float_re + '([fF]|L|i|[fF]i|Li)?|' + integer_re + '(i|[fF]i|Li)' + ')',\n    relevance: 0\n  };\n\n  /**\n   * D character literal\n   *\n   * @type {Object}\n   */\n  const D_CHARACTER_MODE = {\n    className: 'string',\n    begin: '\\'(' + escape_sequence_re + '|.)',\n    end: '\\'',\n    illegal: '.'\n  };\n\n  /**\n   * D string escape sequence\n   *\n   * @type {Object}\n   */\n  const D_ESCAPE_SEQUENCE = {\n    begin: escape_sequence_re,\n    relevance: 0\n  };\n\n  /**\n   * D double quoted string literal\n   *\n   * @type {Object}\n   */\n  const D_STRING_MODE = {\n    className: 'string',\n    begin: '\"',\n    contains: [D_ESCAPE_SEQUENCE],\n    end: '\"[cwd]?'\n  };\n\n  /**\n   * D wysiwyg and delimited string literals\n   *\n   * @type {Object}\n   */\n  const D_WYSIWYG_DELIMITED_STRING_MODE = {\n    className: 'string',\n    begin: '[rq]\"',\n    end: '\"[cwd]?',\n    relevance: 5\n  };\n\n  /**\n   * D alternate wysiwyg string literal\n   *\n   * @type {Object}\n   */\n  const D_ALTERNATE_WYSIWYG_STRING_MODE = {\n    className: 'string',\n    begin: '`',\n    end: '`[cwd]?'\n  };\n\n  /**\n   * D hexadecimal string literal\n   *\n   * @type {Object}\n   */\n  const D_HEX_STRING_MODE = {\n    className: 'string',\n    begin: 'x\"[\\\\da-fA-F\\\\s\\\\n\\\\r]*\"[cwd]?',\n    relevance: 10\n  };\n\n  /**\n   * D delimited string literal\n   *\n   * @type {Object}\n   */\n  const D_TOKEN_STRING_MODE = {\n    className: 'string',\n    begin: 'q\"\\\\{',\n    end: '\\\\}\"'\n  };\n\n  /**\n   * Hashbang support\n   *\n   * @type {Object}\n   */\n  const D_HASHBANG_MODE = {\n    className: 'meta',\n    begin: '^#!',\n    end: '$',\n    relevance: 5\n  };\n\n  /**\n   * D special token sequence\n   *\n   * @type {Object}\n   */\n  const D_SPECIAL_TOKEN_SEQUENCE_MODE = {\n    className: 'meta',\n    begin: '#(line)',\n    end: '$',\n    relevance: 5\n  };\n\n  /**\n   * D attributes\n   *\n   * @type {Object}\n   */\n  const D_ATTRIBUTE_MODE = {\n    className: 'keyword',\n    begin: '@[a-zA-Z_][a-zA-Z_\\\\d]*'\n  };\n\n  /**\n   * D nesting comment\n   *\n   * @type {Object}\n   */\n  const D_NESTING_COMMENT_MODE = hljs.COMMENT('\\\\/\\\\+', '\\\\+\\\\/', {\n    contains: ['self'],\n    relevance: 10\n  });\n  return {\n    name: 'D',\n    keywords: D_KEYWORDS,\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, D_NESTING_COMMENT_MODE, D_HEX_STRING_MODE, D_STRING_MODE, D_WYSIWYG_DELIMITED_STRING_MODE, D_ALTERNATE_WYSIWYG_STRING_MODE, D_TOKEN_STRING_MODE, D_FLOAT_MODE, D_INTEGER_MODE, D_CHARACTER_MODE, D_HASHBANG_MODE, D_SPECIAL_TOKEN_SEQUENCE_MODE, D_ATTRIBUTE_MODE]\n  };\n}\nmodule.exports = d;", "map": {"version": 3, "names": ["d", "hljs", "D_KEYWORDS", "$pattern", "UNDERSCORE_IDENT_RE", "keyword", "built_in", "literal", "decimal_integer_re", "decimal_integer_nosus_re", "binary_integer_re", "hexadecimal_digits_re", "hexadecimal_integer_re", "decimal_exponent_re", "decimal_float_re", "hexadecimal_float_re", "integer_re", "float_re", "escape_sequence_re", "D_INTEGER_MODE", "className", "begin", "relevance", "D_FLOAT_MODE", "D_CHARACTER_MODE", "end", "illegal", "D_ESCAPE_SEQUENCE", "D_STRING_MODE", "contains", "D_WYSIWYG_DELIMITED_STRING_MODE", "D_ALTERNATE_WYSIWYG_STRING_MODE", "D_HEX_STRING_MODE", "D_TOKEN_STRING_MODE", "D_HASHBANG_MODE", "D_SPECIAL_TOKEN_SEQUENCE_MODE", "D_ATTRIBUTE_MODE", "D_NESTING_COMMENT_MODE", "COMMENT", "name", "keywords", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/d.js"], "sourcesContent": ["/*\nLanguage: D\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: D is a language with C-like syntax and static typing. It pragmatically combines efficiency, control, and modeling power, with safety and programmer productivity.\nVersion: 1.0a\nWebsite: https://dlang.org\nDate: 2012-04-08\n*/\n\n/**\n * Known issues:\n *\n * - invalid hex string literals will be recognized as a double quoted strings\n *   but 'x' at the beginning of string will not be matched\n *\n * - delimited string literals are not checked for matching end delimiter\n *   (not possible to do with js regexp)\n *\n * - content of token string is colored as a string (i.e. no keyword coloring inside a token string)\n *   also, content of token string is not validated to contain only valid D tokens\n *\n * - special token sequence rule is not strictly following D grammar (anything following #line\n *   up to the end of line is matched as special token sequence)\n */\n\n/** @type LanguageFn */\nfunction d(hljs) {\n  /**\n   * Language keywords\n   *\n   * @type {Object}\n   */\n  const D_KEYWORDS = {\n    $pattern: hljs.UNDERSCORE_IDENT_RE,\n    keyword:\n      'abstract alias align asm assert auto body break byte case cast catch class ' +\n      'const continue debug default delete deprecated do else enum export extern final ' +\n      'finally for foreach foreach_reverse|10 goto if immutable import in inout int ' +\n      'interface invariant is lazy macro mixin module new nothrow out override package ' +\n      'pragma private protected public pure ref return scope shared static struct ' +\n      'super switch synchronized template this throw try typedef typeid typeof union ' +\n      'unittest version void volatile while with __FILE__ __LINE__ __gshared|10 ' +\n      '__thread __traits __DATE__ __EOF__ __TIME__ __TIMESTAMP__ __VENDOR__ __VERSION__',\n    built_in:\n      'bool cdouble cent cfloat char creal dchar delegate double dstring float function ' +\n      'idouble ifloat ireal long real short string ubyte ucent uint ulong ushort wchar ' +\n      'wstring',\n    literal:\n      'false null true'\n  };\n\n  /**\n   * Number literal regexps\n   *\n   * @type {String}\n   */\n  const decimal_integer_re = '(0|[1-9][\\\\d_]*)';\n  const decimal_integer_nosus_re = '(0|[1-9][\\\\d_]*|\\\\d[\\\\d_]*|[\\\\d_]+?\\\\d)';\n  const binary_integer_re = '0[bB][01_]+';\n  const hexadecimal_digits_re = '([\\\\da-fA-F][\\\\da-fA-F_]*|_[\\\\da-fA-F][\\\\da-fA-F_]*)';\n  const hexadecimal_integer_re = '0[xX]' + hexadecimal_digits_re;\n\n  const decimal_exponent_re = '([eE][+-]?' + decimal_integer_nosus_re + ')';\n  const decimal_float_re = '(' + decimal_integer_nosus_re + '(\\\\.\\\\d*|' + decimal_exponent_re + ')|' +\n                '\\\\d+\\\\.' + decimal_integer_nosus_re + '|' +\n                '\\\\.' + decimal_integer_re + decimal_exponent_re + '?' +\n              ')';\n  const hexadecimal_float_re = '(0[xX](' +\n                  hexadecimal_digits_re + '\\\\.' + hexadecimal_digits_re + '|' +\n                  '\\\\.?' + hexadecimal_digits_re +\n                 ')[pP][+-]?' + decimal_integer_nosus_re + ')';\n\n  const integer_re = '(' +\n      decimal_integer_re + '|' +\n      binary_integer_re + '|' +\n       hexadecimal_integer_re +\n    ')';\n\n  const float_re = '(' +\n      hexadecimal_float_re + '|' +\n      decimal_float_re +\n    ')';\n\n  /**\n   * Escape sequence supported in D string and character literals\n   *\n   * @type {String}\n   */\n  const escape_sequence_re = '\\\\\\\\(' +\n              '[\\'\"\\\\?\\\\\\\\abfnrtv]|' + // common escapes\n              'u[\\\\dA-Fa-f]{4}|' + // four hex digit unicode codepoint\n              '[0-7]{1,3}|' + // one to three octal digit ascii char code\n              'x[\\\\dA-Fa-f]{2}|' + // two hex digit ascii char code\n              'U[\\\\dA-Fa-f]{8}' + // eight hex digit unicode codepoint\n              ')|' +\n              '&[a-zA-Z\\\\d]{2,};'; // named character entity\n\n  /**\n   * D integer number literals\n   *\n   * @type {Object}\n   */\n  const D_INTEGER_MODE = {\n    className: 'number',\n    begin: '\\\\b' + integer_re + '(L|u|U|Lu|LU|uL|UL)?',\n    relevance: 0\n  };\n\n  /**\n   * [D_FLOAT_MODE description]\n   * @type {Object}\n   */\n  const D_FLOAT_MODE = {\n    className: 'number',\n    begin: '\\\\b(' +\n        float_re + '([fF]|L|i|[fF]i|Li)?|' +\n        integer_re + '(i|[fF]i|Li)' +\n      ')',\n    relevance: 0\n  };\n\n  /**\n   * D character literal\n   *\n   * @type {Object}\n   */\n  const D_CHARACTER_MODE = {\n    className: 'string',\n    begin: '\\'(' + escape_sequence_re + '|.)',\n    end: '\\'',\n    illegal: '.'\n  };\n\n  /**\n   * D string escape sequence\n   *\n   * @type {Object}\n   */\n  const D_ESCAPE_SEQUENCE = {\n    begin: escape_sequence_re,\n    relevance: 0\n  };\n\n  /**\n   * D double quoted string literal\n   *\n   * @type {Object}\n   */\n  const D_STRING_MODE = {\n    className: 'string',\n    begin: '\"',\n    contains: [D_ESCAPE_SEQUENCE],\n    end: '\"[cwd]?'\n  };\n\n  /**\n   * D wysiwyg and delimited string literals\n   *\n   * @type {Object}\n   */\n  const D_WYSIWYG_DELIMITED_STRING_MODE = {\n    className: 'string',\n    begin: '[rq]\"',\n    end: '\"[cwd]?',\n    relevance: 5\n  };\n\n  /**\n   * D alternate wysiwyg string literal\n   *\n   * @type {Object}\n   */\n  const D_ALTERNATE_WYSIWYG_STRING_MODE = {\n    className: 'string',\n    begin: '`',\n    end: '`[cwd]?'\n  };\n\n  /**\n   * D hexadecimal string literal\n   *\n   * @type {Object}\n   */\n  const D_HEX_STRING_MODE = {\n    className: 'string',\n    begin: 'x\"[\\\\da-fA-F\\\\s\\\\n\\\\r]*\"[cwd]?',\n    relevance: 10\n  };\n\n  /**\n   * D delimited string literal\n   *\n   * @type {Object}\n   */\n  const D_TOKEN_STRING_MODE = {\n    className: 'string',\n    begin: 'q\"\\\\{',\n    end: '\\\\}\"'\n  };\n\n  /**\n   * Hashbang support\n   *\n   * @type {Object}\n   */\n  const D_HASHBANG_MODE = {\n    className: 'meta',\n    begin: '^#!',\n    end: '$',\n    relevance: 5\n  };\n\n  /**\n   * D special token sequence\n   *\n   * @type {Object}\n   */\n  const D_SPECIAL_TOKEN_SEQUENCE_MODE = {\n    className: 'meta',\n    begin: '#(line)',\n    end: '$',\n    relevance: 5\n  };\n\n  /**\n   * D attributes\n   *\n   * @type {Object}\n   */\n  const D_ATTRIBUTE_MODE = {\n    className: 'keyword',\n    begin: '@[a-zA-Z_][a-zA-Z_\\\\d]*'\n  };\n\n  /**\n   * D nesting comment\n   *\n   * @type {Object}\n   */\n  const D_NESTING_COMMENT_MODE = hljs.COMMENT(\n    '\\\\/\\\\+',\n    '\\\\+\\\\/',\n    {\n      contains: ['self'],\n      relevance: 10\n    }\n  );\n\n  return {\n    name: 'D',\n    keywords: D_KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      D_NESTING_COMMENT_MODE,\n      D_HEX_STRING_MODE,\n      D_STRING_MODE,\n      D_WYSIWYG_DELIMITED_STRING_MODE,\n      D_ALTERNATE_WYSIWYG_STRING_MODE,\n      D_TOKEN_STRING_MODE,\n      D_FLOAT_MODE,\n      D_INTEGER_MODE,\n      D_CHARACTER_MODE,\n      D_HASHBANG_MODE,\n      D_SPECIAL_TOKEN_SEQUENCE_MODE,\n      D_ATTRIBUTE_MODE\n    ]\n  };\n}\n\nmodule.exports = d;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,CAACA,CAACC,IAAI,EAAE;EACf;AACF;AACA;AACA;AACA;EACE,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAEF,IAAI,CAACG,mBAAmB;IAClCC,OAAO,EACL,6EAA6E,GAC7E,kFAAkF,GAClF,+EAA+E,GAC/E,kFAAkF,GAClF,6EAA6E,GAC7E,gFAAgF,GAChF,2EAA2E,GAC3E,kFAAkF;IACpFC,QAAQ,EACN,mFAAmF,GACnF,kFAAkF,GAClF,SAAS;IACXC,OAAO,EACL;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,kBAAkB,GAAG,kBAAkB;EAC7C,MAAMC,wBAAwB,GAAG,yCAAyC;EAC1E,MAAMC,iBAAiB,GAAG,aAAa;EACvC,MAAMC,qBAAqB,GAAG,sDAAsD;EACpF,MAAMC,sBAAsB,GAAG,OAAO,GAAGD,qBAAqB;EAE9D,MAAME,mBAAmB,GAAG,YAAY,GAAGJ,wBAAwB,GAAG,GAAG;EACzE,MAAMK,gBAAgB,GAAG,GAAG,GAAGL,wBAAwB,GAAG,WAAW,GAAGI,mBAAmB,GAAG,IAAI,GACpF,SAAS,GAAGJ,wBAAwB,GAAG,GAAG,GAC1C,KAAK,GAAGD,kBAAkB,GAAGK,mBAAmB,GAAG,GAAG,GACxD,GAAG;EACf,MAAME,oBAAoB,GAAG,SAAS,GACtBJ,qBAAqB,GAAG,KAAK,GAAGA,qBAAqB,GAAG,GAAG,GAC3D,MAAM,GAAGA,qBAAqB,GAC/B,YAAY,GAAGF,wBAAwB,GAAG,GAAG;EAE5D,MAAMO,UAAU,GAAG,GAAG,GAClBR,kBAAkB,GAAG,GAAG,GACxBE,iBAAiB,GAAG,GAAG,GACtBE,sBAAsB,GACzB,GAAG;EAEL,MAAMK,QAAQ,GAAG,GAAG,GAChBF,oBAAoB,GAAG,GAAG,GAC1BD,gBAAgB,GAClB,GAAG;;EAEL;AACF;AACA;AACA;AACA;EACE,MAAMI,kBAAkB,GAAG,OAAO,GACtB,sBAAsB;EAAG;EACzB,kBAAkB;EAAG;EACrB,aAAa;EAAG;EAChB,kBAAkB;EAAG;EACrB,iBAAiB;EAAG;EACpB,IAAI,GACJ,mBAAmB,CAAC,CAAC;;EAEjC;AACF;AACA;AACA;AACA;EACE,MAAMC,cAAc,GAAG;IACrBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK,GAAGL,UAAU,GAAG,sBAAsB;IAClDM,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMC,YAAY,GAAG;IACnBH,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,MAAM,GACTJ,QAAQ,GAAG,uBAAuB,GAClCD,UAAU,GAAG,cAAc,GAC7B,GAAG;IACLM,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAME,gBAAgB,GAAG;IACvBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK,GAAGH,kBAAkB,GAAG,KAAK;IACzCO,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE;EACX,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,iBAAiB,GAAG;IACxBN,KAAK,EAAEH,kBAAkB;IACzBI,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMM,aAAa,GAAG;IACpBR,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVQ,QAAQ,EAAE,CAACF,iBAAiB,CAAC;IAC7BF,GAAG,EAAE;EACP,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMK,+BAA+B,GAAG;IACtCV,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,OAAO;IACdI,GAAG,EAAE,SAAS;IACdH,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMS,+BAA+B,GAAG;IACtCX,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVI,GAAG,EAAE;EACP,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMO,iBAAiB,GAAG;IACxBZ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,gCAAgC;IACvCC,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMW,mBAAmB,GAAG;IAC1Bb,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,OAAO;IACdI,GAAG,EAAE;EACP,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMS,eAAe,GAAG;IACtBd,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,KAAK;IACZI,GAAG,EAAE,GAAG;IACRH,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMa,6BAA6B,GAAG;IACpCf,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,SAAS;IAChBI,GAAG,EAAE,GAAG;IACRH,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMc,gBAAgB,GAAG;IACvBhB,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE;EACT,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMgB,sBAAsB,GAAGpC,IAAI,CAACqC,OAAO,CACzC,QAAQ,EACR,QAAQ,EACR;IACET,QAAQ,EAAE,CAAC,MAAM,CAAC;IAClBP,SAAS,EAAE;EACb,CACF,CAAC;EAED,OAAO;IACLiB,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAEtC,UAAU;IACpB2B,QAAQ,EAAE,CACR5B,IAAI,CAACwC,mBAAmB,EACxBxC,IAAI,CAACyC,oBAAoB,EACzBL,sBAAsB,EACtBL,iBAAiB,EACjBJ,aAAa,EACbE,+BAA+B,EAC/BC,+BAA+B,EAC/BE,mBAAmB,EACnBV,YAAY,EACZJ,cAAc,EACdK,gBAAgB,EAChBU,eAAe,EACfC,6BAA6B,EAC7BC,gBAAgB;EAEpB,CAAC;AACH;AAEAO,MAAM,CAACC,OAAO,GAAG5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}