{"ast": null, "code": "'use strict';\n\nvar high = require('highlight.js/lib/core');\nvar fault = require('fault');\nexports.highlight = highlight;\nexports.highlightAuto = highlightAuto;\nexports.registerLanguage = registerLanguage;\nexports.listLanguages = listLanguages;\nexports.registerAlias = registerAlias;\nEmitter.prototype.addText = text;\nEmitter.prototype.addKeyword = addKeyword;\nEmitter.prototype.addSublanguage = addSublanguage;\nEmitter.prototype.openNode = open;\nEmitter.prototype.closeNode = close;\nEmitter.prototype.closeAllNodes = noop;\nEmitter.prototype.finalize = noop;\nEmitter.prototype.toHTML = toHtmlNoop;\nvar defaultPrefix = 'hljs-';\n\n// Highlighting `value` in the language `name`.\nfunction highlight(name, value, options) {\n  var before = high.configure({});\n  var settings = options || {};\n  var prefix = settings.prefix;\n  var result;\n  if (typeof name !== 'string') {\n    throw fault('Expected `string` for name, got `%s`', name);\n  }\n  if (!high.getLanguage(name)) {\n    throw fault('Unknown language: `%s` is not registered', name);\n  }\n  if (typeof value !== 'string') {\n    throw fault('Expected `string` for value, got `%s`', value);\n  }\n  if (prefix === null || prefix === undefined) {\n    prefix = defaultPrefix;\n  }\n  high.configure({\n    __emitter: Emitter,\n    classPrefix: prefix\n  });\n  result = high.highlight(value, {\n    language: name,\n    ignoreIllegals: true\n  });\n  high.configure(before || {});\n\n  /* istanbul ignore if - Highlight.js seems to use this (currently) for broken\n   * grammars, so let’s keep it in there just to be sure. */\n  if (result.errorRaised) {\n    throw result.errorRaised;\n  }\n  return {\n    relevance: result.relevance,\n    language: result.language,\n    value: result.emitter.rootNode.children\n  };\n}\nfunction highlightAuto(value, options) {\n  var settings = options || {};\n  var subset = settings.subset || high.listLanguages();\n  var prefix = settings.prefix;\n  var length = subset.length;\n  var index = -1;\n  var result;\n  var secondBest;\n  var current;\n  var name;\n  if (prefix === null || prefix === undefined) {\n    prefix = defaultPrefix;\n  }\n  if (typeof value !== 'string') {\n    throw fault('Expected `string` for value, got `%s`', value);\n  }\n  secondBest = {\n    relevance: 0,\n    language: null,\n    value: []\n  };\n  result = {\n    relevance: 0,\n    language: null,\n    value: []\n  };\n  while (++index < length) {\n    name = subset[index];\n    if (!high.getLanguage(name)) {\n      continue;\n    }\n    current = highlight(name, value, options);\n    current.language = name;\n    if (current.relevance > secondBest.relevance) {\n      secondBest = current;\n    }\n    if (current.relevance > result.relevance) {\n      secondBest = result;\n      result = current;\n    }\n  }\n  if (secondBest.language) {\n    result.secondBest = secondBest;\n  }\n  return result;\n}\n\n// Register a language.\nfunction registerLanguage(name, syntax) {\n  high.registerLanguage(name, syntax);\n}\n\n// Get a list of all registered languages.\nfunction listLanguages() {\n  return high.listLanguages();\n}\n\n// Register more aliases for an already registered language.\nfunction registerAlias(name, alias) {\n  var map = name;\n  var key;\n  if (alias) {\n    map = {};\n    map[name] = alias;\n  }\n  for (key in map) {\n    high.registerAliases(map[key], {\n      languageName: key\n    });\n  }\n}\nfunction Emitter(options) {\n  this.options = options;\n  this.rootNode = {\n    children: []\n  };\n  this.stack = [this.rootNode];\n}\nfunction addKeyword(value, name) {\n  this.openNode(name);\n  this.addText(value);\n  this.closeNode();\n}\nfunction addSublanguage(other, name) {\n  var stack = this.stack;\n  var current = stack[stack.length - 1];\n  var results = other.rootNode.children;\n  var node = name ? {\n    type: 'element',\n    tagName: 'span',\n    properties: {\n      className: [name]\n    },\n    children: results\n  } : results;\n  current.children = current.children.concat(node);\n}\nfunction text(value) {\n  var stack = this.stack;\n  var current;\n  var tail;\n  if (value === '') return;\n  current = stack[stack.length - 1];\n  tail = current.children[current.children.length - 1];\n  if (tail && tail.type === 'text') {\n    tail.value += value;\n  } else {\n    current.children.push({\n      type: 'text',\n      value: value\n    });\n  }\n}\nfunction open(name) {\n  var stack = this.stack;\n  var className = this.options.classPrefix + name;\n  var current = stack[stack.length - 1];\n  var child = {\n    type: 'element',\n    tagName: 'span',\n    properties: {\n      className: [className]\n    },\n    children: []\n  };\n  current.children.push(child);\n  stack.push(child);\n}\nfunction close() {\n  this.stack.pop();\n}\nfunction toHtmlNoop() {\n  return '';\n}\nfunction noop() {}", "map": {"version": 3, "names": ["high", "require", "fault", "exports", "highlight", "highlightAuto", "registerLanguage", "listLanguages", "registerAlias", "Emitter", "prototype", "addText", "text", "addKeyword", "addSublanguage", "openNode", "open", "closeNode", "close", "closeAllNodes", "noop", "finalize", "toHTML", "toHtmlNoop", "defaultPrefix", "name", "value", "options", "before", "configure", "settings", "prefix", "result", "getLanguage", "undefined", "__emitter", "classPrefix", "language", "ignoreIllegals", "errorRaised", "relevance", "emitter", "rootNode", "children", "subset", "length", "index", "secondBest", "current", "syntax", "alias", "map", "key", "registerAliases", "languageName", "stack", "other", "results", "node", "type", "tagName", "properties", "className", "concat", "tail", "push", "child", "pop"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/lowlight/lib/core.js"], "sourcesContent": ["'use strict'\n\nvar high = require('highlight.js/lib/core')\nvar fault = require('fault')\n\nexports.highlight = highlight\nexports.highlightAuto = highlightAuto\nexports.registerLanguage = registerLanguage\nexports.listLanguages = listLanguages\nexports.registerAlias = registerAlias\n\nEmitter.prototype.addText = text\nEmitter.prototype.addKeyword = addKeyword\nEmitter.prototype.addSublanguage = addSublanguage\nEmitter.prototype.openNode = open\nEmitter.prototype.closeNode = close\nEmitter.prototype.closeAllNodes = noop\nEmitter.prototype.finalize = noop\nEmitter.prototype.toHTML = toHtmlNoop\n\nvar defaultPrefix = 'hljs-'\n\n// Highlighting `value` in the language `name`.\nfunction highlight(name, value, options) {\n  var before = high.configure({})\n  var settings = options || {}\n  var prefix = settings.prefix\n  var result\n\n  if (typeof name !== 'string') {\n    throw fault('Expected `string` for name, got `%s`', name)\n  }\n\n  if (!high.getLanguage(name)) {\n    throw fault('Unknown language: `%s` is not registered', name)\n  }\n\n  if (typeof value !== 'string') {\n    throw fault('Expected `string` for value, got `%s`', value)\n  }\n\n  if (prefix === null || prefix === undefined) {\n    prefix = defaultPrefix\n  }\n\n  high.configure({__emitter: Emitter, classPrefix: prefix})\n\n  result = high.highlight(value, {language: name, ignoreIllegals: true})\n\n  high.configure(before || {})\n\n  /* istanbul ignore if - Highlight.js seems to use this (currently) for broken\n   * grammars, so let’s keep it in there just to be sure. */\n  if (result.errorRaised) {\n    throw result.errorRaised\n  }\n\n  return {\n    relevance: result.relevance,\n    language: result.language,\n    value: result.emitter.rootNode.children\n  }\n}\n\nfunction highlightAuto(value, options) {\n  var settings = options || {}\n  var subset = settings.subset || high.listLanguages()\n  var prefix = settings.prefix\n  var length = subset.length\n  var index = -1\n  var result\n  var secondBest\n  var current\n  var name\n\n  if (prefix === null || prefix === undefined) {\n    prefix = defaultPrefix\n  }\n\n  if (typeof value !== 'string') {\n    throw fault('Expected `string` for value, got `%s`', value)\n  }\n\n  secondBest = {relevance: 0, language: null, value: []}\n  result = {relevance: 0, language: null, value: []}\n\n  while (++index < length) {\n    name = subset[index]\n\n    if (!high.getLanguage(name)) {\n      continue\n    }\n\n    current = highlight(name, value, options)\n    current.language = name\n\n    if (current.relevance > secondBest.relevance) {\n      secondBest = current\n    }\n\n    if (current.relevance > result.relevance) {\n      secondBest = result\n      result = current\n    }\n  }\n\n  if (secondBest.language) {\n    result.secondBest = secondBest\n  }\n\n  return result\n}\n\n// Register a language.\nfunction registerLanguage(name, syntax) {\n  high.registerLanguage(name, syntax)\n}\n\n// Get a list of all registered languages.\nfunction listLanguages() {\n  return high.listLanguages()\n}\n\n// Register more aliases for an already registered language.\nfunction registerAlias(name, alias) {\n  var map = name\n  var key\n\n  if (alias) {\n    map = {}\n    map[name] = alias\n  }\n\n  for (key in map) {\n    high.registerAliases(map[key], {languageName: key})\n  }\n}\n\nfunction Emitter(options) {\n  this.options = options\n  this.rootNode = {children: []}\n  this.stack = [this.rootNode]\n}\n\nfunction addKeyword(value, name) {\n  this.openNode(name)\n  this.addText(value)\n  this.closeNode()\n}\n\nfunction addSublanguage(other, name) {\n  var stack = this.stack\n  var current = stack[stack.length - 1]\n  var results = other.rootNode.children\n  var node = name\n    ? {\n        type: 'element',\n        tagName: 'span',\n        properties: {className: [name]},\n        children: results\n      }\n    : results\n\n  current.children = current.children.concat(node)\n}\n\nfunction text(value) {\n  var stack = this.stack\n  var current\n  var tail\n\n  if (value === '') return\n\n  current = stack[stack.length - 1]\n  tail = current.children[current.children.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += value\n  } else {\n    current.children.push({type: 'text', value: value})\n  }\n}\n\nfunction open(name) {\n  var stack = this.stack\n  var className = this.options.classPrefix + name\n  var current = stack[stack.length - 1]\n  var child = {\n    type: 'element',\n    tagName: 'span',\n    properties: {className: [className]},\n    children: []\n  }\n\n  current.children.push(child)\n  stack.push(child)\n}\n\nfunction close() {\n  this.stack.pop()\n}\n\nfunction toHtmlNoop() {\n  return ''\n}\n\nfunction noop() {}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAC3C,IAAIC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAE5BE,OAAO,CAACC,SAAS,GAAGA,SAAS;AAC7BD,OAAO,CAACE,aAAa,GAAGA,aAAa;AACrCF,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3CH,OAAO,CAACI,aAAa,GAAGA,aAAa;AACrCJ,OAAO,CAACK,aAAa,GAAGA,aAAa;AAErCC,OAAO,CAACC,SAAS,CAACC,OAAO,GAAGC,IAAI;AAChCH,OAAO,CAACC,SAAS,CAACG,UAAU,GAAGA,UAAU;AACzCJ,OAAO,CAACC,SAAS,CAACI,cAAc,GAAGA,cAAc;AACjDL,OAAO,CAACC,SAAS,CAACK,QAAQ,GAAGC,IAAI;AACjCP,OAAO,CAACC,SAAS,CAACO,SAAS,GAAGC,KAAK;AACnCT,OAAO,CAACC,SAAS,CAACS,aAAa,GAAGC,IAAI;AACtCX,OAAO,CAACC,SAAS,CAACW,QAAQ,GAAGD,IAAI;AACjCX,OAAO,CAACC,SAAS,CAACY,MAAM,GAAGC,UAAU;AAErC,IAAIC,aAAa,GAAG,OAAO;;AAE3B;AACA,SAASpB,SAASA,CAACqB,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACvC,IAAIC,MAAM,GAAG5B,IAAI,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAIC,QAAQ,GAAGH,OAAO,IAAI,CAAC,CAAC;EAC5B,IAAII,MAAM,GAAGD,QAAQ,CAACC,MAAM;EAC5B,IAAIC,MAAM;EAEV,IAAI,OAAOP,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAMvB,KAAK,CAAC,sCAAsC,EAAEuB,IAAI,CAAC;EAC3D;EAEA,IAAI,CAACzB,IAAI,CAACiC,WAAW,CAACR,IAAI,CAAC,EAAE;IAC3B,MAAMvB,KAAK,CAAC,0CAA0C,EAAEuB,IAAI,CAAC;EAC/D;EAEA,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMxB,KAAK,CAAC,uCAAuC,EAAEwB,KAAK,CAAC;EAC7D;EAEA,IAAIK,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKG,SAAS,EAAE;IAC3CH,MAAM,GAAGP,aAAa;EACxB;EAEAxB,IAAI,CAAC6B,SAAS,CAAC;IAACM,SAAS,EAAE1B,OAAO;IAAE2B,WAAW,EAAEL;EAAM,CAAC,CAAC;EAEzDC,MAAM,GAAGhC,IAAI,CAACI,SAAS,CAACsB,KAAK,EAAE;IAACW,QAAQ,EAAEZ,IAAI;IAAEa,cAAc,EAAE;EAAI,CAAC,CAAC;EAEtEtC,IAAI,CAAC6B,SAAS,CAACD,MAAM,IAAI,CAAC,CAAC,CAAC;;EAE5B;AACF;EACE,IAAII,MAAM,CAACO,WAAW,EAAE;IACtB,MAAMP,MAAM,CAACO,WAAW;EAC1B;EAEA,OAAO;IACLC,SAAS,EAAER,MAAM,CAACQ,SAAS;IAC3BH,QAAQ,EAAEL,MAAM,CAACK,QAAQ;IACzBX,KAAK,EAAEM,MAAM,CAACS,OAAO,CAACC,QAAQ,CAACC;EACjC,CAAC;AACH;AAEA,SAAStC,aAAaA,CAACqB,KAAK,EAAEC,OAAO,EAAE;EACrC,IAAIG,QAAQ,GAAGH,OAAO,IAAI,CAAC,CAAC;EAC5B,IAAIiB,MAAM,GAAGd,QAAQ,CAACc,MAAM,IAAI5C,IAAI,CAACO,aAAa,CAAC,CAAC;EACpD,IAAIwB,MAAM,GAAGD,QAAQ,CAACC,MAAM;EAC5B,IAAIc,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC1B,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAId,MAAM;EACV,IAAIe,UAAU;EACd,IAAIC,OAAO;EACX,IAAIvB,IAAI;EAER,IAAIM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKG,SAAS,EAAE;IAC3CH,MAAM,GAAGP,aAAa;EACxB;EAEA,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMxB,KAAK,CAAC,uCAAuC,EAAEwB,KAAK,CAAC;EAC7D;EAEAqB,UAAU,GAAG;IAACP,SAAS,EAAE,CAAC;IAAEH,QAAQ,EAAE,IAAI;IAAEX,KAAK,EAAE;EAAE,CAAC;EACtDM,MAAM,GAAG;IAACQ,SAAS,EAAE,CAAC;IAAEH,QAAQ,EAAE,IAAI;IAAEX,KAAK,EAAE;EAAE,CAAC;EAElD,OAAO,EAAEoB,KAAK,GAAGD,MAAM,EAAE;IACvBpB,IAAI,GAAGmB,MAAM,CAACE,KAAK,CAAC;IAEpB,IAAI,CAAC9C,IAAI,CAACiC,WAAW,CAACR,IAAI,CAAC,EAAE;MAC3B;IACF;IAEAuB,OAAO,GAAG5C,SAAS,CAACqB,IAAI,EAAEC,KAAK,EAAEC,OAAO,CAAC;IACzCqB,OAAO,CAACX,QAAQ,GAAGZ,IAAI;IAEvB,IAAIuB,OAAO,CAACR,SAAS,GAAGO,UAAU,CAACP,SAAS,EAAE;MAC5CO,UAAU,GAAGC,OAAO;IACtB;IAEA,IAAIA,OAAO,CAACR,SAAS,GAAGR,MAAM,CAACQ,SAAS,EAAE;MACxCO,UAAU,GAAGf,MAAM;MACnBA,MAAM,GAAGgB,OAAO;IAClB;EACF;EAEA,IAAID,UAAU,CAACV,QAAQ,EAAE;IACvBL,MAAM,CAACe,UAAU,GAAGA,UAAU;EAChC;EAEA,OAAOf,MAAM;AACf;;AAEA;AACA,SAAS1B,gBAAgBA,CAACmB,IAAI,EAAEwB,MAAM,EAAE;EACtCjD,IAAI,CAACM,gBAAgB,CAACmB,IAAI,EAAEwB,MAAM,CAAC;AACrC;;AAEA;AACA,SAAS1C,aAAaA,CAAA,EAAG;EACvB,OAAOP,IAAI,CAACO,aAAa,CAAC,CAAC;AAC7B;;AAEA;AACA,SAASC,aAAaA,CAACiB,IAAI,EAAEyB,KAAK,EAAE;EAClC,IAAIC,GAAG,GAAG1B,IAAI;EACd,IAAI2B,GAAG;EAEP,IAAIF,KAAK,EAAE;IACTC,GAAG,GAAG,CAAC,CAAC;IACRA,GAAG,CAAC1B,IAAI,CAAC,GAAGyB,KAAK;EACnB;EAEA,KAAKE,GAAG,IAAID,GAAG,EAAE;IACfnD,IAAI,CAACqD,eAAe,CAACF,GAAG,CAACC,GAAG,CAAC,EAAE;MAACE,YAAY,EAAEF;IAAG,CAAC,CAAC;EACrD;AACF;AAEA,SAAS3C,OAAOA,CAACkB,OAAO,EAAE;EACxB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACe,QAAQ,GAAG;IAACC,QAAQ,EAAE;EAAE,CAAC;EAC9B,IAAI,CAACY,KAAK,GAAG,CAAC,IAAI,CAACb,QAAQ,CAAC;AAC9B;AAEA,SAAS7B,UAAUA,CAACa,KAAK,EAAED,IAAI,EAAE;EAC/B,IAAI,CAACV,QAAQ,CAACU,IAAI,CAAC;EACnB,IAAI,CAACd,OAAO,CAACe,KAAK,CAAC;EACnB,IAAI,CAACT,SAAS,CAAC,CAAC;AAClB;AAEA,SAASH,cAAcA,CAAC0C,KAAK,EAAE/B,IAAI,EAAE;EACnC,IAAI8B,KAAK,GAAG,IAAI,CAACA,KAAK;EACtB,IAAIP,OAAO,GAAGO,KAAK,CAACA,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC;EACrC,IAAIY,OAAO,GAAGD,KAAK,CAACd,QAAQ,CAACC,QAAQ;EACrC,IAAIe,IAAI,GAAGjC,IAAI,GACX;IACEkC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE;MAACC,SAAS,EAAE,CAACrC,IAAI;IAAC,CAAC;IAC/BkB,QAAQ,EAAEc;EACZ,CAAC,GACDA,OAAO;EAEXT,OAAO,CAACL,QAAQ,GAAGK,OAAO,CAACL,QAAQ,CAACoB,MAAM,CAACL,IAAI,CAAC;AAClD;AAEA,SAAS9C,IAAIA,CAACc,KAAK,EAAE;EACnB,IAAI6B,KAAK,GAAG,IAAI,CAACA,KAAK;EACtB,IAAIP,OAAO;EACX,IAAIgB,IAAI;EAER,IAAItC,KAAK,KAAK,EAAE,EAAE;EAElBsB,OAAO,GAAGO,KAAK,CAACA,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC;EACjCmB,IAAI,GAAGhB,OAAO,CAACL,QAAQ,CAACK,OAAO,CAACL,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC;EAEpD,IAAImB,IAAI,IAAIA,IAAI,CAACL,IAAI,KAAK,MAAM,EAAE;IAChCK,IAAI,CAACtC,KAAK,IAAIA,KAAK;EACrB,CAAC,MAAM;IACLsB,OAAO,CAACL,QAAQ,CAACsB,IAAI,CAAC;MAACN,IAAI,EAAE,MAAM;MAAEjC,KAAK,EAAEA;IAAK,CAAC,CAAC;EACrD;AACF;AAEA,SAASV,IAAIA,CAACS,IAAI,EAAE;EAClB,IAAI8B,KAAK,GAAG,IAAI,CAACA,KAAK;EACtB,IAAIO,SAAS,GAAG,IAAI,CAACnC,OAAO,CAACS,WAAW,GAAGX,IAAI;EAC/C,IAAIuB,OAAO,GAAGO,KAAK,CAACA,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC;EACrC,IAAIqB,KAAK,GAAG;IACVP,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE;MAACC,SAAS,EAAE,CAACA,SAAS;IAAC,CAAC;IACpCnB,QAAQ,EAAE;EACZ,CAAC;EAEDK,OAAO,CAACL,QAAQ,CAACsB,IAAI,CAACC,KAAK,CAAC;EAC5BX,KAAK,CAACU,IAAI,CAACC,KAAK,CAAC;AACnB;AAEA,SAAShD,KAAKA,CAAA,EAAG;EACf,IAAI,CAACqC,KAAK,CAACY,GAAG,CAAC,CAAC;AAClB;AAEA,SAAS5C,UAAUA,CAAA,EAAG;EACpB,OAAO,EAAE;AACX;AAEA,SAASH,IAAIA,CAAA,EAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}