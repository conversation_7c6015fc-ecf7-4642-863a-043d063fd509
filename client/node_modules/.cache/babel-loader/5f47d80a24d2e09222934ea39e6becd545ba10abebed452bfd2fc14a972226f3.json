{"ast": null, "code": "/*\nLanguage: Cap’n Proto\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Cap’n Proto message definition format\nWebsite: https://capnproto.org/capnp-tool.html\nCategory: protocols\n*/\n\n/** @type LanguageFn */\nfunction capnproto(hljs) {\n  return {\n    name: 'Cap’n Proto',\n    aliases: ['capnp'],\n    keywords: {\n      keyword: 'struct enum interface union group import using const annotation extends in of on as with from fixed',\n      built_in: 'Void Bool Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 ' + 'Text Data AnyPointer AnyStruct Capability List',\n      literal: 'true false'\n    },\n    contains: [hljs.QUOTE_STRING_MODE, hljs.NUMBER_MODE, hljs.HASH_COMMENT_MODE, {\n      className: 'meta',\n      begin: /@0x[\\w\\d]{16};/,\n      illegal: /\\n/\n    }, {\n      className: 'symbol',\n      begin: /@\\d+\\b/\n    }, {\n      className: 'class',\n      beginKeywords: 'struct enum',\n      end: /\\{/,\n      illegal: /\\n/,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        starts: {\n          endsWithParent: true,\n          excludeEnd: true\n        } // hack: eating everything after the first title\n      })]\n    }, {\n      className: 'class',\n      beginKeywords: 'interface',\n      end: /\\{/,\n      illegal: /\\n/,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        starts: {\n          endsWithParent: true,\n          excludeEnd: true\n        } // hack: eating everything after the first title\n      })]\n    }]\n  };\n}\nmodule.exports = capnproto;", "map": {"version": 3, "names": ["capnproto", "hljs", "name", "aliases", "keywords", "keyword", "built_in", "literal", "contains", "QUOTE_STRING_MODE", "NUMBER_MODE", "HASH_COMMENT_MODE", "className", "begin", "illegal", "beginKeywords", "end", "inherit", "TITLE_MODE", "starts", "endsWithParent", "excludeEnd", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/capnproto.js"], "sourcesContent": ["/*\nLanguage: Cap’n Proto\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Cap’n Proto message definition format\nWebsite: https://capnproto.org/capnp-tool.html\nCategory: protocols\n*/\n\n/** @type LanguageFn */\nfunction capnproto(hljs) {\n  return {\n    name: 'Cap’n Proto',\n    aliases: ['capnp'],\n    keywords: {\n      keyword:\n        'struct enum interface union group import using const annotation extends in of on as with from fixed',\n      built_in:\n        'Void Bool Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 ' +\n        'Text Data AnyPointer AnyStruct Capability List',\n      literal:\n        'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: /@0x[\\w\\d]{16};/,\n        illegal: /\\n/\n      },\n      {\n        className: 'symbol',\n        begin: /@\\d+\\b/\n      },\n      {\n        className: 'class',\n        beginKeywords: 'struct enum',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [hljs.inherit(hljs.TITLE_MODE, {\n          starts: {\n            endsWithParent: true,\n            excludeEnd: true\n          } // hack: eating everything after the first title\n        })]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'interface',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [hljs.inherit(hljs.TITLE_MODE, {\n          starts: {\n            endsWithParent: true,\n            excludeEnd: true\n          } // hack: eating everything after the first title\n        })]\n      }\n    ]\n  };\n}\n\nmodule.exports = capnproto;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,SAASA,CAACC,IAAI,EAAE;EACvB,OAAO;IACLC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBC,QAAQ,EAAE;MACRC,OAAO,EACL,qGAAqG;MACvGC,QAAQ,EACN,8EAA8E,GAC9E,gDAAgD;MAClDC,OAAO,EACL;IACJ,CAAC;IACDC,QAAQ,EAAE,CACRP,IAAI,CAACQ,iBAAiB,EACtBR,IAAI,CAACS,WAAW,EAChBT,IAAI,CAACU,iBAAiB,EACtB;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,gBAAgB;MACvBC,OAAO,EAAE;IACX,CAAC,EACD;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,OAAO;MAClBG,aAAa,EAAE,aAAa;MAC5BC,GAAG,EAAE,IAAI;MACTF,OAAO,EAAE,IAAI;MACbN,QAAQ,EAAE,CAACP,IAAI,CAACgB,OAAO,CAAChB,IAAI,CAACiB,UAAU,EAAE;QACvCC,MAAM,EAAE;UACNC,cAAc,EAAE,IAAI;UACpBC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,EACD;MACET,SAAS,EAAE,OAAO;MAClBG,aAAa,EAAE,WAAW;MAC1BC,GAAG,EAAE,IAAI;MACTF,OAAO,EAAE,IAAI;MACbN,QAAQ,EAAE,CAACP,IAAI,CAACgB,OAAO,CAAChB,IAAI,CAACiB,UAAU,EAAE;QACvCC,MAAM,EAAE;UACNC,cAAc,EAAE,IAAI;UACpBC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGvB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}