{"ast": null, "code": "/*\n Language: ArcGIS Arcade\n Category: scripting\n Author: <PERSON> <<EMAIL>>\n Website: https://developers.arcgis.com/arcade/\n Description: ArcGIS Arcade is an expression language used in many Esri ArcGIS products such as Pro, Online, Server, Runtime, JavaScript, and Python\n*/\n\n/** @type LanguageFn */\nfunction arcade(hljs) {\n  const IDENT_RE = '[A-Za-z_][0-9A-Za-z_]*';\n  const KEYWORDS = {\n    keyword: 'if for while var new function do return void else break',\n    literal: 'BackSlash DoubleQuote false ForwardSlash Infinity NaN NewLine null PI SingleQuote Tab TextFormatting true undefined',\n    built_in: 'Abs Acos Angle Attachments Area AreaGeodetic Asin Atan Atan2 Average Bearing Boolean Buffer BufferGeodetic ' + 'Ceil Centroid Clip Console Constrain Contains Cos Count Crosses Cut Date DateAdd ' + 'DateDiff Day Decode DefaultValue Dictionary Difference Disjoint Distance DistanceGeodetic Distinct ' + 'DomainCode DomainName Equals Exp Extent Feature FeatureSet FeatureSetByAssociation FeatureSetById FeatureSetByPortalItem ' + 'FeatureSetByRelationshipName FeatureSetByTitle FeatureSetByUrl Filter First Floor Geometry GroupBy Guid HasKey Hour IIf IndexOf ' + 'Intersection Intersects IsEmpty IsNan IsSelfIntersecting Length LengthGeodetic Log Max Mean Millisecond Min Minute Month ' + 'MultiPartToSinglePart Multipoint NextSequenceValue Now Number OrderBy Overlaps Point Polygon ' + 'Polyline Portal Pow Random Relate Reverse RingIsClockWise Round Second SetGeometry Sin Sort Sqrt Stdev Sum ' + 'SymmetricDifference Tan Text Timestamp Today ToLocal Top Touches ToUTC TrackCurrentTime ' + 'TrackGeometryWindow TrackIndex TrackStartTime TrackWindow TypeOf Union UrlEncode Variance ' + 'Weekday When Within Year '\n  };\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '\\\\$[datastore|feature|layer|map|measure|sourcefeature|sourcelayer|targetfeature|targetlayer|value|view]+'\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [{\n      begin: '\\\\b(0[bB][01]+)'\n    }, {\n      begin: '\\\\b(0[oO][0-7]+)'\n    }, {\n      begin: hljs.C_NUMBER_RE\n    }],\n    relevance: 0\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS,\n    contains: [] // defined later\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [hljs.BACKSLASH_ESCAPE, SUBST]\n  };\n  SUBST.contains = [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, TEMPLATE_STRING, NUMBER, hljs.REGEXP_MODE];\n  const PARAMS_CONTAINS = SUBST.contains.concat([hljs.C_BLOCK_COMMENT_MODE, hljs.C_LINE_COMMENT_MODE]);\n  return {\n    name: 'ArcGIS Arcade',\n    keywords: KEYWORDS,\n    contains: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, TEMPLATE_STRING, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, SYMBOL, NUMBER, {\n      // object attr container\n      begin: /[{,]\\s*/,\n      relevance: 0,\n      contains: [{\n        begin: IDENT_RE + '\\\\s*:',\n        returnBegin: true,\n        relevance: 0,\n        contains: [{\n          className: 'attr',\n          begin: IDENT_RE,\n          relevance: 0\n        }]\n      }]\n    }, {\n      // \"value\" container\n      begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(return)\\\\b)\\\\s*',\n      keywords: 'return',\n      contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.REGEXP_MODE, {\n        className: 'function',\n        begin: '(\\\\(.*?\\\\)|' + IDENT_RE + ')\\\\s*=>',\n        returnBegin: true,\n        end: '\\\\s*=>',\n        contains: [{\n          className: 'params',\n          variants: [{\n            begin: IDENT_RE\n          }, {\n            begin: /\\(\\s*\\)/\n          }, {\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            contains: PARAMS_CONTAINS\n          }]\n        }]\n      }],\n      relevance: 0\n    }, {\n      className: 'function',\n      beginKeywords: 'function',\n      end: /\\{/,\n      excludeEnd: true,\n      contains: [hljs.inherit(hljs.TITLE_MODE, {\n        begin: IDENT_RE\n      }), {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        contains: PARAMS_CONTAINS\n      }],\n      illegal: /\\[|%/\n    }, {\n      begin: /\\$[(.]/\n    }],\n    illegal: /#(?!!)/\n  };\n}\nmodule.exports = arcade;", "map": {"version": 3, "names": ["arcade", "hljs", "IDENT_RE", "KEYWORDS", "keyword", "literal", "built_in", "SYMBOL", "className", "begin", "NUMBER", "variants", "C_NUMBER_RE", "relevance", "SUBST", "end", "keywords", "contains", "TEMPLATE_STRING", "BACKSLASH_ESCAPE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "REGEXP_MODE", "PARAMS_CONTAINS", "concat", "C_BLOCK_COMMENT_MODE", "C_LINE_COMMENT_MODE", "name", "returnBegin", "RE_STARTERS_RE", "excludeBegin", "excludeEnd", "beginKeywords", "inherit", "TITLE_MODE", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/arcade.js"], "sourcesContent": ["/*\n Language: ArcGIS Arcade\n Category: scripting\n Author: <PERSON> <<EMAIL>>\n Website: https://developers.arcgis.com/arcade/\n Description: ArcGIS Arcade is an expression language used in many Esri ArcGIS products such as Pro, Online, Server, Runtime, JavaScript, and Python\n*/\n\n/** @type LanguageFn */\nfunction arcade(hljs) {\n  const IDENT_RE = '[A-Za-z_][0-9A-Za-z_]*';\n  const KEYWORDS = {\n    keyword:\n      'if for while var new function do return void else break',\n    literal:\n      'BackSlash DoubleQuote false ForwardSlash Infinity NaN NewLine null PI SingleQuote Tab TextFormatting true undefined',\n    built_in:\n      'Abs Acos Angle Attachments Area AreaGeodetic Asin Atan Atan2 Average Bearing Boolean Buffer BufferGeodetic ' +\n      'Ceil Centroid Clip Console Constrain Contains Cos Count Crosses Cut Date DateAdd ' +\n      'DateDiff Day Decode DefaultValue Dictionary Difference Disjoint Distance DistanceGeodetic Distinct ' +\n      'DomainCode DomainName Equals Exp Extent Feature FeatureSet FeatureSetByAssociation FeatureSetById FeatureSetByPortalItem ' +\n      'FeatureSetByRelationshipName FeatureSetByTitle FeatureSetByUrl Filter First Floor Geometry GroupBy Guid HasKey Hour IIf IndexOf ' +\n      'Intersection Intersects IsEmpty IsNan IsSelfIntersecting Length LengthGeodetic Log Max Mean Millisecond Min Minute Month ' +\n      'MultiPartToSinglePart Multipoint NextSequenceValue Now Number OrderBy Overlaps Point Polygon ' +\n      'Polyline Portal Pow Random Relate Reverse RingIsClockWise Round Second SetGeometry Sin Sort Sqrt Stdev Sum ' +\n      'SymmetricDifference Tan Text Timestamp Today ToLocal Top Touches ToUTC TrackCurrentTime ' +\n      'TrackGeometryWindow TrackIndex TrackStartTime TrackWindow TypeOf Union UrlEncode Variance ' +\n      'Weekday When Within Year '\n  };\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '\\\\$[datastore|feature|layer|map|measure|sourcefeature|sourcelayer|targetfeature|targetlayer|value|view]+'\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(0[bB][01]+)'\n      },\n      {\n        begin: '\\\\b(0[oO][0-7]+)'\n      },\n      {\n        begin: hljs.C_NUMBER_RE\n      }\n    ],\n    relevance: 0\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS,\n    contains: [] // defined later\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  SUBST.contains = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    TEMPLATE_STRING,\n    NUMBER,\n    hljs.REGEXP_MODE\n  ];\n  const PARAMS_CONTAINS = SUBST.contains.concat([\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.C_LINE_COMMENT_MODE\n  ]);\n\n  return {\n    name: 'ArcGIS Arcade',\n    keywords: KEYWORDS,\n    contains: [\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      TEMPLATE_STRING,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      SYMBOL,\n      NUMBER,\n      { // object attr container\n        begin: /[{,]\\s*/,\n        relevance: 0,\n        contains: [{\n          begin: IDENT_RE + '\\\\s*:',\n          returnBegin: true,\n          relevance: 0,\n          contains: [{\n            className: 'attr',\n            begin: IDENT_RE,\n            relevance: 0\n          }]\n        }]\n      },\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(return)\\\\b)\\\\s*',\n        keywords: 'return',\n        contains: [\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            begin: '(\\\\(.*?\\\\)|' + IDENT_RE + ')\\\\s*=>',\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [{\n              className: 'params',\n              variants: [\n                {\n                  begin: IDENT_RE\n                },\n                {\n                  begin: /\\(\\s*\\)/\n                },\n                {\n                  begin: /\\(/,\n                  end: /\\)/,\n                  excludeBegin: true,\n                  excludeEnd: true,\n                  keywords: KEYWORDS,\n                  contains: PARAMS_CONTAINS\n                }\n              ]\n            }]\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: IDENT_RE\n          }),\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            contains: PARAMS_CONTAINS\n          }\n        ],\n        illegal: /\\[|%/\n      },\n      {\n        begin: /\\$[(.]/\n      }\n    ],\n    illegal: /#(?!!)/\n  };\n}\n\nmodule.exports = arcade;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,QAAQ,GAAG,wBAAwB;EACzC,MAAMC,QAAQ,GAAG;IACfC,OAAO,EACL,yDAAyD;IAC3DC,OAAO,EACL,qHAAqH;IACvHC,QAAQ,EACN,6GAA6G,GAC7G,mFAAmF,GACnF,qGAAqG,GACrG,2HAA2H,GAC3H,kIAAkI,GAClI,2HAA2H,GAC3H,+FAA+F,GAC/F,6GAA6G,GAC7G,0FAA0F,GAC1F,4FAA4F,GAC5F;EACJ,CAAC;EACD,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMC,MAAM,GAAG;IACbF,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAER,IAAI,CAACW;IACd,CAAC,CACF;IACDC,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,KAAK,GAAG;IACZN,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,QAAQ;IACfM,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAEb,QAAQ;IAClBc,QAAQ,EAAE,EAAE,CAAC;EACf,CAAC;EACD,MAAMC,eAAe,GAAG;IACtBV,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVM,GAAG,EAAE,GAAG;IACRE,QAAQ,EAAE,CACRhB,IAAI,CAACkB,gBAAgB,EACrBL,KAAK;EAET,CAAC;EACDA,KAAK,CAACG,QAAQ,GAAG,CACfhB,IAAI,CAACmB,gBAAgB,EACrBnB,IAAI,CAACoB,iBAAiB,EACtBH,eAAe,EACfR,MAAM,EACNT,IAAI,CAACqB,WAAW,CACjB;EACD,MAAMC,eAAe,GAAGT,KAAK,CAACG,QAAQ,CAACO,MAAM,CAAC,CAC5CvB,IAAI,CAACwB,oBAAoB,EACzBxB,IAAI,CAACyB,mBAAmB,CACzB,CAAC;EAEF,OAAO;IACLC,IAAI,EAAE,eAAe;IACrBX,QAAQ,EAAEb,QAAQ;IAClBc,QAAQ,EAAE,CACRhB,IAAI,CAACmB,gBAAgB,EACrBnB,IAAI,CAACoB,iBAAiB,EACtBH,eAAe,EACfjB,IAAI,CAACyB,mBAAmB,EACxBzB,IAAI,CAACwB,oBAAoB,EACzBlB,MAAM,EACNG,MAAM,EACN;MAAE;MACAD,KAAK,EAAE,SAAS;MAChBI,SAAS,EAAE,CAAC;MACZI,QAAQ,EAAE,CAAC;QACTR,KAAK,EAAEP,QAAQ,GAAG,OAAO;QACzB0B,WAAW,EAAE,IAAI;QACjBf,SAAS,EAAE,CAAC;QACZI,QAAQ,EAAE,CAAC;UACTT,SAAS,EAAE,MAAM;UACjBC,KAAK,EAAEP,QAAQ;UACfW,SAAS,EAAE;QACb,CAAC;MACH,CAAC;IACH,CAAC,EACD;MAAE;MACAJ,KAAK,EAAE,GAAG,GAAGR,IAAI,CAAC4B,cAAc,GAAG,sBAAsB;MACzDb,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,CACRhB,IAAI,CAACyB,mBAAmB,EACxBzB,IAAI,CAACwB,oBAAoB,EACzBxB,IAAI,CAACqB,WAAW,EAChB;QACEd,SAAS,EAAE,UAAU;QACrBC,KAAK,EAAE,aAAa,GAAGP,QAAQ,GAAG,SAAS;QAC3C0B,WAAW,EAAE,IAAI;QACjBb,GAAG,EAAE,QAAQ;QACbE,QAAQ,EAAE,CAAC;UACTT,SAAS,EAAE,QAAQ;UACnBG,QAAQ,EAAE,CACR;YACEF,KAAK,EAAEP;UACT,CAAC,EACD;YACEO,KAAK,EAAE;UACT,CAAC,EACD;YACEA,KAAK,EAAE,IAAI;YACXM,GAAG,EAAE,IAAI;YACTe,YAAY,EAAE,IAAI;YAClBC,UAAU,EAAE,IAAI;YAChBf,QAAQ,EAAEb,QAAQ;YAClBc,QAAQ,EAAEM;UACZ,CAAC;QAEL,CAAC;MACH,CAAC,CACF;MACDV,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,UAAU;MACrBwB,aAAa,EAAE,UAAU;MACzBjB,GAAG,EAAE,IAAI;MACTgB,UAAU,EAAE,IAAI;MAChBd,QAAQ,EAAE,CACRhB,IAAI,CAACgC,OAAO,CAAChC,IAAI,CAACiC,UAAU,EAAE;QAC5BzB,KAAK,EAAEP;MACT,CAAC,CAAC,EACF;QACEM,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,IAAI;QACXM,GAAG,EAAE,IAAI;QACTe,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBd,QAAQ,EAAEM;MACZ,CAAC,CACF;MACDY,OAAO,EAAE;IACX,CAAC,EACD;MACE1B,KAAK,EAAE;IACT,CAAC,CACF;IACD0B,OAAO,EAAE;EACX,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGrC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}