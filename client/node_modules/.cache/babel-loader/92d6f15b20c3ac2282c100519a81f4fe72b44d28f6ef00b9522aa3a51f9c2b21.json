{"ast": null, "code": "/*\nLanguage: Leaf\nAuthor: <PERSON> <<EMAIL>>\nDescription: Based on the Leaf reference from https://vapor.github.io/documentation/guide/leaf.html.\n*/\n\nfunction leaf(hljs) {\n  return {\n    name: 'Leaf',\n    contains: [{\n      className: 'function',\n      begin: '#+' + '[A-Za-z_0-9]*' + '\\\\(',\n      end: / \\{/,\n      returnBegin: true,\n      excludeEnd: true,\n      contains: [{\n        className: 'keyword',\n        begin: '#+'\n      }, {\n        className: 'title',\n        begin: '[A-Za-z_][A-Za-z_0-9]*'\n      }, {\n        className: 'params',\n        begin: '\\\\(',\n        end: '\\\\)',\n        endsParent: true,\n        contains: [{\n          className: 'string',\n          begin: '\"',\n          end: '\"'\n        }, {\n          className: 'variable',\n          begin: '[A-Za-z_][A-Za-z_0-9]*'\n        }]\n      }]\n    }]\n  };\n}\nmodule.exports = leaf;", "map": {"version": 3, "names": ["leaf", "hljs", "name", "contains", "className", "begin", "end", "returnBegin", "excludeEnd", "endsParent", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/leaf.js"], "sourcesContent": ["/*\nLanguage: Leaf\nAuthor: <PERSON> <<EMAIL>>\nDescription: Based on the Leaf reference from https://vapor.github.io/documentation/guide/leaf.html.\n*/\n\nfunction leaf(hljs) {\n  return {\n    name: 'Leaf',\n    contains: [\n      {\n        className: 'function',\n        begin: '#+' + '[A-Za-z_0-9]*' + '\\\\(',\n        end: / \\{/,\n        returnBegin: true,\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'keyword',\n            begin: '#+'\n          },\n          {\n            className: 'title',\n            begin: '[A-Za-z_][A-Za-z_0-9]*'\n          },\n          {\n            className: 'params',\n            begin: '\\\\(',\n            end: '\\\\)',\n            endsParent: true,\n            contains: [\n              {\n                className: 'string',\n                begin: '\"',\n                end: '\"'\n              },\n              {\n                className: 'variable',\n                begin: '[A-Za-z_][A-Za-z_0-9]*'\n              }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = leaf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,IAAI,GAAG,eAAe,GAAG,KAAK;MACrCC,GAAG,EAAE,KAAK;MACVC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,IAAI;MAChBL,QAAQ,EAAE,CACR;QACEC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,SAAS,EAAE,OAAO;QAClBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,KAAK;QACZC,GAAG,EAAE,KAAK;QACVG,UAAU,EAAE,IAAI;QAChBN,QAAQ,EAAE,CACR;UACEC,SAAS,EAAE,QAAQ;UACnBC,KAAK,EAAE,GAAG;UACVC,GAAG,EAAE;QACP,CAAC,EACD;UACEF,SAAS,EAAE,UAAU;UACrBC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGX,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}