{"ast": null, "code": "'use strict';\n\nmodule.exports = xojo;\nxojo.displayName = 'xojo';\nxojo.aliases = [];\nfunction xojo(Prism) {\n  Prism.languages.xojo = {\n    comment: {\n      pattern: /(?:'|\\/\\/|Rem\\b).+/i,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    number: [/(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i, /&[bchou][a-z\\d]+/i],\n    directive: {\n      pattern: /#(?:Else|ElseIf|Endif|If|Pragma)\\b/i,\n      alias: 'property'\n    },\n    keyword: /\\b(?:AddHandler|App|Array|As(?:signs)?|Auto|Boolean|Break|By(?:Ref|Val)|Byte|Call|Case|Catch|CFStringRef|CGFloat|Class|Color|Const|Continue|CString|Currency|CurrentMethodName|Declare|Delegate|Dim|Do(?:uble|wnTo)?|Each|Else(?:If)?|End|Enumeration|Event|Exception|Exit|Extends|False|Finally|For|Function|Get|GetTypeInfo|Global|GOTO|If|Implements|In|Inherits|Int(?:8|16|32|64|eger|erface)?|Lib|Loop|Me|Module|Next|Nil|Object|Optional|OSType|ParamArray|Private|Property|Protected|PString|Ptr|Raise(?:Event)?|ReDim|RemoveHandler|Return|Select(?:or)?|Self|Set|Shared|Short|Single|Soft|Static|Step|String|Sub|Super|Text|Then|To|True|Try|Ubound|UInt(?:8|16|32|64|eger)?|Until|Using|Var(?:iant)?|Wend|While|WindowPtr|WString)\\b/i,\n    operator: /<[=>]?|>=?|[+\\-*\\/\\\\^=]|\\b(?:AddressOf|And|Ctype|IsA?|Mod|New|Not|Or|WeakAddressOf|Xor)\\b/i,\n    punctuation: /[.,;:()]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "xojo", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "number", "directive", "alias", "keyword", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/xojo.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = xojo\nxojo.displayName = 'xojo'\nxojo.aliases = []\nfunction xojo(Prism) {\n  Prism.languages.xojo = {\n    comment: {\n      pattern: /(?:'|\\/\\/|Rem\\b).+/i,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    number: [/(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i, /&[bchou][a-z\\d]+/i],\n    directive: {\n      pattern: /#(?:Else|ElseIf|Endif|If|Pragma)\\b/i,\n      alias: 'property'\n    },\n    keyword:\n      /\\b(?:AddHandler|App|Array|As(?:signs)?|Auto|Boolean|Break|By(?:Ref|Val)|Byte|Call|Case|Catch|CFStringRef|CGFloat|Class|Color|Const|Continue|CString|Currency|CurrentMethodName|Declare|Delegate|Dim|Do(?:uble|wnTo)?|Each|Else(?:If)?|End|Enumeration|Event|Exception|Exit|Extends|False|Finally|For|Function|Get|GetTypeInfo|Global|GOTO|If|Implements|In|Inherits|Int(?:8|16|32|64|eger|erface)?|Lib|Loop|Me|Module|Next|Nil|Object|Optional|OSType|ParamArray|Private|Property|Protected|PString|Ptr|Raise(?:Event)?|ReDim|RemoveHandler|Return|Select(?:or)?|Self|Set|Shared|Short|Single|Soft|Static|Step|String|Sub|Super|Text|Then|To|True|Try|Ubound|UInt(?:8|16|32|64|eger)?|Until|Using|Var(?:iant)?|Wend|While|WindowPtr|WString)\\b/i,\n    operator:\n      /<[=>]?|>=?|[+\\-*\\/\\\\^=]|\\b(?:AddressOf|And|Ctype|IsA?|Mod|New|Not|Or|WeakAddressOf|Xor)\\b/i,\n    punctuation: /[.,;:()]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,OAAO,EAAE;MACPC,OAAO,EAAE,qBAAqB;MAC9BC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE;IACV,CAAC;IACDE,MAAM,EAAE,CAAC,4CAA4C,EAAE,mBAAmB,CAAC;IAC3EC,SAAS,EAAE;MACTJ,OAAO,EAAE,qCAAqC;MAC9CK,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EACL,itBAAitB;IACntBC,QAAQ,EACN,4FAA4F;IAC9FC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}