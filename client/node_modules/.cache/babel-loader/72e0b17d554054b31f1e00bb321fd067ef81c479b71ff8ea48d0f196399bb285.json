{"ast": null, "code": "/*\nLanguage: Nim\nDescription: Nim is a statically typed compiled systems programming language.\nWebsite: https://nim-lang.org\nCategory: system\n*/\n\nfunction nim(hljs) {\n  return {\n    name: 'Nim',\n    keywords: {\n      keyword: 'addr and as asm bind block break case cast const continue converter ' + 'discard distinct div do elif else end enum except export finally ' + 'for from func generic if import in include interface is isnot iterator ' + 'let macro method mixin mod nil not notin object of or out proc ptr ' + 'raise ref return shl shr static template try tuple type using var ' + 'when while with without xor yield',\n      literal: 'shared guarded stdin stdout stderr result true false',\n      built_in: 'int int8 int16 int32 int64 uint uint8 uint16 uint32 uint64 float ' + 'float32 float64 bool char string cstring pointer expr stmt void ' + 'auto any range array openarray varargs seq set clong culong cchar ' + 'cschar cshort cint csize clonglong cfloat cdouble clongdouble ' + 'cuchar cushort cuint culonglong cstringarray semistatic'\n    },\n    contains: [{\n      className: 'meta',\n      // Actually pragma\n      begin: /\\{\\./,\n      end: /\\.\\}/,\n      relevance: 10\n    }, {\n      className: 'string',\n      begin: /[a-zA-Z]\\w*\"/,\n      end: /\"/,\n      contains: [{\n        begin: /\"\"/\n      }]\n    }, {\n      className: 'string',\n      begin: /([a-zA-Z]\\w*)?\"\"\"/,\n      end: /\"\"\"/\n    }, hljs.QUOTE_STRING_MODE, {\n      className: 'type',\n      begin: /\\b[A-Z]\\w+\\b/,\n      relevance: 0\n    }, {\n      className: 'number',\n      relevance: 0,\n      variants: [{\n        begin: /\\b(0[xX][0-9a-fA-F][_0-9a-fA-F]*)('?[iIuU](8|16|32|64))?/\n      }, {\n        begin: /\\b(0o[0-7][_0-7]*)('?[iIuUfF](8|16|32|64))?/\n      }, {\n        begin: /\\b(0(b|B)[01][_01]*)('?[iIuUfF](8|16|32|64))?/\n      }, {\n        begin: /\\b(\\d[_\\d]*)('?[iIuUfF](8|16|32|64))?/\n      }]\n    }, hljs.HASH_COMMENT_MODE]\n  };\n}\nmodule.exports = nim;", "map": {"version": 3, "names": ["nim", "hljs", "name", "keywords", "keyword", "literal", "built_in", "contains", "className", "begin", "end", "relevance", "QUOTE_STRING_MODE", "variants", "HASH_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/nim.js"], "sourcesContent": ["/*\nLanguage: Nim\nDescription: Nim is a statically typed compiled systems programming language.\nWebsite: https://nim-lang.org\nCategory: system\n*/\n\nfunction nim(hljs) {\n  return {\n    name: 'Nim',\n    keywords: {\n      keyword:\n        'addr and as asm bind block break case cast const continue converter ' +\n        'discard distinct div do elif else end enum except export finally ' +\n        'for from func generic if import in include interface is isnot iterator ' +\n        'let macro method mixin mod nil not notin object of or out proc ptr ' +\n        'raise ref return shl shr static template try tuple type using var ' +\n        'when while with without xor yield',\n      literal:\n        'shared guarded stdin stdout stderr result true false',\n      built_in:\n        'int int8 int16 int32 int64 uint uint8 uint16 uint32 uint64 float ' +\n        'float32 float64 bool char string cstring pointer expr stmt void ' +\n        'auto any range array openarray varargs seq set clong culong cchar ' +\n        'cschar cshort cint csize clonglong cfloat cdouble clongdouble ' +\n        'cuchar cushort cuint culonglong cstringarray semistatic'\n    },\n    contains: [\n      {\n        className: 'meta', // Actually pragma\n        begin: /\\{\\./,\n        end: /\\.\\}/,\n        relevance: 10\n      },\n      {\n        className: 'string',\n        begin: /[a-zA-Z]\\w*\"/,\n        end: /\"/,\n        contains: [\n          {\n            begin: /\"\"/\n          }\n        ]\n      },\n      {\n        className: 'string',\n        begin: /([a-zA-Z]\\w*)?\"\"\"/,\n        end: /\"\"\"/\n      },\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'type',\n        begin: /\\b[A-Z]\\w+\\b/,\n        relevance: 0\n      },\n      {\n        className: 'number',\n        relevance: 0,\n        variants: [\n          {\n            begin: /\\b(0[xX][0-9a-fA-F][_0-9a-fA-F]*)('?[iIuU](8|16|32|64))?/\n          },\n          {\n            begin: /\\b(0o[0-7][_0-7]*)('?[iIuUfF](8|16|32|64))?/\n          },\n          {\n            begin: /\\b(0(b|B)[01][_01]*)('?[iIuUfF](8|16|32|64))?/\n          },\n          {\n            begin: /\\b(\\d[_\\d]*)('?[iIuUfF](8|16|32|64))?/\n          }\n        ]\n      },\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = nim;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MACRC,OAAO,EACL,sEAAsE,GACtE,mEAAmE,GACnE,yEAAyE,GACzE,qEAAqE,GACrE,oEAAoE,GACpE,mCAAmC;MACrCC,OAAO,EACL,sDAAsD;MACxDC,QAAQ,EACN,mEAAmE,GACnE,kEAAkE,GAClE,oEAAoE,GACpE,gEAAgE,GAChE;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MAAE;MACnBC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,MAAM;MACXC,SAAS,EAAE;IACb,CAAC,EACD;MACEH,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,cAAc;MACrBC,GAAG,EAAE,GAAG;MACRH,QAAQ,EAAE,CACR;QACEE,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACD;MACED,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE;IACP,CAAC,EACDT,IAAI,CAACW,iBAAiB,EACtB;MACEJ,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,cAAc;MACrBE,SAAS,EAAE;IACb,CAAC,EACD;MACEH,SAAS,EAAE,QAAQ;MACnBG,SAAS,EAAE,CAAC;MACZE,QAAQ,EAAE,CACR;QACEJ,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACDR,IAAI,CAACa,iBAAiB;EAE1B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGhB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}