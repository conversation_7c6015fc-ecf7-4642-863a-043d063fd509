{"ast": null, "code": "'use strict';\n\nmodule.exports = jsx;\njsx.displayName = 'jsx';\njsx.aliases = [];\nfunction jsx(Prism) {\n  ;\n  (function (Prism) {\n    var javascript = Prism.util.clone(Prism.languages.javascript);\n    var space = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source;\n    var braces = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source;\n    var spread = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source;\n    /**\n     * @param {string} source\n     * @param {string} [flags]\n     */\n    function re(source, flags) {\n      source = source.replace(/<S>/g, function () {\n        return space;\n      }).replace(/<BRACES>/g, function () {\n        return braces;\n      }).replace(/<SPREAD>/g, function () {\n        return spread;\n      });\n      return RegExp(source, flags);\n    }\n    spread = re(spread).source;\n    Prism.languages.jsx = Prism.languages.extend('markup', javascript);\n    Prism.languages.jsx.tag.pattern = re(/<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/.source);\n    Prism.languages.jsx.tag.inside['tag'].pattern = /^<\\/?[^\\s>\\/]*/;\n    Prism.languages.jsx.tag.inside['attr-value'].pattern = /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/;\n    Prism.languages.jsx.tag.inside['tag'].inside['class-name'] = /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/;\n    Prism.languages.jsx.tag.inside['comment'] = javascript['comment'];\n    Prism.languages.insertBefore('inside', 'attr-name', {\n      spread: {\n        pattern: re(/<SPREAD>/.source),\n        inside: Prism.languages.jsx\n      }\n    }, Prism.languages.jsx.tag);\n    Prism.languages.insertBefore('inside', 'special-attr', {\n      script: {\n        // Allow for two levels of nesting\n        pattern: re(/=<BRACES>/.source),\n        alias: 'language-javascript',\n        inside: {\n          'script-punctuation': {\n            pattern: /^=(?=\\{)/,\n            alias: 'punctuation'\n          },\n          rest: Prism.languages.jsx\n        }\n      }\n    }, Prism.languages.jsx.tag); // The following will handle plain text inside tags\n    var stringifyToken = function (token) {\n      if (!token) {\n        return '';\n      }\n      if (typeof token === 'string') {\n        return token;\n      }\n      if (typeof token.content === 'string') {\n        return token.content;\n      }\n      return token.content.map(stringifyToken).join('');\n    };\n    var walkTokens = function (tokens) {\n      var openedTags = [];\n      for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i];\n        var notTagNorBrace = false;\n        if (typeof token !== 'string') {\n          if (token.type === 'tag' && token.content[0] && token.content[0].type === 'tag') {\n            // We found a tag, now find its kind\n            if (token.content[0].content[0].content === '</') {\n              // Closing tag\n              if (openedTags.length > 0 && openedTags[openedTags.length - 1].tagName === stringifyToken(token.content[0].content[1])) {\n                // Pop matching opening tag\n                openedTags.pop();\n              }\n            } else {\n              if (token.content[token.content.length - 1].content === '/>') {\n                // Autoclosed tag, ignore\n              } else {\n                // Opening tag\n                openedTags.push({\n                  tagName: stringifyToken(token.content[0].content[1]),\n                  openedBraces: 0\n                });\n              }\n            }\n          } else if (openedTags.length > 0 && token.type === 'punctuation' && token.content === '{') {\n            // Here we might have entered a JSX context inside a tag\n            openedTags[openedTags.length - 1].openedBraces++;\n          } else if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces > 0 && token.type === 'punctuation' && token.content === '}') {\n            // Here we might have left a JSX context inside a tag\n            openedTags[openedTags.length - 1].openedBraces--;\n          } else {\n            notTagNorBrace = true;\n          }\n        }\n        if (notTagNorBrace || typeof token === 'string') {\n          if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces === 0) {\n            // Here we are inside a tag, and not inside a JSX context.\n            // That's plain text: drop any tokens matched.\n            var plainText = stringifyToken(token); // And merge text with adjacent text\n            if (i < tokens.length - 1 && (typeof tokens[i + 1] === 'string' || tokens[i + 1].type === 'plain-text')) {\n              plainText += stringifyToken(tokens[i + 1]);\n              tokens.splice(i + 1, 1);\n            }\n            if (i > 0 && (typeof tokens[i - 1] === 'string' || tokens[i - 1].type === 'plain-text')) {\n              plainText = stringifyToken(tokens[i - 1]) + plainText;\n              tokens.splice(i - 1, 1);\n              i--;\n            }\n            tokens[i] = new Prism.Token('plain-text', plainText, null, plainText);\n          }\n        }\n        if (token.content && typeof token.content !== 'string') {\n          walkTokens(token.content);\n        }\n      }\n    };\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'jsx' && env.language !== 'tsx') {\n        return;\n      }\n      walkTokens(env.tokens);\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "jsx", "displayName", "aliases", "Prism", "javascript", "util", "clone", "languages", "space", "source", "braces", "spread", "re", "flags", "replace", "RegExp", "extend", "tag", "pattern", "inside", "insertBefore", "script", "alias", "rest", "stringifyToken", "token", "content", "map", "join", "walkTokens", "tokens", "openedTags", "i", "length", "notTagNorBrace", "type", "tagName", "pop", "push", "openedBraces", "plainText", "splice", "Token", "hooks", "add", "env", "language"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/jsx.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsx\njsx.displayName = 'jsx'\njsx.aliases = []\nfunction jsx(Prism) {\n  ;(function (Prism) {\n    var javascript = Prism.util.clone(Prism.languages.javascript)\n    var space = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source\n    var braces = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source\n    var spread = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source\n    /**\n     * @param {string} source\n     * @param {string} [flags]\n     */\n    function re(source, flags) {\n      source = source\n        .replace(/<S>/g, function () {\n          return space\n        })\n        .replace(/<BRACES>/g, function () {\n          return braces\n        })\n        .replace(/<SPREAD>/g, function () {\n          return spread\n        })\n      return RegExp(source, flags)\n    }\n    spread = re(spread).source\n    Prism.languages.jsx = Prism.languages.extend('markup', javascript)\n    Prism.languages.jsx.tag.pattern = re(\n      /<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/\n        .source\n    )\n    Prism.languages.jsx.tag.inside['tag'].pattern = /^<\\/?[^\\s>\\/]*/\n    Prism.languages.jsx.tag.inside['attr-value'].pattern =\n      /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/\n    Prism.languages.jsx.tag.inside['tag'].inside['class-name'] =\n      /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/\n    Prism.languages.jsx.tag.inside['comment'] = javascript['comment']\n    Prism.languages.insertBefore(\n      'inside',\n      'attr-name',\n      {\n        spread: {\n          pattern: re(/<SPREAD>/.source),\n          inside: Prism.languages.jsx\n        }\n      },\n      Prism.languages.jsx.tag\n    )\n    Prism.languages.insertBefore(\n      'inside',\n      'special-attr',\n      {\n        script: {\n          // Allow for two levels of nesting\n          pattern: re(/=<BRACES>/.source),\n          alias: 'language-javascript',\n          inside: {\n            'script-punctuation': {\n              pattern: /^=(?=\\{)/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.jsx\n          }\n        }\n      },\n      Prism.languages.jsx.tag\n    ) // The following will handle plain text inside tags\n    var stringifyToken = function (token) {\n      if (!token) {\n        return ''\n      }\n      if (typeof token === 'string') {\n        return token\n      }\n      if (typeof token.content === 'string') {\n        return token.content\n      }\n      return token.content.map(stringifyToken).join('')\n    }\n    var walkTokens = function (tokens) {\n      var openedTags = []\n      for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i]\n        var notTagNorBrace = false\n        if (typeof token !== 'string') {\n          if (\n            token.type === 'tag' &&\n            token.content[0] &&\n            token.content[0].type === 'tag'\n          ) {\n            // We found a tag, now find its kind\n            if (token.content[0].content[0].content === '</') {\n              // Closing tag\n              if (\n                openedTags.length > 0 &&\n                openedTags[openedTags.length - 1].tagName ===\n                  stringifyToken(token.content[0].content[1])\n              ) {\n                // Pop matching opening tag\n                openedTags.pop()\n              }\n            } else {\n              if (token.content[token.content.length - 1].content === '/>') {\n                // Autoclosed tag, ignore\n              } else {\n                // Opening tag\n                openedTags.push({\n                  tagName: stringifyToken(token.content[0].content[1]),\n                  openedBraces: 0\n                })\n              }\n            }\n          } else if (\n            openedTags.length > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '{'\n          ) {\n            // Here we might have entered a JSX context inside a tag\n            openedTags[openedTags.length - 1].openedBraces++\n          } else if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '}'\n          ) {\n            // Here we might have left a JSX context inside a tag\n            openedTags[openedTags.length - 1].openedBraces--\n          } else {\n            notTagNorBrace = true\n          }\n        }\n        if (notTagNorBrace || typeof token === 'string') {\n          if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces === 0\n          ) {\n            // Here we are inside a tag, and not inside a JSX context.\n            // That's plain text: drop any tokens matched.\n            var plainText = stringifyToken(token) // And merge text with adjacent text\n            if (\n              i < tokens.length - 1 &&\n              (typeof tokens[i + 1] === 'string' ||\n                tokens[i + 1].type === 'plain-text')\n            ) {\n              plainText += stringifyToken(tokens[i + 1])\n              tokens.splice(i + 1, 1)\n            }\n            if (\n              i > 0 &&\n              (typeof tokens[i - 1] === 'string' ||\n                tokens[i - 1].type === 'plain-text')\n            ) {\n              plainText = stringifyToken(tokens[i - 1]) + plainText\n              tokens.splice(i - 1, 1)\n              i--\n            }\n            tokens[i] = new Prism.Token(\n              'plain-text',\n              plainText,\n              null,\n              plainText\n            )\n          }\n        }\n        if (token.content && typeof token.content !== 'string') {\n          walkTokens(token.content)\n        }\n      }\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'jsx' && env.language !== 'tsx') {\n        return\n      }\n      walkTokens(env.tokens)\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,UAAU,GAAGD,KAAK,CAACE,IAAI,CAACC,KAAK,CAACH,KAAK,CAACI,SAAS,CAACH,UAAU,CAAC;IAC7D,IAAII,KAAK,GAAG,8CAA8C,CAACC,MAAM;IACjE,IAAIC,MAAM,GAAG,8CAA8C,CAACD,MAAM;IAClE,IAAIE,MAAM,GAAG,sCAAsC,CAACF,MAAM;IAC1D;AACJ;AACA;AACA;IACI,SAASG,EAAEA,CAACH,MAAM,EAAEI,KAAK,EAAE;MACzBJ,MAAM,GAAGA,MAAM,CACZK,OAAO,CAAC,MAAM,EAAE,YAAY;QAC3B,OAAON,KAAK;MACd,CAAC,CAAC,CACDM,OAAO,CAAC,WAAW,EAAE,YAAY;QAChC,OAAOJ,MAAM;MACf,CAAC,CAAC,CACDI,OAAO,CAAC,WAAW,EAAE,YAAY;QAChC,OAAOH,MAAM;MACf,CAAC,CAAC;MACJ,OAAOI,MAAM,CAACN,MAAM,EAAEI,KAAK,CAAC;IAC9B;IACAF,MAAM,GAAGC,EAAE,CAACD,MAAM,CAAC,CAACF,MAAM;IAC1BN,KAAK,CAACI,SAAS,CAACP,GAAG,GAAGG,KAAK,CAACI,SAAS,CAACS,MAAM,CAAC,QAAQ,EAAEZ,UAAU,CAAC;IAClED,KAAK,CAACI,SAAS,CAACP,GAAG,CAACiB,GAAG,CAACC,OAAO,GAAGN,EAAE,CAClC,uIAAuI,CACpIH,MACL,CAAC;IACDN,KAAK,CAACI,SAAS,CAACP,GAAG,CAACiB,GAAG,CAACE,MAAM,CAAC,KAAK,CAAC,CAACD,OAAO,GAAG,gBAAgB;IAChEf,KAAK,CAACI,SAAS,CAACP,GAAG,CAACiB,GAAG,CAACE,MAAM,CAAC,YAAY,CAAC,CAACD,OAAO,GAClD,oEAAoE;IACtEf,KAAK,CAACI,SAAS,CAACP,GAAG,CAACiB,GAAG,CAACE,MAAM,CAAC,KAAK,CAAC,CAACA,MAAM,CAAC,YAAY,CAAC,GACxD,2BAA2B;IAC7BhB,KAAK,CAACI,SAAS,CAACP,GAAG,CAACiB,GAAG,CAACE,MAAM,CAAC,SAAS,CAAC,GAAGf,UAAU,CAAC,SAAS,CAAC;IACjED,KAAK,CAACI,SAAS,CAACa,YAAY,CAC1B,QAAQ,EACR,WAAW,EACX;MACET,MAAM,EAAE;QACNO,OAAO,EAAEN,EAAE,CAAC,UAAU,CAACH,MAAM,CAAC;QAC9BU,MAAM,EAAEhB,KAAK,CAACI,SAAS,CAACP;MAC1B;IACF,CAAC,EACDG,KAAK,CAACI,SAAS,CAACP,GAAG,CAACiB,GACtB,CAAC;IACDd,KAAK,CAACI,SAAS,CAACa,YAAY,CAC1B,QAAQ,EACR,cAAc,EACd;MACEC,MAAM,EAAE;QACN;QACAH,OAAO,EAAEN,EAAE,CAAC,WAAW,CAACH,MAAM,CAAC;QAC/Ba,KAAK,EAAE,qBAAqB;QAC5BH,MAAM,EAAE;UACN,oBAAoB,EAAE;YACpBD,OAAO,EAAE,UAAU;YACnBI,KAAK,EAAE;UACT,CAAC;UACDC,IAAI,EAAEpB,KAAK,CAACI,SAAS,CAACP;QACxB;MACF;IACF,CAAC,EACDG,KAAK,CAACI,SAAS,CAACP,GAAG,CAACiB,GACtB,CAAC,EAAC;IACF,IAAIO,cAAc,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACpC,IAAI,CAACA,KAAK,EAAE;QACV,OAAO,EAAE;MACX;MACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOA,KAAK;MACd;MACA,IAAI,OAAOA,KAAK,CAACC,OAAO,KAAK,QAAQ,EAAE;QACrC,OAAOD,KAAK,CAACC,OAAO;MACtB;MACA,OAAOD,KAAK,CAACC,OAAO,CAACC,GAAG,CAACH,cAAc,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;IACnD,CAAC;IACD,IAAIC,UAAU,GAAG,SAAAA,CAAUC,MAAM,EAAE;MACjC,IAAIC,UAAU,GAAG,EAAE;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIP,KAAK,GAAGK,MAAM,CAACE,CAAC,CAAC;QACrB,IAAIE,cAAc,GAAG,KAAK;QAC1B,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;UAC7B,IACEA,KAAK,CAACU,IAAI,KAAK,KAAK,IACpBV,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,IAChBD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACS,IAAI,KAAK,KAAK,EAC/B;YACA;YACA,IAAIV,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,KAAK,IAAI,EAAE;cAChD;cACA,IACEK,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBF,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACG,OAAO,KACvCZ,cAAc,CAACC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC,CAAC,EAC7C;gBACA;gBACAK,UAAU,CAACM,GAAG,CAAC,CAAC;cAClB;YACF,CAAC,MAAM;cACL,IAAIZ,KAAK,CAACC,OAAO,CAACD,KAAK,CAACC,OAAO,CAACO,MAAM,GAAG,CAAC,CAAC,CAACP,OAAO,KAAK,IAAI,EAAE;gBAC5D;cAAA,CACD,MAAM;gBACL;gBACAK,UAAU,CAACO,IAAI,CAAC;kBACdF,OAAO,EAAEZ,cAAc,CAACC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC,CAAC,CAAC;kBACpDa,YAAY,EAAE;gBAChB,CAAC,CAAC;cACJ;YACF;UACF,CAAC,MAAM,IACLR,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBR,KAAK,CAACU,IAAI,KAAK,aAAa,IAC5BV,KAAK,CAACC,OAAO,KAAK,GAAG,EACrB;YACA;YACAK,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,EAAE;UAClD,CAAC,MAAM,IACLR,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBF,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,GAAG,CAAC,IAClDd,KAAK,CAACU,IAAI,KAAK,aAAa,IAC5BV,KAAK,CAACC,OAAO,KAAK,GAAG,EACrB;YACA;YACAK,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,EAAE;UAClD,CAAC,MAAM;YACLL,cAAc,GAAG,IAAI;UACvB;QACF;QACA,IAAIA,cAAc,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;UAC/C,IACEM,UAAU,CAACE,MAAM,GAAG,CAAC,IACrBF,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACM,YAAY,KAAK,CAAC,EACpD;YACA;YACA;YACA,IAAIC,SAAS,GAAGhB,cAAc,CAACC,KAAK,CAAC,EAAC;YACtC,IACEO,CAAC,GAAGF,MAAM,CAACG,MAAM,GAAG,CAAC,KACpB,OAAOH,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,IAChCF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,KAAK,YAAY,CAAC,EACtC;cACAK,SAAS,IAAIhB,cAAc,CAACM,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1CF,MAAM,CAACW,MAAM,CAACT,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACzB;YACA,IACEA,CAAC,GAAG,CAAC,KACJ,OAAOF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,IAChCF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,KAAK,YAAY,CAAC,EACtC;cACAK,SAAS,GAAGhB,cAAc,CAACM,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGQ,SAAS;cACrDV,MAAM,CAACW,MAAM,CAACT,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cACvBA,CAAC,EAAE;YACL;YACAF,MAAM,CAACE,CAAC,CAAC,GAAG,IAAI7B,KAAK,CAACuC,KAAK,CACzB,YAAY,EACZF,SAAS,EACT,IAAI,EACJA,SACF,CAAC;UACH;QACF;QACA,IAAIf,KAAK,CAACC,OAAO,IAAI,OAAOD,KAAK,CAACC,OAAO,KAAK,QAAQ,EAAE;UACtDG,UAAU,CAACJ,KAAK,CAACC,OAAO,CAAC;QAC3B;MACF;IACF,CAAC;IACDvB,KAAK,CAACwC,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/C,IAAIA,GAAG,CAACC,QAAQ,KAAK,KAAK,IAAID,GAAG,CAACC,QAAQ,KAAK,KAAK,EAAE;QACpD;MACF;MACAjB,UAAU,CAACgB,GAAG,CAACf,MAAM,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,EAAE3B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}