{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = ftl;\nftl.displayName = 'ftl';\nftl.aliases = [];\nfunction ftl(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    // https://freemarker.apache.org/docs/dgui_template_exp.html\n    // FTL expression with 4 levels of nesting supported\n    var FTL_EXPR = /[^<()\"']|\\((?:<expr>)*\\)|<(?!#--)|<#--(?:[^-]|-(?!->))*-->|\"(?:[^\\\\\"]|\\\\.)*\"|'(?:[^\\\\']|\\\\.)*'/.source;\n    for (var i = 0; i < 2; i++) {\n      FTL_EXPR = FTL_EXPR.replace(/<expr>/g, function () {\n        return FTL_EXPR;\n      });\n    }\n    FTL_EXPR = FTL_EXPR.replace(/<expr>/g, /[^\\s\\S]/.source);\n    var ftl = {\n      comment: /<#--[\\s\\S]*?-->/,\n      string: [{\n        // raw string\n        pattern: /\\br(\"|')(?:(?!\\1)[^\\\\]|\\\\.)*\\1/,\n        greedy: true\n      }, {\n        pattern: RegExp(/(\"|')(?:(?!\\1|\\$\\{)[^\\\\]|\\\\.|\\$\\{(?:(?!\\})(?:<expr>))*\\})*\\1/.source.replace(/<expr>/g, function () {\n          return FTL_EXPR;\n        })),\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: RegExp(/((?:^|[^\\\\])(?:\\\\\\\\)*)\\$\\{(?:(?!\\})(?:<expr>))*\\}/.source.replace(/<expr>/g, function () {\n              return FTL_EXPR;\n            })),\n            lookbehind: true,\n            inside: {\n              'interpolation-punctuation': {\n                pattern: /^\\$\\{|\\}$/,\n                alias: 'punctuation'\n              },\n              rest: null\n            }\n          }\n        }\n      }],\n      keyword: /\\b(?:as)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      'builtin-function': {\n        pattern: /((?:^|[^?])\\?\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      function: /\\b\\w+(?=\\s*\\()/,\n      number: /\\b\\d+(?:\\.\\d+)?\\b/,\n      operator: /\\.\\.[<*!]?|->|--|\\+\\+|&&|\\|\\||\\?{1,2}|[-+*/%!=<>]=?|\\b(?:gt|gte|lt|lte)\\b/,\n      punctuation: /[,;.:()[\\]{}]/\n    };\n    ftl.string[1].inside.interpolation.inside.rest = ftl;\n    Prism.languages.ftl = {\n      'ftl-comment': {\n        // the pattern is shortened to be more efficient\n        pattern: /^<#--[\\s\\S]*/,\n        alias: 'comment'\n      },\n      'ftl-directive': {\n        pattern: /^<[\\s\\S]+>$/,\n        inside: {\n          directive: {\n            pattern: /(^<\\/?)[#@][a-z]\\w*/i,\n            lookbehind: true,\n            alias: 'keyword'\n          },\n          punctuation: /^<\\/?|\\/?>$/,\n          content: {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            alias: 'ftl',\n            inside: ftl\n          }\n        }\n      },\n      'ftl-interpolation': {\n        pattern: /^\\$\\{[\\s\\S]*\\}$/,\n        inside: {\n          punctuation: /^\\$\\{|\\}$/,\n          content: {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            alias: 'ftl',\n            inside: ftl\n          }\n        }\n      }\n    };\n    Prism.hooks.add('before-tokenize', function (env) {\n      // eslint-disable-next-line regexp/no-useless-lazy\n      var pattern = RegExp(/<#--[\\s\\S]*?-->|<\\/?[#@][a-zA-Z](?:<expr>)*?>|\\$\\{(?:<expr>)*?\\}/.source.replace(/<expr>/g, function () {\n        return FTL_EXPR;\n      }), 'gi');\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'ftl', pattern);\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'ftl');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "ftl", "displayName", "aliases", "Prism", "register", "FTL_EXPR", "source", "i", "replace", "comment", "string", "pattern", "greedy", "RegExp", "inside", "interpolation", "lookbehind", "alias", "rest", "keyword", "boolean", "function", "number", "operator", "punctuation", "languages", "directive", "content", "hooks", "add", "env", "buildPlaceholders", "tokenizePlaceholders"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/ftl.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = ftl\nftl.displayName = 'ftl'\nftl.aliases = []\nfunction ftl(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    // https://freemarker.apache.org/docs/dgui_template_exp.html\n    // FTL expression with 4 levels of nesting supported\n    var FTL_EXPR =\n      /[^<()\"']|\\((?:<expr>)*\\)|<(?!#--)|<#--(?:[^-]|-(?!->))*-->|\"(?:[^\\\\\"]|\\\\.)*\"|'(?:[^\\\\']|\\\\.)*'/\n        .source\n    for (var i = 0; i < 2; i++) {\n      FTL_EXPR = FTL_EXPR.replace(/<expr>/g, function () {\n        return FTL_EXPR\n      })\n    }\n    FTL_EXPR = FTL_EXPR.replace(/<expr>/g, /[^\\s\\S]/.source)\n    var ftl = {\n      comment: /<#--[\\s\\S]*?-->/,\n      string: [\n        {\n          // raw string\n          pattern: /\\br(\"|')(?:(?!\\1)[^\\\\]|\\\\.)*\\1/,\n          greedy: true\n        },\n        {\n          pattern: RegExp(\n            /(\"|')(?:(?!\\1|\\$\\{)[^\\\\]|\\\\.|\\$\\{(?:(?!\\})(?:<expr>))*\\})*\\1/.source.replace(\n              /<expr>/g,\n              function () {\n                return FTL_EXPR\n              }\n            )\n          ),\n          greedy: true,\n          inside: {\n            interpolation: {\n              pattern: RegExp(\n                /((?:^|[^\\\\])(?:\\\\\\\\)*)\\$\\{(?:(?!\\})(?:<expr>))*\\}/.source.replace(\n                  /<expr>/g,\n                  function () {\n                    return FTL_EXPR\n                  }\n                )\n              ),\n              lookbehind: true,\n              inside: {\n                'interpolation-punctuation': {\n                  pattern: /^\\$\\{|\\}$/,\n                  alias: 'punctuation'\n                },\n                rest: null\n              }\n            }\n          }\n        }\n      ],\n      keyword: /\\b(?:as)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      'builtin-function': {\n        pattern: /((?:^|[^?])\\?\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      function: /\\b\\w+(?=\\s*\\()/,\n      number: /\\b\\d+(?:\\.\\d+)?\\b/,\n      operator:\n        /\\.\\.[<*!]?|->|--|\\+\\+|&&|\\|\\||\\?{1,2}|[-+*/%!=<>]=?|\\b(?:gt|gte|lt|lte)\\b/,\n      punctuation: /[,;.:()[\\]{}]/\n    }\n    ftl.string[1].inside.interpolation.inside.rest = ftl\n    Prism.languages.ftl = {\n      'ftl-comment': {\n        // the pattern is shortened to be more efficient\n        pattern: /^<#--[\\s\\S]*/,\n        alias: 'comment'\n      },\n      'ftl-directive': {\n        pattern: /^<[\\s\\S]+>$/,\n        inside: {\n          directive: {\n            pattern: /(^<\\/?)[#@][a-z]\\w*/i,\n            lookbehind: true,\n            alias: 'keyword'\n          },\n          punctuation: /^<\\/?|\\/?>$/,\n          content: {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            alias: 'ftl',\n            inside: ftl\n          }\n        }\n      },\n      'ftl-interpolation': {\n        pattern: /^\\$\\{[\\s\\S]*\\}$/,\n        inside: {\n          punctuation: /^\\$\\{|\\}$/,\n          content: {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            alias: 'ftl',\n            inside: ftl\n          }\n        }\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      // eslint-disable-next-line regexp/no-useless-lazy\n      var pattern = RegExp(\n        /<#--[\\s\\S]*?-->|<\\/?[#@][a-zA-Z](?:<expr>)*?>|\\$\\{(?:<expr>)*?\\}/.source.replace(\n          /<expr>/g,\n          function () {\n            return FTL_EXPR\n          }\n        ),\n        'gi'\n      )\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'ftl',\n        pattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'ftl')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACxC,CAAC,UAAUO,KAAK,EAAE;IACjB;IACA;IACA,IAAIE,QAAQ,GACV,gGAAgG,CAC7FC,MAAM;IACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BF,QAAQ,GAAGA,QAAQ,CAACG,OAAO,CAAC,SAAS,EAAE,YAAY;QACjD,OAAOH,QAAQ;MACjB,CAAC,CAAC;IACJ;IACAA,QAAQ,GAAGA,QAAQ,CAACG,OAAO,CAAC,SAAS,EAAE,SAAS,CAACF,MAAM,CAAC;IACxD,IAAIN,GAAG,GAAG;MACRS,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE,CACN;QACE;QACAC,OAAO,EAAE,gCAAgC;QACzCC,MAAM,EAAE;MACV,CAAC,EACD;QACED,OAAO,EAAEE,MAAM,CACb,8DAA8D,CAACP,MAAM,CAACE,OAAO,CAC3E,SAAS,EACT,YAAY;UACV,OAAOH,QAAQ;QACjB,CACF,CACF,CAAC;QACDO,MAAM,EAAE,IAAI;QACZE,MAAM,EAAE;UACNC,aAAa,EAAE;YACbJ,OAAO,EAAEE,MAAM,CACb,mDAAmD,CAACP,MAAM,CAACE,OAAO,CAChE,SAAS,EACT,YAAY;cACV,OAAOH,QAAQ;YACjB,CACF,CACF,CAAC;YACDW,UAAU,EAAE,IAAI;YAChBF,MAAM,EAAE;cACN,2BAA2B,EAAE;gBAC3BH,OAAO,EAAE,WAAW;gBACpBM,KAAK,EAAE;cACT,CAAC;cACDC,IAAI,EAAE;YACR;UACF;QACF;MACF,CAAC,CACF;MACDC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE,oBAAoB;MAC7B,kBAAkB,EAAE;QAClBT,OAAO,EAAE,sBAAsB;QAC/BK,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE;MACT,CAAC;MACDI,QAAQ,EAAE,gBAAgB;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EACN,2EAA2E;MAC7EC,WAAW,EAAE;IACf,CAAC;IACDxB,GAAG,CAACU,MAAM,CAAC,CAAC,CAAC,CAACI,MAAM,CAACC,aAAa,CAACD,MAAM,CAACI,IAAI,GAAGlB,GAAG;IACpDG,KAAK,CAACsB,SAAS,CAACzB,GAAG,GAAG;MACpB,aAAa,EAAE;QACb;QACAW,OAAO,EAAE,cAAc;QACvBM,KAAK,EAAE;MACT,CAAC;MACD,eAAe,EAAE;QACfN,OAAO,EAAE,aAAa;QACtBG,MAAM,EAAE;UACNY,SAAS,EAAE;YACTf,OAAO,EAAE,sBAAsB;YAC/BK,UAAU,EAAE,IAAI;YAChBC,KAAK,EAAE;UACT,CAAC;UACDO,WAAW,EAAE,aAAa;UAC1BG,OAAO,EAAE;YACPhB,OAAO,EAAE,cAAc;YACvBM,KAAK,EAAE,KAAK;YACZH,MAAM,EAAEd;UACV;QACF;MACF,CAAC;MACD,mBAAmB,EAAE;QACnBW,OAAO,EAAE,iBAAiB;QAC1BG,MAAM,EAAE;UACNU,WAAW,EAAE,WAAW;UACxBG,OAAO,EAAE;YACPhB,OAAO,EAAE,cAAc;YACvBM,KAAK,EAAE,KAAK;YACZH,MAAM,EAAEd;UACV;QACF;MACF;IACF,CAAC;IACDG,KAAK,CAACyB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD;MACA,IAAInB,OAAO,GAAGE,MAAM,CAClB,kEAAkE,CAACP,MAAM,CAACE,OAAO,CAC/E,SAAS,EACT,YAAY;QACV,OAAOH,QAAQ;MACjB,CACF,CAAC,EACD,IACF,CAAC;MACDF,KAAK,CAACsB,SAAS,CAAC,mBAAmB,CAAC,CAACM,iBAAiB,CACpDD,GAAG,EACH,KAAK,EACLnB,OACF,CAAC;IACH,CAAC,CAAC;IACFR,KAAK,CAACyB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/C3B,KAAK,CAACsB,SAAS,CAAC,mBAAmB,CAAC,CAACO,oBAAoB,CAACF,GAAG,EAAE,KAAK,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,EAAE3B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}