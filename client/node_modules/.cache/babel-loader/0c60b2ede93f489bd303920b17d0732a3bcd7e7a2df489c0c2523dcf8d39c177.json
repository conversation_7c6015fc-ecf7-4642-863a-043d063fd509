{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/src/components/TextInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { FiSend, FiPaperclip, FiLoader } from 'react-icons/fi';\nimport { utils } from '../services/api';\nimport './TextInput.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TextInput = ({\n  onLongTextPaste,\n  loading\n}) => {\n  _s();\n  const [inputText, setInputText] = useState('');\n  const [pendingPastes, setPendingPastes] = useState([]);\n  const textareaRef = useRef(null);\n\n  // 处理粘贴事件\n  const handlePaste = async event => {\n    const pastedText = event.clipboardData.getData('text');\n\n    // 检查是否为长文本\n    if (utils.isLongText(pastedText)) {\n      // 阻止默认粘贴行为\n      event.preventDefault();\n\n      // 创建待处理的粘贴项\n      const pasteId = Date.now().toString();\n      const newPaste = {\n        id: pasteId,\n        content: pastedText,\n        preview: utils.generatePreview(pastedText),\n        size: utils.formatFileSize(new Blob([pastedText]).size),\n        lines: pastedText.split('\\n').length,\n        timestamp: Date.now(),\n        status: 'pending' // pending, uploading, uploaded, error\n      };\n      setPendingPastes(prev => [...prev, newPaste]);\n      console.log(`📋 Long text detected: ${newPaste.size}, ${newPaste.lines} lines`);\n    }\n  };\n\n  // 确认上传长文本\n  const handleConfirmPaste = async pasteId => {\n    const paste = pendingPastes.find(p => p.id === pasteId);\n    if (!paste) return;\n    try {\n      // 更新状态为上传中\n      setPendingPastes(prev => prev.map(p => p.id === pasteId ? {\n        ...p,\n        status: 'uploading'\n      } : p));\n\n      // 上传到后端\n      const result = await onLongTextPaste(paste.content, `paste-${pasteId}.txt`);\n\n      // 更新状态为已上传\n      setPendingPastes(prev => prev.map(p => p.id === pasteId ? {\n        ...p,\n        status: 'uploaded',\n        serverId: result.id\n      } : p));\n\n      // 3秒后移除已上传的项目\n      setTimeout(() => {\n        setPendingPastes(prev => prev.filter(p => p.id !== pasteId));\n      }, 3000);\n    } catch (error) {\n      console.error('Upload failed:', error);\n\n      // 更新状态为错误\n      setPendingPastes(prev => prev.map(p => p.id === pasteId ? {\n        ...p,\n        status: 'error',\n        error: error.message\n      } : p));\n    }\n  };\n\n  // 取消粘贴\n  const handleCancelPaste = pasteId => {\n    setPendingPastes(prev => prev.filter(p => p.id !== pasteId));\n  };\n\n  // 处理普通输入\n  const handleInputChange = event => {\n    setInputText(event.target.value);\n  };\n\n  // 处理发送消息\n  const handleSend = () => {\n    if (inputText.trim()) {\n      console.log('📤 Sending message:', inputText);\n      setInputText('');\n      // 这里可以添加发送消息的逻辑\n    }\n  };\n\n  // 处理键盘事件\n  const handleKeyDown = event => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      handleSend();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-input-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"input-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n        ref: textareaRef,\n        value: inputText,\n        onChange: handleInputChange,\n        onPaste: handlePaste,\n        onKeyDown: handleKeyDown,\n        placeholder: \"Type your message here... (Try pasting long text!)\",\n        className: \"text-input\",\n        rows: 3,\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"action-button attach-button\",\n          title: \"Attach file\",\n          disabled: loading,\n          children: /*#__PURE__*/_jsxDEV(FiPaperclip, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"action-button send-button\",\n          onClick: handleSend,\n          disabled: loading || !inputText.trim(),\n          title: \"Send message\",\n          children: loading ? /*#__PURE__*/_jsxDEV(FiLoader, {\n            className: \"spinning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FiSend, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 60\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), pendingPastes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pending-pastes\",\n      children: pendingPastes.map(paste => /*#__PURE__*/_jsxDEV(PendingPasteItem, {\n        paste: paste,\n        onConfirm: () => handleConfirmPaste(paste.id),\n        onCancel: () => handleCancelPaste(paste.id)\n      }, paste.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n\n// 待处理粘贴项组件\n_s(TextInput, \"iiCMDaLLypfRr2X5bUmeo2ICg94=\");\n_c = TextInput;\nconst PendingPasteItem = ({\n  paste,\n  onConfirm,\n  onCancel\n}) => {\n  const getStatusIcon = () => {\n    switch (paste.status) {\n      case 'uploading':\n        return /*#__PURE__*/_jsxDEV(FiLoader, {\n          className: \"spinning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 16\n        }, this);\n      case 'uploaded':\n        return '✅';\n      case 'error':\n        return '❌';\n      default:\n        return '📄';\n    }\n  };\n  const getStatusText = () => {\n    switch (paste.status) {\n      case 'uploading':\n        return 'Uploading...';\n      case 'uploaded':\n        return 'Uploaded successfully!';\n      case 'error':\n        return `Error: ${paste.error}`;\n      default:\n        return 'Long text detected';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `pending-paste-item ${paste.status}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paste-icon\",\n      children: getStatusIcon()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paste-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"paste-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"paste-status\",\n          children: getStatusText()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"paste-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"paste-size\",\n            children: paste.size\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"paste-lines\",\n            children: [paste.lines, \" lines\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"paste-preview\",\n        children: paste.preview\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), paste.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paste-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"paste-action-button confirm\",\n        onClick: onConfirm,\n        title: \"Upload to server\",\n        children: \"Upload\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"paste-action-button cancel\",\n        onClick: onCancel,\n        title: \"Cancel\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this), paste.status === 'error' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paste-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"paste-action-button retry\",\n        onClick: onConfirm,\n        title: \"Retry upload\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"paste-action-button cancel\",\n        onClick: onCancel,\n        title: \"Cancel\",\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n_c2 = PendingPasteItem;\nexport default TextInput;\nvar _c, _c2;\n$RefreshReg$(_c, \"TextInput\");\n$RefreshReg$(_c2, \"PendingPasteItem\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "FiSend", "FiPaperclip", "<PERSON><PERSON><PERSON><PERSON>", "utils", "jsxDEV", "_jsxDEV", "TextInput", "onLongTextPaste", "loading", "_s", "inputText", "setInputText", "pendingPastes", "setPendingPastes", "textareaRef", "handlePaste", "event", "pastedText", "clipboardData", "getData", "isLongText", "preventDefault", "pasteId", "Date", "now", "toString", "newPaste", "id", "content", "preview", "generatePreview", "size", "formatFileSize", "Blob", "lines", "split", "length", "timestamp", "status", "prev", "console", "log", "handleConfirmPaste", "paste", "find", "p", "map", "result", "serverId", "setTimeout", "filter", "error", "message", "handleCancelPaste", "handleInputChange", "target", "value", "handleSend", "trim", "handleKeyDown", "key", "shift<PERSON>ey", "className", "children", "ref", "onChange", "onPaste", "onKeyDown", "placeholder", "rows", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onClick", "PendingPasteItem", "onConfirm", "onCancel", "_c", "getStatusIcon", "getStatusText", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/src/components/TextInput.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { FiSend, FiPaperclip, FiLoader } from 'react-icons/fi';\nimport { utils } from '../services/api';\nimport './TextInput.css';\n\nconst TextInput = ({ onLongTextPaste, loading }) => {\n  const [inputText, setInputText] = useState('');\n  const [pendingPastes, setPendingPastes] = useState([]);\n  const textareaRef = useRef(null);\n\n  // 处理粘贴事件\n  const handlePaste = async (event) => {\n    const pastedText = event.clipboardData.getData('text');\n    \n    // 检查是否为长文本\n    if (utils.isLongText(pastedText)) {\n      // 阻止默认粘贴行为\n      event.preventDefault();\n      \n      // 创建待处理的粘贴项\n      const pasteId = Date.now().toString();\n      const newPaste = {\n        id: pasteId,\n        content: pastedText,\n        preview: utils.generatePreview(pastedText),\n        size: utils.formatFileSize(new Blob([pastedText]).size),\n        lines: pastedText.split('\\n').length,\n        timestamp: Date.now(),\n        status: 'pending' // pending, uploading, uploaded, error\n      };\n\n      setPendingPastes(prev => [...prev, newPaste]);\n      \n      console.log(`📋 Long text detected: ${newPaste.size}, ${newPaste.lines} lines`);\n    }\n  };\n\n  // 确认上传长文本\n  const handleConfirmPaste = async (pasteId) => {\n    const paste = pendingPastes.find(p => p.id === pasteId);\n    if (!paste) return;\n\n    try {\n      // 更新状态为上传中\n      setPendingPastes(prev => \n        prev.map(p => p.id === pasteId ? { ...p, status: 'uploading' } : p)\n      );\n\n      // 上传到后端\n      const result = await onLongTextPaste(paste.content, `paste-${pasteId}.txt`);\n      \n      // 更新状态为已上传\n      setPendingPastes(prev => \n        prev.map(p => p.id === pasteId ? { ...p, status: 'uploaded', serverId: result.id } : p)\n      );\n\n      // 3秒后移除已上传的项目\n      setTimeout(() => {\n        setPendingPastes(prev => prev.filter(p => p.id !== pasteId));\n      }, 3000);\n\n    } catch (error) {\n      console.error('Upload failed:', error);\n      \n      // 更新状态为错误\n      setPendingPastes(prev => \n        prev.map(p => p.id === pasteId ? { ...p, status: 'error', error: error.message } : p)\n      );\n    }\n  };\n\n  // 取消粘贴\n  const handleCancelPaste = (pasteId) => {\n    setPendingPastes(prev => prev.filter(p => p.id !== pasteId));\n  };\n\n  // 处理普通输入\n  const handleInputChange = (event) => {\n    setInputText(event.target.value);\n  };\n\n  // 处理发送消息\n  const handleSend = () => {\n    if (inputText.trim()) {\n      console.log('📤 Sending message:', inputText);\n      setInputText('');\n      // 这里可以添加发送消息的逻辑\n    }\n  };\n\n  // 处理键盘事件\n  const handleKeyDown = (event) => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      handleSend();\n    }\n  };\n\n  return (\n    <div className=\"text-input-container\">\n      <div className=\"input-wrapper\">\n        <textarea\n          ref={textareaRef}\n          value={inputText}\n          onChange={handleInputChange}\n          onPaste={handlePaste}\n          onKeyDown={handleKeyDown}\n          placeholder=\"Type your message here... (Try pasting long text!)\"\n          className=\"text-input\"\n          rows={3}\n          disabled={loading}\n        />\n        \n        <div className=\"input-actions\">\n          <button\n            className=\"action-button attach-button\"\n            title=\"Attach file\"\n            disabled={loading}\n          >\n            <FiPaperclip />\n          </button>\n          \n          <button\n            className=\"action-button send-button\"\n            onClick={handleSend}\n            disabled={loading || !inputText.trim()}\n            title=\"Send message\"\n          >\n            {loading ? <FiLoader className=\"spinning\" /> : <FiSend />}\n          </button>\n        </div>\n      </div>\n\n      {/* 待处理的长文本粘贴 */}\n      {pendingPastes.length > 0 && (\n        <div className=\"pending-pastes\">\n          {pendingPastes.map(paste => (\n            <PendingPasteItem\n              key={paste.id}\n              paste={paste}\n              onConfirm={() => handleConfirmPaste(paste.id)}\n              onCancel={() => handleCancelPaste(paste.id)}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// 待处理粘贴项组件\nconst PendingPasteItem = ({ paste, onConfirm, onCancel }) => {\n  const getStatusIcon = () => {\n    switch (paste.status) {\n      case 'uploading':\n        return <FiLoader className=\"spinning\" />;\n      case 'uploaded':\n        return '✅';\n      case 'error':\n        return '❌';\n      default:\n        return '📄';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (paste.status) {\n      case 'uploading':\n        return 'Uploading...';\n      case 'uploaded':\n        return 'Uploaded successfully!';\n      case 'error':\n        return `Error: ${paste.error}`;\n      default:\n        return 'Long text detected';\n    }\n  };\n\n  return (\n    <div className={`pending-paste-item ${paste.status}`}>\n      <div className=\"paste-icon\">\n        {getStatusIcon()}\n      </div>\n      \n      <div className=\"paste-content\">\n        <div className=\"paste-header\">\n          <span className=\"paste-status\">{getStatusText()}</span>\n          <div className=\"paste-meta\">\n            <span className=\"paste-size\">{paste.size}</span>\n            <span className=\"paste-lines\">{paste.lines} lines</span>\n          </div>\n        </div>\n        \n        <div className=\"paste-preview\">\n          {paste.preview}\n        </div>\n      </div>\n      \n      {paste.status === 'pending' && (\n        <div className=\"paste-actions\">\n          <button\n            className=\"paste-action-button confirm\"\n            onClick={onConfirm}\n            title=\"Upload to server\"\n          >\n            Upload\n          </button>\n          <button\n            className=\"paste-action-button cancel\"\n            onClick={onCancel}\n            title=\"Cancel\"\n          >\n            ✕\n          </button>\n        </div>\n      )}\n      \n      {paste.status === 'error' && (\n        <div className=\"paste-actions\">\n          <button\n            className=\"paste-action-button retry\"\n            onClick={onConfirm}\n            title=\"Retry upload\"\n          >\n            Retry\n          </button>\n          <button\n            className=\"paste-action-button cancel\"\n            onClick={onCancel}\n            title=\"Cancel\"\n          >\n            ✕\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TextInput;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9D,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAMgB,WAAW,GAAGf,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMgB,WAAW,GAAG,MAAOC,KAAK,IAAK;IACnC,MAAMC,UAAU,GAAGD,KAAK,CAACE,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;;IAEtD;IACA,IAAIhB,KAAK,CAACiB,UAAU,CAACH,UAAU,CAAC,EAAE;MAChC;MACAD,KAAK,CAACK,cAAc,CAAC,CAAC;;MAEtB;MACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACrC,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAEL,OAAO;QACXM,OAAO,EAAEX,UAAU;QACnBY,OAAO,EAAE1B,KAAK,CAAC2B,eAAe,CAACb,UAAU,CAAC;QAC1Cc,IAAI,EAAE5B,KAAK,CAAC6B,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAChB,UAAU,CAAC,CAAC,CAACc,IAAI,CAAC;QACvDG,KAAK,EAAEjB,UAAU,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM;QACpCC,SAAS,EAAEd,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBc,MAAM,EAAE,SAAS,CAAC;MACpB,CAAC;MAEDzB,gBAAgB,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEb,QAAQ,CAAC,CAAC;MAE7Cc,OAAO,CAACC,GAAG,CAAC,0BAA0Bf,QAAQ,CAACK,IAAI,KAAKL,QAAQ,CAACQ,KAAK,QAAQ,CAAC;IACjF;EACF,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAG,MAAOpB,OAAO,IAAK;IAC5C,MAAMqB,KAAK,GAAG/B,aAAa,CAACgC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKL,OAAO,CAAC;IACvD,IAAI,CAACqB,KAAK,EAAE;IAEZ,IAAI;MACF;MACA9B,gBAAgB,CAAC0B,IAAI,IACnBA,IAAI,CAACO,GAAG,CAACD,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKL,OAAO,GAAG;QAAE,GAAGuB,CAAC;QAAEP,MAAM,EAAE;MAAY,CAAC,GAAGO,CAAC,CACpE,CAAC;;MAED;MACA,MAAME,MAAM,GAAG,MAAMxC,eAAe,CAACoC,KAAK,CAACf,OAAO,EAAE,SAASN,OAAO,MAAM,CAAC;;MAE3E;MACAT,gBAAgB,CAAC0B,IAAI,IACnBA,IAAI,CAACO,GAAG,CAACD,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKL,OAAO,GAAG;QAAE,GAAGuB,CAAC;QAAEP,MAAM,EAAE,UAAU;QAAEU,QAAQ,EAAED,MAAM,CAACpB;MAAG,CAAC,GAAGkB,CAAC,CACxF,CAAC;;MAED;MACAI,UAAU,CAAC,MAAM;QACfpC,gBAAgB,CAAC0B,IAAI,IAAIA,IAAI,CAACW,MAAM,CAACL,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKL,OAAO,CAAC,CAAC;MAC9D,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;;MAEtC;MACAtC,gBAAgB,CAAC0B,IAAI,IACnBA,IAAI,CAACO,GAAG,CAACD,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKL,OAAO,GAAG;QAAE,GAAGuB,CAAC;QAAEP,MAAM,EAAE,OAAO;QAAEa,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,GAAGP,CAAC,CACtF,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAI/B,OAAO,IAAK;IACrCT,gBAAgB,CAAC0B,IAAI,IAAIA,IAAI,CAACW,MAAM,CAACL,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKL,OAAO,CAAC,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMgC,iBAAiB,GAAItC,KAAK,IAAK;IACnCL,YAAY,CAACK,KAAK,CAACuC,MAAM,CAACC,KAAK,CAAC;EAClC,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI/C,SAAS,CAACgD,IAAI,CAAC,CAAC,EAAE;MACpBlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE/B,SAAS,CAAC;MAC7CC,YAAY,CAAC,EAAE,CAAC;MAChB;IACF;EACF,CAAC;;EAED;EACA,MAAMgD,aAAa,GAAI3C,KAAK,IAAK;IAC/B,IAAIA,KAAK,CAAC4C,GAAG,KAAK,OAAO,IAAI,CAAC5C,KAAK,CAAC6C,QAAQ,EAAE;MAC5C7C,KAAK,CAACK,cAAc,CAAC,CAAC;MACtBoC,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,oBACEpD,OAAA;IAAKyD,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnC1D,OAAA;MAAKyD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1D,OAAA;QACE2D,GAAG,EAAElD,WAAY;QACjB0C,KAAK,EAAE9C,SAAU;QACjBuD,QAAQ,EAAEX,iBAAkB;QAC5BY,OAAO,EAAEnD,WAAY;QACrBoD,SAAS,EAAER,aAAc;QACzBS,WAAW,EAAC,oDAAoD;QAChEN,SAAS,EAAC,YAAY;QACtBO,IAAI,EAAE,CAAE;QACRC,QAAQ,EAAE9D;MAAQ;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAEFrE,OAAA;QAAKyD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1D,OAAA;UACEyD,SAAS,EAAC,6BAA6B;UACvCa,KAAK,EAAC,aAAa;UACnBL,QAAQ,EAAE9D,OAAQ;UAAAuD,QAAA,eAElB1D,OAAA,CAACJ,WAAW;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAETrE,OAAA;UACEyD,SAAS,EAAC,2BAA2B;UACrCc,OAAO,EAAEnB,UAAW;UACpBa,QAAQ,EAAE9D,OAAO,IAAI,CAACE,SAAS,CAACgD,IAAI,CAAC,CAAE;UACvCiB,KAAK,EAAC,cAAc;UAAAZ,QAAA,EAEnBvD,OAAO,gBAAGH,OAAA,CAACH,QAAQ;YAAC4D,SAAS,EAAC;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACL,MAAM;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9D,aAAa,CAACwB,MAAM,GAAG,CAAC,iBACvB/B,OAAA;MAAKyD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BnD,aAAa,CAACkC,GAAG,CAACH,KAAK,iBACtBtC,OAAA,CAACwE,gBAAgB;QAEflC,KAAK,EAAEA,KAAM;QACbmC,SAAS,EAAEA,CAAA,KAAMpC,kBAAkB,CAACC,KAAK,CAAChB,EAAE,CAAE;QAC9CoD,QAAQ,EAAEA,CAAA,KAAM1B,iBAAiB,CAACV,KAAK,CAAChB,EAAE;MAAE,GAHvCgB,KAAK,CAAChB,EAAE;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAId,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAjE,EAAA,CAjJMH,SAAS;AAAA0E,EAAA,GAAT1E,SAAS;AAkJf,MAAMuE,gBAAgB,GAAGA,CAAC;EAAElC,KAAK;EAAEmC,SAAS;EAAEC;AAAS,CAAC,KAAK;EAC3D,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQtC,KAAK,CAACL,MAAM;MAClB,KAAK,WAAW;QACd,oBAAOjC,OAAA,CAACH,QAAQ;UAAC4D,SAAS,EAAC;QAAU;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1C,KAAK,UAAU;QACb,OAAO,GAAG;MACZ,KAAK,OAAO;QACV,OAAO,GAAG;MACZ;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQvC,KAAK,CAACL,MAAM;MAClB,KAAK,WAAW;QACd,OAAO,cAAc;MACvB,KAAK,UAAU;QACb,OAAO,wBAAwB;MACjC,KAAK,OAAO;QACV,OAAO,UAAUK,KAAK,CAACQ,KAAK,EAAE;MAChC;QACE,OAAO,oBAAoB;IAC/B;EACF,CAAC;EAED,oBACE9C,OAAA;IAAKyD,SAAS,EAAE,sBAAsBnB,KAAK,CAACL,MAAM,EAAG;IAAAyB,QAAA,gBACnD1D,OAAA;MAAKyD,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxBkB,aAAa,CAAC;IAAC;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAENrE,OAAA;MAAKyD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1D,OAAA;QAAKyD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1D,OAAA;UAAMyD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAEmB,aAAa,CAAC;QAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvDrE,OAAA;UAAKyD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1D,OAAA;YAAMyD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEpB,KAAK,CAACZ;UAAI;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDrE,OAAA;YAAMyD,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAEpB,KAAK,CAACT,KAAK,EAAC,QAAM;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA;QAAKyD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BpB,KAAK,CAACd;MAAO;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL/B,KAAK,CAACL,MAAM,KAAK,SAAS,iBACzBjC,OAAA;MAAKyD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1D,OAAA;QACEyD,SAAS,EAAC,6BAA6B;QACvCc,OAAO,EAAEE,SAAU;QACnBH,KAAK,EAAC,kBAAkB;QAAAZ,QAAA,EACzB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrE,OAAA;QACEyD,SAAS,EAAC,4BAA4B;QACtCc,OAAO,EAAEG,QAAS;QAClBJ,KAAK,EAAC,QAAQ;QAAAZ,QAAA,EACf;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAEA/B,KAAK,CAACL,MAAM,KAAK,OAAO,iBACvBjC,OAAA;MAAKyD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1D,OAAA;QACEyD,SAAS,EAAC,2BAA2B;QACrCc,OAAO,EAAEE,SAAU;QACnBH,KAAK,EAAC,cAAc;QAAAZ,QAAA,EACrB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrE,OAAA;QACEyD,SAAS,EAAC,4BAA4B;QACtCc,OAAO,EAAEG,QAAS;QAClBJ,KAAK,EAAC,QAAQ;QAAAZ,QAAA,EACf;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACS,GAAA,GAtFIN,gBAAgB;AAwFtB,eAAevE,SAAS;AAAC,IAAA0E,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}