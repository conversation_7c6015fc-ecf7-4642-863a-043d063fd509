{"ast": null, "code": "/*\nLanguage: OCaml\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nDescription: OCaml language definition.\nWebsite: https://ocaml.org\nCategory: functional\n*/\n\nfunction ocaml(hljs) {\n  /* missing support for heredoc-like string (OCaml 4.0.2+) */\n  return {\n    name: 'OCaml',\n    aliases: ['ml'],\n    keywords: {\n      $pattern: '[a-z_]\\\\w*!?',\n      keyword: 'and as assert asr begin class constraint do done downto else end ' + 'exception external for fun function functor if in include ' + 'inherit! inherit initializer land lazy let lor lsl lsr lxor match method!|10 method ' + 'mod module mutable new object of open! open or private rec sig struct ' + 'then to try type val! val virtual when while with ' + /* camlp4 */\n      'parser value',\n      built_in: /* built-in types */\n      'array bool bytes char exn|5 float int int32 int64 list lazy_t|5 nativeint|5 string unit ' + /* (some) types in Pervasives */\n      'in_channel out_channel ref',\n      literal: 'true false'\n    },\n    illegal: /\\/\\/|>>/,\n    contains: [{\n      className: 'literal',\n      begin: '\\\\[(\\\\|\\\\|)?\\\\]|\\\\(\\\\)',\n      relevance: 0\n    }, hljs.COMMENT('\\\\(\\\\*', '\\\\*\\\\)', {\n      contains: ['self']\n    }), {\n      /* type variable */\n      className: 'symbol',\n      begin: '\\'[A-Za-z_](?!\\')[\\\\w\\']*'\n      /* the grammar is ambiguous on how 'a'b should be interpreted but not the compiler */\n    }, {\n      /* polymorphic variant */\n      className: 'type',\n      begin: '`[A-Z][\\\\w\\']*'\n    }, {\n      /* module or constructor */\n      className: 'type',\n      begin: '\\\\b[A-Z][\\\\w\\']*',\n      relevance: 0\n    }, {\n      /* don't color identifiers, but safely catch all identifiers with '*/\n      begin: '[a-z_]\\\\w*\\'[\\\\w\\']*',\n      relevance: 0\n    }, hljs.inherit(hljs.APOS_STRING_MODE, {\n      className: 'string',\n      relevance: 0\n    }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    }), {\n      className: 'number',\n      begin: '\\\\b(0[xX][a-fA-F0-9_]+[Lln]?|' + '0[oO][0-7_]+[Lln]?|' + '0[bB][01_]+[Lln]?|' + '[0-9][0-9_]*([Lln]|(\\\\.[0-9_]*)?([eE][-+]?[0-9_]+)?)?)',\n      relevance: 0\n    }, {\n      begin: /->/ // relevance booster\n    }]\n  };\n}\nmodule.exports = ocaml;", "map": {"version": 3, "names": ["ocaml", "hljs", "name", "aliases", "keywords", "$pattern", "keyword", "built_in", "literal", "illegal", "contains", "className", "begin", "relevance", "COMMENT", "inherit", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/ocaml.js"], "sourcesContent": ["/*\nLanguage: OCaml\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nDescription: OCaml language definition.\nWebsite: https://ocaml.org\nCategory: functional\n*/\n\nfunction ocaml(hljs) {\n  /* missing support for heredoc-like string (OCaml 4.0.2+) */\n  return {\n    name: 'OCaml',\n    aliases: ['ml'],\n    keywords: {\n      $pattern: '[a-z_]\\\\w*!?',\n      keyword:\n        'and as assert asr begin class constraint do done downto else end ' +\n        'exception external for fun function functor if in include ' +\n        'inherit! inherit initializer land lazy let lor lsl lsr lxor match method!|10 method ' +\n        'mod module mutable new object of open! open or private rec sig struct ' +\n        'then to try type val! val virtual when while with ' +\n        /* camlp4 */\n        'parser value',\n      built_in:\n        /* built-in types */\n        'array bool bytes char exn|5 float int int32 int64 list lazy_t|5 nativeint|5 string unit ' +\n        /* (some) types in Pervasives */\n        'in_channel out_channel ref',\n      literal:\n        'true false'\n    },\n    illegal: /\\/\\/|>>/,\n    contains: [\n      {\n        className: 'literal',\n        begin: '\\\\[(\\\\|\\\\|)?\\\\]|\\\\(\\\\)',\n        relevance: 0\n      },\n      hljs.COMMENT(\n        '\\\\(\\\\*',\n        '\\\\*\\\\)',\n        {\n          contains: ['self']\n        }\n      ),\n      { /* type variable */\n        className: 'symbol',\n        begin: '\\'[A-Za-z_](?!\\')[\\\\w\\']*'\n        /* the grammar is ambiguous on how 'a'b should be interpreted but not the compiler */\n      },\n      { /* polymorphic variant */\n        className: 'type',\n        begin: '`[A-Z][\\\\w\\']*'\n      },\n      { /* module or constructor */\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w\\']*',\n        relevance: 0\n      },\n      { /* don't color identifiers, but safely catch all identifiers with '*/\n        begin: '[a-z_]\\\\w*\\'[\\\\w\\']*', relevance: 0\n      },\n      hljs.inherit(hljs.APOS_STRING_MODE, {className: 'string', relevance: 0}),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {illegal: null}),\n      {\n        className: 'number',\n        begin:\n          '\\\\b(0[xX][a-fA-F0-9_]+[Lln]?|' +\n          '0[oO][0-7_]+[Lln]?|' +\n          '0[bB][01_]+[Lln]?|' +\n          '[0-9][0-9_]*([Lln]|(\\\\.[0-9_]*)?([eE][-+]?[0-9_]+)?)?)',\n        relevance: 0\n      },\n      {\n        begin: /->/ // relevance booster\n      }\n    ]\n  }\n}\n\nmodule.exports = ocaml;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB;EACA,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,CAAC,IAAI,CAAC;IACfC,QAAQ,EAAE;MACRC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EACL,mEAAmE,GACnE,4DAA4D,GAC5D,sFAAsF,GACtF,wEAAwE,GACxE,oDAAoD,GACpD;MACA,cAAc;MAChBC,QAAQ,EACN;MACA,0FAA0F,GAC1F;MACA,4BAA4B;MAC9BC,OAAO,EACL;IACJ,CAAC;IACDC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,wBAAwB;MAC/BC,SAAS,EAAE;IACb,CAAC,EACDZ,IAAI,CAACa,OAAO,CACV,QAAQ,EACR,QAAQ,EACR;MACEJ,QAAQ,EAAE,CAAC,MAAM;IACnB,CACF,CAAC,EACD;MAAE;MACAC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;MACP;IACF,CAAC,EACD;MAAE;MACAD,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC,EACD;MAAE;MACAD,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,kBAAkB;MACzBC,SAAS,EAAE;IACb,CAAC,EACD;MAAE;MACAD,KAAK,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAC5C,CAAC,EACDZ,IAAI,CAACc,OAAO,CAACd,IAAI,CAACe,gBAAgB,EAAE;MAACL,SAAS,EAAE,QAAQ;MAAEE,SAAS,EAAE;IAAC,CAAC,CAAC,EACxEZ,IAAI,CAACc,OAAO,CAACd,IAAI,CAACgB,iBAAiB,EAAE;MAACR,OAAO,EAAE;IAAI,CAAC,CAAC,EACrD;MACEE,SAAS,EAAE,QAAQ;MACnBC,KAAK,EACH,+BAA+B,GAC/B,qBAAqB,GACrB,oBAAoB,GACpB,wDAAwD;MAC1DC,SAAS,EAAE;IACb,CAAC,EACD;MACED,KAAK,EAAE,IAAI,CAAC;IACd,CAAC;EAEL,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGnB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}