{"ast": null, "code": "'use strict';\n\nmodule.exports = css;\ncss.displayName = 'css';\ncss.aliases = [];\nfunction css(Prism) {\n  ;\n  (function (Prism) {\n    var string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n    Prism.languages.css = {\n      comment: /\\/\\*[\\s\\S]*?\\*\\//,\n      atrule: {\n        pattern: /@[\\w-](?:[^;{\\s]|\\s+(?![\\s{]))*(?:;|(?=\\s*\\{))/,\n        inside: {\n          rule: /^@[\\w-]+/,\n          'selector-function-argument': {\n            pattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n            lookbehind: true,\n            alias: 'selector'\n          },\n          keyword: {\n            pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n            lookbehind: true\n          } // See rest below\n        }\n      },\n      url: {\n        // https://drafts.csswg.org/css-values-3/#urls\n        pattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n        greedy: true,\n        inside: {\n          function: /^url/i,\n          punctuation: /^\\(|\\)$/,\n          string: {\n            pattern: RegExp('^' + string.source + '$'),\n            alias: 'url'\n          }\n        }\n      },\n      selector: {\n        pattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n        lookbehind: true\n      },\n      string: {\n        pattern: string,\n        greedy: true\n      },\n      property: {\n        pattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n        lookbehind: true\n      },\n      important: /!important\\b/i,\n      function: {\n        pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n        lookbehind: true\n      },\n      punctuation: /[(){};:,]/\n    };\n    Prism.languages.css['atrule'].inside.rest = Prism.languages.css;\n    var markup = Prism.languages.markup;\n    if (markup) {\n      markup.tag.addInlined('style', 'css');\n      markup.tag.addAttribute('style', 'css');\n    }\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "css", "displayName", "aliases", "Prism", "string", "languages", "comment", "at<PERSON>le", "pattern", "inside", "rule", "lookbehind", "alias", "keyword", "url", "RegExp", "source", "greedy", "function", "punctuation", "selector", "property", "important", "rest", "markup", "tag", "addInlined", "addAttribute"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/css.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = css\ncss.displayName = 'css'\ncss.aliases = []\nfunction css(Prism) {\n  ;(function (Prism) {\n    var string =\n      /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/\n    Prism.languages.css = {\n      comment: /\\/\\*[\\s\\S]*?\\*\\//,\n      atrule: {\n        pattern: /@[\\w-](?:[^;{\\s]|\\s+(?![\\s{]))*(?:;|(?=\\s*\\{))/,\n        inside: {\n          rule: /^@[\\w-]+/,\n          'selector-function-argument': {\n            pattern:\n              /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n            lookbehind: true,\n            alias: 'selector'\n          },\n          keyword: {\n            pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n            lookbehind: true\n          } // See rest below\n        }\n      },\n      url: {\n        // https://drafts.csswg.org/css-values-3/#urls\n        pattern: RegExp(\n          '\\\\burl\\\\((?:' +\n            string.source +\n            '|' +\n            /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source +\n            ')\\\\)',\n          'i'\n        ),\n        greedy: true,\n        inside: {\n          function: /^url/i,\n          punctuation: /^\\(|\\)$/,\n          string: {\n            pattern: RegExp('^' + string.source + '$'),\n            alias: 'url'\n          }\n        }\n      },\n      selector: {\n        pattern: RegExp(\n          '(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' +\n            string.source +\n            ')*(?=\\\\s*\\\\{)'\n        ),\n        lookbehind: true\n      },\n      string: {\n        pattern: string,\n        greedy: true\n      },\n      property: {\n        pattern:\n          /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n        lookbehind: true\n      },\n      important: /!important\\b/i,\n      function: {\n        pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n        lookbehind: true\n      },\n      punctuation: /[(){};:,]/\n    }\n    Prism.languages.css['atrule'].inside.rest = Prism.languages.css\n    var markup = Prism.languages.markup\n    if (markup) {\n      markup.tag.addInlined('style', 'css')\n      markup.tag.addAttribute('style', 'css')\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,MAAM,GACR,6EAA6E;IAC/ED,KAAK,CAACE,SAAS,CAACL,GAAG,GAAG;MACpBM,OAAO,EAAE,kBAAkB;MAC3BC,MAAM,EAAE;QACNC,OAAO,EAAE,gDAAgD;QACzDC,MAAM,EAAE;UACNC,IAAI,EAAE,UAAU;UAChB,4BAA4B,EAAE;YAC5BF,OAAO,EACL,2FAA2F;YAC7FG,UAAU,EAAE,IAAI;YAChBC,KAAK,EAAE;UACT,CAAC;UACDC,OAAO,EAAE;YACPL,OAAO,EAAE,wCAAwC;YACjDG,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;MACF,CAAC;MACDG,GAAG,EAAE;QACH;QACAN,OAAO,EAAEO,MAAM,CACb,cAAc,GACZX,MAAM,CAACY,MAAM,GACb,GAAG,GACH,6BAA6B,CAACA,MAAM,GACpC,MAAM,EACR,GACF,CAAC;QACDC,MAAM,EAAE,IAAI;QACZR,MAAM,EAAE;UACNS,QAAQ,EAAE,OAAO;UACjBC,WAAW,EAAE,SAAS;UACtBf,MAAM,EAAE;YACNI,OAAO,EAAEO,MAAM,CAAC,GAAG,GAAGX,MAAM,CAACY,MAAM,GAAG,GAAG,CAAC;YAC1CJ,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDQ,QAAQ,EAAE;QACRZ,OAAO,EAAEO,MAAM,CACb,oDAAoD,GAClDX,MAAM,CAACY,MAAM,GACb,eACJ,CAAC;QACDL,UAAU,EAAE;MACd,CAAC;MACDP,MAAM,EAAE;QACNI,OAAO,EAAEJ,MAAM;QACfa,MAAM,EAAE;MACV,CAAC;MACDI,QAAQ,EAAE;QACRb,OAAO,EACL,mFAAmF;QACrFG,UAAU,EAAE;MACd,CAAC;MACDW,SAAS,EAAE,eAAe;MAC1BJ,QAAQ,EAAE;QACRV,OAAO,EAAE,iCAAiC;QAC1CG,UAAU,EAAE;MACd,CAAC;MACDQ,WAAW,EAAE;IACf,CAAC;IACDhB,KAAK,CAACE,SAAS,CAACL,GAAG,CAAC,QAAQ,CAAC,CAACS,MAAM,CAACc,IAAI,GAAGpB,KAAK,CAACE,SAAS,CAACL,GAAG;IAC/D,IAAIwB,MAAM,GAAGrB,KAAK,CAACE,SAAS,CAACmB,MAAM;IACnC,IAAIA,MAAM,EAAE;MACVA,MAAM,CAACC,GAAG,CAACC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;MACrCF,MAAM,CAACC,GAAG,CAACE,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;IACzC;EACF,CAAC,EAAExB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}