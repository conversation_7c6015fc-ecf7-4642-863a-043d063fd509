{"ast": null, "code": "'use strict';\n\nmodule.exports = dataweave;\ndataweave.displayName = 'dataweave';\ndataweave.aliases = [];\nfunction dataweave(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.dataweave = {\n      url: /\\b[A-Za-z]+:\\/\\/[\\w/:.?=&-]+|\\burn:[\\w:.?=&-]+/,\n      property: {\n        pattern: /(?:\\b\\w+#)?(?:\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|\\b\\w+)(?=\\s*[:@])/,\n        greedy: true\n      },\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n        greedy: true\n      },\n      'mime-type': /\\b(?:application|audio|image|multipart|text|video)\\/[\\w+-]+/,\n      date: {\n        pattern: /\\|[\\w:+-]+\\|/,\n        greedy: true\n      },\n      comment: [{\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        greedy: true\n      }, {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }],\n      regex: {\n        pattern: /\\/(?:[^\\\\\\/\\r\\n]|\\\\[^\\r\\n])+\\//,\n        greedy: true\n      },\n      keyword: /\\b(?:and|as|at|case|do|else|fun|if|input|is|match|not|ns|null|or|output|type|unless|update|using|var)\\b/,\n      function: /\\b[A-Z_]\\w*(?=\\s*\\()/i,\n      number: /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n      punctuation: /[{}[\\];(),.:@]/,\n      operator: /<<|>>|->|[<>~=]=?|!=|--?-?|\\+\\+?|!|\\?/,\n      boolean: /\\b(?:false|true)\\b/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "dataweave", "displayName", "aliases", "Prism", "languages", "url", "property", "pattern", "greedy", "string", "date", "comment", "lookbehind", "regex", "keyword", "function", "number", "punctuation", "operator", "boolean"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/dataweave.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dataweave\ndataweave.displayName = 'dataweave'\ndataweave.aliases = []\nfunction dataweave(Prism) {\n  ;(function (Prism) {\n    Prism.languages.dataweave = {\n      url: /\\b[A-Za-z]+:\\/\\/[\\w/:.?=&-]+|\\burn:[\\w:.?=&-]+/,\n      property: {\n        pattern: /(?:\\b\\w+#)?(?:\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|\\b\\w+)(?=\\s*[:@])/,\n        greedy: true\n      },\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n        greedy: true\n      },\n      'mime-type':\n        /\\b(?:application|audio|image|multipart|text|video)\\/[\\w+-]+/,\n      date: {\n        pattern: /\\|[\\w:+-]+\\|/,\n        greedy: true\n      },\n      comment: [\n        {\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      regex: {\n        pattern: /\\/(?:[^\\\\\\/\\r\\n]|\\\\[^\\r\\n])+\\//,\n        greedy: true\n      },\n      keyword:\n        /\\b(?:and|as|at|case|do|else|fun|if|input|is|match|not|ns|null|or|output|type|unless|update|using|var)\\b/,\n      function: /\\b[A-Z_]\\w*(?=\\s*\\()/i,\n      number: /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n      punctuation: /[{}[\\];(),.:@]/,\n      operator: /<<|>>|->|[<>~=]=?|!=|--?-?|\\+\\+?|!|\\?/,\n      boolean: /\\b(?:false|true)\\b/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,SAAS,GAAG;MAC1BK,GAAG,EAAE,gDAAgD;MACrDC,QAAQ,EAAE;QACRC,OAAO,EAAE,uDAAuD;QAChEC,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNF,OAAO,EAAE,oCAAoC;QAC7CC,MAAM,EAAE;MACV,CAAC;MACD,WAAW,EACT,6DAA6D;MAC/DE,IAAI,EAAE;QACJH,OAAO,EAAE,cAAc;QACvBC,MAAM,EAAE;MACV,CAAC;MACDG,OAAO,EAAE,CACP;QACEJ,OAAO,EAAE,iCAAiC;QAC1CK,UAAU,EAAE,IAAI;QAChBJ,MAAM,EAAE;MACV,CAAC,EACD;QACED,OAAO,EAAE,kBAAkB;QAC3BK,UAAU,EAAE,IAAI;QAChBJ,MAAM,EAAE;MACV,CAAC,CACF;MACDK,KAAK,EAAE;QACLN,OAAO,EAAE,gCAAgC;QACzCC,MAAM,EAAE;MACV,CAAC;MACDM,OAAO,EACL,yGAAyG;MAC3GC,QAAQ,EAAE,uBAAuB;MACjCC,MAAM,EAAE,oCAAoC;MAC5CC,WAAW,EAAE,gBAAgB;MAC7BC,QAAQ,EAAE,uCAAuC;MACjDC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,EAAEhB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}