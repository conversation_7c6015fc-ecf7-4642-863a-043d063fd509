{"ast": null, "code": "'use strict';\n\nmodule.exports = typescript;\ntypescript.displayName = 'typescript';\ntypescript.aliases = ['ts'];\nfunction typescript(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.typescript = Prism.languages.extend('javascript', {\n      'class-name': {\n        pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n        lookbehind: true,\n        greedy: true,\n        inside: null // see below\n      },\n      builtin: /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/\n    }); // The keywords TypeScript adds to JavaScript\n    Prism.languages.typescript.keyword.push(/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,\n    // keywords that have to be followed by an identifier\n    /\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,\n    // This is for `import type *, {}`\n    /\\btype\\b(?=\\s*(?:[\\{*]|$))/); // doesn't work with TS because TS is too complex\n    delete Prism.languages.typescript['parameter'];\n    delete Prism.languages.typescript['literal-property']; // a version of typescript specifically for highlighting types\n    var typeInside = Prism.languages.extend('typescript', {});\n    delete typeInside['class-name'];\n    Prism.languages.typescript['class-name'].inside = typeInside;\n    Prism.languages.insertBefore('typescript', 'function', {\n      decorator: {\n        pattern: /@[$\\w\\xA0-\\uFFFF]+/,\n        inside: {\n          at: {\n            pattern: /^@/,\n            alias: 'operator'\n          },\n          function: /^[\\s\\S]+/\n        }\n      },\n      'generic-function': {\n        // e.g. foo<T extends \"bar\" | \"baz\">( ...\n        pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n        greedy: true,\n        inside: {\n          function: /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n          generic: {\n            pattern: /<[\\s\\S]+/,\n            // everything after the first <\n            alias: 'class-name',\n            inside: typeInside\n          }\n        }\n      }\n    });\n    Prism.languages.ts = Prism.languages.typescript;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "typescript", "displayName", "aliases", "Prism", "languages", "extend", "pattern", "lookbehind", "greedy", "inside", "builtin", "keyword", "push", "typeInside", "insertBefore", "decorator", "at", "alias", "function", "generic", "ts"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/typescript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = typescript\ntypescript.displayName = 'typescript'\ntypescript.aliases = ['ts']\nfunction typescript(Prism) {\n  ;(function (Prism) {\n    Prism.languages.typescript = Prism.languages.extend('javascript', {\n      'class-name': {\n        pattern:\n          /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n        lookbehind: true,\n        greedy: true,\n        inside: null // see below\n      },\n      builtin:\n        /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/\n    }) // The keywords TypeScript adds to JavaScript\n    Prism.languages.typescript.keyword.push(\n      /\\b(?:abstract|declare|is|keyof|readonly|require)\\b/, // keywords that have to be followed by an identifier\n      /\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/, // This is for `import type *, {}`\n      /\\btype\\b(?=\\s*(?:[\\{*]|$))/\n    ) // doesn't work with TS because TS is too complex\n    delete Prism.languages.typescript['parameter']\n    delete Prism.languages.typescript['literal-property'] // a version of typescript specifically for highlighting types\n    var typeInside = Prism.languages.extend('typescript', {})\n    delete typeInside['class-name']\n    Prism.languages.typescript['class-name'].inside = typeInside\n    Prism.languages.insertBefore('typescript', 'function', {\n      decorator: {\n        pattern: /@[$\\w\\xA0-\\uFFFF]+/,\n        inside: {\n          at: {\n            pattern: /^@/,\n            alias: 'operator'\n          },\n          function: /^[\\s\\S]+/\n        }\n      },\n      'generic-function': {\n        // e.g. foo<T extends \"bar\" | \"baz\">( ...\n        pattern:\n          /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n        greedy: true,\n        inside: {\n          function: /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n          generic: {\n            pattern: /<[\\s\\S]+/,\n            // everything after the first <\n            alias: 'class-name',\n            inside: typeInside\n          }\n        }\n      }\n    })\n    Prism.languages.ts = Prism.languages.typescript\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AAC3B,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE;MAChE,YAAY,EAAE;QACZC,OAAO,EACL,8KAA8K;QAChLC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,IAAI,CAAC;MACf,CAAC;MACDC,OAAO,EACL;IACJ,CAAC,CAAC,EAAC;IACHP,KAAK,CAACC,SAAS,CAACJ,UAAU,CAACW,OAAO,CAACC,IAAI,CACrC,oDAAoD;IAAE;IACtD,0FAA0F;IAAE;IAC5F,4BACF,CAAC,EAAC;IACF,OAAOT,KAAK,CAACC,SAAS,CAACJ,UAAU,CAAC,WAAW,CAAC;IAC9C,OAAOG,KAAK,CAACC,SAAS,CAACJ,UAAU,CAAC,kBAAkB,CAAC,EAAC;IACtD,IAAIa,UAAU,GAAGV,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IACzD,OAAOQ,UAAU,CAAC,YAAY,CAAC;IAC/BV,KAAK,CAACC,SAAS,CAACJ,UAAU,CAAC,YAAY,CAAC,CAACS,MAAM,GAAGI,UAAU;IAC5DV,KAAK,CAACC,SAAS,CAACU,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;MACrDC,SAAS,EAAE;QACTT,OAAO,EAAE,oBAAoB;QAC7BG,MAAM,EAAE;UACNO,EAAE,EAAE;YACFV,OAAO,EAAE,IAAI;YACbW,KAAK,EAAE;UACT,CAAC;UACDC,QAAQ,EAAE;QACZ;MACF,CAAC;MACD,kBAAkB,EAAE;QAClB;QACAZ,OAAO,EACL,wGAAwG;QAC1GE,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNS,QAAQ,EAAE,2DAA2D;UACrEC,OAAO,EAAE;YACPb,OAAO,EAAE,UAAU;YACnB;YACAW,KAAK,EAAE,YAAY;YACnBR,MAAM,EAAEI;UACV;QACF;MACF;IACF,CAAC,CAAC;IACFV,KAAK,CAACC,SAAS,CAACgB,EAAE,GAAGjB,KAAK,CAACC,SAAS,CAACJ,UAAU;EACjD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}