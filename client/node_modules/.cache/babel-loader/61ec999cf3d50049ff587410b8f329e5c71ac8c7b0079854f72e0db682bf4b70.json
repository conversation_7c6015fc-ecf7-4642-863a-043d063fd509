{"ast": null, "code": "'use strict';\n\nmodule.exports = docker;\ndocker.displayName = 'docker';\ndocker.aliases = ['dockerfile'];\nfunction docker(Prism) {\n  ;\n  (function (Prism) {\n    // Many of the following regexes will contain negated lookaheads like `[ \\t]+(?![ \\t])`. This is a trick to ensure\n    // that quantifiers behave *atomically*. Atomic quantifiers are necessary to prevent exponential backtracking.\n    var spaceAfterBackSlash = /\\\\[\\r\\n](?:\\s|\\\\[\\r\\n]|#.*(?!.))*(?![\\s#]|\\\\[\\r\\n])/.source; // At least one space, comment, or line break\n    var space = /(?:[ \\t]+(?![ \\t])(?:<SP_BS>)?|<SP_BS>)/.source.replace(/<SP_BS>/g, function () {\n      return spaceAfterBackSlash;\n    });\n    var string = /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"|'(?:[^'\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*'/.source;\n    var option = /--[\\w-]+=(?:<STR>|(?![\"'])(?:[^\\s\\\\]|\\\\.)+)/.source.replace(/<STR>/g, function () {\n      return string;\n    });\n    var stringRule = {\n      pattern: RegExp(string),\n      greedy: true\n    };\n    var commentRule = {\n      pattern: /(^[ \\t]*)#.*/m,\n      lookbehind: true,\n      greedy: true\n    };\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function re(source, flags) {\n      source = source.replace(/<OPT>/g, function () {\n        return option;\n      }).replace(/<SP>/g, function () {\n        return space;\n      });\n      return RegExp(source, flags);\n    }\n    Prism.languages.docker = {\n      instruction: {\n        pattern: /(^[ \\t]*)(?:ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|ONBUILD|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR)(?=\\s)(?:\\\\.|[^\\r\\n\\\\])*(?:\\\\$(?:\\s|#.*$)*(?![\\s#])(?:\\\\.|[^\\r\\n\\\\])*)*/im,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          options: {\n            pattern: re(/(^(?:ONBUILD<SP>)?\\w+<SP>)<OPT>(?:<SP><OPT>)*/.source, 'i'),\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              property: {\n                pattern: /(^|\\s)--[\\w-]+/,\n                lookbehind: true\n              },\n              string: [stringRule, {\n                pattern: /(=)(?![\"'])(?:[^\\s\\\\]|\\\\.)+/,\n                lookbehind: true\n              }],\n              operator: /\\\\$/m,\n              punctuation: /=/\n            }\n          },\n          keyword: [{\n            // https://docs.docker.com/engine/reference/builder/#healthcheck\n            pattern: re(/(^(?:ONBUILD<SP>)?HEALTHCHECK<SP>(?:<OPT><SP>)*)(?:CMD|NONE)\\b/.source, 'i'),\n            lookbehind: true,\n            greedy: true\n          }, {\n            // https://docs.docker.com/engine/reference/builder/#from\n            pattern: re(/(^(?:ONBUILD<SP>)?FROM<SP>(?:<OPT><SP>)*(?!--)[^ \\t\\\\]+<SP>)AS/.source, 'i'),\n            lookbehind: true,\n            greedy: true\n          }, {\n            // https://docs.docker.com/engine/reference/builder/#onbuild\n            pattern: re(/(^ONBUILD<SP>)\\w+/.source, 'i'),\n            lookbehind: true,\n            greedy: true\n          }, {\n            pattern: /^\\w+/,\n            greedy: true\n          }],\n          comment: commentRule,\n          string: stringRule,\n          variable: /\\$(?:\\w+|\\{[^{}\"'\\\\]*\\})/,\n          operator: /\\\\$/m\n        }\n      },\n      comment: commentRule\n    };\n    Prism.languages.dockerfile = Prism.languages.docker;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "docker", "displayName", "aliases", "Prism", "spaceAfterBackSlash", "source", "space", "replace", "string", "option", "stringRule", "pattern", "RegExp", "greedy", "commentRule", "lookbehind", "re", "flags", "languages", "instruction", "inside", "options", "property", "operator", "punctuation", "keyword", "comment", "variable", "dockerfile"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/docker.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = docker\ndocker.displayName = 'docker'\ndocker.aliases = ['dockerfile']\nfunction docker(Prism) {\n  ;(function (Prism) {\n    // Many of the following regexes will contain negated lookaheads like `[ \\t]+(?![ \\t])`. This is a trick to ensure\n    // that quantifiers behave *atomically*. Atomic quantifiers are necessary to prevent exponential backtracking.\n    var spaceAfterBackSlash =\n      /\\\\[\\r\\n](?:\\s|\\\\[\\r\\n]|#.*(?!.))*(?![\\s#]|\\\\[\\r\\n])/.source // At least one space, comment, or line break\n    var space = /(?:[ \\t]+(?![ \\t])(?:<SP_BS>)?|<SP_BS>)/.source.replace(\n      /<SP_BS>/g,\n      function () {\n        return spaceAfterBackSlash\n      }\n    )\n    var string =\n      /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"|'(?:[^'\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*'/\n        .source\n    var option = /--[\\w-]+=(?:<STR>|(?![\"'])(?:[^\\s\\\\]|\\\\.)+)/.source.replace(\n      /<STR>/g,\n      function () {\n        return string\n      }\n    )\n    var stringRule = {\n      pattern: RegExp(string),\n      greedy: true\n    }\n    var commentRule = {\n      pattern: /(^[ \\t]*)#.*/m,\n      lookbehind: true,\n      greedy: true\n    }\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function re(source, flags) {\n      source = source\n        .replace(/<OPT>/g, function () {\n          return option\n        })\n        .replace(/<SP>/g, function () {\n          return space\n        })\n      return RegExp(source, flags)\n    }\n    Prism.languages.docker = {\n      instruction: {\n        pattern:\n          /(^[ \\t]*)(?:ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|ONBUILD|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR)(?=\\s)(?:\\\\.|[^\\r\\n\\\\])*(?:\\\\$(?:\\s|#.*$)*(?![\\s#])(?:\\\\.|[^\\r\\n\\\\])*)*/im,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          options: {\n            pattern: re(\n              /(^(?:ONBUILD<SP>)?\\w+<SP>)<OPT>(?:<SP><OPT>)*/.source,\n              'i'\n            ),\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              property: {\n                pattern: /(^|\\s)--[\\w-]+/,\n                lookbehind: true\n              },\n              string: [\n                stringRule,\n                {\n                  pattern: /(=)(?![\"'])(?:[^\\s\\\\]|\\\\.)+/,\n                  lookbehind: true\n                }\n              ],\n              operator: /\\\\$/m,\n              punctuation: /=/\n            }\n          },\n          keyword: [\n            {\n              // https://docs.docker.com/engine/reference/builder/#healthcheck\n              pattern: re(\n                /(^(?:ONBUILD<SP>)?HEALTHCHECK<SP>(?:<OPT><SP>)*)(?:CMD|NONE)\\b/\n                  .source,\n                'i'\n              ),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              // https://docs.docker.com/engine/reference/builder/#from\n              pattern: re(\n                /(^(?:ONBUILD<SP>)?FROM<SP>(?:<OPT><SP>)*(?!--)[^ \\t\\\\]+<SP>)AS/\n                  .source,\n                'i'\n              ),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              // https://docs.docker.com/engine/reference/builder/#onbuild\n              pattern: re(/(^ONBUILD<SP>)\\w+/.source, 'i'),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              pattern: /^\\w+/,\n              greedy: true\n            }\n          ],\n          comment: commentRule,\n          string: stringRule,\n          variable: /\\$(?:\\w+|\\{[^{}\"'\\\\]*\\})/,\n          operator: /\\\\$/m\n        }\n      },\n      comment: commentRule\n    }\n    Prism.languages.dockerfile = Prism.languages.docker\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,YAAY,CAAC;AAC/B,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA;IACA,IAAIC,mBAAmB,GACrB,qDAAqD,CAACC,MAAM,EAAC;IAC/D,IAAIC,KAAK,GAAG,yCAAyC,CAACD,MAAM,CAACE,OAAO,CAClE,UAAU,EACV,YAAY;MACV,OAAOH,mBAAmB;IAC5B,CACF,CAAC;IACD,IAAII,MAAM,GACR,yEAAyE,CACtEH,MAAM;IACX,IAAII,MAAM,GAAG,6CAA6C,CAACJ,MAAM,CAACE,OAAO,CACvE,QAAQ,EACR,YAAY;MACV,OAAOC,MAAM;IACf,CACF,CAAC;IACD,IAAIE,UAAU,GAAG;MACfC,OAAO,EAAEC,MAAM,CAACJ,MAAM,CAAC;MACvBK,MAAM,EAAE;IACV,CAAC;IACD,IAAIC,WAAW,GAAG;MAChBH,OAAO,EAAE,eAAe;MACxBI,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE;IACV,CAAC;IACD;AACJ;AACA;AACA;AACA;IACI,SAASG,EAAEA,CAACX,MAAM,EAAEY,KAAK,EAAE;MACzBZ,MAAM,GAAGA,MAAM,CACZE,OAAO,CAAC,QAAQ,EAAE,YAAY;QAC7B,OAAOE,MAAM;MACf,CAAC,CAAC,CACDF,OAAO,CAAC,OAAO,EAAE,YAAY;QAC5B,OAAOD,KAAK;MACd,CAAC,CAAC;MACJ,OAAOM,MAAM,CAACP,MAAM,EAAEY,KAAK,CAAC;IAC9B;IACAd,KAAK,CAACe,SAAS,CAAClB,MAAM,GAAG;MACvBmB,WAAW,EAAE;QACXR,OAAO,EACL,iNAAiN;QACnNI,UAAU,EAAE,IAAI;QAChBF,MAAM,EAAE,IAAI;QACZO,MAAM,EAAE;UACNC,OAAO,EAAE;YACPV,OAAO,EAAEK,EAAE,CACT,+CAA+C,CAACX,MAAM,EACtD,GACF,CAAC;YACDU,UAAU,EAAE,IAAI;YAChBF,MAAM,EAAE,IAAI;YACZO,MAAM,EAAE;cACNE,QAAQ,EAAE;gBACRX,OAAO,EAAE,gBAAgB;gBACzBI,UAAU,EAAE;cACd,CAAC;cACDP,MAAM,EAAE,CACNE,UAAU,EACV;gBACEC,OAAO,EAAE,6BAA6B;gBACtCI,UAAU,EAAE;cACd,CAAC,CACF;cACDQ,QAAQ,EAAE,MAAM;cAChBC,WAAW,EAAE;YACf;UACF,CAAC;UACDC,OAAO,EAAE,CACP;YACE;YACAd,OAAO,EAAEK,EAAE,CACT,gEAAgE,CAC7DX,MAAM,EACT,GACF,CAAC;YACDU,UAAU,EAAE,IAAI;YAChBF,MAAM,EAAE;UACV,CAAC,EACD;YACE;YACAF,OAAO,EAAEK,EAAE,CACT,gEAAgE,CAC7DX,MAAM,EACT,GACF,CAAC;YACDU,UAAU,EAAE,IAAI;YAChBF,MAAM,EAAE;UACV,CAAC,EACD;YACE;YACAF,OAAO,EAAEK,EAAE,CAAC,mBAAmB,CAACX,MAAM,EAAE,GAAG,CAAC;YAC5CU,UAAU,EAAE,IAAI;YAChBF,MAAM,EAAE;UACV,CAAC,EACD;YACEF,OAAO,EAAE,MAAM;YACfE,MAAM,EAAE;UACV,CAAC,CACF;UACDa,OAAO,EAAEZ,WAAW;UACpBN,MAAM,EAAEE,UAAU;UAClBiB,QAAQ,EAAE,0BAA0B;UACpCJ,QAAQ,EAAE;QACZ;MACF,CAAC;MACDG,OAAO,EAAEZ;IACX,CAAC;IACDX,KAAK,CAACe,SAAS,CAACU,UAAU,GAAGzB,KAAK,CAACe,SAAS,CAAClB,MAAM;EACrD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}