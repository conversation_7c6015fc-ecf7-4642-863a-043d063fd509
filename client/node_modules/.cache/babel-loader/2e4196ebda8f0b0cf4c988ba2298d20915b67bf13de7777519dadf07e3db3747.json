{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: R\nDescription: R is a free software environment for statistical computing and graphics.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.r-project.org\nCategory: common,scientific\n*/\n\n/** @type LanguageFn */\nfunction r(hljs) {\n  // Identifiers in R cannot start with `_`, but they can start with `.` if it\n  // is not immediately followed by a digit.\n  // R also supports quoted identifiers, which are near-arbitrary sequences\n  // delimited by backticks (`…`), which may contain escape sequences. These are\n  // handled in a separate mode. See `test/markup/r/names.txt` for examples.\n  // FIXME: Support Unicode identifiers.\n  const IDENT_RE = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/;\n  const SIMPLE_IDENT = /[a-zA-Z][a-zA-Z_0-9]*/;\n  return {\n    name: 'R',\n    // only in Haskell, not R\n    illegal: /->/,\n    keywords: {\n      $pattern: IDENT_RE,\n      keyword: 'function if in break next repeat else for while',\n      literal: 'NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 ' + 'NA_character_|10 NA_complex_|10',\n      built_in:\n      // Builtin constants\n      'LETTERS letters month.abb month.name pi T F ' +\n      // Primitive functions\n      // These are all the functions in `base` that are implemented as a\n      // `.Primitive`, minus those functions that are also keywords.\n      'abs acos acosh all any anyNA Arg as.call as.character ' + 'as.complex as.double as.environment as.integer as.logical ' + 'as.null.default as.numeric as.raw asin asinh atan atanh attr ' + 'attributes baseenv browser c call ceiling class Conj cos cosh ' + 'cospi cummax cummin cumprod cumsum digamma dim dimnames ' + 'emptyenv exp expression floor forceAndCall gamma gc.time ' + 'globalenv Im interactive invisible is.array is.atomic is.call ' + 'is.character is.complex is.double is.environment is.expression ' + 'is.finite is.function is.infinite is.integer is.language ' + 'is.list is.logical is.matrix is.na is.name is.nan is.null ' + 'is.numeric is.object is.pairlist is.raw is.recursive is.single ' + 'is.symbol lazyLoadDBfetch length lgamma list log max min ' + 'missing Mod names nargs nzchar oldClass on.exit pos.to.env ' + 'proc.time prod quote range Re rep retracemem return round ' + 'seq_along seq_len seq.int sign signif sin sinh sinpi sqrt ' + 'standardGeneric substitute sum switch tan tanh tanpi tracemem ' + 'trigamma trunc unclass untracemem UseMethod xtfrm'\n    },\n    compilerExtensions: [\n    // allow beforeMatch to act as a \"qualifier\" for the match\n    // the full match begin must be [beforeMatch][begin]\n    (mode, parent) => {\n      if (!mode.beforeMatch) return;\n      // starts conflicts with endsParent which we need to make sure the child\n      // rule is not matched multiple times\n      if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n      const originalMode = Object.assign({}, mode);\n      Object.keys(mode).forEach(key => {\n        delete mode[key];\n      });\n      mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n      mode.starts = {\n        relevance: 0,\n        contains: [Object.assign(originalMode, {\n          endsParent: true\n        })]\n      };\n      mode.relevance = 0;\n      delete originalMode.beforeMatch;\n    }],\n    contains: [\n    // Roxygen comments\n    hljs.COMMENT(/#'/, /$/, {\n      contains: [{\n        // Handle `@examples` separately to cause all subsequent code\n        // until the next `@`-tag on its own line to be kept as-is,\n        // preventing highlighting. This code is example R code, so nested\n        // doctags shouldn’t be treated as such. See\n        // `test/markup/r/roxygen.txt` for an example.\n        className: 'doctag',\n        begin: '@examples',\n        starts: {\n          contains: [{\n            begin: /\\n/\n          }, {\n            begin: /#'\\s*(?=@[a-zA-Z]+)/,\n            endsParent: true\n          }, {\n            begin: /#'/,\n            end: /$/,\n            excludeBegin: true\n          }]\n        }\n      }, {\n        // Handle `@param` to highlight the parameter name following\n        // after.\n        className: 'doctag',\n        begin: '@param',\n        end: /$/,\n        contains: [{\n          className: 'variable',\n          variants: [{\n            begin: IDENT_RE\n          }, {\n            begin: /`(?:\\\\.|[^`\\\\])+`/\n          }],\n          endsParent: true\n        }]\n      }, {\n        className: 'doctag',\n        begin: /@[a-zA-Z]+/\n      }, {\n        className: 'meta-keyword',\n        begin: /\\\\[a-zA-Z]+/\n      }]\n    }), hljs.HASH_COMMENT_MODE, {\n      className: 'string',\n      contains: [hljs.BACKSLASH_ESCAPE],\n      variants: [hljs.END_SAME_AS_BEGIN({\n        begin: /[rR]\"(-*)\\(/,\n        end: /\\)(-*)\"/\n      }), hljs.END_SAME_AS_BEGIN({\n        begin: /[rR]\"(-*)\\{/,\n        end: /\\}(-*)\"/\n      }), hljs.END_SAME_AS_BEGIN({\n        begin: /[rR]\"(-*)\\[/,\n        end: /\\](-*)\"/\n      }), hljs.END_SAME_AS_BEGIN({\n        begin: /[rR]'(-*)\\(/,\n        end: /\\)(-*)'/\n      }), hljs.END_SAME_AS_BEGIN({\n        begin: /[rR]'(-*)\\{/,\n        end: /\\}(-*)'/\n      }), hljs.END_SAME_AS_BEGIN({\n        begin: /[rR]'(-*)\\[/,\n        end: /\\](-*)'/\n      }), {\n        begin: '\"',\n        end: '\"',\n        relevance: 0\n      }, {\n        begin: \"'\",\n        end: \"'\",\n        relevance: 0\n      }]\n    }, {\n      className: 'number',\n      relevance: 0,\n      beforeMatch: /([^a-zA-Z0-9._])/,\n      // not part of an identifier\n      variants: [\n      // TODO: replace with negative look-behind when available\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/ },\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/ },\n      // { begin: /(?<![a-zA-Z0-9._])(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/ }\n      {\n        // Special case: only hexadecimal binary powers can contain fractions.\n        match: /0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/\n      }, {\n        match: /0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/\n      }, {\n        match: /(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/\n      }]\n    }, {\n      // infix operator\n      begin: '%',\n      end: '%'\n    },\n    // relevance boost for assignment\n    {\n      begin: concat(SIMPLE_IDENT, \"\\\\s+<-\\\\s+\")\n    }, {\n      // escaped identifier\n      begin: '`',\n      end: '`',\n      contains: [{\n        begin: /\\\\./\n      }]\n    }]\n  };\n}\nmodule.exports = r;", "map": {"version": 3, "names": ["source", "re", "<PERSON><PERSON><PERSON>", "concat", "args", "joined", "map", "x", "join", "r", "hljs", "IDENT_RE", "SIMPLE_IDENT", "name", "illegal", "keywords", "$pattern", "keyword", "literal", "built_in", "compilerExtensions", "mode", "parent", "beforeMatch", "starts", "Error", "originalMode", "Object", "assign", "keys", "for<PERSON>ach", "key", "begin", "relevance", "contains", "endsParent", "COMMENT", "className", "end", "excludeBegin", "variants", "HASH_COMMENT_MODE", "BACKSLASH_ESCAPE", "END_SAME_AS_BEGIN", "match", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/r.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: R\nDescription: R is a free software environment for statistical computing and graphics.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.r-project.org\nCategory: common,scientific\n*/\n\n/** @type LanguageFn */\nfunction r(hljs) {\n  // Identifiers in R cannot start with `_`, but they can start with `.` if it\n  // is not immediately followed by a digit.\n  // R also supports quoted identifiers, which are near-arbitrary sequences\n  // delimited by backticks (`…`), which may contain escape sequences. These are\n  // handled in a separate mode. See `test/markup/r/names.txt` for examples.\n  // FIXME: Support Unicode identifiers.\n  const IDENT_RE = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/;\n  const SIMPLE_IDENT = /[a-zA-Z][a-zA-Z_0-9]*/;\n\n  return {\n    name: 'R',\n\n    // only in Haskell, not R\n    illegal: /->/,\n    keywords: {\n      $pattern: IDENT_RE,\n      keyword:\n        'function if in break next repeat else for while',\n      literal:\n        'NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 ' +\n        'NA_character_|10 NA_complex_|10',\n      built_in:\n        // Builtin constants\n        'LETTERS letters month.abb month.name pi T F ' +\n        // Primitive functions\n        // These are all the functions in `base` that are implemented as a\n        // `.Primitive`, minus those functions that are also keywords.\n        'abs acos acosh all any anyNA Arg as.call as.character ' +\n        'as.complex as.double as.environment as.integer as.logical ' +\n        'as.null.default as.numeric as.raw asin asinh atan atanh attr ' +\n        'attributes baseenv browser c call ceiling class Conj cos cosh ' +\n        'cospi cummax cummin cumprod cumsum digamma dim dimnames ' +\n        'emptyenv exp expression floor forceAndCall gamma gc.time ' +\n        'globalenv Im interactive invisible is.array is.atomic is.call ' +\n        'is.character is.complex is.double is.environment is.expression ' +\n        'is.finite is.function is.infinite is.integer is.language ' +\n        'is.list is.logical is.matrix is.na is.name is.nan is.null ' +\n        'is.numeric is.object is.pairlist is.raw is.recursive is.single ' +\n        'is.symbol lazyLoadDBfetch length lgamma list log max min ' +\n        'missing Mod names nargs nzchar oldClass on.exit pos.to.env ' +\n        'proc.time prod quote range Re rep retracemem return round ' +\n        'seq_along seq_len seq.int sign signif sin sinh sinpi sqrt ' +\n        'standardGeneric substitute sum switch tan tanh tanpi tracemem ' +\n        'trigamma trunc unclass untracemem UseMethod xtfrm',\n    },\n    compilerExtensions: [\n      // allow beforeMatch to act as a \"qualifier\" for the match\n      // the full match begin must be [beforeMatch][begin]\n      (mode, parent) => {\n        if (!mode.beforeMatch) return;\n        // starts conflicts with endsParent which we need to make sure the child\n        // rule is not matched multiple times\n        if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n        const originalMode = Object.assign({}, mode);\n        Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n        mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n        mode.starts = {\n          relevance: 0,\n          contains: [\n            Object.assign(originalMode, { endsParent: true })\n          ]\n        };\n        mode.relevance = 0;\n\n        delete originalMode.beforeMatch;\n      }\n    ],\n    contains: [\n      // Roxygen comments\n      hljs.COMMENT(\n        /#'/,\n        /$/,\n        {\n          contains: [\n            {\n              // Handle `@examples` separately to cause all subsequent code\n              // until the next `@`-tag on its own line to be kept as-is,\n              // preventing highlighting. This code is example R code, so nested\n              // doctags shouldn’t be treated as such. See\n              // `test/markup/r/roxygen.txt` for an example.\n              className: 'doctag',\n              begin: '@examples',\n              starts: {\n                contains: [\n                  { begin: /\\n/ },\n                  {\n                    begin: /#'\\s*(?=@[a-zA-Z]+)/,\n                    endsParent: true,\n                  },\n                  {\n                    begin: /#'/,\n                    end: /$/,\n                    excludeBegin: true,\n                  }\n                ]\n              }\n            },\n            {\n              // Handle `@param` to highlight the parameter name following\n              // after.\n              className: 'doctag',\n              begin: '@param',\n              end: /$/,\n              contains: [\n                {\n                  className: 'variable',\n                  variants: [\n                    { begin: IDENT_RE },\n                    { begin: /`(?:\\\\.|[^`\\\\])+`/ }\n                  ],\n                  endsParent: true\n                }\n              ]\n            },\n            {\n              className: 'doctag',\n              begin: /@[a-zA-Z]+/\n            },\n            {\n              className: 'meta-keyword',\n              begin: /\\\\[a-zA-Z]+/,\n            }\n          ]\n        }\n      ),\n\n      hljs.HASH_COMMENT_MODE,\n\n      {\n        className: 'string',\n        contains: [hljs.BACKSLASH_ESCAPE],\n        variants: [\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]\"(-*)\\(/, end: /\\)(-*)\"/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]\"(-*)\\{/, end: /\\}(-*)\"/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]\"(-*)\\[/, end: /\\](-*)\"/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\\(/, end: /\\)(-*)'/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\\{/, end: /\\}(-*)'/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\\[/, end: /\\](-*)'/ }),\n          {begin: '\"', end: '\"', relevance: 0},\n          {begin: \"'\", end: \"'\", relevance: 0}\n        ],\n      },\n      {\n        className: 'number',\n        relevance: 0,\n        beforeMatch: /([^a-zA-Z0-9._])/, // not part of an identifier\n        variants: [\n          // TODO: replace with negative look-behind when available\n          // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/ },\n          // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/ },\n          // { begin: /(?<![a-zA-Z0-9._])(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/ }\n          {\n            // Special case: only hexadecimal binary powers can contain fractions.\n            match: /0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/,\n          },\n          {\n            match: /0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/\n          },\n          {\n            match: /(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/,\n          }\n        ],\n      },\n      {\n        // infix operator\n        begin: '%',\n        end: '%'\n      },\n      // relevance boost for assignment\n      {\n        begin: concat(SIMPLE_IDENT, \"\\\\s+<-\\\\s+\")\n      },\n      {\n        // escaped identifier\n        begin: '`',\n        end: '`',\n        contains: [\n          { begin: /\\\\./ }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = r;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOE,MAAM,CAAC,KAAK,EAAEF,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,CAACA,CAACC,IAAI,EAAE;EACf;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,QAAQ,GAAG,sDAAsD;EACvE,MAAMC,YAAY,GAAG,uBAAuB;EAE5C,OAAO;IACLC,IAAI,EAAE,GAAG;IAET;IACAC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;MACRC,QAAQ,EAAEL,QAAQ;MAClBM,OAAO,EACL,iDAAiD;MACnDC,OAAO,EACL,wDAAwD,GACxD,iCAAiC;MACnCC,QAAQ;MACN;MACA,8CAA8C;MAC9C;MACA;MACA;MACA,wDAAwD,GACxD,4DAA4D,GAC5D,+DAA+D,GAC/D,gEAAgE,GAChE,0DAA0D,GAC1D,2DAA2D,GAC3D,gEAAgE,GAChE,iEAAiE,GACjE,2DAA2D,GAC3D,4DAA4D,GAC5D,iEAAiE,GACjE,2DAA2D,GAC3D,6DAA6D,GAC7D,4DAA4D,GAC5D,4DAA4D,GAC5D,gEAAgE,GAChE;IACJ,CAAC;IACDC,kBAAkB,EAAE;IAClB;IACA;IACA,CAACC,IAAI,EAAEC,MAAM,KAAK;MAChB,IAAI,CAACD,IAAI,CAACE,WAAW,EAAE;MACvB;MACA;MACA,IAAIF,IAAI,CAACG,MAAM,EAAE,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;MAE1E,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,CAAC;MAC5CM,MAAM,CAACE,IAAI,CAACR,IAAI,CAAC,CAACS,OAAO,CAAEC,GAAG,IAAK;QAAE,OAAOV,IAAI,CAACU,GAAG,CAAC;MAAE,CAAC,CAAC;MAEzDV,IAAI,CAACW,KAAK,GAAG7B,MAAM,CAACuB,YAAY,CAACH,WAAW,EAAErB,SAAS,CAACwB,YAAY,CAACM,KAAK,CAAC,CAAC;MAC5EX,IAAI,CAACG,MAAM,GAAG;QACZS,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,CACRP,MAAM,CAACC,MAAM,CAACF,YAAY,EAAE;UAAES,UAAU,EAAE;QAAK,CAAC,CAAC;MAErD,CAAC;MACDd,IAAI,CAACY,SAAS,GAAG,CAAC;MAElB,OAAOP,YAAY,CAACH,WAAW;IACjC,CAAC,CACF;IACDW,QAAQ,EAAE;IACR;IACAxB,IAAI,CAAC0B,OAAO,CACV,IAAI,EACJ,GAAG,EACH;MACEF,QAAQ,EAAE,CACR;QACE;QACA;QACA;QACA;QACA;QACAG,SAAS,EAAE,QAAQ;QACnBL,KAAK,EAAE,WAAW;QAClBR,MAAM,EAAE;UACNU,QAAQ,EAAE,CACR;YAAEF,KAAK,EAAE;UAAK,CAAC,EACf;YACEA,KAAK,EAAE,qBAAqB;YAC5BG,UAAU,EAAE;UACd,CAAC,EACD;YACEH,KAAK,EAAE,IAAI;YACXM,GAAG,EAAE,GAAG;YACRC,YAAY,EAAE;UAChB,CAAC;QAEL;MACF,CAAC,EACD;QACE;QACA;QACAF,SAAS,EAAE,QAAQ;QACnBL,KAAK,EAAE,QAAQ;QACfM,GAAG,EAAE,GAAG;QACRJ,QAAQ,EAAE,CACR;UACEG,SAAS,EAAE,UAAU;UACrBG,QAAQ,EAAE,CACR;YAAER,KAAK,EAAErB;UAAS,CAAC,EACnB;YAAEqB,KAAK,EAAE;UAAoB,CAAC,CAC/B;UACDG,UAAU,EAAE;QACd,CAAC;MAEL,CAAC,EACD;QACEE,SAAS,EAAE,QAAQ;QACnBL,KAAK,EAAE;MACT,CAAC,EACD;QACEK,SAAS,EAAE,cAAc;QACzBL,KAAK,EAAE;MACT,CAAC;IAEL,CACF,CAAC,EAEDtB,IAAI,CAAC+B,iBAAiB,EAEtB;MACEJ,SAAS,EAAE,QAAQ;MACnBH,QAAQ,EAAE,CAACxB,IAAI,CAACgC,gBAAgB,CAAC;MACjCF,QAAQ,EAAE,CACR9B,IAAI,CAACiC,iBAAiB,CAAC;QAAEX,KAAK,EAAE,aAAa;QAAEM,GAAG,EAAE;MAAU,CAAC,CAAC,EAChE5B,IAAI,CAACiC,iBAAiB,CAAC;QAAEX,KAAK,EAAE,aAAa;QAAEM,GAAG,EAAE;MAAU,CAAC,CAAC,EAChE5B,IAAI,CAACiC,iBAAiB,CAAC;QAAEX,KAAK,EAAE,aAAa;QAAEM,GAAG,EAAE;MAAU,CAAC,CAAC,EAChE5B,IAAI,CAACiC,iBAAiB,CAAC;QAAEX,KAAK,EAAE,aAAa;QAAEM,GAAG,EAAE;MAAU,CAAC,CAAC,EAChE5B,IAAI,CAACiC,iBAAiB,CAAC;QAAEX,KAAK,EAAE,aAAa;QAAEM,GAAG,EAAE;MAAU,CAAC,CAAC,EAChE5B,IAAI,CAACiC,iBAAiB,CAAC;QAAEX,KAAK,EAAE,aAAa;QAAEM,GAAG,EAAE;MAAU,CAAC,CAAC,EAChE;QAACN,KAAK,EAAE,GAAG;QAAEM,GAAG,EAAE,GAAG;QAAEL,SAAS,EAAE;MAAC,CAAC,EACpC;QAACD,KAAK,EAAE,GAAG;QAAEM,GAAG,EAAE,GAAG;QAAEL,SAAS,EAAE;MAAC,CAAC;IAExC,CAAC,EACD;MACEI,SAAS,EAAE,QAAQ;MACnBJ,SAAS,EAAE,CAAC;MACZV,WAAW,EAAE,kBAAkB;MAAE;MACjCiB,QAAQ,EAAE;MACR;MACA;MACA;MACA;MACA;QACE;QACAI,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACD;MACE;MACAZ,KAAK,EAAE,GAAG;MACVM,GAAG,EAAE;IACP,CAAC;IACD;IACA;MACEN,KAAK,EAAE7B,MAAM,CAACS,YAAY,EAAE,YAAY;IAC1C,CAAC,EACD;MACE;MACAoB,KAAK,EAAE,GAAG;MACVM,GAAG,EAAE,GAAG;MACRJ,QAAQ,EAAE,CACR;QAAEF,KAAK,EAAE;MAAM,CAAC;IAEpB,CAAC;EAEL,CAAC;AACH;AAEAa,MAAM,CAACC,OAAO,GAAGrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}