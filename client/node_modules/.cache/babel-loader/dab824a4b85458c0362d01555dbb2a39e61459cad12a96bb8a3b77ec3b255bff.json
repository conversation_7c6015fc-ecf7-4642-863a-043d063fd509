{"ast": null, "code": "/*\nLanguage: VBScript in HTML\nRequires: xml.js, vbscript.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: \"Bridge\" language defining fragments of VBScript in HTML within <% .. %>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\nfunction vbscriptHtml(hljs) {\n  return {\n    name: 'VBScript in HTML',\n    subLanguage: 'xml',\n    contains: [{\n      begin: '<%',\n      end: '%>',\n      subLanguage: 'vbscript'\n    }]\n  };\n}\nmodule.exports = vbscriptHtml;", "map": {"version": 3, "names": ["vbscriptHtml", "hljs", "name", "subLanguage", "contains", "begin", "end", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/vbscript-html.js"], "sourcesContent": ["/*\nLanguage: VBScript in HTML\nRequires: xml.js, vbscript.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: \"Bridge\" language defining fragments of VBScript in HTML within <% .. %>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\nfunction vbscriptHtml(hljs) {\n  return {\n    name: 'VBScript in HTML',\n    subLanguage: 'xml',\n    contains: [\n      {\n        begin: '<%',\n        end: '%>',\n        subLanguage: 'vbscript'\n      }\n    ]\n  };\n}\n\nmodule.exports = vbscriptHtml;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC1B,OAAO;IACLC,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTH,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;AACH;AAEAI,MAAM,CAACC,OAAO,GAAGR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}