{"ast": null, "code": "'use strict';\n\nvar refractorHaskell = require('./haskell.js');\nmodule.exports = idris;\nidris.displayName = 'idris';\nidris.aliases = ['idr'];\nfunction idris(Prism) {\n  Prism.register(refractorHaskell);\n  Prism.languages.idris = Prism.languages.extend('haskell', {\n    comment: {\n      pattern: /(?:(?:--|\\|\\|\\|).*$|\\{-[\\s\\S]*?-\\})/m\n    },\n    keyword: /\\b(?:Type|case|class|codata|constructor|corecord|data|do|dsl|else|export|if|implementation|implicit|import|impossible|in|infix|infixl|infixr|instance|interface|let|module|mutual|namespace|of|parameters|partial|postulate|private|proof|public|quoteGoal|record|rewrite|syntax|then|total|using|where|with)\\b/,\n    builtin: undefined\n  });\n  Prism.languages.insertBefore('idris', 'keyword', {\n    'import-statement': {\n      pattern: /(^\\s*import\\s+)(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*/m,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    }\n  });\n  Prism.languages.idr = Prism.languages.idris;\n}", "map": {"version": 3, "names": ["refractorHaskell", "require", "module", "exports", "idris", "displayName", "aliases", "Prism", "register", "languages", "extend", "comment", "pattern", "keyword", "builtin", "undefined", "insertBefore", "lookbehind", "inside", "punctuation", "idr"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/idris.js"], "sourcesContent": ["'use strict'\nvar refractorHaskell = require('./haskell.js')\nmodule.exports = idris\nidris.displayName = 'idris'\nidris.aliases = ['idr']\nfunction idris(Prism) {\n  Prism.register(refractorHaskell)\n  Prism.languages.idris = Prism.languages.extend('haskell', {\n    comment: {\n      pattern: /(?:(?:--|\\|\\|\\|).*$|\\{-[\\s\\S]*?-\\})/m\n    },\n    keyword:\n      /\\b(?:Type|case|class|codata|constructor|corecord|data|do|dsl|else|export|if|implementation|implicit|import|impossible|in|infix|infixl|infixr|instance|interface|let|module|mutual|namespace|of|parameters|partial|postulate|private|proof|public|quoteGoal|record|rewrite|syntax|then|total|using|where|with)\\b/,\n    builtin: undefined\n  })\n  Prism.languages.insertBefore('idris', 'keyword', {\n    'import-statement': {\n      pattern: /(^\\s*import\\s+)(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*/m,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    }\n  })\n  Prism.languages.idr = Prism.languages.idris\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC9CC,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AACvB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,QAAQ,CAACR,gBAAgB,CAAC;EAChCO,KAAK,CAACE,SAAS,CAACL,KAAK,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,SAAS,EAAE;IACxDC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EACL,iTAAiT;IACnTC,OAAO,EAAEC;EACX,CAAC,CAAC;EACFR,KAAK,CAACE,SAAS,CAACO,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE;IAC/C,kBAAkB,EAAE;MAClBJ,OAAO,EAAE,mDAAmD;MAC5DK,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF;EACF,CAAC,CAAC;EACFZ,KAAK,CAACE,SAAS,CAACW,GAAG,GAAGb,KAAK,CAACE,SAAS,CAACL,KAAK;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}