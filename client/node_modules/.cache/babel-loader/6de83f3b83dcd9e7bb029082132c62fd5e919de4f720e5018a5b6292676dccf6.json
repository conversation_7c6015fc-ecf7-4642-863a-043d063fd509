{"ast": null, "code": "'use strict';\n\nmodule.exports = wasm;\nwasm.displayName = 'wasm';\nwasm.aliases = [];\nfunction wasm(Prism) {\n  Prism.languages.wasm = {\n    comment: [/\\(;[\\s\\S]*?;\\)/, {\n      pattern: /;;.*/,\n      greedy: true\n    }],\n    string: {\n      pattern: /\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      greedy: true\n    },\n    keyword: [{\n      pattern: /\\b(?:align|offset)=/,\n      inside: {\n        operator: /=/\n      }\n    }, {\n      pattern: /\\b(?:(?:f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|neg?|nearest|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|sqrt|store(?:8|16|32)?|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))?|memory\\.(?:grow|size))\\b/,\n      inside: {\n        punctuation: /\\./\n      }\n    }, /\\b(?:anyfunc|block|br(?:_if|_table)?|call(?:_indirect)?|data|drop|elem|else|end|export|func|get_(?:global|local)|global|if|import|local|loop|memory|module|mut|nop|offset|param|result|return|select|set_(?:global|local)|start|table|tee_local|then|type|unreachable)\\b/],\n    variable: /\\$[\\w!#$%&'*+\\-./:<=>?@\\\\^`|~]+/,\n    number: /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/,\n    punctuation: /[()]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "wasm", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "keyword", "inside", "operator", "punctuation", "variable", "number"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/wasm.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wasm\nwasm.displayName = 'wasm'\nwasm.aliases = []\nfunction wasm(Prism) {\n  Prism.languages.wasm = {\n    comment: [\n      /\\(;[\\s\\S]*?;\\)/,\n      {\n        pattern: /;;.*/,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      greedy: true\n    },\n    keyword: [\n      {\n        pattern: /\\b(?:align|offset)=/,\n        inside: {\n          operator: /=/\n        }\n      },\n      {\n        pattern:\n          /\\b(?:(?:f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|neg?|nearest|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|sqrt|store(?:8|16|32)?|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))?|memory\\.(?:grow|size))\\b/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      /\\b(?:anyfunc|block|br(?:_if|_table)?|call(?:_indirect)?|data|drop|elem|else|end|export|func|get_(?:global|local)|global|if|import|local|loop|memory|module|mut|nop|offset|param|result|return|select|set_(?:global|local)|start|table|tee_local|then|type|unreachable)\\b/\n    ],\n    variable: /\\$[\\w!#$%&'*+\\-./:<=>?@\\\\^`|~]+/,\n    number:\n      /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/,\n    punctuation: /[()]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,OAAO,EAAE,CACP,gBAAgB,EAChB;MACEC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,wBAAwB;MACjCC,MAAM,EAAE;IACV,CAAC;IACDE,OAAO,EAAE,CACP;MACEH,OAAO,EAAE,qBAAqB;MAC9BI,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC,EACD;MACEL,OAAO,EACL,gbAAgb;MAClbI,MAAM,EAAE;QACNE,WAAW,EAAE;MACf;IACF,CAAC,EACD,0QAA0Q,CAC3Q;IACDC,QAAQ,EAAE,iCAAiC;IAC3CC,MAAM,EACJ,iNAAiN;IACnNF,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}