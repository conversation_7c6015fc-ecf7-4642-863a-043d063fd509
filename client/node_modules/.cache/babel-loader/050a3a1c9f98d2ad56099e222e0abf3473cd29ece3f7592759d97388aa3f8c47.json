{"ast": null, "code": "'use strict';\n\nmodule.exports = aql;\naql.displayName = 'aql';\naql.aliases = [];\nfunction aql(Prism) {\n  Prism.languages.aql = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    property: {\n      pattern: /([{,]\\s*)(?:(?!\\d)\\w+|([\"'´`])(?:(?!\\2)[^\\\\\\r\\n]|\\\\.)*\\2)(?=\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    identifier: {\n      pattern: /([´`])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    variable: /@@?\\w+/,\n    keyword: [{\n      pattern: /(\\bWITH\\s+)COUNT(?=\\s+INTO\\b)/i,\n      lookbehind: true\n    }, /\\b(?:AGGREGATE|ALL|AND|ANY|ASC|COLLECT|DESC|DISTINCT|FILTER|FOR|GRAPH|IN|INBOUND|INSERT|INTO|K_PATHS|K_SHORTEST_PATHS|LET|LIKE|LIMIT|NONE|NOT|NULL|OR|OUTBOUND|REMOVE|REPLACE|RETURN|SHORTEST_PATH|SORT|UPDATE|UPSERT|WINDOW|WITH)\\b/i,\n    // pseudo keywords get a lookbehind to avoid false positives\n    {\n      pattern: /(^|[^\\w.[])(?:KEEP|PRUNE|SEARCH|TO)\\b/i,\n      lookbehind: true\n    }, {\n      pattern: /(^|[^\\w.[])(?:CURRENT|NEW|OLD)\\b/,\n      lookbehind: true\n    }, {\n      pattern: /\\bOPTIONS(?=\\s*\\{)/i\n    }],\n    function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n    boolean: /\\b(?:false|true)\\b/i,\n    range: {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    },\n    number: [/\\b0b[01]+/i, /\\b0x[0-9a-f]+/i, /(?:\\B\\.\\d+|\\b(?:0|[1-9]\\d*)(?:\\.\\d+)?)(?:e[+-]?\\d+)?/i],\n    operator: /\\*{2,}|[=!]~|[!=<>]=?|&&|\\|\\||[-+*/%]/,\n    punctuation: /::|[?.:,;()[\\]{}]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "aql", "displayName", "aliases", "Prism", "languages", "comment", "property", "pattern", "lookbehind", "greedy", "string", "identifier", "variable", "keyword", "function", "boolean", "range", "alias", "number", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/aql.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = aql\naql.displayName = 'aql'\naql.aliases = []\nfunction aql(Prism) {\n  Prism.languages.aql = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    property: {\n      pattern:\n        /([{,]\\s*)(?:(?!\\d)\\w+|([\"'´`])(?:(?!\\2)[^\\\\\\r\\n]|\\\\.)*\\2)(?=\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    identifier: {\n      pattern: /([´`])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    variable: /@@?\\w+/,\n    keyword: [\n      {\n        pattern: /(\\bWITH\\s+)COUNT(?=\\s+INTO\\b)/i,\n        lookbehind: true\n      },\n      /\\b(?:AGGREGATE|ALL|AND|ANY|ASC|COLLECT|DESC|DISTINCT|FILTER|FOR|GRAPH|IN|INBOUND|INSERT|INTO|K_PATHS|K_SHORTEST_PATHS|LET|LIKE|LIMIT|NONE|NOT|NULL|OR|OUTBOUND|REMOVE|REPLACE|RETURN|SHORTEST_PATH|SORT|UPDATE|UPSERT|WINDOW|WITH)\\b/i, // pseudo keywords get a lookbehind to avoid false positives\n      {\n        pattern: /(^|[^\\w.[])(?:KEEP|PRUNE|SEARCH|TO)\\b/i,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\w.[])(?:CURRENT|NEW|OLD)\\b/,\n        lookbehind: true\n      },\n      {\n        pattern: /\\bOPTIONS(?=\\s*\\{)/i\n      }\n    ],\n    function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n    boolean: /\\b(?:false|true)\\b/i,\n    range: {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    },\n    number: [\n      /\\b0b[01]+/i,\n      /\\b0x[0-9a-f]+/i,\n      /(?:\\B\\.\\d+|\\b(?:0|[1-9]\\d*)(?:\\.\\d+)?)(?:e[+-]?\\d+)?/i\n    ],\n    operator: /\\*{2,}|[=!]~|[!=<>]=?|&&|\\|\\||[-+*/%]/,\n    punctuation: /::|[?.:,;()[\\]{}]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE,yBAAyB;IAClCC,QAAQ,EAAE;MACRC,OAAO,EACL,mEAAmE;MACrEC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,kCAAkC;MAC3CE,MAAM,EAAE;IACV,CAAC;IACDE,UAAU,EAAE;MACVJ,OAAO,EAAE,kCAAkC;MAC3CE,MAAM,EAAE;IACV,CAAC;IACDG,QAAQ,EAAE,QAAQ;IAClBC,OAAO,EAAE,CACP;MACEN,OAAO,EAAE,gCAAgC;MACzCC,UAAU,EAAE;IACd,CAAC,EACD,uOAAuO;IAAE;IACzO;MACED,OAAO,EAAE,wCAAwC;MACjDC,UAAU,EAAE;IACd,CAAC,EACD;MACED,OAAO,EAAE,kCAAkC;MAC3CC,UAAU,EAAE;IACd,CAAC,EACD;MACED,OAAO,EAAE;IACX,CAAC,CACF;IACDO,QAAQ,EAAE,sBAAsB;IAChCC,OAAO,EAAE,qBAAqB;IAC9BC,KAAK,EAAE;MACLT,OAAO,EAAE,MAAM;MACfU,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE,CACN,YAAY,EACZ,gBAAgB,EAChB,uDAAuD,CACxD;IACDC,QAAQ,EAAE,uCAAuC;IACjDC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}