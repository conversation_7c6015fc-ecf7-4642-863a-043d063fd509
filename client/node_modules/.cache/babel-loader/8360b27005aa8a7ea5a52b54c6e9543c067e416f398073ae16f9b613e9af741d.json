{"ast": null, "code": "/*\nLanguage: Clojure\nDescription: Clojure syntax (based on lisp.js)\nAuthor: mfornos\nWebsite: https://clojure.org\nCategory: lisp\n*/\n\n/** @type LanguageFn */\nfunction clojure(hljs) {\n  const SYMBOLSTART = 'a-zA-Z_\\\\-!.?+*=<>&#\\'';\n  const SYMBOL_RE = '[' + SYMBOLSTART + '][' + SYMBOLSTART + '0-9/;:]*';\n  const globals = 'def defonce defprotocol defstruct defmulti defmethod defn- defn defmacro deftype defrecord';\n  const keywords = {\n    $pattern: SYMBOL_RE,\n    'builtin-name':\n    // Clojure keywords\n    globals + ' ' + 'cond apply if-not if-let if not not= =|0 <|0 >|0 <=|0 >=|0 ==|0 +|0 /|0 *|0 -|0 rem ' + 'quot neg? pos? delay? symbol? keyword? true? false? integer? empty? coll? list? ' + 'set? ifn? fn? associative? sequential? sorted? counted? reversible? number? decimal? ' + 'class? distinct? isa? float? rational? reduced? ratio? odd? even? char? seq? vector? ' + 'string? map? nil? contains? zero? instance? not-every? not-any? libspec? -> ->> .. . ' + 'inc compare do dotimes mapcat take remove take-while drop letfn drop-last take-last ' + 'drop-while while intern condp case reduced cycle split-at split-with repeat replicate ' + 'iterate range merge zipmap declare line-seq sort comparator sort-by dorun doall nthnext ' + 'nthrest partition eval doseq await await-for let agent atom send send-off release-pending-sends ' + 'add-watch mapv filterv remove-watch agent-error restart-agent set-error-handler error-handler ' + 'set-error-mode! error-mode shutdown-agents quote var fn loop recur throw try monitor-enter ' + 'monitor-exit macroexpand macroexpand-1 for dosync and or ' + 'when when-not when-let comp juxt partial sequence memoize constantly complement identity assert ' + 'peek pop doto proxy first rest cons cast coll last butlast ' + 'sigs reify second ffirst fnext nfirst nnext meta with-meta ns in-ns create-ns import ' + 'refer keys select-keys vals key val rseq name namespace promise into transient persistent! conj! ' + 'assoc! dissoc! pop! disj! use class type num float double short byte boolean bigint biginteger ' + 'bigdec print-method print-dup throw-if printf format load compile get-in update-in pr pr-on newline ' + 'flush read slurp read-line subvec with-open memfn time re-find re-groups rand-int rand mod locking ' + 'assert-valid-fdecl alias resolve ref deref refset swap! reset! set-validator! compare-and-set! alter-meta! ' + 'reset-meta! commute get-validator alter ref-set ref-history-count ref-min-history ref-max-history ensure sync io! ' + 'new next conj set! to-array future future-call into-array aset gen-class reduce map filter find empty ' + 'hash-map hash-set sorted-map sorted-map-by sorted-set sorted-set-by vec vector seq flatten reverse assoc dissoc list ' + 'disj get union difference intersection extend extend-type extend-protocol int nth delay count concat chunk chunk-buffer ' + 'chunk-append chunk-first chunk-rest max min dec unchecked-inc-int unchecked-inc unchecked-dec-inc unchecked-dec unchecked-negate ' + 'unchecked-add-int unchecked-add unchecked-subtract-int unchecked-subtract chunk-next chunk-cons chunked-seq? prn vary-meta ' + 'lazy-seq spread list* str find-keyword keyword symbol gensym force rationalize'\n  };\n  const SIMPLE_NUMBER_RE = '[-+]?\\\\d+(\\\\.\\\\d+)?';\n  const SYMBOL = {\n    begin: SYMBOL_RE,\n    relevance: 0\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: SIMPLE_NUMBER_RE,\n    relevance: 0\n  };\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  const COMMENT = hljs.COMMENT(';', '$', {\n    relevance: 0\n  });\n  const LITERAL = {\n    className: 'literal',\n    begin: /\\b(true|false|nil)\\b/\n  };\n  const COLLECTION = {\n    begin: '[\\\\[\\\\{]',\n    end: '[\\\\]\\\\}]'\n  };\n  const HINT = {\n    className: 'comment',\n    begin: '\\\\^' + SYMBOL_RE\n  };\n  const HINT_COL = hljs.COMMENT('\\\\^\\\\{', '\\\\}');\n  const KEY = {\n    className: 'symbol',\n    begin: '[:]{1,2}' + SYMBOL_RE\n  };\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n  const BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  const NAME = {\n    keywords: keywords,\n    className: 'name',\n    begin: SYMBOL_RE,\n    relevance: 0,\n    starts: BODY\n  };\n  const DEFAULT_CONTAINS = [LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL, SYMBOL];\n  const GLOBAL = {\n    beginKeywords: globals,\n    lexemes: SYMBOL_RE,\n    end: '(\\\\[|#|\\\\d|\"|:|\\\\{|\\\\)|\\\\(|$)',\n    contains: [{\n      className: 'title',\n      begin: SYMBOL_RE,\n      relevance: 0,\n      excludeEnd: true,\n      // we can only have a single title\n      endsParent: true\n    }].concat(DEFAULT_CONTAINS)\n  };\n  LIST.contains = [hljs.COMMENT('comment', ''), GLOBAL, NAME, BODY];\n  BODY.contains = DEFAULT_CONTAINS;\n  COLLECTION.contains = DEFAULT_CONTAINS;\n  HINT_COL.contains = [COLLECTION];\n  return {\n    name: 'Clojure',\n    aliases: ['clj'],\n    illegal: /\\S/,\n    contains: [LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL]\n  };\n}\nmodule.exports = clojure;", "map": {"version": 3, "names": ["clojure", "hljs", "SYMBOLSTART", "SYMBOL_RE", "globals", "keywords", "$pattern", "SIMPLE_NUMBER_RE", "SYMBOL", "begin", "relevance", "NUMBER", "className", "STRING", "inherit", "QUOTE_STRING_MODE", "illegal", "COMMENT", "LITERAL", "COLLECTION", "end", "HINT", "HINT_COL", "KEY", "LIST", "BODY", "endsWithParent", "NAME", "starts", "DEFAULT_CONTAINS", "GLOBAL", "beginKeywords", "lexemes", "contains", "excludeEnd", "endsParent", "concat", "name", "aliases", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/clojure.js"], "sourcesContent": ["/*\nLanguage: Clojure\nDescription: Clojure syntax (based on lisp.js)\nAuthor: mfornos\nWebsite: https://clojure.org\nCategory: lisp\n*/\n\n/** @type LanguageFn */\nfunction clojure(hljs) {\n  const SYMBOLSTART = 'a-zA-Z_\\\\-!.?+*=<>&#\\'';\n  const SYMBOL_RE = '[' + SYMBOLSTART + '][' + SYMBOLSTART + '0-9/;:]*';\n  const globals = 'def defonce defprotocol defstruct defmulti defmethod defn- defn defmacro deftype defrecord';\n  const keywords = {\n    $pattern: SYMBOL_RE,\n    'builtin-name':\n      // Clojure keywords\n      globals + ' ' +\n      'cond apply if-not if-let if not not= =|0 <|0 >|0 <=|0 >=|0 ==|0 +|0 /|0 *|0 -|0 rem ' +\n      'quot neg? pos? delay? symbol? keyword? true? false? integer? empty? coll? list? ' +\n      'set? ifn? fn? associative? sequential? sorted? counted? reversible? number? decimal? ' +\n      'class? distinct? isa? float? rational? reduced? ratio? odd? even? char? seq? vector? ' +\n      'string? map? nil? contains? zero? instance? not-every? not-any? libspec? -> ->> .. . ' +\n      'inc compare do dotimes mapcat take remove take-while drop letfn drop-last take-last ' +\n      'drop-while while intern condp case reduced cycle split-at split-with repeat replicate ' +\n      'iterate range merge zipmap declare line-seq sort comparator sort-by dorun doall nthnext ' +\n      'nthrest partition eval doseq await await-for let agent atom send send-off release-pending-sends ' +\n      'add-watch mapv filterv remove-watch agent-error restart-agent set-error-handler error-handler ' +\n      'set-error-mode! error-mode shutdown-agents quote var fn loop recur throw try monitor-enter ' +\n      'monitor-exit macroexpand macroexpand-1 for dosync and or ' +\n      'when when-not when-let comp juxt partial sequence memoize constantly complement identity assert ' +\n      'peek pop doto proxy first rest cons cast coll last butlast ' +\n      'sigs reify second ffirst fnext nfirst nnext meta with-meta ns in-ns create-ns import ' +\n      'refer keys select-keys vals key val rseq name namespace promise into transient persistent! conj! ' +\n      'assoc! dissoc! pop! disj! use class type num float double short byte boolean bigint biginteger ' +\n      'bigdec print-method print-dup throw-if printf format load compile get-in update-in pr pr-on newline ' +\n      'flush read slurp read-line subvec with-open memfn time re-find re-groups rand-int rand mod locking ' +\n      'assert-valid-fdecl alias resolve ref deref refset swap! reset! set-validator! compare-and-set! alter-meta! ' +\n      'reset-meta! commute get-validator alter ref-set ref-history-count ref-min-history ref-max-history ensure sync io! ' +\n      'new next conj set! to-array future future-call into-array aset gen-class reduce map filter find empty ' +\n      'hash-map hash-set sorted-map sorted-map-by sorted-set sorted-set-by vec vector seq flatten reverse assoc dissoc list ' +\n      'disj get union difference intersection extend extend-type extend-protocol int nth delay count concat chunk chunk-buffer ' +\n      'chunk-append chunk-first chunk-rest max min dec unchecked-inc-int unchecked-inc unchecked-dec-inc unchecked-dec unchecked-negate ' +\n      'unchecked-add-int unchecked-add unchecked-subtract-int unchecked-subtract chunk-next chunk-cons chunked-seq? prn vary-meta ' +\n      'lazy-seq spread list* str find-keyword keyword symbol gensym force rationalize'\n  };\n\n  const SIMPLE_NUMBER_RE = '[-+]?\\\\d+(\\\\.\\\\d+)?';\n\n  const SYMBOL = {\n    begin: SYMBOL_RE,\n    relevance: 0\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: SIMPLE_NUMBER_RE,\n    relevance: 0\n  };\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  const COMMENT = hljs.COMMENT(\n    ';',\n    '$',\n    {\n      relevance: 0\n    }\n  );\n  const LITERAL = {\n    className: 'literal',\n    begin: /\\b(true|false|nil)\\b/\n  };\n  const COLLECTION = {\n    begin: '[\\\\[\\\\{]',\n    end: '[\\\\]\\\\}]'\n  };\n  const HINT = {\n    className: 'comment',\n    begin: '\\\\^' + SYMBOL_RE\n  };\n  const HINT_COL = hljs.COMMENT('\\\\^\\\\{', '\\\\}');\n  const KEY = {\n    className: 'symbol',\n    begin: '[:]{1,2}' + SYMBOL_RE\n  };\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n  const BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  const NAME = {\n    keywords: keywords,\n    className: 'name',\n    begin: SYMBOL_RE,\n    relevance: 0,\n    starts: BODY\n  };\n  const DEFAULT_CONTAINS = [\n    LIST,\n    STRING,\n    HINT,\n    HINT_COL,\n    COMMENT,\n    KEY,\n    COLLECTION,\n    NUMBER,\n    LITERAL,\n    SYMBOL\n  ];\n\n  const GLOBAL = {\n    beginKeywords: globals,\n    lexemes: SYMBOL_RE,\n    end: '(\\\\[|#|\\\\d|\"|:|\\\\{|\\\\)|\\\\(|$)',\n    contains: [\n      {\n        className: 'title',\n        begin: SYMBOL_RE,\n        relevance: 0,\n        excludeEnd: true,\n        // we can only have a single title\n        endsParent: true\n      }\n    ].concat(DEFAULT_CONTAINS)\n  };\n\n  LIST.contains = [\n    hljs.COMMENT('comment', ''),\n    GLOBAL,\n    NAME,\n    BODY\n  ];\n  BODY.contains = DEFAULT_CONTAINS;\n  COLLECTION.contains = DEFAULT_CONTAINS;\n  HINT_COL.contains = [ COLLECTION ];\n\n  return {\n    name: 'Clojure',\n    aliases: [ 'clj' ],\n    illegal: /\\S/,\n    contains: [\n      LIST,\n      STRING,\n      HINT,\n      HINT_COL,\n      COMMENT,\n      KEY,\n      COLLECTION,\n      NUMBER,\n      LITERAL\n    ]\n  };\n}\n\nmodule.exports = clojure;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,WAAW,GAAG,wBAAwB;EAC5C,MAAMC,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,IAAI,GAAGA,WAAW,GAAG,UAAU;EACrE,MAAME,OAAO,GAAG,4FAA4F;EAC5G,MAAMC,QAAQ,GAAG;IACfC,QAAQ,EAAEH,SAAS;IACnB,cAAc;IACZ;IACAC,OAAO,GAAG,GAAG,GACb,sFAAsF,GACtF,kFAAkF,GAClF,uFAAuF,GACvF,uFAAuF,GACvF,uFAAuF,GACvF,sFAAsF,GACtF,wFAAwF,GACxF,0FAA0F,GAC1F,kGAAkG,GAClG,gGAAgG,GAChG,6FAA6F,GAC7F,2DAA2D,GAC3D,kGAAkG,GAClG,6DAA6D,GAC7D,uFAAuF,GACvF,mGAAmG,GACnG,iGAAiG,GACjG,sGAAsG,GACtG,qGAAqG,GACrG,6GAA6G,GAC7G,oHAAoH,GACpH,wGAAwG,GACxG,uHAAuH,GACvH,0HAA0H,GAC1H,mIAAmI,GACnI,6HAA6H,GAC7H;EACJ,CAAC;EAED,MAAMG,gBAAgB,GAAG,qBAAqB;EAE9C,MAAMC,MAAM,GAAG;IACbC,KAAK,EAAEN,SAAS;IAChBO,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBH,KAAK,EAAEF,gBAAgB;IACvBG,SAAS,EAAE;EACb,CAAC;EACD,MAAMG,MAAM,GAAGZ,IAAI,CAACa,OAAO,CAACb,IAAI,CAACc,iBAAiB,EAAE;IAClDC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGhB,IAAI,CAACgB,OAAO,CAC1B,GAAG,EACH,GAAG,EACH;IACEP,SAAS,EAAE;EACb,CACF,CAAC;EACD,MAAMQ,OAAO,GAAG;IACdN,SAAS,EAAE,SAAS;IACpBH,KAAK,EAAE;EACT,CAAC;EACD,MAAMU,UAAU,GAAG;IACjBV,KAAK,EAAE,UAAU;IACjBW,GAAG,EAAE;EACP,CAAC;EACD,MAAMC,IAAI,GAAG;IACXT,SAAS,EAAE,SAAS;IACpBH,KAAK,EAAE,KAAK,GAAGN;EACjB,CAAC;EACD,MAAMmB,QAAQ,GAAGrB,IAAI,CAACgB,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC;EAC9C,MAAMM,GAAG,GAAG;IACVX,SAAS,EAAE,QAAQ;IACnBH,KAAK,EAAE,UAAU,GAAGN;EACtB,CAAC;EACD,MAAMqB,IAAI,GAAG;IACXf,KAAK,EAAE,KAAK;IACZW,GAAG,EAAE;EACP,CAAC;EACD,MAAMK,IAAI,GAAG;IACXC,cAAc,EAAE,IAAI;IACpBhB,SAAS,EAAE;EACb,CAAC;EACD,MAAMiB,IAAI,GAAG;IACXtB,QAAQ,EAAEA,QAAQ;IAClBO,SAAS,EAAE,MAAM;IACjBH,KAAK,EAAEN,SAAS;IAChBO,SAAS,EAAE,CAAC;IACZkB,MAAM,EAAEH;EACV,CAAC;EACD,MAAMI,gBAAgB,GAAG,CACvBL,IAAI,EACJX,MAAM,EACNQ,IAAI,EACJC,QAAQ,EACRL,OAAO,EACPM,GAAG,EACHJ,UAAU,EACVR,MAAM,EACNO,OAAO,EACPV,MAAM,CACP;EAED,MAAMsB,MAAM,GAAG;IACbC,aAAa,EAAE3B,OAAO;IACtB4B,OAAO,EAAE7B,SAAS;IAClBiB,GAAG,EAAE,+BAA+B;IACpCa,QAAQ,EAAE,CACR;MACErB,SAAS,EAAE,OAAO;MAClBH,KAAK,EAAEN,SAAS;MAChBO,SAAS,EAAE,CAAC;MACZwB,UAAU,EAAE,IAAI;MAChB;MACAC,UAAU,EAAE;IACd,CAAC,CACF,CAACC,MAAM,CAACP,gBAAgB;EAC3B,CAAC;EAEDL,IAAI,CAACS,QAAQ,GAAG,CACdhC,IAAI,CAACgB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAC3Ba,MAAM,EACNH,IAAI,EACJF,IAAI,CACL;EACDA,IAAI,CAACQ,QAAQ,GAAGJ,gBAAgB;EAChCV,UAAU,CAACc,QAAQ,GAAGJ,gBAAgB;EACtCP,QAAQ,CAACW,QAAQ,GAAG,CAAEd,UAAU,CAAE;EAElC,OAAO;IACLkB,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,CAAE,KAAK,CAAE;IAClBtB,OAAO,EAAE,IAAI;IACbiB,QAAQ,EAAE,CACRT,IAAI,EACJX,MAAM,EACNQ,IAAI,EACJC,QAAQ,EACRL,OAAO,EACPM,GAAG,EACHJ,UAAU,EACVR,MAAM,EACNO,OAAO;EAEX,CAAC;AACH;AAEAqB,MAAM,CAACC,OAAO,GAAGxC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}