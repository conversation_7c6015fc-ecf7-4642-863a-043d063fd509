{"ast": null, "code": "/*\nLanguage: SQF\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Scripting language for the Arma game series\nWebsite: https://community.bistudio.com/wiki/SQF_syntax\nCategory: scripting\n*/\n\nfunction sqf(hljs) {\n  // In SQF, a variable start with _\n  const VARIABLE = {\n    className: 'variable',\n    begin: /\\b_+[a-zA-Z]\\w*/\n  };\n\n  // In SQF, a function should fit myTag_fnc_myFunction pattern\n  // https://community.bistudio.com/wiki/Functions_Library_(Arma_3)#Adding_a_Function\n  const FUNCTION = {\n    className: 'title',\n    begin: /[a-zA-Z][a-zA-Z0-9]+_fnc_\\w*/\n  };\n\n  // In SQF strings, quotes matching the start are escaped by adding a consecutive.\n  // Example of single escaped quotes: \" \"\" \" and  ' '' '.\n  const STRINGS = {\n    className: 'string',\n    variants: [{\n      begin: '\"',\n      end: '\"',\n      contains: [{\n        begin: '\"\"',\n        relevance: 0\n      }]\n    }, {\n      begin: '\\'',\n      end: '\\'',\n      contains: [{\n        begin: '\\'\\'',\n        relevance: 0\n      }]\n    }]\n  };\n\n  // list of keywords from:\n  // https://community.bistudio.com/wiki/PreProcessor_Commands\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword': 'define undef ifdef ifndef else endif include'\n    },\n    contains: [{\n      begin: /\\\\\\n/,\n      relevance: 0\n    }, hljs.inherit(STRINGS, {\n      className: 'meta-string'\n    }), {\n      className: 'meta-string',\n      begin: /<[^\\n>]*>/,\n      end: /$/,\n      illegal: '\\\\n'\n    }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n  };\n  return {\n    name: 'SQF',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'case catch default do else exit exitWith for forEach from if ' + 'private switch then throw to try waitUntil while with',\n      built_in: 'abs accTime acos action actionIDs actionKeys actionKeysImages actionKeysNames ' + 'actionKeysNamesArray actionName actionParams activateAddons activatedAddons activateKey ' + 'add3DENConnection add3DENEventHandler add3DENLayer addAction addBackpack addBackpackCargo ' + 'addBackpackCargoGlobal addBackpackGlobal addCamShake addCuratorAddons addCuratorCameraArea ' + 'addCuratorEditableObjects addCuratorEditingArea addCuratorPoints addEditorObject addEventHandler ' + 'addForce addGoggles addGroupIcon addHandgunItem addHeadgear addItem addItemCargo ' + 'addItemCargoGlobal addItemPool addItemToBackpack addItemToUniform addItemToVest addLiveStats ' + 'addMagazine addMagazineAmmoCargo addMagazineCargo addMagazineCargoGlobal addMagazineGlobal ' + 'addMagazinePool addMagazines addMagazineTurret addMenu addMenuItem addMissionEventHandler ' + 'addMPEventHandler addMusicEventHandler addOwnedMine addPlayerScores addPrimaryWeaponItem ' + 'addPublicVariableEventHandler addRating addResources addScore addScoreSide addSecondaryWeaponItem ' + 'addSwitchableUnit addTeamMember addToRemainsCollector addTorque addUniform addVehicle addVest ' + 'addWaypoint addWeapon addWeaponCargo addWeaponCargoGlobal addWeaponGlobal addWeaponItem ' + 'addWeaponPool addWeaponTurret admin agent agents AGLToASL aimedAtTarget aimPos airDensityRTD ' + 'airplaneThrottle airportSide AISFinishHeal alive all3DENEntities allAirports allControls ' + 'allCurators allCutLayers allDead allDeadMen allDisplays allGroups allMapMarkers allMines ' + 'allMissionObjects allow3DMode allowCrewInImmobile allowCuratorLogicIgnoreAreas allowDamage ' + 'allowDammage allowFileOperations allowFleeing allowGetIn allowSprint allPlayers allSimpleObjects ' + 'allSites allTurrets allUnits allUnitsUAV allVariables ammo ammoOnPylon and animate animateBay ' + 'animateDoor animatePylon animateSource animationNames animationPhase animationSourcePhase ' + 'animationState append apply armoryPoints arrayIntersect asin ASLToAGL ASLToATL assert ' + 'assignAsCargo assignAsCargoIndex assignAsCommander assignAsDriver assignAsGunner assignAsTurret ' + 'assignCurator assignedCargo assignedCommander assignedDriver assignedGunner assignedItems ' + 'assignedTarget assignedTeam assignedVehicle assignedVehicleRole assignItem assignTeam ' + 'assignToAirport atan atan2 atg ATLToASL attachedObject attachedObjects attachedTo attachObject ' + 'attachTo attackEnabled backpack backpackCargo backpackContainer backpackItems backpackMagazines ' + 'backpackSpaceFor behaviour benchmark binocular boundingBox boundingBoxReal boundingCenter ' + 'breakOut breakTo briefingName buildingExit buildingPos buttonAction buttonSetAction cadetMode ' + 'call callExtension camCommand camCommit camCommitPrepared camCommitted camConstuctionSetParams ' + 'camCreate camDestroy cameraEffect cameraEffectEnableHUD cameraInterest cameraOn cameraView ' + 'campaignConfigFile camPreload camPreloaded camPrepareBank camPrepareDir camPrepareDive ' + 'camPrepareFocus camPrepareFov camPrepareFovRange camPreparePos camPrepareRelPos camPrepareTarget ' + 'camSetBank camSetDir camSetDive camSetFocus camSetFov camSetFovRange camSetPos camSetRelPos ' + 'camSetTarget camTarget camUseNVG canAdd canAddItemToBackpack canAddItemToUniform canAddItemToVest ' + 'cancelSimpleTaskDestination canFire canMove canSlingLoad canStand canSuspend ' + 'canTriggerDynamicSimulation canUnloadInCombat canVehicleCargo captive captiveNum cbChecked ' + 'cbSetChecked ceil channelEnabled cheatsEnabled checkAIFeature checkVisibility className ' + 'clearAllItemsFromBackpack clearBackpackCargo clearBackpackCargoGlobal clearGroupIcons ' + 'clearItemCargo clearItemCargoGlobal clearItemPool clearMagazineCargo clearMagazineCargoGlobal ' + 'clearMagazinePool clearOverlay clearRadio clearWeaponCargo clearWeaponCargoGlobal clearWeaponPool ' + 'clientOwner closeDialog closeDisplay closeOverlay collapseObjectTree collect3DENHistory ' + 'collectiveRTD combatMode commandArtilleryFire commandChat commander commandFire commandFollow ' + 'commandFSM commandGetOut commandingMenu commandMove commandRadio commandStop ' + 'commandSuppressiveFire commandTarget commandWatch comment commitOverlay compile compileFinal ' + 'completedFSM composeText configClasses configFile configHierarchy configName configProperties ' + 'configSourceAddonList configSourceMod configSourceModList confirmSensorTarget ' + 'connectTerminalToUAV controlsGroupCtrl copyFromClipboard copyToClipboard copyWaypoints cos count ' + 'countEnemy countFriendly countSide countType countUnknown create3DENComposition create3DENEntity ' + 'createAgent createCenter createDialog createDiaryLink createDiaryRecord createDiarySubject ' + 'createDisplay createGearDialog createGroup createGuardedPoint createLocation createMarker ' + 'createMarkerLocal createMenu createMine createMissionDisplay createMPCampaignDisplay ' + 'createSimpleObject createSimpleTask createSite createSoundSource createTask createTeam ' + 'createTrigger createUnit createVehicle createVehicleCrew createVehicleLocal crew ctAddHeader ' + 'ctAddRow ctClear ctCurSel ctData ctFindHeaderRows ctFindRowHeader ctHeaderControls ctHeaderCount ' + 'ctRemoveHeaders ctRemoveRows ctrlActivate ctrlAddEventHandler ctrlAngle ctrlAutoScrollDelay ' + 'ctrlAutoScrollRewind ctrlAutoScrollSpeed ctrlChecked ctrlClassName ctrlCommit ctrlCommitted ' + 'ctrlCreate ctrlDelete ctrlEnable ctrlEnabled ctrlFade ctrlHTMLLoaded ctrlIDC ctrlIDD ' + 'ctrlMapAnimAdd ctrlMapAnimClear ctrlMapAnimCommit ctrlMapAnimDone ctrlMapCursor ctrlMapMouseOver ' + 'ctrlMapScale ctrlMapScreenToWorld ctrlMapWorldToScreen ctrlModel ctrlModelDirAndUp ctrlModelScale ' + 'ctrlParent ctrlParentControlsGroup ctrlPosition ctrlRemoveAllEventHandlers ctrlRemoveEventHandler ' + 'ctrlScale ctrlSetActiveColor ctrlSetAngle ctrlSetAutoScrollDelay ctrlSetAutoScrollRewind ' + 'ctrlSetAutoScrollSpeed ctrlSetBackgroundColor ctrlSetChecked ctrlSetEventHandler ctrlSetFade ' + 'ctrlSetFocus ctrlSetFont ctrlSetFontH1 ctrlSetFontH1B ctrlSetFontH2 ctrlSetFontH2B ctrlSetFontH3 ' + 'ctrlSetFontH3B ctrlSetFontH4 ctrlSetFontH4B ctrlSetFontH5 ctrlSetFontH5B ctrlSetFontH6 ' + 'ctrlSetFontH6B ctrlSetFontHeight ctrlSetFontHeightH1 ctrlSetFontHeightH2 ctrlSetFontHeightH3 ' + 'ctrlSetFontHeightH4 ctrlSetFontHeightH5 ctrlSetFontHeightH6 ctrlSetFontHeightSecondary ' + 'ctrlSetFontP ctrlSetFontPB ctrlSetFontSecondary ctrlSetForegroundColor ctrlSetModel ' + 'ctrlSetModelDirAndUp ctrlSetModelScale ctrlSetPixelPrecision ctrlSetPosition ctrlSetScale ' + 'ctrlSetStructuredText ctrlSetText ctrlSetTextColor ctrlSetTooltip ctrlSetTooltipColorBox ' + 'ctrlSetTooltipColorShade ctrlSetTooltipColorText ctrlShow ctrlShown ctrlText ctrlTextHeight ' + 'ctrlTextWidth ctrlType ctrlVisible ctRowControls ctRowCount ctSetCurSel ctSetData ' + 'ctSetHeaderTemplate ctSetRowTemplate ctSetValue ctValue curatorAddons curatorCamera ' + 'curatorCameraArea curatorCameraAreaCeiling curatorCoef curatorEditableObjects curatorEditingArea ' + 'curatorEditingAreaType curatorMouseOver curatorPoints curatorRegisteredObjects curatorSelected ' + 'curatorWaypointCost current3DENOperation currentChannel currentCommand currentMagazine ' + 'currentMagazineDetail currentMagazineDetailTurret currentMagazineTurret currentMuzzle ' + 'currentNamespace currentTask currentTasks currentThrowable currentVisionMode currentWaypoint ' + 'currentWeapon currentWeaponMode currentWeaponTurret currentZeroing cursorObject cursorTarget ' + 'customChat customRadio cutFadeOut cutObj cutRsc cutText damage date dateToNumber daytime ' + 'deActivateKey debriefingText debugFSM debugLog deg delete3DENEntities deleteAt deleteCenter ' + 'deleteCollection deleteEditorObject deleteGroup deleteGroupWhenEmpty deleteIdentity ' + 'deleteLocation deleteMarker deleteMarkerLocal deleteRange deleteResources deleteSite deleteStatus ' + 'deleteTeam deleteVehicle deleteVehicleCrew deleteWaypoint detach detectedMines ' + 'diag_activeMissionFSMs diag_activeScripts diag_activeSQFScripts diag_activeSQSScripts ' + 'diag_captureFrame diag_captureFrameToFile diag_captureSlowFrame diag_codePerformance ' + 'diag_drawMode diag_enable diag_enabled diag_fps diag_fpsMin diag_frameNo diag_lightNewLoad ' + 'diag_list diag_log diag_logSlowFrame diag_mergeConfigFile diag_recordTurretLimits ' + 'diag_setLightNew diag_tickTime diag_toggle dialog diarySubjectExists didJIP didJIPOwner ' + 'difficulty difficultyEnabled difficultyEnabledRTD difficultyOption direction directSay disableAI ' + 'disableCollisionWith disableConversation disableDebriefingStats disableMapIndicators ' + 'disableNVGEquipment disableRemoteSensors disableSerialization disableTIEquipment ' + 'disableUAVConnectability disableUserInput displayAddEventHandler displayCtrl displayParent ' + 'displayRemoveAllEventHandlers displayRemoveEventHandler displaySetEventHandler dissolveTeam ' + 'distance distance2D distanceSqr distributionRegion do3DENAction doArtilleryFire doFire doFollow ' + 'doFSM doGetOut doMove doorPhase doStop doSuppressiveFire doTarget doWatch drawArrow drawEllipse ' + 'drawIcon drawIcon3D drawLine drawLine3D drawLink drawLocation drawPolygon drawRectangle ' + 'drawTriangle driver drop dynamicSimulationDistance dynamicSimulationDistanceCoef ' + 'dynamicSimulationEnabled dynamicSimulationSystemEnabled echo edit3DENMissionAttributes editObject ' + 'editorSetEventHandler effectiveCommander emptyPositions enableAI enableAIFeature ' + 'enableAimPrecision enableAttack enableAudioFeature enableAutoStartUpRTD enableAutoTrimRTD ' + 'enableCamShake enableCaustics enableChannel enableCollisionWith enableCopilot ' + 'enableDebriefingStats enableDiagLegend enableDynamicSimulation enableDynamicSimulationSystem ' + 'enableEndDialog enableEngineArtillery enableEnvironment enableFatigue enableGunLights ' + 'enableInfoPanelComponent enableIRLasers enableMimics enablePersonTurret enableRadio enableReload ' + 'enableRopeAttach enableSatNormalOnDetail enableSaving enableSentences enableSimulation ' + 'enableSimulationGlobal enableStamina enableTeamSwitch enableTraffic enableUAVConnectability ' + 'enableUAVWaypoints enableVehicleCargo enableVehicleSensor enableWeaponDisassembly ' + 'endLoadingScreen endMission engineOn enginesIsOnRTD enginesRpmRTD enginesTorqueRTD entities ' + 'environmentEnabled estimatedEndServerTime estimatedTimeLeft evalObjectArgument everyBackpack ' + 'everyContainer exec execEditorScript execFSM execVM exp expectedDestination exportJIPMessages ' + 'eyeDirection eyePos face faction fadeMusic fadeRadio fadeSound fadeSpeech failMission ' + 'fillWeaponsFromPool find findCover findDisplay findEditorObject findEmptyPosition ' + 'findEmptyPositionReady findIf findNearestEnemy finishMissionInit finite fire fireAtTarget ' + 'firstBackpack flag flagAnimationPhase flagOwner flagSide flagTexture fleeing floor flyInHeight ' + 'flyInHeightASL fog fogForecast fogParams forceAddUniform forcedMap forceEnd forceFlagTexture ' + 'forceFollowRoad forceMap forceRespawn forceSpeed forceWalk forceWeaponFire forceWeatherChange ' + 'forEachMember forEachMemberAgent forEachMemberTeam forgetTarget format formation ' + 'formationDirection formationLeader formationMembers formationPosition formationTask formatText ' + 'formLeader freeLook fromEditor fuel fullCrew gearIDCAmmoCount gearSlotAmmoCount gearSlotData ' + 'get3DENActionState get3DENAttribute get3DENCamera get3DENConnections get3DENEntity ' + 'get3DENEntityID get3DENGrid get3DENIconsVisible get3DENLayerEntities get3DENLinesVisible ' + 'get3DENMissionAttribute get3DENMouseOver get3DENSelected getAimingCoef getAllEnvSoundControllers ' + 'getAllHitPointsDamage getAllOwnedMines getAllSoundControllers getAmmoCargo getAnimAimPrecision ' + 'getAnimSpeedCoef getArray getArtilleryAmmo getArtilleryComputerSettings getArtilleryETA ' + 'getAssignedCuratorLogic getAssignedCuratorUnit getBackpackCargo getBleedingRemaining ' + 'getBurningValue getCameraViewDirection getCargoIndex getCenterOfMass getClientState ' + 'getClientStateNumber getCompatiblePylonMagazines getConnectedUAV getContainerMaxLoad ' + 'getCursorObjectParams getCustomAimCoef getDammage getDescription getDir getDirVisual ' + 'getDLCAssetsUsage getDLCAssetsUsageByName getDLCs getEditorCamera getEditorMode ' + 'getEditorObjectScope getElevationOffset getEnvSoundController getFatigue getForcedFlagTexture ' + 'getFriend getFSMVariable getFuelCargo getGroupIcon getGroupIconParams getGroupIcons getHideFrom ' + 'getHit getHitIndex getHitPointDamage getItemCargo getMagazineCargo getMarkerColor getMarkerPos ' + 'getMarkerSize getMarkerType getMass getMissionConfig getMissionConfigValue getMissionDLCs ' + 'getMissionLayerEntities getModelInfo getMousePosition getMusicPlayedTime getNumber ' + 'getObjectArgument getObjectChildren getObjectDLC getObjectMaterials getObjectProxy ' + 'getObjectTextures getObjectType getObjectViewDistance getOxygenRemaining getPersonUsedDLCs ' + 'getPilotCameraDirection getPilotCameraPosition getPilotCameraRotation getPilotCameraTarget ' + 'getPlateNumber getPlayerChannel getPlayerScores getPlayerUID getPos getPosASL getPosASLVisual ' + 'getPosASLW getPosATL getPosATLVisual getPosVisual getPosWorld getPylonMagazines getRelDir ' + 'getRelPos getRemoteSensorsDisabled getRepairCargo getResolution getShadowDistance getShotParents ' + 'getSlingLoad getSoundController getSoundControllerResult getSpeed getStamina getStatValue ' + 'getSuppression getTerrainGrid getTerrainHeightASL getText getTotalDLCUsageTime getUnitLoadout ' + 'getUnitTrait getUserMFDText getUserMFDvalue getVariable getVehicleCargo getWeaponCargo ' + 'getWeaponSway getWingsOrientationRTD getWingsPositionRTD getWPPos glanceAt globalChat globalRadio ' + 'goggles goto group groupChat groupFromNetId groupIconSelectable groupIconsVisible groupId ' + 'groupOwner groupRadio groupSelectedUnits groupSelectUnit gunner gusts halt handgunItems ' + 'handgunMagazine handgunWeapon handsHit hasInterface hasPilotCamera hasWeapon hcAllGroups ' + 'hcGroupParams hcLeader hcRemoveAllGroups hcRemoveGroup hcSelected hcSelectGroup hcSetGroup ' + 'hcShowBar hcShownBar headgear hideBody hideObject hideObjectGlobal hideSelection hint hintC ' + 'hintCadet hintSilent hmd hostMission htmlLoad HUDMovementLevels humidity image importAllGroups ' + 'importance in inArea inAreaArray incapacitatedState inflame inflamed infoPanel ' + 'infoPanelComponentEnabled infoPanelComponents infoPanels inGameUISetEventHandler inheritsFrom ' + 'initAmbientLife inPolygon inputAction inRangeOfArtillery insertEditorObject intersect is3DEN ' + 'is3DENMultiplayer isAbleToBreathe isAgent isArray isAutoHoverOn isAutonomous isAutotest ' + 'isBleeding isBurning isClass isCollisionLightOn isCopilotEnabled isDamageAllowed isDedicated ' + 'isDLCAvailable isEngineOn isEqualTo isEqualType isEqualTypeAll isEqualTypeAny isEqualTypeArray ' + 'isEqualTypeParams isFilePatchingEnabled isFlashlightOn isFlatEmpty isForcedWalk isFormationLeader ' + 'isGroupDeletedWhenEmpty isHidden isInRemainsCollector isInstructorFigureEnabled isIRLaserOn ' + 'isKeyActive isKindOf isLaserOn isLightOn isLocalized isManualFire isMarkedForCollection ' + 'isMultiplayer isMultiplayerSolo isNil isNull isNumber isObjectHidden isObjectRTD isOnRoad ' + 'isPipEnabled isPlayer isRealTime isRemoteExecuted isRemoteExecutedJIP isServer isShowing3DIcons ' + 'isSimpleObject isSprintAllowed isStaminaEnabled isSteamMission isStreamFriendlyUIEnabled isText ' + 'isTouchingGround isTurnedOut isTutHintsEnabled isUAVConnectable isUAVConnected isUIContext ' + 'isUniformAllowed isVehicleCargo isVehicleRadarOn isVehicleSensorEnabled isWalking ' + 'isWeaponDeployed isWeaponRested itemCargo items itemsWithMagazines join joinAs joinAsSilent ' + 'joinSilent joinString kbAddDatabase kbAddDatabaseTargets kbAddTopic kbHasTopic kbReact ' + 'kbRemoveTopic kbTell kbWasSaid keyImage keyName knowsAbout land landAt landResult language ' + 'laserTarget lbAdd lbClear lbColor lbColorRight lbCurSel lbData lbDelete lbIsSelected lbPicture ' + 'lbPictureRight lbSelection lbSetColor lbSetColorRight lbSetCurSel lbSetData lbSetPicture ' + 'lbSetPictureColor lbSetPictureColorDisabled lbSetPictureColorSelected lbSetPictureRight ' + 'lbSetPictureRightColor lbSetPictureRightColorDisabled lbSetPictureRightColorSelected ' + 'lbSetSelectColor lbSetSelectColorRight lbSetSelected lbSetText lbSetTextRight lbSetTooltip ' + 'lbSetValue lbSize lbSort lbSortByValue lbText lbTextRight lbValue leader leaderboardDeInit ' + 'leaderboardGetRows leaderboardInit leaderboardRequestRowsFriends leaderboardsRequestUploadScore ' + 'leaderboardsRequestUploadScoreKeepBest leaderboardState leaveVehicle libraryCredits ' + 'libraryDisclaimers lifeState lightAttachObject lightDetachObject lightIsOn lightnings limitSpeed ' + 'linearConversion lineIntersects lineIntersectsObjs lineIntersectsSurfaces lineIntersectsWith ' + 'linkItem list listObjects listRemoteTargets listVehicleSensors ln lnbAddArray lnbAddColumn ' + 'lnbAddRow lnbClear lnbColor lnbCurSelRow lnbData lnbDeleteColumn lnbDeleteRow ' + 'lnbGetColumnsPosition lnbPicture lnbSetColor lnbSetColumnsPos lnbSetCurSelRow lnbSetData ' + 'lnbSetPicture lnbSetText lnbSetValue lnbSize lnbSort lnbSortByValue lnbText lnbValue load loadAbs ' + 'loadBackpack loadFile loadGame loadIdentity loadMagazine loadOverlay loadStatus loadUniform ' + 'loadVest local localize locationPosition lock lockCameraTo lockCargo lockDriver locked ' + 'lockedCargo lockedDriver lockedTurret lockIdentity lockTurret lockWP log logEntities logNetwork ' + 'logNetworkTerminate lookAt lookAtPos magazineCargo magazines magazinesAllTurrets magazinesAmmo ' + 'magazinesAmmoCargo magazinesAmmoFull magazinesDetail magazinesDetailBackpack ' + 'magazinesDetailUniform magazinesDetailVest magazinesTurret magazineTurretAmmo mapAnimAdd ' + 'mapAnimClear mapAnimCommit mapAnimDone mapCenterOnCamera mapGridPosition markAsFinishedOnSteam ' + 'markerAlpha markerBrush markerColor markerDir markerPos markerShape markerSize markerText ' + 'markerType max members menuAction menuAdd menuChecked menuClear menuCollapse menuData menuDelete ' + 'menuEnable menuEnabled menuExpand menuHover menuPicture menuSetAction menuSetCheck menuSetData ' + 'menuSetPicture menuSetValue menuShortcut menuShortcutText menuSize menuSort menuText menuURL ' + 'menuValue min mineActive mineDetectedBy missionConfigFile missionDifficulty missionName ' + 'missionNamespace missionStart missionVersion mod modelToWorld modelToWorldVisual ' + 'modelToWorldVisualWorld modelToWorldWorld modParams moonIntensity moonPhase morale move ' + 'move3DENCamera moveInAny moveInCargo moveInCommander moveInDriver moveInGunner moveInTurret ' + 'moveObjectToEnd moveOut moveTime moveTo moveToCompleted moveToFailed musicVolume name nameSound ' + 'nearEntities nearestBuilding nearestLocation nearestLocations nearestLocationWithDubbing ' + 'nearestObject nearestObjects nearestTerrainObjects nearObjects nearObjectsReady nearRoads ' + 'nearSupplies nearTargets needReload netId netObjNull newOverlay nextMenuItemIndex ' + 'nextWeatherChange nMenuItems not numberOfEnginesRTD numberToDate objectCurators objectFromNetId ' + 'objectParent objStatus onBriefingGroup onBriefingNotes onBriefingPlan onBriefingTeamSwitch ' + 'onCommandModeChanged onDoubleClick onEachFrame onGroupIconClick onGroupIconOverEnter ' + 'onGroupIconOverLeave onHCGroupSelectionChanged onMapSingleClick onPlayerConnected ' + 'onPlayerDisconnected onPreloadFinished onPreloadStarted onShowNewObject onTeamSwitch ' + 'openCuratorInterface openDLCPage openMap openSteamApp openYoutubeVideo or orderGetIn overcast ' + 'overcastForecast owner param params parseNumber parseSimpleArray parseText parsingNamespace ' + 'particlesQuality pickWeaponPool pitch pixelGrid pixelGridBase pixelGridNoUIScale pixelH pixelW ' + 'playableSlotsNumber playableUnits playAction playActionNow player playerRespawnTime playerSide ' + 'playersNumber playGesture playMission playMove playMoveNow playMusic playScriptedMission ' + 'playSound playSound3D position positionCameraToWorld posScreenToWorld posWorldToScreen ' + 'ppEffectAdjust ppEffectCommit ppEffectCommitted ppEffectCreate ppEffectDestroy ppEffectEnable ' + 'ppEffectEnabled ppEffectForceInNVG precision preloadCamera preloadObject preloadSound ' + 'preloadTitleObj preloadTitleRsc preprocessFile preprocessFileLineNumbers primaryWeapon ' + 'primaryWeaponItems primaryWeaponMagazine priority processDiaryLink productVersion profileName ' + 'profileNamespace profileNameSteam progressLoadingScreen progressPosition progressSetPosition ' + 'publicVariable publicVariableClient publicVariableServer pushBack pushBackUnique putWeaponPool ' + 'queryItemsPool queryMagazinePool queryWeaponPool rad radioChannelAdd radioChannelCreate ' + 'radioChannelRemove radioChannelSetCallSign radioChannelSetLabel radioVolume rain rainbow random ' + 'rank rankId rating rectangular registeredTasks registerTask reload reloadEnabled remoteControl ' + 'remoteExec remoteExecCall remoteExecutedOwner remove3DENConnection remove3DENEventHandler ' + 'remove3DENLayer removeAction removeAll3DENEventHandlers removeAllActions removeAllAssignedItems ' + 'removeAllContainers removeAllCuratorAddons removeAllCuratorCameraAreas ' + 'removeAllCuratorEditingAreas removeAllEventHandlers removeAllHandgunItems removeAllItems ' + 'removeAllItemsWithMagazines removeAllMissionEventHandlers removeAllMPEventHandlers ' + 'removeAllMusicEventHandlers removeAllOwnedMines removeAllPrimaryWeaponItems removeAllWeapons ' + 'removeBackpack removeBackpackGlobal removeCuratorAddons removeCuratorCameraArea ' + 'removeCuratorEditableObjects removeCuratorEditingArea removeDrawIcon removeDrawLinks ' + 'removeEventHandler removeFromRemainsCollector removeGoggles removeGroupIcon removeHandgunItem ' + 'removeHeadgear removeItem removeItemFromBackpack removeItemFromUniform removeItemFromVest ' + 'removeItems removeMagazine removeMagazineGlobal removeMagazines removeMagazinesTurret ' + 'removeMagazineTurret removeMenuItem removeMissionEventHandler removeMPEventHandler ' + 'removeMusicEventHandler removeOwnedMine removePrimaryWeaponItem removeSecondaryWeaponItem ' + 'removeSimpleTask removeSwitchableUnit removeTeamMember removeUniform removeVest removeWeapon ' + 'removeWeaponAttachmentCargo removeWeaponCargo removeWeaponGlobal removeWeaponTurret ' + 'reportRemoteTarget requiredVersion resetCamShake resetSubgroupDirection resize resources ' + 'respawnVehicle restartEditorCamera reveal revealMine reverse reversedMouseY roadAt ' + 'roadsConnectedTo roleDescription ropeAttachedObjects ropeAttachedTo ropeAttachEnabled ' + 'ropeAttachTo ropeCreate ropeCut ropeDestroy ropeDetach ropeEndPosition ropeLength ropes ' + 'ropeUnwind ropeUnwound rotorsForcesRTD rotorsRpmRTD round runInitScript safeZoneH safeZoneW ' + 'safeZoneWAbs safeZoneX safeZoneXAbs safeZoneY save3DENInventory saveGame saveIdentity ' + 'saveJoysticks saveOverlay saveProfileNamespace saveStatus saveVar savingEnabled say say2D say3D ' + 'scopeName score scoreSide screenshot screenToWorld scriptDone scriptName scudState ' + 'secondaryWeapon secondaryWeaponItems secondaryWeaponMagazine select selectBestPlaces ' + 'selectDiarySubject selectedEditorObjects selectEditorObject selectionNames selectionPosition ' + 'selectLeader selectMax selectMin selectNoPlayer selectPlayer selectRandom selectRandomWeighted ' + 'selectWeapon selectWeaponTurret sendAUMessage sendSimpleCommand sendTask sendTaskResult ' + 'sendUDPMessage serverCommand serverCommandAvailable serverCommandExecutable serverName serverTime ' + 'set set3DENAttribute set3DENAttributes set3DENGrid set3DENIconsVisible set3DENLayer ' + 'set3DENLinesVisible set3DENLogicType set3DENMissionAttribute set3DENMissionAttributes ' + 'set3DENModelsVisible set3DENObjectType set3DENSelected setAccTime setActualCollectiveRTD ' + 'setAirplaneThrottle setAirportSide setAmmo setAmmoCargo setAmmoOnPylon setAnimSpeedCoef ' + 'setAperture setApertureNew setArmoryPoints setAttributes setAutonomous setBehaviour ' + 'setBleedingRemaining setBrakesRTD setCameraInterest setCamShakeDefParams setCamShakeParams ' + 'setCamUseTI setCaptive setCenterOfMass setCollisionLight setCombatMode setCompassOscillation ' + 'setConvoySeparation setCuratorCameraAreaCeiling setCuratorCoef setCuratorEditingAreaType ' + 'setCuratorWaypointCost setCurrentChannel setCurrentTask setCurrentWaypoint setCustomAimCoef ' + 'setCustomWeightRTD setDamage setDammage setDate setDebriefingText setDefaultCamera setDestination ' + 'setDetailMapBlendPars setDir setDirection setDrawIcon setDriveOnPath setDropInterval ' + 'setDynamicSimulationDistance setDynamicSimulationDistanceCoef setEditorMode setEditorObjectScope ' + 'setEffectCondition setEngineRPMRTD setFace setFaceAnimation setFatigue setFeatureType ' + 'setFlagAnimationPhase setFlagOwner setFlagSide setFlagTexture setFog setFormation ' + 'setFormationTask setFormDir setFriend setFromEditor setFSMVariable setFuel setFuelCargo ' + 'setGroupIcon setGroupIconParams setGroupIconsSelectable setGroupIconsVisible setGroupId ' + 'setGroupIdGlobal setGroupOwner setGusts setHideBehind setHit setHitIndex setHitPointDamage ' + 'setHorizonParallaxCoef setHUDMovementLevels setIdentity setImportance setInfoPanel setLeader ' + 'setLightAmbient setLightAttenuation setLightBrightness setLightColor setLightDayLight ' + 'setLightFlareMaxDistance setLightFlareSize setLightIntensity setLightnings setLightUseFlare ' + 'setLocalWindParams setMagazineTurretAmmo setMarkerAlpha setMarkerAlphaLocal setMarkerBrush ' + 'setMarkerBrushLocal setMarkerColor setMarkerColorLocal setMarkerDir setMarkerDirLocal ' + 'setMarkerPos setMarkerPosLocal setMarkerShape setMarkerShapeLocal setMarkerSize ' + 'setMarkerSizeLocal setMarkerText setMarkerTextLocal setMarkerType setMarkerTypeLocal setMass ' + 'setMimic setMousePosition setMusicEffect setMusicEventHandler setName setNameSound ' + 'setObjectArguments setObjectMaterial setObjectMaterialGlobal setObjectProxy setObjectTexture ' + 'setObjectTextureGlobal setObjectViewDistance setOvercast setOwner setOxygenRemaining ' + 'setParticleCircle setParticleClass setParticleFire setParticleParams setParticleRandom ' + 'setPilotCameraDirection setPilotCameraRotation setPilotCameraTarget setPilotLight setPiPEffect ' + 'setPitch setPlateNumber setPlayable setPlayerRespawnTime setPos setPosASL setPosASL2 setPosASLW ' + 'setPosATL setPosition setPosWorld setPylonLoadOut setPylonsPriority setRadioMsg setRain ' + 'setRainbow setRandomLip setRank setRectangular setRepairCargo setRotorBrakeRTD setShadowDistance ' + 'setShotParents setSide setSimpleTaskAlwaysVisible setSimpleTaskCustomData ' + 'setSimpleTaskDescription setSimpleTaskDestination setSimpleTaskTarget setSimpleTaskType ' + 'setSimulWeatherLayers setSize setSkill setSlingLoad setSoundEffect setSpeaker setSpeech ' + 'setSpeedMode setStamina setStaminaScheme setStatValue setSuppression setSystemOfUnits ' + 'setTargetAge setTaskMarkerOffset setTaskResult setTaskState setTerrainGrid setText ' + 'setTimeMultiplier setTitleEffect setTrafficDensity setTrafficDistance setTrafficGap ' + 'setTrafficSpeed setTriggerActivation setTriggerArea setTriggerStatements setTriggerText ' + 'setTriggerTimeout setTriggerType setType setUnconscious setUnitAbility setUnitLoadout setUnitPos ' + 'setUnitPosWeak setUnitRank setUnitRecoilCoefficient setUnitTrait setUnloadInCombat ' + 'setUserActionText setUserMFDText setUserMFDvalue setVariable setVectorDir setVectorDirAndUp ' + 'setVectorUp setVehicleAmmo setVehicleAmmoDef setVehicleArmor setVehicleCargo setVehicleId ' + 'setVehicleLock setVehiclePosition setVehicleRadar setVehicleReceiveRemoteTargets ' + 'setVehicleReportOwnPosition setVehicleReportRemoteTargets setVehicleTIPars setVehicleVarName ' + 'setVelocity setVelocityModelSpace setVelocityTransformation setViewDistance ' + 'setVisibleIfTreeCollapsed setWantedRPMRTD setWaves setWaypointBehaviour setWaypointCombatMode ' + 'setWaypointCompletionRadius setWaypointDescription setWaypointForceBehaviour setWaypointFormation ' + 'setWaypointHousePosition setWaypointLoiterRadius setWaypointLoiterType setWaypointName ' + 'setWaypointPosition setWaypointScript setWaypointSpeed setWaypointStatements setWaypointTimeout ' + 'setWaypointType setWaypointVisible setWeaponReloadingTime setWind setWindDir setWindForce ' + 'setWindStr setWingForceScaleRTD setWPPos show3DIcons showChat showCinemaBorder showCommandingMenu ' + 'showCompass showCuratorCompass showGPS showHUD showLegend showMap shownArtilleryComputer ' + 'shownChat shownCompass shownCuratorCompass showNewEditorObject shownGPS shownHUD shownMap ' + 'shownPad shownRadio shownScoretable shownUAVFeed shownWarrant shownWatch showPad showRadio ' + 'showScoretable showSubtitles showUAVFeed showWarrant showWatch showWaypoint showWaypoints side ' + 'sideChat sideEnemy sideFriendly sideRadio simpleTasks simulationEnabled simulCloudDensity ' + 'simulCloudOcclusion simulInClouds simulWeatherSync sin size sizeOf skill skillFinal skipTime ' + 'sleep sliderPosition sliderRange sliderSetPosition sliderSetRange sliderSetSpeed sliderSpeed ' + 'slingLoadAssistantShown soldierMagazines someAmmo sort soundVolume spawn speaker speed speedMode ' + 'splitString sqrt squadParams stance startLoadingScreen step stop stopEngineRTD stopped str ' + 'sunOrMoon supportInfo suppressFor surfaceIsWater surfaceNormal surfaceType swimInDepth ' + 'switchableUnits switchAction switchCamera switchGesture switchLight switchMove ' + 'synchronizedObjects synchronizedTriggers synchronizedWaypoints synchronizeObjectsAdd ' + 'synchronizeObjectsRemove synchronizeTrigger synchronizeWaypoint systemChat systemOfUnits tan ' + 'targetKnowledge targets targetsAggregate targetsQuery taskAlwaysVisible taskChildren ' + 'taskCompleted taskCustomData taskDescription taskDestination taskHint taskMarkerOffset taskParent ' + 'taskResult taskState taskType teamMember teamName teams teamSwitch teamSwitchEnabled teamType ' + 'terminate terrainIntersect terrainIntersectASL terrainIntersectAtASL text textLog textLogFormat ' + 'tg time timeMultiplier titleCut titleFadeOut titleObj titleRsc titleText toArray toFixed toLower ' + 'toString toUpper triggerActivated triggerActivation triggerArea triggerAttachedVehicle ' + 'triggerAttachObject triggerAttachVehicle triggerDynamicSimulation triggerStatements triggerText ' + 'triggerTimeout triggerTimeoutCurrent triggerType turretLocal turretOwner turretUnit tvAdd tvClear ' + 'tvCollapse tvCollapseAll tvCount tvCurSel tvData tvDelete tvExpand tvExpandAll tvPicture ' + 'tvSetColor tvSetCurSel tvSetData tvSetPicture tvSetPictureColor tvSetPictureColorDisabled ' + 'tvSetPictureColorSelected tvSetPictureRight tvSetPictureRightColor tvSetPictureRightColorDisabled ' + 'tvSetPictureRightColorSelected tvSetText tvSetTooltip tvSetValue tvSort tvSortByValue tvText ' + 'tvTooltip tvValue type typeName typeOf UAVControl uiNamespace uiSleep unassignCurator ' + 'unassignItem unassignTeam unassignVehicle underwater uniform uniformContainer uniformItems ' + 'uniformMagazines unitAddons unitAimPosition unitAimPositionVisual unitBackpack unitIsUAV unitPos ' + 'unitReady unitRecoilCoefficient units unitsBelowHeight unlinkItem unlockAchievement ' + 'unregisterTask updateDrawIcon updateMenuItem updateObjectTree useAISteeringComponent ' + 'useAudioTimeForMoves userInputDisabled vectorAdd vectorCos vectorCrossProduct vectorDiff ' + 'vectorDir vectorDirVisual vectorDistance vectorDistanceSqr vectorDotProduct vectorFromTo ' + 'vectorMagnitude vectorMagnitudeSqr vectorModelToWorld vectorModelToWorldVisual vectorMultiply ' + 'vectorNormalized vectorUp vectorUpVisual vectorWorldToModel vectorWorldToModelVisual vehicle ' + 'vehicleCargoEnabled vehicleChat vehicleRadio vehicleReceiveRemoteTargets vehicleReportOwnPosition ' + 'vehicleReportRemoteTargets vehicles vehicleVarName velocity velocityModelSpace verifySignature ' + 'vest vestContainer vestItems vestMagazines viewDistance visibleCompass visibleGPS visibleMap ' + 'visiblePosition visiblePositionASL visibleScoretable visibleWatch waves waypointAttachedObject ' + 'waypointAttachedVehicle waypointAttachObject waypointAttachVehicle waypointBehaviour ' + 'waypointCombatMode waypointCompletionRadius waypointDescription waypointForceBehaviour ' + 'waypointFormation waypointHousePosition waypointLoiterRadius waypointLoiterType waypointName ' + 'waypointPosition waypoints waypointScript waypointsEnabledUAV waypointShow waypointSpeed ' + 'waypointStatements waypointTimeout waypointTimeoutCurrent waypointType waypointVisible ' + 'weaponAccessories weaponAccessoriesCargo weaponCargo weaponDirection weaponInertia weaponLowered ' + 'weapons weaponsItems weaponsItemsCargo weaponState weaponsTurret weightRTD WFSideText wind ',\n      literal: 'blufor civilian configNull controlNull displayNull east endl false grpNull independent lineBreak ' + 'locationNull nil objNull opfor pi resistance scriptNull sideAmbientLife sideEmpty sideLogic ' + 'sideUnknown taskNull teamMemberNull true west'\n    },\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.NUMBER_MODE, VARIABLE, FUNCTION, STRINGS, PREPROCESSOR],\n    illegal: /#|^\\$ /\n  };\n}\nmodule.exports = sqf;", "map": {"version": 3, "names": ["sqf", "hljs", "VARIABLE", "className", "begin", "FUNCTION", "STRINGS", "variants", "end", "contains", "relevance", "PREPROCESSOR", "keywords", "inherit", "illegal", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "name", "case_insensitive", "keyword", "built_in", "literal", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/sqf.js"], "sourcesContent": ["/*\nLanguage: SQF\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Scripting language for the Arma game series\nWebsite: https://community.bistudio.com/wiki/SQF_syntax\nCategory: scripting\n*/\n\nfunction sqf(hljs) {\n  // In SQF, a variable start with _\n  const VARIABLE = {\n    className: 'variable',\n    begin: /\\b_+[a-zA-Z]\\w*/\n  };\n\n  // In SQF, a function should fit myTag_fnc_myFunction pattern\n  // https://community.bistudio.com/wiki/Functions_Library_(Arma_3)#Adding_a_Function\n  const FUNCTION = {\n    className: 'title',\n    begin: /[a-zA-Z][a-zA-Z0-9]+_fnc_\\w*/\n  };\n\n  // In SQF strings, quotes matching the start are escaped by adding a consecutive.\n  // Example of single escaped quotes: \" \"\" \" and  ' '' '.\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"',\n        end: '\"',\n        contains: [ {\n          begin: '\"\"',\n          relevance: 0\n        } ]\n      },\n      {\n        begin: '\\'',\n        end: '\\'',\n        contains: [ {\n          begin: '\\'\\'',\n          relevance: 0\n        } ]\n      }\n    ]\n  };\n\n  // list of keywords from:\n  // https://community.bistudio.com/wiki/PreProcessor_Commands\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword':\n        'define undef ifdef ifndef else endif include'\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, {\n        className: 'meta-string'\n      }),\n      {\n        className: 'meta-string',\n        begin: /<[^\\n>]*>/,\n        end: /$/,\n        illegal: '\\\\n'\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  return {\n    name: 'SQF',\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        'case catch default do else exit exitWith for forEach from if ' +\n        'private switch then throw to try waitUntil while with',\n      built_in:\n        'abs accTime acos action actionIDs actionKeys actionKeysImages actionKeysNames ' +\n        'actionKeysNamesArray actionName actionParams activateAddons activatedAddons activateKey ' +\n        'add3DENConnection add3DENEventHandler add3DENLayer addAction addBackpack addBackpackCargo ' +\n        'addBackpackCargoGlobal addBackpackGlobal addCamShake addCuratorAddons addCuratorCameraArea ' +\n        'addCuratorEditableObjects addCuratorEditingArea addCuratorPoints addEditorObject addEventHandler ' +\n        'addForce addGoggles addGroupIcon addHandgunItem addHeadgear addItem addItemCargo ' +\n        'addItemCargoGlobal addItemPool addItemToBackpack addItemToUniform addItemToVest addLiveStats ' +\n        'addMagazine addMagazineAmmoCargo addMagazineCargo addMagazineCargoGlobal addMagazineGlobal ' +\n        'addMagazinePool addMagazines addMagazineTurret addMenu addMenuItem addMissionEventHandler ' +\n        'addMPEventHandler addMusicEventHandler addOwnedMine addPlayerScores addPrimaryWeaponItem ' +\n        'addPublicVariableEventHandler addRating addResources addScore addScoreSide addSecondaryWeaponItem ' +\n        'addSwitchableUnit addTeamMember addToRemainsCollector addTorque addUniform addVehicle addVest ' +\n        'addWaypoint addWeapon addWeaponCargo addWeaponCargoGlobal addWeaponGlobal addWeaponItem ' +\n        'addWeaponPool addWeaponTurret admin agent agents AGLToASL aimedAtTarget aimPos airDensityRTD ' +\n        'airplaneThrottle airportSide AISFinishHeal alive all3DENEntities allAirports allControls ' +\n        'allCurators allCutLayers allDead allDeadMen allDisplays allGroups allMapMarkers allMines ' +\n        'allMissionObjects allow3DMode allowCrewInImmobile allowCuratorLogicIgnoreAreas allowDamage ' +\n        'allowDammage allowFileOperations allowFleeing allowGetIn allowSprint allPlayers allSimpleObjects ' +\n        'allSites allTurrets allUnits allUnitsUAV allVariables ammo ammoOnPylon and animate animateBay ' +\n        'animateDoor animatePylon animateSource animationNames animationPhase animationSourcePhase ' +\n        'animationState append apply armoryPoints arrayIntersect asin ASLToAGL ASLToATL assert ' +\n        'assignAsCargo assignAsCargoIndex assignAsCommander assignAsDriver assignAsGunner assignAsTurret ' +\n        'assignCurator assignedCargo assignedCommander assignedDriver assignedGunner assignedItems ' +\n        'assignedTarget assignedTeam assignedVehicle assignedVehicleRole assignItem assignTeam ' +\n        'assignToAirport atan atan2 atg ATLToASL attachedObject attachedObjects attachedTo attachObject ' +\n        'attachTo attackEnabled backpack backpackCargo backpackContainer backpackItems backpackMagazines ' +\n        'backpackSpaceFor behaviour benchmark binocular boundingBox boundingBoxReal boundingCenter ' +\n        'breakOut breakTo briefingName buildingExit buildingPos buttonAction buttonSetAction cadetMode ' +\n        'call callExtension camCommand camCommit camCommitPrepared camCommitted camConstuctionSetParams ' +\n        'camCreate camDestroy cameraEffect cameraEffectEnableHUD cameraInterest cameraOn cameraView ' +\n        'campaignConfigFile camPreload camPreloaded camPrepareBank camPrepareDir camPrepareDive ' +\n        'camPrepareFocus camPrepareFov camPrepareFovRange camPreparePos camPrepareRelPos camPrepareTarget ' +\n        'camSetBank camSetDir camSetDive camSetFocus camSetFov camSetFovRange camSetPos camSetRelPos ' +\n        'camSetTarget camTarget camUseNVG canAdd canAddItemToBackpack canAddItemToUniform canAddItemToVest ' +\n        'cancelSimpleTaskDestination canFire canMove canSlingLoad canStand canSuspend ' +\n        'canTriggerDynamicSimulation canUnloadInCombat canVehicleCargo captive captiveNum cbChecked ' +\n        'cbSetChecked ceil channelEnabled cheatsEnabled checkAIFeature checkVisibility className ' +\n        'clearAllItemsFromBackpack clearBackpackCargo clearBackpackCargoGlobal clearGroupIcons ' +\n        'clearItemCargo clearItemCargoGlobal clearItemPool clearMagazineCargo clearMagazineCargoGlobal ' +\n        'clearMagazinePool clearOverlay clearRadio clearWeaponCargo clearWeaponCargoGlobal clearWeaponPool ' +\n        'clientOwner closeDialog closeDisplay closeOverlay collapseObjectTree collect3DENHistory ' +\n        'collectiveRTD combatMode commandArtilleryFire commandChat commander commandFire commandFollow ' +\n        'commandFSM commandGetOut commandingMenu commandMove commandRadio commandStop ' +\n        'commandSuppressiveFire commandTarget commandWatch comment commitOverlay compile compileFinal ' +\n        'completedFSM composeText configClasses configFile configHierarchy configName configProperties ' +\n        'configSourceAddonList configSourceMod configSourceModList confirmSensorTarget ' +\n        'connectTerminalToUAV controlsGroupCtrl copyFromClipboard copyToClipboard copyWaypoints cos count ' +\n        'countEnemy countFriendly countSide countType countUnknown create3DENComposition create3DENEntity ' +\n        'createAgent createCenter createDialog createDiaryLink createDiaryRecord createDiarySubject ' +\n        'createDisplay createGearDialog createGroup createGuardedPoint createLocation createMarker ' +\n        'createMarkerLocal createMenu createMine createMissionDisplay createMPCampaignDisplay ' +\n        'createSimpleObject createSimpleTask createSite createSoundSource createTask createTeam ' +\n        'createTrigger createUnit createVehicle createVehicleCrew createVehicleLocal crew ctAddHeader ' +\n        'ctAddRow ctClear ctCurSel ctData ctFindHeaderRows ctFindRowHeader ctHeaderControls ctHeaderCount ' +\n        'ctRemoveHeaders ctRemoveRows ctrlActivate ctrlAddEventHandler ctrlAngle ctrlAutoScrollDelay ' +\n        'ctrlAutoScrollRewind ctrlAutoScrollSpeed ctrlChecked ctrlClassName ctrlCommit ctrlCommitted ' +\n        'ctrlCreate ctrlDelete ctrlEnable ctrlEnabled ctrlFade ctrlHTMLLoaded ctrlIDC ctrlIDD ' +\n        'ctrlMapAnimAdd ctrlMapAnimClear ctrlMapAnimCommit ctrlMapAnimDone ctrlMapCursor ctrlMapMouseOver ' +\n        'ctrlMapScale ctrlMapScreenToWorld ctrlMapWorldToScreen ctrlModel ctrlModelDirAndUp ctrlModelScale ' +\n        'ctrlParent ctrlParentControlsGroup ctrlPosition ctrlRemoveAllEventHandlers ctrlRemoveEventHandler ' +\n        'ctrlScale ctrlSetActiveColor ctrlSetAngle ctrlSetAutoScrollDelay ctrlSetAutoScrollRewind ' +\n        'ctrlSetAutoScrollSpeed ctrlSetBackgroundColor ctrlSetChecked ctrlSetEventHandler ctrlSetFade ' +\n        'ctrlSetFocus ctrlSetFont ctrlSetFontH1 ctrlSetFontH1B ctrlSetFontH2 ctrlSetFontH2B ctrlSetFontH3 ' +\n        'ctrlSetFontH3B ctrlSetFontH4 ctrlSetFontH4B ctrlSetFontH5 ctrlSetFontH5B ctrlSetFontH6 ' +\n        'ctrlSetFontH6B ctrlSetFontHeight ctrlSetFontHeightH1 ctrlSetFontHeightH2 ctrlSetFontHeightH3 ' +\n        'ctrlSetFontHeightH4 ctrlSetFontHeightH5 ctrlSetFontHeightH6 ctrlSetFontHeightSecondary ' +\n        'ctrlSetFontP ctrlSetFontPB ctrlSetFontSecondary ctrlSetForegroundColor ctrlSetModel ' +\n        'ctrlSetModelDirAndUp ctrlSetModelScale ctrlSetPixelPrecision ctrlSetPosition ctrlSetScale ' +\n        'ctrlSetStructuredText ctrlSetText ctrlSetTextColor ctrlSetTooltip ctrlSetTooltipColorBox ' +\n        'ctrlSetTooltipColorShade ctrlSetTooltipColorText ctrlShow ctrlShown ctrlText ctrlTextHeight ' +\n        'ctrlTextWidth ctrlType ctrlVisible ctRowControls ctRowCount ctSetCurSel ctSetData ' +\n        'ctSetHeaderTemplate ctSetRowTemplate ctSetValue ctValue curatorAddons curatorCamera ' +\n        'curatorCameraArea curatorCameraAreaCeiling curatorCoef curatorEditableObjects curatorEditingArea ' +\n        'curatorEditingAreaType curatorMouseOver curatorPoints curatorRegisteredObjects curatorSelected ' +\n        'curatorWaypointCost current3DENOperation currentChannel currentCommand currentMagazine ' +\n        'currentMagazineDetail currentMagazineDetailTurret currentMagazineTurret currentMuzzle ' +\n        'currentNamespace currentTask currentTasks currentThrowable currentVisionMode currentWaypoint ' +\n        'currentWeapon currentWeaponMode currentWeaponTurret currentZeroing cursorObject cursorTarget ' +\n        'customChat customRadio cutFadeOut cutObj cutRsc cutText damage date dateToNumber daytime ' +\n        'deActivateKey debriefingText debugFSM debugLog deg delete3DENEntities deleteAt deleteCenter ' +\n        'deleteCollection deleteEditorObject deleteGroup deleteGroupWhenEmpty deleteIdentity ' +\n        'deleteLocation deleteMarker deleteMarkerLocal deleteRange deleteResources deleteSite deleteStatus ' +\n        'deleteTeam deleteVehicle deleteVehicleCrew deleteWaypoint detach detectedMines ' +\n        'diag_activeMissionFSMs diag_activeScripts diag_activeSQFScripts diag_activeSQSScripts ' +\n        'diag_captureFrame diag_captureFrameToFile diag_captureSlowFrame diag_codePerformance ' +\n        'diag_drawMode diag_enable diag_enabled diag_fps diag_fpsMin diag_frameNo diag_lightNewLoad ' +\n        'diag_list diag_log diag_logSlowFrame diag_mergeConfigFile diag_recordTurretLimits ' +\n        'diag_setLightNew diag_tickTime diag_toggle dialog diarySubjectExists didJIP didJIPOwner ' +\n        'difficulty difficultyEnabled difficultyEnabledRTD difficultyOption direction directSay disableAI ' +\n        'disableCollisionWith disableConversation disableDebriefingStats disableMapIndicators ' +\n        'disableNVGEquipment disableRemoteSensors disableSerialization disableTIEquipment ' +\n        'disableUAVConnectability disableUserInput displayAddEventHandler displayCtrl displayParent ' +\n        'displayRemoveAllEventHandlers displayRemoveEventHandler displaySetEventHandler dissolveTeam ' +\n        'distance distance2D distanceSqr distributionRegion do3DENAction doArtilleryFire doFire doFollow ' +\n        'doFSM doGetOut doMove doorPhase doStop doSuppressiveFire doTarget doWatch drawArrow drawEllipse ' +\n        'drawIcon drawIcon3D drawLine drawLine3D drawLink drawLocation drawPolygon drawRectangle ' +\n        'drawTriangle driver drop dynamicSimulationDistance dynamicSimulationDistanceCoef ' +\n        'dynamicSimulationEnabled dynamicSimulationSystemEnabled echo edit3DENMissionAttributes editObject ' +\n        'editorSetEventHandler effectiveCommander emptyPositions enableAI enableAIFeature ' +\n        'enableAimPrecision enableAttack enableAudioFeature enableAutoStartUpRTD enableAutoTrimRTD ' +\n        'enableCamShake enableCaustics enableChannel enableCollisionWith enableCopilot ' +\n        'enableDebriefingStats enableDiagLegend enableDynamicSimulation enableDynamicSimulationSystem ' +\n        'enableEndDialog enableEngineArtillery enableEnvironment enableFatigue enableGunLights ' +\n        'enableInfoPanelComponent enableIRLasers enableMimics enablePersonTurret enableRadio enableReload ' +\n        'enableRopeAttach enableSatNormalOnDetail enableSaving enableSentences enableSimulation ' +\n        'enableSimulationGlobal enableStamina enableTeamSwitch enableTraffic enableUAVConnectability ' +\n        'enableUAVWaypoints enableVehicleCargo enableVehicleSensor enableWeaponDisassembly ' +\n        'endLoadingScreen endMission engineOn enginesIsOnRTD enginesRpmRTD enginesTorqueRTD entities ' +\n        'environmentEnabled estimatedEndServerTime estimatedTimeLeft evalObjectArgument everyBackpack ' +\n        'everyContainer exec execEditorScript execFSM execVM exp expectedDestination exportJIPMessages ' +\n        'eyeDirection eyePos face faction fadeMusic fadeRadio fadeSound fadeSpeech failMission ' +\n        'fillWeaponsFromPool find findCover findDisplay findEditorObject findEmptyPosition ' +\n        'findEmptyPositionReady findIf findNearestEnemy finishMissionInit finite fire fireAtTarget ' +\n        'firstBackpack flag flagAnimationPhase flagOwner flagSide flagTexture fleeing floor flyInHeight ' +\n        'flyInHeightASL fog fogForecast fogParams forceAddUniform forcedMap forceEnd forceFlagTexture ' +\n        'forceFollowRoad forceMap forceRespawn forceSpeed forceWalk forceWeaponFire forceWeatherChange ' +\n        'forEachMember forEachMemberAgent forEachMemberTeam forgetTarget format formation ' +\n        'formationDirection formationLeader formationMembers formationPosition formationTask formatText ' +\n        'formLeader freeLook fromEditor fuel fullCrew gearIDCAmmoCount gearSlotAmmoCount gearSlotData ' +\n        'get3DENActionState get3DENAttribute get3DENCamera get3DENConnections get3DENEntity ' +\n        'get3DENEntityID get3DENGrid get3DENIconsVisible get3DENLayerEntities get3DENLinesVisible ' +\n        'get3DENMissionAttribute get3DENMouseOver get3DENSelected getAimingCoef getAllEnvSoundControllers ' +\n        'getAllHitPointsDamage getAllOwnedMines getAllSoundControllers getAmmoCargo getAnimAimPrecision ' +\n        'getAnimSpeedCoef getArray getArtilleryAmmo getArtilleryComputerSettings getArtilleryETA ' +\n        'getAssignedCuratorLogic getAssignedCuratorUnit getBackpackCargo getBleedingRemaining ' +\n        'getBurningValue getCameraViewDirection getCargoIndex getCenterOfMass getClientState ' +\n        'getClientStateNumber getCompatiblePylonMagazines getConnectedUAV getContainerMaxLoad ' +\n        'getCursorObjectParams getCustomAimCoef getDammage getDescription getDir getDirVisual ' +\n        'getDLCAssetsUsage getDLCAssetsUsageByName getDLCs getEditorCamera getEditorMode ' +\n        'getEditorObjectScope getElevationOffset getEnvSoundController getFatigue getForcedFlagTexture ' +\n        'getFriend getFSMVariable getFuelCargo getGroupIcon getGroupIconParams getGroupIcons getHideFrom ' +\n        'getHit getHitIndex getHitPointDamage getItemCargo getMagazineCargo getMarkerColor getMarkerPos ' +\n        'getMarkerSize getMarkerType getMass getMissionConfig getMissionConfigValue getMissionDLCs ' +\n        'getMissionLayerEntities getModelInfo getMousePosition getMusicPlayedTime getNumber ' +\n        'getObjectArgument getObjectChildren getObjectDLC getObjectMaterials getObjectProxy ' +\n        'getObjectTextures getObjectType getObjectViewDistance getOxygenRemaining getPersonUsedDLCs ' +\n        'getPilotCameraDirection getPilotCameraPosition getPilotCameraRotation getPilotCameraTarget ' +\n        'getPlateNumber getPlayerChannel getPlayerScores getPlayerUID getPos getPosASL getPosASLVisual ' +\n        'getPosASLW getPosATL getPosATLVisual getPosVisual getPosWorld getPylonMagazines getRelDir ' +\n        'getRelPos getRemoteSensorsDisabled getRepairCargo getResolution getShadowDistance getShotParents ' +\n        'getSlingLoad getSoundController getSoundControllerResult getSpeed getStamina getStatValue ' +\n        'getSuppression getTerrainGrid getTerrainHeightASL getText getTotalDLCUsageTime getUnitLoadout ' +\n        'getUnitTrait getUserMFDText getUserMFDvalue getVariable getVehicleCargo getWeaponCargo ' +\n        'getWeaponSway getWingsOrientationRTD getWingsPositionRTD getWPPos glanceAt globalChat globalRadio ' +\n        'goggles goto group groupChat groupFromNetId groupIconSelectable groupIconsVisible groupId ' +\n        'groupOwner groupRadio groupSelectedUnits groupSelectUnit gunner gusts halt handgunItems ' +\n        'handgunMagazine handgunWeapon handsHit hasInterface hasPilotCamera hasWeapon hcAllGroups ' +\n        'hcGroupParams hcLeader hcRemoveAllGroups hcRemoveGroup hcSelected hcSelectGroup hcSetGroup ' +\n        'hcShowBar hcShownBar headgear hideBody hideObject hideObjectGlobal hideSelection hint hintC ' +\n        'hintCadet hintSilent hmd hostMission htmlLoad HUDMovementLevels humidity image importAllGroups ' +\n        'importance in inArea inAreaArray incapacitatedState inflame inflamed infoPanel ' +\n        'infoPanelComponentEnabled infoPanelComponents infoPanels inGameUISetEventHandler inheritsFrom ' +\n        'initAmbientLife inPolygon inputAction inRangeOfArtillery insertEditorObject intersect is3DEN ' +\n        'is3DENMultiplayer isAbleToBreathe isAgent isArray isAutoHoverOn isAutonomous isAutotest ' +\n        'isBleeding isBurning isClass isCollisionLightOn isCopilotEnabled isDamageAllowed isDedicated ' +\n        'isDLCAvailable isEngineOn isEqualTo isEqualType isEqualTypeAll isEqualTypeAny isEqualTypeArray ' +\n        'isEqualTypeParams isFilePatchingEnabled isFlashlightOn isFlatEmpty isForcedWalk isFormationLeader ' +\n        'isGroupDeletedWhenEmpty isHidden isInRemainsCollector isInstructorFigureEnabled isIRLaserOn ' +\n        'isKeyActive isKindOf isLaserOn isLightOn isLocalized isManualFire isMarkedForCollection ' +\n        'isMultiplayer isMultiplayerSolo isNil isNull isNumber isObjectHidden isObjectRTD isOnRoad ' +\n        'isPipEnabled isPlayer isRealTime isRemoteExecuted isRemoteExecutedJIP isServer isShowing3DIcons ' +\n        'isSimpleObject isSprintAllowed isStaminaEnabled isSteamMission isStreamFriendlyUIEnabled isText ' +\n        'isTouchingGround isTurnedOut isTutHintsEnabled isUAVConnectable isUAVConnected isUIContext ' +\n        'isUniformAllowed isVehicleCargo isVehicleRadarOn isVehicleSensorEnabled isWalking ' +\n        'isWeaponDeployed isWeaponRested itemCargo items itemsWithMagazines join joinAs joinAsSilent ' +\n        'joinSilent joinString kbAddDatabase kbAddDatabaseTargets kbAddTopic kbHasTopic kbReact ' +\n        'kbRemoveTopic kbTell kbWasSaid keyImage keyName knowsAbout land landAt landResult language ' +\n        'laserTarget lbAdd lbClear lbColor lbColorRight lbCurSel lbData lbDelete lbIsSelected lbPicture ' +\n        'lbPictureRight lbSelection lbSetColor lbSetColorRight lbSetCurSel lbSetData lbSetPicture ' +\n        'lbSetPictureColor lbSetPictureColorDisabled lbSetPictureColorSelected lbSetPictureRight ' +\n        'lbSetPictureRightColor lbSetPictureRightColorDisabled lbSetPictureRightColorSelected ' +\n        'lbSetSelectColor lbSetSelectColorRight lbSetSelected lbSetText lbSetTextRight lbSetTooltip ' +\n        'lbSetValue lbSize lbSort lbSortByValue lbText lbTextRight lbValue leader leaderboardDeInit ' +\n        'leaderboardGetRows leaderboardInit leaderboardRequestRowsFriends leaderboardsRequestUploadScore ' +\n        'leaderboardsRequestUploadScoreKeepBest leaderboardState leaveVehicle libraryCredits ' +\n        'libraryDisclaimers lifeState lightAttachObject lightDetachObject lightIsOn lightnings limitSpeed ' +\n        'linearConversion lineIntersects lineIntersectsObjs lineIntersectsSurfaces lineIntersectsWith ' +\n        'linkItem list listObjects listRemoteTargets listVehicleSensors ln lnbAddArray lnbAddColumn ' +\n        'lnbAddRow lnbClear lnbColor lnbCurSelRow lnbData lnbDeleteColumn lnbDeleteRow ' +\n        'lnbGetColumnsPosition lnbPicture lnbSetColor lnbSetColumnsPos lnbSetCurSelRow lnbSetData ' +\n        'lnbSetPicture lnbSetText lnbSetValue lnbSize lnbSort lnbSortByValue lnbText lnbValue load loadAbs ' +\n        'loadBackpack loadFile loadGame loadIdentity loadMagazine loadOverlay loadStatus loadUniform ' +\n        'loadVest local localize locationPosition lock lockCameraTo lockCargo lockDriver locked ' +\n        'lockedCargo lockedDriver lockedTurret lockIdentity lockTurret lockWP log logEntities logNetwork ' +\n        'logNetworkTerminate lookAt lookAtPos magazineCargo magazines magazinesAllTurrets magazinesAmmo ' +\n        'magazinesAmmoCargo magazinesAmmoFull magazinesDetail magazinesDetailBackpack ' +\n        'magazinesDetailUniform magazinesDetailVest magazinesTurret magazineTurretAmmo mapAnimAdd ' +\n        'mapAnimClear mapAnimCommit mapAnimDone mapCenterOnCamera mapGridPosition markAsFinishedOnSteam ' +\n        'markerAlpha markerBrush markerColor markerDir markerPos markerShape markerSize markerText ' +\n        'markerType max members menuAction menuAdd menuChecked menuClear menuCollapse menuData menuDelete ' +\n        'menuEnable menuEnabled menuExpand menuHover menuPicture menuSetAction menuSetCheck menuSetData ' +\n        'menuSetPicture menuSetValue menuShortcut menuShortcutText menuSize menuSort menuText menuURL ' +\n        'menuValue min mineActive mineDetectedBy missionConfigFile missionDifficulty missionName ' +\n        'missionNamespace missionStart missionVersion mod modelToWorld modelToWorldVisual ' +\n        'modelToWorldVisualWorld modelToWorldWorld modParams moonIntensity moonPhase morale move ' +\n        'move3DENCamera moveInAny moveInCargo moveInCommander moveInDriver moveInGunner moveInTurret ' +\n        'moveObjectToEnd moveOut moveTime moveTo moveToCompleted moveToFailed musicVolume name nameSound ' +\n        'nearEntities nearestBuilding nearestLocation nearestLocations nearestLocationWithDubbing ' +\n        'nearestObject nearestObjects nearestTerrainObjects nearObjects nearObjectsReady nearRoads ' +\n        'nearSupplies nearTargets needReload netId netObjNull newOverlay nextMenuItemIndex ' +\n        'nextWeatherChange nMenuItems not numberOfEnginesRTD numberToDate objectCurators objectFromNetId ' +\n        'objectParent objStatus onBriefingGroup onBriefingNotes onBriefingPlan onBriefingTeamSwitch ' +\n        'onCommandModeChanged onDoubleClick onEachFrame onGroupIconClick onGroupIconOverEnter ' +\n        'onGroupIconOverLeave onHCGroupSelectionChanged onMapSingleClick onPlayerConnected ' +\n        'onPlayerDisconnected onPreloadFinished onPreloadStarted onShowNewObject onTeamSwitch ' +\n        'openCuratorInterface openDLCPage openMap openSteamApp openYoutubeVideo or orderGetIn overcast ' +\n        'overcastForecast owner param params parseNumber parseSimpleArray parseText parsingNamespace ' +\n        'particlesQuality pickWeaponPool pitch pixelGrid pixelGridBase pixelGridNoUIScale pixelH pixelW ' +\n        'playableSlotsNumber playableUnits playAction playActionNow player playerRespawnTime playerSide ' +\n        'playersNumber playGesture playMission playMove playMoveNow playMusic playScriptedMission ' +\n        'playSound playSound3D position positionCameraToWorld posScreenToWorld posWorldToScreen ' +\n        'ppEffectAdjust ppEffectCommit ppEffectCommitted ppEffectCreate ppEffectDestroy ppEffectEnable ' +\n        'ppEffectEnabled ppEffectForceInNVG precision preloadCamera preloadObject preloadSound ' +\n        'preloadTitleObj preloadTitleRsc preprocessFile preprocessFileLineNumbers primaryWeapon ' +\n        'primaryWeaponItems primaryWeaponMagazine priority processDiaryLink productVersion profileName ' +\n        'profileNamespace profileNameSteam progressLoadingScreen progressPosition progressSetPosition ' +\n        'publicVariable publicVariableClient publicVariableServer pushBack pushBackUnique putWeaponPool ' +\n        'queryItemsPool queryMagazinePool queryWeaponPool rad radioChannelAdd radioChannelCreate ' +\n        'radioChannelRemove radioChannelSetCallSign radioChannelSetLabel radioVolume rain rainbow random ' +\n        'rank rankId rating rectangular registeredTasks registerTask reload reloadEnabled remoteControl ' +\n        'remoteExec remoteExecCall remoteExecutedOwner remove3DENConnection remove3DENEventHandler ' +\n        'remove3DENLayer removeAction removeAll3DENEventHandlers removeAllActions removeAllAssignedItems ' +\n        'removeAllContainers removeAllCuratorAddons removeAllCuratorCameraAreas ' +\n        'removeAllCuratorEditingAreas removeAllEventHandlers removeAllHandgunItems removeAllItems ' +\n        'removeAllItemsWithMagazines removeAllMissionEventHandlers removeAllMPEventHandlers ' +\n        'removeAllMusicEventHandlers removeAllOwnedMines removeAllPrimaryWeaponItems removeAllWeapons ' +\n        'removeBackpack removeBackpackGlobal removeCuratorAddons removeCuratorCameraArea ' +\n        'removeCuratorEditableObjects removeCuratorEditingArea removeDrawIcon removeDrawLinks ' +\n        'removeEventHandler removeFromRemainsCollector removeGoggles removeGroupIcon removeHandgunItem ' +\n        'removeHeadgear removeItem removeItemFromBackpack removeItemFromUniform removeItemFromVest ' +\n        'removeItems removeMagazine removeMagazineGlobal removeMagazines removeMagazinesTurret ' +\n        'removeMagazineTurret removeMenuItem removeMissionEventHandler removeMPEventHandler ' +\n        'removeMusicEventHandler removeOwnedMine removePrimaryWeaponItem removeSecondaryWeaponItem ' +\n        'removeSimpleTask removeSwitchableUnit removeTeamMember removeUniform removeVest removeWeapon ' +\n        'removeWeaponAttachmentCargo removeWeaponCargo removeWeaponGlobal removeWeaponTurret ' +\n        'reportRemoteTarget requiredVersion resetCamShake resetSubgroupDirection resize resources ' +\n        'respawnVehicle restartEditorCamera reveal revealMine reverse reversedMouseY roadAt ' +\n        'roadsConnectedTo roleDescription ropeAttachedObjects ropeAttachedTo ropeAttachEnabled ' +\n        'ropeAttachTo ropeCreate ropeCut ropeDestroy ropeDetach ropeEndPosition ropeLength ropes ' +\n        'ropeUnwind ropeUnwound rotorsForcesRTD rotorsRpmRTD round runInitScript safeZoneH safeZoneW ' +\n        'safeZoneWAbs safeZoneX safeZoneXAbs safeZoneY save3DENInventory saveGame saveIdentity ' +\n        'saveJoysticks saveOverlay saveProfileNamespace saveStatus saveVar savingEnabled say say2D say3D ' +\n        'scopeName score scoreSide screenshot screenToWorld scriptDone scriptName scudState ' +\n        'secondaryWeapon secondaryWeaponItems secondaryWeaponMagazine select selectBestPlaces ' +\n        'selectDiarySubject selectedEditorObjects selectEditorObject selectionNames selectionPosition ' +\n        'selectLeader selectMax selectMin selectNoPlayer selectPlayer selectRandom selectRandomWeighted ' +\n        'selectWeapon selectWeaponTurret sendAUMessage sendSimpleCommand sendTask sendTaskResult ' +\n        'sendUDPMessage serverCommand serverCommandAvailable serverCommandExecutable serverName serverTime ' +\n        'set set3DENAttribute set3DENAttributes set3DENGrid set3DENIconsVisible set3DENLayer ' +\n        'set3DENLinesVisible set3DENLogicType set3DENMissionAttribute set3DENMissionAttributes ' +\n        'set3DENModelsVisible set3DENObjectType set3DENSelected setAccTime setActualCollectiveRTD ' +\n        'setAirplaneThrottle setAirportSide setAmmo setAmmoCargo setAmmoOnPylon setAnimSpeedCoef ' +\n        'setAperture setApertureNew setArmoryPoints setAttributes setAutonomous setBehaviour ' +\n        'setBleedingRemaining setBrakesRTD setCameraInterest setCamShakeDefParams setCamShakeParams ' +\n        'setCamUseTI setCaptive setCenterOfMass setCollisionLight setCombatMode setCompassOscillation ' +\n        'setConvoySeparation setCuratorCameraAreaCeiling setCuratorCoef setCuratorEditingAreaType ' +\n        'setCuratorWaypointCost setCurrentChannel setCurrentTask setCurrentWaypoint setCustomAimCoef ' +\n        'setCustomWeightRTD setDamage setDammage setDate setDebriefingText setDefaultCamera setDestination ' +\n        'setDetailMapBlendPars setDir setDirection setDrawIcon setDriveOnPath setDropInterval ' +\n        'setDynamicSimulationDistance setDynamicSimulationDistanceCoef setEditorMode setEditorObjectScope ' +\n        'setEffectCondition setEngineRPMRTD setFace setFaceAnimation setFatigue setFeatureType ' +\n        'setFlagAnimationPhase setFlagOwner setFlagSide setFlagTexture setFog setFormation ' +\n        'setFormationTask setFormDir setFriend setFromEditor setFSMVariable setFuel setFuelCargo ' +\n        'setGroupIcon setGroupIconParams setGroupIconsSelectable setGroupIconsVisible setGroupId ' +\n        'setGroupIdGlobal setGroupOwner setGusts setHideBehind setHit setHitIndex setHitPointDamage ' +\n        'setHorizonParallaxCoef setHUDMovementLevels setIdentity setImportance setInfoPanel setLeader ' +\n        'setLightAmbient setLightAttenuation setLightBrightness setLightColor setLightDayLight ' +\n        'setLightFlareMaxDistance setLightFlareSize setLightIntensity setLightnings setLightUseFlare ' +\n        'setLocalWindParams setMagazineTurretAmmo setMarkerAlpha setMarkerAlphaLocal setMarkerBrush ' +\n        'setMarkerBrushLocal setMarkerColor setMarkerColorLocal setMarkerDir setMarkerDirLocal ' +\n        'setMarkerPos setMarkerPosLocal setMarkerShape setMarkerShapeLocal setMarkerSize ' +\n        'setMarkerSizeLocal setMarkerText setMarkerTextLocal setMarkerType setMarkerTypeLocal setMass ' +\n        'setMimic setMousePosition setMusicEffect setMusicEventHandler setName setNameSound ' +\n        'setObjectArguments setObjectMaterial setObjectMaterialGlobal setObjectProxy setObjectTexture ' +\n        'setObjectTextureGlobal setObjectViewDistance setOvercast setOwner setOxygenRemaining ' +\n        'setParticleCircle setParticleClass setParticleFire setParticleParams setParticleRandom ' +\n        'setPilotCameraDirection setPilotCameraRotation setPilotCameraTarget setPilotLight setPiPEffect ' +\n        'setPitch setPlateNumber setPlayable setPlayerRespawnTime setPos setPosASL setPosASL2 setPosASLW ' +\n        'setPosATL setPosition setPosWorld setPylonLoadOut setPylonsPriority setRadioMsg setRain ' +\n        'setRainbow setRandomLip setRank setRectangular setRepairCargo setRotorBrakeRTD setShadowDistance ' +\n        'setShotParents setSide setSimpleTaskAlwaysVisible setSimpleTaskCustomData ' +\n        'setSimpleTaskDescription setSimpleTaskDestination setSimpleTaskTarget setSimpleTaskType ' +\n        'setSimulWeatherLayers setSize setSkill setSlingLoad setSoundEffect setSpeaker setSpeech ' +\n        'setSpeedMode setStamina setStaminaScheme setStatValue setSuppression setSystemOfUnits ' +\n        'setTargetAge setTaskMarkerOffset setTaskResult setTaskState setTerrainGrid setText ' +\n        'setTimeMultiplier setTitleEffect setTrafficDensity setTrafficDistance setTrafficGap ' +\n        'setTrafficSpeed setTriggerActivation setTriggerArea setTriggerStatements setTriggerText ' +\n        'setTriggerTimeout setTriggerType setType setUnconscious setUnitAbility setUnitLoadout setUnitPos ' +\n        'setUnitPosWeak setUnitRank setUnitRecoilCoefficient setUnitTrait setUnloadInCombat ' +\n        'setUserActionText setUserMFDText setUserMFDvalue setVariable setVectorDir setVectorDirAndUp ' +\n        'setVectorUp setVehicleAmmo setVehicleAmmoDef setVehicleArmor setVehicleCargo setVehicleId ' +\n        'setVehicleLock setVehiclePosition setVehicleRadar setVehicleReceiveRemoteTargets ' +\n        'setVehicleReportOwnPosition setVehicleReportRemoteTargets setVehicleTIPars setVehicleVarName ' +\n        'setVelocity setVelocityModelSpace setVelocityTransformation setViewDistance ' +\n        'setVisibleIfTreeCollapsed setWantedRPMRTD setWaves setWaypointBehaviour setWaypointCombatMode ' +\n        'setWaypointCompletionRadius setWaypointDescription setWaypointForceBehaviour setWaypointFormation ' +\n        'setWaypointHousePosition setWaypointLoiterRadius setWaypointLoiterType setWaypointName ' +\n        'setWaypointPosition setWaypointScript setWaypointSpeed setWaypointStatements setWaypointTimeout ' +\n        'setWaypointType setWaypointVisible setWeaponReloadingTime setWind setWindDir setWindForce ' +\n        'setWindStr setWingForceScaleRTD setWPPos show3DIcons showChat showCinemaBorder showCommandingMenu ' +\n        'showCompass showCuratorCompass showGPS showHUD showLegend showMap shownArtilleryComputer ' +\n        'shownChat shownCompass shownCuratorCompass showNewEditorObject shownGPS shownHUD shownMap ' +\n        'shownPad shownRadio shownScoretable shownUAVFeed shownWarrant shownWatch showPad showRadio ' +\n        'showScoretable showSubtitles showUAVFeed showWarrant showWatch showWaypoint showWaypoints side ' +\n        'sideChat sideEnemy sideFriendly sideRadio simpleTasks simulationEnabled simulCloudDensity ' +\n        'simulCloudOcclusion simulInClouds simulWeatherSync sin size sizeOf skill skillFinal skipTime ' +\n        'sleep sliderPosition sliderRange sliderSetPosition sliderSetRange sliderSetSpeed sliderSpeed ' +\n        'slingLoadAssistantShown soldierMagazines someAmmo sort soundVolume spawn speaker speed speedMode ' +\n        'splitString sqrt squadParams stance startLoadingScreen step stop stopEngineRTD stopped str ' +\n        'sunOrMoon supportInfo suppressFor surfaceIsWater surfaceNormal surfaceType swimInDepth ' +\n        'switchableUnits switchAction switchCamera switchGesture switchLight switchMove ' +\n        'synchronizedObjects synchronizedTriggers synchronizedWaypoints synchronizeObjectsAdd ' +\n        'synchronizeObjectsRemove synchronizeTrigger synchronizeWaypoint systemChat systemOfUnits tan ' +\n        'targetKnowledge targets targetsAggregate targetsQuery taskAlwaysVisible taskChildren ' +\n        'taskCompleted taskCustomData taskDescription taskDestination taskHint taskMarkerOffset taskParent ' +\n        'taskResult taskState taskType teamMember teamName teams teamSwitch teamSwitchEnabled teamType ' +\n        'terminate terrainIntersect terrainIntersectASL terrainIntersectAtASL text textLog textLogFormat ' +\n        'tg time timeMultiplier titleCut titleFadeOut titleObj titleRsc titleText toArray toFixed toLower ' +\n        'toString toUpper triggerActivated triggerActivation triggerArea triggerAttachedVehicle ' +\n        'triggerAttachObject triggerAttachVehicle triggerDynamicSimulation triggerStatements triggerText ' +\n        'triggerTimeout triggerTimeoutCurrent triggerType turretLocal turretOwner turretUnit tvAdd tvClear ' +\n        'tvCollapse tvCollapseAll tvCount tvCurSel tvData tvDelete tvExpand tvExpandAll tvPicture ' +\n        'tvSetColor tvSetCurSel tvSetData tvSetPicture tvSetPictureColor tvSetPictureColorDisabled ' +\n        'tvSetPictureColorSelected tvSetPictureRight tvSetPictureRightColor tvSetPictureRightColorDisabled ' +\n        'tvSetPictureRightColorSelected tvSetText tvSetTooltip tvSetValue tvSort tvSortByValue tvText ' +\n        'tvTooltip tvValue type typeName typeOf UAVControl uiNamespace uiSleep unassignCurator ' +\n        'unassignItem unassignTeam unassignVehicle underwater uniform uniformContainer uniformItems ' +\n        'uniformMagazines unitAddons unitAimPosition unitAimPositionVisual unitBackpack unitIsUAV unitPos ' +\n        'unitReady unitRecoilCoefficient units unitsBelowHeight unlinkItem unlockAchievement ' +\n        'unregisterTask updateDrawIcon updateMenuItem updateObjectTree useAISteeringComponent ' +\n        'useAudioTimeForMoves userInputDisabled vectorAdd vectorCos vectorCrossProduct vectorDiff ' +\n        'vectorDir vectorDirVisual vectorDistance vectorDistanceSqr vectorDotProduct vectorFromTo ' +\n        'vectorMagnitude vectorMagnitudeSqr vectorModelToWorld vectorModelToWorldVisual vectorMultiply ' +\n        'vectorNormalized vectorUp vectorUpVisual vectorWorldToModel vectorWorldToModelVisual vehicle ' +\n        'vehicleCargoEnabled vehicleChat vehicleRadio vehicleReceiveRemoteTargets vehicleReportOwnPosition ' +\n        'vehicleReportRemoteTargets vehicles vehicleVarName velocity velocityModelSpace verifySignature ' +\n        'vest vestContainer vestItems vestMagazines viewDistance visibleCompass visibleGPS visibleMap ' +\n        'visiblePosition visiblePositionASL visibleScoretable visibleWatch waves waypointAttachedObject ' +\n        'waypointAttachedVehicle waypointAttachObject waypointAttachVehicle waypointBehaviour ' +\n        'waypointCombatMode waypointCompletionRadius waypointDescription waypointForceBehaviour ' +\n        'waypointFormation waypointHousePosition waypointLoiterRadius waypointLoiterType waypointName ' +\n        'waypointPosition waypoints waypointScript waypointsEnabledUAV waypointShow waypointSpeed ' +\n        'waypointStatements waypointTimeout waypointTimeoutCurrent waypointType waypointVisible ' +\n        'weaponAccessories weaponAccessoriesCargo weaponCargo weaponDirection weaponInertia weaponLowered ' +\n        'weapons weaponsItems weaponsItemsCargo weaponState weaponsTurret weightRTD WFSideText wind ',\n      literal:\n        'blufor civilian configNull controlNull displayNull east endl false grpNull independent lineBreak ' +\n        'locationNull nil objNull opfor pi resistance scriptNull sideAmbientLife sideEmpty sideLogic ' +\n        'sideUnknown taskNull teamMemberNull true west'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.NUMBER_MODE,\n      VARIABLE,\n      FUNCTION,\n      STRINGS,\n      PREPROCESSOR\n    ],\n    illegal: /#|^\\$ /\n  };\n}\n\nmodule.exports = sqf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB;EACA,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;;EAED;EACA;EACA,MAAMC,QAAQ,GAAG;IACfF,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE;EACT,CAAC;;EAED;EACA;EACA,MAAME,OAAO,GAAG;IACdH,SAAS,EAAE,QAAQ;IACnBI,QAAQ,EAAE,CACR;MACEH,KAAK,EAAE,GAAG;MACVI,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CAAE;QACVL,KAAK,EAAE,IAAI;QACXM,SAAS,EAAE;MACb,CAAC;IACH,CAAC,EACD;MACEN,KAAK,EAAE,IAAI;MACXI,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,CAAE;QACVL,KAAK,EAAE,MAAM;QACbM,SAAS,EAAE;MACb,CAAC;IACH,CAAC;EAEL,CAAC;;EAED;EACA;EACA,MAAMC,YAAY,GAAG;IACnBR,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,cAAc;IACrBI,GAAG,EAAE,GAAG;IACRI,QAAQ,EAAE;MACR,cAAc,EACZ;IACJ,CAAC;IACDH,QAAQ,EAAE,CACR;MACEL,KAAK,EAAE,MAAM;MACbM,SAAS,EAAE;IACb,CAAC,EACDT,IAAI,CAACY,OAAO,CAACP,OAAO,EAAE;MACpBH,SAAS,EAAE;IACb,CAAC,CAAC,EACF;MACEA,SAAS,EAAE,aAAa;MACxBC,KAAK,EAAE,WAAW;MAClBI,GAAG,EAAE,GAAG;MACRM,OAAO,EAAE;IACX,CAAC,EACDb,IAAI,CAACc,mBAAmB,EACxBd,IAAI,CAACe,oBAAoB;EAE7B,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,gBAAgB,EAAE,IAAI;IACtBN,QAAQ,EAAE;MACRO,OAAO,EACL,+DAA+D,GAC/D,uDAAuD;MACzDC,QAAQ,EACN,gFAAgF,GAChF,0FAA0F,GAC1F,4FAA4F,GAC5F,6FAA6F,GAC7F,mGAAmG,GACnG,mFAAmF,GACnF,+FAA+F,GAC/F,6FAA6F,GAC7F,4FAA4F,GAC5F,2FAA2F,GAC3F,oGAAoG,GACpG,gGAAgG,GAChG,0FAA0F,GAC1F,+FAA+F,GAC/F,2FAA2F,GAC3F,2FAA2F,GAC3F,6FAA6F,GAC7F,mGAAmG,GACnG,gGAAgG,GAChG,4FAA4F,GAC5F,wFAAwF,GACxF,kGAAkG,GAClG,4FAA4F,GAC5F,wFAAwF,GACxF,iGAAiG,GACjG,kGAAkG,GAClG,4FAA4F,GAC5F,gGAAgG,GAChG,iGAAiG,GACjG,6FAA6F,GAC7F,yFAAyF,GACzF,mGAAmG,GACnG,8FAA8F,GAC9F,oGAAoG,GACpG,+EAA+E,GAC/E,6FAA6F,GAC7F,0FAA0F,GAC1F,wFAAwF,GACxF,gGAAgG,GAChG,oGAAoG,GACpG,0FAA0F,GAC1F,gGAAgG,GAChG,+EAA+E,GAC/E,+FAA+F,GAC/F,gGAAgG,GAChG,gFAAgF,GAChF,mGAAmG,GACnG,mGAAmG,GACnG,6FAA6F,GAC7F,4FAA4F,GAC5F,uFAAuF,GACvF,yFAAyF,GACzF,+FAA+F,GAC/F,mGAAmG,GACnG,8FAA8F,GAC9F,8FAA8F,GAC9F,uFAAuF,GACvF,mGAAmG,GACnG,oGAAoG,GACpG,oGAAoG,GACpG,2FAA2F,GAC3F,+FAA+F,GAC/F,mGAAmG,GACnG,yFAAyF,GACzF,+FAA+F,GAC/F,yFAAyF,GACzF,sFAAsF,GACtF,4FAA4F,GAC5F,2FAA2F,GAC3F,8FAA8F,GAC9F,oFAAoF,GACpF,sFAAsF,GACtF,mGAAmG,GACnG,iGAAiG,GACjG,yFAAyF,GACzF,wFAAwF,GACxF,+FAA+F,GAC/F,+FAA+F,GAC/F,2FAA2F,GAC3F,8FAA8F,GAC9F,sFAAsF,GACtF,oGAAoG,GACpG,iFAAiF,GACjF,wFAAwF,GACxF,uFAAuF,GACvF,6FAA6F,GAC7F,oFAAoF,GACpF,0FAA0F,GAC1F,mGAAmG,GACnG,uFAAuF,GACvF,mFAAmF,GACnF,6FAA6F,GAC7F,8FAA8F,GAC9F,kGAAkG,GAClG,kGAAkG,GAClG,0FAA0F,GAC1F,mFAAmF,GACnF,oGAAoG,GACpG,mFAAmF,GACnF,4FAA4F,GAC5F,gFAAgF,GAChF,+FAA+F,GAC/F,wFAAwF,GACxF,mGAAmG,GACnG,yFAAyF,GACzF,8FAA8F,GAC9F,oFAAoF,GACpF,8FAA8F,GAC9F,+FAA+F,GAC/F,gGAAgG,GAChG,wFAAwF,GACxF,oFAAoF,GACpF,4FAA4F,GAC5F,iGAAiG,GACjG,+FAA+F,GAC/F,gGAAgG,GAChG,mFAAmF,GACnF,iGAAiG,GACjG,+FAA+F,GAC/F,qFAAqF,GACrF,2FAA2F,GAC3F,mGAAmG,GACnG,iGAAiG,GACjG,0FAA0F,GAC1F,uFAAuF,GACvF,sFAAsF,GACtF,uFAAuF,GACvF,uFAAuF,GACvF,kFAAkF,GAClF,gGAAgG,GAChG,kGAAkG,GAClG,iGAAiG,GACjG,4FAA4F,GAC5F,qFAAqF,GACrF,qFAAqF,GACrF,6FAA6F,GAC7F,6FAA6F,GAC7F,gGAAgG,GAChG,4FAA4F,GAC5F,mGAAmG,GACnG,4FAA4F,GAC5F,gGAAgG,GAChG,yFAAyF,GACzF,oGAAoG,GACpG,4FAA4F,GAC5F,0FAA0F,GAC1F,2FAA2F,GAC3F,6FAA6F,GAC7F,8FAA8F,GAC9F,iGAAiG,GACjG,iFAAiF,GACjF,gGAAgG,GAChG,+FAA+F,GAC/F,0FAA0F,GAC1F,+FAA+F,GAC/F,iGAAiG,GACjG,oGAAoG,GACpG,8FAA8F,GAC9F,0FAA0F,GAC1F,4FAA4F,GAC5F,kGAAkG,GAClG,kGAAkG,GAClG,6FAA6F,GAC7F,oFAAoF,GACpF,8FAA8F,GAC9F,yFAAyF,GACzF,6FAA6F,GAC7F,iGAAiG,GACjG,2FAA2F,GAC3F,0FAA0F,GAC1F,uFAAuF,GACvF,6FAA6F,GAC7F,6FAA6F,GAC7F,kGAAkG,GAClG,sFAAsF,GACtF,mGAAmG,GACnG,+FAA+F,GAC/F,6FAA6F,GAC7F,gFAAgF,GAChF,2FAA2F,GAC3F,oGAAoG,GACpG,8FAA8F,GAC9F,yFAAyF,GACzF,kGAAkG,GAClG,iGAAiG,GACjG,+EAA+E,GAC/E,2FAA2F,GAC3F,iGAAiG,GACjG,4FAA4F,GAC5F,mGAAmG,GACnG,iGAAiG,GACjG,+FAA+F,GAC/F,0FAA0F,GAC1F,mFAAmF,GACnF,0FAA0F,GAC1F,8FAA8F,GAC9F,kGAAkG,GAClG,2FAA2F,GAC3F,4FAA4F,GAC5F,oFAAoF,GACpF,kGAAkG,GAClG,6FAA6F,GAC7F,uFAAuF,GACvF,oFAAoF,GACpF,uFAAuF,GACvF,gGAAgG,GAChG,8FAA8F,GAC9F,iGAAiG,GACjG,iGAAiG,GACjG,2FAA2F,GAC3F,yFAAyF,GACzF,gGAAgG,GAChG,wFAAwF,GACxF,yFAAyF,GACzF,gGAAgG,GAChG,+FAA+F,GAC/F,iGAAiG,GACjG,0FAA0F,GAC1F,kGAAkG,GAClG,iGAAiG,GACjG,4FAA4F,GAC5F,kGAAkG,GAClG,yEAAyE,GACzE,2FAA2F,GAC3F,qFAAqF,GACrF,+FAA+F,GAC/F,kFAAkF,GAClF,uFAAuF,GACvF,gGAAgG,GAChG,4FAA4F,GAC5F,wFAAwF,GACxF,qFAAqF,GACrF,4FAA4F,GAC5F,+FAA+F,GAC/F,sFAAsF,GACtF,2FAA2F,GAC3F,qFAAqF,GACrF,wFAAwF,GACxF,0FAA0F,GAC1F,8FAA8F,GAC9F,wFAAwF,GACxF,kGAAkG,GAClG,qFAAqF,GACrF,uFAAuF,GACvF,+FAA+F,GAC/F,iGAAiG,GACjG,0FAA0F,GAC1F,oGAAoG,GACpG,sFAAsF,GACtF,wFAAwF,GACxF,2FAA2F,GAC3F,0FAA0F,GAC1F,sFAAsF,GACtF,6FAA6F,GAC7F,+FAA+F,GAC/F,2FAA2F,GAC3F,8FAA8F,GAC9F,oGAAoG,GACpG,uFAAuF,GACvF,mGAAmG,GACnG,wFAAwF,GACxF,oFAAoF,GACpF,0FAA0F,GAC1F,0FAA0F,GAC1F,6FAA6F,GAC7F,+FAA+F,GAC/F,wFAAwF,GACxF,8FAA8F,GAC9F,6FAA6F,GAC7F,wFAAwF,GACxF,kFAAkF,GAClF,+FAA+F,GAC/F,qFAAqF,GACrF,+FAA+F,GAC/F,uFAAuF,GACvF,yFAAyF,GACzF,iGAAiG,GACjG,kGAAkG,GAClG,0FAA0F,GAC1F,mGAAmG,GACnG,4EAA4E,GAC5E,0FAA0F,GAC1F,0FAA0F,GAC1F,wFAAwF,GACxF,qFAAqF,GACrF,sFAAsF,GACtF,0FAA0F,GAC1F,mGAAmG,GACnG,qFAAqF,GACrF,8FAA8F,GAC9F,4FAA4F,GAC5F,mFAAmF,GACnF,+FAA+F,GAC/F,8EAA8E,GAC9E,gGAAgG,GAChG,oGAAoG,GACpG,yFAAyF,GACzF,kGAAkG,GAClG,4FAA4F,GAC5F,oGAAoG,GACpG,2FAA2F,GAC3F,4FAA4F,GAC5F,6FAA6F,GAC7F,iGAAiG,GACjG,4FAA4F,GAC5F,+FAA+F,GAC/F,+FAA+F,GAC/F,mGAAmG,GACnG,6FAA6F,GAC7F,yFAAyF,GACzF,iFAAiF,GACjF,uFAAuF,GACvF,+FAA+F,GAC/F,uFAAuF,GACvF,oGAAoG,GACpG,gGAAgG,GAChG,kGAAkG,GAClG,mGAAmG,GACnG,yFAAyF,GACzF,kGAAkG,GAClG,oGAAoG,GACpG,2FAA2F,GAC3F,4FAA4F,GAC5F,oGAAoG,GACpG,+FAA+F,GAC/F,wFAAwF,GACxF,6FAA6F,GAC7F,mGAAmG,GACnG,sFAAsF,GACtF,uFAAuF,GACvF,2FAA2F,GAC3F,2FAA2F,GAC3F,gGAAgG,GAChG,+FAA+F,GAC/F,oGAAoG,GACpG,iGAAiG,GACjG,+FAA+F,GAC/F,iGAAiG,GACjG,uFAAuF,GACvF,yFAAyF,GACzF,+FAA+F,GAC/F,2FAA2F,GAC3F,yFAAyF,GACzF,mGAAmG,GACnG,6FAA6F;MAC/FC,OAAO,EACL,mGAAmG,GACnG,8FAA8F,GAC9F;IACJ,CAAC;IACDZ,QAAQ,EAAE,CACRR,IAAI,CAACc,mBAAmB,EACxBd,IAAI,CAACe,oBAAoB,EACzBf,IAAI,CAACqB,WAAW,EAChBpB,QAAQ,EACRG,QAAQ,EACRC,OAAO,EACPK,YAAY,CACb;IACDG,OAAO,EAAE;EACX,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGxB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}