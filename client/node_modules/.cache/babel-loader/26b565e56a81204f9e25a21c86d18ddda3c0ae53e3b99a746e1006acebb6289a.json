{"ast": null, "code": "'use strict';\n\nmodule.exports = erlang;\nerlang.displayName = 'erlang';\nerlang.aliases = [];\nfunction erlang(Prism) {\n  Prism.languages.erlang = {\n    comment: /%.+/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n      greedy: true\n    },\n    'quoted-function': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'(?=\\()/,\n      alias: 'function'\n    },\n    'quoted-atom': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'/,\n      alias: 'atom'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: /\\b(?:after|case|catch|end|fun|if|of|receive|try|when)\\b/,\n    number: [/\\$\\\\?./, /\\b\\d+#[a-z0-9]+/i, /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i],\n    function: /\\b[a-z][\\w@]*(?=\\()/,\n    variable: {\n      // Look-behind is used to prevent wrong highlighting of atoms containing \"@\"\n      pattern: /(^|[^@])(?:\\b|\\?)[A-Z_][\\w@]*/,\n      lookbehind: true\n    },\n    operator: [/[=\\/<>:]=|=[:\\/]=|\\+\\+?|--?|[=*\\/!]|\\b(?:and|andalso|band|bnot|bor|bsl|bsr|bxor|div|not|or|orelse|rem|xor)\\b/, {\n      // We don't want to match <<\n      pattern: /(^|[^<])<(?!<)/,\n      lookbehind: true\n    }, {\n      // We don't want to match >>\n      pattern: /(^|[^>])>(?!>)/,\n      lookbehind: true\n    }],\n    atom: /\\b[a-z][\\w@]*/,\n    punctuation: /[()[\\]{}:;,.#|]|<<|>>/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "erlang", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "alias", "boolean", "keyword", "number", "function", "variable", "lookbehind", "operator", "atom", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/erlang.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = erlang\nerlang.displayName = 'erlang'\nerlang.aliases = []\nfunction erlang(Prism) {\n  Prism.languages.erlang = {\n    comment: /%.+/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n      greedy: true\n    },\n    'quoted-function': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'(?=\\()/,\n      alias: 'function'\n    },\n    'quoted-atom': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'/,\n      alias: 'atom'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: /\\b(?:after|case|catch|end|fun|if|of|receive|try|when)\\b/,\n    number: [\n      /\\$\\\\?./,\n      /\\b\\d+#[a-z0-9]+/i,\n      /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i\n    ],\n    function: /\\b[a-z][\\w@]*(?=\\()/,\n    variable: {\n      // Look-behind is used to prevent wrong highlighting of atoms containing \"@\"\n      pattern: /(^|[^@])(?:\\b|\\?)[A-Z_][\\w@]*/,\n      lookbehind: true\n    },\n    operator: [\n      /[=\\/<>:]=|=[:\\/]=|\\+\\+?|--?|[=*\\/!]|\\b(?:and|andalso|band|bnot|bor|bsl|bsr|bxor|div|not|or|orelse|rem|xor)\\b/,\n      {\n        // We don't want to match <<\n        pattern: /(^|[^<])<(?!<)/,\n        lookbehind: true\n      },\n      {\n        // We don't want to match >>\n        pattern: /(^|[^>])>(?!>)/,\n        lookbehind: true\n      }\n    ],\n    atom: /\\b[a-z][\\w@]*/,\n    punctuation: /[()[\\]{}:;,.#|]|<<|>>/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,MAAM,EAAE;IACV,CAAC;IACD,iBAAiB,EAAE;MACjBD,OAAO,EAAE,6BAA6B;MACtCE,KAAK,EAAE;IACT,CAAC;IACD,aAAa,EAAE;MACbF,OAAO,EAAE,uBAAuB;MAChCE,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE,oBAAoB;IAC7BC,OAAO,EAAE,yDAAyD;IAClEC,MAAM,EAAE,CACN,QAAQ,EACR,kBAAkB,EAClB,4CAA4C,CAC7C;IACDC,QAAQ,EAAE,qBAAqB;IAC/BC,QAAQ,EAAE;MACR;MACAP,OAAO,EAAE,+BAA+B;MACxCQ,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE,CACR,8GAA8G,EAC9G;MACE;MACAT,OAAO,EAAE,gBAAgB;MACzBQ,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAR,OAAO,EAAE,gBAAgB;MACzBQ,UAAU,EAAE;IACd,CAAC,CACF;IACDE,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}