{"ast": null, "code": "/*\nLanguage: Mizar\nDescription: The Mizar Language is a formal language derived from the mathematical vernacular.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://mizar.org/language/\nCategory: scientific\n*/\n\nfunction mizar(hljs) {\n  return {\n    name: '<PERSON>zar',\n    keywords: 'environ vocabularies notations constructors definitions ' + 'registrations theorems schemes requirements begin end definition ' + 'registration cluster existence pred func defpred deffunc theorem ' + 'proof let take assume then thus hence ex for st holds consider ' + 'reconsider such that and in provided of as from be being by means ' + 'equals implies iff redefine define now not or attr is mode ' + 'suppose per cases set thesis contradiction scheme reserve struct ' + 'correctness compatibility coherence symmetry assymetry ' + 'reflexivity irreflexivity connectedness uniqueness commutativity ' + 'idempotence involutiveness projectivity',\n    contains: [hljs.COMMENT('::', '$')]\n  };\n}\nmodule.exports = mizar;", "map": {"version": 3, "names": ["mizar", "hljs", "name", "keywords", "contains", "COMMENT", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/mizar.js"], "sourcesContent": ["/*\nLanguage: Mizar\nDescription: The Mizar Language is a formal language derived from the mathematical vernacular.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://mizar.org/language/\nCategory: scientific\n*/\n\nfunction mizar(hljs) {\n  return {\n    name: '<PERSON>zar',\n    keywords:\n      'environ vocabularies notations constructors definitions ' +\n      'registrations theorems schemes requirements begin end definition ' +\n      'registration cluster existence pred func defpred deffunc theorem ' +\n      'proof let take assume then thus hence ex for st holds consider ' +\n      'reconsider such that and in provided of as from be being by means ' +\n      'equals implies iff redefine define now not or attr is mode ' +\n      'suppose per cases set thesis contradiction scheme reserve struct ' +\n      'correctness compatibility coherence symmetry assymetry ' +\n      'reflexivity irreflexivity connectedness uniqueness commutativity ' +\n      'idempotence involutiveness projectivity',\n    contains: [\n      hljs.COMMENT('::', '$')\n    ]\n  };\n}\n\nmodule.exports = mizar;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,QAAQ,EACN,0DAA0D,GAC1D,mEAAmE,GACnE,mEAAmE,GACnE,iEAAiE,GACjE,oEAAoE,GACpE,6DAA6D,GAC7D,mEAAmE,GACnE,yDAAyD,GACzD,mEAAmE,GACnE,yCAAyC;IAC3CC,QAAQ,EAAE,CACRH,IAAI,CAACI,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EAE3B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}