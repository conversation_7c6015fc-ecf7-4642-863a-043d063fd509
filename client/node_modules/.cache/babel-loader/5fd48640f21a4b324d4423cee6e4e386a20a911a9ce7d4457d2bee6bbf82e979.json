{"ast": null, "code": "'use strict';\n\nmodule.exports = cfscript;\ncfscript.displayName = 'cfscript';\ncfscript.aliases = [];\nfunction cfscript(Prism) {\n  // https://cfdocs.org/script\n  Prism.languages.cfscript = Prism.languages.extend('clike', {\n    comment: [{\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      lookbehind: true,\n      inside: {\n        annotation: {\n          pattern: /(?:^|[^.])@[\\w\\.]+/,\n          alias: 'punctuation'\n        }\n      }\n    }, {\n      pattern: /(^|[^\\\\:])\\/\\/.*/,\n      lookbehind: true,\n      greedy: true\n    }],\n    keyword: /\\b(?:abstract|break|catch|component|continue|default|do|else|extends|final|finally|for|function|if|in|include|package|private|property|public|remote|required|rethrow|return|static|switch|throw|try|var|while|xml)\\b(?!\\s*=)/,\n    operator: [/\\+\\+|--|&&|\\|\\||::|=>|[!=]==|<=?|>=?|[-+*/%&|^!=<>]=?|\\?(?:\\.|:)?|[?:]/, /\\b(?:and|contains|eq|equal|eqv|gt|gte|imp|is|lt|lte|mod|not|or|xor)\\b/],\n    scope: {\n      pattern: /\\b(?:application|arguments|cgi|client|cookie|local|session|super|this|variables)\\b/,\n      alias: 'global'\n    },\n    type: {\n      pattern: /\\b(?:any|array|binary|boolean|date|guid|numeric|query|string|struct|uuid|void|xml)\\b/,\n      alias: 'builtin'\n    }\n  });\n  Prism.languages.insertBefore('cfscript', 'keyword', {\n    // This must be declared before keyword because we use \"function\" inside the lookahead\n    'function-variable': {\n      pattern: /[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    }\n  });\n  delete Prism.languages.cfscript['class-name'];\n  Prism.languages.cfc = Prism.languages['cfscript'];\n}", "map": {"version": 3, "names": ["module", "exports", "cfscript", "displayName", "aliases", "Prism", "languages", "extend", "comment", "pattern", "lookbehind", "inside", "annotation", "alias", "greedy", "keyword", "operator", "scope", "type", "insertBefore", "cfc"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/cfscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = cfscript\ncfscript.displayName = 'cfscript'\ncfscript.aliases = []\nfunction cfscript(Prism) {\n  // https://cfdocs.org/script\n  Prism.languages.cfscript = Prism.languages.extend('clike', {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        inside: {\n          annotation: {\n            pattern: /(?:^|[^.])@[\\w\\.]+/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|break|catch|component|continue|default|do|else|extends|final|finally|for|function|if|in|include|package|private|property|public|remote|required|rethrow|return|static|switch|throw|try|var|while|xml)\\b(?!\\s*=)/,\n    operator: [\n      /\\+\\+|--|&&|\\|\\||::|=>|[!=]==|<=?|>=?|[-+*/%&|^!=<>]=?|\\?(?:\\.|:)?|[?:]/,\n      /\\b(?:and|contains|eq|equal|eqv|gt|gte|imp|is|lt|lte|mod|not|or|xor)\\b/\n    ],\n    scope: {\n      pattern:\n        /\\b(?:application|arguments|cgi|client|cookie|local|session|super|this|variables)\\b/,\n      alias: 'global'\n    },\n    type: {\n      pattern:\n        /\\b(?:any|array|binary|boolean|date|guid|numeric|query|string|struct|uuid|void|xml)\\b/,\n      alias: 'builtin'\n    }\n  })\n  Prism.languages.insertBefore('cfscript', 'keyword', {\n    // This must be declared before keyword because we use \"function\" inside the lookahead\n    'function-variable': {\n      pattern:\n        /[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    }\n  })\n  delete Prism.languages.cfscript['class-name']\n  Prism.languages.cfc = Prism.languages['cfscript']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EACAA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACzDC,OAAO,EAAE,CACP;MACEC,OAAO,EAAE,iCAAiC;MAC1CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,UAAU,EAAE;UACVH,OAAO,EAAE,oBAAoB;UAC7BI,KAAK,EAAE;QACT;MACF;IACF,CAAC,EACD;MACEJ,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE,IAAI;MAChBI,MAAM,EAAE;IACV,CAAC,CACF;IACDC,OAAO,EACL,+NAA+N;IACjOC,QAAQ,EAAE,CACR,wEAAwE,EACxE,uEAAuE,CACxE;IACDC,KAAK,EAAE;MACLR,OAAO,EACL,oFAAoF;MACtFI,KAAK,EAAE;IACT,CAAC;IACDK,IAAI,EAAE;MACJT,OAAO,EACL,sFAAsF;MACxFI,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFR,KAAK,CAACC,SAAS,CAACa,YAAY,CAAC,UAAU,EAAE,SAAS,EAAE;IAClD;IACA,mBAAmB,EAAE;MACnBV,OAAO,EACL,0KAA0K;MAC5KI,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAOR,KAAK,CAACC,SAAS,CAACJ,QAAQ,CAAC,YAAY,CAAC;EAC7CG,KAAK,CAACC,SAAS,CAACc,GAAG,GAAGf,KAAK,CAACC,SAAS,CAAC,UAAU,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}