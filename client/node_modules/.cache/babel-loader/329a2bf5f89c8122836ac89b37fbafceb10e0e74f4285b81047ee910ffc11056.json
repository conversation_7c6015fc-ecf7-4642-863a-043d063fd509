{"ast": null, "code": "'use strict';\n\nmodule.exports = ini;\nini.displayName = 'ini';\nini.aliases = [];\nfunction ini(Prism) {\n  Prism.languages.ini = {\n    /**\n     * The component mimics the behavior of the Win32 API parser.\n     *\n     * @see {@link https://github.com/PrismJS/prism/issues/2775#issuecomment-787477723}\n     */\n    comment: {\n      pattern: /(^[ \\f\\t\\v]*)[#;][^\\n\\r]*/m,\n      lookbehind: true\n    },\n    section: {\n      pattern: /(^[ \\f\\t\\v]*)\\[[^\\n\\r\\]]*\\]?/m,\n      lookbehind: true,\n      inside: {\n        'section-name': {\n          pattern: /(^\\[[ \\f\\t\\v]*)[^ \\f\\t\\v\\]]+(?:[ \\f\\t\\v]+[^ \\f\\t\\v\\]]+)*/,\n          lookbehind: true,\n          alias: 'selector'\n        },\n        punctuation: /\\[|\\]/\n      }\n    },\n    key: {\n      pattern: /(^[ \\f\\t\\v]*)[^ \\f\\n\\r\\t\\v=]+(?:[ \\f\\t\\v]+[^ \\f\\n\\r\\t\\v=]+)*(?=[ \\f\\t\\v]*=)/m,\n      lookbehind: true,\n      alias: 'attr-name'\n    },\n    value: {\n      pattern: /(=[ \\f\\t\\v]*)[^ \\f\\n\\r\\t\\v]+(?:[ \\f\\t\\v]+[^ \\f\\n\\r\\t\\v]+)*/,\n      lookbehind: true,\n      alias: 'attr-value',\n      inside: {\n        'inner-value': {\n          pattern: /^(\"|').+(?=\\1$)/,\n          lookbehind: true\n        }\n      }\n    },\n    punctuation: /=/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "ini", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "section", "inside", "alias", "punctuation", "key", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/ini.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ini\nini.displayName = 'ini'\nini.aliases = []\nfunction ini(Prism) {\n  Prism.languages.ini = {\n    /**\n     * The component mimics the behavior of the Win32 API parser.\n     *\n     * @see {@link https://github.com/PrismJS/prism/issues/2775#issuecomment-787477723}\n     */\n    comment: {\n      pattern: /(^[ \\f\\t\\v]*)[#;][^\\n\\r]*/m,\n      lookbehind: true\n    },\n    section: {\n      pattern: /(^[ \\f\\t\\v]*)\\[[^\\n\\r\\]]*\\]?/m,\n      lookbehind: true,\n      inside: {\n        'section-name': {\n          pattern: /(^\\[[ \\f\\t\\v]*)[^ \\f\\t\\v\\]]+(?:[ \\f\\t\\v]+[^ \\f\\t\\v\\]]+)*/,\n          lookbehind: true,\n          alias: 'selector'\n        },\n        punctuation: /\\[|\\]/\n      }\n    },\n    key: {\n      pattern:\n        /(^[ \\f\\t\\v]*)[^ \\f\\n\\r\\t\\v=]+(?:[ \\f\\t\\v]+[^ \\f\\n\\r\\t\\v=]+)*(?=[ \\f\\t\\v]*=)/m,\n      lookbehind: true,\n      alias: 'attr-name'\n    },\n    value: {\n      pattern: /(=[ \\f\\t\\v]*)[^ \\f\\n\\r\\t\\v]+(?:[ \\f\\t\\v]+[^ \\f\\n\\r\\t\\v]+)*/,\n      lookbehind: true,\n      alias: 'attr-value',\n      inside: {\n        'inner-value': {\n          pattern: /^(\"|').+(?=\\1$)/,\n          lookbehind: true\n        }\n      }\n    },\n    punctuation: /=/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpB;AACJ;AACA;AACA;AACA;IACIK,OAAO,EAAE;MACPC,OAAO,EAAE,4BAA4B;MACrCC,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EAAE;MACPF,OAAO,EAAE,+BAA+B;MACxCC,UAAU,EAAE,IAAI;MAChBE,MAAM,EAAE;QACN,cAAc,EAAE;UACdH,OAAO,EAAE,0DAA0D;UACnEC,UAAU,EAAE,IAAI;UAChBG,KAAK,EAAE;QACT,CAAC;QACDC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,GAAG,EAAE;MACHN,OAAO,EACL,8EAA8E;MAChFC,UAAU,EAAE,IAAI;MAChBG,KAAK,EAAE;IACT,CAAC;IACDG,KAAK,EAAE;MACLP,OAAO,EAAE,4DAA4D;MACrEC,UAAU,EAAE,IAAI;MAChBG,KAAK,EAAE,YAAY;MACnBD,MAAM,EAAE;QACN,aAAa,EAAE;UACbH,OAAO,EAAE,iBAAiB;UAC1BC,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDI,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}