{"ast": null, "code": "'use strict';\n\nmodule.exports = livescript;\nlivescript.displayName = 'livescript';\nlivescript.aliases = [];\nfunction livescript(Prism) {\n  Prism.languages.livescript = {\n    comment: [{\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n      lookbehind: true\n    }, {\n      pattern: /(^|[^\\\\])#.*/,\n      lookbehind: true\n    }],\n    'interpolated-string': {\n      /* Look-behind and look-ahead prevents wrong behavior of the greedy pattern\n       * forcing it to match \"\"\"-quoted string when it would otherwise match \"-quoted first. */\n      pattern: /(^|[^\"])(\"\"\"|\")(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2(?!\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /(^|[^\\\\])#[a-z_](?:-?[a-z]|[\\d_])*/m,\n          lookbehind: true\n        },\n        interpolation: {\n          pattern: /(^|[^\\\\])#\\{[^}]+\\}/m,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^#\\{|\\}$/,\n              alias: 'variable'\n            } // See rest below\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    string: [{\n      pattern: /('''|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n      greedy: true\n    }, {\n      pattern: /<\\[[\\s\\S]*?\\]>/,\n      greedy: true\n    }, /\\\\[^\\s,;\\])}]+/],\n    regex: [{\n      pattern: /\\/\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|(?!\\/\\/)[^\\\\\\[])+\\/\\/[gimyu]{0,5}/,\n      greedy: true,\n      inside: {\n        comment: {\n          pattern: /(^|[^\\\\])#.*/,\n          lookbehind: true\n        }\n      }\n    }, {\n      pattern: /\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/[gimyu]{0,5}/,\n      greedy: true\n    }],\n    keyword: {\n      pattern: /(^|(?!-).)\\b(?:break|case|catch|class|const|continue|default|do|else|extends|fallthrough|finally|for(?: ever)?|function|if|implements|it|let|loop|new|null|otherwise|own|return|super|switch|that|then|this|throw|try|unless|until|var|void|when|while|yield)(?!-)\\b/m,\n      lookbehind: true\n    },\n    'keyword-operator': {\n      pattern: /(^|[^-])\\b(?:(?:delete|require|typeof)!|(?:and|by|delete|export|from|import(?: all)?|in|instanceof|is(?: not|nt)?|not|of|or|til|to|typeof|with|xor)(?!-)\\b)/m,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    boolean: {\n      pattern: /(^|[^-])\\b(?:false|no|off|on|true|yes)(?!-)\\b/m,\n      lookbehind: true\n    },\n    argument: {\n      // Don't match .&. nor &&\n      pattern: /(^|(?!\\.&\\.)[^&])&(?!&)\\d*/m,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    number: /\\b(?:\\d+~[\\da-z]+|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[a-z]\\w*)?)/i,\n    identifier: /[a-z_](?:-?[a-z]|[\\d_])*/i,\n    operator: [\n    // Spaced .\n    {\n      pattern: /( )\\.(?= )/,\n      lookbehind: true\n    },\n    // Full list, in order:\n    // .= .~ .. ...\n    // .&. .^. .<<. .>>. .>>>.\n    // := :: ::=\n    // &&\n    // || |>\n    // < << <<< <<<<\n    // <- <-- <-! <--!\n    // <~ <~~ <~! <~~!\n    // <| <= <?\n    // > >> >= >?\n    // - -- -> -->\n    // + ++\n    // @ @@\n    // % %%\n    // * **\n    // ! != !~=\n    // !~> !~~>\n    // !-> !-->\n    // ~ ~> ~~> ~=\n    // = ==\n    // ^ ^^\n    // / ?\n    /\\.(?:[=~]|\\.\\.?)|\\.(?:[&|^]|<<|>>>?)\\.|:(?:=|:=?)|&&|\\|[|>]|<(?:<<?<?|--?!?|~~?!?|[|=?])?|>[>=?]?|-(?:->?|>)?|\\+\\+?|@@?|%%?|\\*\\*?|!(?:~?=|--?>|~?~>)?|~(?:~?>|=)?|==?|\\^\\^?|[\\/?]/],\n    punctuation: /[(){}\\[\\]|.,:;`]/\n  };\n  Prism.languages.livescript['interpolated-string'].inside['interpolation'].inside.rest = Prism.languages.livescript;\n}", "map": {"version": 3, "names": ["module", "exports", "livescript", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "inside", "variable", "interpolation", "alias", "string", "regex", "keyword", "boolean", "argument", "number", "identifier", "operator", "punctuation", "rest"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/livescript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = livescript\nlivescript.displayName = 'livescript'\nlivescript.aliases = []\nfunction livescript(Prism) {\n  Prism.languages.livescript = {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\\\])#.*/,\n        lookbehind: true\n      }\n    ],\n    'interpolated-string': {\n      /* Look-behind and look-ahead prevents wrong behavior of the greedy pattern\n       * forcing it to match \"\"\"-quoted string when it would otherwise match \"-quoted first. */\n      pattern: /(^|[^\"])(\"\"\"|\")(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2(?!\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /(^|[^\\\\])#[a-z_](?:-?[a-z]|[\\d_])*/m,\n          lookbehind: true\n        },\n        interpolation: {\n          pattern: /(^|[^\\\\])#\\{[^}]+\\}/m,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^#\\{|\\}$/,\n              alias: 'variable'\n            } // See rest below\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    string: [\n      {\n        pattern: /('''|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n        greedy: true\n      },\n      {\n        pattern: /<\\[[\\s\\S]*?\\]>/,\n        greedy: true\n      },\n      /\\\\[^\\s,;\\])}]+/\n    ],\n    regex: [\n      {\n        pattern: /\\/\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|(?!\\/\\/)[^\\\\\\[])+\\/\\/[gimyu]{0,5}/,\n        greedy: true,\n        inside: {\n          comment: {\n            pattern: /(^|[^\\\\])#.*/,\n            lookbehind: true\n          }\n        }\n      },\n      {\n        pattern: /\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/[gimyu]{0,5}/,\n        greedy: true\n      }\n    ],\n    keyword: {\n      pattern:\n        /(^|(?!-).)\\b(?:break|case|catch|class|const|continue|default|do|else|extends|fallthrough|finally|for(?: ever)?|function|if|implements|it|let|loop|new|null|otherwise|own|return|super|switch|that|then|this|throw|try|unless|until|var|void|when|while|yield)(?!-)\\b/m,\n      lookbehind: true\n    },\n    'keyword-operator': {\n      pattern:\n        /(^|[^-])\\b(?:(?:delete|require|typeof)!|(?:and|by|delete|export|from|import(?: all)?|in|instanceof|is(?: not|nt)?|not|of|or|til|to|typeof|with|xor)(?!-)\\b)/m,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    boolean: {\n      pattern: /(^|[^-])\\b(?:false|no|off|on|true|yes)(?!-)\\b/m,\n      lookbehind: true\n    },\n    argument: {\n      // Don't match .&. nor &&\n      pattern: /(^|(?!\\.&\\.)[^&])&(?!&)\\d*/m,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    number: /\\b(?:\\d+~[\\da-z]+|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[a-z]\\w*)?)/i,\n    identifier: /[a-z_](?:-?[a-z]|[\\d_])*/i,\n    operator: [\n      // Spaced .\n      {\n        pattern: /( )\\.(?= )/,\n        lookbehind: true\n      }, // Full list, in order:\n      // .= .~ .. ...\n      // .&. .^. .<<. .>>. .>>>.\n      // := :: ::=\n      // &&\n      // || |>\n      // < << <<< <<<<\n      // <- <-- <-! <--!\n      // <~ <~~ <~! <~~!\n      // <| <= <?\n      // > >> >= >?\n      // - -- -> -->\n      // + ++\n      // @ @@\n      // % %%\n      // * **\n      // ! != !~=\n      // !~> !~~>\n      // !-> !-->\n      // ~ ~> ~~> ~=\n      // = ==\n      // ^ ^^\n      // / ?\n      /\\.(?:[=~]|\\.\\.?)|\\.(?:[&|^]|<<|>>>?)\\.|:(?:=|:=?)|&&|\\|[|>]|<(?:<<?<?|--?!?|~~?!?|[|=?])?|>[>=?]?|-(?:->?|>)?|\\+\\+?|@@?|%%?|\\*\\*?|!(?:~?=|--?>|~?~>)?|~(?:~?>|=)?|==?|\\^\\^?|[\\/?]/\n    ],\n    punctuation: /[(){}\\[\\]|.,:;`]/\n  }\n  Prism.languages.livescript['interpolated-string'].inside[\n    'interpolation'\n  ].inside.rest = Prism.languages.livescript\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,EAAE;AACvB,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzBA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAG;IAC3BK,OAAO,EAAE,CACP;MACEC,OAAO,EAAE,2BAA2B;MACpCC,UAAU,EAAE;IACd,CAAC,EACD;MACED,OAAO,EAAE,cAAc;MACvBC,UAAU,EAAE;IACd,CAAC,CACF;IACD,qBAAqB,EAAE;MACrB;AACN;MACMD,OAAO,EAAE,iDAAiD;MAC1DC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNC,QAAQ,EAAE;UACRJ,OAAO,EAAE,qCAAqC;UAC9CC,UAAU,EAAE;QACd,CAAC;QACDI,aAAa,EAAE;UACbL,OAAO,EAAE,sBAAsB;UAC/BC,UAAU,EAAE,IAAI;UAChBE,MAAM,EAAE;YACN,2BAA2B,EAAE;cAC3BH,OAAO,EAAE,UAAU;cACnBM,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;QACF,CAAC;QACDC,MAAM,EAAE;MACV;IACF,CAAC;IACDA,MAAM,EAAE,CACN;MACEP,OAAO,EAAE,oCAAoC;MAC7CE,MAAM,EAAE;IACV,CAAC,EACD;MACEF,OAAO,EAAE,gBAAgB;MACzBE,MAAM,EAAE;IACV,CAAC,EACD,gBAAgB,CACjB;IACDM,KAAK,EAAE,CACL;MACER,OAAO,EAAE,6DAA6D;MACtEE,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNJ,OAAO,EAAE;UACPC,OAAO,EAAE,cAAc;UACvBC,UAAU,EAAE;QACd;MACF;IACF,CAAC,EACD;MACED,OAAO,EAAE,sDAAsD;MAC/DE,MAAM,EAAE;IACV,CAAC,CACF;IACDO,OAAO,EAAE;MACPT,OAAO,EACL,uQAAuQ;MACzQC,UAAU,EAAE;IACd,CAAC;IACD,kBAAkB,EAAE;MAClBD,OAAO,EACL,8JAA8J;MAChKC,UAAU,EAAE,IAAI;MAChBK,KAAK,EAAE;IACT,CAAC;IACDI,OAAO,EAAE;MACPV,OAAO,EAAE,gDAAgD;MACzDC,UAAU,EAAE;IACd,CAAC;IACDU,QAAQ,EAAE;MACR;MACAX,OAAO,EAAE,6BAA6B;MACtCC,UAAU,EAAE,IAAI;MAChBK,KAAK,EAAE;IACT,CAAC;IACDM,MAAM,EAAE,0DAA0D;IAClEC,UAAU,EAAE,2BAA2B;IACvCC,QAAQ,EAAE;IACR;IACA;MACEd,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC;IAAE;IACH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,mLAAmL,CACpL;IACDc,WAAW,EAAE;EACf,CAAC;EACDlB,KAAK,CAACC,SAAS,CAACJ,UAAU,CAAC,qBAAqB,CAAC,CAACS,MAAM,CACtD,eAAe,CAChB,CAACA,MAAM,CAACa,IAAI,GAAGnB,KAAK,CAACC,SAAS,CAACJ,UAAU;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}