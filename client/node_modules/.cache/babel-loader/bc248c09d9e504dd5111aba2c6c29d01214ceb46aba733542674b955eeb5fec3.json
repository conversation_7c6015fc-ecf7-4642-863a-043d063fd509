{"ast": null, "code": "'use strict';\n\nmodule.exports = arff;\narff.displayName = 'arff';\narff.aliases = [];\nfunction arff(Prism) {\n  Prism.languages.arff = {\n    comment: /%.*/,\n    string: {\n      pattern: /([\"'])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword: /@(?:attribute|data|end|relation)\\b/i,\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    punctuation: /[{},]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "arff", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "keyword", "number", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/arff.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = arff\narff.displayName = 'arff'\narff.aliases = []\nfunction arff(Prism) {\n  Prism.languages.arff = {\n    comment: /%.*/,\n    string: {\n      pattern: /([\"'])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword: /@(?:attribute|data|end|relation)\\b/i,\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    punctuation: /[{},]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrBK,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;MACNC,OAAO,EAAE,kCAAkC;MAC3CC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,qCAAqC;IAC9CC,MAAM,EAAE,mBAAmB;IAC3BC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}