{"ast": null, "code": "'use strict';\n\nmodule.exports = sas;\nsas.displayName = 'sas';\nsas.aliases = [];\nfunction sas(Prism) {\n  ;\n  (function (Prism) {\n    var stringPattern = /(?:\"(?:\"\"|[^\"])*\"(?!\")|'(?:''|[^'])*'(?!'))/.source;\n    var number = /\\b(?:\\d[\\da-f]*x|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i;\n    var numericConstant = {\n      pattern: RegExp(stringPattern + '[bx]'),\n      alias: 'number'\n    };\n    var macroVariable = {\n      pattern: /&[a-z_]\\w*/i\n    };\n    var macroKeyword = {\n      pattern: /((?:^|\\s|=|\\())%(?:ABORT|BY|CMS|COPY|DISPLAY|DO|ELSE|END|EVAL|GLOBAL|GO|GOTO|IF|INC|INCLUDE|INDEX|INPUT|KTRIM|LENGTH|LET|LIST|LOCAL|PUT|QKTRIM|QSCAN|QSUBSTR|QSYSFUNC|QUPCASE|RETURN|RUN|SCAN|SUBSTR|SUPERQ|SYMDEL|SYMEXIST|SYMGLOBL|SYMLOCAL|SYSCALL|SYSEVALF|SYSEXEC|SYSFUNC|SYSGET|SYSRPUT|THEN|TO|TSO|UNQUOTE|UNTIL|UPCASE|WHILE|WINDOW)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    };\n    var step = {\n      pattern: /(^|\\s)(?:proc\\s+\\w+|data(?!=)|quit|run)\\b/i,\n      alias: 'keyword',\n      lookbehind: true\n    };\n    var comment = [/\\/\\*[\\s\\S]*?\\*\\//, {\n      pattern: /(^[ \\t]*|;\\s*)\\*[^;]*;/m,\n      lookbehind: true\n    }];\n    var string = {\n      pattern: RegExp(stringPattern),\n      greedy: true\n    };\n    var punctuation = /[$%@.(){}\\[\\];,\\\\]/;\n    var func = {\n      pattern: /%?\\b\\w+(?=\\()/,\n      alias: 'keyword'\n    };\n    var args = {\n      function: func,\n      'arg-value': {\n        pattern: /(=\\s*)[A-Z\\.]+/i,\n        lookbehind: true\n      },\n      operator: /=/,\n      'macro-variable': macroVariable,\n      arg: {\n        pattern: /[A-Z]+/i,\n        alias: 'keyword'\n      },\n      number: number,\n      'numeric-constant': numericConstant,\n      punctuation: punctuation,\n      string: string\n    };\n    var format = {\n      pattern: /\\b(?:format|put)\\b=?[\\w'$.]+/i,\n      inside: {\n        keyword: /^(?:format|put)(?==)/i,\n        equals: /=/,\n        format: {\n          pattern: /(?:\\w|\\$\\d)+\\.\\d?/,\n          alias: 'number'\n        }\n      }\n    };\n    var altformat = {\n      pattern: /\\b(?:format|put)\\s+[\\w']+(?:\\s+[$.\\w]+)+(?=;)/i,\n      inside: {\n        keyword: /^(?:format|put)/i,\n        format: {\n          pattern: /[\\w$]+\\.\\d?/,\n          alias: 'number'\n        }\n      }\n    };\n    var globalStatements = {\n      pattern: /((?:^|\\s)=?)(?:catname|checkpoint execute_always|dm|endsas|filename|footnote|%include|libname|%list|lock|missing|options|page|resetline|%run|sasfile|skip|sysecho|title\\d?)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    };\n    var submitStatement = {\n      pattern: /(^|\\s)(?:submit(?:\\s+(?:load|norun|parseonly))?|endsubmit)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    };\n    var actionSets = /aStore|accessControl|aggregation|audio|autotune|bayesianNetClassifier|bioMedImage|boolRule|builtins|cardinality|cdm|clustering|conditionalRandomFields|configuration|copula|countreg|dataDiscovery|dataPreprocess|dataSciencePilot|dataStep|decisionTree|deduplication|deepLearn|deepNeural|deepRnn|ds2|ecm|entityRes|espCluster|explainModel|factmac|fastKnn|fcmpact|fedSql|freqTab|gVarCluster|gam|gleam|graphSemiSupLearn|hiddenMarkovModel|hyperGroup|ica|image|iml|kernalPca|langModel|ldaTopic|loadStreams|mbc|mixed|mlTools|modelPublishing|network|neuralNet|nmf|nonParametricBayes|nonlinear|optNetwork|optimization|panel|pca|percentile|phreg|pls|qkb|qlim|quantreg|recommend|regression|reinforcementLearn|robustPca|ruleMining|sampling|sandwich|sccasl|search(?:Analytics)?|sentimentAnalysis|sequence|session(?:Prop)?|severity|simSystem|simple|smartData|sparkEmbeddedProcess|sparseML|spatialreg|spc|stabilityMonitoring|svDataDescription|svm|table|text(?:Filters|Frequency|Mining|Parse|Rule(?:Develop|Score)|Topic|Util)|timeData|transpose|tsInfo|tsReconcile|uniTimeSeries|varReduce/.source;\n    var casActions = {\n      pattern: RegExp(/(^|\\s)(?:action\\s+)?(?:<act>)\\.[a-z]+\\b[^;]+/.source.replace(/<act>/g, function () {\n        return actionSets;\n      }), 'i'),\n      lookbehind: true,\n      inside: {\n        keyword: RegExp(/(?:<act>)\\.[a-z]+\\b/.source.replace(/<act>/g, function () {\n          return actionSets;\n        }), 'i'),\n        action: {\n          pattern: /(?:action)/i,\n          alias: 'keyword'\n        },\n        comment: comment,\n        function: func,\n        'arg-value': args['arg-value'],\n        operator: args.operator,\n        argument: args.arg,\n        number: number,\n        'numeric-constant': numericConstant,\n        punctuation: punctuation,\n        string: string\n      }\n    };\n    var keywords = {\n      pattern: /((?:^|\\s)=?)(?:after|analysis|and|array|barchart|barwidth|begingraph|by|call|cas|cbarline|cfill|class(?:lev)?|close|column|computed?|contains|continue|data(?==)|define|delete|describe|document|do\\s+over|do|dol|drop|dul|else|end(?:comp|source)?|entryTitle|eval(?:uate)?|exec(?:ute)?|exit|file(?:name)?|fill(?:attrs)?|flist|fnc|function(?:list)?|global|goto|group(?:by)?|headline|headskip|histogram|if|infile|keep|keylabel|keyword|label|layout|leave|legendlabel|length|libname|loadactionset|merge|midpoints|_?null_|name|noobs|nowd|ods|options|or|otherwise|out(?:put)?|over(?:lay)?|plot|print|put|raise|ranexp|rannor|rbreak|retain|return|select|session|sessref|set|source|statgraph|sum|summarize|table|temp|terminate|then\\s+do|then|title\\d?|to|var|when|where|xaxisopts|y2axisopts|yaxisopts)\\b/i,\n      lookbehind: true\n    };\n    Prism.languages.sas = {\n      datalines: {\n        pattern: /^([ \\t]*)(?:cards|(?:data)?lines);[\\s\\S]+?^[ \\t]*;/im,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          keyword: {\n            pattern: /^(?:cards|(?:data)?lines)/i\n          },\n          punctuation: /;/\n        }\n      },\n      'proc-sql': {\n        pattern: /(^proc\\s+(?:fed)?sql(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          sql: {\n            pattern: RegExp(/^[ \\t]*(?:select|alter\\s+table|(?:create|describe|drop)\\s+(?:index|table(?:\\s+constraints)?|view)|create\\s+unique\\s+index|insert\\s+into|update)(?:<str>|[^;\"'])+;/.source.replace(/<str>/g, function () {\n              return stringPattern;\n            }), 'im'),\n            alias: 'language-sql',\n            inside: Prism.languages.sql\n          },\n          'global-statements': globalStatements,\n          'sql-statements': {\n            pattern: /(^|\\s)(?:disconnect\\s+from|begin|commit|exec(?:ute)?|reset|rollback|validate)\\b/i,\n            lookbehind: true,\n            alias: 'keyword'\n          },\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-groovy': {\n        pattern: /(^proc\\s+groovy(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          groovy: {\n            pattern: RegExp(/(^[ \\t]*submit(?:\\s+(?:load|norun|parseonly))?)(?:<str>|[^\"'])+?(?=endsubmit;)/.source.replace(/<str>/g, function () {\n              return stringPattern;\n            }), 'im'),\n            lookbehind: true,\n            alias: 'language-groovy',\n            inside: Prism.languages.groovy\n          },\n          keyword: keywords,\n          'submit-statement': submitStatement,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-lua': {\n        pattern: /(^proc\\s+lua(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          lua: {\n            pattern: RegExp(/(^[ \\t]*submit(?:\\s+(?:load|norun|parseonly))?)(?:<str>|[^\"'])+?(?=endsubmit;)/.source.replace(/<str>/g, function () {\n              return stringPattern;\n            }), 'im'),\n            lookbehind: true,\n            alias: 'language-lua',\n            inside: Prism.languages.lua\n          },\n          keyword: keywords,\n          'submit-statement': submitStatement,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-cas': {\n        pattern: /(^proc\\s+cas(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|quit|data);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          'statement-var': {\n            pattern: /((?:^|\\s)=?)saveresult\\s[^;]+/im,\n            lookbehind: true,\n            inside: {\n              statement: {\n                pattern: /^saveresult\\s+\\S+/i,\n                inside: {\n                  keyword: /^(?:saveresult)/i\n                }\n              },\n              rest: args\n            }\n          },\n          'cas-actions': casActions,\n          statement: {\n            pattern: /((?:^|\\s)=?)(?:default|(?:un)?set|on|output|upload)[^;]+/im,\n            lookbehind: true,\n            inside: args\n          },\n          step: step,\n          keyword: keywords,\n          function: func,\n          format: format,\n          altformat: altformat,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-args': {\n        pattern: RegExp(/(^proc\\s+\\w+\\s+)(?!\\s)(?:[^;\"']|<str>)+;/.source.replace(/<str>/g, function () {\n          return stringPattern;\n        }), 'im'),\n        lookbehind: true,\n        inside: args\n      },\n      /*Special keywords within macros*/\n      'macro-keyword': macroKeyword,\n      'macro-variable': macroVariable,\n      'macro-string-functions': {\n        pattern: /((?:^|\\s|=))%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)\\(.*?(?:[^%]\\))/i,\n        lookbehind: true,\n        inside: {\n          function: {\n            pattern: /%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)/i,\n            alias: 'keyword'\n          },\n          'macro-keyword': macroKeyword,\n          'macro-variable': macroVariable,\n          'escaped-char': {\n            pattern: /%['\"()<>=¬^~;,#]/\n          },\n          punctuation: punctuation\n        }\n      },\n      'macro-declaration': {\n        pattern: /^%macro[^;]+(?=;)/im,\n        inside: {\n          keyword: /%macro/i\n        }\n      },\n      'macro-end': {\n        pattern: /^%mend[^;]+(?=;)/im,\n        inside: {\n          keyword: /%mend/i\n        }\n      },\n      /*%_zscore(headcir, _lhc, _mhc, _shc, headcz, headcpct, _Fheadcz); */\n      macro: {\n        pattern: /%_\\w+(?=\\()/,\n        alias: 'keyword'\n      },\n      input: {\n        pattern: /\\binput\\s[-\\w\\s/*.$&]+;/i,\n        inside: {\n          input: {\n            alias: 'keyword',\n            pattern: /^input/i\n          },\n          comment: comment,\n          number: number,\n          'numeric-constant': numericConstant\n        }\n      },\n      'options-args': {\n        pattern: /(^options)[-'\"|/\\\\<>*+=:()\\w\\s]*(?=;)/im,\n        lookbehind: true,\n        inside: args\n      },\n      'cas-actions': casActions,\n      comment: comment,\n      function: func,\n      format: format,\n      altformat: altformat,\n      'numeric-constant': numericConstant,\n      datetime: {\n        // '1jan2013'd, '9:25:19pm't, '18jan2003:9:27:05am'dt\n        pattern: RegExp(stringPattern + '(?:dt?|t)'),\n        alias: 'number'\n      },\n      string: string,\n      step: step,\n      keyword: keywords,\n      // In SAS Studio syntax highlighting, these operators are styled like keywords\n      'operator-keyword': {\n        pattern: /\\b(?:eq|ge|gt|in|le|lt|ne|not)\\b/i,\n        alias: 'operator'\n      },\n      // Decimal (1.2e23), hexadecimal (0c1x)\n      number: number,\n      operator: /\\*\\*?|\\|\\|?|!!?|¦¦?|<[>=]?|>[<=]?|[-+\\/=&]|[~¬^]=?/,\n      punctuation: punctuation\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "sas", "displayName", "aliases", "Prism", "stringPattern", "source", "number", "numericConstant", "pattern", "RegExp", "alias", "macroVariable", "macroKeyword", "lookbehind", "step", "comment", "string", "greedy", "punctuation", "func", "args", "function", "operator", "arg", "format", "inside", "keyword", "equals", "altformat", "globalStatements", "submitStatement", "actionSets", "casActions", "replace", "action", "argument", "keywords", "languages", "datalines", "sql", "groovy", "lua", "statement", "rest", "macro", "input", "datetime"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/sas.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = sas\nsas.displayName = 'sas'\nsas.aliases = []\nfunction sas(Prism) {\n  ;(function (Prism) {\n    var stringPattern = /(?:\"(?:\"\"|[^\"])*\"(?!\")|'(?:''|[^'])*'(?!'))/.source\n    var number = /\\b(?:\\d[\\da-f]*x|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i\n    var numericConstant = {\n      pattern: RegExp(stringPattern + '[bx]'),\n      alias: 'number'\n    }\n    var macroVariable = {\n      pattern: /&[a-z_]\\w*/i\n    }\n    var macroKeyword = {\n      pattern:\n        /((?:^|\\s|=|\\())%(?:ABORT|BY|CMS|COPY|DISPLAY|DO|ELSE|END|EVAL|GLOBAL|GO|GOTO|IF|INC|INCLUDE|INDEX|INPUT|KTRIM|LENGTH|LET|LIST|LOCAL|PUT|QKTRIM|QSCAN|QSUBSTR|QSYSFUNC|QUPCASE|RETURN|RUN|SCAN|SUBSTR|SUPERQ|SYMDEL|SYMEXIST|SYMGLOBL|SYMLOCAL|SYSCALL|SYSEVALF|SYSEXEC|SYSFUNC|SYSGET|SYSRPUT|THEN|TO|TSO|UNQUOTE|UNTIL|UPCASE|WHILE|WINDOW)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    }\n    var step = {\n      pattern: /(^|\\s)(?:proc\\s+\\w+|data(?!=)|quit|run)\\b/i,\n      alias: 'keyword',\n      lookbehind: true\n    }\n    var comment = [\n      /\\/\\*[\\s\\S]*?\\*\\//,\n      {\n        pattern: /(^[ \\t]*|;\\s*)\\*[^;]*;/m,\n        lookbehind: true\n      }\n    ]\n    var string = {\n      pattern: RegExp(stringPattern),\n      greedy: true\n    }\n    var punctuation = /[$%@.(){}\\[\\];,\\\\]/\n    var func = {\n      pattern: /%?\\b\\w+(?=\\()/,\n      alias: 'keyword'\n    }\n    var args = {\n      function: func,\n      'arg-value': {\n        pattern: /(=\\s*)[A-Z\\.]+/i,\n        lookbehind: true\n      },\n      operator: /=/,\n      'macro-variable': macroVariable,\n      arg: {\n        pattern: /[A-Z]+/i,\n        alias: 'keyword'\n      },\n      number: number,\n      'numeric-constant': numericConstant,\n      punctuation: punctuation,\n      string: string\n    }\n    var format = {\n      pattern: /\\b(?:format|put)\\b=?[\\w'$.]+/i,\n      inside: {\n        keyword: /^(?:format|put)(?==)/i,\n        equals: /=/,\n        format: {\n          pattern: /(?:\\w|\\$\\d)+\\.\\d?/,\n          alias: 'number'\n        }\n      }\n    }\n    var altformat = {\n      pattern: /\\b(?:format|put)\\s+[\\w']+(?:\\s+[$.\\w]+)+(?=;)/i,\n      inside: {\n        keyword: /^(?:format|put)/i,\n        format: {\n          pattern: /[\\w$]+\\.\\d?/,\n          alias: 'number'\n        }\n      }\n    }\n    var globalStatements = {\n      pattern:\n        /((?:^|\\s)=?)(?:catname|checkpoint execute_always|dm|endsas|filename|footnote|%include|libname|%list|lock|missing|options|page|resetline|%run|sasfile|skip|sysecho|title\\d?)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    }\n    var submitStatement = {\n      pattern: /(^|\\s)(?:submit(?:\\s+(?:load|norun|parseonly))?|endsubmit)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    }\n    var actionSets =\n      /aStore|accessControl|aggregation|audio|autotune|bayesianNetClassifier|bioMedImage|boolRule|builtins|cardinality|cdm|clustering|conditionalRandomFields|configuration|copula|countreg|dataDiscovery|dataPreprocess|dataSciencePilot|dataStep|decisionTree|deduplication|deepLearn|deepNeural|deepRnn|ds2|ecm|entityRes|espCluster|explainModel|factmac|fastKnn|fcmpact|fedSql|freqTab|gVarCluster|gam|gleam|graphSemiSupLearn|hiddenMarkovModel|hyperGroup|ica|image|iml|kernalPca|langModel|ldaTopic|loadStreams|mbc|mixed|mlTools|modelPublishing|network|neuralNet|nmf|nonParametricBayes|nonlinear|optNetwork|optimization|panel|pca|percentile|phreg|pls|qkb|qlim|quantreg|recommend|regression|reinforcementLearn|robustPca|ruleMining|sampling|sandwich|sccasl|search(?:Analytics)?|sentimentAnalysis|sequence|session(?:Prop)?|severity|simSystem|simple|smartData|sparkEmbeddedProcess|sparseML|spatialreg|spc|stabilityMonitoring|svDataDescription|svm|table|text(?:Filters|Frequency|Mining|Parse|Rule(?:Develop|Score)|Topic|Util)|timeData|transpose|tsInfo|tsReconcile|uniTimeSeries|varReduce/\n        .source\n    var casActions = {\n      pattern: RegExp(\n        /(^|\\s)(?:action\\s+)?(?:<act>)\\.[a-z]+\\b[^;]+/.source.replace(\n          /<act>/g,\n          function () {\n            return actionSets\n          }\n        ),\n        'i'\n      ),\n      lookbehind: true,\n      inside: {\n        keyword: RegExp(\n          /(?:<act>)\\.[a-z]+\\b/.source.replace(/<act>/g, function () {\n            return actionSets\n          }),\n          'i'\n        ),\n        action: {\n          pattern: /(?:action)/i,\n          alias: 'keyword'\n        },\n        comment: comment,\n        function: func,\n        'arg-value': args['arg-value'],\n        operator: args.operator,\n        argument: args.arg,\n        number: number,\n        'numeric-constant': numericConstant,\n        punctuation: punctuation,\n        string: string\n      }\n    }\n    var keywords = {\n      pattern:\n        /((?:^|\\s)=?)(?:after|analysis|and|array|barchart|barwidth|begingraph|by|call|cas|cbarline|cfill|class(?:lev)?|close|column|computed?|contains|continue|data(?==)|define|delete|describe|document|do\\s+over|do|dol|drop|dul|else|end(?:comp|source)?|entryTitle|eval(?:uate)?|exec(?:ute)?|exit|file(?:name)?|fill(?:attrs)?|flist|fnc|function(?:list)?|global|goto|group(?:by)?|headline|headskip|histogram|if|infile|keep|keylabel|keyword|label|layout|leave|legendlabel|length|libname|loadactionset|merge|midpoints|_?null_|name|noobs|nowd|ods|options|or|otherwise|out(?:put)?|over(?:lay)?|plot|print|put|raise|ranexp|rannor|rbreak|retain|return|select|session|sessref|set|source|statgraph|sum|summarize|table|temp|terminate|then\\s+do|then|title\\d?|to|var|when|where|xaxisopts|y2axisopts|yaxisopts)\\b/i,\n      lookbehind: true\n    }\n    Prism.languages.sas = {\n      datalines: {\n        pattern: /^([ \\t]*)(?:cards|(?:data)?lines);[\\s\\S]+?^[ \\t]*;/im,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          keyword: {\n            pattern: /^(?:cards|(?:data)?lines)/i\n          },\n          punctuation: /;/\n        }\n      },\n      'proc-sql': {\n        pattern:\n          /(^proc\\s+(?:fed)?sql(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          sql: {\n            pattern: RegExp(\n              /^[ \\t]*(?:select|alter\\s+table|(?:create|describe|drop)\\s+(?:index|table(?:\\s+constraints)?|view)|create\\s+unique\\s+index|insert\\s+into|update)(?:<str>|[^;\"'])+;/.source.replace(\n                /<str>/g,\n                function () {\n                  return stringPattern\n                }\n              ),\n              'im'\n            ),\n            alias: 'language-sql',\n            inside: Prism.languages.sql\n          },\n          'global-statements': globalStatements,\n          'sql-statements': {\n            pattern:\n              /(^|\\s)(?:disconnect\\s+from|begin|commit|exec(?:ute)?|reset|rollback|validate)\\b/i,\n            lookbehind: true,\n            alias: 'keyword'\n          },\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-groovy': {\n        pattern:\n          /(^proc\\s+groovy(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          groovy: {\n            pattern: RegExp(\n              /(^[ \\t]*submit(?:\\s+(?:load|norun|parseonly))?)(?:<str>|[^\"'])+?(?=endsubmit;)/.source.replace(\n                /<str>/g,\n                function () {\n                  return stringPattern\n                }\n              ),\n              'im'\n            ),\n            lookbehind: true,\n            alias: 'language-groovy',\n            inside: Prism.languages.groovy\n          },\n          keyword: keywords,\n          'submit-statement': submitStatement,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-lua': {\n        pattern:\n          /(^proc\\s+lua(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          lua: {\n            pattern: RegExp(\n              /(^[ \\t]*submit(?:\\s+(?:load|norun|parseonly))?)(?:<str>|[^\"'])+?(?=endsubmit;)/.source.replace(\n                /<str>/g,\n                function () {\n                  return stringPattern\n                }\n              ),\n              'im'\n            ),\n            lookbehind: true,\n            alias: 'language-lua',\n            inside: Prism.languages.lua\n          },\n          keyword: keywords,\n          'submit-statement': submitStatement,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-cas': {\n        pattern:\n          /(^proc\\s+cas(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|quit|data);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          'statement-var': {\n            pattern: /((?:^|\\s)=?)saveresult\\s[^;]+/im,\n            lookbehind: true,\n            inside: {\n              statement: {\n                pattern: /^saveresult\\s+\\S+/i,\n                inside: {\n                  keyword: /^(?:saveresult)/i\n                }\n              },\n              rest: args\n            }\n          },\n          'cas-actions': casActions,\n          statement: {\n            pattern:\n              /((?:^|\\s)=?)(?:default|(?:un)?set|on|output|upload)[^;]+/im,\n            lookbehind: true,\n            inside: args\n          },\n          step: step,\n          keyword: keywords,\n          function: func,\n          format: format,\n          altformat: altformat,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-args': {\n        pattern: RegExp(\n          /(^proc\\s+\\w+\\s+)(?!\\s)(?:[^;\"']|<str>)+;/.source.replace(\n            /<str>/g,\n            function () {\n              return stringPattern\n            }\n          ),\n          'im'\n        ),\n        lookbehind: true,\n        inside: args\n      },\n      /*Special keywords within macros*/\n      'macro-keyword': macroKeyword,\n      'macro-variable': macroVariable,\n      'macro-string-functions': {\n        pattern:\n          /((?:^|\\s|=))%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)\\(.*?(?:[^%]\\))/i,\n        lookbehind: true,\n        inside: {\n          function: {\n            pattern: /%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)/i,\n            alias: 'keyword'\n          },\n          'macro-keyword': macroKeyword,\n          'macro-variable': macroVariable,\n          'escaped-char': {\n            pattern: /%['\"()<>=¬^~;,#]/\n          },\n          punctuation: punctuation\n        }\n      },\n      'macro-declaration': {\n        pattern: /^%macro[^;]+(?=;)/im,\n        inside: {\n          keyword: /%macro/i\n        }\n      },\n      'macro-end': {\n        pattern: /^%mend[^;]+(?=;)/im,\n        inside: {\n          keyword: /%mend/i\n        }\n      },\n      /*%_zscore(headcir, _lhc, _mhc, _shc, headcz, headcpct, _Fheadcz); */\n      macro: {\n        pattern: /%_\\w+(?=\\()/,\n        alias: 'keyword'\n      },\n      input: {\n        pattern: /\\binput\\s[-\\w\\s/*.$&]+;/i,\n        inside: {\n          input: {\n            alias: 'keyword',\n            pattern: /^input/i\n          },\n          comment: comment,\n          number: number,\n          'numeric-constant': numericConstant\n        }\n      },\n      'options-args': {\n        pattern: /(^options)[-'\"|/\\\\<>*+=:()\\w\\s]*(?=;)/im,\n        lookbehind: true,\n        inside: args\n      },\n      'cas-actions': casActions,\n      comment: comment,\n      function: func,\n      format: format,\n      altformat: altformat,\n      'numeric-constant': numericConstant,\n      datetime: {\n        // '1jan2013'd, '9:25:19pm't, '18jan2003:9:27:05am'dt\n        pattern: RegExp(stringPattern + '(?:dt?|t)'),\n        alias: 'number'\n      },\n      string: string,\n      step: step,\n      keyword: keywords,\n      // In SAS Studio syntax highlighting, these operators are styled like keywords\n      'operator-keyword': {\n        pattern: /\\b(?:eq|ge|gt|in|le|lt|ne|not)\\b/i,\n        alias: 'operator'\n      },\n      // Decimal (1.2e23), hexadecimal (0c1x)\n      number: number,\n      operator: /\\*\\*?|\\|\\|?|!!?|¦¦?|<[>=]?|>[<=]?|[-+\\/=&]|[~¬^]=?/,\n      punctuation: punctuation\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,aAAa,GAAG,6CAA6C,CAACC,MAAM;IACxE,IAAIC,MAAM,GAAG,kDAAkD;IAC/D,IAAIC,eAAe,GAAG;MACpBC,OAAO,EAAEC,MAAM,CAACL,aAAa,GAAG,MAAM,CAAC;MACvCM,KAAK,EAAE;IACT,CAAC;IACD,IAAIC,aAAa,GAAG;MAClBH,OAAO,EAAE;IACX,CAAC;IACD,IAAII,YAAY,GAAG;MACjBJ,OAAO,EACL,iVAAiV;MACnVK,UAAU,EAAE,IAAI;MAChBH,KAAK,EAAE;IACT,CAAC;IACD,IAAII,IAAI,GAAG;MACTN,OAAO,EAAE,4CAA4C;MACrDE,KAAK,EAAE,SAAS;MAChBG,UAAU,EAAE;IACd,CAAC;IACD,IAAIE,OAAO,GAAG,CACZ,kBAAkB,EAClB;MACEP,OAAO,EAAE,yBAAyB;MAClCK,UAAU,EAAE;IACd,CAAC,CACF;IACD,IAAIG,MAAM,GAAG;MACXR,OAAO,EAAEC,MAAM,CAACL,aAAa,CAAC;MAC9Ba,MAAM,EAAE;IACV,CAAC;IACD,IAAIC,WAAW,GAAG,oBAAoB;IACtC,IAAIC,IAAI,GAAG;MACTX,OAAO,EAAE,eAAe;MACxBE,KAAK,EAAE;IACT,CAAC;IACD,IAAIU,IAAI,GAAG;MACTC,QAAQ,EAAEF,IAAI;MACd,WAAW,EAAE;QACXX,OAAO,EAAE,iBAAiB;QAC1BK,UAAU,EAAE;MACd,CAAC;MACDS,QAAQ,EAAE,GAAG;MACb,gBAAgB,EAAEX,aAAa;MAC/BY,GAAG,EAAE;QACHf,OAAO,EAAE,SAAS;QAClBE,KAAK,EAAE;MACT,CAAC;MACDJ,MAAM,EAAEA,MAAM;MACd,kBAAkB,EAAEC,eAAe;MACnCW,WAAW,EAAEA,WAAW;MACxBF,MAAM,EAAEA;IACV,CAAC;IACD,IAAIQ,MAAM,GAAG;MACXhB,OAAO,EAAE,+BAA+B;MACxCiB,MAAM,EAAE;QACNC,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE,GAAG;QACXH,MAAM,EAAE;UACNhB,OAAO,EAAE,mBAAmB;UAC5BE,KAAK,EAAE;QACT;MACF;IACF,CAAC;IACD,IAAIkB,SAAS,GAAG;MACdpB,OAAO,EAAE,gDAAgD;MACzDiB,MAAM,EAAE;QACNC,OAAO,EAAE,kBAAkB;QAC3BF,MAAM,EAAE;UACNhB,OAAO,EAAE,aAAa;UACtBE,KAAK,EAAE;QACT;MACF;IACF,CAAC;IACD,IAAImB,gBAAgB,GAAG;MACrBrB,OAAO,EACL,gLAAgL;MAClLK,UAAU,EAAE,IAAI;MAChBH,KAAK,EAAE;IACT,CAAC;IACD,IAAIoB,eAAe,GAAG;MACpBtB,OAAO,EAAE,+DAA+D;MACxEK,UAAU,EAAE,IAAI;MAChBH,KAAK,EAAE;IACT,CAAC;IACD,IAAIqB,UAAU,GACZ,8iCAA8iC,CAC3iC1B,MAAM;IACX,IAAI2B,UAAU,GAAG;MACfxB,OAAO,EAAEC,MAAM,CACb,8CAA8C,CAACJ,MAAM,CAAC4B,OAAO,CAC3D,QAAQ,EACR,YAAY;QACV,OAAOF,UAAU;MACnB,CACF,CAAC,EACD,GACF,CAAC;MACDlB,UAAU,EAAE,IAAI;MAChBY,MAAM,EAAE;QACNC,OAAO,EAAEjB,MAAM,CACb,qBAAqB,CAACJ,MAAM,CAAC4B,OAAO,CAAC,QAAQ,EAAE,YAAY;UACzD,OAAOF,UAAU;QACnB,CAAC,CAAC,EACF,GACF,CAAC;QACDG,MAAM,EAAE;UACN1B,OAAO,EAAE,aAAa;UACtBE,KAAK,EAAE;QACT,CAAC;QACDK,OAAO,EAAEA,OAAO;QAChBM,QAAQ,EAAEF,IAAI;QACd,WAAW,EAAEC,IAAI,CAAC,WAAW,CAAC;QAC9BE,QAAQ,EAAEF,IAAI,CAACE,QAAQ;QACvBa,QAAQ,EAAEf,IAAI,CAACG,GAAG;QAClBjB,MAAM,EAAEA,MAAM;QACd,kBAAkB,EAAEC,eAAe;QACnCW,WAAW,EAAEA,WAAW;QACxBF,MAAM,EAAEA;MACV;IACF,CAAC;IACD,IAAIoB,QAAQ,GAAG;MACb5B,OAAO,EACL,wxBAAwxB;MAC1xBK,UAAU,EAAE;IACd,CAAC;IACDV,KAAK,CAACkC,SAAS,CAACrC,GAAG,GAAG;MACpBsC,SAAS,EAAE;QACT9B,OAAO,EAAE,sDAAsD;QAC/DK,UAAU,EAAE,IAAI;QAChBH,KAAK,EAAE,QAAQ;QACfe,MAAM,EAAE;UACNC,OAAO,EAAE;YACPlB,OAAO,EAAE;UACX,CAAC;UACDU,WAAW,EAAE;QACf;MACF,CAAC;MACD,UAAU,EAAE;QACVV,OAAO,EACL,8FAA8F;QAChGK,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAE;UACNc,GAAG,EAAE;YACH/B,OAAO,EAAEC,MAAM,CACb,mKAAmK,CAACJ,MAAM,CAAC4B,OAAO,CAChL,QAAQ,EACR,YAAY;cACV,OAAO7B,aAAa;YACtB,CACF,CAAC,EACD,IACF,CAAC;YACDM,KAAK,EAAE,cAAc;YACrBe,MAAM,EAAEtB,KAAK,CAACkC,SAAS,CAACE;UAC1B,CAAC;UACD,mBAAmB,EAAEV,gBAAgB;UACrC,gBAAgB,EAAE;YAChBrB,OAAO,EACL,kFAAkF;YACpFK,UAAU,EAAE,IAAI;YAChBH,KAAK,EAAE;UACT,CAAC;UACDJ,MAAM,EAAEA,MAAM;UACd,kBAAkB,EAAEC,eAAe;UACnCW,WAAW,EAAEA,WAAW;UACxBF,MAAM,EAAEA;QACV;MACF,CAAC;MACD,aAAa,EAAE;QACbR,OAAO,EACL,yFAAyF;QAC3FK,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAE;UACNV,OAAO,EAAEA,OAAO;UAChByB,MAAM,EAAE;YACNhC,OAAO,EAAEC,MAAM,CACb,gFAAgF,CAACJ,MAAM,CAAC4B,OAAO,CAC7F,QAAQ,EACR,YAAY;cACV,OAAO7B,aAAa;YACtB,CACF,CAAC,EACD,IACF,CAAC;YACDS,UAAU,EAAE,IAAI;YAChBH,KAAK,EAAE,iBAAiB;YACxBe,MAAM,EAAEtB,KAAK,CAACkC,SAAS,CAACG;UAC1B,CAAC;UACDd,OAAO,EAAEU,QAAQ;UACjB,kBAAkB,EAAEN,eAAe;UACnC,mBAAmB,EAAED,gBAAgB;UACrCvB,MAAM,EAAEA,MAAM;UACd,kBAAkB,EAAEC,eAAe;UACnCW,WAAW,EAAEA,WAAW;UACxBF,MAAM,EAAEA;QACV;MACF,CAAC;MACD,UAAU,EAAE;QACVR,OAAO,EACL,sFAAsF;QACxFK,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAE;UACNV,OAAO,EAAEA,OAAO;UAChB0B,GAAG,EAAE;YACHjC,OAAO,EAAEC,MAAM,CACb,gFAAgF,CAACJ,MAAM,CAAC4B,OAAO,CAC7F,QAAQ,EACR,YAAY;cACV,OAAO7B,aAAa;YACtB,CACF,CAAC,EACD,IACF,CAAC;YACDS,UAAU,EAAE,IAAI;YAChBH,KAAK,EAAE,cAAc;YACrBe,MAAM,EAAEtB,KAAK,CAACkC,SAAS,CAACI;UAC1B,CAAC;UACDf,OAAO,EAAEU,QAAQ;UACjB,kBAAkB,EAAEN,eAAe;UACnC,mBAAmB,EAAED,gBAAgB;UACrCvB,MAAM,EAAEA,MAAM;UACd,kBAAkB,EAAEC,eAAe;UACnCW,WAAW,EAAEA,WAAW;UACxBF,MAAM,EAAEA;QACV;MACF,CAAC;MACD,UAAU,EAAE;QACVR,OAAO,EACL,kFAAkF;QACpFK,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAE;UACNV,OAAO,EAAEA,OAAO;UAChB,eAAe,EAAE;YACfP,OAAO,EAAE,iCAAiC;YAC1CK,UAAU,EAAE,IAAI;YAChBY,MAAM,EAAE;cACNiB,SAAS,EAAE;gBACTlC,OAAO,EAAE,oBAAoB;gBAC7BiB,MAAM,EAAE;kBACNC,OAAO,EAAE;gBACX;cACF,CAAC;cACDiB,IAAI,EAAEvB;YACR;UACF,CAAC;UACD,aAAa,EAAEY,UAAU;UACzBU,SAAS,EAAE;YACTlC,OAAO,EACL,4DAA4D;YAC9DK,UAAU,EAAE,IAAI;YAChBY,MAAM,EAAEL;UACV,CAAC;UACDN,IAAI,EAAEA,IAAI;UACVY,OAAO,EAAEU,QAAQ;UACjBf,QAAQ,EAAEF,IAAI;UACdK,MAAM,EAAEA,MAAM;UACdI,SAAS,EAAEA,SAAS;UACpB,mBAAmB,EAAEC,gBAAgB;UACrCvB,MAAM,EAAEA,MAAM;UACd,kBAAkB,EAAEC,eAAe;UACnCW,WAAW,EAAEA,WAAW;UACxBF,MAAM,EAAEA;QACV;MACF,CAAC;MACD,WAAW,EAAE;QACXR,OAAO,EAAEC,MAAM,CACb,0CAA0C,CAACJ,MAAM,CAAC4B,OAAO,CACvD,QAAQ,EACR,YAAY;UACV,OAAO7B,aAAa;QACtB,CACF,CAAC,EACD,IACF,CAAC;QACDS,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAEL;MACV,CAAC;MACD;MACA,eAAe,EAAER,YAAY;MAC7B,gBAAgB,EAAED,aAAa;MAC/B,wBAAwB,EAAE;QACxBH,OAAO,EACL,0EAA0E;QAC5EK,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAE;UACNJ,QAAQ,EAAE;YACRb,OAAO,EAAE,+CAA+C;YACxDE,KAAK,EAAE;UACT,CAAC;UACD,eAAe,EAAEE,YAAY;UAC7B,gBAAgB,EAAED,aAAa;UAC/B,cAAc,EAAE;YACdH,OAAO,EAAE;UACX,CAAC;UACDU,WAAW,EAAEA;QACf;MACF,CAAC;MACD,mBAAmB,EAAE;QACnBV,OAAO,EAAE,qBAAqB;QAC9BiB,MAAM,EAAE;UACNC,OAAO,EAAE;QACX;MACF,CAAC;MACD,WAAW,EAAE;QACXlB,OAAO,EAAE,oBAAoB;QAC7BiB,MAAM,EAAE;UACNC,OAAO,EAAE;QACX;MACF,CAAC;MACD;MACAkB,KAAK,EAAE;QACLpC,OAAO,EAAE,aAAa;QACtBE,KAAK,EAAE;MACT,CAAC;MACDmC,KAAK,EAAE;QACLrC,OAAO,EAAE,0BAA0B;QACnCiB,MAAM,EAAE;UACNoB,KAAK,EAAE;YACLnC,KAAK,EAAE,SAAS;YAChBF,OAAO,EAAE;UACX,CAAC;UACDO,OAAO,EAAEA,OAAO;UAChBT,MAAM,EAAEA,MAAM;UACd,kBAAkB,EAAEC;QACtB;MACF,CAAC;MACD,cAAc,EAAE;QACdC,OAAO,EAAE,yCAAyC;QAClDK,UAAU,EAAE,IAAI;QAChBY,MAAM,EAAEL;MACV,CAAC;MACD,aAAa,EAAEY,UAAU;MACzBjB,OAAO,EAAEA,OAAO;MAChBM,QAAQ,EAAEF,IAAI;MACdK,MAAM,EAAEA,MAAM;MACdI,SAAS,EAAEA,SAAS;MACpB,kBAAkB,EAAErB,eAAe;MACnCuC,QAAQ,EAAE;QACR;QACAtC,OAAO,EAAEC,MAAM,CAACL,aAAa,GAAG,WAAW,CAAC;QAC5CM,KAAK,EAAE;MACT,CAAC;MACDM,MAAM,EAAEA,MAAM;MACdF,IAAI,EAAEA,IAAI;MACVY,OAAO,EAAEU,QAAQ;MACjB;MACA,kBAAkB,EAAE;QAClB5B,OAAO,EAAE,mCAAmC;QAC5CE,KAAK,EAAE;MACT,CAAC;MACD;MACAJ,MAAM,EAAEA,MAAM;MACdgB,QAAQ,EAAE,oDAAoD;MAC9DJ,WAAW,EAAEA;IACf,CAAC;EACH,CAAC,EAAEf,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}