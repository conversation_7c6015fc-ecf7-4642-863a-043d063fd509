{"ast": null, "code": "/*\n Language: Gherkin\n Author: <PERSON> (@pikesley) <<EMAIL>>\n Description: Gherkin is the format for cucumber specifications. It is a domain specific language which helps you to describe business behavior without the need to go into detail of implementation.\n Website: https://cucumber.io/docs/gherkin/\n */\n\nfunction gherkin(hljs) {\n  return {\n    name: '<PERSON>her<PERSON>',\n    aliases: ['feature'],\n    keywords: 'Feature Background Ability Business\\ Need Scenario Scenarios Scenario\\ Outline Scenario\\ Template Examples Given And Then But When',\n    contains: [{\n      className: 'symbol',\n      begin: '\\\\*',\n      relevance: 0\n    }, {\n      className: 'meta',\n      begin: '@[^@\\\\s]+'\n    }, {\n      begin: '\\\\|',\n      end: '\\\\|\\\\w*$',\n      contains: [{\n        className: 'string',\n        begin: '[^|]+'\n      }]\n    }, {\n      className: 'variable',\n      begin: '<',\n      end: '>'\n    }, hljs.HASH_COMMENT_MODE, {\n      className: 'string',\n      begin: '\"\"\"',\n      end: '\"\"\"'\n    }, hljs.QUOTE_STRING_MODE]\n  };\n}\nmodule.exports = gherkin;", "map": {"version": 3, "names": ["g<PERSON>kin", "hljs", "name", "aliases", "keywords", "contains", "className", "begin", "relevance", "end", "HASH_COMMENT_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/gherkin.js"], "sourcesContent": ["/*\n Language: Gherkin\n Author: <PERSON> (@pikesley) <<EMAIL>>\n Description: Gherkin is the format for cucumber specifications. It is a domain specific language which helps you to describe business behavior without the need to go into detail of implementation.\n Website: https://cucumber.io/docs/gherkin/\n */\n\nfunction gherkin(hljs) {\n  return {\n    name: '<PERSON>her<PERSON>',\n    aliases: ['feature'],\n    keywords: 'Feature Background Ability Business\\ Need Scenario Scenarios Scenario\\ Outline Scenario\\ Template Examples Given And Then But When',\n    contains: [\n      {\n        className: 'symbol',\n        begin: '\\\\*',\n        relevance: 0\n      },\n      {\n        className: 'meta',\n        begin: '@[^@\\\\s]+'\n      },\n      {\n        begin: '\\\\|',\n        end: '\\\\|\\\\w*$',\n        contains: [\n          {\n            className: 'string',\n            begin: '[^|]+'\n          }\n        ]\n      },\n      {\n        className: 'variable',\n        begin: '<',\n        end: '>'\n      },\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'string',\n        begin: '\"\"\"',\n        end: '\"\"\"'\n      },\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n}\n\nmodule.exports = gherkin;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,OAAO;IACLC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,QAAQ,EAAE,oIAAoI;IAC9IC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,KAAK;MACZE,GAAG,EAAE,UAAU;MACfJ,QAAQ,EAAE,CACR;QACEC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACD;MACED,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,GAAG;MACVE,GAAG,EAAE;IACP,CAAC,EACDR,IAAI,CAACS,iBAAiB,EACtB;MACEJ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,KAAK;MACZE,GAAG,EAAE;IACP,CAAC,EACDR,IAAI,CAACU,iBAAiB;EAE1B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGb,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}