{"ast": null, "code": "'use strict';\n\nmodule.exports = powerquery;\npowerquery.displayName = 'powerquery';\npowerquery.aliases = [];\nfunction powerquery(Prism) {\n  // https://docs.microsoft.com/en-us/powerquery-m/power-query-m-language-specification\n  Prism.languages.powerquery = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'quoted-identifier': {\n      pattern: /#\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    string: {\n      pattern: /(?:#!)?\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    constant: [/\\bDay\\.(?:Friday|Monday|Saturday|Sunday|Thursday|Tuesday|Wednesday)\\b/, /\\bTraceLevel\\.(?:Critical|Error|Information|Verbose|Warning)\\b/, /\\bOccurrence\\.(?:All|First|Last)\\b/, /\\bOrder\\.(?:Ascending|Descending)\\b/, /\\bRoundingMode\\.(?:AwayFromZero|Down|ToEven|TowardZero|Up)\\b/, /\\bMissingField\\.(?:Error|Ignore|UseNull)\\b/, /\\bQuoteStyle\\.(?:Csv|None)\\b/, /\\bJoinKind\\.(?:FullOuter|Inner|LeftAnti|LeftOuter|RightAnti|RightOuter)\\b/, /\\bGroupKind\\.(?:Global|Local)\\b/, /\\bExtraValues\\.(?:Error|Ignore|List)\\b/, /\\bJoinAlgorithm\\.(?:Dynamic|LeftHash|LeftIndex|PairwiseHash|RightHash|RightIndex|SortMerge)\\b/, /\\bJoinSide\\.(?:Left|Right)\\b/, /\\bPrecision\\.(?:Decimal|Double)\\b/, /\\bRelativePosition\\.From(?:End|Start)\\b/, /\\bTextEncoding\\.(?:Ascii|BigEndianUnicode|Unicode|Utf16|Utf8|Windows)\\b/, /\\b(?:Any|Binary|Date|DateTime|DateTimeZone|Duration|Function|Int16|Int32|Int64|Int8|List|Logical|None|Number|Record|Table|Text|Time)\\.Type\\b/, /\\bnull\\b/],\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: /\\b(?:and|as|each|else|error|if|in|is|let|meta|not|nullable|optional|or|otherwise|section|shared|then|try|type)\\b|#(?:binary|date|datetime|datetimezone|duration|infinity|nan|sections|shared|table|time)\\b/,\n    function: {\n      pattern: /(^|[^#\\w.])[a-z_][\\w.]*(?=\\s*\\()/i,\n      lookbehind: true\n    },\n    'data-type': {\n      pattern: /\\b(?:any|anynonnull|binary|date|datetime|datetimezone|duration|function|list|logical|none|number|record|table|text|time)\\b/,\n      alias: 'class-name'\n    },\n    number: {\n      pattern: /\\b0x[\\da-f]+\\b|(?:[+-]?(?:\\b\\d+\\.)?\\b\\d+|[+-]\\.\\d+|(^|[^.])\\B\\.\\d+)(?:e[+-]?\\d+)?\\b/i,\n      lookbehind: true\n    },\n    operator: /[-+*\\/&?@^]|<(?:=>?|>)?|>=?|=>?|\\.\\.\\.?/,\n    punctuation: /[,;\\[\\](){}]/\n  };\n  Prism.languages.pq = Prism.languages['powerquery'];\n  Prism.languages.mscript = Prism.languages['powerquery'];\n}", "map": {"version": 3, "names": ["module", "exports", "powerquery", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "string", "constant", "boolean", "keyword", "function", "alias", "number", "operator", "punctuation", "pq", "mscript"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/powerquery.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = powerquery\npowerquery.displayName = 'powerquery'\npowerquery.aliases = []\nfunction powerquery(Prism) {\n  // https://docs.microsoft.com/en-us/powerquery-m/power-query-m-language-specification\n  Prism.languages.powerquery = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'quoted-identifier': {\n      pattern: /#\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    string: {\n      pattern: /(?:#!)?\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    constant: [\n      /\\bDay\\.(?:Friday|Monday|Saturday|Sunday|Thursday|Tuesday|Wednesday)\\b/,\n      /\\bTraceLevel\\.(?:Critical|Error|Information|Verbose|Warning)\\b/,\n      /\\bOccurrence\\.(?:All|First|Last)\\b/,\n      /\\bOrder\\.(?:Ascending|Descending)\\b/,\n      /\\bRoundingMode\\.(?:AwayFromZero|Down|ToEven|TowardZero|Up)\\b/,\n      /\\bMissingField\\.(?:Error|Ignore|UseNull)\\b/,\n      /\\bQuoteStyle\\.(?:Csv|None)\\b/,\n      /\\bJoinKind\\.(?:FullOuter|Inner|LeftAnti|LeftOuter|RightAnti|RightOuter)\\b/,\n      /\\bGroupKind\\.(?:Global|Local)\\b/,\n      /\\bExtraValues\\.(?:Error|Ignore|List)\\b/,\n      /\\bJoinAlgorithm\\.(?:Dynamic|LeftHash|LeftIndex|PairwiseHash|RightHash|RightIndex|SortMerge)\\b/,\n      /\\bJoinSide\\.(?:Left|Right)\\b/,\n      /\\bPrecision\\.(?:Decimal|Double)\\b/,\n      /\\bRelativePosition\\.From(?:End|Start)\\b/,\n      /\\bTextEncoding\\.(?:Ascii|BigEndianUnicode|Unicode|Utf16|Utf8|Windows)\\b/,\n      /\\b(?:Any|Binary|Date|DateTime|DateTimeZone|Duration|Function|Int16|Int32|Int64|Int8|List|Logical|None|Number|Record|Table|Text|Time)\\.Type\\b/,\n      /\\bnull\\b/\n    ],\n    boolean: /\\b(?:false|true)\\b/,\n    keyword:\n      /\\b(?:and|as|each|else|error|if|in|is|let|meta|not|nullable|optional|or|otherwise|section|shared|then|try|type)\\b|#(?:binary|date|datetime|datetimezone|duration|infinity|nan|sections|shared|table|time)\\b/,\n    function: {\n      pattern: /(^|[^#\\w.])[a-z_][\\w.]*(?=\\s*\\()/i,\n      lookbehind: true\n    },\n    'data-type': {\n      pattern:\n        /\\b(?:any|anynonnull|binary|date|datetime|datetimezone|duration|function|list|logical|none|number|record|table|text|time)\\b/,\n      alias: 'class-name'\n    },\n    number: {\n      pattern:\n        /\\b0x[\\da-f]+\\b|(?:[+-]?(?:\\b\\d+\\.)?\\b\\d+|[+-]\\.\\d+|(^|[^.])\\B\\.\\d+)(?:e[+-]?\\d+)?\\b/i,\n      lookbehind: true\n    },\n    operator: /[-+*\\/&?@^]|<(?:=>?|>)?|>=?|=>?|\\.\\.\\.?/,\n    punctuation: /[,;\\[\\](){}]/\n  }\n  Prism.languages.pq = Prism.languages['powerquery']\n  Prism.languages.mscript = Prism.languages['powerquery']\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,UAAU;AAC3BA,UAAU,CAACC,WAAW,GAAG,YAAY;AACrCD,UAAU,CAACE,OAAO,GAAG,EAAE;AACvB,SAASF,UAAUA,CAACG,KAAK,EAAE;EACzB;EACAA,KAAK,CAACC,SAAS,CAACJ,UAAU,GAAG;IAC3BK,OAAO,EAAE;MACPC,OAAO,EAAE,sCAAsC;MAC/CC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,mBAAmB,EAAE;MACnBF,OAAO,EAAE,0BAA0B;MACnCE,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,gCAAgC;MACzCE,MAAM,EAAE;IACV,CAAC;IACDE,QAAQ,EAAE,CACR,uEAAuE,EACvE,gEAAgE,EAChE,oCAAoC,EACpC,qCAAqC,EACrC,8DAA8D,EAC9D,4CAA4C,EAC5C,8BAA8B,EAC9B,2EAA2E,EAC3E,iCAAiC,EACjC,wCAAwC,EACxC,+FAA+F,EAC/F,8BAA8B,EAC9B,mCAAmC,EACnC,yCAAyC,EACzC,yEAAyE,EACzE,8IAA8I,EAC9I,UAAU,CACX;IACDC,OAAO,EAAE,oBAAoB;IAC7BC,OAAO,EACL,4MAA4M;IAC9MC,QAAQ,EAAE;MACRP,OAAO,EAAE,mCAAmC;MAC5CC,UAAU,EAAE;IACd,CAAC;IACD,WAAW,EAAE;MACXD,OAAO,EACL,4HAA4H;MAC9HQ,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNT,OAAO,EACL,sFAAsF;MACxFC,UAAU,EAAE;IACd,CAAC;IACDS,QAAQ,EAAE,yCAAyC;IACnDC,WAAW,EAAE;EACf,CAAC;EACDd,KAAK,CAACC,SAAS,CAACc,EAAE,GAAGf,KAAK,CAACC,SAAS,CAAC,YAAY,CAAC;EAClDD,KAAK,CAACC,SAAS,CAACe,OAAO,GAAGhB,KAAK,CAACC,SAAS,CAAC,YAAY,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}