{"ast": null, "code": "'use strict';\n\nmodule.exports = dhall;\ndhall.displayName = 'dhall';\ndhall.aliases = [];\nfunction dhall(Prism) {\n  // ABNF grammar:\n  // https://github.com/dhall-lang/dhall-lang/blob/master/standard/dhall.abnf\n  Prism.languages.dhall = {\n    // Multi-line comments can be nested. E.g. {- foo {- bar -} -}\n    // The multi-line pattern is essentially this:\n    //   \\{-(?:[^-{]|-(?!\\})|\\{(?!-)|<SELF>)*-\\}\n    comment: /--.*|\\{-(?:[^-{]|-(?!\\})|\\{(?!-)|\\{-(?:[^-{]|-(?!\\})|\\{(?!-))*-\\})*-\\}/,\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\.)*\"|''(?:[^']|'(?!')|'''|''\\$\\{)*''(?!'|\\$)/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              alias: 'language-dhall',\n              inside: null // see blow\n            },\n            punctuation: /\\$\\{|\\}/\n          }\n        }\n      }\n    },\n    label: {\n      pattern: /`[^`]*`/,\n      greedy: true\n    },\n    url: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L596\n      pattern: /\\bhttps?:\\/\\/[\\w.:%!$&'*+;=@~-]+(?:\\/[\\w.:%!$&'*+;=@~-]*)*(?:\\?[/?\\w.:%!$&'*+;=@~-]*)?/,\n      greedy: true\n    },\n    env: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L661\n      pattern: /\\benv:(?:(?!\\d)\\w+|\"(?:[^\"\\\\=]|\\\\.)*\")/,\n      greedy: true,\n      inside: {\n        function: /^env/,\n        operator: /^:/,\n        variable: /[\\s\\S]+/\n      }\n    },\n    hash: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L725\n      pattern: /\\bsha256:[\\da-fA-F]{64}\\b/,\n      inside: {\n        function: /sha256/,\n        operator: /:/,\n        number: /[\\da-fA-F]{64}/\n      }\n    },\n    // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L359\n    keyword: /\\b(?:as|assert|else|forall|if|in|let|merge|missing|then|toMap|using|with)\\b|\\u2200/,\n    builtin: /\\b(?:None|Some)\\b/,\n    boolean: /\\b(?:False|True)\\b/,\n    number: /\\bNaN\\b|-?\\bInfinity\\b|[+-]?\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/,\n    operator: /\\/\\\\|\\/\\/\\\\\\\\|&&|\\|\\||===|[!=]=|\\/\\/|->|\\+\\+|::|[+*#@=:?<>|\\\\\\u2227\\u2a53\\u2261\\u2afd\\u03bb\\u2192]/,\n    punctuation: /\\.\\.|[{}\\[\\](),./]/,\n    // we'll just assume that every capital word left is a type name\n    'class-name': /\\b[A-Z]\\w*\\b/\n  };\n  Prism.languages.dhall.string.inside.interpolation.inside.expression.inside = Prism.languages.dhall;\n}", "map": {"version": 3, "names": ["module", "exports", "dhall", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "inside", "interpolation", "expression", "lookbehind", "alias", "punctuation", "label", "url", "env", "function", "operator", "variable", "hash", "number", "keyword", "builtin", "boolean"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/dhall.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dhall\ndhall.displayName = 'dhall'\ndhall.aliases = []\nfunction dhall(Prism) {\n  // ABNF grammar:\n  // https://github.com/dhall-lang/dhall-lang/blob/master/standard/dhall.abnf\n  Prism.languages.dhall = {\n    // Multi-line comments can be nested. E.g. {- foo {- bar -} -}\n    // The multi-line pattern is essentially this:\n    //   \\{-(?:[^-{]|-(?!\\})|\\{(?!-)|<SELF>)*-\\}\n    comment:\n      /--.*|\\{-(?:[^-{]|-(?!\\})|\\{(?!-)|\\{-(?:[^-{]|-(?!\\})|\\{(?!-))*-\\})*-\\}/,\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\.)*\"|''(?:[^']|'(?!')|'''|''\\$\\{)*''(?!'|\\$)/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              alias: 'language-dhall',\n              inside: null // see blow\n            },\n            punctuation: /\\$\\{|\\}/\n          }\n        }\n      }\n    },\n    label: {\n      pattern: /`[^`]*`/,\n      greedy: true\n    },\n    url: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L596\n      pattern:\n        /\\bhttps?:\\/\\/[\\w.:%!$&'*+;=@~-]+(?:\\/[\\w.:%!$&'*+;=@~-]*)*(?:\\?[/?\\w.:%!$&'*+;=@~-]*)?/,\n      greedy: true\n    },\n    env: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L661\n      pattern: /\\benv:(?:(?!\\d)\\w+|\"(?:[^\"\\\\=]|\\\\.)*\")/,\n      greedy: true,\n      inside: {\n        function: /^env/,\n        operator: /^:/,\n        variable: /[\\s\\S]+/\n      }\n    },\n    hash: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L725\n      pattern: /\\bsha256:[\\da-fA-F]{64}\\b/,\n      inside: {\n        function: /sha256/,\n        operator: /:/,\n        number: /[\\da-fA-F]{64}/\n      }\n    },\n    // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L359\n    keyword:\n      /\\b(?:as|assert|else|forall|if|in|let|merge|missing|then|toMap|using|with)\\b|\\u2200/,\n    builtin: /\\b(?:None|Some)\\b/,\n    boolean: /\\b(?:False|True)\\b/,\n    number:\n      /\\bNaN\\b|-?\\bInfinity\\b|[+-]?\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/,\n    operator:\n      /\\/\\\\|\\/\\/\\\\\\\\|&&|\\|\\||===|[!=]=|\\/\\/|->|\\+\\+|::|[+*#@=:?<>|\\\\\\u2227\\u2a53\\u2261\\u2afd\\u03bb\\u2192]/,\n    punctuation: /\\.\\.|[{}\\[\\](),./]/,\n    // we'll just assume that every capital word left is a type name\n    'class-name': /\\b[A-Z]\\w*\\b/\n  }\n  Prism.languages.dhall.string.inside.interpolation.inside.expression.inside =\n    Prism.languages.dhall\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpB;EACA;EACAA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtB;IACA;IACA;IACAK,OAAO,EACL,wEAAwE;IAC1EC,MAAM,EAAE;MACNC,OAAO,EAAE,2DAA2D;MACpEC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNC,aAAa,EAAE;UACbH,OAAO,EAAE,cAAc;UACvBE,MAAM,EAAE;YACNE,UAAU,EAAE;cACVJ,OAAO,EAAE,uBAAuB;cAChCK,UAAU,EAAE,IAAI;cAChBC,KAAK,EAAE,gBAAgB;cACvBJ,MAAM,EAAE,IAAI,CAAC;YACf,CAAC;YACDK,WAAW,EAAE;UACf;QACF;MACF;IACF,CAAC;IACDC,KAAK,EAAE;MACLR,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;IACV,CAAC;IACDQ,GAAG,EAAE;MACH;MACAT,OAAO,EACL,wFAAwF;MAC1FC,MAAM,EAAE;IACV,CAAC;IACDS,GAAG,EAAE;MACH;MACAV,OAAO,EAAE,wCAAwC;MACjDC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNS,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,IAAI,EAAE;MACJ;MACAd,OAAO,EAAE,2BAA2B;MACpCE,MAAM,EAAE;QACNS,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,GAAG;QACbG,MAAM,EAAE;MACV;IACF,CAAC;IACD;IACAC,OAAO,EACL,oFAAoF;IACtFC,OAAO,EAAE,mBAAmB;IAC5BC,OAAO,EAAE,oBAAoB;IAC7BH,MAAM,EACJ,+EAA+E;IACjFH,QAAQ,EACN,oGAAoG;IACtGL,WAAW,EAAE,oBAAoB;IACjC;IACA,YAAY,EAAE;EAChB,CAAC;EACDX,KAAK,CAACC,SAAS,CAACJ,KAAK,CAACM,MAAM,CAACG,MAAM,CAACC,aAAa,CAACD,MAAM,CAACE,UAAU,CAACF,MAAM,GACxEN,KAAK,CAACC,SAAS,CAACJ,KAAK;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}