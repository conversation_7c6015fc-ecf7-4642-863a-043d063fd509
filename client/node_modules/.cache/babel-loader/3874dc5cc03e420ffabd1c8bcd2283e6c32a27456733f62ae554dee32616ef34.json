{"ast": null, "code": "'use strict';\n\nmodule.exports = asmatmel;\nasmatmel.displayName = 'asmatmel';\nasmatmel.aliases = [];\nfunction asmatmel(Prism) {\n  Prism.languages.asmatmel = {\n    comment: {\n      pattern: /;.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    constant: /\\b(?:PORT[A-Z]|DDR[A-Z]|(?:DD|P)[A-Z](?:\\d|[0-2]\\d|3[01]))\\b/,\n    directive: {\n      pattern: /\\.\\w+(?= )/,\n      alias: 'property'\n    },\n    'r-register': {\n      pattern: /\\br(?:\\d|[12]\\d|3[01])\\b/,\n      alias: 'variable'\n    },\n    'op-code': {\n      pattern: /\\b(?:ADC|ADD|ADIW|AND|ANDI|ASR|BCLR|BLD|BRBC|BRBS|BRCC|BRCS|BREAK|BREQ|BRGE|BRHC|BRHS|BRID|BRIE|BRLO|BRLT|BRMI|BRNE|BRPL|BRSH|BRTC|BRTS|BRVC|BRVS|BSET|BST|CALL|CBI|CBR|CLC|CLH|CLI|CLN|CLR|CLS|CLT|CLV|CLZ|COM|CP|CPC|CPI|CPSE|DEC|DES|EICALL|EIJMP|ELPM|EOR|FMUL|FMULS|FMULSU|ICALL|IJMP|IN|INC|JMP|LAC|LAS|LAT|LD|LD[A-Za-z0-9]|LPM|LSL|LSR|MOV|MOVW|MUL|MULS|MULSU|NEG|NOP|OR|ORI|OUT|POP|PUSH|RCALL|RET|RETI|RJMP|ROL|ROR|SBC|SBCI|SBI|SBIC|SBIS|SBIW|SBR|SBRC|SBRS|SEC|SEH|SEI|SEN|SER|SES|SET|SEV|SEZ|SLEEP|SPM|ST|ST[A-Z0-9]|SUB|SUBI|SWAP|TST|WDR|XCH|adc|add|adiw|and|andi|asr|bclr|bld|brbc|brbs|brcc|brcs|break|breq|brge|brhc|brhs|brid|brie|brlo|brlt|brmi|brne|brpl|brsh|brtc|brts|brvc|brvs|bset|bst|call|cbi|cbr|clc|clh|cli|cln|clr|cls|clt|clv|clz|com|cp|cpc|cpi|cpse|dec|des|eicall|eijmp|elpm|eor|fmul|fmuls|fmulsu|icall|ijmp|in|inc|jmp|lac|las|lat|ld|ld[a-z0-9]|lpm|lsl|lsr|mov|movw|mul|muls|mulsu|neg|nop|or|ori|out|pop|push|rcall|ret|reti|rjmp|rol|ror|sbc|sbci|sbi|sbic|sbis|sbiw|sbr|sbrc|sbrs|sec|seh|sei|sen|ser|ses|set|sev|sez|sleep|spm|st|st[a-zA-Z0-9]|sub|subi|swap|tst|wdr|xch)\\b/,\n      alias: 'keyword'\n    },\n    'hex-number': {\n      pattern: /#?\\$[\\da-f]{2,4}\\b/i,\n      alias: 'number'\n    },\n    'binary-number': {\n      pattern: /#?%[01]+\\b/,\n      alias: 'number'\n    },\n    'decimal-number': {\n      pattern: /#?\\b\\d+\\b/,\n      alias: 'number'\n    },\n    register: {\n      pattern: /\\b[acznvshtixy]\\b/i,\n      alias: 'variable'\n    },\n    operator: />>=?|<<=?|&&?|\\|\\|?|[-+*/%&|^!=<>?]=?/,\n    punctuation: /[(),:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON>", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "constant", "directive", "alias", "register", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/asmatmel.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = asmatmel\nasmatmel.displayName = 'asmatmel'\nasmatmel.aliases = []\nfunction asmatmel(Prism) {\n  Prism.languages.asmatmel = {\n    comment: {\n      pattern: /;.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    constant: /\\b(?:PORT[A-Z]|DDR[A-Z]|(?:DD|P)[A-Z](?:\\d|[0-2]\\d|3[01]))\\b/,\n    directive: {\n      pattern: /\\.\\w+(?= )/,\n      alias: 'property'\n    },\n    'r-register': {\n      pattern: /\\br(?:\\d|[12]\\d|3[01])\\b/,\n      alias: 'variable'\n    },\n    'op-code': {\n      pattern:\n        /\\b(?:ADC|ADD|ADIW|AND|ANDI|ASR|BCLR|BLD|BRBC|BRBS|BRCC|BRCS|BREAK|BREQ|BRGE|BRHC|BRHS|BRID|BRIE|BRLO|BRLT|BRMI|BRNE|BRPL|BRSH|BRTC|BRTS|BRVC|BRVS|BSET|BST|CALL|CBI|CBR|CLC|CLH|CLI|CLN|CLR|CLS|CLT|CLV|CLZ|COM|CP|CPC|CPI|CPSE|DEC|DES|EICALL|EIJMP|ELPM|EOR|FMUL|FMULS|FMULSU|ICALL|IJMP|IN|INC|JMP|LAC|LAS|LAT|LD|LD[A-Za-z0-9]|LPM|LSL|LSR|MOV|MOVW|MUL|MULS|MULSU|NEG|NOP|OR|ORI|OUT|POP|PUSH|RCALL|RET|RETI|RJMP|ROL|ROR|SBC|SBCI|SBI|SBIC|SBIS|SBIW|SBR|SBRC|SBRS|SEC|SEH|SEI|SEN|SER|SES|SET|SEV|SEZ|SLEEP|SPM|ST|ST[A-Z0-9]|SUB|SUBI|SWAP|TST|WDR|XCH|adc|add|adiw|and|andi|asr|bclr|bld|brbc|brbs|brcc|brcs|break|breq|brge|brhc|brhs|brid|brie|brlo|brlt|brmi|brne|brpl|brsh|brtc|brts|brvc|brvs|bset|bst|call|cbi|cbr|clc|clh|cli|cln|clr|cls|clt|clv|clz|com|cp|cpc|cpi|cpse|dec|des|eicall|eijmp|elpm|eor|fmul|fmuls|fmulsu|icall|ijmp|in|inc|jmp|lac|las|lat|ld|ld[a-z0-9]|lpm|lsl|lsr|mov|movw|mul|muls|mulsu|neg|nop|or|ori|out|pop|push|rcall|ret|reti|rjmp|rol|ror|sbc|sbci|sbi|sbic|sbis|sbiw|sbr|sbrc|sbrs|sec|seh|sei|sen|ser|ses|set|sev|sez|sleep|spm|st|st[a-zA-Z0-9]|sub|subi|swap|tst|wdr|xch)\\b/,\n      alias: 'keyword'\n    },\n    'hex-number': {\n      pattern: /#?\\$[\\da-f]{2,4}\\b/i,\n      alias: 'number'\n    },\n    'binary-number': {\n      pattern: /#?%[01]+\\b/,\n      alias: 'number'\n    },\n    'decimal-number': {\n      pattern: /#?\\b\\d+\\b/,\n      alias: 'number'\n    },\n    register: {\n      pattern: /\\b[acznvshtixy]\\b/i,\n      alias: 'variable'\n    },\n    operator: />>=?|<<=?|&&?|\\|\\|?|[-+*/%&|^!=<>?]=?/,\n    punctuation: /[(),:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvBA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAG;IACzBK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,mCAAmC;MAC5CC,MAAM,EAAE;IACV,CAAC;IACDE,QAAQ,EAAE,8DAA8D;IACxEC,SAAS,EAAE;MACTJ,OAAO,EAAE,YAAY;MACrBK,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE;MACZL,OAAO,EAAE,0BAA0B;MACnCK,KAAK,EAAE;IACT,CAAC;IACD,SAAS,EAAE;MACTL,OAAO,EACL,6jCAA6jC;MAC/jCK,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE;MACZL,OAAO,EAAE,qBAAqB;MAC9BK,KAAK,EAAE;IACT,CAAC;IACD,eAAe,EAAE;MACfL,OAAO,EAAE,YAAY;MACrBK,KAAK,EAAE;IACT,CAAC;IACD,gBAAgB,EAAE;MAChBL,OAAO,EAAE,WAAW;MACpBK,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRN,OAAO,EAAE,oBAAoB;MAC7BK,KAAK,EAAE;IACT,CAAC;IACDE,QAAQ,EAAE,uCAAuC;IACjDC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}