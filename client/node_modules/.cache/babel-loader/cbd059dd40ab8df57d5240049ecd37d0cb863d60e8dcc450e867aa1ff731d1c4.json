{"ast": null, "code": "/*\nLanguage: SAS\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Syntax Highlighting for SAS\n*/\n\nfunction sas(hljs) {\n  // Data step and PROC SQL statements\n  const SAS_KEYWORDS = 'do if then else end until while ' + '' + 'abort array attrib by call cards cards4 catname continue ' + 'datalines datalines4 delete delim delimiter display dm drop ' + 'endsas error file filename footnote format goto in infile ' + 'informat input keep label leave length libname link list ' + 'lostcard merge missing modify options output out page put ' + 'redirect remove rename replace retain return select set skip ' + 'startsas stop title update waitsas where window x systask ' + '' + 'add and alter as cascade check create delete describe ' + 'distinct drop foreign from group having index insert into in ' + 'key like message modify msgtype not null on or order primary ' + 'references reset restrict select set table unique update ' + 'validate view where';\n\n  // Built-in SAS functions\n  const SAS_FUN = 'abs|addr|airy|arcos|arsin|atan|attrc|attrn|band|' + 'betainv|blshift|bnot|bor|brshift|bxor|byte|cdf|ceil|' + 'cexist|cinv|close|cnonct|collate|compbl|compound|' + 'compress|cos|cosh|css|curobs|cv|daccdb|daccdbsl|' + 'daccsl|daccsyd|dacctab|dairy|date|datejul|datepart|' + 'datetime|day|dclose|depdb|depdbsl|depdbsl|depsl|' + 'depsl|depsyd|depsyd|deptab|deptab|dequote|dhms|dif|' + 'digamma|dim|dinfo|dnum|dopen|doptname|doptnum|dread|' + 'dropnote|dsname|erf|erfc|exist|exp|fappend|fclose|' + 'fcol|fdelete|fetch|fetchobs|fexist|fget|fileexist|' + 'filename|fileref|finfo|finv|fipname|fipnamel|' + 'fipstate|floor|fnonct|fnote|fopen|foptname|foptnum|' + 'fpoint|fpos|fput|fread|frewind|frlen|fsep|fuzz|' + 'fwrite|gaminv|gamma|getoption|getvarc|getvarn|hbound|' + 'hms|hosthelp|hour|ibessel|index|indexc|indexw|input|' + 'inputc|inputn|int|intck|intnx|intrr|irr|jbessel|' + 'juldate|kurtosis|lag|lbound|left|length|lgamma|' + 'libname|libref|log|log10|log2|logpdf|logpmf|logsdf|' + 'lowcase|max|mdy|mean|min|minute|mod|month|mopen|' + 'mort|n|netpv|nmiss|normal|note|npv|open|ordinal|' + 'pathname|pdf|peek|peekc|pmf|point|poisson|poke|' + 'probbeta|probbnml|probchi|probf|probgam|probhypr|' + 'probit|probnegb|probnorm|probt|put|putc|putn|qtr|' + 'quote|ranbin|rancau|ranexp|rangam|range|rank|rannor|' + 'ranpoi|rantbl|rantri|ranuni|repeat|resolve|reverse|' + 'rewind|right|round|saving|scan|sdf|second|sign|' + 'sin|sinh|skewness|soundex|spedis|sqrt|std|stderr|' + 'stfips|stname|stnamel|substr|sum|symget|sysget|' + 'sysmsg|sysprod|sysrc|system|tan|tanh|time|timepart|' + 'tinv|tnonct|today|translate|tranwrd|trigamma|' + 'trim|trimn|trunc|uniform|upcase|uss|var|varfmt|' + 'varinfmt|varlabel|varlen|varname|varnum|varray|' + 'varrayx|vartype|verify|vformat|vformatd|vformatdx|' + 'vformatn|vformatnx|vformatw|vformatwx|vformatx|' + 'vinarray|vinarrayx|vinformat|vinformatd|vinformatdx|' + 'vinformatn|vinformatnx|vinformatw|vinformatwx|' + 'vinformatx|vlabel|vlabelx|vlength|vlengthx|vname|' + 'vnamex|vtype|vtypex|weekday|year|yyq|zipfips|zipname|' + 'zipnamel|zipstate';\n\n  // Built-in macro functions\n  const SAS_MACRO_FUN = 'bquote|nrbquote|cmpres|qcmpres|compstor|' + 'datatyp|display|do|else|end|eval|global|goto|' + 'if|index|input|keydef|label|left|length|let|' + 'local|lowcase|macro|mend|nrbquote|nrquote|' + 'nrstr|put|qcmpres|qleft|qlowcase|qscan|' + 'qsubstr|qsysfunc|qtrim|quote|qupcase|scan|str|' + 'substr|superq|syscall|sysevalf|sysexec|sysfunc|' + 'sysget|syslput|sysprod|sysrc|sysrput|then|to|' + 'trim|unquote|until|upcase|verify|while|window';\n  return {\n    name: 'SAS',\n    case_insensitive: true,\n    // SAS is case-insensitive\n    keywords: {\n      literal: 'null missing _all_ _automatic_ _character_ _infile_ ' + '_n_ _name_ _null_ _numeric_ _user_ _webout_',\n      meta: SAS_KEYWORDS\n    },\n    contains: [{\n      // Distinct highlight for proc <proc>, data, run, quit\n      className: 'keyword',\n      begin: /^\\s*(proc [\\w\\d_]+|data|run|quit)[\\s;]/\n    }, {\n      // Macro variables\n      className: 'variable',\n      begin: /&[a-zA-Z_&][a-zA-Z0-9_]*\\.?/\n    }, {\n      // Special emphasis for datalines|cards\n      className: 'emphasis',\n      begin: /^\\s*datalines|cards.*;/,\n      end: /^\\s*;\\s*$/\n    }, {\n      // Built-in macro variables take precedence\n      className: 'built_in',\n      begin: '%(' + SAS_MACRO_FUN + ')'\n    }, {\n      // User-defined macro functions highlighted after\n      className: 'name',\n      begin: /%[a-zA-Z_][a-zA-Z_0-9]*/\n    }, {\n      className: 'meta',\n      begin: '[^%](' + SAS_FUN + ')[\\(]'\n    }, {\n      className: 'string',\n      variants: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n    }, hljs.COMMENT('\\\\*', ';'), hljs.C_BLOCK_COMMENT_MODE]\n  };\n}\nmodule.exports = sas;", "map": {"version": 3, "names": ["sas", "hljs", "SAS_KEYWORDS", "SAS_FUN", "SAS_MACRO_FUN", "name", "case_insensitive", "keywords", "literal", "meta", "contains", "className", "begin", "end", "variants", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "COMMENT", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/sas.js"], "sourcesContent": ["/*\nLanguage: SAS\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Syntax Highlighting for SAS\n*/\n\nfunction sas(hljs) {\n  // Data step and PROC SQL statements\n  const SAS_KEYWORDS =\n    'do if then else end until while ' +\n    '' +\n    'abort array attrib by call cards cards4 catname continue ' +\n    'datalines datalines4 delete delim delimiter display dm drop ' +\n    'endsas error file filename footnote format goto in infile ' +\n    'informat input keep label leave length libname link list ' +\n    'lostcard merge missing modify options output out page put ' +\n    'redirect remove rename replace retain return select set skip ' +\n    'startsas stop title update waitsas where window x systask ' +\n    '' +\n    'add and alter as cascade check create delete describe ' +\n    'distinct drop foreign from group having index insert into in ' +\n    'key like message modify msgtype not null on or order primary ' +\n    'references reset restrict select set table unique update ' +\n    'validate view where';\n\n  // Built-in SAS functions\n  const SAS_FUN =\n    'abs|addr|airy|arcos|arsin|atan|attrc|attrn|band|' +\n    'betainv|blshift|bnot|bor|brshift|bxor|byte|cdf|ceil|' +\n    'cexist|cinv|close|cnonct|collate|compbl|compound|' +\n    'compress|cos|cosh|css|curobs|cv|daccdb|daccdbsl|' +\n    'daccsl|daccsyd|dacctab|dairy|date|datejul|datepart|' +\n    'datetime|day|dclose|depdb|depdbsl|depdbsl|depsl|' +\n    'depsl|depsyd|depsyd|deptab|deptab|dequote|dhms|dif|' +\n    'digamma|dim|dinfo|dnum|dopen|doptname|doptnum|dread|' +\n    'dropnote|dsname|erf|erfc|exist|exp|fappend|fclose|' +\n    'fcol|fdelete|fetch|fetchobs|fexist|fget|fileexist|' +\n    'filename|fileref|finfo|finv|fipname|fipnamel|' +\n    'fipstate|floor|fnonct|fnote|fopen|foptname|foptnum|' +\n    'fpoint|fpos|fput|fread|frewind|frlen|fsep|fuzz|' +\n    'fwrite|gaminv|gamma|getoption|getvarc|getvarn|hbound|' +\n    'hms|hosthelp|hour|ibessel|index|indexc|indexw|input|' +\n    'inputc|inputn|int|intck|intnx|intrr|irr|jbessel|' +\n    'juldate|kurtosis|lag|lbound|left|length|lgamma|' +\n    'libname|libref|log|log10|log2|logpdf|logpmf|logsdf|' +\n    'lowcase|max|mdy|mean|min|minute|mod|month|mopen|' +\n    'mort|n|netpv|nmiss|normal|note|npv|open|ordinal|' +\n    'pathname|pdf|peek|peekc|pmf|point|poisson|poke|' +\n    'probbeta|probbnml|probchi|probf|probgam|probhypr|' +\n    'probit|probnegb|probnorm|probt|put|putc|putn|qtr|' +\n    'quote|ranbin|rancau|ranexp|rangam|range|rank|rannor|' +\n    'ranpoi|rantbl|rantri|ranuni|repeat|resolve|reverse|' +\n    'rewind|right|round|saving|scan|sdf|second|sign|' +\n    'sin|sinh|skewness|soundex|spedis|sqrt|std|stderr|' +\n    'stfips|stname|stnamel|substr|sum|symget|sysget|' +\n    'sysmsg|sysprod|sysrc|system|tan|tanh|time|timepart|' +\n    'tinv|tnonct|today|translate|tranwrd|trigamma|' +\n    'trim|trimn|trunc|uniform|upcase|uss|var|varfmt|' +\n    'varinfmt|varlabel|varlen|varname|varnum|varray|' +\n    'varrayx|vartype|verify|vformat|vformatd|vformatdx|' +\n    'vformatn|vformatnx|vformatw|vformatwx|vformatx|' +\n    'vinarray|vinarrayx|vinformat|vinformatd|vinformatdx|' +\n    'vinformatn|vinformatnx|vinformatw|vinformatwx|' +\n    'vinformatx|vlabel|vlabelx|vlength|vlengthx|vname|' +\n    'vnamex|vtype|vtypex|weekday|year|yyq|zipfips|zipname|' +\n    'zipnamel|zipstate';\n\n  // Built-in macro functions\n  const SAS_MACRO_FUN =\n    'bquote|nrbquote|cmpres|qcmpres|compstor|' +\n    'datatyp|display|do|else|end|eval|global|goto|' +\n    'if|index|input|keydef|label|left|length|let|' +\n    'local|lowcase|macro|mend|nrbquote|nrquote|' +\n    'nrstr|put|qcmpres|qleft|qlowcase|qscan|' +\n    'qsubstr|qsysfunc|qtrim|quote|qupcase|scan|str|' +\n    'substr|superq|syscall|sysevalf|sysexec|sysfunc|' +\n    'sysget|syslput|sysprod|sysrc|sysrput|then|to|' +\n    'trim|unquote|until|upcase|verify|while|window';\n\n  return {\n    name: 'SAS',\n    case_insensitive: true, // SAS is case-insensitive\n    keywords: {\n      literal:\n        'null missing _all_ _automatic_ _character_ _infile_ ' +\n        '_n_ _name_ _null_ _numeric_ _user_ _webout_',\n      meta:\n        SAS_KEYWORDS\n    },\n    contains: [\n      {\n        // Distinct highlight for proc <proc>, data, run, quit\n        className: 'keyword',\n        begin: /^\\s*(proc [\\w\\d_]+|data|run|quit)[\\s;]/\n      },\n      {\n        // Macro variables\n        className: 'variable',\n        begin: /&[a-zA-Z_&][a-zA-Z0-9_]*\\.?/\n      },\n      {\n        // Special emphasis for datalines|cards\n        className: 'emphasis',\n        begin: /^\\s*datalines|cards.*;/,\n        end: /^\\s*;\\s*$/\n      },\n      { // Built-in macro variables take precedence\n        className: 'built_in',\n        begin: '%(' + SAS_MACRO_FUN + ')'\n      },\n      {\n        // User-defined macro functions highlighted after\n        className: 'name',\n        begin: /%[a-zA-Z_][a-zA-Z_0-9]*/\n      },\n      {\n        className: 'meta',\n        begin: '[^%](' + SAS_FUN + ')[\\(]'\n      },\n      {\n        className: 'string',\n        variants: [\n          hljs.APOS_STRING_MODE,\n          hljs.QUOTE_STRING_MODE\n        ]\n      },\n      hljs.COMMENT('\\\\*', ';'),\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = sas;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB;EACA,MAAMC,YAAY,GAChB,kCAAkC,GAClC,EAAE,GACF,2DAA2D,GAC3D,8DAA8D,GAC9D,4DAA4D,GAC5D,2DAA2D,GAC3D,4DAA4D,GAC5D,+DAA+D,GAC/D,4DAA4D,GAC5D,EAAE,GACF,wDAAwD,GACxD,+DAA+D,GAC/D,+DAA+D,GAC/D,2DAA2D,GAC3D,qBAAqB;;EAEvB;EACA,MAAMC,OAAO,GACX,kDAAkD,GAClD,sDAAsD,GACtD,mDAAmD,GACnD,kDAAkD,GAClD,qDAAqD,GACrD,kDAAkD,GAClD,qDAAqD,GACrD,sDAAsD,GACtD,oDAAoD,GACpD,oDAAoD,GACpD,+CAA+C,GAC/C,qDAAqD,GACrD,iDAAiD,GACjD,uDAAuD,GACvD,sDAAsD,GACtD,kDAAkD,GAClD,iDAAiD,GACjD,qDAAqD,GACrD,kDAAkD,GAClD,kDAAkD,GAClD,iDAAiD,GACjD,mDAAmD,GACnD,mDAAmD,GACnD,sDAAsD,GACtD,qDAAqD,GACrD,iDAAiD,GACjD,mDAAmD,GACnD,iDAAiD,GACjD,qDAAqD,GACrD,+CAA+C,GAC/C,iDAAiD,GACjD,iDAAiD,GACjD,oDAAoD,GACpD,iDAAiD,GACjD,sDAAsD,GACtD,gDAAgD,GAChD,mDAAmD,GACnD,uDAAuD,GACvD,mBAAmB;;EAErB;EACA,MAAMC,aAAa,GACjB,0CAA0C,GAC1C,+CAA+C,GAC/C,8CAA8C,GAC9C,4CAA4C,GAC5C,yCAAyC,GACzC,gDAAgD,GAChD,iDAAiD,GACjD,+CAA+C,GAC/C,+CAA+C;EAEjD,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,gBAAgB,EAAE,IAAI;IAAE;IACxBC,QAAQ,EAAE;MACRC,OAAO,EACL,sDAAsD,GACtD,6CAA6C;MAC/CC,IAAI,EACFP;IACJ,CAAC;IACDQ,QAAQ,EAAE,CACR;MACE;MACAC,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAD,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAD,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,wBAAwB;MAC/BC,GAAG,EAAE;IACP,CAAC,EACD;MAAE;MACAF,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,IAAI,GAAGR,aAAa,GAAG;IAChC,CAAC,EACD;MACE;MACAO,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,OAAO,GAAGT,OAAO,GAAG;IAC7B,CAAC,EACD;MACEQ,SAAS,EAAE,QAAQ;MACnBG,QAAQ,EAAE,CACRb,IAAI,CAACc,gBAAgB,EACrBd,IAAI,CAACe,iBAAiB;IAE1B,CAAC,EACDf,IAAI,CAACgB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EACxBhB,IAAI,CAACiB,oBAAoB;EAE7B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGpB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}