{"ast": null, "code": "'use strict';\n\nmodule.exports = autoit;\nautoit.displayName = 'autoit';\nautoit.aliases = [];\nfunction autoit(Prism) {\n  Prism.languages.autoit = {\n    comment: [/;.*/, {\n      // The multi-line comments delimiters can actually be commented out with \";\"\n      pattern: /(^[\\t ]*)#(?:comments-start|cs)[\\s\\S]*?^[ \\t]*#(?:ce|comments-end)/m,\n      lookbehind: true\n    }],\n    url: {\n      pattern: /(^[\\t ]*#include\\s+)(?:<[^\\r\\n>]+>|\"[^\\r\\n\"]+\")/m,\n      lookbehind: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\1\\1|(?!\\1)[^\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        variable: /([%$@])\\w+\\1/\n      }\n    },\n    directive: {\n      pattern: /(^[\\t ]*)#[\\w-]+/m,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    function: /\\b\\w+(?=\\()/,\n    // Variables and macros\n    variable: /[$@]\\w+/,\n    keyword: /\\b(?:Case|Const|Continue(?:Case|Loop)|Default|Dim|Do|Else(?:If)?|End(?:Func|If|Select|Switch|With)|Enum|Exit(?:Loop)?|For|Func|Global|If|In|Local|Next|Null|ReDim|Select|Static|Step|Switch|Then|To|Until|Volatile|WEnd|While|With)\\b/i,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    operator: /<[=>]?|[-+*\\/=&>]=?|[?^]|\\b(?:And|Not|Or)\\b/i,\n    punctuation: /[\\[\\]().,:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "autoit", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "url", "string", "greedy", "inside", "variable", "directive", "alias", "function", "keyword", "number", "boolean", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/autoit.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = autoit\nautoit.displayName = 'autoit'\nautoit.aliases = []\nfunction autoit(Prism) {\n  Prism.languages.autoit = {\n    comment: [\n      /;.*/,\n      {\n        // The multi-line comments delimiters can actually be commented out with \";\"\n        pattern:\n          /(^[\\t ]*)#(?:comments-start|cs)[\\s\\S]*?^[ \\t]*#(?:ce|comments-end)/m,\n        lookbehind: true\n      }\n    ],\n    url: {\n      pattern: /(^[\\t ]*#include\\s+)(?:<[^\\r\\n>]+>|\"[^\\r\\n\"]+\")/m,\n      lookbehind: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\1\\1|(?!\\1)[^\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        variable: /([%$@])\\w+\\1/\n      }\n    },\n    directive: {\n      pattern: /(^[\\t ]*)#[\\w-]+/m,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    function: /\\b\\w+(?=\\()/,\n    // Variables and macros\n    variable: /[$@]\\w+/,\n    keyword:\n      /\\b(?:Case|Const|Continue(?:Case|Loop)|Default|Dim|Do|Else(?:If)?|End(?:Func|If|Select|Switch|With)|Enum|Exit(?:Loop)?|For|Func|Global|If|In|Local|Next|Null|ReDim|Select|Static|Step|Switch|Then|To|Until|Volatile|WEnd|While|With)\\b/i,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    operator: /<[=>]?|[-+*\\/=&>]=?|[?^]|\\b(?:And|Not|Or)\\b/i,\n    punctuation: /[\\[\\]().,:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE,CACP,KAAK,EACL;MACE;MACAC,OAAO,EACL,qEAAqE;MACvEC,UAAU,EAAE;IACd,CAAC,CACF;IACDC,GAAG,EAAE;MACHF,OAAO,EAAE,kDAAkD;MAC3DC,UAAU,EAAE;IACd,CAAC;IACDE,MAAM,EAAE;MACNH,OAAO,EAAE,iCAAiC;MAC1CI,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,SAAS,EAAE;MACTP,OAAO,EAAE,mBAAmB;MAC5BC,UAAU,EAAE,IAAI;MAChBO,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE,aAAa;IACvB;IACAH,QAAQ,EAAE,SAAS;IACnBI,OAAO,EACL,wOAAwO;IAC1OC,MAAM,EAAE,iDAAiD;IACzDC,OAAO,EAAE,qBAAqB;IAC9BC,QAAQ,EAAE,8CAA8C;IACxDC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}