{"ast": null, "code": "/*\nLanguage: Twig\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Twig is a templating language for PHP\nWebsite: https://twig.symfony.com\nCategory: template\n*/\n\nfunction twig(hljs) {\n  var PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n  var FUNCTION_NAMES = 'attribute block constant cycle date dump include ' + 'max min parent random range source template_from_string';\n  var FUNCTIONS = {\n    beginKeywords: FUNCTION_NAMES,\n    keywords: {\n      name: FUNCTION_NAMES\n    },\n    relevance: 0,\n    contains: [PARAMS]\n  };\n  var FILTER = {\n    begin: /\\|[A-Za-z_]+:?/,\n    keywords: 'abs batch capitalize column convert_encoding date date_modify default ' + 'escape filter first format inky_to_html inline_css join json_encode keys last ' + 'length lower map markdown merge nl2br number_format raw reduce replace ' + 'reverse round slice sort spaceless split striptags title trim upper url_encode',\n    contains: [FUNCTIONS]\n  };\n  var TAGS = 'apply autoescape block deprecated do embed extends filter flush for from ' + 'if import include macro sandbox set use verbatim with';\n  TAGS = TAGS + ' ' + TAGS.split(' ').map(function (t) {\n    return 'end' + t;\n  }).join(' ');\n  return {\n    name: 'Twig',\n    aliases: ['craftcms'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [hljs.COMMENT(/\\{#/, /#\\}/), {\n      className: 'template-tag',\n      begin: /\\{%/,\n      end: /%\\}/,\n      contains: [{\n        className: 'name',\n        begin: /\\w+/,\n        keywords: TAGS,\n        starts: {\n          endsWithParent: true,\n          contains: [FILTER, FUNCTIONS],\n          relevance: 0\n        }\n      }]\n    }, {\n      className: 'template-variable',\n      begin: /\\{\\{/,\n      end: /\\}\\}/,\n      contains: ['self', FILTER, FUNCTIONS]\n    }]\n  };\n}\nmodule.exports = twig;", "map": {"version": 3, "names": ["twig", "hljs", "PARAMS", "className", "begin", "end", "FUNCTION_NAMES", "FUNCTIONS", "beginKeywords", "keywords", "name", "relevance", "contains", "FILTER", "TAGS", "split", "map", "t", "join", "aliases", "case_insensitive", "subLanguage", "COMMENT", "starts", "endsWithParent", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/twig.js"], "sourcesContent": ["/*\nLanguage: Twig\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Twig is a templating language for PHP\nWebsite: https://twig.symfony.com\nCategory: template\n*/\n\nfunction twig(hljs) {\n  var PARAMS = {\n    className: 'params',\n    begin: '\\\\(', end: '\\\\)'\n  };\n\n  var FUNCTION_NAMES = 'attribute block constant cycle date dump include ' +\n                  'max min parent random range source template_from_string';\n\n  var FUNCTIONS = {\n    beginKeywords: FUNCTION_NAMES,\n    keywords: {name: FUNCTION_NAMES},\n    relevance: 0,\n    contains: [\n      PARAMS\n    ]\n  };\n\n  var FILTER = {\n    begin: /\\|[A-Za-z_]+:?/,\n    keywords:\n      'abs batch capitalize column convert_encoding date date_modify default ' +\n      'escape filter first format inky_to_html inline_css join json_encode keys last ' +\n      'length lower map markdown merge nl2br number_format raw reduce replace ' +\n      'reverse round slice sort spaceless split striptags title trim upper url_encode',\n    contains: [\n      FUNCTIONS\n    ]\n  };\n\n  var TAGS = 'apply autoescape block deprecated do embed extends filter flush for from ' +\n    'if import include macro sandbox set use verbatim with';\n\n  TAGS = TAGS + ' ' + TAGS.split(' ').map(function(t){return 'end' + t}).join(' ');\n\n  return {\n    name: 'Twig',\n    aliases: ['craftcms'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      hljs.COMMENT(/\\{#/, /#\\}/),\n      {\n        className: 'template-tag',\n        begin: /\\{%/, end: /%\\}/,\n        contains: [\n          {\n            className: 'name',\n            begin: /\\w+/,\n            keywords: TAGS,\n            starts: {\n              endsWithParent: true,\n              contains: [FILTER, FUNCTIONS],\n              relevance: 0\n            }\n          }\n        ]\n      },\n      {\n        className: 'template-variable',\n        begin: /\\{\\{/, end: /\\}\\}/,\n        contains: ['self', FILTER, FUNCTIONS]\n      }\n    ]\n  };\n}\n\nmodule.exports = twig;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,MAAM,GAAG;IACXC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IAAEC,GAAG,EAAE;EACrB,CAAC;EAED,IAAIC,cAAc,GAAG,mDAAmD,GACxD,yDAAyD;EAEzE,IAAIC,SAAS,GAAG;IACdC,aAAa,EAAEF,cAAc;IAC7BG,QAAQ,EAAE;MAACC,IAAI,EAAEJ;IAAc,CAAC;IAChCK,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACRV,MAAM;EAEV,CAAC;EAED,IAAIW,MAAM,GAAG;IACXT,KAAK,EAAE,gBAAgB;IACvBK,QAAQ,EACN,wEAAwE,GACxE,gFAAgF,GAChF,yEAAyE,GACzE,gFAAgF;IAClFG,QAAQ,EAAE,CACRL,SAAS;EAEb,CAAC;EAED,IAAIO,IAAI,GAAG,2EAA2E,GACpF,uDAAuD;EAEzDA,IAAI,GAAGA,IAAI,GAAG,GAAG,GAAGA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAASC,CAAC,EAAC;IAAC,OAAO,KAAK,GAAGA,CAAC;EAAA,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAEhF,OAAO;IACLR,IAAI,EAAE,MAAM;IACZS,OAAO,EAAE,CAAC,UAAU,CAAC;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,WAAW,EAAE,KAAK;IAClBT,QAAQ,EAAE,CACRX,IAAI,CAACqB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAC1B;MACEnB,SAAS,EAAE,cAAc;MACzBC,KAAK,EAAE,KAAK;MAAEC,GAAG,EAAE,KAAK;MACxBO,QAAQ,EAAE,CACR;QACET,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAE,KAAK;QACZK,QAAQ,EAAEK,IAAI;QACdS,MAAM,EAAE;UACNC,cAAc,EAAE,IAAI;UACpBZ,QAAQ,EAAE,CAACC,MAAM,EAAEN,SAAS,CAAC;UAC7BI,SAAS,EAAE;QACb;MACF,CAAC;IAEL,CAAC,EACD;MACER,SAAS,EAAE,mBAAmB;MAC9BC,KAAK,EAAE,MAAM;MAAEC,GAAG,EAAE,MAAM;MAC1BO,QAAQ,EAAE,CAAC,MAAM,EAAEC,MAAM,EAAEN,SAAS;IACtC,CAAC;EAEL,CAAC;AACH;AAEAkB,MAAM,CAACC,OAAO,GAAG1B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}