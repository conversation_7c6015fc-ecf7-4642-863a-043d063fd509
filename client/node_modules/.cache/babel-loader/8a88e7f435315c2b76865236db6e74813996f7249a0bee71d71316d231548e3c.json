{"ast": null, "code": "/*\nLanguage: .properties\nContributors: <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/.properties\nCategory: common, config\n*/\n\nfunction properties(hljs) {\n  // whitespaces: space, tab, formfeed\n  var WS0 = '[ \\\\t\\\\f]*';\n  var WS1 = '[ \\\\t\\\\f]+';\n  // delimiter\n  var EQUAL_DELIM = WS0 + '[:=]' + WS0;\n  var WS_DELIM = WS1;\n  var DELIM = '(' + EQUAL_DELIM + '|' + WS_DELIM + ')';\n  var KEY_ALPHANUM = '([^\\\\\\\\\\\\W:= \\\\t\\\\f\\\\n]|\\\\\\\\.)+';\n  var KEY_OTHER = '([^\\\\\\\\:= \\\\t\\\\f\\\\n]|\\\\\\\\.)+';\n  var DELIM_AND_VALUE = {\n    // skip DELIM\n    end: DELIM,\n    relevance: 0,\n    starts: {\n      // value: everything until end of line (again, taking into account backslashes)\n      className: 'string',\n      end: /$/,\n      relevance: 0,\n      contains: [{\n        begin: '\\\\\\\\\\\\\\\\'\n      }, {\n        begin: '\\\\\\\\\\\\n'\n      }]\n    }\n  };\n  return {\n    name: '.properties',\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [hljs.COMMENT('^\\\\s*[!#]', '$'),\n    // key: everything until whitespace or = or : (taking into account backslashes)\n    // case of a \"normal\" key\n    {\n      returnBegin: true,\n      variants: [{\n        begin: KEY_ALPHANUM + EQUAL_DELIM,\n        relevance: 1\n      }, {\n        begin: KEY_ALPHANUM + WS_DELIM,\n        relevance: 0\n      }],\n      contains: [{\n        className: 'attr',\n        begin: KEY_ALPHANUM,\n        endsParent: true,\n        relevance: 0\n      }],\n      starts: DELIM_AND_VALUE\n    },\n    // case of key containing non-alphanumeric chars => relevance = 0\n    {\n      begin: KEY_OTHER + DELIM,\n      returnBegin: true,\n      relevance: 0,\n      contains: [{\n        className: 'meta',\n        begin: KEY_OTHER,\n        endsParent: true,\n        relevance: 0\n      }],\n      starts: DELIM_AND_VALUE\n    },\n    // case of an empty key\n    {\n      className: 'attr',\n      relevance: 0,\n      begin: KEY_OTHER + WS0 + '$'\n    }]\n  };\n}\nmodule.exports = properties;", "map": {"version": 3, "names": ["properties", "hljs", "WS0", "WS1", "EQUAL_DELIM", "WS_DELIM", "DELIM", "KEY_ALPHANUM", "KEY_OTHER", "DELIM_AND_VALUE", "end", "relevance", "starts", "className", "contains", "begin", "name", "case_insensitive", "illegal", "COMMENT", "returnBegin", "variants", "endsParent", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/properties.js"], "sourcesContent": ["/*\nLanguage: .properties\nContributors: <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/.properties\nCategory: common, config\n*/\n\nfunction properties(hljs) {\n\n  // whitespaces: space, tab, formfeed\n  var WS0 = '[ \\\\t\\\\f]*';\n  var WS1 = '[ \\\\t\\\\f]+';\n  // delimiter\n  var EQUAL_DELIM = WS0+'[:=]'+WS0;\n  var WS_DELIM = WS1;\n  var DELIM = '(' + EQUAL_DELIM + '|' + WS_DELIM + ')';\n  var KEY_ALPHANUM = '([^\\\\\\\\\\\\W:= \\\\t\\\\f\\\\n]|\\\\\\\\.)+';\n  var KEY_OTHER = '([^\\\\\\\\:= \\\\t\\\\f\\\\n]|\\\\\\\\.)+';\n\n  var DELIM_AND_VALUE = {\n          // skip DELIM\n          end: DELIM,\n          relevance: 0,\n          starts: {\n            // value: everything until end of line (again, taking into account backslashes)\n            className: 'string',\n            end: /$/,\n            relevance: 0,\n            contains: [\n              { begin: '\\\\\\\\\\\\\\\\'},\n              { begin: '\\\\\\\\\\\\n' }\n            ]\n          }\n        };\n\n  return {\n    name: '.properties',\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [\n      hljs.COMMENT('^\\\\s*[!#]', '$'),\n      // key: everything until whitespace or = or : (taking into account backslashes)\n      // case of a \"normal\" key\n      {\n        returnBegin: true,\n        variants: [\n          { begin: KEY_ALPHANUM + EQUAL_DELIM, relevance: 1 },\n          { begin: KEY_ALPHANUM + WS_DELIM, relevance: 0 }\n        ],\n        contains: [\n          {\n            className: 'attr',\n            begin: KEY_ALPHANUM,\n            endsParent: true,\n            relevance: 0\n          }\n        ],\n        starts: DELIM_AND_VALUE\n      },\n      // case of key containing non-alphanumeric chars => relevance = 0\n      {\n        begin: KEY_OTHER + DELIM,\n        returnBegin: true,\n        relevance: 0,\n        contains: [\n          {\n            className: 'meta',\n            begin: KEY_OTHER,\n            endsParent: true,\n            relevance: 0\n          }\n        ],\n        starts: DELIM_AND_VALUE\n      },\n      // case of an empty key\n      {\n        className: 'attr',\n        relevance: 0,\n        begin: KEY_OTHER + WS0 + '$'\n      }\n    ]\n  };\n}\n\nmodule.exports = properties;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAUA,CAACC,IAAI,EAAE;EAExB;EACA,IAAIC,GAAG,GAAG,YAAY;EACtB,IAAIC,GAAG,GAAG,YAAY;EACtB;EACA,IAAIC,WAAW,GAAGF,GAAG,GAAC,MAAM,GAACA,GAAG;EAChC,IAAIG,QAAQ,GAAGF,GAAG;EAClB,IAAIG,KAAK,GAAG,GAAG,GAAGF,WAAW,GAAG,GAAG,GAAGC,QAAQ,GAAG,GAAG;EACpD,IAAIE,YAAY,GAAG,iCAAiC;EACpD,IAAIC,SAAS,GAAG,8BAA8B;EAE9C,IAAIC,eAAe,GAAG;IACd;IACAC,GAAG,EAAEJ,KAAK;IACVK,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE;MACN;MACAC,SAAS,EAAE,QAAQ;MACnBH,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE,CAAC;MACZG,QAAQ,EAAE,CACR;QAAEC,KAAK,EAAE;MAAU,CAAC,EACpB;QAAEA,KAAK,EAAE;MAAU,CAAC;IAExB;EACF,CAAC;EAEP,OAAO;IACLC,IAAI,EAAE,aAAa;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,IAAI;IACbJ,QAAQ,EAAE,CACRb,IAAI,CAACkB,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;IAC9B;IACA;IACA;MACEC,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE,CACR;QAAEN,KAAK,EAAER,YAAY,GAAGH,WAAW;QAAEO,SAAS,EAAE;MAAE,CAAC,EACnD;QAAEI,KAAK,EAAER,YAAY,GAAGF,QAAQ;QAAEM,SAAS,EAAE;MAAE,CAAC,CACjD;MACDG,QAAQ,EAAE,CACR;QACED,SAAS,EAAE,MAAM;QACjBE,KAAK,EAAER,YAAY;QACnBe,UAAU,EAAE,IAAI;QAChBX,SAAS,EAAE;MACb,CAAC,CACF;MACDC,MAAM,EAAEH;IACV,CAAC;IACD;IACA;MACEM,KAAK,EAAEP,SAAS,GAAGF,KAAK;MACxBc,WAAW,EAAE,IAAI;MACjBT,SAAS,EAAE,CAAC;MACZG,QAAQ,EAAE,CACR;QACED,SAAS,EAAE,MAAM;QACjBE,KAAK,EAAEP,SAAS;QAChBc,UAAU,EAAE,IAAI;QAChBX,SAAS,EAAE;MACb,CAAC,CACF;MACDC,MAAM,EAAEH;IACV,CAAC;IACD;IACA;MACEI,SAAS,EAAE,MAAM;MACjBF,SAAS,EAAE,CAAC;MACZI,KAAK,EAAEP,SAAS,GAAGN,GAAG,GAAG;IAC3B,CAAC;EAEL,CAAC;AACH;AAEAqB,MAAM,CAACC,OAAO,GAAGxB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}