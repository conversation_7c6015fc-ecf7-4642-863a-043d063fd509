{"ast": null, "code": "'use strict';\n\nvar refractorYaml = require('./yaml.js');\nmodule.exports = tap;\ntap.displayName = 'tap';\ntap.aliases = [];\nfunction tap(Prism) {\n  Prism.register(refractorYaml);\n  // https://en.wikipedia.org/wiki/Test_Anything_Protocol\n  Prism.languages.tap = {\n    fail: /not ok[^#{\\n\\r]*/,\n    pass: /ok[^#{\\n\\r]*/,\n    pragma: /pragma [+-][a-z]+/,\n    bailout: /bail out!.*/i,\n    version: /TAP version \\d+/i,\n    plan: /\\b\\d+\\.\\.\\d+(?: +#.*)?/,\n    subtest: {\n      pattern: /# Subtest(?:: .*)?/,\n      greedy: true\n    },\n    punctuation: /[{}]/,\n    directive: /#.*/,\n    yamlish: {\n      pattern: /(^[ \\t]*)---[\\s\\S]*?[\\r\\n][ \\t]*\\.\\.\\.$/m,\n      lookbehind: true,\n      inside: Prism.languages.yaml,\n      alias: 'language-yaml'\n    }\n  };\n}", "map": {"version": 3, "names": ["refractorYaml", "require", "module", "exports", "tap", "displayName", "aliases", "Prism", "register", "languages", "fail", "pass", "pragma", "bailout", "version", "plan", "subtest", "pattern", "greedy", "punctuation", "directive", "yamlish", "lookbehind", "inside", "yaml", "alias"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/tap.js"], "sourcesContent": ["'use strict'\nvar refractorYaml = require('./yaml.js')\nmodule.exports = tap\ntap.displayName = 'tap'\ntap.aliases = []\nfunction tap(Prism) {\n  Prism.register(refractorYaml)\n  // https://en.wikipedia.org/wiki/Test_Anything_Protocol\n  Prism.languages.tap = {\n    fail: /not ok[^#{\\n\\r]*/,\n    pass: /ok[^#{\\n\\r]*/,\n    pragma: /pragma [+-][a-z]+/,\n    bailout: /bail out!.*/i,\n    version: /TAP version \\d+/i,\n    plan: /\\b\\d+\\.\\.\\d+(?: +#.*)?/,\n    subtest: {\n      pattern: /# Subtest(?:: .*)?/,\n      greedy: true\n    },\n    punctuation: /[{}]/,\n    directive: /#.*/,\n    yamlish: {\n      pattern: /(^[ \\t]*)---[\\s\\S]*?[\\r\\n][ \\t]*\\.\\.\\.$/m,\n      lookbehind: true,\n      inside: Prism.languages.yaml,\n      alias: 'language-yaml'\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxCC,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,QAAQ,CAACR,aAAa,CAAC;EAC7B;EACAO,KAAK,CAACE,SAAS,CAACL,GAAG,GAAG;IACpBM,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,mBAAmB;IAC3BC,OAAO,EAAE,cAAc;IACvBC,OAAO,EAAE,kBAAkB;IAC3BC,IAAI,EAAE,wBAAwB;IAC9BC,OAAO,EAAE;MACPC,OAAO,EAAE,oBAAoB;MAC7BC,MAAM,EAAE;IACV,CAAC;IACDC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE;MACPJ,OAAO,EAAE,0CAA0C;MACnDK,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAEhB,KAAK,CAACE,SAAS,CAACe,IAAI;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}