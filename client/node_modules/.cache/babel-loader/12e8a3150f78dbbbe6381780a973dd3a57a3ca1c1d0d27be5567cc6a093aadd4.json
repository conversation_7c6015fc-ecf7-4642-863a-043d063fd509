{"ast": null, "code": "/*\n Language: N1QL\n Author: <PERSON><PERSON> <<EMAIL>>\n Contributors: <PERSON> <<EMAIL>>\n Description: Couchbase query language\n Website: https://www.couchbase.com/products/n1ql\n */\n\nfunction n1ql(hljs) {\n  return {\n    name: 'N1QL',\n    case_insensitive: true,\n    contains: [{\n      beginKeywords: 'build create index delete drop explain infer|10 insert merge prepare select update upsert|10',\n      end: /;/,\n      endsWithParent: true,\n      keywords: {\n        // Taken from http://developer.couchbase.com/documentation/server/current/n1ql/n1ql-language-reference/reservedwords.html\n        keyword: 'all alter analyze and any array as asc begin between binary boolean break bucket build by call ' + 'case cast cluster collate collection commit connect continue correlate cover create database ' + 'dataset datastore declare decrement delete derived desc describe distinct do drop each element ' + 'else end every except exclude execute exists explain fetch first flatten for force from ' + 'function grant group gsi having if ignore ilike in include increment index infer inline inner ' + 'insert intersect into is join key keys keyspace known last left let letting like limit lsm map ' + 'mapping matched materialized merge minus namespace nest not number object offset on ' + 'option or order outer over parse partition password path pool prepare primary private privilege ' + 'procedure public raw realm reduce rename return returning revoke right role rollback satisfies ' + 'schema select self semi set show some start statistics string system then to transaction trigger ' + 'truncate under union unique unknown unnest unset update upsert use user using validate value ' + 'valued values via view when where while with within work xor',\n        // Taken from http://developer.couchbase.com/documentation/server/4.5/n1ql/n1ql-language-reference/literals.html\n        literal: 'true false null missing|5',\n        // Taken from http://developer.couchbase.com/documentation/server/4.5/n1ql/n1ql-language-reference/functions.html\n        built_in: 'array_agg array_append array_concat array_contains array_count array_distinct array_ifnull array_length ' + 'array_max array_min array_position array_prepend array_put array_range array_remove array_repeat array_replace ' + 'array_reverse array_sort array_sum avg count max min sum greatest least ifmissing ifmissingornull ifnull ' + 'missingif nullif ifinf ifnan ifnanorinf naninf neginfif posinfif clock_millis clock_str date_add_millis ' + 'date_add_str date_diff_millis date_diff_str date_part_millis date_part_str date_trunc_millis date_trunc_str ' + 'duration_to_str millis str_to_millis millis_to_str millis_to_utc millis_to_zone_name now_millis now_str ' + 'str_to_duration str_to_utc str_to_zone_name decode_json encode_json encoded_size poly_length base64 base64_encode ' + 'base64_decode meta uuid abs acos asin atan atan2 ceil cos degrees e exp ln log floor pi power radians random ' + 'round sign sin sqrt tan trunc object_length object_names object_pairs object_inner_pairs object_values ' + 'object_inner_values object_add object_put object_remove object_unwrap regexp_contains regexp_like regexp_position ' + 'regexp_replace contains initcap length lower ltrim position repeat replace rtrim split substr title trim upper ' + 'isarray isatom isboolean isnumber isobject isstring type toarray toatom toboolean tonumber toobject tostring'\n      },\n      contains: [{\n        className: 'string',\n        begin: '\\'',\n        end: '\\'',\n        contains: [hljs.BACKSLASH_ESCAPE]\n      }, {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        contains: [hljs.BACKSLASH_ESCAPE]\n      }, {\n        className: 'symbol',\n        begin: '`',\n        end: '`',\n        contains: [hljs.BACKSLASH_ESCAPE],\n        relevance: 2\n      }, hljs.C_NUMBER_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, hljs.C_BLOCK_COMMENT_MODE]\n  };\n}\nmodule.exports = n1ql;", "map": {"version": 3, "names": ["n1ql", "hljs", "name", "case_insensitive", "contains", "beginKeywords", "end", "endsWithParent", "keywords", "keyword", "literal", "built_in", "className", "begin", "BACKSLASH_ESCAPE", "relevance", "C_NUMBER_MODE", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/n1ql.js"], "sourcesContent": ["/*\n Language: N1QL\n Author: <PERSON><PERSON> <<EMAIL>>\n Contributors: <PERSON> <<EMAIL>>\n Description: Couchbase query language\n Website: https://www.couchbase.com/products/n1ql\n */\n\nfunction n1ql(hljs) {\n  return {\n    name: 'N1QL',\n    case_insensitive: true,\n    contains: [\n      {\n        beginKeywords:\n          'build create index delete drop explain infer|10 insert merge prepare select update upsert|10',\n        end: /;/, endsWithParent: true,\n        keywords: {\n          // Taken from http://developer.couchbase.com/documentation/server/current/n1ql/n1ql-language-reference/reservedwords.html\n          keyword:\n            'all alter analyze and any array as asc begin between binary boolean break bucket build by call ' +\n            'case cast cluster collate collection commit connect continue correlate cover create database ' +\n            'dataset datastore declare decrement delete derived desc describe distinct do drop each element ' +\n            'else end every except exclude execute exists explain fetch first flatten for force from ' +\n            'function grant group gsi having if ignore ilike in include increment index infer inline inner ' +\n            'insert intersect into is join key keys keyspace known last left let letting like limit lsm map ' +\n            'mapping matched materialized merge minus namespace nest not number object offset on ' +\n            'option or order outer over parse partition password path pool prepare primary private privilege ' +\n            'procedure public raw realm reduce rename return returning revoke right role rollback satisfies ' +\n            'schema select self semi set show some start statistics string system then to transaction trigger ' +\n            'truncate under union unique unknown unnest unset update upsert use user using validate value ' +\n            'valued values via view when where while with within work xor',\n          // Taken from http://developer.couchbase.com/documentation/server/4.5/n1ql/n1ql-language-reference/literals.html\n          literal:\n            'true false null missing|5',\n          // Taken from http://developer.couchbase.com/documentation/server/4.5/n1ql/n1ql-language-reference/functions.html\n          built_in:\n            'array_agg array_append array_concat array_contains array_count array_distinct array_ifnull array_length ' +\n            'array_max array_min array_position array_prepend array_put array_range array_remove array_repeat array_replace ' +\n            'array_reverse array_sort array_sum avg count max min sum greatest least ifmissing ifmissingornull ifnull ' +\n            'missingif nullif ifinf ifnan ifnanorinf naninf neginfif posinfif clock_millis clock_str date_add_millis ' +\n            'date_add_str date_diff_millis date_diff_str date_part_millis date_part_str date_trunc_millis date_trunc_str ' +\n            'duration_to_str millis str_to_millis millis_to_str millis_to_utc millis_to_zone_name now_millis now_str ' +\n            'str_to_duration str_to_utc str_to_zone_name decode_json encode_json encoded_size poly_length base64 base64_encode ' +\n            'base64_decode meta uuid abs acos asin atan atan2 ceil cos degrees e exp ln log floor pi power radians random ' +\n            'round sign sin sqrt tan trunc object_length object_names object_pairs object_inner_pairs object_values ' +\n            'object_inner_values object_add object_put object_remove object_unwrap regexp_contains regexp_like regexp_position ' +\n            'regexp_replace contains initcap length lower ltrim position repeat replace rtrim split substr title trim upper ' +\n            'isarray isatom isboolean isnumber isobject isstring type toarray toatom toboolean tonumber toobject tostring'\n        },\n        contains: [\n          {\n            className: 'string',\n            begin: '\\'', end: '\\'',\n            contains: [hljs.BACKSLASH_ESCAPE]\n          },\n          {\n            className: 'string',\n            begin: '\"', end: '\"',\n            contains: [hljs.BACKSLASH_ESCAPE]\n          },\n          {\n            className: 'symbol',\n            begin: '`', end: '`',\n            contains: [hljs.BACKSLASH_ESCAPE],\n            relevance: 2\n          },\n          hljs.C_NUMBER_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = n1ql;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,CACR;MACEC,aAAa,EACX,8FAA8F;MAChGC,GAAG,EAAE,GAAG;MAAEC,cAAc,EAAE,IAAI;MAC9BC,QAAQ,EAAE;QACR;QACAC,OAAO,EACL,iGAAiG,GACjG,+FAA+F,GAC/F,iGAAiG,GACjG,0FAA0F,GAC1F,gGAAgG,GAChG,iGAAiG,GACjG,sFAAsF,GACtF,kGAAkG,GAClG,iGAAiG,GACjG,mGAAmG,GACnG,+FAA+F,GAC/F,8DAA8D;QAChE;QACAC,OAAO,EACL,2BAA2B;QAC7B;QACAC,QAAQ,EACN,0GAA0G,GAC1G,iHAAiH,GACjH,2GAA2G,GAC3G,0GAA0G,GAC1G,8GAA8G,GAC9G,0GAA0G,GAC1G,oHAAoH,GACpH,+GAA+G,GAC/G,yGAAyG,GACzG,oHAAoH,GACpH,iHAAiH,GACjH;MACJ,CAAC;MACDP,QAAQ,EAAE,CACR;QACEQ,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,IAAI;QAAEP,GAAG,EAAE,IAAI;QACtBF,QAAQ,EAAE,CAACH,IAAI,CAACa,gBAAgB;MAClC,CAAC,EACD;QACEF,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,GAAG;QAAEP,GAAG,EAAE,GAAG;QACpBF,QAAQ,EAAE,CAACH,IAAI,CAACa,gBAAgB;MAClC,CAAC,EACD;QACEF,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE,GAAG;QAAEP,GAAG,EAAE,GAAG;QACpBF,QAAQ,EAAE,CAACH,IAAI,CAACa,gBAAgB,CAAC;QACjCC,SAAS,EAAE;MACb,CAAC,EACDd,IAAI,CAACe,aAAa,EAClBf,IAAI,CAACgB,oBAAoB;IAE7B,CAAC,EACDhB,IAAI,CAACgB,oBAAoB;EAE7B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGnB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}