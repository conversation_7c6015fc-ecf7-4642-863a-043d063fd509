{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: AspectJ\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.eclipse.org/aspectj/\nDescription: Syntax Highlighting for the AspectJ Language which is a general-purpose aspect-oriented extension to the Java programming language.\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction aspectj(hljs) {\n  const KEYWORDS = 'false synchronized int abstract float private char boolean static null if const ' + 'for true while long throw strictfp finally protected import native final return void ' + 'enum else extends implements break transient new catch instanceof byte super volatile case ' + 'assert short package default double public try this switch continue throws privileged ' + 'aspectOf adviceexecution proceed cflowbelow cflow initialization preinitialization ' + 'staticinitialization withincode target within execution getWithinTypeName handler ' + 'thisJoinPoint thisJoinPointStaticPart thisEnclosingJoinPointStaticPart declare parents ' + 'warning error soft precedence thisAspectInstance';\n  const SHORTKEYS = 'get set args call';\n  return {\n    name: 'AspectJ',\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [hljs.COMMENT(/\\/\\*\\*/, /\\*\\//, {\n      relevance: 0,\n      contains: [{\n        // eat up @'s in emails to prevent them to be recognized as doctags\n        begin: /\\w+@/,\n        relevance: 0\n      }, {\n        className: 'doctag',\n        begin: /@[A-Za-z]+/\n      }]\n    }), hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, {\n      className: 'class',\n      beginKeywords: 'aspect',\n      end: /[{;=]/,\n      excludeEnd: true,\n      illegal: /[:;\"\\[\\]]/,\n      contains: [{\n        beginKeywords: 'extends implements pertypewithin perthis pertarget percflowbelow percflow issingleton'\n      }, hljs.UNDERSCORE_TITLE_MODE, {\n        begin: /\\([^\\)]*/,\n        end: /[)]+/,\n        keywords: KEYWORDS + ' ' + SHORTKEYS,\n        excludeEnd: false\n      }]\n    }, {\n      className: 'class',\n      beginKeywords: 'class interface',\n      end: /[{;=]/,\n      excludeEnd: true,\n      relevance: 0,\n      keywords: 'class interface',\n      illegal: /[:\"\\[\\]]/,\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      // AspectJ Constructs\n      beginKeywords: 'pointcut after before around throwing returning',\n      end: /[)]/,\n      excludeEnd: false,\n      illegal: /[\"\\[\\]]/,\n      contains: [{\n        begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n        returnBegin: true,\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      }]\n    }, {\n      begin: /[:]/,\n      returnBegin: true,\n      end: /[{;]/,\n      relevance: 0,\n      excludeEnd: false,\n      keywords: KEYWORDS,\n      illegal: /[\"\\[\\]]/,\n      contains: [{\n        begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n        keywords: KEYWORDS + ' ' + SHORTKEYS,\n        relevance: 0\n      }, hljs.QUOTE_STRING_MODE]\n    }, {\n      // this prevents 'new Name(...), or throw ...' from being recognized as a function definition\n      beginKeywords: 'new throw',\n      relevance: 0\n    }, {\n      // the function class is a bit different for AspectJ compared to the Java language\n      className: 'function',\n      begin: /\\w+ +\\w+(\\.\\w+)?\\s*\\([^\\)]*\\)\\s*((throws)[\\w\\s,]+)?[\\{;]/,\n      returnBegin: true,\n      end: /[{;=]/,\n      keywords: KEYWORDS,\n      excludeEnd: true,\n      contains: [{\n        begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n        returnBegin: true,\n        relevance: 0,\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      }, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        relevance: 0,\n        keywords: KEYWORDS,\n        contains: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE, hljs.C_BLOCK_COMMENT_MODE]\n      }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, hljs.C_NUMBER_MODE, {\n      // annotation is also used in this language\n      className: 'meta',\n      begin: /@[A-Za-z]+/\n    }]\n  };\n}\nmodule.exports = aspectj;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "<PERSON>j", "hljs", "KEYWORDS", "SHORTKEYS", "name", "keywords", "illegal", "contains", "COMMENT", "relevance", "begin", "className", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "beginKeywords", "end", "excludeEnd", "UNDERSCORE_TITLE_MODE", "UNDERSCORE_IDENT_RE", "returnBegin", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/aspectj.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: AspectJ\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.eclipse.org/aspectj/\nDescription: Syntax Highlighting for the AspectJ Language which is a general-purpose aspect-oriented extension to the Java programming language.\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction aspectj(hljs) {\n  const KEYWORDS =\n    'false synchronized int abstract float private char boolean static null if const ' +\n    'for true while long throw strictfp finally protected import native final return void ' +\n    'enum else extends implements break transient new catch instanceof byte super volatile case ' +\n    'assert short package default double public try this switch continue throws privileged ' +\n    'aspectOf adviceexecution proceed cflowbelow cflow initialization preinitialization ' +\n    'staticinitialization withincode target within execution getWithinTypeName handler ' +\n    'thisJoinPoint thisJoinPointStaticPart thisEnclosingJoinPointStaticPart declare parents ' +\n    'warning error soft precedence thisAspectInstance';\n  const SHORTKEYS = 'get set args call';\n\n  return {\n    name: 'AspectJ',\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        /\\/\\*\\*/,\n        /\\*\\//,\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/,\n              relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: /@[A-Za-z]+/\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'aspect',\n        end: /[{;=]/,\n        excludeEnd: true,\n        illegal: /[:;\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends implements pertypewithin perthis pertarget percflowbelow percflow issingleton'\n          },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: /\\([^\\)]*/,\n            end: /[)]+/,\n            keywords: KEYWORDS + ' ' + SHORTKEYS,\n            excludeEnd: false\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /[{;=]/,\n        excludeEnd: true,\n        relevance: 0,\n        keywords: 'class interface',\n        illegal: /[:\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        // AspectJ Constructs\n        beginKeywords: 'pointcut after before around throwing returning',\n        end: /[)]/,\n        excludeEnd: false,\n        illegal: /[\"\\[\\]]/,\n        contains: [\n          {\n            begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n            returnBegin: true,\n            contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n          }\n        ]\n      },\n      {\n        begin: /[:]/,\n        returnBegin: true,\n        end: /[{;]/,\n        relevance: 0,\n        excludeEnd: false,\n        keywords: KEYWORDS,\n        illegal: /[\"\\[\\]]/,\n        contains: [\n          {\n            begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n            keywords: KEYWORDS + ' ' + SHORTKEYS,\n            relevance: 0\n          },\n          hljs.QUOTE_STRING_MODE\n        ]\n      },\n      {\n        // this prevents 'new Name(...), or throw ...' from being recognized as a function definition\n        beginKeywords: 'new throw',\n        relevance: 0\n      },\n      {\n        // the function class is a bit different for AspectJ compared to the Java language\n        className: 'function',\n        begin: /\\w+ +\\w+(\\.\\w+)?\\s*\\([^\\)]*\\)\\s*((throws)[\\w\\s,]+)?[\\{;]/,\n        returnBegin: true,\n        end: /[{;=]/,\n        keywords: KEYWORDS,\n        excludeEnd: true,\n        contains: [\n          {\n            begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n            returnBegin: true,\n            relevance: 0,\n            contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            relevance: 0,\n            keywords: KEYWORDS,\n            contains: [\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              hljs.C_NUMBER_MODE,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      hljs.C_NUMBER_MODE,\n      {\n        // annotation is also used in this language\n        className: 'meta',\n        begin: /@[A-Za-z]+/\n      }\n    ]\n  };\n}\n\nmodule.exports = aspectj;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,QAAQ,GACZ,kFAAkF,GAClF,uFAAuF,GACvF,6FAA6F,GAC7F,wFAAwF,GACxF,qFAAqF,GACrF,oFAAoF,GACpF,yFAAyF,GACzF,kDAAkD;EACpD,MAAMC,SAAS,GAAG,mBAAmB;EAErC,OAAO;IACLC,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAEH,QAAQ;IAClBI,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE,CACRN,IAAI,CAACO,OAAO,CACV,QAAQ,EACR,MAAM,EACN;MACEC,SAAS,EAAE,CAAC;MACZF,QAAQ,EAAE,CACR;QACE;QACAG,KAAK,EAAE,MAAM;QACbD,SAAS,EAAE;MACb,CAAC,EACD;QACEE,SAAS,EAAE,QAAQ;QACnBD,KAAK,EAAE;MACT,CAAC;IAEL,CACF,CAAC,EACDT,IAAI,CAACW,mBAAmB,EACxBX,IAAI,CAACY,oBAAoB,EACzBZ,IAAI,CAACa,gBAAgB,EACrBb,IAAI,CAACc,iBAAiB,EACtB;MACEJ,SAAS,EAAE,OAAO;MAClBK,aAAa,EAAE,QAAQ;MACvBC,GAAG,EAAE,OAAO;MACZC,UAAU,EAAE,IAAI;MAChBZ,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,CACR;QACES,aAAa,EAAE;MACjB,CAAC,EACDf,IAAI,CAACkB,qBAAqB,EAC1B;QACET,KAAK,EAAE,UAAU;QACjBO,GAAG,EAAE,MAAM;QACXZ,QAAQ,EAAEH,QAAQ,GAAG,GAAG,GAAGC,SAAS;QACpCe,UAAU,EAAE;MACd,CAAC;IAEL,CAAC,EACD;MACEP,SAAS,EAAE,OAAO;MAClBK,aAAa,EAAE,iBAAiB;MAChCC,GAAG,EAAE,OAAO;MACZC,UAAU,EAAE,IAAI;MAChBT,SAAS,EAAE,CAAC;MACZJ,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,CACR;QACES,aAAa,EAAE;MACjB,CAAC,EACDf,IAAI,CAACkB,qBAAqB;IAE9B,CAAC,EACD;MACE;MACAH,aAAa,EAAE,iDAAiD;MAChEC,GAAG,EAAE,KAAK;MACVC,UAAU,EAAE,KAAK;MACjBZ,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE,CACR;QACEG,KAAK,EAAEhB,MAAM,CAACO,IAAI,CAACmB,mBAAmB,EAAE,OAAO,CAAC;QAChDC,WAAW,EAAE,IAAI;QACjBd,QAAQ,EAAE,CAAEN,IAAI,CAACkB,qBAAqB;MACxC,CAAC;IAEL,CAAC,EACD;MACET,KAAK,EAAE,KAAK;MACZW,WAAW,EAAE,IAAI;MACjBJ,GAAG,EAAE,MAAM;MACXR,SAAS,EAAE,CAAC;MACZS,UAAU,EAAE,KAAK;MACjBb,QAAQ,EAAEH,QAAQ;MAClBI,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE,CACR;QACEG,KAAK,EAAEhB,MAAM,CAACO,IAAI,CAACmB,mBAAmB,EAAE,OAAO,CAAC;QAChDf,QAAQ,EAAEH,QAAQ,GAAG,GAAG,GAAGC,SAAS;QACpCM,SAAS,EAAE;MACb,CAAC,EACDR,IAAI,CAACc,iBAAiB;IAE1B,CAAC,EACD;MACE;MACAC,aAAa,EAAE,WAAW;MAC1BP,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACAE,SAAS,EAAE,UAAU;MACrBD,KAAK,EAAE,0DAA0D;MACjEW,WAAW,EAAE,IAAI;MACjBJ,GAAG,EAAE,OAAO;MACZZ,QAAQ,EAAEH,QAAQ;MAClBgB,UAAU,EAAE,IAAI;MAChBX,QAAQ,EAAE,CACR;QACEG,KAAK,EAAEhB,MAAM,CAACO,IAAI,CAACmB,mBAAmB,EAAE,OAAO,CAAC;QAChDC,WAAW,EAAE,IAAI;QACjBZ,SAAS,EAAE,CAAC;QACZF,QAAQ,EAAE,CAAEN,IAAI,CAACkB,qBAAqB;MACxC,CAAC,EACD;QACER,SAAS,EAAE,QAAQ;QACnBD,KAAK,EAAE,IAAI;QACXO,GAAG,EAAE,IAAI;QACTR,SAAS,EAAE,CAAC;QACZJ,QAAQ,EAAEH,QAAQ;QAClBK,QAAQ,EAAE,CACRN,IAAI,CAACa,gBAAgB,EACrBb,IAAI,CAACc,iBAAiB,EACtBd,IAAI,CAACqB,aAAa,EAClBrB,IAAI,CAACY,oBAAoB;MAE7B,CAAC,EACDZ,IAAI,CAACW,mBAAmB,EACxBX,IAAI,CAACY,oBAAoB;IAE7B,CAAC,EACDZ,IAAI,CAACqB,aAAa,EAClB;MACE;MACAX,SAAS,EAAE,MAAM;MACjBD,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;AACH;AAEAa,MAAM,CAACC,OAAO,GAAGxB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}