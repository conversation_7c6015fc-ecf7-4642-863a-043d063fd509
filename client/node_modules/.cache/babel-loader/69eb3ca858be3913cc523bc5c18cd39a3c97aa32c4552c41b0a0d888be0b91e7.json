{"ast": null, "code": "'use strict';\n\nmodule.exports = hcl;\nhcl.displayName = 'hcl';\nhcl.aliases = [];\nfunction hcl(Prism) {\n  Prism.languages.hcl = {\n    comment: /(?:\\/\\/|#).*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    heredoc: {\n      pattern: /<<-?(\\w+\\b)[\\s\\S]*?^[ \\t]*\\1/m,\n      greedy: true,\n      alias: 'string'\n    },\n    keyword: [{\n      pattern: /(?:data|resource)\\s+(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")(?=\\s+\"[\\w-]+\"\\s+\\{)/i,\n      inside: {\n        type: {\n          pattern: /(resource|data|\\s+)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")/i,\n          lookbehind: true,\n          alias: 'variable'\n        }\n      }\n    }, {\n      pattern: /(?:backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+(?=\\{)/i,\n      inside: {\n        type: {\n          pattern: /(backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+/i,\n          lookbehind: true,\n          alias: 'variable'\n        }\n      }\n    }, /[\\w-]+(?=\\s+\\{)/],\n    property: [/[-\\w\\.]+(?=\\s*=(?!=))/, /\"(?:\\\\[\\s\\S]|[^\\\\\"])+\"(?=\\s*[:=])/],\n    string: {\n      pattern: /\"(?:[^\\\\$\"]|\\\\[\\s\\S]|\\$(?:(?=\")|\\$+(?!\\$)|[^\"${])|\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\})*\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^$])\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\}/,\n          lookbehind: true,\n          inside: {\n            type: {\n              pattern: /(\\b(?:count|data|local|module|path|self|terraform|var)\\b\\.)[\\w\\*]+/i,\n              lookbehind: true,\n              alias: 'variable'\n            },\n            keyword: /\\b(?:count|data|local|module|path|self|terraform|var)\\b/i,\n            function: /\\w+(?=\\()/,\n            string: {\n              pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n              greedy: true\n            },\n            number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n            punctuation: /[!\\$#%&'()*+,.\\/;<=>@\\[\\\\\\]^`{|}~?:]/\n          }\n        }\n      }\n    },\n    number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    punctuation: /[=\\[\\]{}]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "hcl", "displayName", "aliases", "Prism", "languages", "comment", "heredoc", "pattern", "greedy", "alias", "keyword", "inside", "type", "lookbehind", "property", "string", "interpolation", "function", "number", "punctuation", "boolean"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/hcl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = hcl\nhcl.displayName = 'hcl'\nhcl.aliases = []\nfunction hcl(Prism) {\n  Prism.languages.hcl = {\n    comment: /(?:\\/\\/|#).*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    heredoc: {\n      pattern: /<<-?(\\w+\\b)[\\s\\S]*?^[ \\t]*\\1/m,\n      greedy: true,\n      alias: 'string'\n    },\n    keyword: [\n      {\n        pattern:\n          /(?:data|resource)\\s+(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")(?=\\s+\"[\\w-]+\"\\s+\\{)/i,\n        inside: {\n          type: {\n            pattern: /(resource|data|\\s+)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")/i,\n            lookbehind: true,\n            alias: 'variable'\n          }\n        }\n      },\n      {\n        pattern:\n          /(?:backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+(?=\\{)/i,\n        inside: {\n          type: {\n            pattern:\n              /(backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+/i,\n            lookbehind: true,\n            alias: 'variable'\n          }\n        }\n      },\n      /[\\w-]+(?=\\s+\\{)/\n    ],\n    property: [/[-\\w\\.]+(?=\\s*=(?!=))/, /\"(?:\\\\[\\s\\S]|[^\\\\\"])+\"(?=\\s*[:=])/],\n    string: {\n      pattern:\n        /\"(?:[^\\\\$\"]|\\\\[\\s\\S]|\\$(?:(?=\")|\\$+(?!\\$)|[^\"${])|\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\})*\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^$])\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\}/,\n          lookbehind: true,\n          inside: {\n            type: {\n              pattern:\n                /(\\b(?:count|data|local|module|path|self|terraform|var)\\b\\.)[\\w\\*]+/i,\n              lookbehind: true,\n              alias: 'variable'\n            },\n            keyword: /\\b(?:count|data|local|module|path|self|terraform|var)\\b/i,\n            function: /\\w+(?=\\()/,\n            string: {\n              pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n              greedy: true\n            },\n            number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n            punctuation: /[!\\$#%&'()*+,.\\/;<=>@\\[\\\\\\]^`{|}~?:]/\n          }\n        }\n      }\n    },\n    number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    punctuation: /[=\\[\\]{}]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE,qCAAqC;IAC9CC,OAAO,EAAE;MACPC,OAAO,EAAE,+BAA+B;MACxCC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE,CACP;MACEH,OAAO,EACL,qEAAqE;MACvEI,MAAM,EAAE;QACNC,IAAI,EAAE;UACJL,OAAO,EAAE,gDAAgD;UACzDM,UAAU,EAAE,IAAI;UAChBJ,KAAK,EAAE;QACT;MACF;IACF,CAAC,EACD;MACEF,OAAO,EACL,uGAAuG;MACzGI,MAAM,EAAE;QACNC,IAAI,EAAE;UACJL,OAAO,EACL,+FAA+F;UACjGM,UAAU,EAAE,IAAI;UAChBJ,KAAK,EAAE;QACT;MACF;IACF,CAAC,EACD,iBAAiB,CAClB;IACDK,QAAQ,EAAE,CAAC,uBAAuB,EAAE,mCAAmC,CAAC;IACxEC,MAAM,EAAE;MACNR,OAAO,EACL,+FAA+F;MACjGC,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE;QACNK,aAAa,EAAE;UACbT,OAAO,EAAE,kDAAkD;UAC3DM,UAAU,EAAE,IAAI;UAChBF,MAAM,EAAE;YACNC,IAAI,EAAE;cACJL,OAAO,EACL,qEAAqE;cACvEM,UAAU,EAAE,IAAI;cAChBJ,KAAK,EAAE;YACT,CAAC;YACDC,OAAO,EAAE,0DAA0D;YACnEO,QAAQ,EAAE,WAAW;YACrBF,MAAM,EAAE;cACNR,OAAO,EAAE,wBAAwB;cACjCC,MAAM,EAAE;YACV,CAAC;YACDU,MAAM,EAAE,+CAA+C;YACvDC,WAAW,EAAE;UACf;QACF;MACF;IACF,CAAC;IACDD,MAAM,EAAE,+CAA+C;IACvDE,OAAO,EAAE,qBAAqB;IAC9BD,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}