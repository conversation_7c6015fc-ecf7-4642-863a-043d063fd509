{"ast": null, "code": "/*\nLanguage: Vala\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Vala is a new programming language that aims to bring modern programming language features to GNOME developers without imposing any additional runtime requirements and without using a different ABI compared to applications and libraries written in C.\nWebsite: https://wiki.gnome.org/Projects/Vala\n*/\n\nfunction vala(hljs) {\n  return {\n    name: 'Vala',\n    keywords: {\n      keyword:\n      // Value types\n      'char uchar unichar int uint long ulong short ushort int8 int16 int32 int64 uint8 ' + 'uint16 uint32 uint64 float double bool struct enum string void ' +\n      // Reference types\n      'weak unowned owned ' +\n      // Modifiers\n      'async signal static abstract interface override virtual delegate ' +\n      // Control Structures\n      'if while do for foreach else switch case break default return try catch ' +\n      // Visibility\n      'public private protected internal ' +\n      // Other\n      'using new this get set const stdout stdin stderr var',\n      built_in: 'DBus GLib CCode Gee Object Gtk Posix',\n      literal: 'false true null'\n    },\n    contains: [{\n      className: 'class',\n      beginKeywords: 'class interface namespace',\n      end: /\\{/,\n      excludeEnd: true,\n      illegal: '[^,:\\\\n\\\\s\\\\.]',\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'string',\n      begin: '\"\"\"',\n      end: '\"\"\"',\n      relevance: 5\n    }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE, {\n      className: 'meta',\n      begin: '^#',\n      end: '$',\n      relevance: 2\n    }]\n  };\n}\nmodule.exports = vala;", "map": {"version": 3, "names": ["vala", "hljs", "name", "keywords", "keyword", "built_in", "literal", "contains", "className", "beginKeywords", "end", "excludeEnd", "illegal", "UNDERSCORE_TITLE_MODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "begin", "relevance", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/vala.js"], "sourcesContent": ["/*\nLanguage: Vala\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Vala is a new programming language that aims to bring modern programming language features to GNOME developers without imposing any additional runtime requirements and without using a different ABI compared to applications and libraries written in C.\nWebsite: https://wiki.gnome.org/Projects/Vala\n*/\n\nfunction vala(hljs) {\n  return {\n    name: 'Vala',\n    keywords: {\n      keyword:\n        // Value types\n        'char uchar unichar int uint long ulong short ushort int8 int16 int32 int64 uint8 ' +\n        'uint16 uint32 uint64 float double bool struct enum string void ' +\n        // Reference types\n        'weak unowned owned ' +\n        // Modifiers\n        'async signal static abstract interface override virtual delegate ' +\n        // Control Structures\n        'if while do for foreach else switch case break default return try catch ' +\n        // Visibility\n        'public private protected internal ' +\n        // Other\n        'using new this get set const stdout stdin stderr var',\n      built_in:\n        'DBus GLib CCode Gee Object Gtk Posix',\n      literal:\n        'false true null'\n    },\n    contains: [\n      {\n        className: 'class',\n        beginKeywords: 'class interface namespace',\n        end: /\\{/,\n        excludeEnd: true,\n        illegal: '[^,:\\\\n\\\\s\\\\.]',\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'string',\n        begin: '\"\"\"',\n        end: '\"\"\"',\n        relevance: 5\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '^#',\n        end: '$',\n        relevance: 2\n      }\n    ]\n  };\n}\n\nmodule.exports = vala;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;MACRC,OAAO;MACL;MACA,mFAAmF,GACnF,iEAAiE;MACjE;MACA,qBAAqB;MACrB;MACA,mEAAmE;MACnE;MACA,0EAA0E;MAC1E;MACA,oCAAoC;MACpC;MACA,sDAAsD;MACxDC,QAAQ,EACN,sCAAsC;MACxCC,OAAO,EACL;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,OAAO;MAClBC,aAAa,EAAE,2BAA2B;MAC1CC,GAAG,EAAE,IAAI;MACTC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,gBAAgB;MACzBL,QAAQ,EAAE,CAAEN,IAAI,CAACY,qBAAqB;IACxC,CAAC,EACDZ,IAAI,CAACa,mBAAmB,EACxBb,IAAI,CAACc,oBAAoB,EACzB;MACEP,SAAS,EAAE,QAAQ;MACnBQ,KAAK,EAAE,KAAK;MACZN,GAAG,EAAE,KAAK;MACVO,SAAS,EAAE;IACb,CAAC,EACDhB,IAAI,CAACiB,gBAAgB,EACrBjB,IAAI,CAACkB,iBAAiB,EACtBlB,IAAI,CAACmB,aAAa,EAClB;MACEZ,SAAS,EAAE,MAAM;MACjBQ,KAAK,EAAE,IAAI;MACXN,GAAG,EAAE,GAAG;MACRO,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAI,MAAM,CAACC,OAAO,GAAGtB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}