{"ast": null, "code": "/*\nLanguage: Elm\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://elm-lang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction elm(hljs) {\n  const COMMENT = {\n    variants: [hljs.COMMENT('--', '$'), hljs.COMMENT(/\\{-/, /-\\}/, {\n      contains: ['self']\n    })]\n  };\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*',\n    // TODO: other constructors (built-in, infix).\n    relevance: 0\n  };\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [{\n      className: 'type',\n      begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n    }, COMMENT]\n  };\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n  const CHARACTER = {\n    className: 'string',\n    begin: '\\'\\\\\\\\?.',\n    end: '\\'',\n    illegal: '.'\n  };\n  return {\n    name: 'Elm',\n    keywords: 'let in if then else case of where module import exposing ' + 'type alias as infix infixl infixr port effect command subscription',\n    contains: [\n    // Top-level constructions.\n\n    {\n      beginKeywords: 'port effect module',\n      end: 'exposing',\n      keywords: 'port effect module where command subscription exposing',\n      contains: [LIST, COMMENT],\n      illegal: '\\\\W\\\\.|;'\n    }, {\n      begin: 'import',\n      end: '$',\n      keywords: 'import as exposing',\n      contains: [LIST, COMMENT],\n      illegal: '\\\\W\\\\.|;'\n    }, {\n      begin: 'type',\n      end: '$',\n      keywords: 'type alias',\n      contains: [CONSTRUCTOR, LIST, RECORD, COMMENT]\n    }, {\n      beginKeywords: 'infix infixl infixr',\n      end: '$',\n      contains: [hljs.C_NUMBER_MODE, COMMENT]\n    }, {\n      begin: 'port',\n      end: '$',\n      keywords: 'port',\n      contains: [COMMENT]\n    },\n    // Literals and names.\n\n    CHARACTER, hljs.QUOTE_STRING_MODE, hljs.C_NUMBER_MODE, CONSTRUCTOR, hljs.inherit(hljs.TITLE_MODE, {\n      begin: '^[_a-z][\\\\w\\']*'\n    }), COMMENT, {\n      begin: '->|<-'\n    } // No markup, relevance booster\n    ],\n    illegal: /;/\n  };\n}\nmodule.exports = elm;", "map": {"version": 3, "names": ["elm", "hljs", "COMMENT", "variants", "contains", "CONSTRUCTOR", "className", "begin", "relevance", "LIST", "end", "illegal", "RECORD", "CHARACTER", "name", "keywords", "beginKeywords", "C_NUMBER_MODE", "QUOTE_STRING_MODE", "inherit", "TITLE_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/elm.js"], "sourcesContent": ["/*\nLanguage: Elm\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://elm-lang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction elm(hljs) {\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT('--', '$'),\n      hljs.COMMENT(\n        /\\{-/,\n        /-\\}/,\n        {\n          contains: ['self']\n        }\n      )\n    ]\n  };\n\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*', // TODO: other constructors (built-in, infix).\n    relevance: 0\n  };\n\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n      },\n      COMMENT\n    ]\n  };\n\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n\n  const CHARACTER = {\n    className: 'string',\n    begin: '\\'\\\\\\\\?.',\n    end: '\\'',\n    illegal: '.'\n  };\n\n  return {\n    name: 'Elm',\n    keywords:\n      'let in if then else case of where module import exposing ' +\n      'type alias as infix infixl infixr port effect command subscription',\n    contains: [\n\n      // Top-level constructions.\n\n      {\n        beginKeywords: 'port effect module',\n        end: 'exposing',\n        keywords: 'port effect module where command subscription exposing',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: 'import',\n        end: '$',\n        keywords: 'import as exposing',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: 'type',\n        end: '$',\n        keywords: 'type alias',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          RECORD,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'infix infixl infixr',\n        end: '$',\n        contains: [\n          hljs.C_NUMBER_MODE,\n          COMMENT\n        ]\n      },\n      {\n        begin: 'port',\n        end: '$',\n        keywords: 'port',\n        contains: [COMMENT]\n      },\n\n      // Literals and names.\n\n      CHARACTER,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      CONSTRUCTOR,\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '^[_a-z][\\\\w\\']*'\n      }),\n      COMMENT,\n\n      {\n        begin: '->|<-'\n      } // No markup, relevance booster\n    ],\n    illegal: /;/\n  };\n}\n\nmodule.exports = elm;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,OAAO,GAAG;IACdC,QAAQ,EAAE,CACRF,IAAI,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EACvBD,IAAI,CAACC,OAAO,CACV,KAAK,EACL,KAAK,EACL;MACEE,QAAQ,EAAE,CAAC,MAAM;IACnB,CACF,CAAC;EAEL,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,kBAAkB;IAAE;IAC3BC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,IAAI,GAAG;IACXF,KAAK,EAAE,KAAK;IACZG,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE,GAAG;IACZP,QAAQ,EAAE,CACR;MACEE,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC,EACDL,OAAO;EAEX,CAAC;EAED,MAAMU,MAAM,GAAG;IACbL,KAAK,EAAE,IAAI;IACXG,GAAG,EAAE,IAAI;IACTN,QAAQ,EAAEK,IAAI,CAACL;EACjB,CAAC;EAED,MAAMS,SAAS,GAAG;IAChBP,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,UAAU;IACjBG,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE;EACX,CAAC;EAED,OAAO;IACLG,IAAI,EAAE,KAAK;IACXC,QAAQ,EACN,2DAA2D,GAC3D,oEAAoE;IACtEX,QAAQ,EAAE;IAER;;IAEA;MACEY,aAAa,EAAE,oBAAoB;MACnCN,GAAG,EAAE,UAAU;MACfK,QAAQ,EAAE,wDAAwD;MAClEX,QAAQ,EAAE,CACRK,IAAI,EACJP,OAAO,CACR;MACDS,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,KAAK,EAAE,QAAQ;MACfG,GAAG,EAAE,GAAG;MACRK,QAAQ,EAAE,oBAAoB;MAC9BX,QAAQ,EAAE,CACRK,IAAI,EACJP,OAAO,CACR;MACDS,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,GAAG;MACRK,QAAQ,EAAE,YAAY;MACtBX,QAAQ,EAAE,CACRC,WAAW,EACXI,IAAI,EACJG,MAAM,EACNV,OAAO;IAEX,CAAC,EACD;MACEc,aAAa,EAAE,qBAAqB;MACpCN,GAAG,EAAE,GAAG;MACRN,QAAQ,EAAE,CACRH,IAAI,CAACgB,aAAa,EAClBf,OAAO;IAEX,CAAC,EACD;MACEK,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,GAAG;MACRK,QAAQ,EAAE,MAAM;MAChBX,QAAQ,EAAE,CAACF,OAAO;IACpB,CAAC;IAED;;IAEAW,SAAS,EACTZ,IAAI,CAACiB,iBAAiB,EACtBjB,IAAI,CAACgB,aAAa,EAClBZ,WAAW,EACXJ,IAAI,CAACkB,OAAO,CAAClB,IAAI,CAACmB,UAAU,EAAE;MAC5Bb,KAAK,EAAE;IACT,CAAC,CAAC,EACFL,OAAO,EAEP;MACEK,KAAK,EAAE;IACT,CAAC,CAAC;IAAA,CACH;IACDI,OAAO,EAAE;EACX,CAAC;AACH;AAEAU,MAAM,CAACC,OAAO,GAAGtB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}