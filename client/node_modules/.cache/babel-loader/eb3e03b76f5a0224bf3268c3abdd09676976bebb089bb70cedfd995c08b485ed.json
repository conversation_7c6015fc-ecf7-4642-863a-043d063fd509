{"ast": null, "code": "/*\nLanguage: Delphi\nWebsite: https://www.embarcadero.com/products/delphi\n*/\n\n/** @type LanguageFn */\nfunction delphi(hljs) {\n  const KEYWORDS = 'exports register file shl array record property for mod while set ally label uses raise not ' + 'stored class safecall var interface or private static exit index inherited to else stdcall ' + 'override shr asm far resourcestring finalization packed virtual out and protected library do ' + 'xorwrite goto near function end div overload object unit begin string on inline repeat until ' + 'destructor write message program with read initialization except default nil if case cdecl in ' + 'downto threadvar of try pascal const external constructor type public then implementation ' + 'finally published procedure absolute reintroduce operator as is abstract alias assembler ' + 'bitpacked break continue cppdecl cvar enumerator experimental platform deprecated ' + 'unimplemented dynamic export far16 forward generic helper implements interrupt iochecks ' + 'local name nodefault noreturn nostackframe oldfpccall otherwise saveregisters softfloat ' + 'specialize strict unaligned varargs ';\n  const COMMENT_MODES = [hljs.C_LINE_COMMENT_MODE, hljs.COMMENT(/\\{/, /\\}/, {\n    relevance: 0\n  }), hljs.COMMENT(/\\(\\*/, /\\*\\)/, {\n    relevance: 10\n  })];\n  const DIRECTIVE = {\n    className: 'meta',\n    variants: [{\n      begin: /\\{\\$/,\n      end: /\\}/\n    }, {\n      begin: /\\(\\*\\$/,\n      end: /\\*\\)/\n    }]\n  };\n  const STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/,\n    contains: [{\n      begin: /''/\n    }]\n  };\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    // Source: https://www.freepascal.org/docs-html/ref/refse6.html\n    variants: [{\n      // Hexadecimal notation, e.g., $7F.\n      begin: '\\\\$[0-9A-Fa-f]+'\n    }, {\n      // Octal notation, e.g., &42.\n      begin: '&[0-7]+'\n    }, {\n      // Binary notation, e.g., %1010.\n      begin: '%[01]+'\n    }]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: /(#\\d+)+/\n  };\n  const CLASS = {\n    begin: hljs.IDENT_RE + '\\\\s*=\\\\s*class\\\\s*\\\\(',\n    returnBegin: true,\n    contains: [hljs.TITLE_MODE]\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'function constructor destructor procedure',\n    end: /[:;]/,\n    keywords: 'function constructor|10 destructor|10 procedure|10',\n    contains: [hljs.TITLE_MODE, {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS,\n      contains: [STRING, CHAR_STRING, DIRECTIVE].concat(COMMENT_MODES)\n    }, DIRECTIVE].concat(COMMENT_MODES)\n  };\n  return {\n    name: 'Delphi',\n    aliases: ['dpr', 'dfm', 'pas', 'pascal', 'freepascal', 'lazarus', 'lpr', 'lfm'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    illegal: /\"|\\$[G-Zg-z]|\\/\\*|<\\/|\\|/,\n    contains: [STRING, CHAR_STRING, hljs.NUMBER_MODE, NUMBER, CLASS, FUNCTION, DIRECTIVE].concat(COMMENT_MODES)\n  };\n}\nmodule.exports = delphi;", "map": {"version": 3, "names": ["delphi", "hljs", "KEYWORDS", "COMMENT_MODES", "C_LINE_COMMENT_MODE", "COMMENT", "relevance", "DIRECTIVE", "className", "variants", "begin", "end", "STRING", "contains", "NUMBER", "CHAR_STRING", "CLASS", "IDENT_RE", "returnBegin", "TITLE_MODE", "FUNCTION", "beginKeywords", "keywords", "concat", "name", "aliases", "case_insensitive", "illegal", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/delphi.js"], "sourcesContent": ["/*\nLanguage: Delphi\nWebsite: https://www.embarcadero.com/products/delphi\n*/\n\n/** @type LanguageFn */\nfunction delphi(hljs) {\n  const KEYWORDS =\n    'exports register file shl array record property for mod while set ally label uses raise not ' +\n    'stored class safecall var interface or private static exit index inherited to else stdcall ' +\n    'override shr asm far resourcestring finalization packed virtual out and protected library do ' +\n    'xorwrite goto near function end div overload object unit begin string on inline repeat until ' +\n    'destructor write message program with read initialization except default nil if case cdecl in ' +\n    'downto threadvar of try pascal const external constructor type public then implementation ' +\n    'finally published procedure absolute reintroduce operator as is abstract alias assembler ' +\n    'bitpacked break continue cppdecl cvar enumerator experimental platform deprecated ' +\n    'unimplemented dynamic export far16 forward generic helper implements interrupt iochecks ' +\n    'local name nodefault noreturn nostackframe oldfpccall otherwise saveregisters softfloat ' +\n    'specialize strict unaligned varargs ';\n  const COMMENT_MODES = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.COMMENT(/\\{/, /\\}/, {\n      relevance: 0\n    }),\n    hljs.COMMENT(/\\(\\*/, /\\*\\)/, {\n      relevance: 10\n    })\n  ];\n  const DIRECTIVE = {\n    className: 'meta',\n    variants: [\n      {\n        begin: /\\{\\$/,\n        end: /\\}/\n      },\n      {\n        begin: /\\(\\*\\$/,\n        end: /\\*\\)/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/,\n    contains: [{\n      begin: /''/\n    }]\n  };\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    // Source: https://www.freepascal.org/docs-html/ref/refse6.html\n    variants: [\n      {\n        // Hexadecimal notation, e.g., $7F.\n        begin: '\\\\$[0-9A-Fa-f]+'\n      },\n      {\n        // Octal notation, e.g., &42.\n        begin: '&[0-7]+'\n      },\n      {\n        // Binary notation, e.g., %1010.\n        begin: '%[01]+'\n      }\n    ]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: /(#\\d+)+/\n  };\n  const CLASS = {\n    begin: hljs.IDENT_RE + '\\\\s*=\\\\s*class\\\\s*\\\\(',\n    returnBegin: true,\n    contains: [hljs.TITLE_MODE]\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'function constructor destructor procedure',\n    end: /[:;]/,\n    keywords: 'function constructor|10 destructor|10 procedure|10',\n    contains: [\n      hljs.TITLE_MODE,\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING,\n          DIRECTIVE\n        ].concat(COMMENT_MODES)\n      },\n      DIRECTIVE\n    ].concat(COMMENT_MODES)\n  };\n  return {\n    name: 'Delphi',\n    aliases: [\n      'dpr',\n      'dfm',\n      'pas',\n      'pascal',\n      'freepascal',\n      'lazarus',\n      'lpr',\n      'lfm'\n    ],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    illegal: /\"|\\$[G-Zg-z]|\\/\\*|<\\/|\\|/,\n    contains: [\n      STRING,\n      CHAR_STRING,\n      hljs.NUMBER_MODE,\n      NUMBER,\n      CLASS,\n      FUNCTION,\n      DIRECTIVE\n    ].concat(COMMENT_MODES)\n  };\n}\n\nmodule.exports = delphi;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,QAAQ,GACZ,8FAA8F,GAC9F,6FAA6F,GAC7F,+FAA+F,GAC/F,+FAA+F,GAC/F,gGAAgG,GAChG,4FAA4F,GAC5F,2FAA2F,GAC3F,oFAAoF,GACpF,0FAA0F,GAC1F,0FAA0F,GAC1F,sCAAsC;EACxC,MAAMC,aAAa,GAAG,CACpBF,IAAI,CAACG,mBAAmB,EACxBH,IAAI,CAACI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC,EACFL,IAAI,CAACI,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE;IAC3BC,SAAS,EAAE;EACb,CAAC,CAAC,CACH;EACD,MAAMC,SAAS,GAAG;IAChBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,QAAQ;MACfC,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EACD,MAAMC,MAAM,GAAG;IACbJ,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRE,QAAQ,EAAE,CAAC;MACTH,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,MAAMI,MAAM,GAAG;IACbN,SAAS,EAAE,QAAQ;IACnBF,SAAS,EAAE,CAAC;IACZ;IACAG,QAAQ,EAAE,CACR;MACE;MACAC,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACAA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,MAAMK,WAAW,GAAG;IAClBP,SAAS,EAAE,QAAQ;IACnBE,KAAK,EAAE;EACT,CAAC;EACD,MAAMM,KAAK,GAAG;IACZN,KAAK,EAAET,IAAI,CAACgB,QAAQ,GAAG,uBAAuB;IAC9CC,WAAW,EAAE,IAAI;IACjBL,QAAQ,EAAE,CAACZ,IAAI,CAACkB,UAAU;EAC5B,CAAC;EACD,MAAMC,QAAQ,GAAG;IACfZ,SAAS,EAAE,UAAU;IACrBa,aAAa,EAAE,2CAA2C;IAC1DV,GAAG,EAAE,MAAM;IACXW,QAAQ,EAAE,oDAAoD;IAC9DT,QAAQ,EAAE,CACRZ,IAAI,CAACkB,UAAU,EACf;MACEX,SAAS,EAAE,QAAQ;MACnBE,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTW,QAAQ,EAAEpB,QAAQ;MAClBW,QAAQ,EAAE,CACRD,MAAM,EACNG,WAAW,EACXR,SAAS,CACV,CAACgB,MAAM,CAACpB,aAAa;IACxB,CAAC,EACDI,SAAS,CACV,CAACgB,MAAM,CAACpB,aAAa;EACxB,CAAC;EACD,OAAO;IACLqB,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,KAAK,EACL,KAAK,CACN;IACDC,gBAAgB,EAAE,IAAI;IACtBJ,QAAQ,EAAEpB,QAAQ;IAClByB,OAAO,EAAE,0BAA0B;IACnCd,QAAQ,EAAE,CACRD,MAAM,EACNG,WAAW,EACXd,IAAI,CAAC2B,WAAW,EAChBd,MAAM,EACNE,KAAK,EACLI,QAAQ,EACRb,SAAS,CACV,CAACgB,MAAM,CAACpB,aAAa;EACxB,CAAC;AACH;AAEA0B,MAAM,CAACC,OAAO,GAAG9B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}