{"ast": null, "code": "'use strict';\n\nmodule.exports = pascal;\npascal.displayName = 'pascal';\npascal.aliases = ['objectpascal'];\nfunction pascal(Prism) {\n  // Based on Free Pascal\n  /* TODO\n  Support inline asm ?\n  */\n  Prism.languages.pascal = {\n    directive: {\n      pattern: /\\{\\$[\\s\\S]*?\\}/,\n      greedy: true,\n      alias: ['marco', 'property']\n    },\n    comment: {\n      pattern: /\\(\\*[\\s\\S]*?\\*\\)|\\{[\\s\\S]*?\\}|\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /(?:'(?:''|[^'\\r\\n])*'(?!')|#[&$%]?[a-f\\d]+)+|\\^[a-z]/i,\n      greedy: true\n    },\n    asm: {\n      pattern: /(\\basm\\b)[\\s\\S]+?(?=\\bend\\s*[;[])/i,\n      lookbehind: true,\n      greedy: true,\n      inside: null // see below\n    },\n    keyword: [{\n      // Turbo Pascal\n      pattern: /(^|[^&])\\b(?:absolute|array|asm|begin|case|const|constructor|destructor|do|downto|else|end|file|for|function|goto|if|implementation|inherited|inline|interface|label|nil|object|of|operator|packed|procedure|program|record|reintroduce|repeat|self|set|string|then|to|type|unit|until|uses|var|while|with)\\b/i,\n      lookbehind: true\n    }, {\n      // Free Pascal\n      pattern: /(^|[^&])\\b(?:dispose|exit|false|new|true)\\b/i,\n      lookbehind: true\n    }, {\n      // Object Pascal\n      pattern: /(^|[^&])\\b(?:class|dispinterface|except|exports|finalization|finally|initialization|inline|library|on|out|packed|property|raise|resourcestring|threadvar|try)\\b/i,\n      lookbehind: true\n    }, {\n      // Modifiers\n      pattern: /(^|[^&])\\b(?:absolute|abstract|alias|assembler|bitpacked|break|cdecl|continue|cppdecl|cvar|default|deprecated|dynamic|enumerator|experimental|export|external|far|far16|forward|generic|helper|implements|index|interrupt|iochecks|local|message|name|near|nodefault|noreturn|nostackframe|oldfpccall|otherwise|overload|override|pascal|platform|private|protected|public|published|read|register|reintroduce|result|safecall|saveregisters|softfloat|specialize|static|stdcall|stored|strict|unaligned|unimplemented|varargs|virtual|write)\\b/i,\n      lookbehind: true\n    }],\n    number: [\n    // Hexadecimal, octal and binary\n    /(?:[&%]\\d+|\\$[a-f\\d]+)/i,\n    // Decimal\n    /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?/i],\n    operator: [/\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=]/, {\n      pattern: /(^|[^&])\\b(?:and|as|div|exclude|in|include|is|mod|not|or|shl|shr|xor)\\b/,\n      lookbehind: true\n    }],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/\n  };\n  Prism.languages.pascal.asm.inside = Prism.languages.extend('pascal', {\n    asm: undefined,\n    keyword: undefined,\n    operator: undefined\n  });\n  Prism.languages.objectpascal = Prism.languages.pascal;\n}", "map": {"version": 3, "names": ["module", "exports", "pascal", "displayName", "aliases", "Prism", "languages", "directive", "pattern", "greedy", "alias", "comment", "string", "asm", "lookbehind", "inside", "keyword", "number", "operator", "punctuation", "extend", "undefined", "objectpascal"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/pascal.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pascal\npascal.displayName = 'pascal'\npascal.aliases = ['objectpascal']\nfunction pascal(Prism) {\n  // Based on Free Pascal\n  /* TODO\nSupport inline asm ?\n*/\n  Prism.languages.pascal = {\n    directive: {\n      pattern: /\\{\\$[\\s\\S]*?\\}/,\n      greedy: true,\n      alias: ['marco', 'property']\n    },\n    comment: {\n      pattern: /\\(\\*[\\s\\S]*?\\*\\)|\\{[\\s\\S]*?\\}|\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /(?:'(?:''|[^'\\r\\n])*'(?!')|#[&$%]?[a-f\\d]+)+|\\^[a-z]/i,\n      greedy: true\n    },\n    asm: {\n      pattern: /(\\basm\\b)[\\s\\S]+?(?=\\bend\\s*[;[])/i,\n      lookbehind: true,\n      greedy: true,\n      inside: null // see below\n    },\n    keyword: [\n      {\n        // Turbo Pascal\n        pattern:\n          /(^|[^&])\\b(?:absolute|array|asm|begin|case|const|constructor|destructor|do|downto|else|end|file|for|function|goto|if|implementation|inherited|inline|interface|label|nil|object|of|operator|packed|procedure|program|record|reintroduce|repeat|self|set|string|then|to|type|unit|until|uses|var|while|with)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Free Pascal\n        pattern: /(^|[^&])\\b(?:dispose|exit|false|new|true)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Object Pascal\n        pattern:\n          /(^|[^&])\\b(?:class|dispinterface|except|exports|finalization|finally|initialization|inline|library|on|out|packed|property|raise|resourcestring|threadvar|try)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Modifiers\n        pattern:\n          /(^|[^&])\\b(?:absolute|abstract|alias|assembler|bitpacked|break|cdecl|continue|cppdecl|cvar|default|deprecated|dynamic|enumerator|experimental|export|external|far|far16|forward|generic|helper|implements|index|interrupt|iochecks|local|message|name|near|nodefault|noreturn|nostackframe|oldfpccall|otherwise|overload|override|pascal|platform|private|protected|public|published|read|register|reintroduce|result|safecall|saveregisters|softfloat|specialize|static|stdcall|stored|strict|unaligned|unimplemented|varargs|virtual|write)\\b/i,\n        lookbehind: true\n      }\n    ],\n    number: [\n      // Hexadecimal, octal and binary\n      /(?:[&%]\\d+|\\$[a-f\\d]+)/i, // Decimal\n      /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?/i\n    ],\n    operator: [\n      /\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=]/,\n      {\n        pattern:\n          /(^|[^&])\\b(?:and|as|div|exclude|in|include|is|mod|not|or|shl|shr|xor)\\b/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/\n  }\n  Prism.languages.pascal.asm.inside = Prism.languages.extend('pascal', {\n    asm: undefined,\n    keyword: undefined,\n    operator: undefined\n  })\n  Prism.languages.objectpascal = Prism.languages.pascal\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,cAAc,CAAC;AACjC,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EACA;AACF;AACA;EACEA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,SAAS,EAAE;MACTC,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU;IAC7B,CAAC;IACDC,OAAO,EAAE;MACPH,OAAO,EAAE,sCAAsC;MAC/CC,MAAM,EAAE;IACV,CAAC;IACDG,MAAM,EAAE;MACNJ,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE;IACV,CAAC;IACDI,GAAG,EAAE;MACHL,OAAO,EAAE,oCAAoC;MAC7CM,UAAU,EAAE,IAAI;MAChBL,MAAM,EAAE,IAAI;MACZM,MAAM,EAAE,IAAI,CAAC;IACf,CAAC;IACDC,OAAO,EAAE,CACP;MACE;MACAR,OAAO,EACL,gTAAgT;MAClTM,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAN,OAAO,EAAE,8CAA8C;MACvDM,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAN,OAAO,EACL,kKAAkK;MACpKM,UAAU,EAAE;IACd,CAAC,EACD;MACE;MACAN,OAAO,EACL,khBAAkhB;MACphBM,UAAU,EAAE;IACd,CAAC,CACF;IACDG,MAAM,EAAE;IACN;IACA,yBAAyB;IAAE;IAC3B,gCAAgC,CACjC;IACDC,QAAQ,EAAE,CACR,8CAA8C,EAC9C;MACEV,OAAO,EACL,yEAAyE;MAC3EM,UAAU,EAAE;IACd,CAAC,CACF;IACDK,WAAW,EAAE;EACf,CAAC;EACDd,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACW,GAAG,CAACE,MAAM,GAAGV,KAAK,CAACC,SAAS,CAACc,MAAM,CAAC,QAAQ,EAAE;IACnEP,GAAG,EAAEQ,SAAS;IACdL,OAAO,EAAEK,SAAS;IAClBH,QAAQ,EAAEG;EACZ,CAAC,CAAC;EACFhB,KAAK,CAACC,SAAS,CAACgB,YAAY,GAAGjB,KAAK,CAACC,SAAS,CAACJ,MAAM;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}