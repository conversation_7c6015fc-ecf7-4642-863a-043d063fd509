{"ast": null, "code": "'use strict';\n\nmodule.exports = yang;\nyang.displayName = 'yang';\nyang.aliases = [];\nfunction yang(Prism) {\n  Prism.languages.yang = {\n    // https://tools.ietf.org/html/rfc6020#page-34\n    // http://www.yang-central.org/twiki/bin/view/Main/YangExamples\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*/,\n    string: {\n      pattern: /\"(?:[^\\\\\"]|\\\\.)*\"|'[^']*'/,\n      greedy: true\n    },\n    keyword: {\n      pattern: /(^|[{};\\r\\n][ \\t]*)[a-z_][\\w.-]*/i,\n      lookbehind: true\n    },\n    namespace: {\n      pattern: /(\\s)[a-z_][\\w.-]*(?=:)/i,\n      lookbehind: true\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /\\+/,\n    punctuation: /[{};:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "yang", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "keyword", "lookbehind", "namespace", "boolean", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/yang.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = yang\nyang.displayName = 'yang'\nyang.aliases = []\nfunction yang(Prism) {\n  Prism.languages.yang = {\n    // https://tools.ietf.org/html/rfc6020#page-34\n    // http://www.yang-central.org/twiki/bin/view/Main/YangExamples\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*/,\n    string: {\n      pattern: /\"(?:[^\\\\\"]|\\\\.)*\"|'[^']*'/,\n      greedy: true\n    },\n    keyword: {\n      pattern: /(^|[{};\\r\\n][ \\t]*)[a-z_][\\w.-]*/i,\n      lookbehind: true\n    },\n    namespace: {\n      pattern: /(\\s)[a-z_][\\w.-]*(?=:)/i,\n      lookbehind: true\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /\\+/,\n    punctuation: /[{};:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAG;IACrB;IACA;IACAK,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;MACNC,OAAO,EAAE,2BAA2B;MACpCC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPF,OAAO,EAAE,mCAAmC;MAC5CG,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE;MACTJ,OAAO,EAAE,yBAAyB;MAClCG,UAAU,EAAE;IACd,CAAC;IACDE,OAAO,EAAE,oBAAoB;IAC7BC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}