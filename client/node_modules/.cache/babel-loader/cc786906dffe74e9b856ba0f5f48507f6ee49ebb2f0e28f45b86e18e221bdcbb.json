{"ast": null, "code": "'use strict';\n\nmodule.exports = ignore;\nignore.displayName = 'ignore';\nignore.aliases = ['gitignore', 'hgignore', 'npmignore'];\nfunction ignore(Prism) {\n  ;\n  (function (Prism) {\n    Prism.languages.ignore = {\n      // https://git-scm.com/docs/gitignore\n      comment: /^#.*/m,\n      entry: {\n        pattern: /\\S(?:.*(?:(?:\\\\ )|\\S))?/,\n        alias: 'string',\n        inside: {\n          operator: /^!|\\*\\*?|\\?/,\n          regex: {\n            pattern: /(^|[^\\\\])\\[[^\\[\\]]*\\]/,\n            lookbehind: true\n          },\n          punctuation: /\\//\n        }\n      }\n    };\n    Prism.languages.gitignore = Prism.languages.ignore;\n    Prism.languages.hgignore = Prism.languages.ignore;\n    Prism.languages.npmignore = Prism.languages.ignore;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "ignore", "displayName", "aliases", "Prism", "languages", "comment", "entry", "pattern", "alias", "inside", "operator", "regex", "lookbehind", "punctuation", "gitignore", "hgignore", "npmignore"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/ignore.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ignore\nignore.displayName = 'ignore'\nignore.aliases = ['gitignore', 'hgignore', 'npmignore']\nfunction ignore(Prism) {\n  ;(function (Prism) {\n    Prism.languages.ignore = {\n      // https://git-scm.com/docs/gitignore\n      comment: /^#.*/m,\n      entry: {\n        pattern: /\\S(?:.*(?:(?:\\\\ )|\\S))?/,\n        alias: 'string',\n        inside: {\n          operator: /^!|\\*\\*?|\\?/,\n          regex: {\n            pattern: /(^|[^\\\\])\\[[^\\[\\]]*\\]/,\n            lookbehind: true\n          },\n          punctuation: /\\//\n        }\n      }\n    }\n    Prism.languages.gitignore = Prism.languages.ignore\n    Prism.languages.hgignore = Prism.languages.ignore\n    Prism.languages.npmignore = Prism.languages.ignore\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC;AACvD,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;MACvB;MACAK,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE;QACLC,OAAO,EAAE,yBAAyB;QAClCC,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE;UACNC,QAAQ,EAAE,aAAa;UACvBC,KAAK,EAAE;YACLJ,OAAO,EAAE,uBAAuB;YAChCK,UAAU,EAAE;UACd,CAAC;UACDC,WAAW,EAAE;QACf;MACF;IACF,CAAC;IACDV,KAAK,CAACC,SAAS,CAACU,SAAS,GAAGX,KAAK,CAACC,SAAS,CAACJ,MAAM;IAClDG,KAAK,CAACC,SAAS,CAACW,QAAQ,GAAGZ,KAAK,CAACC,SAAS,CAACJ,MAAM;IACjDG,KAAK,CAACC,SAAS,CAACY,SAAS,GAAGb,KAAK,CAACC,SAAS,CAACJ,MAAM;EACpD,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}