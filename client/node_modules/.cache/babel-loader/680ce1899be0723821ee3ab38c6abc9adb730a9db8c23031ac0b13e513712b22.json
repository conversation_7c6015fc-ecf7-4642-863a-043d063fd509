{"ast": null, "code": "/*\nLanguage: CMake\nDescription: CMake is an open-source cross-platform system for build automation.\nAuthor: <PERSON> <i<PERSON>@kalnitsky.org>\nWebsite: https://cmake.org\n*/\n\n/** @type LanguageFn */\nfunction cmake(hljs) {\n  return {\n    name: 'CMake',\n    aliases: ['cmake.in'],\n    case_insensitive: true,\n    keywords: {\n      keyword:\n      // scripting commands\n      'break cmake_host_system_information cmake_minimum_required cmake_parse_arguments ' + 'cmake_policy configure_file continue elseif else endforeach endfunction endif endmacro ' + 'endwhile execute_process file find_file find_library find_package find_path ' + 'find_program foreach function get_cmake_property get_directory_property ' + 'get_filename_component get_property if include include_guard list macro ' + 'mark_as_advanced math message option return separate_arguments ' + 'set_directory_properties set_property set site_name string unset variable_watch while ' +\n      // project commands\n      'add_compile_definitions add_compile_options add_custom_command add_custom_target ' + 'add_definitions add_dependencies add_executable add_library add_link_options ' + 'add_subdirectory add_test aux_source_directory build_command create_test_sourcelist ' + 'define_property enable_language enable_testing export fltk_wrap_ui ' + 'get_source_file_property get_target_property get_test_property include_directories ' + 'include_external_msproject include_regular_expression install link_directories ' + 'link_libraries load_cache project qt_wrap_cpp qt_wrap_ui remove_definitions ' + 'set_source_files_properties set_target_properties set_tests_properties source_group ' + 'target_compile_definitions target_compile_features target_compile_options ' + 'target_include_directories target_link_directories target_link_libraries ' + 'target_link_options target_sources try_compile try_run ' +\n      // CTest commands\n      'ctest_build ctest_configure ctest_coverage ctest_empty_binary_directory ctest_memcheck ' + 'ctest_read_custom_files ctest_run_script ctest_sleep ctest_start ctest_submit ' + 'ctest_test ctest_update ctest_upload ' +\n      // deprecated commands\n      'build_name exec_program export_library_dependencies install_files install_programs ' + 'install_targets load_command make_directory output_required_files remove ' + 'subdir_depends subdirs use_mangled_mesa utility_source variable_requires write_file ' + 'qt5_use_modules qt5_use_package qt5_wrap_cpp ' +\n      // core keywords\n      'on off true false and or not command policy target test exists is_newer_than ' + 'is_directory is_symlink is_absolute matches less greater equal less_equal ' + 'greater_equal strless strgreater strequal strless_equal strgreater_equal version_less ' + 'version_greater version_equal version_less_equal version_greater_equal in_list defined'\n    },\n    contains: [{\n      className: 'variable',\n      begin: /\\$\\{/,\n      end: /\\}/\n    }, hljs.HASH_COMMENT_MODE, hljs.QUOTE_STRING_MODE, hljs.NUMBER_MODE]\n  };\n}\nmodule.exports = cmake;", "map": {"version": 3, "names": ["cmake", "hljs", "name", "aliases", "case_insensitive", "keywords", "keyword", "contains", "className", "begin", "end", "HASH_COMMENT_MODE", "QUOTE_STRING_MODE", "NUMBER_MODE", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/cmake.js"], "sourcesContent": ["/*\nLanguage: CMake\nDescription: CMake is an open-source cross-platform system for build automation.\nAuthor: <PERSON> <i<PERSON>@kalnitsky.org>\nWebsite: https://cmake.org\n*/\n\n/** @type LanguageFn */\nfunction cmake(hljs) {\n  return {\n    name: 'CMake',\n    aliases: ['cmake.in'],\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        // scripting commands\n        'break cmake_host_system_information cmake_minimum_required cmake_parse_arguments ' +\n        'cmake_policy configure_file continue elseif else endforeach endfunction endif endmacro ' +\n        'endwhile execute_process file find_file find_library find_package find_path ' +\n        'find_program foreach function get_cmake_property get_directory_property ' +\n        'get_filename_component get_property if include include_guard list macro ' +\n        'mark_as_advanced math message option return separate_arguments ' +\n        'set_directory_properties set_property set site_name string unset variable_watch while ' +\n        // project commands\n        'add_compile_definitions add_compile_options add_custom_command add_custom_target ' +\n        'add_definitions add_dependencies add_executable add_library add_link_options ' +\n        'add_subdirectory add_test aux_source_directory build_command create_test_sourcelist ' +\n        'define_property enable_language enable_testing export fltk_wrap_ui ' +\n        'get_source_file_property get_target_property get_test_property include_directories ' +\n        'include_external_msproject include_regular_expression install link_directories ' +\n        'link_libraries load_cache project qt_wrap_cpp qt_wrap_ui remove_definitions ' +\n        'set_source_files_properties set_target_properties set_tests_properties source_group ' +\n        'target_compile_definitions target_compile_features target_compile_options ' +\n        'target_include_directories target_link_directories target_link_libraries ' +\n        'target_link_options target_sources try_compile try_run ' +\n        // CTest commands\n        'ctest_build ctest_configure ctest_coverage ctest_empty_binary_directory ctest_memcheck ' +\n        'ctest_read_custom_files ctest_run_script ctest_sleep ctest_start ctest_submit ' +\n        'ctest_test ctest_update ctest_upload ' +\n        // deprecated commands\n        'build_name exec_program export_library_dependencies install_files install_programs ' +\n        'install_targets load_command make_directory output_required_files remove ' +\n        'subdir_depends subdirs use_mangled_mesa utility_source variable_requires write_file ' +\n        'qt5_use_modules qt5_use_package qt5_wrap_cpp ' +\n        // core keywords\n        'on off true false and or not command policy target test exists is_newer_than ' +\n        'is_directory is_symlink is_absolute matches less greater equal less_equal ' +\n        'greater_equal strless strgreater strequal strless_equal strgreater_equal version_less ' +\n        'version_greater version_equal version_less_equal version_greater_equal in_list defined'\n    },\n    contains: [\n      {\n        className: 'variable',\n        begin: /\\$\\{/,\n        end: /\\}/\n      },\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = cmake;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,CAAC,UAAU,CAAC;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO;MACL;MACA,mFAAmF,GACnF,yFAAyF,GACzF,8EAA8E,GAC9E,0EAA0E,GAC1E,0EAA0E,GAC1E,iEAAiE,GACjE,wFAAwF;MACxF;MACA,mFAAmF,GACnF,+EAA+E,GAC/E,sFAAsF,GACtF,qEAAqE,GACrE,qFAAqF,GACrF,iFAAiF,GACjF,8EAA8E,GAC9E,sFAAsF,GACtF,4EAA4E,GAC5E,2EAA2E,GAC3E,yDAAyD;MACzD;MACA,yFAAyF,GACzF,gFAAgF,GAChF,uCAAuC;MACvC;MACA,qFAAqF,GACrF,2EAA2E,GAC3E,sFAAsF,GACtF,+CAA+C;MAC/C;MACA,+EAA+E,GAC/E,4EAA4E,GAC5E,wFAAwF,GACxF;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,EACDT,IAAI,CAACU,iBAAiB,EACtBV,IAAI,CAACW,iBAAiB,EACtBX,IAAI,CAACY,WAAW;EAEpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGf,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}