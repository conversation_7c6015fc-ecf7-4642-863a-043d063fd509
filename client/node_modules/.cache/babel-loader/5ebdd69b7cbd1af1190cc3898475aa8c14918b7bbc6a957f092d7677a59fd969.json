{"ast": null, "code": "'use strict';\n\nvar refractorT4Templating = require('./t4-templating.js');\nvar refractorCsharp = require('./csharp.js');\nmodule.exports = t4Cs;\nt4Cs.displayName = 't4Cs';\nt4Cs.aliases = [];\nfunction t4Cs(Prism) {\n  Prism.register(refractorT4Templating);\n  Prism.register(refractorCsharp);\n  Prism.languages.t4 = Prism.languages['t4-cs'] = Prism.languages['t4-templating'].createT4('csharp');\n}", "map": {"version": 3, "names": ["refractorT4Templating", "require", "refractorCsharp", "module", "exports", "t4Cs", "displayName", "aliases", "Prism", "register", "languages", "t4", "createT4"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/t4-cs.js"], "sourcesContent": ["'use strict'\nvar refractorT4Templating = require('./t4-templating.js')\nvar refractorCsharp = require('./csharp.js')\nmodule.exports = t4Cs\nt4Cs.displayName = 't4Cs'\nt4Cs.aliases = []\nfunction t4Cs(Prism) {\n  Prism.register(refractorT4Templating)\n  Prism.register(refractorCsharp)\n  Prism.languages.t4 = Prism.languages['t4-cs'] =\n    Prism.languages['t4-templating'].createT4('csharp')\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,qBAAqB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACzD,IAAIC,eAAe,GAAGD,OAAO,CAAC,aAAa,CAAC;AAC5CE,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,QAAQ,CAACT,qBAAqB,CAAC;EACrCQ,KAAK,CAACC,QAAQ,CAACP,eAAe,CAAC;EAC/BM,KAAK,CAACE,SAAS,CAACC,EAAE,GAAGH,KAAK,CAACE,SAAS,CAAC,OAAO,CAAC,GAC3CF,KAAK,CAACE,SAAS,CAAC,eAAe,CAAC,CAACE,QAAQ,CAAC,QAAQ,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}