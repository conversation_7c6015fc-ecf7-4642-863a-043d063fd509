{"ast": null, "code": "'use strict';\n\nmodule.exports = lisp;\nlisp.displayName = 'lisp';\nlisp.aliases = [];\nfunction lisp(Prism) {\n  ;\n  (function (Prism) {\n    /**\n     * Functions to construct regular expressions\n     * e.g. (interactive ... or (interactive)\n     *\n     * @param {string} name\n     * @returns {RegExp}\n     */\n    function simple_form(name) {\n      return RegExp(/(\\()/.source + '(?:' + name + ')' + /(?=[\\s\\)])/.source);\n    }\n    /**\n     * booleans and numbers\n     *\n     * @param {string} pattern\n     * @returns {RegExp}\n     */\n    function primitive(pattern) {\n      return RegExp(/([\\s([])/.source + '(?:' + pattern + ')' + /(?=[\\s)])/.source);\n    } // Patterns in regular expressions\n    // Symbol name. See https://www.gnu.org/software/emacs/manual/html_node/elisp/Symbol-Type.html\n    // & and : are excluded as they are usually used for special purposes\n    var symbol = /(?!\\d)[-+*/~!@$%^=<>{}\\w]+/.source; // symbol starting with & used in function arguments\n    var marker = '&' + symbol; // Open parenthesis for look-behind\n    var par = '(\\\\()';\n    var endpar = '(?=\\\\))'; // End the pattern with look-ahead space\n    var space = '(?=\\\\s)';\n    var nestedPar = /(?:[^()]|\\((?:[^()]|\\((?:[^()]|\\((?:[^()]|\\((?:[^()]|\\([^()]*\\))*\\))*\\))*\\))*\\))*/.source;\n    var language = {\n      // Three or four semicolons are considered a heading.\n      // See https://www.gnu.org/software/emacs/manual/html_node/elisp/Comment-Tips.html\n      heading: {\n        pattern: /;;;.*/,\n        alias: ['comment', 'title']\n      },\n      comment: /;.*/,\n      string: {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true,\n        inside: {\n          argument: /[-A-Z]+(?=[.,\\s])/,\n          symbol: RegExp('`' + symbol + \"'\")\n        }\n      },\n      'quoted-symbol': {\n        pattern: RegExp(\"#?'\" + symbol),\n        alias: ['variable', 'symbol']\n      },\n      'lisp-property': {\n        pattern: RegExp(':' + symbol),\n        alias: 'property'\n      },\n      splice: {\n        pattern: RegExp(',@?' + symbol),\n        alias: ['symbol', 'variable']\n      },\n      keyword: [{\n        pattern: RegExp(par + '(?:and|(?:cl-)?letf|cl-loop|cond|cons|error|if|(?:lexical-)?let\\\\*?|message|not|null|or|provide|require|setq|unless|use-package|when|while)' + space),\n        lookbehind: true\n      }, {\n        pattern: RegExp(par + '(?:append|by|collect|concat|do|finally|for|in|return)' + space),\n        lookbehind: true\n      }],\n      declare: {\n        pattern: simple_form(/declare/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      interactive: {\n        pattern: simple_form(/interactive/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      boolean: {\n        pattern: primitive(/nil|t/.source),\n        lookbehind: true\n      },\n      number: {\n        pattern: primitive(/[-+]?\\d+(?:\\.\\d*)?/.source),\n        lookbehind: true\n      },\n      defvar: {\n        pattern: RegExp(par + 'def(?:const|custom|group|var)\\\\s+' + symbol),\n        lookbehind: true,\n        inside: {\n          keyword: /^def[a-z]+/,\n          variable: RegExp(symbol)\n        }\n      },\n      defun: {\n        pattern: RegExp(par + /(?:cl-)?(?:defmacro|defun\\*?)\\s+/.source + symbol + /\\s+\\(/.source + nestedPar + /\\)/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^(?:cl-)?def\\S+/,\n          // See below, this property needs to be defined later so that it can\n          // reference the language object.\n          arguments: null,\n          function: {\n            pattern: RegExp('(^\\\\s)' + symbol),\n            lookbehind: true\n          },\n          punctuation: /[()]/\n        }\n      },\n      lambda: {\n        pattern: RegExp(par + 'lambda\\\\s+\\\\(\\\\s*(?:&?' + symbol + '(?:\\\\s+&?' + symbol + ')*\\\\s*)?\\\\)'),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^lambda/,\n          // See below, this property needs to be defined later so that it can\n          // reference the language object.\n          arguments: null,\n          punctuation: /[()]/\n        }\n      },\n      car: {\n        pattern: RegExp(par + symbol),\n        lookbehind: true\n      },\n      punctuation: [\n      // open paren, brackets, and close paren\n      /(?:['`,]?\\(|[)\\[\\]])/,\n      // cons\n      {\n        pattern: /(\\s)\\.(?=\\s)/,\n        lookbehind: true\n      }]\n    };\n    var arg = {\n      'lisp-marker': RegExp(marker),\n      varform: {\n        pattern: RegExp(/\\(/.source + symbol + /\\s+(?=\\S)/.source + nestedPar + /\\)/.source),\n        inside: language\n      },\n      argument: {\n        pattern: RegExp(/(^|[\\s(])/.source + symbol),\n        lookbehind: true,\n        alias: 'variable'\n      },\n      rest: language\n    };\n    var forms = '\\\\S+(?:\\\\s+\\\\S+)*';\n    var arglist = {\n      pattern: RegExp(par + nestedPar + endpar),\n      lookbehind: true,\n      inside: {\n        'rest-vars': {\n          pattern: RegExp('&(?:body|rest)\\\\s+' + forms),\n          inside: arg\n        },\n        'other-marker-vars': {\n          pattern: RegExp('&(?:aux|optional)\\\\s+' + forms),\n          inside: arg\n        },\n        keys: {\n          pattern: RegExp('&key\\\\s+' + forms + '(?:\\\\s+&allow-other-keys)?'),\n          inside: arg\n        },\n        argument: {\n          pattern: RegExp(symbol),\n          alias: 'variable'\n        },\n        punctuation: /[()]/\n      }\n    };\n    language['lambda'].inside.arguments = arglist;\n    language['defun'].inside.arguments = Prism.util.clone(arglist);\n    language['defun'].inside.arguments.inside.sublist = arglist;\n    Prism.languages.lisp = language;\n    Prism.languages.elisp = language;\n    Prism.languages.emacs = language;\n    Prism.languages['emacs-lisp'] = language;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "lisp", "displayName", "aliases", "Prism", "simple_form", "name", "RegExp", "source", "primitive", "pattern", "symbol", "marker", "par", "endpar", "space", "nestedPar", "language", "heading", "alias", "comment", "string", "greedy", "inside", "argument", "splice", "keyword", "lookbehind", "declare", "interactive", "boolean", "number", "defvar", "variable", "defun", "arguments", "function", "punctuation", "lambda", "car", "arg", "varform", "rest", "forms", "arglist", "keys", "util", "clone", "sublist", "languages", "elisp", "emacs"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/lisp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = lisp\nlisp.displayName = 'lisp'\nlisp.aliases = []\nfunction lisp(Prism) {\n  ;(function (Prism) {\n    /**\n     * Functions to construct regular expressions\n     * e.g. (interactive ... or (interactive)\n     *\n     * @param {string} name\n     * @returns {RegExp}\n     */\n    function simple_form(name) {\n      return RegExp(/(\\()/.source + '(?:' + name + ')' + /(?=[\\s\\)])/.source)\n    }\n    /**\n     * booleans and numbers\n     *\n     * @param {string} pattern\n     * @returns {RegExp}\n     */\n    function primitive(pattern) {\n      return RegExp(\n        /([\\s([])/.source + '(?:' + pattern + ')' + /(?=[\\s)])/.source\n      )\n    } // Patterns in regular expressions\n    // Symbol name. See https://www.gnu.org/software/emacs/manual/html_node/elisp/Symbol-Type.html\n    // & and : are excluded as they are usually used for special purposes\n    var symbol = /(?!\\d)[-+*/~!@$%^=<>{}\\w]+/.source // symbol starting with & used in function arguments\n    var marker = '&' + symbol // Open parenthesis for look-behind\n    var par = '(\\\\()'\n    var endpar = '(?=\\\\))' // End the pattern with look-ahead space\n    var space = '(?=\\\\s)'\n    var nestedPar =\n      /(?:[^()]|\\((?:[^()]|\\((?:[^()]|\\((?:[^()]|\\((?:[^()]|\\([^()]*\\))*\\))*\\))*\\))*\\))*/\n        .source\n    var language = {\n      // Three or four semicolons are considered a heading.\n      // See https://www.gnu.org/software/emacs/manual/html_node/elisp/Comment-Tips.html\n      heading: {\n        pattern: /;;;.*/,\n        alias: ['comment', 'title']\n      },\n      comment: /;.*/,\n      string: {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true,\n        inside: {\n          argument: /[-A-Z]+(?=[.,\\s])/,\n          symbol: RegExp('`' + symbol + \"'\")\n        }\n      },\n      'quoted-symbol': {\n        pattern: RegExp(\"#?'\" + symbol),\n        alias: ['variable', 'symbol']\n      },\n      'lisp-property': {\n        pattern: RegExp(':' + symbol),\n        alias: 'property'\n      },\n      splice: {\n        pattern: RegExp(',@?' + symbol),\n        alias: ['symbol', 'variable']\n      },\n      keyword: [\n        {\n          pattern: RegExp(\n            par +\n              '(?:and|(?:cl-)?letf|cl-loop|cond|cons|error|if|(?:lexical-)?let\\\\*?|message|not|null|or|provide|require|setq|unless|use-package|when|while)' +\n              space\n          ),\n          lookbehind: true\n        },\n        {\n          pattern: RegExp(\n            par +\n              '(?:append|by|collect|concat|do|finally|for|in|return)' +\n              space\n          ),\n          lookbehind: true\n        }\n      ],\n      declare: {\n        pattern: simple_form(/declare/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      interactive: {\n        pattern: simple_form(/interactive/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      boolean: {\n        pattern: primitive(/nil|t/.source),\n        lookbehind: true\n      },\n      number: {\n        pattern: primitive(/[-+]?\\d+(?:\\.\\d*)?/.source),\n        lookbehind: true\n      },\n      defvar: {\n        pattern: RegExp(par + 'def(?:const|custom|group|var)\\\\s+' + symbol),\n        lookbehind: true,\n        inside: {\n          keyword: /^def[a-z]+/,\n          variable: RegExp(symbol)\n        }\n      },\n      defun: {\n        pattern: RegExp(\n          par +\n            /(?:cl-)?(?:defmacro|defun\\*?)\\s+/.source +\n            symbol +\n            /\\s+\\(/.source +\n            nestedPar +\n            /\\)/.source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^(?:cl-)?def\\S+/,\n          // See below, this property needs to be defined later so that it can\n          // reference the language object.\n          arguments: null,\n          function: {\n            pattern: RegExp('(^\\\\s)' + symbol),\n            lookbehind: true\n          },\n          punctuation: /[()]/\n        }\n      },\n      lambda: {\n        pattern: RegExp(\n          par +\n            'lambda\\\\s+\\\\(\\\\s*(?:&?' +\n            symbol +\n            '(?:\\\\s+&?' +\n            symbol +\n            ')*\\\\s*)?\\\\)'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^lambda/,\n          // See below, this property needs to be defined later so that it can\n          // reference the language object.\n          arguments: null,\n          punctuation: /[()]/\n        }\n      },\n      car: {\n        pattern: RegExp(par + symbol),\n        lookbehind: true\n      },\n      punctuation: [\n        // open paren, brackets, and close paren\n        /(?:['`,]?\\(|[)\\[\\]])/, // cons\n        {\n          pattern: /(\\s)\\.(?=\\s)/,\n          lookbehind: true\n        }\n      ]\n    }\n    var arg = {\n      'lisp-marker': RegExp(marker),\n      varform: {\n        pattern: RegExp(\n          /\\(/.source + symbol + /\\s+(?=\\S)/.source + nestedPar + /\\)/.source\n        ),\n        inside: language\n      },\n      argument: {\n        pattern: RegExp(/(^|[\\s(])/.source + symbol),\n        lookbehind: true,\n        alias: 'variable'\n      },\n      rest: language\n    }\n    var forms = '\\\\S+(?:\\\\s+\\\\S+)*'\n    var arglist = {\n      pattern: RegExp(par + nestedPar + endpar),\n      lookbehind: true,\n      inside: {\n        'rest-vars': {\n          pattern: RegExp('&(?:body|rest)\\\\s+' + forms),\n          inside: arg\n        },\n        'other-marker-vars': {\n          pattern: RegExp('&(?:aux|optional)\\\\s+' + forms),\n          inside: arg\n        },\n        keys: {\n          pattern: RegExp('&key\\\\s+' + forms + '(?:\\\\s+&allow-other-keys)?'),\n          inside: arg\n        },\n        argument: {\n          pattern: RegExp(symbol),\n          alias: 'variable'\n        },\n        punctuation: /[()]/\n      }\n    }\n    language['lambda'].inside.arguments = arglist\n    language['defun'].inside.arguments = Prism.util.clone(arglist)\n    language['defun'].inside.arguments.inside.sublist = arglist\n    Prism.languages.lisp = language\n    Prism.languages.elisp = language\n    Prism.languages.emacs = language\n    Prism.languages['emacs-lisp'] = language\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,WAAWA,CAACC,IAAI,EAAE;MACzB,OAAOC,MAAM,CAAC,MAAM,CAACC,MAAM,GAAG,KAAK,GAAGF,IAAI,GAAG,GAAG,GAAG,YAAY,CAACE,MAAM,CAAC;IACzE;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,SAASA,CAACC,OAAO,EAAE;MAC1B,OAAOH,MAAM,CACX,UAAU,CAACC,MAAM,GAAG,KAAK,GAAGE,OAAO,GAAG,GAAG,GAAG,WAAW,CAACF,MAC1D,CAAC;IACH,CAAC,CAAC;IACF;IACA;IACA,IAAIG,MAAM,GAAG,4BAA4B,CAACH,MAAM,EAAC;IACjD,IAAII,MAAM,GAAG,GAAG,GAAGD,MAAM,EAAC;IAC1B,IAAIE,GAAG,GAAG,OAAO;IACjB,IAAIC,MAAM,GAAG,SAAS,EAAC;IACvB,IAAIC,KAAK,GAAG,SAAS;IACrB,IAAIC,SAAS,GACX,mFAAmF,CAChFR,MAAM;IACX,IAAIS,QAAQ,GAAG;MACb;MACA;MACAC,OAAO,EAAE;QACPR,OAAO,EAAE,OAAO;QAChBS,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO;MAC5B,CAAC;MACDC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;QACNX,OAAO,EAAE,mBAAmB;QAC5BY,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,QAAQ,EAAE,mBAAmB;UAC7Bb,MAAM,EAAEJ,MAAM,CAAC,GAAG,GAAGI,MAAM,GAAG,GAAG;QACnC;MACF,CAAC;MACD,eAAe,EAAE;QACfD,OAAO,EAAEH,MAAM,CAAC,KAAK,GAAGI,MAAM,CAAC;QAC/BQ,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ;MAC9B,CAAC;MACD,eAAe,EAAE;QACfT,OAAO,EAAEH,MAAM,CAAC,GAAG,GAAGI,MAAM,CAAC;QAC7BQ,KAAK,EAAE;MACT,CAAC;MACDM,MAAM,EAAE;QACNf,OAAO,EAAEH,MAAM,CAAC,KAAK,GAAGI,MAAM,CAAC;QAC/BQ,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU;MAC9B,CAAC;MACDO,OAAO,EAAE,CACP;QACEhB,OAAO,EAAEH,MAAM,CACbM,GAAG,GACD,6IAA6I,GAC7IE,KACJ,CAAC;QACDY,UAAU,EAAE;MACd,CAAC,EACD;QACEjB,OAAO,EAAEH,MAAM,CACbM,GAAG,GACD,uDAAuD,GACvDE,KACJ,CAAC;QACDY,UAAU,EAAE;MACd,CAAC,CACF;MACDC,OAAO,EAAE;QACPlB,OAAO,EAAEL,WAAW,CAAC,SAAS,CAACG,MAAM,CAAC;QACtCmB,UAAU,EAAE,IAAI;QAChBR,KAAK,EAAE;MACT,CAAC;MACDU,WAAW,EAAE;QACXnB,OAAO,EAAEL,WAAW,CAAC,aAAa,CAACG,MAAM,CAAC;QAC1CmB,UAAU,EAAE,IAAI;QAChBR,KAAK,EAAE;MACT,CAAC;MACDW,OAAO,EAAE;QACPpB,OAAO,EAAED,SAAS,CAAC,OAAO,CAACD,MAAM,CAAC;QAClCmB,UAAU,EAAE;MACd,CAAC;MACDI,MAAM,EAAE;QACNrB,OAAO,EAAED,SAAS,CAAC,oBAAoB,CAACD,MAAM,CAAC;QAC/CmB,UAAU,EAAE;MACd,CAAC;MACDK,MAAM,EAAE;QACNtB,OAAO,EAAEH,MAAM,CAACM,GAAG,GAAG,mCAAmC,GAAGF,MAAM,CAAC;QACnEgB,UAAU,EAAE,IAAI;QAChBJ,MAAM,EAAE;UACNG,OAAO,EAAE,YAAY;UACrBO,QAAQ,EAAE1B,MAAM,CAACI,MAAM;QACzB;MACF,CAAC;MACDuB,KAAK,EAAE;QACLxB,OAAO,EAAEH,MAAM,CACbM,GAAG,GACD,kCAAkC,CAACL,MAAM,GACzCG,MAAM,GACN,OAAO,CAACH,MAAM,GACdQ,SAAS,GACT,IAAI,CAACR,MACT,CAAC;QACDmB,UAAU,EAAE,IAAI;QAChBL,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNG,OAAO,EAAE,iBAAiB;UAC1B;UACA;UACAS,SAAS,EAAE,IAAI;UACfC,QAAQ,EAAE;YACR1B,OAAO,EAAEH,MAAM,CAAC,QAAQ,GAAGI,MAAM,CAAC;YAClCgB,UAAU,EAAE;UACd,CAAC;UACDU,WAAW,EAAE;QACf;MACF,CAAC;MACDC,MAAM,EAAE;QACN5B,OAAO,EAAEH,MAAM,CACbM,GAAG,GACD,wBAAwB,GACxBF,MAAM,GACN,WAAW,GACXA,MAAM,GACN,aACJ,CAAC;QACDgB,UAAU,EAAE,IAAI;QAChBL,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNG,OAAO,EAAE,SAAS;UAClB;UACA;UACAS,SAAS,EAAE,IAAI;UACfE,WAAW,EAAE;QACf;MACF,CAAC;MACDE,GAAG,EAAE;QACH7B,OAAO,EAAEH,MAAM,CAACM,GAAG,GAAGF,MAAM,CAAC;QAC7BgB,UAAU,EAAE;MACd,CAAC;MACDU,WAAW,EAAE;MACX;MACA,sBAAsB;MAAE;MACxB;QACE3B,OAAO,EAAE,cAAc;QACvBiB,UAAU,EAAE;MACd,CAAC;IAEL,CAAC;IACD,IAAIa,GAAG,GAAG;MACR,aAAa,EAAEjC,MAAM,CAACK,MAAM,CAAC;MAC7B6B,OAAO,EAAE;QACP/B,OAAO,EAAEH,MAAM,CACb,IAAI,CAACC,MAAM,GAAGG,MAAM,GAAG,WAAW,CAACH,MAAM,GAAGQ,SAAS,GAAG,IAAI,CAACR,MAC/D,CAAC;QACDe,MAAM,EAAEN;MACV,CAAC;MACDO,QAAQ,EAAE;QACRd,OAAO,EAAEH,MAAM,CAAC,WAAW,CAACC,MAAM,GAAGG,MAAM,CAAC;QAC5CgB,UAAU,EAAE,IAAI;QAChBR,KAAK,EAAE;MACT,CAAC;MACDuB,IAAI,EAAEzB;IACR,CAAC;IACD,IAAI0B,KAAK,GAAG,mBAAmB;IAC/B,IAAIC,OAAO,GAAG;MACZlC,OAAO,EAAEH,MAAM,CAACM,GAAG,GAAGG,SAAS,GAAGF,MAAM,CAAC;MACzCa,UAAU,EAAE,IAAI;MAChBJ,MAAM,EAAE;QACN,WAAW,EAAE;UACXb,OAAO,EAAEH,MAAM,CAAC,oBAAoB,GAAGoC,KAAK,CAAC;UAC7CpB,MAAM,EAAEiB;QACV,CAAC;QACD,mBAAmB,EAAE;UACnB9B,OAAO,EAAEH,MAAM,CAAC,uBAAuB,GAAGoC,KAAK,CAAC;UAChDpB,MAAM,EAAEiB;QACV,CAAC;QACDK,IAAI,EAAE;UACJnC,OAAO,EAAEH,MAAM,CAAC,UAAU,GAAGoC,KAAK,GAAG,4BAA4B,CAAC;UAClEpB,MAAM,EAAEiB;QACV,CAAC;QACDhB,QAAQ,EAAE;UACRd,OAAO,EAAEH,MAAM,CAACI,MAAM,CAAC;UACvBQ,KAAK,EAAE;QACT,CAAC;QACDkB,WAAW,EAAE;MACf;IACF,CAAC;IACDpB,QAAQ,CAAC,QAAQ,CAAC,CAACM,MAAM,CAACY,SAAS,GAAGS,OAAO;IAC7C3B,QAAQ,CAAC,OAAO,CAAC,CAACM,MAAM,CAACY,SAAS,GAAG/B,KAAK,CAAC0C,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;IAC9D3B,QAAQ,CAAC,OAAO,CAAC,CAACM,MAAM,CAACY,SAAS,CAACZ,MAAM,CAACyB,OAAO,GAAGJ,OAAO;IAC3DxC,KAAK,CAAC6C,SAAS,CAAChD,IAAI,GAAGgB,QAAQ;IAC/Bb,KAAK,CAAC6C,SAAS,CAACC,KAAK,GAAGjC,QAAQ;IAChCb,KAAK,CAAC6C,SAAS,CAACE,KAAK,GAAGlC,QAAQ;IAChCb,KAAK,CAAC6C,SAAS,CAAC,YAAY,CAAC,GAAGhC,QAAQ;EAC1C,CAAC,EAAEb,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}