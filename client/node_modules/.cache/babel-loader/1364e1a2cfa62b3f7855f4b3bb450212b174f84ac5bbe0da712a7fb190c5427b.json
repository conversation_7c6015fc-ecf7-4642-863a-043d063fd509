{"ast": null, "code": "'use strict';\n\nvar refractorScheme = require('./scheme.js');\nmodule.exports = racket;\nracket.displayName = 'racket';\nracket.aliases = ['rkt'];\nfunction racket(Prism) {\n  Prism.register(refractorScheme);\n  Prism.languages.racket = Prism.languages.extend('scheme', {\n    'lambda-parameter': {\n      // the racket lambda syntax is a lot more complex, so we won't even attempt to capture it.\n      // this will just prevent false positives of the `function` pattern\n      pattern: /([(\\[]lambda\\s+[(\\[])[^()\\[\\]'\\s]+/,\n      lookbehind: true\n    }\n  });\n  Prism.languages.insertBefore('racket', 'string', {\n    lang: {\n      pattern: /^#lang.+/m,\n      greedy: true,\n      alias: 'keyword'\n    }\n  });\n  Prism.languages.rkt = Prism.languages.racket;\n}", "map": {"version": 3, "names": ["refractorScheme", "require", "module", "exports", "racket", "displayName", "aliases", "Prism", "register", "languages", "extend", "pattern", "lookbehind", "insertBefore", "lang", "greedy", "alias", "rkt"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/racket.js"], "sourcesContent": ["'use strict'\nvar refractorScheme = require('./scheme.js')\nmodule.exports = racket\nracket.displayName = 'racket'\nracket.aliases = ['rkt']\nfunction racket(Prism) {\n  Prism.register(refractorScheme)\n  Prism.languages.racket = Prism.languages.extend('scheme', {\n    'lambda-parameter': {\n      // the racket lambda syntax is a lot more complex, so we won't even attempt to capture it.\n      // this will just prevent false positives of the `function` pattern\n      pattern: /([(\\[]lambda\\s+[(\\[])[^()\\[\\]'\\s]+/,\n      lookbehind: true\n    }\n  })\n  Prism.languages.insertBefore('racket', 'string', {\n    lang: {\n      pattern: /^#lang.+/m,\n      greedy: true,\n      alias: 'keyword'\n    }\n  })\n  Prism.languages.rkt = Prism.languages.racket\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,aAAa,CAAC;AAC5CC,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,KAAK,CAAC;AACxB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACR,eAAe,CAAC;EAC/BO,KAAK,CAACE,SAAS,CAACL,MAAM,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;IACxD,kBAAkB,EAAE;MAClB;MACA;MACAC,OAAO,EAAE,oCAAoC;MAC7CC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EACFL,KAAK,CAACE,SAAS,CAACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;IAC/CC,IAAI,EAAE;MACJH,OAAO,EAAE,WAAW;MACpBI,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFT,KAAK,CAACE,SAAS,CAACQ,GAAG,GAAGV,KAAK,CAACE,SAAS,CAACL,MAAM;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}