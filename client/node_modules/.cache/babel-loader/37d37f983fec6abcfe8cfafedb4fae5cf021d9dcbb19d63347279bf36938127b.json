{"ast": null, "code": "'use strict';\n\nmodule.exports = markdown;\nmarkdown.displayName = 'markdown';\nmarkdown.aliases = ['md'];\nfunction markdown(Prism) {\n  ;\n  (function (Prism) {\n    // Allow only one line break\n    var inner = /(?:\\\\.|[^\\\\\\n\\r]|(?:\\n|\\r\\n?)(?![\\r\\n]))/.source;\n    /**\n     * This function is intended for the creation of the bold or italic pattern.\n     *\n     * This also adds a lookbehind group to the given pattern to ensure that the pattern is not backslash-escaped.\n     *\n     * _Note:_ Keep in mind that this adds a capturing group.\n     *\n     * @param {string} pattern\n     * @returns {RegExp}\n     */\n    function createInline(pattern) {\n      pattern = pattern.replace(/<inner>/g, function () {\n        return inner;\n      });\n      return RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + '(?:' + pattern + ')');\n    }\n    var tableCell = /(?:\\\\.|``(?:[^`\\r\\n]|`(?!`))+``|`[^`\\r\\n]+`|[^\\\\|\\r\\n`])+/.source;\n    var tableRow = /\\|?__(?:\\|__)+\\|?(?:(?:\\n|\\r\\n?)|(?![\\s\\S]))/.source.replace(/__/g, function () {\n      return tableCell;\n    });\n    var tableLine = /\\|?[ \\t]*:?-{3,}:?[ \\t]*(?:\\|[ \\t]*:?-{3,}:?[ \\t]*)+\\|?(?:\\n|\\r\\n?)/.source;\n    Prism.languages.markdown = Prism.languages.extend('markup', {});\n    Prism.languages.insertBefore('markdown', 'prolog', {\n      'front-matter-block': {\n        pattern: /(^(?:\\s*[\\r\\n])?)---(?!.)[\\s\\S]*?[\\r\\n]---(?!.)/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          punctuation: /^---|---$/,\n          'front-matter': {\n            pattern: /\\S+(?:\\s+\\S+)*/,\n            alias: ['yaml', 'language-yaml'],\n            inside: Prism.languages.yaml\n          }\n        }\n      },\n      blockquote: {\n        // > ...\n        pattern: /^>(?:[\\t ]*>)*/m,\n        alias: 'punctuation'\n      },\n      table: {\n        pattern: RegExp('^' + tableRow + tableLine + '(?:' + tableRow + ')*', 'm'),\n        inside: {\n          'table-data-rows': {\n            pattern: RegExp('^(' + tableRow + tableLine + ')(?:' + tableRow + ')*$'),\n            lookbehind: true,\n            inside: {\n              'table-data': {\n                pattern: RegExp(tableCell),\n                inside: Prism.languages.markdown\n              },\n              punctuation: /\\|/\n            }\n          },\n          'table-line': {\n            pattern: RegExp('^(' + tableRow + ')' + tableLine + '$'),\n            lookbehind: true,\n            inside: {\n              punctuation: /\\||:?-{3,}:?/\n            }\n          },\n          'table-header-row': {\n            pattern: RegExp('^' + tableRow + '$'),\n            inside: {\n              'table-header': {\n                pattern: RegExp(tableCell),\n                alias: 'important',\n                inside: Prism.languages.markdown\n              },\n              punctuation: /\\|/\n            }\n          }\n        }\n      },\n      code: [{\n        // Prefixed by 4 spaces or 1 tab and preceded by an empty line\n        pattern: /((?:^|\\n)[ \\t]*\\n|(?:^|\\r\\n?)[ \\t]*\\r\\n?)(?: {4}|\\t).+(?:(?:\\n|\\r\\n?)(?: {4}|\\t).+)*/,\n        lookbehind: true,\n        alias: 'keyword'\n      }, {\n        // ```optional language\n        // code block\n        // ```\n        pattern: /^```[\\s\\S]*?^```$/m,\n        greedy: true,\n        inside: {\n          'code-block': {\n            pattern: /^(```.*(?:\\n|\\r\\n?))[\\s\\S]+?(?=(?:\\n|\\r\\n?)^```$)/m,\n            lookbehind: true\n          },\n          'code-language': {\n            pattern: /^(```).+/,\n            lookbehind: true\n          },\n          punctuation: /```/\n        }\n      }],\n      title: [{\n        // title 1\n        // =======\n        // title 2\n        // -------\n        pattern: /\\S.*(?:\\n|\\r\\n?)(?:==+|--+)(?=[ \\t]*$)/m,\n        alias: 'important',\n        inside: {\n          punctuation: /==+$|--+$/\n        }\n      }, {\n        // # title 1\n        // ###### title 6\n        pattern: /(^\\s*)#.+/m,\n        lookbehind: true,\n        alias: 'important',\n        inside: {\n          punctuation: /^#+|#+$/\n        }\n      }],\n      hr: {\n        // ***\n        // ---\n        // * * *\n        // -----------\n        pattern: /(^\\s*)([*-])(?:[\\t ]*\\2){2,}(?=\\s*$)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      list: {\n        // * item\n        // + item\n        // - item\n        // 1. item\n        pattern: /(^\\s*)(?:[*+-]|\\d+\\.)(?=[\\t ].)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      'url-reference': {\n        // [id]: http://example.com \"Optional title\"\n        // [id]: http://example.com 'Optional title'\n        // [id]: http://example.com (Optional title)\n        // [id]: <http://example.com> \"Optional title\"\n        pattern: /!?\\[[^\\]]+\\]:[\\t ]+(?:\\S+|<(?:\\\\.|[^>\\\\])+>)(?:[\\t ]+(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\)))?/,\n        inside: {\n          variable: {\n            pattern: /^(!?\\[)[^\\]]+/,\n            lookbehind: true\n          },\n          string: /(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\))$/,\n          punctuation: /^[\\[\\]!:]|[<>]/\n        },\n        alias: 'url'\n      },\n      bold: {\n        // **strong**\n        // __strong__\n        // allow one nested instance of italic text using the same delimiter\n        pattern: createInline(/\\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\\b|\\*\\*(?:(?!\\*)<inner>|\\*(?:(?!\\*)<inner>)+\\*)+\\*\\*/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^..)[\\s\\S]+(?=..$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /\\*\\*|__/\n        }\n      },\n      italic: {\n        // *em*\n        // _em_\n        // allow one nested instance of bold text using the same delimiter\n        pattern: createInline(/\\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\\b|\\*(?:(?!\\*)<inner>|\\*\\*(?:(?!\\*)<inner>)+\\*\\*)+\\*/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^.)[\\s\\S]+(?=.$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /[*_]/\n        }\n      },\n      strike: {\n        // ~~strike through~~\n        // ~strike~\n        // eslint-disable-next-line regexp/strict\n        pattern: createInline(/(~~?)(?:(?!~)<inner>)+\\2/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^~~?)[\\s\\S]+(?=\\1$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /~~?/\n        }\n      },\n      'code-snippet': {\n        // `code`\n        // ``code``\n        pattern: /(^|[^\\\\`])(?:``[^`\\r\\n]+(?:`[^`\\r\\n]+)*``(?!`)|`[^`\\r\\n]+`(?!`))/,\n        lookbehind: true,\n        greedy: true,\n        alias: ['code', 'keyword']\n      },\n      url: {\n        // [example](http://example.com \"Optional title\")\n        // [example][id]\n        // [example] [id]\n        pattern: createInline(/!?\\[(?:(?!\\])<inner>)+\\](?:\\([^\\s)]+(?:[\\t ]+\"(?:\\\\.|[^\"\\\\])*\")?\\)|[ \\t]?\\[(?:(?!\\])<inner>)+\\])/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          operator: /^!/,\n          content: {\n            pattern: /(^\\[)[^\\]]+(?=\\])/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          variable: {\n            pattern: /(^\\][ \\t]?\\[)[^\\]]+(?=\\]$)/,\n            lookbehind: true\n          },\n          url: {\n            pattern: /(^\\]\\()[^\\s)]+/,\n            lookbehind: true\n          },\n          string: {\n            pattern: /(^[ \\t]+)\"(?:\\\\.|[^\"\\\\])*\"(?=\\)$)/,\n            lookbehind: true\n          }\n        }\n      }\n    });\n    ['url', 'bold', 'italic', 'strike'].forEach(function (token) {\n      ;\n      ['url', 'bold', 'italic', 'strike', 'code-snippet'].forEach(function (inside) {\n        if (token !== inside) {\n          Prism.languages.markdown[token].inside.content.inside[inside] = Prism.languages.markdown[inside];\n        }\n      });\n    });\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'markdown' && env.language !== 'md') {\n        return;\n      }\n      function walkTokens(tokens) {\n        if (!tokens || typeof tokens === 'string') {\n          return;\n        }\n        for (var i = 0, l = tokens.length; i < l; i++) {\n          var token = tokens[i];\n          if (token.type !== 'code') {\n            walkTokens(token.content);\n            continue;\n          }\n          /*\n           * Add the correct `language-xxxx` class to this code block. Keep in mind that the `code-language` token\n           * is optional. But the grammar is defined so that there is only one case we have to handle:\n           *\n           * token.content = [\n           *     <span class=\"punctuation\">```</span>,\n           *     <span class=\"code-language\">xxxx</span>,\n           *     '\\n', // exactly one new lines (\\r or \\n or \\r\\n)\n           *     <span class=\"code-block\">...</span>,\n           *     '\\n', // exactly one new lines again\n           *     <span class=\"punctuation\">```</span>\n           * ];\n           */\n          var codeLang = token.content[1];\n          var codeBlock = token.content[3];\n          if (codeLang && codeBlock && codeLang.type === 'code-language' && codeBlock.type === 'code-block' && typeof codeLang.content === 'string') {\n            // this might be a language that Prism does not support\n            // do some replacements to support C++, C#, and F#\n            var lang = codeLang.content.replace(/\\b#/g, 'sharp').replace(/\\b\\+\\+/g, 'pp'); // only use the first word\n            lang = (/[a-z][\\w-]*/i.exec(lang) || [''])[0].toLowerCase();\n            var alias = 'language-' + lang; // add alias\n            if (!codeBlock.alias) {\n              codeBlock.alias = [alias];\n            } else if (typeof codeBlock.alias === 'string') {\n              codeBlock.alias = [codeBlock.alias, alias];\n            } else {\n              codeBlock.alias.push(alias);\n            }\n          }\n        }\n      }\n      walkTokens(env.tokens);\n    });\n    Prism.hooks.add('wrap', function (env) {\n      if (env.type !== 'code-block') {\n        return;\n      }\n      var codeLang = '';\n      for (var i = 0, l = env.classes.length; i < l; i++) {\n        var cls = env.classes[i];\n        var match = /language-(.+)/.exec(cls);\n        if (match) {\n          codeLang = match[1];\n          break;\n        }\n      }\n      var grammar = Prism.languages[codeLang];\n      if (!grammar) {\n        if (codeLang && codeLang !== 'none' && Prism.plugins.autoloader) {\n          var id = 'md-' + new Date().valueOf() + '-' + Math.floor(Math.random() * 1e16);\n          env.attributes['id'] = id;\n          Prism.plugins.autoloader.loadLanguages(codeLang, function () {\n            var ele = document.getElementById(id);\n            if (ele) {\n              ele.innerHTML = Prism.highlight(ele.textContent, Prism.languages[codeLang], codeLang);\n            }\n          });\n        }\n      } else {\n        env.content = Prism.highlight(textContent(env.content.value), grammar, codeLang);\n      }\n    });\n    var tagPattern = RegExp(Prism.languages.markup.tag.pattern.source, 'gi');\n    /**\n     * A list of known entity names.\n     *\n     * This will always be incomplete to save space. The current list is the one used by lowdash's unescape function.\n     *\n     * @see {@link https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/unescape.js#L2}\n     */\n    var KNOWN_ENTITY_NAMES = {\n      amp: '&',\n      lt: '<',\n      gt: '>',\n      quot: '\"'\n    }; // IE 11 doesn't support `String.fromCodePoint`\n    var fromCodePoint = String.fromCodePoint || String.fromCharCode;\n    /**\n     * Returns the text content of a given HTML source code string.\n     *\n     * @param {string} html\n     * @returns {string}\n     */\n    function textContent(html) {\n      // remove all tags\n      var text = html.replace(tagPattern, ''); // decode known entities\n      text = text.replace(/&(\\w{1,8}|#x?[\\da-f]{1,8});/gi, function (m, code) {\n        code = code.toLowerCase();\n        if (code[0] === '#') {\n          var value;\n          if (code[1] === 'x') {\n            value = parseInt(code.slice(2), 16);\n          } else {\n            value = Number(code.slice(1));\n          }\n          return fromCodePoint(value);\n        } else {\n          var known = KNOWN_ENTITY_NAMES[code];\n          if (known) {\n            return known;\n          } // unable to decode\n          return m;\n        }\n      });\n      return text;\n    }\n    Prism.languages.md = Prism.languages.markdown;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "markdown", "displayName", "aliases", "Prism", "inner", "source", "createInline", "pattern", "replace", "RegExp", "tableCell", "tableRow", "tableLine", "languages", "extend", "insertBefore", "lookbehind", "greedy", "inside", "punctuation", "alias", "yaml", "blockquote", "table", "code", "title", "hr", "list", "variable", "string", "bold", "content", "italic", "strike", "url", "operator", "for<PERSON>ach", "token", "hooks", "add", "env", "language", "walkTokens", "tokens", "i", "l", "length", "type", "codeLang", "codeBlock", "lang", "exec", "toLowerCase", "push", "classes", "cls", "match", "grammar", "plugins", "autoloader", "id", "Date", "valueOf", "Math", "floor", "random", "attributes", "loadLanguages", "ele", "document", "getElementById", "innerHTML", "highlight", "textContent", "value", "tagPattern", "markup", "tag", "KNOWN_ENTITY_NAMES", "amp", "lt", "gt", "quot", "fromCodePoint", "String", "fromCharCode", "html", "text", "m", "parseInt", "slice", "Number", "known", "md"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/markdown.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = markdown\nmarkdown.displayName = 'markdown'\nmarkdown.aliases = ['md']\nfunction markdown(Prism) {\n  ;(function (Prism) {\n    // Allow only one line break\n    var inner = /(?:\\\\.|[^\\\\\\n\\r]|(?:\\n|\\r\\n?)(?![\\r\\n]))/.source\n    /**\n     * This function is intended for the creation of the bold or italic pattern.\n     *\n     * This also adds a lookbehind group to the given pattern to ensure that the pattern is not backslash-escaped.\n     *\n     * _Note:_ Keep in mind that this adds a capturing group.\n     *\n     * @param {string} pattern\n     * @returns {RegExp}\n     */\n    function createInline(pattern) {\n      pattern = pattern.replace(/<inner>/g, function () {\n        return inner\n      })\n      return RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + '(?:' + pattern + ')')\n    }\n    var tableCell = /(?:\\\\.|``(?:[^`\\r\\n]|`(?!`))+``|`[^`\\r\\n]+`|[^\\\\|\\r\\n`])+/\n      .source\n    var tableRow =\n      /\\|?__(?:\\|__)+\\|?(?:(?:\\n|\\r\\n?)|(?![\\s\\S]))/.source.replace(\n        /__/g,\n        function () {\n          return tableCell\n        }\n      )\n    var tableLine =\n      /\\|?[ \\t]*:?-{3,}:?[ \\t]*(?:\\|[ \\t]*:?-{3,}:?[ \\t]*)+\\|?(?:\\n|\\r\\n?)/\n        .source\n    Prism.languages.markdown = Prism.languages.extend('markup', {})\n    Prism.languages.insertBefore('markdown', 'prolog', {\n      'front-matter-block': {\n        pattern: /(^(?:\\s*[\\r\\n])?)---(?!.)[\\s\\S]*?[\\r\\n]---(?!.)/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          punctuation: /^---|---$/,\n          'front-matter': {\n            pattern: /\\S+(?:\\s+\\S+)*/,\n            alias: ['yaml', 'language-yaml'],\n            inside: Prism.languages.yaml\n          }\n        }\n      },\n      blockquote: {\n        // > ...\n        pattern: /^>(?:[\\t ]*>)*/m,\n        alias: 'punctuation'\n      },\n      table: {\n        pattern: RegExp(\n          '^' + tableRow + tableLine + '(?:' + tableRow + ')*',\n          'm'\n        ),\n        inside: {\n          'table-data-rows': {\n            pattern: RegExp(\n              '^(' + tableRow + tableLine + ')(?:' + tableRow + ')*$'\n            ),\n            lookbehind: true,\n            inside: {\n              'table-data': {\n                pattern: RegExp(tableCell),\n                inside: Prism.languages.markdown\n              },\n              punctuation: /\\|/\n            }\n          },\n          'table-line': {\n            pattern: RegExp('^(' + tableRow + ')' + tableLine + '$'),\n            lookbehind: true,\n            inside: {\n              punctuation: /\\||:?-{3,}:?/\n            }\n          },\n          'table-header-row': {\n            pattern: RegExp('^' + tableRow + '$'),\n            inside: {\n              'table-header': {\n                pattern: RegExp(tableCell),\n                alias: 'important',\n                inside: Prism.languages.markdown\n              },\n              punctuation: /\\|/\n            }\n          }\n        }\n      },\n      code: [\n        {\n          // Prefixed by 4 spaces or 1 tab and preceded by an empty line\n          pattern:\n            /((?:^|\\n)[ \\t]*\\n|(?:^|\\r\\n?)[ \\t]*\\r\\n?)(?: {4}|\\t).+(?:(?:\\n|\\r\\n?)(?: {4}|\\t).+)*/,\n          lookbehind: true,\n          alias: 'keyword'\n        },\n        {\n          // ```optional language\n          // code block\n          // ```\n          pattern: /^```[\\s\\S]*?^```$/m,\n          greedy: true,\n          inside: {\n            'code-block': {\n              pattern: /^(```.*(?:\\n|\\r\\n?))[\\s\\S]+?(?=(?:\\n|\\r\\n?)^```$)/m,\n              lookbehind: true\n            },\n            'code-language': {\n              pattern: /^(```).+/,\n              lookbehind: true\n            },\n            punctuation: /```/\n          }\n        }\n      ],\n      title: [\n        {\n          // title 1\n          // =======\n          // title 2\n          // -------\n          pattern: /\\S.*(?:\\n|\\r\\n?)(?:==+|--+)(?=[ \\t]*$)/m,\n          alias: 'important',\n          inside: {\n            punctuation: /==+$|--+$/\n          }\n        },\n        {\n          // # title 1\n          // ###### title 6\n          pattern: /(^\\s*)#.+/m,\n          lookbehind: true,\n          alias: 'important',\n          inside: {\n            punctuation: /^#+|#+$/\n          }\n        }\n      ],\n      hr: {\n        // ***\n        // ---\n        // * * *\n        // -----------\n        pattern: /(^\\s*)([*-])(?:[\\t ]*\\2){2,}(?=\\s*$)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      list: {\n        // * item\n        // + item\n        // - item\n        // 1. item\n        pattern: /(^\\s*)(?:[*+-]|\\d+\\.)(?=[\\t ].)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      'url-reference': {\n        // [id]: http://example.com \"Optional title\"\n        // [id]: http://example.com 'Optional title'\n        // [id]: http://example.com (Optional title)\n        // [id]: <http://example.com> \"Optional title\"\n        pattern:\n          /!?\\[[^\\]]+\\]:[\\t ]+(?:\\S+|<(?:\\\\.|[^>\\\\])+>)(?:[\\t ]+(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\)))?/,\n        inside: {\n          variable: {\n            pattern: /^(!?\\[)[^\\]]+/,\n            lookbehind: true\n          },\n          string:\n            /(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\))$/,\n          punctuation: /^[\\[\\]!:]|[<>]/\n        },\n        alias: 'url'\n      },\n      bold: {\n        // **strong**\n        // __strong__\n        // allow one nested instance of italic text using the same delimiter\n        pattern: createInline(\n          /\\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\\b|\\*\\*(?:(?!\\*)<inner>|\\*(?:(?!\\*)<inner>)+\\*)+\\*\\*/\n            .source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^..)[\\s\\S]+(?=..$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /\\*\\*|__/\n        }\n      },\n      italic: {\n        // *em*\n        // _em_\n        // allow one nested instance of bold text using the same delimiter\n        pattern: createInline(\n          /\\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\\b|\\*(?:(?!\\*)<inner>|\\*\\*(?:(?!\\*)<inner>)+\\*\\*)+\\*/\n            .source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^.)[\\s\\S]+(?=.$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /[*_]/\n        }\n      },\n      strike: {\n        // ~~strike through~~\n        // ~strike~\n        // eslint-disable-next-line regexp/strict\n        pattern: createInline(/(~~?)(?:(?!~)<inner>)+\\2/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^~~?)[\\s\\S]+(?=\\1$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /~~?/\n        }\n      },\n      'code-snippet': {\n        // `code`\n        // ``code``\n        pattern:\n          /(^|[^\\\\`])(?:``[^`\\r\\n]+(?:`[^`\\r\\n]+)*``(?!`)|`[^`\\r\\n]+`(?!`))/,\n        lookbehind: true,\n        greedy: true,\n        alias: ['code', 'keyword']\n      },\n      url: {\n        // [example](http://example.com \"Optional title\")\n        // [example][id]\n        // [example] [id]\n        pattern: createInline(\n          /!?\\[(?:(?!\\])<inner>)+\\](?:\\([^\\s)]+(?:[\\t ]+\"(?:\\\\.|[^\"\\\\])*\")?\\)|[ \\t]?\\[(?:(?!\\])<inner>)+\\])/\n            .source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          operator: /^!/,\n          content: {\n            pattern: /(^\\[)[^\\]]+(?=\\])/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          variable: {\n            pattern: /(^\\][ \\t]?\\[)[^\\]]+(?=\\]$)/,\n            lookbehind: true\n          },\n          url: {\n            pattern: /(^\\]\\()[^\\s)]+/,\n            lookbehind: true\n          },\n          string: {\n            pattern: /(^[ \\t]+)\"(?:\\\\.|[^\"\\\\])*\"(?=\\)$)/,\n            lookbehind: true\n          }\n        }\n      }\n    })\n    ;['url', 'bold', 'italic', 'strike'].forEach(function (token) {\n      ;['url', 'bold', 'italic', 'strike', 'code-snippet'].forEach(function (\n        inside\n      ) {\n        if (token !== inside) {\n          Prism.languages.markdown[token].inside.content.inside[inside] =\n            Prism.languages.markdown[inside]\n        }\n      })\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'markdown' && env.language !== 'md') {\n        return\n      }\n      function walkTokens(tokens) {\n        if (!tokens || typeof tokens === 'string') {\n          return\n        }\n        for (var i = 0, l = tokens.length; i < l; i++) {\n          var token = tokens[i]\n          if (token.type !== 'code') {\n            walkTokens(token.content)\n            continue\n          }\n          /*\n           * Add the correct `language-xxxx` class to this code block. Keep in mind that the `code-language` token\n           * is optional. But the grammar is defined so that there is only one case we have to handle:\n           *\n           * token.content = [\n           *     <span class=\"punctuation\">```</span>,\n           *     <span class=\"code-language\">xxxx</span>,\n           *     '\\n', // exactly one new lines (\\r or \\n or \\r\\n)\n           *     <span class=\"code-block\">...</span>,\n           *     '\\n', // exactly one new lines again\n           *     <span class=\"punctuation\">```</span>\n           * ];\n           */\n          var codeLang = token.content[1]\n          var codeBlock = token.content[3]\n          if (\n            codeLang &&\n            codeBlock &&\n            codeLang.type === 'code-language' &&\n            codeBlock.type === 'code-block' &&\n            typeof codeLang.content === 'string'\n          ) {\n            // this might be a language that Prism does not support\n            // do some replacements to support C++, C#, and F#\n            var lang = codeLang.content\n              .replace(/\\b#/g, 'sharp')\n              .replace(/\\b\\+\\+/g, 'pp') // only use the first word\n            lang = (/[a-z][\\w-]*/i.exec(lang) || [''])[0].toLowerCase()\n            var alias = 'language-' + lang // add alias\n            if (!codeBlock.alias) {\n              codeBlock.alias = [alias]\n            } else if (typeof codeBlock.alias === 'string') {\n              codeBlock.alias = [codeBlock.alias, alias]\n            } else {\n              codeBlock.alias.push(alias)\n            }\n          }\n        }\n      }\n      walkTokens(env.tokens)\n    })\n    Prism.hooks.add('wrap', function (env) {\n      if (env.type !== 'code-block') {\n        return\n      }\n      var codeLang = ''\n      for (var i = 0, l = env.classes.length; i < l; i++) {\n        var cls = env.classes[i]\n        var match = /language-(.+)/.exec(cls)\n        if (match) {\n          codeLang = match[1]\n          break\n        }\n      }\n      var grammar = Prism.languages[codeLang]\n      if (!grammar) {\n        if (codeLang && codeLang !== 'none' && Prism.plugins.autoloader) {\n          var id =\n            'md-' +\n            new Date().valueOf() +\n            '-' +\n            Math.floor(Math.random() * 1e16)\n          env.attributes['id'] = id\n          Prism.plugins.autoloader.loadLanguages(codeLang, function () {\n            var ele = document.getElementById(id)\n            if (ele) {\n              ele.innerHTML = Prism.highlight(\n                ele.textContent,\n                Prism.languages[codeLang],\n                codeLang\n              )\n            }\n          })\n        }\n      } else {\n        env.content = Prism.highlight(\n          textContent(env.content.value),\n          grammar,\n          codeLang\n        )\n      }\n    })\n    var tagPattern = RegExp(Prism.languages.markup.tag.pattern.source, 'gi')\n    /**\n     * A list of known entity names.\n     *\n     * This will always be incomplete to save space. The current list is the one used by lowdash's unescape function.\n     *\n     * @see {@link https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/unescape.js#L2}\n     */\n    var KNOWN_ENTITY_NAMES = {\n      amp: '&',\n      lt: '<',\n      gt: '>',\n      quot: '\"'\n    } // IE 11 doesn't support `String.fromCodePoint`\n    var fromCodePoint = String.fromCodePoint || String.fromCharCode\n    /**\n     * Returns the text content of a given HTML source code string.\n     *\n     * @param {string} html\n     * @returns {string}\n     */\n    function textContent(html) {\n      // remove all tags\n      var text = html.replace(tagPattern, '') // decode known entities\n      text = text.replace(/&(\\w{1,8}|#x?[\\da-f]{1,8});/gi, function (m, code) {\n        code = code.toLowerCase()\n        if (code[0] === '#') {\n          var value\n          if (code[1] === 'x') {\n            value = parseInt(code.slice(2), 16)\n          } else {\n            value = Number(code.slice(1))\n          }\n          return fromCodePoint(value)\n        } else {\n          var known = KNOWN_ENTITY_NAMES[code]\n          if (known) {\n            return known\n          } // unable to decode\n          return m\n        }\n      })\n      return text\n    }\n    Prism.languages.md = Prism.languages.markdown\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AACzB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB;IACA,IAAIC,KAAK,GAAG,0CAA0C,CAACC,MAAM;IAC7D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,YAAYA,CAACC,OAAO,EAAE;MAC7BA,OAAO,GAAGA,OAAO,CAACC,OAAO,CAAC,UAAU,EAAE,YAAY;QAChD,OAAOJ,KAAK;MACd,CAAC,CAAC;MACF,OAAOK,MAAM,CAAC,yBAAyB,CAACJ,MAAM,GAAG,KAAK,GAAGE,OAAO,GAAG,GAAG,CAAC;IACzE;IACA,IAAIG,SAAS,GAAG,2DAA2D,CACxEL,MAAM;IACT,IAAIM,QAAQ,GACV,8CAA8C,CAACN,MAAM,CAACG,OAAO,CAC3D,KAAK,EACL,YAAY;MACV,OAAOE,SAAS;IAClB,CACF,CAAC;IACH,IAAIE,SAAS,GACX,qEAAqE,CAClEP,MAAM;IACXF,KAAK,CAACU,SAAS,CAACb,QAAQ,GAAGG,KAAK,CAACU,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC/DX,KAAK,CAACU,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE;MACjD,oBAAoB,EAAE;QACpBR,OAAO,EAAE,iDAAiD;QAC1DS,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,WAAW,EAAE,WAAW;UACxB,cAAc,EAAE;YACdZ,OAAO,EAAE,gBAAgB;YACzBa,KAAK,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC;YAChCF,MAAM,EAAEf,KAAK,CAACU,SAAS,CAACQ;UAC1B;QACF;MACF,CAAC;MACDC,UAAU,EAAE;QACV;QACAf,OAAO,EAAE,iBAAiB;QAC1Ba,KAAK,EAAE;MACT,CAAC;MACDG,KAAK,EAAE;QACLhB,OAAO,EAAEE,MAAM,CACb,GAAG,GAAGE,QAAQ,GAAGC,SAAS,GAAG,KAAK,GAAGD,QAAQ,GAAG,IAAI,EACpD,GACF,CAAC;QACDO,MAAM,EAAE;UACN,iBAAiB,EAAE;YACjBX,OAAO,EAAEE,MAAM,CACb,IAAI,GAAGE,QAAQ,GAAGC,SAAS,GAAG,MAAM,GAAGD,QAAQ,GAAG,KACpD,CAAC;YACDK,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAE;cACN,YAAY,EAAE;gBACZX,OAAO,EAAEE,MAAM,CAACC,SAAS,CAAC;gBAC1BQ,MAAM,EAAEf,KAAK,CAACU,SAAS,CAACb;cAC1B,CAAC;cACDmB,WAAW,EAAE;YACf;UACF,CAAC;UACD,YAAY,EAAE;YACZZ,OAAO,EAAEE,MAAM,CAAC,IAAI,GAAGE,QAAQ,GAAG,GAAG,GAAGC,SAAS,GAAG,GAAG,CAAC;YACxDI,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAE;cACNC,WAAW,EAAE;YACf;UACF,CAAC;UACD,kBAAkB,EAAE;YAClBZ,OAAO,EAAEE,MAAM,CAAC,GAAG,GAAGE,QAAQ,GAAG,GAAG,CAAC;YACrCO,MAAM,EAAE;cACN,cAAc,EAAE;gBACdX,OAAO,EAAEE,MAAM,CAACC,SAAS,CAAC;gBAC1BU,KAAK,EAAE,WAAW;gBAClBF,MAAM,EAAEf,KAAK,CAACU,SAAS,CAACb;cAC1B,CAAC;cACDmB,WAAW,EAAE;YACf;UACF;QACF;MACF,CAAC;MACDK,IAAI,EAAE,CACJ;QACE;QACAjB,OAAO,EACL,sFAAsF;QACxFS,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC,EACD;QACE;QACA;QACA;QACAb,OAAO,EAAE,oBAAoB;QAC7BU,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACN,YAAY,EAAE;YACZX,OAAO,EAAE,oDAAoD;YAC7DS,UAAU,EAAE;UACd,CAAC;UACD,eAAe,EAAE;YACfT,OAAO,EAAE,UAAU;YACnBS,UAAU,EAAE;UACd,CAAC;UACDG,WAAW,EAAE;QACf;MACF,CAAC,CACF;MACDM,KAAK,EAAE,CACL;QACE;QACA;QACA;QACA;QACAlB,OAAO,EAAE,yCAAyC;QAClDa,KAAK,EAAE,WAAW;QAClBF,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC,EACD;QACE;QACA;QACAZ,OAAO,EAAE,YAAY;QACrBS,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE,WAAW;QAClBF,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC,CACF;MACDO,EAAE,EAAE;QACF;QACA;QACA;QACA;QACAnB,OAAO,EAAE,uCAAuC;QAChDS,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJ;QACA;QACA;QACA;QACApB,OAAO,EAAE,kCAAkC;QAC3CS,UAAU,EAAE,IAAI;QAChBI,KAAK,EAAE;MACT,CAAC;MACD,eAAe,EAAE;QACf;QACA;QACA;QACA;QACAb,OAAO,EACL,oHAAoH;QACtHW,MAAM,EAAE;UACNU,QAAQ,EAAE;YACRrB,OAAO,EAAE,eAAe;YACxBS,UAAU,EAAE;UACd,CAAC;UACDa,MAAM,EACJ,8DAA8D;UAChEV,WAAW,EAAE;QACf,CAAC;QACDC,KAAK,EAAE;MACT,CAAC;MACDU,IAAI,EAAE;QACJ;QACA;QACA;QACAvB,OAAO,EAAED,YAAY,CACnB,iGAAiG,CAC9FD,MACL,CAAC;QACDW,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNa,OAAO,EAAE;YACPxB,OAAO,EAAE,qBAAqB;YAC9BS,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAE,CAAC,CAAC,CAAC;UACb,CAAC;UACDC,WAAW,EAAE;QACf;MACF,CAAC;MACDa,MAAM,EAAE;QACN;QACA;QACA;QACAzB,OAAO,EAAED,YAAY,CACnB,iGAAiG,CAC9FD,MACL,CAAC;QACDW,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNa,OAAO,EAAE;YACPxB,OAAO,EAAE,mBAAmB;YAC5BS,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAE,CAAC,CAAC,CAAC;UACb,CAAC;UACDC,WAAW,EAAE;QACf;MACF,CAAC;MACDc,MAAM,EAAE;QACN;QACA;QACA;QACA1B,OAAO,EAAED,YAAY,CAAC,0BAA0B,CAACD,MAAM,CAAC;QACxDW,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNa,OAAO,EAAE;YACPxB,OAAO,EAAE,sBAAsB;YAC/BS,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAE,CAAC,CAAC,CAAC;UACb,CAAC;UACDC,WAAW,EAAE;QACf;MACF,CAAC;MACD,cAAc,EAAE;QACd;QACA;QACAZ,OAAO,EACL,kEAAkE;QACpES,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZG,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS;MAC3B,CAAC;MACDc,GAAG,EAAE;QACH;QACA;QACA;QACA3B,OAAO,EAAED,YAAY,CACnB,kGAAkG,CAC/FD,MACL,CAAC;QACDW,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNiB,QAAQ,EAAE,IAAI;UACdJ,OAAO,EAAE;YACPxB,OAAO,EAAE,mBAAmB;YAC5BS,UAAU,EAAE,IAAI;YAChBE,MAAM,EAAE,CAAC,CAAC,CAAC;UACb,CAAC;UACDU,QAAQ,EAAE;YACRrB,OAAO,EAAE,4BAA4B;YACrCS,UAAU,EAAE;UACd,CAAC;UACDkB,GAAG,EAAE;YACH3B,OAAO,EAAE,gBAAgB;YACzBS,UAAU,EAAE;UACd,CAAC;UACDa,MAAM,EAAE;YACNtB,OAAO,EAAE,mCAAmC;YAC5CS,UAAU,EAAE;UACd;QACF;MACF;IACF,CAAC,CAAC;IACD,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACoB,OAAO,CAAC,UAAUC,KAAK,EAAE;MAC5D;MAAC,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC,CAACD,OAAO,CAAC,UAC3DlB,MAAM,EACN;QACA,IAAImB,KAAK,KAAKnB,MAAM,EAAE;UACpBf,KAAK,CAACU,SAAS,CAACb,QAAQ,CAACqC,KAAK,CAAC,CAACnB,MAAM,CAACa,OAAO,CAACb,MAAM,CAACA,MAAM,CAAC,GAC3Df,KAAK,CAACU,SAAS,CAACb,QAAQ,CAACkB,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFf,KAAK,CAACmC,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/C,IAAIA,GAAG,CAACC,QAAQ,KAAK,UAAU,IAAID,GAAG,CAACC,QAAQ,KAAK,IAAI,EAAE;QACxD;MACF;MACA,SAASC,UAAUA,CAACC,MAAM,EAAE;QAC1B,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UACzC;QACF;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;UAC7C,IAAIP,KAAK,GAAGM,MAAM,CAACC,CAAC,CAAC;UACrB,IAAIP,KAAK,CAACU,IAAI,KAAK,MAAM,EAAE;YACzBL,UAAU,CAACL,KAAK,CAACN,OAAO,CAAC;YACzB;UACF;UACA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,IAAIiB,QAAQ,GAAGX,KAAK,CAACN,OAAO,CAAC,CAAC,CAAC;UAC/B,IAAIkB,SAAS,GAAGZ,KAAK,CAACN,OAAO,CAAC,CAAC,CAAC;UAChC,IACEiB,QAAQ,IACRC,SAAS,IACTD,QAAQ,CAACD,IAAI,KAAK,eAAe,IACjCE,SAAS,CAACF,IAAI,KAAK,YAAY,IAC/B,OAAOC,QAAQ,CAACjB,OAAO,KAAK,QAAQ,EACpC;YACA;YACA;YACA,IAAImB,IAAI,GAAGF,QAAQ,CAACjB,OAAO,CACxBvB,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CACxBA,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,EAAC;YAC5B0C,IAAI,GAAG,CAAC,cAAc,CAACC,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;YAC3D,IAAIhC,KAAK,GAAG,WAAW,GAAG8B,IAAI,EAAC;YAC/B,IAAI,CAACD,SAAS,CAAC7B,KAAK,EAAE;cACpB6B,SAAS,CAAC7B,KAAK,GAAG,CAACA,KAAK,CAAC;YAC3B,CAAC,MAAM,IAAI,OAAO6B,SAAS,CAAC7B,KAAK,KAAK,QAAQ,EAAE;cAC9C6B,SAAS,CAAC7B,KAAK,GAAG,CAAC6B,SAAS,CAAC7B,KAAK,EAAEA,KAAK,CAAC;YAC5C,CAAC,MAAM;cACL6B,SAAS,CAAC7B,KAAK,CAACiC,IAAI,CAACjC,KAAK,CAAC;YAC7B;UACF;QACF;MACF;MACAsB,UAAU,CAACF,GAAG,CAACG,MAAM,CAAC;IACxB,CAAC,CAAC;IACFxC,KAAK,CAACmC,KAAK,CAACC,GAAG,CAAC,MAAM,EAAE,UAAUC,GAAG,EAAE;MACrC,IAAIA,GAAG,CAACO,IAAI,KAAK,YAAY,EAAE;QAC7B;MACF;MACA,IAAIC,QAAQ,GAAG,EAAE;MACjB,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGL,GAAG,CAACc,OAAO,CAACR,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QAClD,IAAIW,GAAG,GAAGf,GAAG,CAACc,OAAO,CAACV,CAAC,CAAC;QACxB,IAAIY,KAAK,GAAG,eAAe,CAACL,IAAI,CAACI,GAAG,CAAC;QACrC,IAAIC,KAAK,EAAE;UACTR,QAAQ,GAAGQ,KAAK,CAAC,CAAC,CAAC;UACnB;QACF;MACF;MACA,IAAIC,OAAO,GAAGtD,KAAK,CAACU,SAAS,CAACmC,QAAQ,CAAC;MACvC,IAAI,CAACS,OAAO,EAAE;QACZ,IAAIT,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAI7C,KAAK,CAACuD,OAAO,CAACC,UAAU,EAAE;UAC/D,IAAIC,EAAE,GACJ,KAAK,GACL,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GACpB,GAAG,GACHC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;UAClCzB,GAAG,CAAC0B,UAAU,CAAC,IAAI,CAAC,GAAGN,EAAE;UACzBzD,KAAK,CAACuD,OAAO,CAACC,UAAU,CAACQ,aAAa,CAACnB,QAAQ,EAAE,YAAY;YAC3D,IAAIoB,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAACV,EAAE,CAAC;YACrC,IAAIQ,GAAG,EAAE;cACPA,GAAG,CAACG,SAAS,GAAGpE,KAAK,CAACqE,SAAS,CAC7BJ,GAAG,CAACK,WAAW,EACftE,KAAK,CAACU,SAAS,CAACmC,QAAQ,CAAC,EACzBA,QACF,CAAC;YACH;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLR,GAAG,CAACT,OAAO,GAAG5B,KAAK,CAACqE,SAAS,CAC3BC,WAAW,CAACjC,GAAG,CAACT,OAAO,CAAC2C,KAAK,CAAC,EAC9BjB,OAAO,EACPT,QACF,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAI2B,UAAU,GAAGlE,MAAM,CAACN,KAAK,CAACU,SAAS,CAAC+D,MAAM,CAACC,GAAG,CAACtE,OAAO,CAACF,MAAM,EAAE,IAAI,CAAC;IACxE;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIyE,kBAAkB,GAAG;MACvBC,GAAG,EAAE,GAAG;MACRC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE;IACR,CAAC,EAAC;IACF,IAAIC,aAAa,GAAGC,MAAM,CAACD,aAAa,IAAIC,MAAM,CAACC,YAAY;IAC/D;AACJ;AACA;AACA;AACA;AACA;IACI,SAASZ,WAAWA,CAACa,IAAI,EAAE;MACzB;MACA,IAAIC,IAAI,GAAGD,IAAI,CAAC9E,OAAO,CAACmE,UAAU,EAAE,EAAE,CAAC,EAAC;MACxCY,IAAI,GAAGA,IAAI,CAAC/E,OAAO,CAAC,+BAA+B,EAAE,UAAUgF,CAAC,EAAEhE,IAAI,EAAE;QACtEA,IAAI,GAAGA,IAAI,CAAC4B,WAAW,CAAC,CAAC;QACzB,IAAI5B,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACnB,IAAIkD,KAAK;UACT,IAAIlD,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnBkD,KAAK,GAAGe,QAAQ,CAACjE,IAAI,CAACkE,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACrC,CAAC,MAAM;YACLhB,KAAK,GAAGiB,MAAM,CAACnE,IAAI,CAACkE,KAAK,CAAC,CAAC,CAAC,CAAC;UAC/B;UACA,OAAOP,aAAa,CAACT,KAAK,CAAC;QAC7B,CAAC,MAAM;UACL,IAAIkB,KAAK,GAAGd,kBAAkB,CAACtD,IAAI,CAAC;UACpC,IAAIoE,KAAK,EAAE;YACT,OAAOA,KAAK;UACd,CAAC,CAAC;UACF,OAAOJ,CAAC;QACV;MACF,CAAC,CAAC;MACF,OAAOD,IAAI;IACb;IACApF,KAAK,CAACU,SAAS,CAACgF,EAAE,GAAG1F,KAAK,CAACU,SAAS,CAACb,QAAQ;EAC/C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}