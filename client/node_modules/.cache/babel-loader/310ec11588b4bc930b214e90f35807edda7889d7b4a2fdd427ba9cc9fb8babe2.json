{"ast": null, "code": "/*\nLanguage: SubUnit\nAuthor: <PERSON> <serge<PERSON><EMAIL>>\nWebsite: https://pypi.org/project/python-subunit/\n*/\n\nfunction subunit(hljs) {\n  const DETAILS = {\n    className: 'string',\n    begin: '\\\\[\\n(multipart)?',\n    end: '\\\\]\\n'\n  };\n  const TIME = {\n    className: 'string',\n    begin: '\\\\d{4}-\\\\d{2}-\\\\d{2}(\\\\s+)\\\\d{2}:\\\\d{2}:\\\\d{2}\\.\\\\d+Z'\n  };\n  const PROGRESSVALUE = {\n    className: 'string',\n    begin: '(\\\\+|-)\\\\d+'\n  };\n  const KEYWORDS = {\n    className: 'keyword',\n    relevance: 10,\n    variants: [{\n      begin: '^(test|testing|success|successful|failure|error|skip|xfail|uxsuccess)(:?)\\\\s+(test)?'\n    }, {\n      begin: '^progress(:?)(\\\\s+)?(pop|push)?'\n    }, {\n      begin: '^tags:'\n    }, {\n      begin: '^time:'\n    }]\n  };\n  return {\n    name: 'SubUnit',\n    case_insensitive: true,\n    contains: [DETAILS, TIME, PROGRESSVALUE, KEYWORDS]\n  };\n}\nmodule.exports = subunit;", "map": {"version": 3, "names": ["subunit", "hljs", "DETAILS", "className", "begin", "end", "TIME", "PROGRESSVALUE", "KEYWORDS", "relevance", "variants", "name", "case_insensitive", "contains", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/subunit.js"], "sourcesContent": ["/*\nLanguage: SubUnit\nAuthor: <PERSON> <serge<PERSON><EMAIL>>\nWebsite: https://pypi.org/project/python-subunit/\n*/\n\nfunction subunit(hljs) {\n  const DETAILS = {\n    className: 'string',\n    begin: '\\\\[\\n(multipart)?',\n    end: '\\\\]\\n'\n  };\n  const TIME = {\n    className: 'string',\n    begin: '\\\\d{4}-\\\\d{2}-\\\\d{2}(\\\\s+)\\\\d{2}:\\\\d{2}:\\\\d{2}\\.\\\\d+Z'\n  };\n  const PROGRESSVALUE = {\n    className: 'string',\n    begin: '(\\\\+|-)\\\\d+'\n  };\n  const KEYWORDS = {\n    className: 'keyword',\n    relevance: 10,\n    variants: [\n      {\n        begin: '^(test|testing|success|successful|failure|error|skip|xfail|uxsuccess)(:?)\\\\s+(test)?'\n      },\n      {\n        begin: '^progress(:?)(\\\\s+)?(pop|push)?'\n      },\n      {\n        begin: '^tags:'\n      },\n      {\n        begin: '^time:'\n      }\n    ]\n  };\n  return {\n    name: 'SubUnit',\n    case_insensitive: true,\n    contains: [\n      DETAILS,\n      TIME,\n      PROGRESSVALUE,\n      KEYWORDS\n    ]\n  };\n}\n\nmodule.exports = subunit;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,mBAAmB;IAC1BC,GAAG,EAAE;EACP,CAAC;EACD,MAAMC,IAAI,GAAG;IACXH,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMG,aAAa,GAAG;IACpBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,MAAMI,QAAQ,GAAG;IACfL,SAAS,EAAE,SAAS;IACpBM,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,CACR;MACEN,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EACD,OAAO;IACLO,IAAI,EAAE,SAAS;IACfC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,CACRX,OAAO,EACPI,IAAI,EACJC,aAAa,EACbC,QAAQ;EAEZ,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGf,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}