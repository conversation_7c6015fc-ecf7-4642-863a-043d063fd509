{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\n Language: Groovy\n Author: <PERSON> <<EMAIL>>\n Description: Groovy programming language implementation inspired from Vsevolod's Java mode\n Website: https://groovy-lang.org\n */\n\nfunction variants(variants, obj = {}) {\n  obj.variants = variants;\n  return obj;\n}\nfunction groovy(hljs) {\n  const IDENT_RE = '[A-Za-z0-9_$]+';\n  const COMMENT = variants([hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.COMMENT('/\\\\*\\\\*', '\\\\*/', {\n    relevance: 0,\n    contains: [{\n      // eat up @'s in emails to prevent them to be recognized as doctags\n      begin: /\\w+@/,\n      relevance: 0\n    }, {\n      className: 'doctag',\n      begin: '@[A-Za-z]+'\n    }]\n  })]);\n  const REGEXP = {\n    className: 'regexp',\n    begin: /~?\\/[^\\/\\n]+\\//,\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const NUMBER = variants([hljs.BINARY_NUMBER_MODE, hljs.C_NUMBER_MODE]);\n  const STRING = variants([{\n    begin: /\"\"\"/,\n    end: /\"\"\"/\n  }, {\n    begin: /'''/,\n    end: /'''/\n  }, {\n    begin: \"\\\\$/\",\n    end: \"/\\\\$\",\n    relevance: 10\n  }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE], {\n    className: \"string\"\n  });\n  return {\n    name: 'Groovy',\n    keywords: {\n      built_in: 'this super',\n      literal: 'true false null',\n      keyword: 'byte short char int long boolean float double void ' +\n      // groovy specific keywords\n      'def as in assert trait ' +\n      // common keywords with Java\n      'abstract static volatile transient public private protected synchronized final ' + 'class interface enum if else for while switch case break default continue ' + 'throw throws try catch finally implements extends new import package return instanceof'\n    },\n    contains: [hljs.SHEBANG({\n      binary: \"groovy\",\n      relevance: 10\n    }), COMMENT, STRING, REGEXP, NUMBER, {\n      className: 'class',\n      beginKeywords: 'class interface trait enum',\n      end: /\\{/,\n      illegal: ':',\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }, {\n      className: 'meta',\n      begin: '@[A-Za-z]+',\n      relevance: 0\n    }, {\n      // highlight map keys and named parameters as attrs\n      className: 'attr',\n      begin: IDENT_RE + '[ \\t]*:',\n      relevance: 0\n    }, {\n      // catch middle element of the ternary operator\n      // to avoid highlight it as a label, named parameter, or map key\n      begin: /\\?/,\n      end: /:/,\n      relevance: 0,\n      contains: [COMMENT, STRING, REGEXP, NUMBER, 'self']\n    }, {\n      // highlight labeled statements\n      className: 'symbol',\n      begin: '^[ \\t]*' + lookahead(IDENT_RE + ':'),\n      excludeBegin: true,\n      end: IDENT_RE + ':',\n      relevance: 0\n    }],\n    illegal: /#|<\\//\n  };\n}\nmodule.exports = groovy;", "map": {"version": 3, "names": ["source", "re", "<PERSON><PERSON><PERSON>", "concat", "args", "joined", "map", "x", "join", "variants", "obj", "groovy", "hljs", "IDENT_RE", "COMMENT", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "relevance", "contains", "begin", "className", "REGEXP", "BACKSLASH_ESCAPE", "NUMBER", "BINARY_NUMBER_MODE", "C_NUMBER_MODE", "STRING", "end", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "name", "keywords", "built_in", "literal", "keyword", "SHEBANG", "binary", "beginKeywords", "illegal", "UNDERSCORE_TITLE_MODE", "excludeBegin", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/groovy.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\n Language: Groovy\n Author: <PERSON> <<EMAIL>>\n Description: Groovy programming language implementation inspired from Vsevolod's Java mode\n Website: https://groovy-lang.org\n */\n\nfunction variants(variants, obj = {}) {\n  obj.variants = variants;\n  return obj;\n}\n\nfunction groovy(hljs) {\n  const IDENT_RE = '[A-Za-z0-9_$]+';\n  const COMMENT = variants([\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.COMMENT(\n      '/\\\\*\\\\*',\n      '\\\\*/',\n      {\n        relevance: 0,\n        contains: [\n          {\n            // eat up @'s in emails to prevent them to be recognized as doctags\n            begin: /\\w+@/,\n            relevance: 0\n          },\n          {\n            className: 'doctag',\n            begin: '@[A-Za-z]+'\n          }\n        ]\n      }\n    )\n  ]);\n  const REGEXP = {\n    className: 'regexp',\n    begin: /~?\\/[^\\/\\n]+\\//,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n  const NUMBER = variants([\n    hljs.BINARY_NUMBER_MODE,\n    hljs.C_NUMBER_MODE\n  ]);\n  const STRING = variants([\n    {\n      begin: /\"\"\"/,\n      end: /\"\"\"/\n    },\n    {\n      begin: /'''/,\n      end: /'''/\n    },\n    {\n      begin: \"\\\\$/\",\n      end: \"/\\\\$\",\n      relevance: 10\n    },\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ],\n  {\n    className: \"string\"\n  }\n  );\n\n  return {\n    name: 'Groovy',\n    keywords: {\n      built_in: 'this super',\n      literal: 'true false null',\n      keyword:\n            'byte short char int long boolean float double void ' +\n            // groovy specific keywords\n            'def as in assert trait ' +\n            // common keywords with Java\n            'abstract static volatile transient public private protected synchronized final ' +\n            'class interface enum if else for while switch case break default continue ' +\n            'throw throws try catch finally implements extends new import package return instanceof'\n    },\n    contains: [\n      hljs.SHEBANG({\n        binary: \"groovy\",\n        relevance: 10\n      }),\n      COMMENT,\n      STRING,\n      REGEXP,\n      NUMBER,\n      {\n        className: 'class',\n        beginKeywords: 'class interface trait enum',\n        end: /\\{/,\n        illegal: ':',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '@[A-Za-z]+',\n        relevance: 0\n      },\n      {\n        // highlight map keys and named parameters as attrs\n        className: 'attr',\n        begin: IDENT_RE + '[ \\t]*:',\n        relevance: 0\n      },\n      {\n        // catch middle element of the ternary operator\n        // to avoid highlight it as a label, named parameter, or map key\n        begin: /\\?/,\n        end: /:/,\n        relevance: 0,\n        contains: [\n          COMMENT,\n          STRING,\n          REGEXP,\n          NUMBER,\n          'self'\n        ]\n      },\n      {\n        // highlight labeled statements\n        className: 'symbol',\n        begin: '^[ \\t]*' + lookahead(IDENT_RE + ':'),\n        excludeBegin: true,\n        end: IDENT_RE + ':',\n        relevance: 0\n      }\n    ],\n    illegal: /#|<\\//\n  };\n}\n\nmodule.exports = groovy;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOE,MAAM,CAAC,KAAK,EAAEF,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKP,MAAM,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,QAAQA,CAACA,QAAQ,EAAEC,GAAG,GAAG,CAAC,CAAC,EAAE;EACpCA,GAAG,CAACD,QAAQ,GAAGA,QAAQ;EACvB,OAAOC,GAAG;AACZ;AAEA,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,QAAQ,GAAG,gBAAgB;EACjC,MAAMC,OAAO,GAAGL,QAAQ,CAAC,CACvBG,IAAI,CAACG,mBAAmB,EACxBH,IAAI,CAACI,oBAAoB,EACzBJ,IAAI,CAACE,OAAO,CACV,SAAS,EACT,MAAM,EACN;IACEG,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACR;MACE;MACAC,KAAK,EAAE,MAAM;MACbF,SAAS,EAAE;IACb,CAAC,EACD;MACEG,SAAS,EAAE,QAAQ;MACnBD,KAAK,EAAE;IACT,CAAC;EAEL,CACF,CAAC,CACF,CAAC;EACF,MAAME,MAAM,GAAG;IACbD,SAAS,EAAE,QAAQ;IACnBD,KAAK,EAAE,gBAAgB;IACvBD,QAAQ,EAAE,CAAEN,IAAI,CAACU,gBAAgB;EACnC,CAAC;EACD,MAAMC,MAAM,GAAGd,QAAQ,CAAC,CACtBG,IAAI,CAACY,kBAAkB,EACvBZ,IAAI,CAACa,aAAa,CACnB,CAAC;EACF,MAAMC,MAAM,GAAGjB,QAAQ,CAAC,CACtB;IACEU,KAAK,EAAE,KAAK;IACZQ,GAAG,EAAE;EACP,CAAC,EACD;IACER,KAAK,EAAE,KAAK;IACZQ,GAAG,EAAE;EACP,CAAC,EACD;IACER,KAAK,EAAE,MAAM;IACbQ,GAAG,EAAE,MAAM;IACXV,SAAS,EAAE;EACb,CAAC,EACDL,IAAI,CAACgB,gBAAgB,EACrBhB,IAAI,CAACiB,iBAAiB,CACvB,EACD;IACET,SAAS,EAAE;EACb,CACA,CAAC;EAED,OAAO;IACLU,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE;MACRC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EACD,qDAAqD;MACrD;MACA,yBAAyB;MACzB;MACA,iFAAiF,GACjF,4EAA4E,GAC5E;IACR,CAAC;IACDhB,QAAQ,EAAE,CACRN,IAAI,CAACuB,OAAO,CAAC;MACXC,MAAM,EAAE,QAAQ;MAChBnB,SAAS,EAAE;IACb,CAAC,CAAC,EACFH,OAAO,EACPY,MAAM,EACNL,MAAM,EACNE,MAAM,EACN;MACEH,SAAS,EAAE,OAAO;MAClBiB,aAAa,EAAE,4BAA4B;MAC3CV,GAAG,EAAE,IAAI;MACTW,OAAO,EAAE,GAAG;MACZpB,QAAQ,EAAE,CACR;QACEmB,aAAa,EAAE;MACjB,CAAC,EACDzB,IAAI,CAAC2B,qBAAqB;IAE9B,CAAC,EACD;MACEnB,SAAS,EAAE,MAAM;MACjBD,KAAK,EAAE,YAAY;MACnBF,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACAG,SAAS,EAAE,MAAM;MACjBD,KAAK,EAAEN,QAAQ,GAAG,SAAS;MAC3BI,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACA;MACAE,KAAK,EAAE,IAAI;MACXQ,GAAG,EAAE,GAAG;MACRV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CACRJ,OAAO,EACPY,MAAM,EACNL,MAAM,EACNE,MAAM,EACN,MAAM;IAEV,CAAC,EACD;MACE;MACAH,SAAS,EAAE,QAAQ;MACnBD,KAAK,EAAE,SAAS,GAAGjB,SAAS,CAACW,QAAQ,GAAG,GAAG,CAAC;MAC5C2B,YAAY,EAAE,IAAI;MAClBb,GAAG,EAAEd,QAAQ,GAAG,GAAG;MACnBI,SAAS,EAAE;IACb,CAAC,CACF;IACDqB,OAAO,EAAE;EACX,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAG/B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}