{"ast": null, "code": "'use strict';\n\nmodule.exports = sml;\nsml.displayName = 'sml';\nsml.aliases = ['smlnj'];\nfunction sml(Prism) {\n  // https://smlfamily.github.io/sml97-defn.pdf\n  // https://people.mpi-sws.org/~rossberg/sml.html\n  ;\n  (function (Prism) {\n    var keywords = /\\b(?:abstype|and|andalso|as|case|datatype|do|else|end|eqtype|exception|fn|fun|functor|handle|if|in|include|infix|infixr|let|local|nonfix|of|op|open|orelse|raise|rec|sharing|sig|signature|struct|structure|then|type|val|where|while|with|withtype)\\b/i;\n    Prism.languages.sml = {\n      // allow one level of nesting\n      comment: /\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*)|\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*))*\\*\\))*\\*\\)/,\n      string: {\n        pattern: /#?\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      'class-name': [{\n        // This is only an approximation since the real grammar is context-free\n        //\n        // Why the main loop so complex?\n        // The main loop is approximately the same as /(?:\\s*(?:[*,]|->)\\s*<TERMINAL>)*/ which is, obviously, a lot\n        // simpler. The difference is that if a comma is the last iteration of the loop, then the terminal must be\n        // followed by a long identifier.\n        pattern: RegExp(/((?:^|[^:]):\\s*)<TERMINAL>(?:\\s*(?:(?:\\*|->)\\s*<TERMINAL>|,\\s*<TERMINAL>(?:(?=<NOT-LAST>)|(?!<NOT-LAST>)\\s+<LONG-ID>)))*/.source.replace(/<NOT-LAST>/g, function () {\n          return /\\s*(?:[*,]|->)/.source;\n        }).replace(/<TERMINAL>/g, function () {\n          return /(?:'[\\w']*|<LONG-ID>|\\((?:[^()]|\\([^()]*\\))*\\)|\\{(?:[^{}]|\\{[^{}]*\\})*\\})(?:\\s+<LONG-ID>)*/.source;\n        }).replace(/<LONG-ID>/g, function () {\n          return /(?!<KEYWORD>)[a-z\\d_][\\w'.]*/.source;\n        }).replace(/<KEYWORD>/g, function () {\n          return keywords.source;\n        }), 'i'),\n        lookbehind: true,\n        greedy: true,\n        inside: null // see below\n      }, {\n        pattern: /((?:^|[^\\w'])(?:datatype|exception|functor|signature|structure|type)\\s+)[a-z_][\\w'.]*/i,\n        lookbehind: true\n      }],\n      function: {\n        pattern: /((?:^|[^\\w'])fun\\s+)[a-z_][\\w'.]*/i,\n        lookbehind: true\n      },\n      keyword: keywords,\n      variable: {\n        pattern: /(^|[^\\w'])'[\\w']*/,\n        lookbehind: true\n      },\n      number: /~?\\b(?:\\d+(?:\\.\\d+)?(?:e~?\\d+)?|0x[\\da-f]+)\\b/i,\n      word: {\n        pattern: /\\b0w(?:\\d+|x[\\da-f]+)\\b/i,\n        alias: 'constant'\n      },\n      boolean: /\\b(?:false|true)\\b/i,\n      operator: /\\.\\.\\.|:[>=:]|=>?|->|[<>]=?|[!+\\-*/^#|@~]/,\n      punctuation: /[(){}\\[\\].:,;]/\n    };\n    Prism.languages.sml['class-name'][0].inside = Prism.languages.sml;\n    Prism.languages.smlnj = Prism.languages.sml;\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "sml", "displayName", "aliases", "Prism", "keywords", "languages", "comment", "string", "pattern", "greedy", "RegExp", "source", "replace", "lookbehind", "inside", "function", "keyword", "variable", "number", "word", "alias", "boolean", "operator", "punctuation", "smlnj"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/sml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = sml\nsml.displayName = 'sml'\nsml.aliases = ['smlnj']\nfunction sml(Prism) {\n  // https://smlfamily.github.io/sml97-defn.pdf\n  // https://people.mpi-sws.org/~rossberg/sml.html\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:abstype|and|andalso|as|case|datatype|do|else|end|eqtype|exception|fn|fun|functor|handle|if|in|include|infix|infixr|let|local|nonfix|of|op|open|orelse|raise|rec|sharing|sig|signature|struct|structure|then|type|val|where|while|with|withtype)\\b/i\n    Prism.languages.sml = {\n      // allow one level of nesting\n      comment:\n        /\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*)|\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*))*\\*\\))*\\*\\)/,\n      string: {\n        pattern: /#?\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      'class-name': [\n        {\n          // This is only an approximation since the real grammar is context-free\n          //\n          // Why the main loop so complex?\n          // The main loop is approximately the same as /(?:\\s*(?:[*,]|->)\\s*<TERMINAL>)*/ which is, obviously, a lot\n          // simpler. The difference is that if a comma is the last iteration of the loop, then the terminal must be\n          // followed by a long identifier.\n          pattern: RegExp(\n            /((?:^|[^:]):\\s*)<TERMINAL>(?:\\s*(?:(?:\\*|->)\\s*<TERMINAL>|,\\s*<TERMINAL>(?:(?=<NOT-LAST>)|(?!<NOT-LAST>)\\s+<LONG-ID>)))*/.source\n              .replace(/<NOT-LAST>/g, function () {\n                return /\\s*(?:[*,]|->)/.source\n              })\n              .replace(/<TERMINAL>/g, function () {\n                return /(?:'[\\w']*|<LONG-ID>|\\((?:[^()]|\\([^()]*\\))*\\)|\\{(?:[^{}]|\\{[^{}]*\\})*\\})(?:\\s+<LONG-ID>)*/\n                  .source\n              })\n              .replace(/<LONG-ID>/g, function () {\n                return /(?!<KEYWORD>)[a-z\\d_][\\w'.]*/.source\n              })\n              .replace(/<KEYWORD>/g, function () {\n                return keywords.source\n              }),\n            'i'\n          ),\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        {\n          pattern:\n            /((?:^|[^\\w'])(?:datatype|exception|functor|signature|structure|type)\\s+)[a-z_][\\w'.]*/i,\n          lookbehind: true\n        }\n      ],\n      function: {\n        pattern: /((?:^|[^\\w'])fun\\s+)[a-z_][\\w'.]*/i,\n        lookbehind: true\n      },\n      keyword: keywords,\n      variable: {\n        pattern: /(^|[^\\w'])'[\\w']*/,\n        lookbehind: true\n      },\n      number: /~?\\b(?:\\d+(?:\\.\\d+)?(?:e~?\\d+)?|0x[\\da-f]+)\\b/i,\n      word: {\n        pattern: /\\b0w(?:\\d+|x[\\da-f]+)\\b/i,\n        alias: 'constant'\n      },\n      boolean: /\\b(?:false|true)\\b/i,\n      operator: /\\.\\.\\.|:[>=:]|=>?|->|[<>]=?|[!+\\-*/^#|@~]/,\n      punctuation: /[(){}\\[\\].:,;]/\n    }\n    Prism.languages.sml['class-name'][0].inside = Prism.languages.sml\n    Prism.languages.smlnj = Prism.languages.sml\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,CAAC,OAAO,CAAC;AACvB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EACA;EACA;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GACV,yPAAyP;IAC3PD,KAAK,CAACE,SAAS,CAACL,GAAG,GAAG;MACpB;MACAM,OAAO,EACL,2EAA2E;MAC7EC,MAAM,EAAE;QACNC,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE;MACV,CAAC;MACD,YAAY,EAAE,CACZ;QACE;QACA;QACA;QACA;QACA;QACA;QACAD,OAAO,EAAEE,MAAM,CACb,0HAA0H,CAACC,MAAM,CAC9HC,OAAO,CAAC,aAAa,EAAE,YAAY;UAClC,OAAO,gBAAgB,CAACD,MAAM;QAChC,CAAC,CAAC,CACDC,OAAO,CAAC,aAAa,EAAE,YAAY;UAClC,OAAO,4FAA4F,CAChGD,MAAM;QACX,CAAC,CAAC,CACDC,OAAO,CAAC,YAAY,EAAE,YAAY;UACjC,OAAO,8BAA8B,CAACD,MAAM;QAC9C,CAAC,CAAC,CACDC,OAAO,CAAC,YAAY,EAAE,YAAY;UACjC,OAAOR,QAAQ,CAACO,MAAM;QACxB,CAAC,CAAC,EACJ,GACF,CAAC;QACDE,UAAU,EAAE,IAAI;QAChBJ,MAAM,EAAE,IAAI;QACZK,MAAM,EAAE,IAAI,CAAC;MACf,CAAC,EACD;QACEN,OAAO,EACL,wFAAwF;QAC1FK,UAAU,EAAE;MACd,CAAC,CACF;MACDE,QAAQ,EAAE;QACRP,OAAO,EAAE,oCAAoC;QAC7CK,UAAU,EAAE;MACd,CAAC;MACDG,OAAO,EAAEZ,QAAQ;MACjBa,QAAQ,EAAE;QACRT,OAAO,EAAE,mBAAmB;QAC5BK,UAAU,EAAE;MACd,CAAC;MACDK,MAAM,EAAE,gDAAgD;MACxDC,IAAI,EAAE;QACJX,OAAO,EAAE,0BAA0B;QACnCY,KAAK,EAAE;MACT,CAAC;MACDC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,2CAA2C;MACrDC,WAAW,EAAE;IACf,CAAC;IACDpB,KAAK,CAACE,SAAS,CAACL,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAACc,MAAM,GAAGX,KAAK,CAACE,SAAS,CAACL,GAAG;IACjEG,KAAK,CAACE,SAAS,CAACmB,KAAK,GAAGrB,KAAK,CAACE,SAAS,CAACL,GAAG;EAC7C,CAAC,EAAEG,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}