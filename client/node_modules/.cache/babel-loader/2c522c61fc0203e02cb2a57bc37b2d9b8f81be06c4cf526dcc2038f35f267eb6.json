{"ast": null, "code": "'use strict';\n\nvar refractorRuby = require('./ruby.js');\nmodule.exports = crystal;\ncrystal.displayName = 'crystal';\ncrystal.aliases = [];\nfunction crystal(Prism) {\n  Prism.register(refractorRuby);\n  (function (Prism) {\n    Prism.languages.crystal = Prism.languages.extend('ruby', {\n      keyword: [/\\b(?:__DIR__|__END_LINE__|__FILE__|__LINE__|abstract|alias|annotation|as|asm|begin|break|case|class|def|do|else|elsif|end|ensure|enum|extend|for|fun|if|ifdef|include|instance_sizeof|lib|macro|module|next|of|out|pointerof|private|protected|ptr|require|rescue|return|select|self|sizeof|struct|super|then|type|typeof|undef|uninitialized|union|unless|until|when|while|with|yield)\\b/, {\n        pattern: /(\\.\\s*)(?:is_a|responds_to)\\?/,\n        lookbehind: true\n      }],\n      number: /\\b(?:0b[01_]*[01]|0o[0-7_]*[0-7]|0x[\\da-fA-F_]*[\\da-fA-F]|(?:\\d(?:[\\d_]*\\d)?)(?:\\.[\\d_]*\\d)?(?:[eE][+-]?[\\d_]*\\d)?)(?:_(?:[uif](?:8|16|32|64))?)?\\b/,\n      operator: [/->/, Prism.languages.ruby.operator],\n      punctuation: /[(){}[\\].,;\\\\]/\n    });\n    Prism.languages.insertBefore('crystal', 'string-literal', {\n      attribute: {\n        pattern: /@\\[.*?\\]/,\n        inside: {\n          delimiter: {\n            pattern: /^@\\[|\\]$/,\n            alias: 'punctuation'\n          },\n          attribute: {\n            pattern: /^(\\s*)\\w+/,\n            lookbehind: true,\n            alias: 'class-name'\n          },\n          args: {\n            pattern: /\\S(?:[\\s\\S]*\\S)?/,\n            inside: Prism.languages.crystal\n          }\n        }\n      },\n      expansion: {\n        pattern: /\\{(?:\\{.*?\\}|%.*?%)\\}/,\n        inside: {\n          content: {\n            pattern: /^(\\{.)[\\s\\S]+(?=.\\}$)/,\n            lookbehind: true,\n            inside: Prism.languages.crystal\n          },\n          delimiter: {\n            pattern: /^\\{[\\{%]|[\\}%]\\}$/,\n            alias: 'operator'\n          }\n        }\n      },\n      char: {\n        pattern: /'(?:[^\\\\\\r\\n]{1,2}|\\\\(?:.|u(?:[A-Fa-f0-9]{1,4}|\\{[A-Fa-f0-9]{1,6}\\})))'/,\n        greedy: true\n      }\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractor<PERSON>uby", "require", "module", "exports", "crystal", "displayName", "aliases", "Prism", "register", "languages", "extend", "keyword", "pattern", "lookbehind", "number", "operator", "ruby", "punctuation", "insertBefore", "attribute", "inside", "delimiter", "alias", "args", "expansion", "content", "char", "greedy"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/crystal.js"], "sourcesContent": ["'use strict'\nvar refractorRuby = require('./ruby.js')\nmodule.exports = crystal\ncrystal.displayName = 'crystal'\ncrystal.aliases = []\nfunction crystal(Prism) {\n  Prism.register(refractorRuby)\n  ;(function (Prism) {\n    Prism.languages.crystal = Prism.languages.extend('ruby', {\n      keyword: [\n        /\\b(?:__DIR__|__END_LINE__|__FILE__|__LINE__|abstract|alias|annotation|as|asm|begin|break|case|class|def|do|else|elsif|end|ensure|enum|extend|for|fun|if|ifdef|include|instance_sizeof|lib|macro|module|next|of|out|pointerof|private|protected|ptr|require|rescue|return|select|self|sizeof|struct|super|then|type|typeof|undef|uninitialized|union|unless|until|when|while|with|yield)\\b/,\n        {\n          pattern: /(\\.\\s*)(?:is_a|responds_to)\\?/,\n          lookbehind: true\n        }\n      ],\n      number:\n        /\\b(?:0b[01_]*[01]|0o[0-7_]*[0-7]|0x[\\da-fA-F_]*[\\da-fA-F]|(?:\\d(?:[\\d_]*\\d)?)(?:\\.[\\d_]*\\d)?(?:[eE][+-]?[\\d_]*\\d)?)(?:_(?:[uif](?:8|16|32|64))?)?\\b/,\n      operator: [/->/, Prism.languages.ruby.operator],\n      punctuation: /[(){}[\\].,;\\\\]/\n    })\n    Prism.languages.insertBefore('crystal', 'string-literal', {\n      attribute: {\n        pattern: /@\\[.*?\\]/,\n        inside: {\n          delimiter: {\n            pattern: /^@\\[|\\]$/,\n            alias: 'punctuation'\n          },\n          attribute: {\n            pattern: /^(\\s*)\\w+/,\n            lookbehind: true,\n            alias: 'class-name'\n          },\n          args: {\n            pattern: /\\S(?:[\\s\\S]*\\S)?/,\n            inside: Prism.languages.crystal\n          }\n        }\n      },\n      expansion: {\n        pattern: /\\{(?:\\{.*?\\}|%.*?%)\\}/,\n        inside: {\n          content: {\n            pattern: /^(\\{.)[\\s\\S]+(?=.\\}$)/,\n            lookbehind: true,\n            inside: Prism.languages.crystal\n          },\n          delimiter: {\n            pattern: /^\\{[\\{%]|[\\}%]\\}$/,\n            alias: 'operator'\n          }\n        }\n      },\n      char: {\n        pattern:\n          /'(?:[^\\\\\\r\\n]{1,2}|\\\\(?:.|u(?:[A-Fa-f0-9]{1,4}|\\{[A-Fa-f0-9]{1,6}\\})))'/,\n        greedy: true\n      }\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,WAAW,CAAC;AACxCC,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,QAAQ,CAACR,aAAa,CAAC;EAC5B,CAAC,UAAUO,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,OAAO,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE;MACvDC,OAAO,EAAE,CACP,2XAA2X,EAC3X;QACEC,OAAO,EAAE,+BAA+B;QACxCC,UAAU,EAAE;MACd,CAAC,CACF;MACDC,MAAM,EACJ,qJAAqJ;MACvJC,QAAQ,EAAE,CAAC,IAAI,EAAER,KAAK,CAACE,SAAS,CAACO,IAAI,CAACD,QAAQ,CAAC;MAC/CE,WAAW,EAAE;IACf,CAAC,CAAC;IACFV,KAAK,CAACE,SAAS,CAACS,YAAY,CAAC,SAAS,EAAE,gBAAgB,EAAE;MACxDC,SAAS,EAAE;QACTP,OAAO,EAAE,UAAU;QACnBQ,MAAM,EAAE;UACNC,SAAS,EAAE;YACTT,OAAO,EAAE,UAAU;YACnBU,KAAK,EAAE;UACT,CAAC;UACDH,SAAS,EAAE;YACTP,OAAO,EAAE,WAAW;YACpBC,UAAU,EAAE,IAAI;YAChBS,KAAK,EAAE;UACT,CAAC;UACDC,IAAI,EAAE;YACJX,OAAO,EAAE,kBAAkB;YAC3BQ,MAAM,EAAEb,KAAK,CAACE,SAAS,CAACL;UAC1B;QACF;MACF,CAAC;MACDoB,SAAS,EAAE;QACTZ,OAAO,EAAE,uBAAuB;QAChCQ,MAAM,EAAE;UACNK,OAAO,EAAE;YACPb,OAAO,EAAE,uBAAuB;YAChCC,UAAU,EAAE,IAAI;YAChBO,MAAM,EAAEb,KAAK,CAACE,SAAS,CAACL;UAC1B,CAAC;UACDiB,SAAS,EAAE;YACTT,OAAO,EAAE,mBAAmB;YAC5BU,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACDI,IAAI,EAAE;QACJd,OAAO,EACL,yEAAyE;QAC3Ee,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ,CAAC,EAAEpB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}