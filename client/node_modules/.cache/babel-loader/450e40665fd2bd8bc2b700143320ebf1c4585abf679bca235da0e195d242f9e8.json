{"ast": null, "code": "'use strict';\n\nmodule.exports = parigp;\nparigp.displayName = 'parigp';\nparigp.aliases = [];\nfunction parigp(Prism) {\n  Prism.languages.parigp = {\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\\\\\\\.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"/,\n      greedy: true\n    },\n    // PARI/GP does not care about white spaces at all\n    // so let's process the keywords to build an appropriate regexp\n    // (e.g. \"b *r *e *a *k\", etc.)\n    keyword: function () {\n      var keywords = ['breakpoint', 'break', 'dbg_down', 'dbg_err', 'dbg_up', 'dbg_x', 'forcomposite', 'fordiv', 'forell', 'forpart', 'forprime', 'forstep', 'forsubgroup', 'forvec', 'for', 'iferr', 'if', 'local', 'my', 'next', 'return', 'until', 'while'];\n      keywords = keywords.map(function (keyword) {\n        return keyword.split('').join(' *');\n      }).join('|');\n      return RegExp('\\\\b(?:' + keywords + ')\\\\b');\n    }(),\n    function: /\\b\\w(?:[\\w ]*\\w)?(?= *\\()/,\n    number: {\n      // The lookbehind and the negative lookahead prevent from breaking the .. operator\n      pattern: /((?:\\. *\\. *)?)(?:\\b\\d(?: *\\d)*(?: *(?!\\. *\\.)\\.(?: *\\d)*)?|\\. *\\d(?: *\\d)*)(?: *e *(?:[+-] *)?\\d(?: *\\d)*)?/i,\n      lookbehind: true\n    },\n    operator: /\\. *\\.|[*\\/!](?: *=)?|%(?: *=|(?: *#)?(?: *')*)?|\\+(?: *[+=])?|-(?: *[-=>])?|<(?: *>|(?: *<)?(?: *=)?)?|>(?: *>)?(?: *=)?|=(?: *=){0,2}|\\\\(?: *\\/)?(?: *=)?|&(?: *&)?|\\| *\\||['#~^]/,\n    punctuation: /[\\[\\]{}().,:;|]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "parigp", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "keyword", "keywords", "map", "split", "join", "RegExp", "function", "number", "lookbehind", "operator", "punctuation"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/parigp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = parigp\nparigp.displayName = 'parigp'\nparigp.aliases = []\nfunction parigp(Prism) {\n  Prism.languages.parigp = {\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\\\\\\\.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"/,\n      greedy: true\n    },\n    // PARI/GP does not care about white spaces at all\n    // so let's process the keywords to build an appropriate regexp\n    // (e.g. \"b *r *e *a *k\", etc.)\n    keyword: (function () {\n      var keywords = [\n        'breakpoint',\n        'break',\n        'dbg_down',\n        'dbg_err',\n        'dbg_up',\n        'dbg_x',\n        'forcomposite',\n        'fordiv',\n        'forell',\n        'forpart',\n        'forprime',\n        'forstep',\n        'forsubgroup',\n        'forvec',\n        'for',\n        'iferr',\n        'if',\n        'local',\n        'my',\n        'next',\n        'return',\n        'until',\n        'while'\n      ]\n      keywords = keywords\n        .map(function (keyword) {\n          return keyword.split('').join(' *')\n        })\n        .join('|')\n      return RegExp('\\\\b(?:' + keywords + ')\\\\b')\n    })(),\n    function: /\\b\\w(?:[\\w ]*\\w)?(?= *\\()/,\n    number: {\n      // The lookbehind and the negative lookahead prevent from breaking the .. operator\n      pattern:\n        /((?:\\. *\\. *)?)(?:\\b\\d(?: *\\d)*(?: *(?!\\. *\\.)\\.(?: *\\d)*)?|\\. *\\d(?: *\\d)*)(?: *e *(?:[+-] *)?\\d(?: *\\d)*)?/i,\n      lookbehind: true\n    },\n    operator:\n      /\\. *\\.|[*\\/!](?: *=)?|%(?: *=|(?: *#)?(?: *')*)?|\\+(?: *[+=])?|-(?: *[-=>])?|<(?: *>|(?: *<)?(?: *=)?)?|>(?: *>)?(?: *=)?|=(?: *=){0,2}|\\\\(?: *\\/)?(?: *=)?|&(?: *&)?|\\| *\\||['#~^]/,\n    punctuation: /[\\[\\]{}().,:;|]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,MAAM,EAAE;IACV,CAAC;IACD;IACA;IACA;IACAC,OAAO,EAAG,YAAY;MACpB,IAAIC,QAAQ,GAAG,CACb,YAAY,EACZ,OAAO,EACP,UAAU,EACV,SAAS,EACT,QAAQ,EACR,OAAO,EACP,cAAc,EACd,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,UAAU,EACV,SAAS,EACT,aAAa,EACb,QAAQ,EACR,KAAK,EACL,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,CACR;MACDA,QAAQ,GAAGA,QAAQ,CAChBC,GAAG,CAAC,UAAUF,OAAO,EAAE;QACtB,OAAOA,OAAO,CAACG,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrC,CAAC,CAAC,CACDA,IAAI,CAAC,GAAG,CAAC;MACZ,OAAOC,MAAM,CAAC,QAAQ,GAAGJ,QAAQ,GAAG,MAAM,CAAC;IAC7C,CAAC,CAAE,CAAC;IACJK,QAAQ,EAAE,2BAA2B;IACrCC,MAAM,EAAE;MACN;MACAT,OAAO,EACL,+GAA+G;MACjHU,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EACN,qLAAqL;IACvLC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}