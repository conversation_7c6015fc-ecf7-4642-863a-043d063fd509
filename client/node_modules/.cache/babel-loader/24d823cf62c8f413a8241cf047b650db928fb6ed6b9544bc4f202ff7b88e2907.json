{"ast": null, "code": "'use strict';\n\nmodule.exports = groovy;\ngroovy.displayName = 'groovy';\ngroovy.aliases = [];\nfunction groovy(Prism) {\n  Prism.languages.groovy = Prism.languages.extend('clike', {\n    string: [{\n      // https://groovy-lang.org/syntax.html#_dollar_slashy_string\n      pattern: /(\"\"\"|''')(?:[^\\\\]|\\\\[\\s\\S])*?\\1|\\$\\/(?:[^/$]|\\$(?:[/$]|(?![/$]))|\\/(?!\\$))*\\/\\$/,\n      greedy: true\n    }, {\n      // TODO: Slash strings (e.g. /foo/) can contain line breaks but this will cause a lot of trouble with\n      // simple division (see JS regex), so find a fix maybe?\n      pattern: /([\"'/])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    }],\n    keyword: /\\b(?:abstract|as|assert|boolean|break|byte|case|catch|char|class|const|continue|def|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|in|instanceof|int|interface|long|native|new|package|private|protected|public|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|trait|transient|try|void|volatile|while)\\b/,\n    number: /\\b(?:0b[01_]+|0x[\\da-f_]+(?:\\.[\\da-f_p\\-]+)?|[\\d_]+(?:\\.[\\d_]+)?(?:e[+-]?\\d+)?)[glidf]?\\b/i,\n    operator: {\n      pattern: /(^|[^.])(?:~|==?~?|\\?[.:]?|\\*(?:[.=]|\\*=?)?|\\.[@&]|\\.\\.<|\\.\\.(?!\\.)|-[-=>]?|\\+[+=]?|!=?|<(?:<=?|=>?)?|>(?:>>?=?|=)?|&[&=]?|\\|[|=]?|\\/=?|\\^=?|%=?)/,\n      lookbehind: true\n    },\n    punctuation: /\\.+|[{}[\\];(),:$]/\n  });\n  Prism.languages.insertBefore('groovy', 'string', {\n    shebang: {\n      pattern: /#!.+/,\n      alias: 'comment'\n    }\n  });\n  Prism.languages.insertBefore('groovy', 'punctuation', {\n    'spock-block': /\\b(?:and|cleanup|expect|given|setup|then|when|where):/\n  });\n  Prism.languages.insertBefore('groovy', 'function', {\n    annotation: {\n      pattern: /(^|[^.])@\\w+/,\n      lookbehind: true,\n      alias: 'punctuation'\n    }\n  }); // Handle string interpolation\n  Prism.hooks.add('wrap', function (env) {\n    if (env.language === 'groovy' && env.type === 'string') {\n      var delimiter = env.content.value[0];\n      if (delimiter != \"'\") {\n        var pattern = /([^\\\\])(?:\\$(?:\\{.*?\\}|[\\w.]+))/;\n        if (delimiter === '$') {\n          pattern = /([^\\$])(?:\\$(?:\\{.*?\\}|[\\w.]+))/;\n        } // To prevent double HTML-encoding we have to decode env.content first\n        env.content.value = env.content.value.replace(/&lt;/g, '<').replace(/&amp;/g, '&');\n        env.content = Prism.highlight(env.content.value, {\n          expression: {\n            pattern: pattern,\n            lookbehind: true,\n            inside: Prism.languages.groovy\n          }\n        });\n        env.classes.push(delimiter === '/' ? 'regex' : 'gstring');\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "groovy", "displayName", "aliases", "Prism", "languages", "extend", "string", "pattern", "greedy", "keyword", "number", "operator", "lookbehind", "punctuation", "insertBefore", "shebang", "alias", "annotation", "hooks", "add", "env", "language", "type", "delimiter", "content", "value", "replace", "highlight", "expression", "inside", "classes", "push"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/refractor/lang/groovy.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = groovy\ngroovy.displayName = 'groovy'\ngroovy.aliases = []\nfunction groovy(Prism) {\n  Prism.languages.groovy = Prism.languages.extend('clike', {\n    string: [\n      {\n        // https://groovy-lang.org/syntax.html#_dollar_slashy_string\n        pattern:\n          /(\"\"\"|''')(?:[^\\\\]|\\\\[\\s\\S])*?\\1|\\$\\/(?:[^/$]|\\$(?:[/$]|(?![/$]))|\\/(?!\\$))*\\/\\$/,\n        greedy: true\n      },\n      {\n        // TODO: Slash strings (e.g. /foo/) can contain line breaks but this will cause a lot of trouble with\n        // simple division (see JS regex), so find a fix maybe?\n        pattern: /([\"'/])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|as|assert|boolean|break|byte|case|catch|char|class|const|continue|def|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|in|instanceof|int|interface|long|native|new|package|private|protected|public|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|trait|transient|try|void|volatile|while)\\b/,\n    number:\n      /\\b(?:0b[01_]+|0x[\\da-f_]+(?:\\.[\\da-f_p\\-]+)?|[\\d_]+(?:\\.[\\d_]+)?(?:e[+-]?\\d+)?)[glidf]?\\b/i,\n    operator: {\n      pattern:\n        /(^|[^.])(?:~|==?~?|\\?[.:]?|\\*(?:[.=]|\\*=?)?|\\.[@&]|\\.\\.<|\\.\\.(?!\\.)|-[-=>]?|\\+[+=]?|!=?|<(?:<=?|=>?)?|>(?:>>?=?|=)?|&[&=]?|\\|[|=]?|\\/=?|\\^=?|%=?)/,\n      lookbehind: true\n    },\n    punctuation: /\\.+|[{}[\\];(),:$]/\n  })\n  Prism.languages.insertBefore('groovy', 'string', {\n    shebang: {\n      pattern: /#!.+/,\n      alias: 'comment'\n    }\n  })\n  Prism.languages.insertBefore('groovy', 'punctuation', {\n    'spock-block': /\\b(?:and|cleanup|expect|given|setup|then|when|where):/\n  })\n  Prism.languages.insertBefore('groovy', 'function', {\n    annotation: {\n      pattern: /(^|[^.])@\\w+/,\n      lookbehind: true,\n      alias: 'punctuation'\n    }\n  }) // Handle string interpolation\n  Prism.hooks.add('wrap', function (env) {\n    if (env.language === 'groovy' && env.type === 'string') {\n      var delimiter = env.content.value[0]\n      if (delimiter != \"'\") {\n        var pattern = /([^\\\\])(?:\\$(?:\\{.*?\\}|[\\w.]+))/\n        if (delimiter === '$') {\n          pattern = /([^\\$])(?:\\$(?:\\{.*?\\}|[\\w.]+))/\n        } // To prevent double HTML-encoding we have to decode env.content first\n        env.content.value = env.content.value\n          .replace(/&lt;/g, '<')\n          .replace(/&amp;/g, '&')\n        env.content = Prism.highlight(env.content.value, {\n          expression: {\n            pattern: pattern,\n            lookbehind: true,\n            inside: Prism.languages.groovy\n          }\n        })\n        env.classes.push(delimiter === '/' ? 'regex' : 'gstring')\n      }\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACvDC,MAAM,EAAE,CACN;MACE;MACAC,OAAO,EACL,iFAAiF;MACnFC,MAAM,EAAE;IACV,CAAC,EACD;MACE;MACA;MACAD,OAAO,EAAE,mCAAmC;MAC5CC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,OAAO,EACL,4WAA4W;IAC9WC,MAAM,EACJ,4FAA4F;IAC9FC,QAAQ,EAAE;MACRJ,OAAO,EACL,mJAAmJ;MACrJK,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE;EACf,CAAC,CAAC;EACFV,KAAK,CAACC,SAAS,CAACU,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;IAC/CC,OAAO,EAAE;MACPR,OAAO,EAAE,MAAM;MACfS,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFb,KAAK,CAACC,SAAS,CAACU,YAAY,CAAC,QAAQ,EAAE,aAAa,EAAE;IACpD,aAAa,EAAE;EACjB,CAAC,CAAC;EACFX,KAAK,CAACC,SAAS,CAACU,YAAY,CAAC,QAAQ,EAAE,UAAU,EAAE;IACjDG,UAAU,EAAE;MACVV,OAAO,EAAE,cAAc;MACvBK,UAAU,EAAE,IAAI;MAChBI,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EAAC;EACHb,KAAK,CAACe,KAAK,CAACC,GAAG,CAAC,MAAM,EAAE,UAAUC,GAAG,EAAE;IACrC,IAAIA,GAAG,CAACC,QAAQ,KAAK,QAAQ,IAAID,GAAG,CAACE,IAAI,KAAK,QAAQ,EAAE;MACtD,IAAIC,SAAS,GAAGH,GAAG,CAACI,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;MACpC,IAAIF,SAAS,IAAI,GAAG,EAAE;QACpB,IAAIhB,OAAO,GAAG,iCAAiC;QAC/C,IAAIgB,SAAS,KAAK,GAAG,EAAE;UACrBhB,OAAO,GAAG,iCAAiC;QAC7C,CAAC,CAAC;QACFa,GAAG,CAACI,OAAO,CAACC,KAAK,GAAGL,GAAG,CAACI,OAAO,CAACC,KAAK,CAClCC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;QACzBN,GAAG,CAACI,OAAO,GAAGrB,KAAK,CAACwB,SAAS,CAACP,GAAG,CAACI,OAAO,CAACC,KAAK,EAAE;UAC/CG,UAAU,EAAE;YACVrB,OAAO,EAAEA,OAAO;YAChBK,UAAU,EAAE,IAAI;YAChBiB,MAAM,EAAE1B,KAAK,CAACC,SAAS,CAACJ;UAC1B;QACF,CAAC,CAAC;QACFoB,GAAG,CAACU,OAAO,CAACC,IAAI,CAACR,SAAS,KAAK,GAAG,GAAG,OAAO,GAAG,SAAS,CAAC;MAC3D;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}