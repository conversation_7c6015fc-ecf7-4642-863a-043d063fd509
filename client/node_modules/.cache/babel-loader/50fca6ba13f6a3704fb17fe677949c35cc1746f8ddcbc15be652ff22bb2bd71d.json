{"ast": null, "code": "/*\nLanguage: Haxe\nDescription: Haxe is an open source toolkit based on a modern, high level, strictly typed programming language.\nAuthor: <PERSON> <i<PERSON><PERSON>@gmail.com> (Based on the actionscript.js language file by <PERSON>)\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://haxe.org\n*/\n\nfunction haxe(hljs) {\n  const HAXE_BASIC_TYPES = 'Int Float String Bool Dynamic Void Array ';\n  return {\n    name: 'Haxe',\n    aliases: ['hx'],\n    keywords: {\n      keyword: 'break case cast catch continue default do dynamic else enum extern ' + 'for function here if import in inline never new override package private get set ' + 'public return static super switch this throw trace try typedef untyped using var while ' + HAXE_BASIC_TYPES,\n      built_in: 'trace this',\n      literal: 'true false null _'\n    },\n    contains: [{\n      className: 'string',\n      // interpolate-able strings\n      begin: '\\'',\n      end: '\\'',\n      contains: [hljs.BACKSLASH_ESCAPE, {\n        className: 'subst',\n        // interpolation\n        begin: '\\\\$\\\\{',\n        end: '\\\\}'\n      }, {\n        className: 'subst',\n        // interpolation\n        begin: '\\\\$',\n        end: /\\W\\}/\n      }]\n    }, hljs.QUOTE_STRING_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.C_NUMBER_MODE, {\n      className: 'meta',\n      // compiler meta\n      begin: '@:',\n      end: '$'\n    }, {\n      className: 'meta',\n      // compiler conditionals\n      begin: '#',\n      end: '$',\n      keywords: {\n        'meta-keyword': 'if else elseif end error'\n      }\n    }, {\n      className: 'type',\n      // function types\n      begin: ':[ \\t]*',\n      end: '[^A-Za-z0-9_ \\t\\\\->]',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    }, {\n      className: 'type',\n      // types\n      begin: ':[ \\t]*',\n      end: '\\\\W',\n      excludeBegin: true,\n      excludeEnd: true\n    }, {\n      className: 'type',\n      // instantiation\n      begin: 'new *',\n      end: '\\\\W',\n      excludeBegin: true,\n      excludeEnd: true\n    }, {\n      className: 'class',\n      // enums\n      beginKeywords: 'enum',\n      end: '\\\\{',\n      contains: [hljs.TITLE_MODE]\n    }, {\n      className: 'class',\n      // abstracts\n      beginKeywords: 'abstract',\n      end: '[\\\\{$]',\n      contains: [{\n        className: 'type',\n        begin: '\\\\(',\n        end: '\\\\)',\n        excludeBegin: true,\n        excludeEnd: true\n      }, {\n        className: 'type',\n        begin: 'from +',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      }, {\n        className: 'type',\n        begin: 'to +',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      }, hljs.TITLE_MODE],\n      keywords: {\n        keyword: 'abstract from to'\n      }\n    }, {\n      className: 'class',\n      // classes\n      begin: '\\\\b(class|interface) +',\n      end: '[\\\\{$]',\n      excludeEnd: true,\n      keywords: 'class interface',\n      contains: [{\n        className: 'keyword',\n        begin: '\\\\b(extends|implements) +',\n        keywords: 'extends implements',\n        contains: [{\n          className: 'type',\n          begin: hljs.IDENT_RE,\n          relevance: 0\n        }]\n      }, hljs.TITLE_MODE]\n    }, {\n      className: 'function',\n      beginKeywords: 'function',\n      end: '\\\\(',\n      excludeEnd: true,\n      illegal: '\\\\S',\n      contains: [hljs.TITLE_MODE]\n    }],\n    illegal: /<\\//\n  };\n}\nmodule.exports = haxe;", "map": {"version": 3, "names": ["haxe", "hljs", "HAXE_BASIC_TYPES", "name", "aliases", "keywords", "keyword", "built_in", "literal", "contains", "className", "begin", "end", "BACKSLASH_ESCAPE", "QUOTE_STRING_MODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "C_NUMBER_MODE", "excludeBegin", "excludeEnd", "relevance", "beginKeywords", "TITLE_MODE", "IDENT_RE", "illegal", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/haxe.js"], "sourcesContent": ["/*\nLanguage: Haxe\nDescription: Haxe is an open source toolkit based on a modern, high level, strictly typed programming language.\nAuthor: <PERSON> <i<PERSON><PERSON>@gmail.com> (Based on the actionscript.js language file by <PERSON>)\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://haxe.org\n*/\n\nfunction haxe(hljs) {\n\n  const HAXE_BASIC_TYPES = 'Int Float String Bool Dynamic Void Array ';\n\n  return {\n    name: 'Haxe',\n    aliases: ['hx'],\n    keywords: {\n      keyword: 'break case cast catch continue default do dynamic else enum extern ' +\n               'for function here if import in inline never new override package private get set ' +\n               'public return static super switch this throw trace try typedef untyped using var while ' +\n               HAXE_BASIC_TYPES,\n      built_in:\n        'trace this',\n      literal:\n        'true false null _'\n    },\n    contains: [\n      {\n        className: 'string', // interpolate-able strings\n        begin: '\\'',\n        end: '\\'',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          {\n            className: 'subst', // interpolation\n            begin: '\\\\$\\\\{',\n            end: '\\\\}'\n          },\n          {\n            className: 'subst', // interpolation\n            begin: '\\\\$',\n            end: /\\W\\}/\n          }\n        ]\n      },\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta', // compiler meta\n        begin: '@:',\n        end: '$'\n      },\n      {\n        className: 'meta', // compiler conditionals\n        begin: '#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elseif end error'\n        }\n      },\n      {\n        className: 'type', // function types\n        begin: ':[ \\t]*',\n        end: '[^A-Za-z0-9_ \\t\\\\->]',\n        excludeBegin: true,\n        excludeEnd: true,\n        relevance: 0\n      },\n      {\n        className: 'type', // types\n        begin: ':[ \\t]*',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'type', // instantiation\n        begin: 'new *',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'class', // enums\n        beginKeywords: 'enum',\n        end: '\\\\{',\n        contains: [hljs.TITLE_MODE]\n      },\n      {\n        className: 'class', // abstracts\n        beginKeywords: 'abstract',\n        end: '[\\\\{$]',\n        contains: [\n          {\n            className: 'type',\n            begin: '\\\\(',\n            end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: 'type',\n            begin: 'from +',\n            end: '\\\\W',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: 'type',\n            begin: 'to +',\n            end: '\\\\W',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          hljs.TITLE_MODE\n        ],\n        keywords: {\n          keyword: 'abstract from to'\n        }\n      },\n      {\n        className: 'class', // classes\n        begin: '\\\\b(class|interface) +',\n        end: '[\\\\{$]',\n        excludeEnd: true,\n        keywords: 'class interface',\n        contains: [\n          {\n            className: 'keyword',\n            begin: '\\\\b(extends|implements) +',\n            keywords: 'extends implements',\n            contains: [\n              {\n                className: 'type',\n                begin: hljs.IDENT_RE,\n                relevance: 0\n              }\n            ]\n          },\n          hljs.TITLE_MODE\n        ]\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '\\\\(',\n        excludeEnd: true,\n        illegal: '\\\\S',\n        contains: [hljs.TITLE_MODE]\n      }\n    ],\n    illegal: /<\\//\n  };\n}\n\nmodule.exports = haxe;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAElB,MAAMC,gBAAgB,GAAG,2CAA2C;EAEpE,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,CAAC,IAAI,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,qEAAqE,GACrE,mFAAmF,GACnF,yFAAyF,GACzFJ,gBAAgB;MACzBK,QAAQ,EACN,YAAY;MACdC,OAAO,EACL;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,QAAQ;MAAE;MACrBC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTH,QAAQ,EAAE,CACRR,IAAI,CAACY,gBAAgB,EACrB;QACEH,SAAS,EAAE,OAAO;QAAE;QACpBC,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE;MACP,CAAC,EACD;QACEF,SAAS,EAAE,OAAO;QAAE;QACpBC,KAAK,EAAE,KAAK;QACZC,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACDX,IAAI,CAACa,iBAAiB,EACtBb,IAAI,CAACc,mBAAmB,EACxBd,IAAI,CAACe,oBAAoB,EACzBf,IAAI,CAACgB,aAAa,EAClB;MACEP,SAAS,EAAE,MAAM;MAAE;MACnBC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACD;MACEF,SAAS,EAAE,MAAM;MAAE;MACnBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRP,QAAQ,EAAE;QACR,cAAc,EAAE;MAClB;IACF,CAAC,EACD;MACEK,SAAS,EAAE,MAAM;MAAE;MACnBC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,sBAAsB;MAC3BM,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;IACb,CAAC,EACD;MACEV,SAAS,EAAE,MAAM;MAAE;MACnBC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,KAAK;MACVM,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;IACd,CAAC,EACD;MACET,SAAS,EAAE,MAAM;MAAE;MACnBC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,KAAK;MACVM,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;IACd,CAAC,EACD;MACET,SAAS,EAAE,OAAO;MAAE;MACpBW,aAAa,EAAE,MAAM;MACrBT,GAAG,EAAE,KAAK;MACVH,QAAQ,EAAE,CAACR,IAAI,CAACqB,UAAU;IAC5B,CAAC,EACD;MACEZ,SAAS,EAAE,OAAO;MAAE;MACpBW,aAAa,EAAE,UAAU;MACzBT,GAAG,EAAE,QAAQ;MACbH,QAAQ,EAAE,CACR;QACEC,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAE,KAAK;QACZC,GAAG,EAAE,KAAK;QACVM,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACET,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE,KAAK;QACVM,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACET,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,KAAK;QACVM,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE;MACd,CAAC,EACDlB,IAAI,CAACqB,UAAU,CAChB;MACDjB,QAAQ,EAAE;QACRC,OAAO,EAAE;MACX;IACF,CAAC,EACD;MACEI,SAAS,EAAE,OAAO;MAAE;MACpBC,KAAK,EAAE,wBAAwB;MAC/BC,GAAG,EAAE,QAAQ;MACbO,UAAU,EAAE,IAAI;MAChBd,QAAQ,EAAE,iBAAiB;MAC3BI,QAAQ,EAAE,CACR;QACEC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,2BAA2B;QAClCN,QAAQ,EAAE,oBAAoB;QAC9BI,QAAQ,EAAE,CACR;UACEC,SAAS,EAAE,MAAM;UACjBC,KAAK,EAAEV,IAAI,CAACsB,QAAQ;UACpBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACDnB,IAAI,CAACqB,UAAU;IAEnB,CAAC,EACD;MACEZ,SAAS,EAAE,UAAU;MACrBW,aAAa,EAAE,UAAU;MACzBT,GAAG,EAAE,KAAK;MACVO,UAAU,EAAE,IAAI;MAChBK,OAAO,EAAE,KAAK;MACdf,QAAQ,EAAE,CAACR,IAAI,CAACqB,UAAU;IAC5B,CAAC,CACF;IACDE,OAAO,EAAE;EACX,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAG1B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}