{"ast": null, "code": "/*\nLanguage: ERB (Embedded Ruby)\nRequires: xml.js, ruby.js\nAuthor: <PERSON> <luca<PERSON><PERSON><PERSON>@gmail.com>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: \"Bridge\" language defining fragments of Ruby in HTML within <% .. %>\nWebsite: https://ruby-doc.org/stdlib-2.6.5/libdoc/erb/rdoc/ERB.html\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction erb(hljs) {\n  return {\n    name: 'ERB',\n    subLanguage: 'xml',\n    contains: [hljs.COMMENT('<%#', '%>'), {\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true\n    }]\n  };\n}\nmodule.exports = erb;", "map": {"version": 3, "names": ["erb", "hljs", "name", "subLanguage", "contains", "COMMENT", "begin", "end", "excludeBegin", "excludeEnd", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/chat-feat-explore/client/node_modules/highlight.js/lib/languages/erb.js"], "sourcesContent": ["/*\nLanguage: ERB (Embedded Ruby)\nRequires: xml.js, ruby.js\nAuthor: <PERSON> <luca<PERSON><PERSON><PERSON>@gmail.com>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: \"Bridge\" language defining fragments of Ruby in HTML within <% .. %>\nWebsite: https://ruby-doc.org/stdlib-2.6.5/libdoc/erb/rdoc/ERB.html\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction erb(hljs) {\n  return {\n    name: 'ERB',\n    subLanguage: 'xml',\n    contains: [\n      hljs.COMMENT('<%#', '%>'),\n      {\n        begin: '<%[%=-]?',\n        end: '[%-]?%>',\n        subLanguage: 'ruby',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n}\n\nmodule.exports = erb;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,OAAO;IACLC,IAAI,EAAE,KAAK;IACXC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CACRH,IAAI,CAACI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EACzB;MACEC,KAAK,EAAE,UAAU;MACjBC,GAAG,EAAE,SAAS;MACdJ,WAAW,EAAE,MAAM;MACnBK,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGX,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}